"use client";

import { useState, useEffect } from "react";
import { useForm, FieldValues, Path, UseFormReturn } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { motion } from "framer-motion";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, AlertCircle, CheckCircle } from "lucide-react";
import { BaseEntity } from './base-service';

// Generic form field configuration
export interface FormFieldConfig<T extends FieldValues> {
  name: Path<T>;
  label: string;
  type: 'text' | 'textarea' | 'number' | 'select' | 'switch' | 'checkbox' | 'date' | 'email' | 'password';
  placeholder?: string;
  description?: string;
  required?: boolean;
  options?: { value: string | number; label: string }[];
  icon?: React.ComponentType<{ className?: string }>;
  validation?: z.ZodType<any>;
  dependencies?: Path<T>[];
  conditional?: (values: T) => boolean;
  transform?: (value: any) => any;
}

export interface BaseFormProps<T extends BaseEntity, F extends FieldValues> {
  entity?: T;
  onSubmit: (data: F) => Promise<void>;
  onCancel: () => void;
  className?: string;
  title?: string;
  description?: string;
  icon?: React.ComponentType<{ className?: string }>;
  renderCustomComponent?: (componentType: string) => React.ReactNode;
}

export interface FormConfig<T extends FieldValues> {
  schema: z.ZodType<T>;
  fields: FormFieldConfig<T>[];
  defaultValues?: Partial<T>;
  sections?: {
    title: string;
    description?: string;
    fields: Path<T>[];
    customComponent?: string;
  }[];
}

// Generic form component factory
export function createFormComponent<T extends BaseEntity, F extends FieldValues>(
  entityName: string,
  formConfig: FormConfig<F>
) {
  return function EntityForm({
    entity,
    onSubmit,
    onCancel,
    className = "",
    title,
    description,
    icon: Icon,
    renderCustomComponent,
  }: BaseFormProps<T, F>) {
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [submitError, setSubmitError] = useState<string | null>(null);
    const [showSuccess, setShowSuccess] = useState(false);

    // Prepare initial values
    const getInitialValues = () => {
      if (entity && entity.id) {
        console.log('🔍 Base form - entity:', entity);
        console.log('🔍 Base form - default values:', formConfig.defaultValues);

        // Create a clean copy of entity data
        const entityData = { ...entity };

        // Transform date fields if they exist
        if (entityData.dateOfBirth) {
          if (typeof entityData.dateOfBirth === 'string') {
            // If it's already a string, keep it as is
            entityData.dateOfBirth = entityData.dateOfBirth;
          } else if (entityData.dateOfBirth instanceof Date) {
            // If it's a Date object, convert to YYYY-MM-DD format
            entityData.dateOfBirth = entityData.dateOfBirth.toISOString().split('T')[0];
          }
        }

        const values = {
          ...formConfig.defaultValues,
          ...entityData,
        };

        console.log('🔍 Base form - computed values:', values);
        return values;
      }
      return formConfig.defaultValues;
    };

    const form = useForm<F>({
      resolver: zodResolver(formConfig.schema),
      defaultValues: getInitialValues(),
      mode: "onChange",
    });

    const { handleSubmit, formState: { errors, isValid }, reset } = form;

    // Reset form when entity changes
    useEffect(() => {
      console.log('🔍 [BaseForm] Entity changed:', entity);
      if (entity) {
        console.log('🏠 [BaseForm] Entity address fields:', {
          addressLine1: (entity as any).addressLine1,
          addressLine2: (entity as any).addressLine2,
          city: (entity as any).city,
          state: (entity as any).state,
          zip: (entity as any).zip,
          country: (entity as any).country,
        });
      }
      const newValues = getInitialValues();
      console.log('🔍 [BaseForm] Resetting form with values:', newValues);
      console.log('🏠 [BaseForm] Form address values:', {
        addressLine1: newValues.addressLine1,
        addressLine2: newValues.addressLine2,
        city: newValues.city,
        state: newValues.state,
        zip: newValues.zip,
        country: newValues.country,
      });
      reset(newValues);
    }, [entity?.id, entity, reset]);

    const handleFormSubmit = async (data: F) => {
      try {
        setIsSubmitting(true);
        setSubmitError(null);
        
        await onSubmit(data);
        
        setShowSuccess(true);
        setTimeout(() => setShowSuccess(false), 2000);
      } catch (error) {
        setSubmitError(error instanceof Error ? error.message : "An error occurred");
      } finally {
        setIsSubmitting(false);
      }
    };

    const renderField = (fieldConfig: FormFieldConfig<F>) => {
      const { name, label, type, placeholder, description, required, options, icon: FieldIcon } = fieldConfig;

      // Check conditional rendering
      if (fieldConfig.conditional && !fieldConfig.conditional(form.watch())) {
        return null;
      }

      return (
        <FormField
          key={name}
          form={form}
          config={fieldConfig}
        />
      );
    };

    const renderSection = (section: { title: string; description?: string; fields: Path<F>[]; customComponent?: string }) => {
      const sectionFields = formConfig.fields.filter(field => section.fields.includes(field.name));

      return (
        <div key={section.title} className="space-y-4">
          <div className="border-b pb-2">
            <h3 className="text-lg font-medium">{section.title}</h3>
            {section.description && (
              <p className="text-sm text-gray-500">{section.description}</p>
            )}
          </div>
          <div className="space-y-4">
            {sectionFields.map(renderField)}
            {section.customComponent && renderCustomComponent?.(section.customComponent)}
          </div>
        </div>
      );
    };

    return (
      <Card className={`w-full max-w-2xl mx-auto ${className}`}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            {Icon && <Icon className="h-5 w-5" />}
            {title || `${entity ? 'Edit' : 'Add'} ${entityName}`}
          </CardTitle>
          <CardDescription>
            {description || `${entity ? 'Update' : 'Create'} ${entityName.toLowerCase()} details below.`}
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          {submitError && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{submitError}</AlertDescription>
            </Alert>
          )}

          <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
            {formConfig.sections ? (
              formConfig.sections.map(renderSection)
            ) : (
              <div className="space-y-4">
                {formConfig.fields.map(renderField)}
              </div>
            )}

            {/* Form Actions */}
            <div className="flex items-center gap-3 pt-6 border-t">
              <motion.div
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                className="flex-1"
              >
                <Button
                  type="submit"
                  disabled={!isValid || isSubmitting}
                  className="w-full"
                >
                  <motion.div
                    initial={false}
                    animate={{
                      scale: showSuccess ? [1, 1.1, 1] : 1,
                      backgroundColor: showSuccess ? "#10b981" : undefined,
                    }}
                    transition={{ duration: 0.3 }}
                    className="flex items-center gap-2"
                  >
                    {isSubmitting ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : showSuccess ? (
                      <CheckCircle className="h-4 w-4" />
                    ) : Icon ? (
                      <Icon className="h-4 w-4" />
                    ) : null}
                    {isSubmitting
                      ? "Saving..."
                      : showSuccess
                      ? "Success!"
                      : entity
                      ? `Update ${entityName}`
                      : `Create ${entityName}`
                    }
                  </motion.div>
                </Button>
              </motion.div>

              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isSubmitting}
                className="px-6"
              >
                Cancel
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    );
  };
}

// Generic form field component
interface FormFieldProps<T extends FieldValues> {
  form: UseFormReturn<T>;
  config: FormFieldConfig<T>;
}

function FormField<T extends FieldValues>({ form, config }: FormFieldProps<T>) {
  const { register, setValue, watch, formState: { errors } } = form;
  const { name, label, type, placeholder, description, required, options, icon: FieldIcon } = config;

  const error = errors[name];
  const value = watch(name);

  // Dynamic imports for UI components based on type
  const renderInput = () => {
    switch (type) {
      case 'text':
      case 'email':
      case 'password':
        return (
          <input
            {...register(name)}
            type={type}
            placeholder={placeholder}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        );

      case 'textarea':
        return (
          <textarea
            {...register(name)}
            placeholder={placeholder}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 min-h-[100px]"
          />
        );

      case 'number':
        return (
          <input
            {...register(name, { valueAsNumber: true })}
            type="number"
            placeholder={placeholder}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        );

      case 'select':
        return (
          <select
            {...register(name, {
              valueAsNumber: typeof options?.[0]?.value === 'number',
              setValueAs: (value) => {
                if (typeof options?.[0]?.value === 'number') {
                  return value === '' ? undefined : Number(value);
                }
                return value;
              }
            })}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
          >
            <option value="" className="text-gray-500 dark:text-gray-400">
              {placeholder || `Select ${label}`}
            </option>
            {options?.map((option) => (
              <option
                key={option.value}
                value={option.value}
                className="text-gray-900 dark:text-gray-100 bg-white dark:bg-gray-800"
              >
                {option.label}
              </option>
            ))}
          </select>
        );

      case 'date':
        return (
          <input
            {...register(name)}
            type="date"
            placeholder={placeholder}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        );

      case 'switch':
        return (
          <div className="flex items-center space-x-2">
            <button
              type="button"
              className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${
                value ? 'bg-blue-600' : 'bg-gray-200'
              }`}
              onClick={() => setValue(name, !value, { shouldValidate: true })}
            >
              <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                value ? 'translate-x-6' : 'translate-x-1'
              }`} />
            </button>
            <span className="text-sm">{value ? 'Active' : 'Inactive'}</span>
            <input
              {...register(name)}
              type="checkbox"
              className="sr-only"
              checked={value}
              readOnly
            />
          </div>
        );

      default:
        return (
          <input
            {...register(name)}
            type="text"
            placeholder={placeholder}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        );
    }
  };

  return (
    <div className="space-y-2">
      <label className="flex items-center gap-2 text-sm font-medium">
        {FieldIcon && <FieldIcon className="h-4 w-4" />}
        {label}
        {required && <span className="text-red-500">*</span>}
      </label>
      
      {renderInput()}
      
      {error && (
        <p className="text-sm text-red-600">{error.message as string}</p>
      )}
      
      {description && (
        <p className="text-sm text-gray-500">{description}</p>
      )}
    </div>
  );
}
