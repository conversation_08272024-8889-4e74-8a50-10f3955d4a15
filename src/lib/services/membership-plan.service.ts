import { db } from "@/lib/db";
import { membership_plans, tenants, type MembershipPlan, type NewMembershipPlan } from "@/lib/db/schema";
import { eq, and, ilike, desc, sql } from "drizzle-orm";
import { createId } from "@paralleldrive/cuid2";

/**
 * Membership Plan Service
 * 
 * Service untuk manage membership plans dengan pattern simple seperti ClassService.
 * Ini kayak "manager" yang handle semua operasi CRUD untuk membership plans.
 * 
 * Pattern ini udah terbukti berhasil tanpa bug di classes, categories, dan subcategories.
 */

// Interface untuk search results
export interface MembershipPlanSearchResult {
  plans: MembershipPlan[];
  total: number;
  hasMore: boolean;
}

/**
 * MembershipPlanService - Service untuk manage membership plans
 * 
 * Menggunakan static methods pattern yang simple dan proven.
 * Tidak pakai BaseService yang kompleks, langsung ke database dengan Drizzle ORM.
 */
export class MembershipPlanService {
  /**
   * Search membership plans dengan filtering dan pagination
   * 
   * Ini method utama untuk ambil data plans dengan berbagai filter:
   * - tenantId: wajib untuk tenant isolation
   * - search: optional, search berdasarkan nama plan
   * - is_active: optional, filter berdasarkan status aktif
   * - limit & offset: untuk pagination
   */
  static async searchPlans(
    tenantId: number, 
    search?: string,
    is_active?: boolean,
    limit = 20, 
    offset = 0
  ): Promise<MembershipPlanSearchResult> {
    try {
      // Build where conditions - mulai dari tenant isolation
      let whereConditions = eq(membership_plans.tenantId, tenantId);

      // Filter by active status if provided
      if (is_active !== undefined) {
        whereConditions = and(
          whereConditions,
          eq(membership_plans.is_active, is_active)
        ) as any;
      }

      // Kalau ada search term, tambahin filter nama
      if (search && search.trim()) {
        whereConditions = and(
          whereConditions,
          ilike(membership_plans.name, `%${search.trim()}%`)
        ) as any;
      }

      // Query dengan pagination - ambil +1 untuk cek hasMore
      const planResults = await db
        .select()
        .from(membership_plans)
        .where(whereConditions)
        .orderBy(desc(membership_plans.createdAt))
        .limit(limit + 1) // +1 untuk cek hasMore
        .offset(offset);

      // Cek apakah ada data lebih
      const hasMore = planResults.length > limit;
      if (hasMore) {
        planResults.pop(); // Hapus item extra
      }

      // Count total untuk metadata
      const totalResult = await db
        .select({ count: sql<number>`count(*)` })
        .from(membership_plans)
        .where(whereConditions);

      const total = Number(totalResult[0]?.count || 0);

      return {
        plans: planResults,
        total,
        hasMore,
      };
    } catch (error) {
      console.error("Error searching membership plans:", error);
      throw error;
    }
  }

  /**
   * Get all membership plans by tenant ID
   * 
   * Method simple untuk ambil semua plans dalam satu tenant.
   * Biasanya dipake untuk dropdown atau list sederhana.
   */
  static async getByTenantId(tenantId: number): Promise<MembershipPlan[]> {
    return await db
      .select()
      .from(membership_plans)
      .where(eq(membership_plans.tenantId, tenantId))
      .orderBy(desc(membership_plans.createdAt));
  }

  /**
   * Get active membership plans by tenant ID
   * 
   * Ambil hanya plans yang aktif untuk tenant tertentu.
   * Berguna untuk customer-facing features.
   */
  static async getActiveByTenantId(tenantId: number): Promise<MembershipPlan[]> {
    return await db
      .select()
      .from(membership_plans)
      .where(and(
        eq(membership_plans.tenantId, tenantId),
        eq(membership_plans.is_active, true)
      ))
      .orderBy(desc(membership_plans.createdAt));
  }

  /**
   * Get membership plan by ID
   * 
   * Ambil single plan berdasarkan ID.
   * Return null kalau tidak ditemukan.
   */
  static async getById(id: string): Promise<MembershipPlan | null> {
    const [planResult] = await db
      .select()
      .from(membership_plans)
      .where(eq(membership_plans.id, id))
      .limit(1);
    return planResult || null;
  }

  /**
   * Create membership plan
   * 
   * Buat plan baru dengan data yang diperlukan.
   * ID akan di-generate otomatis dengan createId().
   */
  static async create(data: {
    tenantId: number;
    name: string;
    description?: string;
    price?: number;
    currency?: string;
    duration_value?: number;
    duration_unit?: string;
    is_active?: boolean;
  }): Promise<MembershipPlan> {
    // Validate that tenant exists before creating plan
    const [tenantExists] = await db
      .select({ id: tenants.id })
      .from(tenants)
      .where(eq(tenants.id, data.tenantId))
      .limit(1);
    
    if (!tenantExists) {
      throw new Error(`Tenant with ID ${data.tenantId} not found`);
    }

    const [planResult] = await db
      .insert(membership_plans)
      .values({
        id: createId(),
        tenantId: data.tenantId,
        name: data.name,
        description: data.description || null,
        price: data.price || null,
        currency: data.currency || null,
        duration_value: data.duration_value || null,
        duration_unit: data.duration_unit || null,
        is_active: data.is_active !== undefined ? data.is_active : true,
      })
      .returning();
    return planResult;
  }

  /**
   * Update membership plan
   * 
   * Update plan dengan data baru.
   * Hanya field yang di-provide yang akan di-update.
   */
  static async update(id: string, data: {
    name?: string;
    description?: string;
    price?: number;
    currency?: string;
    duration_value?: number;
    duration_unit?: string;
    is_active?: boolean;
  }): Promise<MembershipPlan | null> {
    const updateData: Partial<NewMembershipPlan> = {};
    
    if (data.name !== undefined) updateData.name = data.name;
    if (data.description !== undefined) updateData.description = data.description;
    if (data.price !== undefined) updateData.price = data.price;
    if (data.currency !== undefined) updateData.currency = data.currency;
    if (data.duration_value !== undefined) updateData.duration_value = data.duration_value;
    if (data.duration_unit !== undefined) updateData.duration_unit = data.duration_unit;
    if (data.is_active !== undefined) updateData.is_active = data.is_active;

    const [planResult] = await db
      .update(membership_plans)
      .set(updateData)
      .where(eq(membership_plans.id, id))
      .returning();
    return planResult || null;
  }

  /**
   * Delete membership plan
   * 
   * Hapus plan berdasarkan ID.
   * Return plan yang dihapus untuk konfirmasi.
   */
  static async delete(id: string): Promise<MembershipPlan> {
    // First check if plan exists
    const existingPlan = await this.getById(id);
    if (!existingPlan) {
      throw new Error("Membership plan not found");
    }

    // Delete the plan and return the deleted record
    const [deletedPlan] = await db
      .delete(membership_plans)
      .where(eq(membership_plans.id, id))
      .returning();

    if (!deletedPlan) {
      throw new Error("Failed to delete membership plan");
    }

    return deletedPlan;
  }

  /**
   * Get plan statistics for a tenant
   * 
   * Ambil statistik plans untuk tenant.
   * Berguna untuk dashboard atau monitoring.
   */
  static async getPlanStats(tenantId: number) {
    const plans = await this.getByTenantId(tenantId);
    const activePlans = plans.filter(plan => plan.is_active);
    const inactivePlans = plans.filter(plan => !plan.is_active);
    
    return {
      totalPlans: plans.length,
      activePlans: activePlans.length,
      inactivePlans: inactivePlans.length,
      latestPlan: plans.length > 0 ? plans[0] : null,
    };
  }

  /**
   * Toggle plan active status
   * 
   * Toggle status aktif/non-aktif plan.
   * Berguna untuk quick enable/disable.
   */
  static async toggleActiveStatus(id: string): Promise<MembershipPlan | null> {
    const existingPlan = await this.getById(id);
    if (!existingPlan) {
      throw new Error("Membership plan not found");
    }

    return await this.update(id, {
      is_active: !existingPlan.is_active
    });
  }
}
