import { Location, locations, NewLocation, tenants } from "./../db/schema";
import { db } from "../db";
import { and, desc, eq, like } from "drizzle-orm";
import { createId } from "@paralleldrive/cuid2";

export class LocationService {
  static async getByTenantId(tenantId: number): Promise<Location[]> {
    return await db
      .select()
      .from(locations)
      .where(eq(locations.tenantId, tenantId))
      .orderBy(desc(locations.createdAt));
  }

  // Get location by ID
  static async getById(id: string): Promise<Location | null> {
    const [location] = await db
      .select()
      .from(locations)
      .where(eq(locations.id, id))
      .limit(1);
    return location || null;
  }

  // Create location
  static async create(
    data: Omit<NewLocation, "id" | "createdAt">
  ): Promise<Location> {
    const [location] = await db
      .insert(locations)
      .values({
        ...data,
        id: createId(),
      })
      .returning();
    return location;
  }

  //Update Location
  static async update(
    id: string,
    data: Partial<Omit<NewLocation, "id" | "tenantId" | "createdAt">>
  ): Promise<Location | null> {
    const [location] = await db
      .update(locations)
      .set(data)
      .where(eq(locations.id, id))
      .returning();
    return location || null;
  }

  // Delete location
  static async delete(id: string): Promise<Location> {
    // First check if location exists
    const existingLocation = await this.getById(id);
    if (!existingLocation) {
      throw new Error("Location not found");
    }

    // Delete the location and return the deleted record
    const [deletedLocation] = await db
      .delete(locations)
      .where(eq(locations.id, id))
      .returning();

    if (!deletedLocation) {
      throw new Error("Failed to delete location");
    }

    return deletedLocation;
  }

  // Get location with tenant info
  static async getWithTenant(id: string) {
    const result = await db
      .select({
        location: locations,
        tenant: tenants,
      })
      .from(locations)
      .leftJoin(tenants, eq(locations.tenantId, tenants.id))
      .where(eq(locations.id, id))
      .limit(1);
    return result[0] || null;
  }

  // Get all locations (admin only)
  static async getAll(): Promise<Location[]> {
    return await db.select().from(locations).orderBy(desc(locations.createdAt));
  }

  // Search locations
  static async searchLocations(query: string): Promise<Location[]> {
    return await db
      .select()
      .from(locations)
      .where(and(like(locations.name, `%${query}%`)))
      .orderBy(desc(locations.createdAt));
  }

  // Get locations by tenant
  static async getLocationsByTenant(tenantId: number): Promise<Location[]> {
    return await db
      .select()
      .from(locations)
      .where(eq(locations.tenantId, tenantId))
      .orderBy(desc(locations.createdAt));
  }

  // Get locations by name
  static async getLocationsByName(name: string): Promise<Location[]> {
    return await db
      .select()
      .from(locations)
      .where(eq(locations.name, name))
      .orderBy(desc(locations.createdAt));
  }

  // Get locations by address
  static async getLocationsByAddress(address: string): Promise<Location[]> {
    return await db
      .select()
      .from(locations)
      .where(eq(locations.addressLine1, address))
      .orderBy(desc(locations.createdAt));
  }

  // Get locations by city
  static async getLocationsByCity(city: string): Promise<Location[]> {
    return await db
      .select()
      .from(locations)
      .where(eq(locations.city, city))
      .orderBy(desc(locations.createdAt));
  }

  // Get locations by state
  static async getLocationsByState(state: string): Promise<Location[]> {
    return await db
      .select()
      .from(locations)
      .where(eq(locations.state, state))
      .orderBy(desc(locations.createdAt));
  }

  // Get locations by country
  static async getLocationsByCountry(country: string): Promise<Location[]> {
    return await db
      .select()
      .from(locations)
      .where(eq(locations.country, country))
      .orderBy(desc(locations.createdAt));
  }

  // Get locations by postal code
  static async getLocationsByPostalCode(
    postalCode: string
  ): Promise<Location[]> {
    return await db
      .select()
      .from(locations)
      .where(eq(locations.postalCode, postalCode))
      .orderBy(desc(locations.createdAt));
  }

  // Get locations by phone number
  static async getLocationsByPhoneNumber(
    phoneNumber: string
  ): Promise<Location[]> {
    return await db
      .select()
      .from(locations)
      .where(eq(locations.phoneNumber, phoneNumber))
      .orderBy(desc(locations.createdAt));
  }

  static async getCreatedLocations(tenantId: number): Promise<Location[]> {
    return await db
      .select()
      .from(locations)
      .where(and(eq(locations.tenantId, tenantId)))
      .orderBy(desc(locations.createdAt));
  }

  static async bulkCreate(
    locationsData: Omit<NewLocation, "id" | "createdAt">[]
  ): Promise<Location[]> {
    const locationsWithIds = locationsData.map((data) => ({
      ...data,
      id: createId(),
    }));

    return await db.insert(locations).values(locationsWithIds).returning();
  }

  static async getStats(tenantId: number) {
    const [totalLocations] = await db
      .select({ count: locations.id })
      .from(locations)
      .where(eq(locations.tenantId, tenantId));

    return {
      totalLocations: totalLocations?.count || 0,
      activeLocations: 0,
      inactiveLocations: 0,
    };
  }

  static formatLocations(location: Location): string {
    const parts = [
      location.id,
      location.name,
      location.addressLine1,
      location.addressLine2,
      location.city,
      location.state,
      location.postalCode,
      location.country,
    ].filter(Boolean);

    return parts.join(", ");
  }
}
