"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import { Search, X, MapPin, Users, Check } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useLocations } from "@/lib/hooks/queries/use-location-queries";

interface LocationAccessSelectorProps {
  tenantId: number;
  selectedLocations: Array<{
    id: string;
    name: string;
    access_level: "full" | "read_only" | "restricted";
    assigned_at: string;
  }>;
  onSelectionChange: (locations: Array<{
    id: string;
    name: string;
    access_level: "full" | "read_only" | "restricted";
    assigned_at: string;
  }>) => void;
  maxSelections?: number;
  placeholder?: string;
  defaultAccessLevel?: "full" | "read_only" | "restricted";
}

export function LocationAccessSelector({
  tenantId,
  selectedLocations,
  onSelectionChange,
  maxSelections = 20,
  placeholder = "Search and select locations...",
  defaultAccessLevel = "read_only"
}: LocationAccessSelectorProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [isOpen, setIsOpen] = useState(false);
  const [selectedAccessLevel, setSelectedAccessLevel] = useState<"full" | "read_only" | "restricted">(defaultAccessLevel);

  // Fetch locations untuk tenant ini
  const { data: locations = [], isLoading, error } = useLocations(tenantId);

  // Filter locations yang belum dipilih dan sesuai search
  const availableLocations = locations.filter((location: any) => {
    // Skip jika sudah dipilih
    if (selectedLocations.some(selected => selected.id === location.id)) {
      return false;
    }
    
    // Filter berdasarkan search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      return location.name.toLowerCase().includes(query) ||
             location.address?.toLowerCase().includes(query);
    }
    
    return true;
  });

  // Handle location selection
  const handleSelectLocation = (location: any) => {
    if (selectedLocations.length >= maxSelections) {
      return; // Sudah mencapai limit
    }

    const newLocation = {
      id: location.id,
      name: location.name,
      access_level: selectedAccessLevel,
      assigned_at: new Date().toISOString(),
    };

    onSelectionChange([...selectedLocations, newLocation]);
    setSearchQuery(""); // Clear search setelah select
  };

  // Handle remove location
  const handleRemoveLocation = (locationId: string) => {
    onSelectionChange(selectedLocations.filter(loc => loc.id !== locationId));
  };

  // Handle update access level
  const handleUpdateAccessLevel = (locationId: string, newAccessLevel: "full" | "read_only" | "restricted") => {
    const updatedLocations = selectedLocations.map(loc =>
      loc.id === locationId ? { ...loc, access_level: newAccessLevel } : loc
    );
    onSelectionChange(updatedLocations);
  };

  // Handle select all locations
  const handleSelectAll = () => {
    const remainingSlots = maxSelections - selectedLocations.length;
    const locationsToAdd = availableLocations.slice(0, remainingSlots).map((location: any) => ({
      id: location.id,
      name: location.name,
      access_level: selectedAccessLevel,
      assigned_at: new Date().toISOString(),
    }));

    onSelectionChange([...selectedLocations, ...locationsToAdd]);
  };

  // Get access level badge color
  const getAccessLevelColor = (level: string) => {
    switch (level) {
      case "full": return "bg-green-100 text-green-800";
      case "read_only": return "bg-blue-100 text-blue-800";
      case "restricted": return "bg-yellow-100 text-yellow-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  // Get access level display name
  const getAccessLevelDisplay = (level: string) => {
    switch (level) {
      case "full": return "Full Access";
      case "read_only": return "Read Only";
      case "restricted": return "Restricted";
      default: return level;
    }
  };

  return (
    <div className="space-y-4">
      {/* Header dengan info */}
      <div className="flex items-center justify-between">
        <Label className="flex items-center gap-2">
          <MapPin className="h-4 w-4" />
          Location Access
          {selectedLocations.length > 0 && (
            <Badge variant="secondary" className="text-xs">
              {selectedLocations.length} selected
            </Badge>
          )}
        </Label>
        
        {availableLocations.length > 0 && selectedLocations.length < maxSelections && (
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={handleSelectAll}
            className="text-xs"
          >
            <Users className="h-3 w-3 mr-1" />
            Select All ({Math.min(availableLocations.length, maxSelections - selectedLocations.length)})
          </Button>
        )}
      </div>

      {/* Access level selector */}
      <div className="flex items-center gap-2">
        <Label className="text-sm">Default Access Level:</Label>
        <Select value={selectedAccessLevel} onValueChange={(value: any) => setSelectedAccessLevel(value)}>
          <SelectTrigger className="w-40">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="full">Full Access</SelectItem>
            <SelectItem value="read_only">Read Only</SelectItem>
            <SelectItem value="restricted">Restricted</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Search input */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        <Input
          type="text"
          placeholder={placeholder}
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          onFocus={() => setIsOpen(true)}
          className="pl-10"
        />
      </div>

      {/* Selected locations */}
      {selectedLocations.length > 0 && (
        <div className="space-y-2">
          <Label className="text-sm text-gray-600">Selected Locations:</Label>
          <div className="space-y-2">
            {selectedLocations.map((location) => (
              <motion.div
                key={location.id}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                className="flex items-center justify-between p-2 bg-gray-50 rounded"
              >
                <div className="flex items-center gap-2">
                  <Check className="h-4 w-4 text-green-600" />
                  <span className="font-medium text-sm">{location.name}</span>
                  <Badge className={`text-xs ${getAccessLevelColor(location.access_level)}`}>
                    {getAccessLevelDisplay(location.access_level)}
                  </Badge>
                </div>
                
                <div className="flex items-center gap-2">
                  <Select
                    value={location.access_level}
                    onValueChange={(value: any) => handleUpdateAccessLevel(location.id, value)}
                  >
                    <SelectTrigger className="w-32 h-8">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="full">Full Access</SelectItem>
                      <SelectItem value="read_only">Read Only</SelectItem>
                      <SelectItem value="restricted">Restricted</SelectItem>
                    </SelectContent>
                  </Select>
                  
                  <button
                    type="button"
                    onClick={() => handleRemoveLocation(location.id)}
                    className="p-1 hover:bg-red-500 hover:text-white rounded transition-colors"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {/* Available locations dropdown */}
      {isOpen && (searchQuery || availableLocations.length > 0) && (
        <Card className="border shadow-lg">
          <CardContent className="p-2 max-h-60 overflow-y-auto">
            {isLoading ? (
              <div className="flex items-center justify-center py-4">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                <span className="ml-2 text-sm text-gray-600">Loading locations...</span>
              </div>
            ) : availableLocations.length === 0 ? (
              <div className="text-center py-4 text-sm text-gray-500">
                {searchQuery ? "No locations found matching your search" : "No available locations"}
              </div>
            ) : (
              <div className="space-y-1">
                {availableLocations.map((location: any) => (
                  <button
                    key={location.id}
                    type="button"
                    onClick={() => handleSelectLocation(location)}
                    disabled={selectedLocations.length >= maxSelections}
                    className="w-full text-left p-2 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-medium text-sm">{location.name}</div>
                        {location.address && (
                          <div className="text-xs text-gray-500">{location.address}</div>
                        )}
                        <div className="text-xs text-gray-400 mt-1">
                          Will be assigned: {getAccessLevelDisplay(selectedAccessLevel)}
                        </div>
                      </div>
                      <MapPin className="h-4 w-4 text-gray-400" />
                    </div>
                  </button>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Click outside to close */}
      {isOpen && (
        <div
          className="fixed inset-0 z-10"
          onClick={() => setIsOpen(false)}
        />
      )}

      {/* Info text */}
      <p className="text-xs text-gray-500">
        Select locations and access levels for this user. 
        {maxSelections && ` Maximum ${maxSelections} locations allowed.`}
        {selectedLocations.length >= maxSelections && (
          <span className="text-orange-600 font-medium"> Limit reached!</span>
        )}
      </p>
    </div>
  );
}
