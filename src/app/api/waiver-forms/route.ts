import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { z } from "zod";
import { WaiverFormService } from "@/lib/services/waiver-forms.service";

// Validation schema for creating waiver form
export const createWaiverFormSchema = z.object({
  tenantId: z.number().int().positive("Tenant ID must be positive"),
  name: z.string().min(1, "Name is required").max(255, "Name too long"),
  description: z.string().max(1000, "Description too long").optional(),
  content: z.string().min(1, "Content is required"),
  version: z.string().max(50, "Version too long").optional(),
  isActive: z.boolean().optional(),
  isRequired: z.boolean().optional(),
  expiryDays: z.number().int().min(1, "Expiry days must be at least 1").optional(),
  sortOrder: z.number().int().min(0).optional(),
});

// Validation schema for bulk operations
export const bulkOperationSchema = z.object({
  ids: z.array(z.string().min(1)),
  action: z.enum(["activate", "deactivate", "require", "unrequire", "delete"]),
});

const waiverFormService = new WaiverFormService();

// GET /api/waiver-forms - Get waiver forms list
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const tenantIdParam = searchParams.get("tenantId");
    const activeOnly = searchParams.get("activeOnly") === "true";
    const requiredOnly = searchParams.get("requiredOnly") === "true";
    const search = searchParams.get("search");
    const limit = searchParams.get("limit");
    const offset = searchParams.get("offset");

    let waiverForms;
    const options: any = {
      orderField: 'sortOrder',
      orderBy: 'asc',
    };

    if (limit) options.limit = parseInt(limit);
    if (offset) options.offset = parseInt(offset);

    if (tenantIdParam) {
      const tenantIdNum = parseInt(tenantIdParam);
      if (isNaN(tenantIdNum)) {
        return NextResponse.json(
          { error: "Invalid tenant ID" },
          { status: 400 }
        );
      }
      
      options.tenantId = tenantIdNum;
      
      if (activeOnly && requiredOnly) {
        waiverForms = await waiverFormService.getRequiredByTenantId(tenantIdNum);
      } else if (activeOnly) {
        waiverForms = await waiverFormService.getActiveByTenantId(tenantIdNum);
      } else {
        if (search) {
          waiverForms = await waiverFormService.search(search, tenantIdNum);
        } else {
          waiverForms = await waiverFormService.getByTenantId(tenantIdNum, options);
        }
      }
    } else {
      if (search) {
        waiverForms = await waiverFormService.search(search);
      } else {
        // Apply filters
        const filters: any = {};
        if (activeOnly) filters.isActive = true;
        if (requiredOnly) filters.isRequired = true;
        
        options.filters = filters;
        waiverForms = await waiverFormService.getAll(options);
      }
    }

    return NextResponse.json({ data: waiverForms });
  } catch (error) {
    console.error("Error fetching waiver forms:", error);
    return NextResponse.json(
      { error: "Failed to fetch waiver forms" },
      { status: 500 }
    );
  }
}

// POST /api/waiver-forms - Create waiver form
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = createWaiverFormSchema.parse(body);

    const data = await waiverFormService.create(validatedData);

    return NextResponse.json({ data }, { status: 201 });
  } catch (error) {
    console.error("Error creating waiver form:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to create waiver form" },
      { status: 500 }
    );
  }
}

// PUT /api/waiver-forms - Bulk operations
export async function PUT(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = bulkOperationSchema.parse(body);

    let result;
    switch (validatedData.action) {
      case "activate":
        result = await waiverFormService.bulkToggleActive(validatedData.ids, true);
        break;
      case "deactivate":
        result = await waiverFormService.bulkToggleActive(validatedData.ids, false);
        break;
      case "require":
        result = await waiverFormService.bulkToggleRequired(validatedData.ids, true);
        break;
      case "unrequire":
        result = await waiverFormService.bulkToggleRequired(validatedData.ids, false);
        break;
      case "delete":
        result = await waiverFormService.bulkDelete(validatedData.ids);
        break;
      default:
        return NextResponse.json(
          { error: "Invalid action" },
          { status: 400 }
        );
    }

    return NextResponse.json({
      message: `Successfully ${validatedData.action}d ${result.length} waiver forms`,
      data: result
    });
  } catch (error) {
    console.error("Error in bulk operation:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to perform bulk operation" },
      { status: 500 }
    );
  }
}
