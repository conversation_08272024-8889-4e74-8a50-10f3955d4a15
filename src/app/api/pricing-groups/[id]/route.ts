import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { z } from "zod";
import { PricingGroupService } from "@/lib/services/pricing-groups.service";

// Validation schema for updating pricing group
const updatePricingGroupSchema = z.object({
  tenantId: z.number().int().positive().optional(),
  name: z.string().min(1).max(255).optional(),
  description: z.string().max(1000).optional(),
  discountPercentage: z.number().int().min(0).max(100).optional(),
  isActive: z.boolean().optional(),
  isDefault: z.boolean().optional(),
  sortOrder: z.number().int().min(0).optional(),
});

const pricingGroupService = new PricingGroupService();

// PUT /api/pricing-groups/[id] - Update pricing group
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id: pricingGroupId } = await params;
    if (!pricingGroupId) {
      return NextResponse.json(
        { error: "Pricing group ID is required" },
        { status: 400 }
      );
    }

    const body = await request.json();
    const validatedData = updatePricingGroupSchema.parse(body);

    const updatedPricingGroup = await pricingGroupService.update(pricingGroupId, validatedData);

    return NextResponse.json(
      { 
        message: "Pricing group updated successfully", 
        data: updatedPricingGroup 
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error updating pricing group:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid data", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to update pricing group" },
      { status: 500 }
    );
  }
}

// DELETE /api/pricing-groups/[id] - Delete pricing group
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id: pricingGroupId } = await params;
    if (!pricingGroupId) {
      return NextResponse.json(
        { error: "Pricing group ID is required" },
        { status: 400 }
      );
    }

    const deletedPricingGroup = await pricingGroupService.delete(pricingGroupId);

    return NextResponse.json(
      {
        message: "Pricing group deleted successfully",
        data: deletedPricingGroup
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error deleting pricing group:", error);
    return NextResponse.json(
      { error: "Failed to delete pricing group" },
      { status: 500 }
    );
  }
}

// GET /api/pricing-groups/[id] - Get single pricing group
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id: pricingGroupId } = await params;
    if (!pricingGroupId) {
      return NextResponse.json(
        { error: "Pricing group ID is required" },
        { status: 400 }
      );
    }

    const pricingGroup = await pricingGroupService.getById(pricingGroupId);

    if (!pricingGroup) {
      return NextResponse.json(
        { error: "Pricing group not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { data: pricingGroup },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error fetching pricing group:", error);
    return NextResponse.json(
      { error: "Failed to fetch pricing group" },
      { status: 500 }
    );
  }
}

// PATCH /api/pricing-groups/[id] - Toggle status (active/default)
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id: pricingGroupId } = await params;
    if (!pricingGroupId) {
      return NextResponse.json(
        { error: "Pricing group ID is required" },
        { status: 400 }
      );
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get("action");

    let updatedPricingGroup;
    let message;

    switch (action) {
      case "toggle-active":
        updatedPricingGroup = await pricingGroupService.toggleActive(pricingGroupId);
        message = `Pricing group ${updatedPricingGroup.isActive ? 'activated' : 'deactivated'} successfully`;
        break;
      case "toggle-default":
        updatedPricingGroup = await pricingGroupService.toggleDefault(pricingGroupId);
        message = `Pricing group ${updatedPricingGroup.isDefault ? 'set as default' : 'unset as default'} successfully`;
        break;
      default:
        return NextResponse.json(
          { error: "Invalid action. Use 'toggle-active' or 'toggle-default'" },
          { status: 400 }
        );
    }

    return NextResponse.json(
      {
        message,
        data: updatedPricingGroup
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error toggling pricing group status:", error);
    return NextResponse.json(
      { error: "Failed to toggle pricing group status" },
      { status: 500 }
    );
  }
}
