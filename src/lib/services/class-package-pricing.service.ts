import { db } from "@/lib/db";
import { class_package_pricing, package_pricing, packages, type ClassPackagePricing, type NewClassPackagePricing } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";

/**
 * Class Package Pricing Service
 * 
 * Service untuk manage relationship antara classes dan package pricing.
 * Ini handle access control berdasarkan package pricing yang ada.
 */

export class ClassPackagePricingService {
  /**
   * Get package pricing IDs for a class
   */
  static async getPackagePricingIdsByClassId(classId: string): Promise<string[]> {
    const results = await db
      .select({ package_pricing_id: class_package_pricing.package_pricing_id })
      .from(class_package_pricing)
      .where(eq(class_package_pricing.class_id, classId));

    return results.map(r => r.package_pricing_id);
  }

  /**
   * Get package pricing details for a class
   */
  static async getPackagePricingByClassId(classId: string) {
    const results = await db
      .select({
        id: package_pricing.id,
        package_id: package_pricing.package_id,
        pricing_group_id: package_pricing.pricing_group_id,
        price: package_pricing.price,
        credit_amount: package_pricing.credit_amount,
        currency: package_pricing.currency,
        package_name: packages.name,
      })
      .from(class_package_pricing)
      .innerJoin(package_pricing, eq(class_package_pricing.package_pricing_id, package_pricing.id))
      .innerJoin(packages, eq(package_pricing.package_id, packages.id))
      .where(eq(class_package_pricing.class_id, classId));

    return results;
  }

  /**
   * Set package pricing access for a class
   */
  static async setClassPackagePricing(classId: string, packagePricingIds: string[]): Promise<void> {
    // First, remove existing relationships
    await db
      .delete(class_package_pricing)
      .where(eq(class_package_pricing.class_id, classId));

    // Then, add new relationships
    if (packagePricingIds.length > 0) {
      const insertData: NewClassPackagePricing[] = packagePricingIds.map(packagePricingId => ({
        class_id: classId,
        package_pricing_id: packagePricingId,
      }));

      await db.insert(class_package_pricing).values(insertData);
    }
  }

  /**
   * Add package pricing access to a class
   */
  static async addPackagePricingToClass(classId: string, packagePricingId: string): Promise<ClassPackagePricing> {
    // Check if relationship already exists
    const existing = await db
      .select()
      .from(class_package_pricing)
      .where(and(
        eq(class_package_pricing.class_id, classId),
        eq(class_package_pricing.package_pricing_id, packagePricingId)
      ))
      .limit(1);

    if (existing.length > 0) {
      return existing[0];
    }

    const [result] = await db
      .insert(class_package_pricing)
      .values({
        class_id: classId,
        package_pricing_id: packagePricingId,
      })
      .returning();

    return result;
  }

  /**
   * Remove package pricing access from a class
   */
  static async removePackagePricingFromClass(classId: string, packagePricingId: string): Promise<void> {
    await db
      .delete(class_package_pricing)
      .where(and(
        eq(class_package_pricing.class_id, classId),
        eq(class_package_pricing.package_pricing_id, packagePricingId)
      ));
  }

  /**
   * Get classes that can be accessed by a package pricing
   */
  static async getClassesByPackagePricingId(packagePricingId: string): Promise<string[]> {
    const results = await db
      .select({ class_id: class_package_pricing.class_id })
      .from(class_package_pricing)
      .where(eq(class_package_pricing.package_pricing_id, packagePricingId));

    return results.map(r => r.class_id);
  }

  /**
   * Check if a package pricing has access to a class
   */
  static async hasAccess(classId: string, packagePricingId: string): Promise<boolean> {
    const result = await db
      .select()
      .from(class_package_pricing)
      .where(and(
        eq(class_package_pricing.class_id, classId),
        eq(class_package_pricing.package_pricing_id, packagePricingId)
      ))
      .limit(1);

    return result.length > 0;
  }

  /**
   * Get access control stats for a class
   */
  static async getClassAccessStats(classId: string) {
    const packagePricings = await this.getPackagePricingByClassId(classId);
    const uniquePackages = new Set(packagePricings.map(p => p.package_id));

    return {
      totalPackagePricings: packagePricings.length,
      uniquePackages: uniquePackages.size,
      packagePricings: packagePricings,
    };
  }
}
