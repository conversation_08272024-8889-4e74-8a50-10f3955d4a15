import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { z } from "zod";
import { 
  ClassBookingService, 
  type UpdateClassBookingData 
} from "@/lib/services/class-booking.service";

// Validation schema for update
const updateClassBookingSchema = z.object({
  scheduleId: z.string().optional(),
  classId: z.string().optional(),
  customerId: z.string().optional(),
  bookedByUserId: z.string().optional(),
  bookedByCustomerId: z.string().optional(),
  status: z.string().optional(),
  isWaitlist: z.boolean().optional(),
  waitlistPosition: z.number().int().min(1).optional(),
  paymentStatus: z.string().optional(),
  paymentMethod: z.string().optional(),
  creditsUsed: z.number().int().min(0).optional(),
  checkInTime: z.string().optional(),
  cancelTime: z.string().optional(),
  cancellationReason: z.string().max(255).optional(),
  notes: z.string().max(255).optional(),
});

/**
 * GET /api/class-bookings/[id]
 * Get specific class booking by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = params;

    console.log(`📅 [GET /api/class-bookings/${id}] Fetching booking`);

    // Check permissions
    if (session.user.role !== "admin" && !session.user.permissions?.includes("bookings.read")) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      );
    }

    const booking = await ClassBookingService.getById(id);

    if (!booking) {
      return NextResponse.json(
        { error: "Class booking not found" },
        { status: 404 }
      );
    }

    // Tenant isolation - pastikan user hanya bisa akses booking dari tenant mereka
    if (session.user.role !== "admin" && booking.tenantId !== session.user.tenantId) {
      return NextResponse.json(
        { error: "Access denied" },
        { status: 403 }
      );
    }

    return NextResponse.json({
      success: true,
      data: booking
    });
  } catch (error) {
    console.error(`📅 [GET /api/class-bookings/${params.id}] Error:`, error);

    return NextResponse.json(
      { error: "Failed to fetch class booking" },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/class-bookings/[id]
 * Update specific class booking
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = params;
    const body = await request.json();
    const validatedData = updateClassBookingSchema.parse(body);

    console.log(`📅 [PUT /api/class-bookings/${id}] Updating booking:`, validatedData);

    // Check permissions
    if (session.user.role !== "admin" && !session.user.permissions?.includes("bookings.manage")) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      );
    }

    // Get existing booking untuk tenant isolation check
    const existingBooking = await ClassBookingService.getById(id);
    if (!existingBooking) {
      return NextResponse.json(
        { error: "Class booking not found" },
        { status: 404 }
      );
    }

    // Tenant isolation
    if (session.user.role !== "admin" && existingBooking.tenantId !== session.user.tenantId) {
      return NextResponse.json(
        { error: "Access denied" },
        { status: 403 }
      );
    }

    // Convert date strings to Date objects if needed
    const updateData: UpdateClassBookingData = { ...validatedData };
    if (validatedData.checkInTime) {
      updateData.checkInTime = new Date(validatedData.checkInTime);
    }
    if (validatedData.cancelTime) {
      updateData.cancelTime = new Date(validatedData.cancelTime);
    }

    const updatedBooking = await ClassBookingService.update(id, updateData);

    if (!updatedBooking) {
      return NextResponse.json(
        { error: "Failed to update class booking" },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: updatedBooking,
      message: "Class booking updated successfully"
    });
  } catch (error) {
    console.error(`📅 [PUT /api/class-bookings/${params.id}] Error:`, error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid booking data", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to update class booking" },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/class-bookings/[id]
 * Delete specific class booking
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = params;

    console.log(`📅 [DELETE /api/class-bookings/${id}] Deleting booking`);

    // Check permissions
    if (session.user.role !== "admin" && !session.user.permissions?.includes("bookings.manage")) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      );
    }

    // Get existing booking untuk tenant isolation check
    const existingBooking = await ClassBookingService.getById(id);
    if (!existingBooking) {
      return NextResponse.json(
        { error: "Class booking not found" },
        { status: 404 }
      );
    }

    // Tenant isolation
    if (session.user.role !== "admin" && existingBooking.tenantId !== session.user.tenantId) {
      return NextResponse.json(
        { error: "Access denied" },
        { status: 403 }
      );
    }

    const deletedBooking = await ClassBookingService.delete(id);

    return NextResponse.json({
      success: true,
      data: deletedBooking,
      message: "Class booking deleted successfully"
    });
  } catch (error) {
    console.error(`📅 [DELETE /api/class-bookings/${params.id}] Error:`, error);

    if (error instanceof Error && error.message.includes("not found")) {
      return NextResponse.json(
        { error: "Class booking not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { error: "Failed to delete class booking" },
      { status: 500 }
    );
  }
}
