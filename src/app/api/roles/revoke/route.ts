import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { RoleService } from "@/lib/services/role.service";
import { withRBAC } from "@/lib/middleware/rbac-middleware";

/**
 * API Routes untuk Role Revocation
 */

const revokeRoleSchema = z.object({
  userId: z.string().min(1, "User ID is required"),
  roleId: z.string().min(1, "Role ID is required"),
  tenantId: z.number().int().positive("Tenant ID must be positive").optional().nullable(),
});

/**
 * POST /api/roles/revoke
 * Revoke role from user
 */
export const POST = withRBAC(
  async (request: NextRequest) => {
    try {
      const body = await request.json();
      
      // Validate request body
      const validatedData = revokeRoleSchema.parse(body);

      // Call service untuk revoke role
      await RoleService.revokeRole(
        validatedData.userId,
        validatedData.roleId,
        validatedData.tenantId
      );

      return NextResponse.json({
        success: true,
        message: "Role revoked successfully",
      });
    } catch (error) {
      console.error("POST /api/roles/revoke error:", error);
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { success: false, error: "Validation failed", details: error.errors },
          { status: 400 }
        );
      }

      return NextResponse.json(
        { success: false, error: "Internal server error" },
        { status: 500 }
      );
    }
  },
  { module: "roles", action: "revoke" }
);
