"use client";

import { useState } from "react";
import { CustomerOAuthLogin, useCustomerOAuth } from "@/components/auth/customer-oauth-login";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, XCircle, Loader2 } from "lucide-react";

/**
 * Test Page untuk Customer OAuth Authentication
 * 
 * Halaman ini untuk testing dan demonstrasi sistem Customer OAuth
 * yang baru (bukan NextAuth)
 */
export default function TestOAuthPage() {
  const [tenantId, setTenantId] = useState(1);
  const [testResults, setTestResults] = useState<any[]>([]);
  const [isTestingAPI, setIsTestingAPI] = useState(false);
  const { initiateOAuth, isLoading } = useCustomerOAuth();

  const addTestResult = (test: string, success: boolean, message: string, data?: any) => {
    setTestResults(prev => [...prev, {
      test,
      success,
      message,
      data,
      timestamp: new Date().toLocaleTimeString()
    }]);
  };

  const testOAuthAPI = async () => {
    setIsTestingAPI(true);
    setTestResults([]);

    try {
      // Test 1: Valid OAuth initialization
      addTestResult("OAuth Init", false, "Testing...", null);
      
      const result = await initiateOAuth(tenantId, 'web');
      
      if (result.success) {
        addTestResult("OAuth Init", true, "✅ OAuth initialization successful!", {
          authUrl: result.authUrl,
          state: result.state
        });
      } else {
        addTestResult("OAuth Init", false, `❌ ${result.error}`, null);
      }

      // Test 2: Test with invalid tenant ID
      addTestResult("Invalid Tenant", false, "Testing invalid tenant ID...", null);
      
      const invalidResult = await fetch('/api/auth/customer/google/init', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          tenantId: -1,
          clientType: 'web'
        })
      });

      if (invalidResult.status === 400) {
        addTestResult("Invalid Tenant", true, "✅ Correctly rejected invalid tenant ID", null);
      } else {
        addTestResult("Invalid Tenant", false, "❌ Should have rejected invalid tenant ID", null);
      }

    } catch (error) {
      addTestResult("API Test", false, `❌ Error: ${error}`, null);
    } finally {
      setIsTestingAPI(false);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-4xl mx-auto px-4 space-y-6">
        
        {/* Header */}
        <Card>
          <CardHeader>
            <CardTitle>🧪 Customer OAuth Testing Page</CardTitle>
            <CardDescription>
              Test halaman untuk sistem Customer OAuth Authentication yang baru.
              Sistem ini menggunakan JWT tokens dan PKCE untuk keamanan maksimal.
            </CardDescription>
          </CardHeader>
        </Card>

        {/* Configuration */}
        <Card>
          <CardHeader>
            <CardTitle>⚙️ Configuration</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="tenantId">Tenant ID</Label>
              <Input
                id="tenantId"
                type="number"
                value={tenantId}
                onChange={(e) => setTenantId(parseInt(e.target.value) || 1)}
                className="w-32"
              />
            </div>
          </CardContent>
        </Card>

        {/* OAuth Login Test */}
        <Card>
          <CardHeader>
            <CardTitle>🔐 OAuth Login Test</CardTitle>
            <CardDescription>
              Test the complete OAuth flow dengan Google. Ini akan redirect ke Google
              dan kembali ke callback page.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <CustomerOAuthLogin 
              tenantId={tenantId}
              redirectPath="/test-oauth"
              className="w-full max-w-sm"
            />
          </CardContent>
        </Card>

        {/* API Testing */}
        <Card>
          <CardHeader>
            <CardTitle>🔧 API Testing</CardTitle>
            <CardDescription>
              Test OAuth API endpoints secara langsung tanpa redirect.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex gap-2">
              <Button 
                onClick={testOAuthAPI}
                disabled={isTestingAPI}
                className="flex items-center gap-2"
              >
                {isTestingAPI && <Loader2 className="h-4 w-4 animate-spin" />}
                Test OAuth API
              </Button>
              <Button 
                onClick={clearResults}
                variant="outline"
                disabled={isTestingAPI}
              >
                Clear Results
              </Button>
            </div>

            {/* Test Results */}
            {testResults.length > 0 && (
              <div className="space-y-2">
                <h4 className="font-medium">Test Results:</h4>
                {testResults.map((result, index) => (
                  <div key={index} className="flex items-start gap-2 p-3 border rounded-lg">
                    {result.success ? (
                      <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                    ) : (
                      <XCircle className="h-5 w-5 text-red-500 mt-0.5" />
                    )}
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <Badge variant={result.success ? "default" : "destructive"}>
                          {result.test}
                        </Badge>
                        <span className="text-sm text-gray-500">{result.timestamp}</span>
                      </div>
                      <p className="text-sm mt-1">{result.message}</p>
                      {result.data && (
                        <pre className="text-xs bg-gray-100 p-2 rounded mt-2 overflow-x-auto">
                          {JSON.stringify(result.data, null, 2)}
                        </pre>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Current Session Info */}
        <Card>
          <CardHeader>
            <CardTitle>📊 Current Session</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 text-sm">
              <div>
                <strong>Access Token:</strong> {
                  typeof window !== 'undefined' && localStorage.getItem('customer_access_token') 
                    ? '✅ Present' 
                    : '❌ Not found'
                }
              </div>
              <div>
                <strong>Refresh Token:</strong> {
                  typeof window !== 'undefined' && localStorage.getItem('customer_refresh_token') 
                    ? '✅ Present' 
                    : '❌ Not found'
                }
              </div>
              <div>
                <strong>Customer Data:</strong> {
                  typeof window !== 'undefined' && localStorage.getItem('customer_data') 
                    ? '✅ Present' 
                    : '❌ Not found'
                }
              </div>
            </div>
            
            {typeof window !== 'undefined' && localStorage.getItem('customer_data') && (
              <div className="mt-4">
                <h5 className="font-medium mb-2">Customer Info:</h5>
                <pre className="text-xs bg-gray-100 p-2 rounded overflow-x-auto">
                  {JSON.stringify(JSON.parse(localStorage.getItem('customer_data') || '{}'), null, 2)}
                </pre>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>📝 Instructions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2 text-sm">
            <p><strong>1. Test OAuth Flow:</strong> Click "Continue with Google" button above</p>
            <p><strong>2. Google Redirect:</strong> You'll be redirected to Google OAuth consent screen</p>
            <p><strong>3. Callback Handling:</strong> After consent, you'll return to our callback page</p>
            <p><strong>4. Token Storage:</strong> JWT tokens will be stored in localStorage</p>
            <p><strong>5. Session Check:</strong> Refresh this page to see stored session data</p>
          </CardContent>
        </Card>

      </div>
    </div>
  );
}
