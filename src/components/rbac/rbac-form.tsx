"use client";

import { ReactNode, useState } from "react";
import { useRBAC } from "@/lib/hooks/use-rbac";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { ShieldX } from "lucide-react";

/**
 * RBAC-aware Form Component
 * 
 * Component ini handle form submission dengan permission checks.
 * Bisa disable fields atau entire form berdasarkan permissions.
 */

interface RBACFormProps {
  /** Form children */
  children: ReactNode;
  /** Required permission untuk submit form */
  submitPermission?: { module: string; action: string };
  /** Required permission untuk edit fields */
  editPermission?: { module: string; action: string };
  /** Required location access */
  requiredLocationId?: string;
  /** Form submit handler */
  onSubmit?: (event: React.FormEvent) => void;
  /** Show permission errors */
  showPermissionErrors?: boolean;
  /** Custom submit button text */
  submitText?: string;
  /** Loading state */
  isLoading?: boolean;
  /** Additional CSS classes */
  className?: string;
}

export function RBACForm({
  children,
  submitPermission,
  editPermission,
  requiredLocationId,
  onSubmit,
  showPermissionErrors = true,
  submitText = "Submit",
  isLoading = false,
  className = "",
}: RBACFormProps) {
  const {
    hasPermission,
    canAccessLocation,
    isSuperAdmin,
    isLoading: rbacLoading,
  } = useRBAC();

  const [permissionError, setPermissionError] = useState<string | null>(null);

  // Check permissions
  const canEdit = !editPermission || hasPermission(editPermission.module, editPermission.action);
  const canSubmit = !submitPermission || hasPermission(submitPermission.module, submitPermission.action);
  const hasLocationAccess = !requiredLocationId || isSuperAdmin() || canAccessLocation(requiredLocationId);

  const canInteract = canEdit && hasLocationAccess;
  const canSubmitForm = canSubmit && hasLocationAccess;

  // Handle form submission
  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    
    if (!canSubmitForm) {
      setPermissionError("Anda tidak memiliki permission untuk menyimpan data ini");
      return;
    }

    setPermissionError(null);
    onSubmit?.(event);
  };

  if (rbacLoading) {
    return <div className="animate-pulse bg-gray-200 h-32 rounded" />;
  }

  return (
    <form onSubmit={handleSubmit} className={className}>
      {/* Permission Error Alert */}
      {permissionError && showPermissionErrors && (
        <Alert variant="destructive" className="mb-4">
          <ShieldX className="h-4 w-4" />
          <AlertDescription>{permissionError}</AlertDescription>
        </Alert>
      )}

      {/* No Edit Permission Alert */}
      {!canEdit && showPermissionErrors && (
        <Alert variant="destructive" className="mb-4">
          <ShieldX className="h-4 w-4" />
          <AlertDescription>
            Anda tidak memiliki permission untuk mengedit data ini
          </AlertDescription>
        </Alert>
      )}

      {/* No Location Access Alert */}
      {!hasLocationAccess && showPermissionErrors && (
        <Alert variant="destructive" className="mb-4">
          <ShieldX className="h-4 w-4" />
          <AlertDescription>
            Anda tidak memiliki akses ke location ini
          </AlertDescription>
        </Alert>
      )}

      {/* Form Fields */}
      <fieldset disabled={!canInteract}>
        {children}
      </fieldset>

      {/* Submit Button */}
      {onSubmit && (
        <div className="mt-6">
          <Button
            type="submit"
            disabled={!canSubmitForm || isLoading}
            className="w-full"
          >
            {isLoading ? "Loading..." : submitText}
          </Button>
        </div>
      )}
    </form>
  );
}

/**
 * RBAC Field Component
 * 
 * Component ini wrap individual form fields dengan permission checks.
 */

interface RBACFieldProps {
  /** Field children */
  children: ReactNode;
  /** Required permission untuk edit field */
  editPermission?: { module: string; action: string };
  /** Required permission untuk view field */
  viewPermission?: { module: string; action: string };
  /** Show field as read-only instead of hiding */
  showAsReadOnly?: boolean;
  /** Hide field completely kalau gak punya permission */
  hideIfNoAccess?: boolean;
}

export function RBACField({
  children,
  editPermission,
  viewPermission,
  showAsReadOnly = true,
  hideIfNoAccess = false,
}: RBACFieldProps) {
  const { hasPermission, isLoading } = useRBAC();

  if (isLoading) {
    return <div className="animate-pulse bg-gray-200 h-10 rounded" />;
  }

  // Check view permission
  const canView = !viewPermission || hasPermission(viewPermission.module, viewPermission.action);
  
  if (!canView) {
    return hideIfNoAccess ? null : (
      <div className="text-sm text-gray-500 italic">
        [Field tersembunyi - tidak ada permission]
      </div>
    );
  }

  // Check edit permission
  const canEdit = !editPermission || hasPermission(editPermission.module, editPermission.action);

  if (!canEdit && showAsReadOnly) {
    return (
      <fieldset disabled>
        {children}
      </fieldset>
    );
  }

  if (!canEdit && hideIfNoAccess) {
    return null;
  }

  return <>{children}</>;
}

/**
 * RBAC Button Component
 * 
 * Component ini wrap buttons dengan permission checks.
 */

interface RBACButtonProps {
  /** Button children */
  children: ReactNode;
  /** Required permission untuk click button */
  permission?: { module: string; action: string };
  /** Required role untuk click button */
  role?: string;
  /** Required location access */
  requiredLocationId?: string;
  /** Button click handler */
  onClick?: () => void;
  /** Button variant */
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
  /** Button size */
  size?: "default" | "sm" | "lg" | "icon";
  /** Additional CSS classes */
  className?: string;
  /** Show tooltip kalau gak punya permission */
  showTooltip?: boolean;
  /** Custom tooltip message */
  tooltipMessage?: string;
}

export function RBACButton({
  children,
  permission,
  role,
  requiredLocationId,
  onClick,
  variant = "default",
  size = "default",
  className = "",
  showTooltip = true,
  tooltipMessage,
}: RBACButtonProps) {
  const {
    hasPermission,
    hasRole,
    canAccessLocation,
    isSuperAdmin,
    isLoading,
  } = useRBAC();

  if (isLoading) {
    return <div className="animate-pulse bg-gray-200 h-10 w-20 rounded" />;
  }

  // Check permissions
  const hasRequiredPermission = !permission || hasPermission(permission.module, permission.action);
  const hasRequiredRole = !role || hasRole(role);
  const hasLocationAccess = !requiredLocationId || isSuperAdmin() || canAccessLocation(requiredLocationId);

  const canClick = hasRequiredPermission && hasRequiredRole && hasLocationAccess;

  if (!canClick) {
    return null; // Hide button kalau gak punya permission
  }

  return (
    <Button
      variant={variant}
      size={size}
      className={className}
      onClick={onClick}
    >
      {children}
    </Button>
  );
}
