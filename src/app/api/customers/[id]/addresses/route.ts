import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { CustomerAddressService } from "@/lib/services/customer-address.service";
import { z } from "zod";

// Validation schema for customer address
const createCustomerAddressSchema = z.object({
  addressLine1: z.string().max(255).optional(),
  addressLine2: z.string().max(255).optional(),
  city: z.string().max(255).optional(),
  state: z.string().max(255).optional(),
  zip: z.string().max(255).optional(),
  country: z.string().max(255).optional(),
  is_primary: z.boolean().optional(),
});

const updateCustomerAddressSchema = createCustomerAddressSchema.partial();

// GET /api/customers/[id]/addresses - Get customer addresses
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id: customerId } = await params;
    if (!customerId) {
      return NextResponse.json(
        { error: "Customer ID is required" },
        { status: 400 }
      );
    }

    const addresses = await CustomerAddressService.getByCustomerId(customerId);

    return NextResponse.json({ addresses });
  } catch (error) {
    console.error("Error fetching customer addresses:", error);
    return NextResponse.json(
      { error: "Failed to fetch customer addresses" },
      { status: 500 }
    );
  }
}

// POST /api/customers/[id]/addresses - Create customer address
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id: customerId } = await params;
    if (!customerId) {
      return NextResponse.json(
        { error: "Customer ID is required" },
        { status: 400 }
      );
    }

    const body = await request.json();
    const validatedData = createCustomerAddressSchema.parse(body);

    const address = await CustomerAddressService.create({
      ...validatedData,
      customerId,
    });

    return NextResponse.json(
      { 
        message: "Customer address created successfully", 
        data: address 
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error creating customer address:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to create customer address" },
      { status: 500 }
    );
  }
}
