CREATE TABLE "class_categories" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"tenant_id" integer NOT NULL,
	"name" varchar(255) NOT NULL,
	"created_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updated_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL
);
--> statement-breakpoint
CREATE TABLE "class_images" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"class_id" varchar(255) NOT NULL,
	"image_url" varchar(255) NOT NULL,
	"upload_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"created_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updated_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL
);
--> statement-breakpoint
CREATE TABLE "class_items_to_bring" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"class_id" varchar(255) NOT NULL,
	"is_required" boolean DEFAULT false NOT NULL,
	"item_name" varchar(255) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "class_membership_plans " (
	"class_id" varchar(255) NOT NULL,
	"membership_plan_id" varchar(255) NOT NULL,
	"created_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updated_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL
);
--> statement-breakpoint
CREATE TABLE "class_package_plans" (
	"class_id" varchar(255) NOT NULL,
	"package_id" varchar(255) NOT NULL,
	"created_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL
);
--> statement-breakpoint
CREATE TABLE "class_payment_options" (
	"class_id" varchar(255) NOT NULL,
	"payment_type" varchar(255) NOT NULL,
	"drop_in_enable" boolean DEFAULT false NOT NULL,
	"drop_in_price" integer,
	"currency" varchar(255),
	"name" varchar(255),
	"created_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updated_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL
);
--> statement-breakpoint
CREATE TABLE "class_subcategories " (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"category_id" varchar(255) NOT NULL,
	"name" varchar(255) NOT NULL,
	"created_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updated_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL
);
--> statement-breakpoint
CREATE TABLE "class_youtube_links" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"class_id" varchar(255) NOT NULL,
	"yt_url" varchar(255) NOT NULL
);
--> statement-breakpoint
CREATE TABLE "membership_plans" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"tenant_id" integer NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" varchar(255),
	"price" integer,
	"currency" varchar(255),
	"duration" integer,
	"duration_unit" varchar(50),
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updated_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL
);
--> statement-breakpoint
ALTER TABLE "classes" ADD COLUMN "category_id" varchar(255) NOT NULL;--> statement-breakpoint
ALTER TABLE "classes" ADD COLUMN "subcategory_id" varchar(255);--> statement-breakpoint
ALTER TABLE "classes" ADD COLUMN "duration_unit" varchar(50);--> statement-breakpoint
ALTER TABLE "classes" ADD COLUMN "level_id" varchar(255);--> statement-breakpoint
ALTER TABLE "classes" ADD COLUMN "delivery_mode" varchar(50);--> statement-breakpoint
ALTER TABLE "classes" ADD COLUMN "is_private" boolean DEFAULT false NOT NULL;--> statement-breakpoint
ALTER TABLE "classes" ADD COLUMN "custom_cancellation_policy" boolean DEFAULT false NOT NULL;--> statement-breakpoint
ALTER TABLE "classes" ADD COLUMN "cancellation_policy_description" varchar(255);--> statement-breakpoint
ALTER TABLE "classes" ADD COLUMN "is_active" boolean DEFAULT true NOT NULL;--> statement-breakpoint
ALTER TABLE "class_categories" ADD CONSTRAINT "class_categories_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "class_images" ADD CONSTRAINT "class_images_class_id_classes_id_fk" FOREIGN KEY ("class_id") REFERENCES "public"."classes"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "class_items_to_bring" ADD CONSTRAINT "class_items_to_bring_class_id_classes_id_fk" FOREIGN KEY ("class_id") REFERENCES "public"."classes"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "class_membership_plans " ADD CONSTRAINT "class_membership_plans _class_id_classes_id_fk" FOREIGN KEY ("class_id") REFERENCES "public"."classes"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "class_membership_plans " ADD CONSTRAINT "class_membership_plans _membership_plan_id_membership_plans_id_fk" FOREIGN KEY ("membership_plan_id") REFERENCES "public"."membership_plans"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "class_package_plans" ADD CONSTRAINT "class_package_plans_class_id_classes_id_fk" FOREIGN KEY ("class_id") REFERENCES "public"."classes"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "class_package_plans" ADD CONSTRAINT "class_package_plans_package_id_package_id_fk" FOREIGN KEY ("package_id") REFERENCES "public"."package"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "class_payment_options" ADD CONSTRAINT "class_payment_options_class_id_classes_id_fk" FOREIGN KEY ("class_id") REFERENCES "public"."classes"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "class_subcategories " ADD CONSTRAINT "class_subcategories _category_id_class_categories_id_fk" FOREIGN KEY ("category_id") REFERENCES "public"."class_categories"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "class_youtube_links" ADD CONSTRAINT "class_youtube_links_class_id_classes_id_fk" FOREIGN KEY ("class_id") REFERENCES "public"."classes"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "membership_plans" ADD CONSTRAINT "membership_plans_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "classes" ADD CONSTRAINT "classes_category_id_class_categories_id_fk" FOREIGN KEY ("category_id") REFERENCES "public"."class_categories"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "classes" ADD CONSTRAINT "classes_subcategory_id_class_subcategories _id_fk" FOREIGN KEY ("subcategory_id") REFERENCES "public"."class_subcategories "("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "classes" ADD CONSTRAINT "classes_level_id_class_levels_id_fk" FOREIGN KEY ("level_id") REFERENCES "public"."class_levels"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "classes" DROP COLUMN "capacity";--> statement-breakpoint
ALTER TABLE "classes" DROP COLUMN "start_time";--> statement-breakpoint
ALTER TABLE "classes" DROP COLUMN "end_time";