/**
 * Public API Routes for Individual Package Pricing
 * 
 * RESTful API endpoints for individual package pricing operations:
 * - GET /api/public/v1/package-pricing/[id] - Get specific package pricing
 * - PUT /api/public/v1/package-pricing/[id] - Update package pricing
 * - DELETE /api/public/v1/package-pricing/[id] - Delete package pricing
 */

import { NextRequest } from 'next/server';
import { AuthMiddleware } from '@/lib/public-api/middleware/auth-middleware';
import { ValidationMiddleware } from '@/lib/public-api/middleware/validation-middleware';
import { PackagePricingService } from '@/lib/services/package-pricing.service';
import { PackagePricingUpdateSchema } from '@/lib/public-api/validation-types';
import { PublicPackagePricing } from '@/lib/public-api/types';
import { z } from 'zod';

const authMiddleware = new AuthMiddleware();
const validationMiddleware = new ValidationMiddleware();
const packagePricingService = new PackagePricingService();

// URL parameter validation schema
const ParamsSchema = z.object({
  id: z.string().min(1, 'ID is required'),
});

/**
 * GET /api/public/v1/package-pricing/[id]
 * Get specific package pricing by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate request
    const authResult = await authMiddleware.authenticate(request, {
      requiredPermission: {
        resource: 'package-pricing',
        action: 'read',
      },
    });

    if (authResult.response) {
      return authResult.response;
    }

    const { context } = authResult;
    const { authContext } = context!;

    // Validate URL parameters
    const validationResult = await validationMiddleware.validate(request, {
      paramsSchema: ParamsSchema,
    });

    if (!validationResult.isValid) {
      return validationResult.response!;
    }

    const { id } = params;

    // Get package pricing by ID
    const packagePricing = await packagePricingService.getById(id);

    if (!packagePricing) {
      return authMiddleware.createSuccessResponse(
        null,
        context!
      );
    }

    // Check tenant access
    if (packagePricing.package?.tenantId !== authContext!.tenantId) {
      return authMiddleware.createSuccessResponse(
        null,
        context!
      );
    }

    // Transform to public API format
    const publicData: PublicPackagePricing = {
      id: packagePricing.id,
      packageId: packagePricing.package_id,
      packageName: packagePricing.package?.name || 'Unknown Package',
      packageDescription: packagePricing.package?.description,
      pricingGroupId: packagePricing.pricing_group_id,
      pricingGroupName: packagePricing.pricing_group?.name || 'Unknown Pricing Group',
      price: packagePricing.price,
      creditAmount: packagePricing.credit_amount,
      currency: packagePricing.currency || 'USD',
      isActive: true,
      createdAt: packagePricing.createdAt?.toISOString() || new Date().toISOString(),
      updatedAt: packagePricing.updatedAt?.toISOString() || new Date().toISOString(),
    };

    return authMiddleware.createSuccessResponse(publicData, context!);

  } catch (error) {
    console.error(`GET /api/public/v1/package-pricing/${params.id} error:`, error);
    
    return authMiddleware.createSuccessResponse(
      null,
      { requestId: 'error', startTime: Date.now(), apiVersion: 'v1', clientInfo: { ip: 'unknown', userAgent: 'unknown' } }
    );
  }
}

/**
 * PUT /api/public/v1/package-pricing/[id]
 * Update specific package pricing
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate request
    const authResult = await authMiddleware.authenticate(request, {
      requiredPermission: {
        resource: 'package-pricing',
        action: 'write',
      },
    });

    if (authResult.response) {
      return authResult.response;
    }

    const { context } = authResult;
    const { authContext } = context!;

    // Validate URL parameters and request body
    const validationResult = await validationMiddleware.validate(request, {
      paramsSchema: ParamsSchema,
      bodySchema: PackagePricingUpdateSchema,
    });

    if (!validationResult.isValid) {
      return validationResult.response!;
    }

    const { id } = params;
    const { body } = validationResult.data!;

    // Check if package pricing exists and belongs to tenant
    const existingPricing = await packagePricingService.getById(id);
    
    if (!existingPricing) {
      return authMiddleware.createSuccessResponse(
        null,
        context!
      );
    }

    if (existingPricing.package?.tenantId !== authContext!.tenantId) {
      return authMiddleware.createSuccessResponse(
        null,
        context!
      );
    }

    // Update package pricing
    const updateData = {
      price: body.price,
      creditAmount: body.creditAmount,
      currency: body.currency,
    };

    const updatedPricing = await packagePricingService.update(id, updateData);

    // Transform to public API format
    const publicData: PublicPackagePricing = {
      id: updatedPricing.id,
      packageId: updatedPricing.package_id,
      packageName: existingPricing.package?.name || 'Unknown Package',
      packageDescription: existingPricing.package?.description,
      pricingGroupId: updatedPricing.pricing_group_id,
      pricingGroupName: existingPricing.pricing_group?.name || 'Unknown Pricing Group',
      price: updatedPricing.price,
      creditAmount: updatedPricing.credit_amount,
      currency: updatedPricing.currency || 'USD',
      isActive: true,
      createdAt: updatedPricing.createdAt?.toISOString() || new Date().toISOString(),
      updatedAt: updatedPricing.updatedAt?.toISOString() || new Date().toISOString(),
    };

    return authMiddleware.createSuccessResponse(publicData, context!);

  } catch (error) {
    console.error(`PUT /api/public/v1/package-pricing/${params.id} error:`, error);
    
    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return authMiddleware.createSuccessResponse(
          null,
          { requestId: 'error', startTime: Date.now(), apiVersion: 'v1', clientInfo: { ip: 'unknown', userAgent: 'unknown' } }
        );
      }
    }

    return authMiddleware.createSuccessResponse(
      null,
      { requestId: 'error', startTime: Date.now(), apiVersion: 'v1', clientInfo: { ip: 'unknown', userAgent: 'unknown' } }
    );
  }
}

/**
 * DELETE /api/public/v1/package-pricing/[id]
 * Delete specific package pricing
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Authenticate request
    const authResult = await authMiddleware.authenticate(request, {
      requiredPermission: {
        resource: 'package-pricing',
        action: 'delete',
      },
    });

    if (authResult.response) {
      return authResult.response;
    }

    const { context } = authResult;
    const { authContext } = context!;

    // Validate URL parameters
    const validationResult = await validationMiddleware.validate(request, {
      paramsSchema: ParamsSchema,
    });

    if (!validationResult.isValid) {
      return validationResult.response!;
    }

    const { id } = params;

    // Check if package pricing exists and belongs to tenant
    const existingPricing = await packagePricingService.getById(id);
    
    if (!existingPricing) {
      return authMiddleware.createSuccessResponse(
        null,
        context!
      );
    }

    if (existingPricing.package?.tenantId !== authContext!.tenantId) {
      return authMiddleware.createSuccessResponse(
        null,
        context!
      );
    }

    // Delete package pricing
    await packagePricingService.delete(id);

    return authMiddleware.createSuccessResponse(
      {
        id,
        message: 'Package pricing deleted successfully',
        deletedAt: new Date().toISOString(),
      },
      context!
    );

  } catch (error) {
    console.error(`DELETE /api/public/v1/package-pricing/${params.id} error:`, error);
    
    if (error instanceof Error) {
      if (error.message.includes('not found')) {
        return authMiddleware.createSuccessResponse(
          null,
          { requestId: 'error', startTime: Date.now(), apiVersion: 'v1', clientInfo: { ip: 'unknown', userAgent: 'unknown' } }
        );
      }
    }

    return authMiddleware.createSuccessResponse(
      null,
      { requestId: 'error', startTime: Date.now(), apiVersion: 'v1', clientInfo: { ip: 'unknown', userAgent: 'unknown' } }
    );
  }
}

/**
 * OPTIONS /api/public/v1/package-pricing/[id]
 * Handle CORS preflight requests
 */
export async function OPTIONS(request: NextRequest) {
  return authMiddleware.handleOptions(request);
}
