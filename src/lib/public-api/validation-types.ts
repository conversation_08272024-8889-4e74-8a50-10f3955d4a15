/**
 * Validation Types for Public API
 * 
 * Comprehensive validation schemas and types
 */

import { z } from 'zod';

// ============================================================================
// Base Validation Schemas
// ============================================================================

export const PaginationSchema = z.object({
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(20),
  sort: z.string().optional(),
  order: z.enum(['asc', 'desc']).default('asc'),
});

export const TimestampSchema = z.object({
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
});

export const IDSchema = z.string().min(1).max(255);

// ============================================================================
// Package Pricing Validation Schemas
// ============================================================================

export const PackagePricingFiltersSchema = z.object({
  packageId: IDSchema.optional(),
  pricingGroupId: IDSchema.optional(),
  minPrice: z.number().min(0).optional(),
  maxPrice: z.number().min(0).optional(),
  currency: z.string().length(3).optional(), // ISO 4217 currency codes
  isActive: z.boolean().optional(),
  search: z.string().min(1).max(255).optional(),
}).refine(
  (data) => !data.minPrice || !data.maxPrice || data.minPrice <= data.maxPrice,
  {
    message: "minPrice must be less than or equal to maxPrice",
    path: ["minPrice"],
  }
);

export const PackagePricingCreateSchema = z.object({
  packageId: IDSchema,
  pricingGroupId: IDSchema,
  price: z.number().min(0).optional(),
  creditAmount: z.number().int().min(0).optional(),
  currency: z.string().length(3).default('USD'),
}).refine(
  (data) => data.price !== undefined || data.creditAmount !== undefined,
  {
    message: "Either price or creditAmount must be provided",
    path: ["price"],
  }
);

export const PackagePricingUpdateSchema = z.object({
  price: z.number().min(0).optional(),
  creditAmount: z.number().int().min(0).optional(),
  currency: z.string().length(3).optional(),
}).refine(
  (data) => Object.keys(data).length > 0,
  {
    message: "At least one field must be provided for update",
  }
);

export const PackagePricingBulkCreateSchema = z.object({
  items: z.array(PackagePricingCreateSchema).min(1).max(100),
});

export const PackagePricingResponseSchema = z.object({
  id: IDSchema,
  packageId: IDSchema,
  packageName: z.string(),
  packageDescription: z.string().optional(),
  pricingGroupId: IDSchema,
  pricingGroupName: z.string(),
  price: z.number().min(0).nullable(),
  creditAmount: z.number().int().min(0).nullable(),
  currency: z.string().length(3),
  isActive: z.boolean(),
}).merge(TimestampSchema);

// ============================================================================
// API Key Validation Schemas
// ============================================================================

export const APIKeyCreateSchema = z.object({
  name: z.string().min(1).max(255),
  permissions: z.array(z.object({
    resource: z.string().min(1),
    actions: z.array(z.string().min(1)),
    conditions: z.record(z.any()).optional(),
  })),
  rateLimit: z.object({
    requestsPerMinute: z.number().int().min(1).max(10000),
    requestsPerHour: z.number().int().min(1).max(100000),
    requestsPerDay: z.number().int().min(1).max(1000000),
    burstLimit: z.number().int().min(1).max(1000),
  }),
  expiresAt: z.string().datetime().optional(),
});

export const APIKeyUpdateSchema = z.object({
  name: z.string().min(1).max(255).optional(),
  permissions: z.array(z.object({
    resource: z.string().min(1),
    actions: z.array(z.string().min(1)),
    conditions: z.record(z.any()).optional(),
  })).optional(),
  rateLimit: z.object({
    requestsPerMinute: z.number().int().min(1).max(10000),
    requestsPerHour: z.number().int().min(1).max(100000),
    requestsPerDay: z.number().int().min(1).max(1000000),
    burstLimit: z.number().int().min(1).max(1000),
  }).optional(),
  isActive: z.boolean().optional(),
  expiresAt: z.string().datetime().optional(),
});

// ============================================================================
// Webhook Validation Schemas
// ============================================================================

export const WebhookEndpointCreateSchema = z.object({
  url: z.string().url(),
  events: z.array(z.string().min(1)).min(1),
  secret: z.string().min(32).max(255).optional(),
  retryPolicy: z.object({
    maxRetries: z.number().int().min(0).max(10).default(3),
    backoffMultiplier: z.number().min(1).max(10).default(2),
    maxBackoffSeconds: z.number().int().min(1).max(3600).default(300),
  }).optional(),
});

export const WebhookEndpointUpdateSchema = z.object({
  url: z.string().url().optional(),
  events: z.array(z.string().min(1)).min(1).optional(),
  isActive: z.boolean().optional(),
  retryPolicy: z.object({
    maxRetries: z.number().int().min(0).max(10),
    backoffMultiplier: z.number().min(1).max(10),
    maxBackoffSeconds: z.number().int().min(1).max(3600),
  }).optional(),
});

// ============================================================================
// Request Validation Schemas
// ============================================================================

export const APIRequestSchema = z.object({
  data: z.any().optional(),
  metadata: z.object({
    requestId: z.string().uuid(),
    timestamp: z.string().datetime(),
    version: z.string().regex(/^v\d+$/),
    clientInfo: z.object({
      userAgent: z.string().optional(),
      ip: z.string().ip().optional(),
      country: z.string().length(2).optional(),
    }).optional(),
  }).optional(),
});

export const BulkOperationSchema = z.object({
  operations: z.array(z.object({
    operation: z.enum(['create', 'update', 'delete']),
    id: IDSchema.optional(),
    data: z.any().optional(),
  })).min(1).max(100),
  continueOnError: z.boolean().default(false),
});

// ============================================================================
// Query Parameter Schemas
// ============================================================================

export const DateRangeSchema = z.object({
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
}).refine(
  (data) => !data.startDate || !data.endDate || new Date(data.startDate) <= new Date(data.endDate),
  {
    message: "startDate must be before or equal to endDate",
    path: ["startDate"],
  }
);

export const SearchSchema = z.object({
  q: z.string().min(1).max(255).optional(),
  fields: z.array(z.string()).optional(),
  fuzzy: z.boolean().default(false),
  highlight: z.boolean().default(false),
});

// ============================================================================
// Response Validation Schemas
// ============================================================================

export const APIResponseSchema = z.object({
  success: z.boolean(),
  data: z.any().optional(),
  error: z.object({
    code: z.string(),
    message: z.string(),
    details: z.record(z.any()).optional(),
    stack: z.string().optional(),
  }).optional(),
  metadata: z.object({
    requestId: z.string().uuid(),
    timestamp: z.string().datetime(),
    version: z.string(),
    processingTime: z.number().min(0),
    rateLimit: z.object({
      remaining: z.number().int().min(0),
      resetTime: z.string().datetime(),
      limit: z.number().int().min(1),
    }).optional(),
  }),
  pagination: z.object({
    page: z.number().int().min(1),
    limit: z.number().int().min(1),
    total: z.number().int().min(0),
    totalPages: z.number().int().min(0),
    hasNext: z.boolean(),
    hasPrev: z.boolean(),
  }).optional(),
});

export const ErrorResponseSchema = z.object({
  success: z.literal(false),
  error: z.object({
    code: z.string(),
    message: z.string(),
    details: z.record(z.any()).optional(),
    stack: z.string().optional(),
  }),
  metadata: z.object({
    requestId: z.string().uuid(),
    timestamp: z.string().datetime(),
    version: z.string(),
    processingTime: z.number().min(0),
  }),
});

// ============================================================================
// Health Check Schemas
// ============================================================================

export const HealthCheckSchema = z.object({
  status: z.enum(['healthy', 'degraded', 'unhealthy']),
  timestamp: z.string().datetime(),
  version: z.string(),
  uptime: z.number().min(0),
  checks: z.object({
    database: z.object({
      status: z.enum(['pass', 'fail', 'warn']),
      responseTime: z.number().min(0).optional(),
      error: z.string().optional(),
    }),
    redis: z.object({
      status: z.enum(['pass', 'fail', 'warn']),
      responseTime: z.number().min(0).optional(),
      error: z.string().optional(),
    }),
    external: z.object({
      status: z.enum(['pass', 'fail', 'warn']),
      responseTime: z.number().min(0).optional(),
      error: z.string().optional(),
    }),
  }),
});

// ============================================================================
// Type Exports
// ============================================================================

export type PaginationInput = z.infer<typeof PaginationSchema>;
export type PackagePricingFilters = z.infer<typeof PackagePricingFiltersSchema>;
export type PackagePricingCreateInput = z.infer<typeof PackagePricingCreateSchema>;
export type PackagePricingUpdateInput = z.infer<typeof PackagePricingUpdateSchema>;
export type PackagePricingBulkCreateInput = z.infer<typeof PackagePricingBulkCreateSchema>;
export type PackagePricingResponse = z.infer<typeof PackagePricingResponseSchema>;
export type APIKeyCreateInput = z.infer<typeof APIKeyCreateSchema>;
export type APIKeyUpdateInput = z.infer<typeof APIKeyUpdateSchema>;
export type WebhookEndpointCreateInput = z.infer<typeof WebhookEndpointCreateSchema>;
export type WebhookEndpointUpdateInput = z.infer<typeof WebhookEndpointUpdateSchema>;
export type APIRequestInput = z.infer<typeof APIRequestSchema>;
export type BulkOperationInput = z.infer<typeof BulkOperationSchema>;
export type DateRangeInput = z.infer<typeof DateRangeSchema>;
export type SearchInput = z.infer<typeof SearchSchema>;
export type APIResponseOutput = z.infer<typeof APIResponseSchema>;
export type ErrorResponseOutput = z.infer<typeof ErrorResponseSchema>;
export type HealthCheckOutput = z.infer<typeof HealthCheckSchema>;

// ============================================================================
// Validation Utilities
// ============================================================================

export class ValidationError extends Error {
  public readonly issues: z.ZodIssue[];
  public readonly statusCode: number = 400;

  constructor(issues: z.ZodIssue[]) {
    const message = issues.map(issue => `${issue.path.join('.')}: ${issue.message}`).join(', ');
    super(`Validation failed: ${message}`);
    this.name = 'ValidationError';
    this.issues = issues;
  }
}

export function validateInput<T>(schema: z.ZodSchema<T>, data: unknown): T {
  const result = schema.safeParse(data);
  if (!result.success) {
    throw new ValidationError(result.error.issues);
  }
  return result.data;
}

export function validateOptionalInput<T>(schema: z.ZodSchema<T>, data: unknown): T | undefined {
  if (data === undefined || data === null) {
    return undefined;
  }
  return validateInput(schema, data);
}
