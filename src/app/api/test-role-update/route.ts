import { NextRequest, NextResponse } from "next/server";
import { RoleService } from "@/lib/services/role.service";

// Test endpoint to verify role update works without auth
export async function POST(request: NextRequest) {
  try {
    console.log("🧪 Testing role update...");
    
    const body = await request.json();
    const { roleId, updateData } = body;
    
    if (!roleId) {
      return NextResponse.json({
        success: false,
        error: "Role ID is required"
      }, { status: 400 });
    }

    console.log(`🔄 Attempting to update role: ${roleId}`);
    console.log("Update data:", updateData);
    
    // Get role first to verify it exists
    const existingRole = await RoleService.getById(roleId);
    if (!existingRole) {
      return NextResponse.json({
        success: false,
        error: "Role not found"
      }, { status: 404 });
    }

    console.log(`📋 Role found: ${existingRole.display_name} (${existingRole.name})`);
    
    // Test update
    const updatedRole = await RoleService.update(roleId, updateData);
    
    console.log("✅ Role update successful:", updatedRole);

    return NextResponse.json({
      success: true,
      message: "Role update test successful!",
      data: {
        roleId,
        originalRole: {
          id: existingRole.id,
          name: existingRole.name,
          display_name: existingRole.display_name,
          description: existingRole.description,
          hierarchy_level: existingRole.hierarchy_level
        },
        updatedRole: {
          id: updatedRole.id,
          name: updatedRole.name,
          display_name: updatedRole.display_name,
          description: updatedRole.description,
          hierarchy_level: updatedRole.hierarchy_level
        },
        updateData
      }
    });
  } catch (error) {
    console.error("❌ Role update test failed:", error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}
