import { Metadata } from "next";
import { getCurrentUser } from "@/lib/auth/utils";
import { redirect } from "next/navigation";
import { ClassBookingManagement } from "@/components/class-bookings/class-booking-management";

export const metadata: Metadata = {
  title: "Class Bookings - TanStack SaaS App",
  description: "Manage class bookings, check-ins, and customer attendance",
};

/**
 * Class Bookings Page
 * 
 * Halaman untuk manage class bookings dengan full CRUD functionality.
 * Mengi<PERSON><PERSON> pattern yang sama dengan halaman locations, equipment, dll.
 * 
 * Features:
 * - Authentication check dengan redirect ke signin jika belum login
 * - RBAC check untuk memastikan user punya permission "bookings.manage"
 * - Tenant isolation - user hanya bisa lihat bookings dari tenant mereka
 * - Integration dengan ClassBookingManagement component
 */

export default async function ClassBookingsPage() {
  const user = await getCurrentUser();

  if (!user) {
    redirect("/auth/signin");
  }

  // Check if user has permission to manage bookings
  const hasBookingPermission = user.permissions?.includes("bookings.manage") || user.role === "admin";
  
  if (!hasBookingPermission) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Access Denied</h1>
          <p className="text-muted-foreground mb-4">
            You don't have permission to manage class bookings.
          </p>
          <a href="/dashboard" className="text-primary hover:underline">
            Back to Dashboard
          </a>
        </div>
      </div>
    );
  }

  // Get tenant ID from user session
  const tenantId = user.tenantId;
  
  if (!tenantId) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Configuration Error</h1>
          <p className="text-muted-foreground mb-4">
            No tenant information found. Please contact support.
          </p>
          <a href="/dashboard" className="text-primary hover:underline">
            Back to Dashboard
          </a>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <ClassBookingManagement tenantId={tenantId} />
    </div>
  );
}
