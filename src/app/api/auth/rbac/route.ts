import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";
import { RoleService } from "@/lib/services/role.service";
import { PermissionService } from "@/lib/services/permission.service";
import { LocationAccessService } from "@/lib/services/location-access.service";

/**
 * GET /api/auth/rbac - Load RBAC data for current user
 * 
 * This API route loads RBAC data (roles, permissions, accessible locations)
 * for the currently authenticated user. This is called from client-side
 * to avoid edge runtime compatibility issues.
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth();

    console.log("RBAC API - Session check:", {
      hasSession: !!session,
      hasUser: !!session?.user,
      userId: session?.user?.id,
      email: session?.user?.email
    });

    if (!session?.user?.id) {
      console.log("RBAC API - Unauthorized: No session or user ID");
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const userId = session.user.id;
    const tenantId = session.user.tenantId;

    console.log("Loading RBAC data for user:", userId, "tenant:", tenantId);

    // Get user roles dengan role names
    const userRolesResult = await RoleService.getUserRoles(tenantId, userId);

    // Get role names dari roleIds
    const roleNames: string[] = [];
    for (const userRole of userRolesResult.userRoles) {
      try {
        const role = await RoleService.getById(userRole.roleId);
        if (role) {
          roleNames.push(role.name);
        }
      } catch (error) {
        console.error(`Error getting role ${userRole.roleId}:`, error);
      }
    }

    // Get user permissions
    const userPermissions = await PermissionService.getUserPermissions(userId, tenantId);
    const permissions = userPermissions.map(p => `${p.module}.${p.action}`);

    // Get accessible locations (jika ada tenantId)
    let accessibleLocations: string[] = [];
    if (tenantId) {
      try {
        const locationAccess = await LocationAccessService.getUserAccessibleLocations(userId, tenantId);
        accessibleLocations = locationAccess.map(la => la.locationId);
      } catch (error) {
        console.error("Error getting accessible locations:", error);
        // Ignore error, just use empty array
      }
    }

    const rbacData = {
      roles: roleNames,
      permissions,
      accessibleLocations,
    };

    console.log("RBAC data loaded:", rbacData);

    return NextResponse.json(rbacData);
  } catch (error) {
    console.error("Error loading RBAC data:", error);
    return NextResponse.json(
      { 
        error: "Failed to load RBAC data",
        details: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    );
  }
}
