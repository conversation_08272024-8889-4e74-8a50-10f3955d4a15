"use client";

import React from "react";
import { QueryErrorResetBoundary } from "@tanstack/react-query";
import { ErrorBoundary } from "react-error-boundary";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  AlertTriangle, 
  RefreshCw, 
  Home, 
  Bug, 
  Zap,
  Database,
  Wifi,
  WifiOff
} from "lucide-react";

interface ErrorFallbackProps {
  error: Error;
  resetErrorBoundary: () => void;
}

function ErrorFallback({ error, resetErrorBoundary }: ErrorFallbackProps) {
  const isNetworkError = error.message.includes("fetch") || error.message.includes("network");
  const isQueryError = error.message.includes("query") || error.message.includes("Query");
  const isAuthError = error.message.includes("Unauthorized") || error.message.includes("401");

  return (
    <div className="min-h-[400px] flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            {isNetworkError ? (
              <WifiOff className="h-12 w-12 text-destructive" />
            ) : isQueryError ? (
              <Database className="h-12 w-12 text-destructive" />
            ) : isAuthError ? (
              <AlertTriangle className="h-12 w-12 text-destructive" />
            ) : (
              <Bug className="h-12 w-12 text-destructive" />
            )}
          </div>
          
          <CardTitle className="flex items-center justify-center gap-2">
            Something went wrong
            <Badge variant="outline" className="bg-destructive/10">
              <Zap className="h-3 w-3 mr-1" />
              TanStack Error
            </Badge>
          </CardTitle>
          
          <CardDescription>
            {isNetworkError && "Network connection error. Please check your internet connection."}
            {isQueryError && "Data loading error. There was a problem fetching data."}
            {isAuthError && "Authentication error. Please sign in again."}
            {!isNetworkError && !isQueryError && !isAuthError && "An unexpected error occurred."}
          </CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* Error Details (Development only) */}
          {process.env.NODE_ENV === 'development' && (
            <div className="p-3 bg-muted rounded-lg">
              <p className="text-sm font-medium mb-2">Error Details (Dev):</p>
              <p className="text-xs text-muted-foreground font-mono break-all">
                {error.message}
              </p>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2">
            <Button 
              onClick={resetErrorBoundary} 
              className="flex-1"
              variant="default"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
            
            <Button 
              onClick={() => window.location.href = '/dashboard'} 
              variant="outline"
              className="flex-1"
            >
              <Home className="h-4 w-4 mr-2" />
              Go Home
            </Button>
          </div>

          {/* Network Status */}
          <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
            {navigator.onLine ? (
              <>
                <Wifi className="h-4 w-4 text-green-600" />
                <span>Connected</span>
              </>
            ) : (
              <>
                <WifiOff className="h-4 w-4 text-destructive" />
                <span>Offline</span>
              </>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

interface GlobalErrorBoundaryProps {
  children: React.ReactNode;
}

export function GlobalErrorBoundary({ children }: GlobalErrorBoundaryProps) {
  return (
    <QueryErrorResetBoundary>
      {({ reset }) => (
        <ErrorBoundary
          FallbackComponent={ErrorFallback}
          onReset={reset}
          onError={(error, errorInfo) => {
            // Log error to monitoring service
            console.error("Global Error Boundary caught an error:", error, errorInfo);
            
            // In production, send to error tracking service
            if (process.env.NODE_ENV === 'production') {
              // Example: Sentry.captureException(error);
            }
          }}
        >
          {children}
        </ErrorBoundary>
      )}
    </QueryErrorResetBoundary>
  );
}

// Loading Fallback Component
interface LoadingFallbackProps {
  message?: string;
}

export function LoadingFallback({ message = "Loading..." }: LoadingFallbackProps) {
  return (
    <div className="min-h-[200px] flex items-center justify-center p-4">
      <Card className="w-full max-w-sm">
        <CardContent className="flex flex-col items-center justify-center py-8">
          <div className="flex items-center gap-2 mb-4">
            <RefreshCw className="h-6 w-6 animate-spin text-primary" />
            <Badge variant="outline" className="bg-primary/10">
              <Zap className="h-3 w-3 mr-1" />
              TanStack Loading
            </Badge>
          </div>
          
          <p className="text-sm text-muted-foreground text-center">
            {message}
          </p>
        </CardContent>
      </Card>
    </div>
  );
}

// Network Status Component
export function NetworkStatus() {
  const [isOnline, setIsOnline] = React.useState(true); // Default to online
  const [showOfflineMessage, setShowOfflineMessage] = React.useState(false);

  React.useEffect(() => {
    // Initialize with current online status
    setIsOnline(navigator.onLine);

    const handleOnline = () => {
      setIsOnline(true);
      setShowOfflineMessage(false);
    };

    const handleOffline = () => {
      setIsOnline(false);
      setShowOfflineMessage(true);
    };

    // Add event listeners
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    // Additional check with a small delay to avoid false positives
    const checkConnection = async () => {
      try {
        const response = await fetch('/api/health', {
          method: 'HEAD',
          cache: 'no-cache'
        });
        if (response.ok) {
          setIsOnline(true);
          setShowOfflineMessage(false);
        }
      } catch {
        // If health check fails, rely on navigator.onLine
        if (!navigator.onLine) {
          setIsOnline(false);
          setShowOfflineMessage(true);
        }
      }
    };

    // Check connection on mount
    checkConnection();

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  // Only show message if explicitly offline and we want to show it
  if (isOnline || !showOfflineMessage) return null;

  return (
    <div className="fixed top-0 left-0 right-0 z-50 bg-destructive text-destructive-foreground p-2">
      <div className="container mx-auto flex items-center justify-center gap-2 text-sm">
        <WifiOff className="h-4 w-4" />
        <span>You're offline. Some features may not work properly.</span>
      </div>
    </div>
  );
}

// Query Status Indicator
interface QueryStatusProps {
  isLoading?: boolean;
  isError?: boolean;
  isFetching?: boolean;
  isStale?: boolean;
}

export function QueryStatus({ isLoading, isError, isFetching, isStale }: QueryStatusProps) {
  if (!isLoading && !isError && !isFetching && !isStale) return null;

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <div className="flex flex-col gap-2">
        {isLoading && (
          <Badge variant="secondary" className="bg-blue-100 text-blue-800">
            <RefreshCw className="h-3 w-3 mr-1 animate-spin" />
            Loading
          </Badge>
        )}
        
        {isFetching && !isLoading && (
          <Badge variant="secondary" className="bg-green-100 text-green-800">
            <Database className="h-3 w-3 mr-1" />
            Syncing
          </Badge>
        )}
        
        {isStale && (
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800">
            <AlertTriangle className="h-3 w-3 mr-1" />
            Stale
          </Badge>
        )}
        
        {isError && (
          <Badge variant="destructive">
            <AlertTriangle className="h-3 w-3 mr-1" />
            Error
          </Badge>
        )}
      </div>
    </div>
  );
}
