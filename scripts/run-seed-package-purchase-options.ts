#!/usr/bin/env tsx
// Simple script to run package purchase options seeding
// Usage: npm run seed:package-purchase-options

import { seedPackagePurchaseOptions } from './seed-package-purchase-options';

async function main() {
  console.log('🚀 Starting Package Purchase Options Seeding...');
  console.log('================================================');
  
  try {
    await seedPackagePurchaseOptions();
    console.log('================================================');
    console.log('🎉 Package Purchase Options seeding completed successfully!');
  } catch (error) {
    console.error('================================================');
    console.error('❌ Package Purchase Options seeding failed:', error);
    process.exit(1);
  }
}

main();
