import { NextRequest } from "next/server";
import { z } from "zod";
import { googleOAuthService } from "@/lib/services/google-oauth.service";
import { customerAuthService } from "@/lib/services/customer-auth.service";
import { 
  createAuthenticatedResponse, 
  createErrorResponse,
  rateLimit,
  addSecurityHeaders 
} from "@/lib/middleware/customer-auth.middleware";
import { rateLimitConfig } from "@/lib/security/rate-limit.config";

/**
 * Handle Google OAuth Callback for Customer Authentication
 * 
 * POST /api/auth/customer/google/callback
 * 
 * Processes OAuth authorization code and returns JWT tokens
 */

const callbackSchema = z.object({
  code: z.string().min(1, "Authorization code is required"),
  state: z.string().min(1, "State parameter is required"),
  tenantId: z.number().int().positive(),
  codeVerifier: z.string().min(43).max(128).optional(), // Required for mobile PKCE
  clientType: z.enum(["web", "mobile"]).default("web"), // ← Changed to match init endpoint
  deviceId: z.string().optional(),
});

export async function POST(request: NextRequest) {
  try {
    // Apply rate limiting
    const clientIP = request.headers.get("x-forwarded-for")?.split(",")[0] || 
                    request.headers.get("x-real-ip") || 
                    request.ip || "unknown";

    const rateLimitResult = rateLimit({
      windowMs: rateLimitConfig.auth.oauth.windowMs,
      max: rateLimitConfig.auth.oauth.max,
      keyGenerator: () => `oauth-callback:${clientIP}`,
    })(request);

    if (!rateLimitResult.allowed) {
      return createErrorResponse(
        "Too many OAuth callback attempts",
        "RATE_LIMIT_EXCEEDED",
        429,
        rateLimitResult.headers
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = callbackSchema.parse(body);

    // Get user agent
    const userAgent = request.headers.get("user-agent") || undefined;

    // Handle OAuth callback
    const oauthResult = await googleOAuthService.handleCallback({
      code: validatedData.code,
      state: validatedData.state,
      codeVerifier: validatedData.codeVerifier,
      tenantId: validatedData.tenantId,
      ipAddress: clientIP,
      userAgent,
    });

    if (!oauthResult.success) {
      return createErrorResponse(
        oauthResult.error || "OAuth authentication failed",
        oauthResult.errorCode || "OAUTH_ERROR",
        400
      );
    }

    // Authenticate customer and generate JWT tokens
    const authResult = await customerAuthService.authenticateWithOAuth(
      oauthResult,
      {
        ipAddress: clientIP,
        userAgent,
        deviceType: validatedData.clientType, // ← Fixed to use clientType
        deviceId: validatedData.deviceId,
      }
    );

    if (!authResult.success || !authResult.tokens) {
      return createErrorResponse(
        authResult.error || "Authentication failed",
        "AUTH_ERROR",
        500
      );
    }

    // Prepare response data
    const responseData = {
      success: true,
      customer: authResult.customer,
      tokens: {
        accessToken: authResult.tokens.accessToken,
        refreshToken: authResult.tokens.refreshToken,
        tokenType: authResult.tokens.tokenType,
        expiresIn: authResult.tokens.expiresIn,
      },
      isNewCustomer: oauthResult.customer?.isNewCustomer || false,
    };

    return createAuthenticatedResponse(responseData, 200, rateLimitResult.headers);

  } catch (error) {
    console.error("OAuth callback error:", error);

    if (error instanceof z.ZodError) {
      return createErrorResponse(
        "Invalid request data",
        "INVALID_INPUT",
        400
      );
    }

    return createErrorResponse(
      "OAuth callback processing failed",
      "OAUTH_CALLBACK_ERROR",
      500
    );
  }
}

// Handle GET request for web OAuth callback (redirect from Google)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const code = searchParams.get("code");
    const state = searchParams.get("state");
    const error = searchParams.get("error");

    // Handle OAuth error from Google
    if (error) {
      const errorDescription = searchParams.get("error_description") || "OAuth error";
      console.error("OAuth error from Google:", error, errorDescription);
      
      // Redirect to frontend with error
      const frontendUrl = process.env.FRONTEND_URL || "http://localhost:3000";
      const redirectUrl = new URL("/auth/error", frontendUrl);
      redirectUrl.searchParams.set("error", error);
      redirectUrl.searchParams.set("description", errorDescription);
      
      return Response.redirect(redirectUrl.toString(), 302);
    }

    if (!code || !state) {
      const frontendUrl = process.env.FRONTEND_URL || "http://localhost:3000";
      const redirectUrl = new URL("/auth/error", frontendUrl);
      redirectUrl.searchParams.set("error", "missing_parameters");
      redirectUrl.searchParams.set("description", "Missing authorization code or state");
      
      return Response.redirect(redirectUrl.toString(), 302);
    }

    // For web OAuth, we need to handle this differently
    // This is typically handled by the frontend JavaScript
    // Redirect to frontend with code and state
    const frontendUrl = process.env.FRONTEND_URL || "http://localhost:3000";
    const redirectUrl = new URL("/auth/callback", frontendUrl);
    redirectUrl.searchParams.set("code", code);
    redirectUrl.searchParams.set("state", state);
    
    return Response.redirect(redirectUrl.toString(), 302);

  } catch (error) {
    console.error("OAuth GET callback error:", error);
    
    const frontendUrl = process.env.FRONTEND_URL || "http://localhost:3000";
    const redirectUrl = new URL("/auth/error", frontendUrl);
    redirectUrl.searchParams.set("error", "callback_error");
    redirectUrl.searchParams.set("description", "OAuth callback processing failed");
    
    return Response.redirect(redirectUrl.toString(), 302);
  }
}

// Handle preflight OPTIONS request
export async function OPTIONS(request: NextRequest) {
  const response = new Response(null, { status: 200 });
  return addSecurityHeaders(response);
}
