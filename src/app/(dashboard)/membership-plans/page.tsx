import { Metadata } from "next";
import { getCurrentUser } from "@/lib/auth/utils";
import { MembershipPlansWithLocations } from "@/components/membership-plans/membership-plans-with-locations";

/**
 * Membership Plans Page
 *
 * Page untuk manage membership plans dengan full CRUD functionality dan location assignments.
 * Mengiku<PERSON> pattern yang sama dengan classes page dan pages lainnya.
 */

export const metadata: Metadata = {
  title: "Membership Plans - Your SaaS App",
  description: "Manage your membership plans, pricing, and location assignments",
};

export default async function MembershipPlansPage() {
  const user = await getCurrentUser();

  if (!user) {
    return <div>Please sign in to access this page.</div>;
  }

  // In a real app, you would get the tenant ID from the session or context
  // Untuk sekarang kita hardcode dulu, nanti bisa diambil dari auth system
  const tenantId = 1; // This should come from your auth/session system

  return (
    <div className="container mx-auto py-6">
      <MembershipPlansWithLocations tenantId={tenantId} />
    </div>
  );
}
