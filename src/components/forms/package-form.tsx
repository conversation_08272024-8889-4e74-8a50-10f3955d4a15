"use client";

import React, { useState, useEffect } from "react";
import { createId } from "@paralleldrive/cuid2";
import { useForm } from "@tanstack/react-form";
import { z } from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { AlertCircle, Package, FileText, Calendar, Clock, Eye, EyeOff, Plus, Trash2, BookOpen } from "lucide-react";
import { Package as PackageType } from "@/lib/db/schema";
import { PackagePurchaseOptionsSection, packagePurchaseOptionsSchema } from "./package-purchase-options-section";
import { ClassSelector } from "./class-selector";

// Form validation schema
const packageSchema = z.object({
  tenantId: z.number().int().positive("Tenant ID must be positive"),
  name: z.string().min(1, "Package name is required").max(255, "Package name too long"),
  description: z.string().optional(),
  isActive: z.boolean().default(true),
  is_private: z.boolean().default(false),
  validity_date: z.string().optional(),
  validity_duration: z.number().int().min(0, "Validity duration cannot be negative").optional(),
  schedule_availability: z.array(z.object({
    id: z.string(),
    start_date: z.string().optional(),
    start_time: z.string().optional(),
    end_date: z.string().optional(),
    end_time: z.string().optional(),
  })).optional(),
  included_classes: z.array(z.object({
    id: z.string(),
    name: z.string(),
    category: z.string().optional(),
    included_at: z.string(),
  })).optional(),
  purchaseOptions: packagePurchaseOptionsSchema.optional(),
});

type PackageFormData = z.infer<typeof packageSchema>;

interface PackageFormProps {
  entity?: PackageType | null;
  onSubmit: (data: PackageFormData) => void;
  onCancel: () => void;
  tenantId?: number;
  className?: string;
  title?: string;
  description?: string;
  existingPurchaseOptions?: any; // Package purchase options if editing
  existingIndividualTargeting?: any[]; // Individual customer targeting if editing
}

export function PackageForm({
  entity,
  onSubmit,
  onCancel,
  tenantId = 1,
  className = "",
  title = "Package",
  description = "Create and manage packages for your business",
  existingPurchaseOptions,
  existingIndividualTargeting = []
}: PackageFormProps) {
  const [scheduleAvailability, setScheduleAvailability] = useState<Array<{
    id: string;
    start_date?: string;
    start_time?: string;
    end_date?: string;
    end_time?: string;
  }>>(entity?.schedule_availability || []);

  const [includedClasses, setIncludedClasses] = useState<Array<{
    id: string;
    name: string;
    category?: string;
    included_at: string;
  }>>(entity?.included_classes || []);

  // Handle schedule availability management
  const addScheduleAvailability = () => {
    const newSchedule = {
      id: createId(),
      start_date: "",
      start_time: "",
      end_date: "",
      end_time: ""
    };
    setScheduleAvailability(prev => [...prev, newSchedule]);
  };

  const updateScheduleAvailability = (index: number, field: string, value: string) => {
    setScheduleAvailability(prev => prev.map((schedule, i) =>
      i === index ? { ...schedule, [field]: value } : schedule
    ));
  };

  const removeScheduleAvailability = (index: number) => {
    setScheduleAvailability(prev => prev.filter((_, i) => i !== index));
  };

  const getDefaultValues = () => ({
    tenantId: entity?.tenantId || tenantId,
    name: entity?.name || "",
    description: entity?.description || "",
    isActive: entity?.isActive ?? true,
    is_private: entity?.is_private ?? false,
    validity_date: entity?.validity_date || "",
    validity_duration: entity?.validity_duration || undefined,
    schedule_availability: scheduleAvailability,
    included_classes: includedClasses,
    purchaseOptions: {
      purchaseLimit: existingPurchaseOptions?.purchase_limit || undefined,
      restrictTo: existingPurchaseOptions?.restrict_to || "all_customers",
      transferable: existingPurchaseOptions?.transferable ?? false,
      specifySoldAtLocation: existingPurchaseOptions?.specify_sold_at_location ?? false,
      soldAtLocationId: existingPurchaseOptions?.sold_at_location_id || "",
      classBookingLimit: existingPurchaseOptions?.class_booking_limit || undefined,
      showOnline: existingPurchaseOptions?.show_online ?? false,
      individualCustomerIds: existingIndividualTargeting.map(customer => customer.id) || [],
    },
  });

  const form = useForm({
    defaultValues: getDefaultValues(),
    onSubmit: async ({ value }) => {
      try {
        // Include schedule availability in submission
        const submissionData = {
          ...value,
          schedule_availability: scheduleAvailability.filter(schedule =>
            schedule.start_date || schedule.start_time || schedule.end_date || schedule.end_time
          ),
          included_classes: includedClasses
        };
        await onSubmit(submissionData);
      } catch (error) {
        console.error("Form submission error:", error);
      }
    },
  });

  // Reset form when data changes
  useEffect(() => {
    if (existingPurchaseOptions || existingIndividualTargeting.length > 0) {
      const newValues = getDefaultValues();

      // Update form fields individually
      form.setFieldValue("purchaseOptions.transferable", newValues.purchaseOptions.transferable);
      form.setFieldValue("purchaseOptions.specifySoldAtLocation", newValues.purchaseOptions.specifySoldAtLocation);
      form.setFieldValue("purchaseOptions.soldAtLocationId", newValues.purchaseOptions.soldAtLocationId);
      form.setFieldValue("purchaseOptions.restrictTo", newValues.purchaseOptions.restrictTo);
      form.setFieldValue("purchaseOptions.purchaseLimit", newValues.purchaseOptions.purchaseLimit);
      form.setFieldValue("purchaseOptions.classBookingLimit", newValues.purchaseOptions.classBookingLimit);
      form.setFieldValue("purchaseOptions.showOnline", newValues.purchaseOptions.showOnline);
      form.setFieldValue("purchaseOptions.individualCustomerIds", newValues.purchaseOptions.individualCustomerIds);
    }
  }, [existingPurchaseOptions, existingIndividualTargeting, entity]);

  return (
    <Card className={`w-full max-w-4xl mx-auto ${className}`}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Package className="h-5 w-5" />
          {entity ? `Edit ${title}` : `Create ${title}`}
        </CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>

      <CardContent>
        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            form.handleSubmit();
          }}
          className="space-y-6"
        >
          {/* Basic Information Section */}
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-medium">Basic Information</h3>
              <p className="text-sm text-muted-foreground">
                Enter the basic details for the package
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Package Name */}
              <form.Field
                name="name"
                validators={{
                  onChange: (value) => {
                    if (!value || (typeof value === 'string' && value.trim() === '')) {
                      return 'Package name is required';
                    }
                    if (typeof value === 'string' && value.length > 255) {
                      return 'Package name must be less than 255 characters';
                    }
                    return undefined;
                  },
                }}
                children={(field) => (
                  <div className="space-y-2">
                    <Label htmlFor={field.name} className="flex items-center gap-2">
                      <Package className="h-4 w-4" />
                      Package Name *
                    </Label>
                    <Input
                      id={field.name}
                      name={field.name}
                      value={field.state.value}
                      onBlur={field.handleBlur}
                      onChange={(e) => field.handleChange(e.target.value)}
                      placeholder="Enter package name (e.g., Basic Fitness Package)"
                    />
                    {field.state.meta.errors.length > 0 && (
                      <div className="flex items-center gap-2 text-sm text-destructive">
                        <AlertCircle className="h-4 w-4" />
                        {field.state.meta.errors[0]}
                      </div>
                    )}
                    <p className="text-xs text-muted-foreground">
                      Enter a descriptive name for the package
                    </p>
                  </div>
                )}
              />

              {/* Validity Duration */}
              <form.Field
                name="validity_duration"
                validators={{
                  onChange: (value) => {
                    // Allow undefined (empty input)
                    if (value === undefined || value === null) {
                      return undefined;
                    }

                    // Check if it's a valid number
                    // if (typeof value !== 'number' || isNaN(value)) {
                    //   return 'Validity duration must be a valid number';
                    // }

                    // Check if it's a whole number
                    // if (!Number.isInteger(value)) {
                    //   return 'Validity duration must be a whole number';
                    // }

                    // Check range
                    // if (value < 0) {
                    //   return 'Validity duration cannot be negative';
                    // }

                    return undefined;
                  },
                }}
                children={(field) => (
                  <div className="space-y-2">
                    <Label htmlFor={field.name} className="flex items-center gap-2">
                      <Clock className="h-4 w-4" />
                      Validity Duration (days)
                    </Label>
                    <Input
                      id={field.name}
                      name={field.name}
                      type="number"
                      min="0"
                      value={field.state.value || ""}
                      onBlur={field.handleBlur}
                      onChange={(e) => {
                        const value = e.target.value.trim();

                        // Handle empty input
                        if (value === "") {
                          field.handleChange(undefined);
                          return;
                        }

                        // Parse and validate number
                        const numValue = parseInt(value, 10);
                        if (!isNaN(numValue)) {
                          field.handleChange(numValue);
                        }
                      }}
                      placeholder="Enter validity duration in days"
                    />
                    {field.state.meta.errors.length > 0 && (
                      <div className="flex items-center gap-2 text-sm text-destructive">
                        <AlertCircle className="h-4 w-4" />
                        {field.state.meta.errors[0]}
                      </div>
                    )}
                    <p className="text-xs text-muted-foreground">
                      How many days the package is valid (leave empty for unlimited)
                    </p>
                  </div>
                )}
              />
            </div>

            {/* Description */}
            <form.Field
              name="description"
              children={(field) => (
                <div className="space-y-2">
                  <Label htmlFor={field.name} className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    Description
                  </Label>
                  <Textarea
                    id={field.name}
                    name={field.name}
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    placeholder="Enter package description (optional)"
                    rows={3}
                  />
                  <p className="text-xs text-muted-foreground">
                    Provide a detailed description of what this package includes
                  </p>
                </div>
              )}
            />
          </div>

          {/* Settings Section */}
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-medium">Package Settings</h3>
              <p className="text-sm text-muted-foreground">
                Configure package visibility and status
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Active Status */}
              <form.Field
                name="isActive"
                children={(field) => (
                  <div className="space-y-2">
                    <Label className="flex items-center gap-2">
                      <Eye className="h-4 w-4" />
                      Package Status
                    </Label>
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={field.state.value}
                        onCheckedChange={field.handleChange}
                      />
                      <span className="text-sm">
                        {field.state.value ? "Active" : "Inactive"}
                      </span>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      {field.state.value 
                        ? "Package is active and available for purchase" 
                        : "Package is inactive and not available for purchase"
                      }
                    </p>
                  </div>
                )}
              />

              {/* Private Status */}
              <form.Field
                name="is_private"
                children={(field) => (
                  <div className="space-y-2">
                    <Label className="flex items-center gap-2">
                      <EyeOff className="h-4 w-4" />
                      Package Visibility
                    </Label>
                    <div className="flex items-center space-x-2">
                      <Switch
                        checked={field.state.value}
                        onCheckedChange={field.handleChange}
                      />
                      <span className="text-sm">
                        {field.state.value ? "Private" : "Public"}
                      </span>
                    </div>
                    <p className="text-xs text-muted-foreground">
                      {field.state.value 
                        ? "Package is private and only visible to specific customers" 
                        : "Package is public and visible to all customers"
                      }
                    </p>
                  </div>
                )}
              />
            </div>

            {/* Validity Date */}
            <form.Field
              name="validity_date"
              children={(field) => (
                <div className="space-y-2">
                  <Label htmlFor={field.name} className="flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    Validity Date
                  </Label>
                  <Input
                    id={field.name}
                    name={field.name}
                    type="date"
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                  />
                  <p className="text-xs text-muted-foreground">
                    Specific date until when the package is valid (optional)
                  </p>
                </div>
              )}
            />
          </div>

          {/* Included Classes Section */}
          <div className="border-t pt-6 mt-6">
            <ClassSelector
              tenantId={tenantId}
              selectedClasses={includedClasses}
              onSelectionChange={setIncludedClasses}
              maxSelections={20}
              placeholder="Search and select classes to include in this package..."
            />
          </div>

          {/* Schedule Availability Section */}
          <div className="border-t pt-6 mt-6">
            <div className="flex items-center gap-2 mb-4">
              <Calendar className="h-5 w-5 text-blue-600" />
              <h3 className="text-lg font-semibold">Schedule Availability</h3>
              {scheduleAvailability.length > 0 && (
                <span className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                  {scheduleAvailability.length} schedule{scheduleAvailability.length !== 1 ? 's' : ''}
                </span>
              )}
            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <p className="text-sm text-gray-600">
                  Define when this package is available for purchase or use.
                </p>
              </div>

              {/* Schedule List */}
              <div className="space-y-3">
                {scheduleAvailability.map((schedule, index) => (
                  <div key={schedule.id} className="grid grid-cols-1 md:grid-cols-5 gap-3 p-4 border rounded-lg bg-gray-50">
                    <div>
                      <Label htmlFor={`start-date-${index}`} className="text-sm font-medium">
                        Start Date
                      </Label>
                      <Input
                        id={`start-date-${index}`}
                        type="date"
                        value={schedule.start_date || ""}
                        onChange={(e) => updateScheduleAvailability(index, 'start_date', e.target.value)}
                        className="mt-1"
                      />
                    </div>
                    <div>
                      <Label htmlFor={`start-time-${index}`} className="text-sm font-medium">
                        Start Time
                      </Label>
                      <Input
                        id={`start-time-${index}`}
                        type="time"
                        value={schedule.start_time || ""}
                        onChange={(e) => updateScheduleAvailability(index, 'start_time', e.target.value)}
                        className="mt-1"
                      />
                    </div>
                    <div>
                      <Label htmlFor={`end-date-${index}`} className="text-sm font-medium">
                        End Date
                      </Label>
                      <Input
                        id={`end-date-${index}`}
                        type="date"
                        value={schedule.end_date || ""}
                        onChange={(e) => updateScheduleAvailability(index, 'end_date', e.target.value)}
                        className="mt-1"
                      />
                    </div>
                    <div>
                      <Label htmlFor={`end-time-${index}`} className="text-sm font-medium">
                        End Time
                      </Label>
                      <Input
                        id={`end-time-${index}`}
                        type="time"
                        value={schedule.end_time || ""}
                        onChange={(e) => updateScheduleAvailability(index, 'end_time', e.target.value)}
                        className="mt-1"
                      />
                    </div>
                    <div className="flex items-end">
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeScheduleAvailability(index)}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}

                {/* Add Schedule Button */}
                <Button
                  type="button"
                  variant="outline"
                  onClick={addScheduleAvailability}
                  className="w-full border-dashed border-2 hover:border-blue-400 hover:text-blue-600"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Schedule Availability
                </Button>
              </div>
            </div>
          </div>

          {/* Package Purchase Options Section */}
          <PackagePurchaseOptionsSection
            form={form}
            tenantId={tenantId}
            className="border-t pt-6"
          />

          {/* Form Actions */}
          <div className="flex items-center justify-end space-x-2 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={form.state.isSubmitting}
            >
              {form.state.isSubmitting
                ? "Saving..."
                : entity
                ? "Update Package"
                : "Create Package"
              }
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
