import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { z } from "zod";
import { CustomerService } from "@/lib/services/customer.service";

// Validation schema for updating customer
const updateCustomerSchema = z.object({
  tenantId: z.number().int().positive().optional(),
  locationId: z.string().optional(),
  email: z.string().email().optional(),
  mobileNumber: z.string().optional(),
  mobileCountryCode: z.string().optional(),
  firstName: z.string().min(1).max(255).optional(),
  lastName: z.string().max(255).optional(),
  dateOfBirth: z.string().optional(),
  gender: z.string().max(50).optional(),
  pricingGroupId: z.string().optional(),
  isActive: z.boolean().optional(),
  notes: z.string().max(1000).optional(),
  // Address fields
  addressLine1: z.string().max(255).optional(),
  addressLine2: z.string().max(255).optional(),
  city: z.string().max(255).optional(),
  state: z.string().max(255).optional(),
  zip: z.string().max(255).optional(),
  country: z.string().max(255).optional(),
  // Waiver fields
  waiverFormIds: z.array(z.string()).optional(),
});

const customerService = new CustomerService();

// PUT /api/customers/[id] - Update customer
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id: customerId } = await params;
    if (!customerId) {
      return NextResponse.json(
        { error: "Customer ID is required" },
        { status: 400 }
      );
    }

    const body = await request.json();
    const validatedData = updateCustomerSchema.parse(body);

    const updatedCustomer = await customerService.update(customerId, validatedData);

    return NextResponse.json(
      { 
        message: "Customer updated successfully", 
        data: updatedCustomer 
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error updating customer:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid data", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to update customer" },
      { status: 500 }
    );
  }
}

// DELETE /api/customers/[id] - Delete customer
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id: customerId } = await params;
    if (!customerId) {
      return NextResponse.json(
        { error: "Customer ID is required" },
        { status: 400 }
      );
    }

    const deletedCustomer = await customerService.delete(customerId);

    return NextResponse.json(
      {
        message: "Customer deleted successfully",
        data: deletedCustomer
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error deleting customer:", error);
    return NextResponse.json(
      { error: "Failed to delete customer" },
      { status: 500 }
    );
  }
}

// GET /api/customers/[id] - Get single customer
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id: customerId } = await params;
    if (!customerId) {
      return NextResponse.json(
        { error: "Customer ID is required" },
        { status: 400 }
      );
    }

    // Check if address should be included
    const { searchParams } = new URL(request.url);
    const includeAddress = searchParams.get("includeAddress") === "true";

    const customer = includeAddress
      ? await customerService.getByIdWithAddress(customerId)
      : await customerService.getById(customerId);

    if (!customer) {
      return NextResponse.json(
        { error: "Customer not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { data: customer },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error fetching customer:", error);
    return NextResponse.json(
      { error: "Failed to fetch customer" },
      { status: 500 }
    );
  }
}

// PATCH /api/customers/[id] - Toggle status
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id: customerId } = await params;
    if (!customerId) {
      return NextResponse.json(
        { error: "Customer ID is required" },
        { status: 400 }
      );
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get("action");

    let updatedCustomer;
    let message;

    switch (action) {
      case "toggle-active":
        updatedCustomer = await customerService.toggleActive(customerId);
        message = `Customer ${updatedCustomer.isActive ? 'activated' : 'deactivated'} successfully`;
        break;
      default:
        return NextResponse.json(
          { error: "Invalid action. Use 'toggle-active'" },
          { status: 400 }
        );
    }

    return NextResponse.json(
      {
        message,
        data: updatedCustomer
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error toggling customer status:", error);
    return NextResponse.json(
      { error: "Failed to toggle customer status" },
      { status: 500 }
    );
  }
}
