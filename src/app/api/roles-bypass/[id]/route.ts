import { NextRequest, NextResponse } from "next/server";
import { RoleService } from "@/lib/services/role.service";
import { z } from "zod";

// Temporary bypass endpoint for role management by ID (testing only)

const updateRoleSchema = z.object({
  name: z.string().optional(),
  display_name: z.string().optional(),
  description: z.string().optional(),
  hierarchy_level: z.number().optional(),
  permissions: z.array(z.string()).optional(),
  is_active: z.boolean().optional(),
});

// GET - Get role by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log("🔓 Roles Bypass - GET by ID:", params.id);
    
    const role = await RoleService.getById(params.id);

    if (!role) {
      return NextResponse.json(
        { success: false, error: "Role not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: role,
    });
  } catch (error) {
    console.error("❌ GET role by ID bypass error:", error);
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}

// PUT - Update role
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    console.log("🔓 Roles Bypass - PUT:", params.id, body);
    
    const validatedData = updateRoleSchema.parse(body);
    const roleResult = await RoleService.update(params.id, validatedData);

    return NextResponse.json({
      success: true,
      data: roleResult,
      message: "Role updated successfully",
    });
  } catch (error) {
    console.error("❌ PUT role bypass error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}

// DELETE - Delete role
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log("🔓 Roles Bypass - DELETE:", params.id);
    
    await RoleService.delete(params.id);

    return NextResponse.json({
      success: true,
      message: "Role deleted successfully",
    });
  } catch (error) {
    console.error("❌ DELETE role bypass error:", error);
    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}
