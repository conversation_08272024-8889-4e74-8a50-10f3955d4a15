# Class Schedules API - Filter is_private

## Overview

Filter `is_private` telah ditambahkan ke endpoint class schedules untuk memberikan fleksibilitas dalam memfilter schedules berdasarkan status private/public dengan API key authentication.

## Endpoint

### API Key Endpoint (untuk akses private schedules)
```
GET /api/public/v1/class-schedules
```
**Requires**: `X-API-Key` header

## Parameter Baru

### `is_private` (optional)

- **Type**: String ("true" atau "false")
- **Description**: Filter schedules berdasarkan status private
- **Values**:
  - `"true"`: Hanya tampilkan private schedules (is_private = true)
  - `"false"`: Hanya tampilkan public schedules (is_private = false atau null)
  - Tidak disertakan: Tampilkan semua schedules (default behavior)

## Contoh Penggunaan

### API Key Endpoint (untuk Private Access)

#### 1. Mendapatkan Semua Schedules dengan API Key
```bash
curl -X GET "http://localhost:3000/api/public/v1/class-schedules?tenantId=1&limit=10" \
  -H "X-API-Key: pk_9b4d8a5f3e9ec298be21ac0589d0346d0289f717d9ca3841093498ef4530c64b"
```

#### 2. Hanya Private Schedules dengan API Key
```bash
curl -X GET "http://localhost:3000/api/public/v1/class-schedules?tenantId=1&is_private=true&limit=10" \
  -H "X-API-Key: pk_9b4d8a5f3e9ec298be21ac0589d0346d0289f717d9ca3841093498ef4530c64b"
```

#### 3. Hanya Public Schedules dengan API Key
```bash
curl -X GET "http://localhost:3000/api/public/v1/class-schedules?tenantId=1&is_private=false&limit=10" \
  -H "X-API-Key: pk_9b4d8a5f3e9ec298be21ac0589d0346d0289f717d9ca3841093498ef4530c64b"
```

#### 4. Kombinasi dengan Filter Lain
```bash
curl -X GET "http://localhost:3000/api/public/v1/class-schedules?tenantId=1&is_private=true&classId=class_123&startDate=2024-01-15" \
  -H "X-API-Key: pk_9b4d8a5f3e9ec298be21ac0589d0346d0289f717d9ca3841093498ef4530c64b"
```

## Response Format

Response format tetap sama seperti sebelumnya:

```json
{
  "success": true,
  "data": {
    "schedules": [
      {
        "id": "schedule_123",
        "class_id": "class_456",
        "tenant_id": 1,
        "is_private": true,
        // ... other fields
      }
    ],
    "total": 5,
    "hasMore": false
  }
}
```

## Error Handling

### Invalid Parameter Value
```bash
GET /api/class-schedules?tenantId=1&is_private=invalid
```

Response:
```json
{
  "error": "Invalid is_private parameter. Must be 'true' or 'false'"
}
```
Status: 400 Bad Request

## Implementation Details

### Database Query Logic

- **is_private=true**: `WHERE is_private = true`
- **is_private=false**: `WHERE (is_private = false OR is_private IS NULL)`
- **Parameter tidak ada**: Tidak ada filter tambahan

### Backward Compatibility

✅ **Fully backward compatible** - existing API calls tanpa parameter `is_private` akan tetap bekerja seperti sebelumnya dan menampilkan semua schedules.

### Konsistensi dengan Public API

Filter logic untuk `is_private=false` menggunakan logika yang sama dengan public API:
```sql
WHERE (is_private = false OR is_private IS NULL)
```

Ini memastikan konsistensi behavior antara authenticated dan public endpoints.

## Use Cases

1. **Admin Dashboard**: Melihat hanya private schedules untuk management internal
2. **Public Calendar**: Melihat hanya public schedules untuk display ke customer
3. **Full Management**: Melihat semua schedules untuk comprehensive overview
4. **Filtering Integration**: Kombinasi dengan filter lain untuk pencarian yang lebih spesifik

## Tips Penggunaan

1. **Performance**: Gunakan filter `is_private` bersama dengan filter lain (seperti `startDate`, `endDate`) untuk query yang lebih efisien
2. **UI/UX**: Implementasikan toggle di frontend untuk switch antara "All", "Private Only", dan "Public Only"
3. **Caching**: Consider caching results untuk kombinasi filter yang sering digunakan
4. **Monitoring**: Monitor usage pattern untuk optimasi database indexing

## Troubleshooting

### Common Issues

1. **Parameter tidak bekerja**: Pastikan menggunakan string "true"/"false", bukan boolean
2. **Unexpected results**: Ingat bahwa `is_private=false` juga include records dengan `is_private=null`
3. **Performance issues**: Gunakan kombinasi dengan filter tanggal untuk membatasi scope query

### Debug Tips

1. Check parameter value di network tab browser
2. Verify tenant isolation masih berfungsi
3. Test dengan berbagai kombinasi filter

## Summary

Filter `is_private` berhasil diimplementasikan dengan API key authentication:

1. **Single Endpoint**: Hanya menggunakan `/api/public/v1/class-schedules` dengan API key
2. **Security**: Private schedules hanya bisa diakses dengan API key yang valid
3. **Flexible Filtering**:
   - `is_private=true` → Hanya private schedules
   - `is_private=false` → Hanya public schedules (false atau null)
   - Tanpa parameter → Semua schedules
4. **Proper Validation**: Parameter invalid akan mengembalikan error 400 dengan pesan yang jelas
5. **Database Optimization**: Menggunakan Drizzle ORM dengan kondisi SQL yang efisien

## Testing

Jalankan test script untuk memverifikasi implementasi:
```bash
node test-api-key-is-private-filter.js
```

**Deprecated test script** (endpoint sudah dihapus):
```bash
node test-is-private-filter.js  # Akan menampilkan pesan deprecated
```

## API Key

```
pk_9b4d8a5f3e9ec298be21ac0589d0346d0289f717d9ca3841093498ef4530c64b
```

## Endpoints Status

- ❌ `/api/class-schedules` - **DIHAPUS** (authenticated endpoint)
- ❌ `/api/public/class-schedules` - **DIHAPUS** (public endpoint tanpa API key)
- ✅ `/api/public/v1/class-schedules` - **AKTIF** (API key endpoint)
