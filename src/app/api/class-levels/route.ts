import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { z } from "zod";
import { ClassLevelService } from "@/lib/services/class-levels.service";

// Validation schema for creating class level
export const createClassLevelSchema = z.object({
  tenantId: z.number().int().positive("Tenant ID must be positive"),
  name: z.string().min(1, "Name is required").max(255, "Name too long"),
  description: z.string().max(500, "Description too long").optional(),
  sortOrder: z.number().int().min(0).optional(),
  isActive: z.boolean().optional(),
});

// Validation schema for bulk operations
export const bulkOperationSchema = z.object({
  ids: z.array(z.string().min(1)),
  action: z.enum(["activate", "deactivate", "delete"]),
});

// GET /api/class-levels - Get class levels list
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const tenantIdParam = searchParams.get("tenantId");
    const activeOnly = searchParams.get("activeOnly") === "true";

    let classLevels;
    if (tenantIdParam) {
      const tenantIdNum = parseInt(tenantIdParam);
      if (isNaN(tenantIdNum)) {
        return NextResponse.json(
          { error: "Invalid tenant ID" },
          { status: 400 }
        );
      }
      
      if (activeOnly) {
        classLevels = await ClassLevelService.getActiveByTenantId(tenantIdNum);
      } else {
        classLevels = await ClassLevelService.getByTenantId(tenantIdNum);
      }
    } else {
      classLevels = await ClassLevelService.getAll();
    }

    return NextResponse.json({ data: classLevels });
  } catch (error) {
    console.error("Error fetching class levels:", error);
    return NextResponse.json(
      { error: "Failed to fetch class levels" },
      { status: 500 }
    );
  }
}

// POST /api/class-levels - Create class level
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = createClassLevelSchema.parse(body);

    const data = await ClassLevelService.create(validatedData);

    return NextResponse.json({ data }, { status: 201 });
  } catch (error) {
    console.error("Error creating class level:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to create class level" },
      { status: 500 }
    );
  }
}

// PUT /api/class-levels - Bulk operations
export async function PUT(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = bulkOperationSchema.parse(body);

    let result;
    switch (validatedData.action) {
      case "activate":
        result = await ClassLevelService.bulkUpdateActive(validatedData.ids, true);
        break;
      case "deactivate":
        result = await ClassLevelService.bulkUpdateActive(validatedData.ids, false);
        break;
      case "delete":
        result = await ClassLevelService.bulkDelete(validatedData.ids);
        break;
      default:
        return NextResponse.json(
          { error: "Invalid action" },
          { status: 400 }
        );
    }

    return NextResponse.json({
      message: `Successfully ${validatedData.action}d ${result.length} class levels`,
      data: result
    });
  } catch (error) {
    console.error("Error in bulk operation:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to perform bulk operation" },
      { status: 500 }
    );
  }
}
