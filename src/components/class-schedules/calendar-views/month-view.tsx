"use client";

import { useMemo } from "react";
import { motion } from "framer-motion";
import { Plus, Clock, Users, MapPin, Edit, Trash2, Calendar } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { type ClassSchedule } from "@/lib/db/schema";

/**
 * MonthView Component
 * 
 * Komponen untuk menampilkan calendar dalam view bulanan.
 * Mengikuti FAANG standards dengan performance optimization dan accessibility.
 */

interface CalendarEvent extends ClassSchedule {
  class_name?: string;
  location_name?: string;
  instructor_name?: string;
}

interface MonthViewProps {
  currentDate: Date;
  schedulesByDate: Record<string, CalendarEvent[]>;
  onDateClick: (date: Date, time?: string) => void;
  onEventClick: (event: CalendarEvent) => void;
  onEditEvent: (event: CalendarEvent) => void;
  onDeleteEvent: (event: CalendarEvent) => void;
  getClassName: (classId: string) => string;
  getLocationName: (locationId: string | null) => string;
  formatTime: (time: Date | string | null) => string;
}

export function MonthView({
  currentDate,
  schedulesByDate,
  onDateClick,
  onEventClick,
  onEditEvent,
  onDeleteEvent,
  getClassName,
  getLocationName,
  formatTime,
}: MonthViewProps) {
  // Helper function untuk format date ke local date string (timezone-safe)
  const formatLocalDate = (date: Date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  // Generate calendar grid
  const calendarDays = useMemo(() => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();

    // First day of the month
    const firstDay = new Date(year, month, 1);
    // Last day of the month
    const lastDay = new Date(year, month + 1, 0);

    // Start from Sunday of the first week
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - startDate.getDay());

    // End at Saturday of the last week
    const endDate = new Date(lastDay);
    endDate.setDate(endDate.getDate() + (6 - endDate.getDay()));

    const days = [];
    const current = new Date(startDate);

    while (current <= endDate) {
      days.push({
        date: new Date(current),
        dateString: formatLocalDate(current), // Use timezone-safe formatting
        isCurrentMonth: current.getMonth() === month,
        isToday: current.toDateString() === new Date().toDateString(),
        dayNumber: current.getDate(),
      });
      current.setDate(current.getDate() + 1);
    }

    return days;
  }, [currentDate]);

  const weekDays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  return (
    <TooltipProvider>
      <div className="month-view">
        {/* Week Headers */}
        <div className="grid grid-cols-7 border-b">
          {weekDays.map((day) => (
            <div
              key={day}
              className="p-3 text-center text-sm font-medium text-muted-foreground bg-muted/50"
            >
              {day}
            </div>
          ))}
        </div>

        {/* Calendar Grid */}
        <div className="grid grid-cols-7">
          {calendarDays.map((day, index) => {
            const daySchedules = schedulesByDate[day.dateString] || [];
            const hasSchedules = daySchedules.length > 0;
            
            return (
              <motion.div
                key={day.dateString}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.2, delay: index * 0.01 }}
                className={`
                  min-h-[120px] border-r border-b p-2 relative cursor-pointer
                  hover:bg-muted/50 transition-colors
                  ${!day.isCurrentMonth ? 'bg-muted/20 text-muted-foreground' : ''}
                  ${day.isToday ? 'bg-primary/5 border-primary/20' : ''}
                  ${hasSchedules ? (
                    daySchedules.length > 3 ? 'bg-red-50 border-red-200' :
                    daySchedules.length > 1 ? 'bg-orange-50 border-orange-200' :
                    'bg-green-50 border-green-200'
                  ) : 'bg-gray-50/50'}
                `}
                onClick={() => onDateClick(day.date)}
              >
                {/* Date Number */}
                <div className="flex items-center justify-between mb-2">
                  <span
                    className={`
                      text-sm font-medium
                      ${day.isToday ? 'bg-primary text-primary-foreground rounded-full w-6 h-6 flex items-center justify-center' : ''}
                      ${!day.isCurrentMonth ? 'text-muted-foreground' : ''}
                    `}
                  >
                    {day.dayNumber}
                  </span>
                  
                  {/* Schedule Count Indicator */}
                  {hasSchedules ? (
                    <div className="flex items-center gap-1">
                      <Badge
                        variant={daySchedules.length > 3 ? "destructive" : "secondary"}
                        className="text-xs h-5 px-1.5 font-medium"
                      >
                        {daySchedules.length}
                      </Badge>
                      {daySchedules.length > 3 && (
                        <div className="w-2 h-2 bg-orange-500 rounded-full animate-pulse" title="High booking density" />
                      )}
                    </div>
                  ) : (
                    /* Quick Add Button - only show when no schedules */
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 hover:opacity-100 transition-opacity"
                      onClick={(e) => {
                        e.stopPropagation();
                        onDateClick(day.date);
                      }}
                    >
                      <Plus className="h-3 w-3" />
                    </Button>
                  )}
                </div>

                {/* Schedules */}
                <div className="space-y-1">
                  {daySchedules.slice(0, 3).map((schedule, scheduleIndex) => (
                    <Tooltip key={schedule.id}>
                      <TooltipTrigger asChild>
                        <motion.div
                          initial={{ opacity: 0, scale: 0.8 }}
                          animate={{ opacity: 1, scale: 1 }}
                          transition={{ duration: 0.2, delay: scheduleIndex * 0.05 }}
                          className={`
                            text-xs p-1.5 rounded-md cursor-pointer
                            hover:shadow-md transition-all duration-200
                            group relative border-l-2 font-medium
                            hover:scale-105 transform
                          `}
                          style={{
                            backgroundColor: `${schedule.calender_color || '#3b82f6'}20`,
                            borderLeftColor: schedule.calender_color || '#3b82f6',
                            color: schedule.calender_color || '#3b82f6',
                          }}
                          onClick={(e) => {
                            e.stopPropagation();
                            onEventClick(schedule);
                          }}
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex-1 min-w-0">
                              <div className="font-medium truncate">
                                {schedule.class_name || getClassName(schedule.class_id)}
                              </div>
                              <div className="flex items-center gap-1 text-xs opacity-90">
                                <Clock className="h-3 w-3" />
                                {formatTime(schedule.start_time)}
                              </div>
                            </div>
                            
                            {/* Quick Actions */}
                            <div className="opacity-0 group-hover:opacity-100 transition-opacity flex gap-1">
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-4 w-4 p-0 text-white hover:bg-white/20"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  onEditEvent(schedule);
                                }}
                              >
                                <Edit className="h-3 w-3" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-4 w-4 p-0 text-white hover:bg-white/20"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  onDeleteEvent(schedule);
                                }}
                              >
                                <Trash2 className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>
                        </motion.div>
                      </TooltipTrigger>
                      <TooltipContent side="top" className="max-w-xs">
                        <div className="space-y-2">
                          <div className="font-semibold text-sm">
                            {schedule.class_name || getClassName(schedule.class_id)}
                          </div>
                          <div className="flex items-center gap-2 text-xs">
                            <Clock className="h-3 w-3" />
                            {formatTime(schedule.start_time)} - {formatTime(schedule.end_time)}
                          </div>
                          {schedule.location_id && (
                            <div className="flex items-center gap-2 text-xs">
                              <MapPin className="h-3 w-3" />
                              {getLocationName(schedule.location_id)}
                            </div>
                          )}
                          <div className="flex items-center gap-2 text-xs">
                            <Users className="h-3 w-3" />
                            Capacity: {schedule.pax} slots
                          </div>
                          <div className="flex items-center gap-2 text-xs">
                            <Calendar className="h-3 w-3" />
                            Duration: {schedule.duration} minutes
                          </div>
                          <div className="flex gap-2 flex-wrap">
                            {schedule.is_private && (
                              <Badge variant="secondary" className="text-xs">
                                Private Class
                              </Badge>
                            )}
                            {schedule.allow_classpass && (
                              <Badge variant="outline" className="text-xs">
                                ClassPass OK
                              </Badge>
                            )}
                          </div>
                          <div className="text-xs text-muted-foreground pt-1 border-t">
                            Click to view details
                          </div>
                        </div>
                      </TooltipContent>
                    </Tooltip>
                  ))}
                  
                  {/* Show more indicator */}
                  {daySchedules.length > 3 && (
                    <div className="text-xs text-muted-foreground text-center py-1 bg-muted/30 rounded">
                      +{daySchedules.length - 3} more schedules
                    </div>
                  )}

                  {/* Empty slot indicator for days with no schedules */}
                  {!hasSchedules && day.isCurrentMonth && (
                    <div className="text-xs text-muted-foreground text-center py-2 opacity-50">
                      <Plus className="h-4 w-4 mx-auto mb-1" />
                      Click to add schedule
                    </div>
                  )}
                </div>

                {/* Schedule count indicator */}
                {hasSchedules && (
                  <div className="absolute top-1 right-1">
                    <div className="w-2 h-2 bg-primary rounded-full"></div>
                  </div>
                )}
              </motion.div>
            );
          })}
        </div>
      </div>
    </TooltipProvider>
  );
}
