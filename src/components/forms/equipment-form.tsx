"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, Wrench, Building, CheckCircle, Package } from "lucide-react";
import { Equipment, NewEquipment } from "@/lib/db/schema";

// Validation schema
const equipmentSchema = z.object({
  tenantId: z.number().int().positive(),
  name: z.string().min(1, "Equipment name is required").max(255),
  default_display_name: z.string().max(255).optional(),
});

type EquipmentFormData = z.infer<typeof equipmentSchema>;

interface EquipmentFormProps {
  equipment?: Equipment;
  tenantId: number;
  onSubmit: (data: Omit<NewEquipment, "id" | "createdAt" | "updatedAt">) => Promise<void>;
  onCancel?: () => void;
  isLoading?: boolean;
  className?: string;
}

export function EquipmentForm({
  equipment,
  tenantId,
  onSubmit,
  onCancel,
  isLoading = false,
  className = "",
}: EquipmentFormProps) {
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [isSuccess, setIsSuccess] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<EquipmentFormData>({
    resolver: zodResolver(equipmentSchema),
    defaultValues: {
      tenantId,
      name: equipment?.name || "",
      default_display_name: equipment?.default_display_name || "",
    },
  });

  const handleFormSubmit = async (data: EquipmentFormData) => {
    try {
      setSubmitError(null);
      setIsSuccess(false);

      await onSubmit(data);

      // Show success state briefly
      setIsSuccess(true);
      setTimeout(() => setIsSuccess(false), 2000);

      if (!equipment) {
        reset(); // Reset form only for new data
      }
    } catch (error) {
      setSubmitError(error instanceof Error ? error.message : "Failed to save data");
      setIsSuccess(false);
    }
  };

  const isFormLoading = isLoading || isSubmitting;

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Wrench className="h-5 w-5" />
          {equipment ? "Edit Equipment" : "Add New Equipment"}
        </CardTitle>
        <CardDescription>
          {equipment 
            ? "Update the equipment information below." 
            : "Enter the details for the new equipment."
          }
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          {submitError && (
            <Alert variant="destructive">
              <AlertDescription>{submitError}</AlertDescription>
            </Alert>
          )}

          {/* Equipment Information Section */}
          <div className="space-y-4">
            <div className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300">
              <Package className="h-4 w-4" />
              Equipment Information
            </div>

            <div className="grid grid-cols-1 gap-4">
              <div>
                <Label htmlFor="name">Equipment Name *</Label>
                <Input
                  id="name"
                  {...register("name")}
                  placeholder="e.g., Industrial Printer, Forklift, Computer"
                  disabled={isFormLoading}
                />
                {errors.name && (
                  <p className="text-sm text-red-600 mt-1">{errors.name.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="default_display_name">Display Name</Label>
                <Input
                  id="default_display_name"
                  {...register("default_display_name")}
                  placeholder="How this equipment should be displayed (optional)"
                  disabled={isFormLoading}
                />
                {errors.default_display_name && (
                  <p className="text-sm text-red-600 mt-1">{errors.default_display_name.message}</p>
                )}
              </div>
            </div>
          </div>

          
          {/* Form Actions */}
          <div className="flex gap-3 pt-4">
            <motion.div
              className="flex-1"
              whileTap={{ scale: 0.98 }}
              animate={isSuccess ? { scale: [1, 1.02, 1] } : {}}
              transition={{ duration: 0.2 }}
            >
              <Button
                type="submit"
                disabled={isFormLoading}
                className={`w-full transition-all duration-200 ${
                  isSuccess
                    ? "bg-green-600 hover:bg-green-700 text-white"
                    : ""
                }`}
              >
                {isFormLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isSuccess && <CheckCircle className="mr-2 h-4 w-4" />}
                {isSuccess
                  ? "✓ Success!"
                  : equipment
                    ? "Update Equipment"
                    : "Create Equipment"
                }
              </Button>
            </motion.div>
            
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isFormLoading}
              >
                Cancel
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
