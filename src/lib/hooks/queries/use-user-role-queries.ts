import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { User, UserRole } from "@/lib/db/schema";

/**
 * TanStack Query Hooks untuk User Role Assignment
 * 
 * Hooks ini mengikuti pola yang sama dengan role queries dan permission queries.
 * Menyediakan caching, optimistic updates, dan error handling yang proper.
 */

// Types
interface UserSearchResult {
  users: Array<User & { currentRoles?: string[] }>;
  total: number;
  hasMore: boolean;
}

interface UserRoleAssignmentData {
  userId: string;
  roleId: string;
  tenantId?: number | null;
  assignedBy?: string;
  expiresAt?: Date;
}

interface UserRoleRevocationData {
  userId: string;
  roleId: string;
  tenantId?: number | null;
}

// Query Keys
export const userRoleQueryKeys = {
  all: ["user-roles"] as const,
  userSearch: (query: string, tenantId?: number | null) => 
    ["user-roles", "search", { query, tenantId }] as const,
  userRoles: (userId: string, tenantId?: number | null) => 
    ["user-roles", "assignments", { userId, tenantId }] as const,
  roleUsers: (roleId: string, tenantId?: number | null) => 
    ["user-roles", "role-users", { roleId, tenantId }] as const,
};

/**
 * Hook untuk search users untuk role assignment
 */
export function useUserSearch(
  query: string = "",
  tenantId?: number | null,
  options?: {
    enabled?: boolean;
    limit?: number;
    offset?: number;
  }
) {
  return useQuery({
    queryKey: userRoleQueryKeys.userSearch(query, tenantId),
    queryFn: async (): Promise<UserSearchResult> => {
      const params = new URLSearchParams({
        query,
        limit: (options?.limit || 20).toString(),
        offset: (options?.offset || 0).toString(),
      });

      if (tenantId !== undefined) {
        params.append("tenantId", tenantId?.toString() || "null");
      }

      const response = await fetch(`/api/users/search?${params}`);
      if (!response.ok) {
        throw new Error("Failed to search users");
      }

      const result = await response.json();
      return result.data;
    },
    enabled: options?.enabled !== false,
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook untuk get user role assignments
 */
export function useUserRoleAssignments(
  userId: string,
  tenantId?: number | null,
  options?: { enabled?: boolean }
) {
  return useQuery({
    queryKey: userRoleQueryKeys.userRoles(userId, tenantId),
    queryFn: async (): Promise<UserRole[]> => {
      const params = new URLSearchParams({
        userId,
      });

      if (tenantId !== undefined) {
        params.append("tenantId", tenantId?.toString() || "null");
      }

      const response = await fetch(`/api/roles/user-roles?${params}`);
      if (!response.ok) {
        throw new Error("Failed to fetch user role assignments");
      }

      const result = await response.json();
      return result.data.userRoles || [];
    },
    enabled: options?.enabled !== false && !!userId,
    staleTime: 30 * 1000,
    gcTime: 5 * 60 * 1000,
  });
}

/**
 * Mutation untuk assign role ke user
 */
export function useAssignRole() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: UserRoleAssignmentData): Promise<UserRole> => {
      const response = await fetch("/api/roles/assign", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to assign role");
      }

      const result = await response.json();
      return result.data;
    },
    onSuccess: (data, variables) => {
      // Invalidate related queries
      queryClient.invalidateQueries({
        queryKey: userRoleQueryKeys.userRoles(variables.userId, variables.tenantId),
      });
      queryClient.invalidateQueries({
        queryKey: userRoleQueryKeys.roleUsers(variables.roleId, variables.tenantId),
      });
      queryClient.invalidateQueries({
        queryKey: userRoleQueryKeys.userSearch("", variables.tenantId),
      });
    },
  });
}

/**
 * Mutation untuk revoke role dari user
 */
export function useRevokeRole() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: UserRoleRevocationData): Promise<void> => {
      const response = await fetch("/api/roles/revoke", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to revoke role");
      }
    },
    onSuccess: (_, variables) => {
      // Invalidate related queries
      queryClient.invalidateQueries({
        queryKey: userRoleQueryKeys.userRoles(variables.userId, variables.tenantId),
      });
      queryClient.invalidateQueries({
        queryKey: userRoleQueryKeys.roleUsers(variables.roleId, variables.tenantId),
      });
      queryClient.invalidateQueries({
        queryKey: userRoleQueryKeys.userSearch("", variables.tenantId),
      });
    },
  });
}

/**
 * Mutation untuk bulk assign roles
 */
export function useBulkAssignRoles() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: {
      assignments: UserRoleAssignmentData[];
    }): Promise<UserRole[]> => {
      const response = await fetch("/api/roles/bulk-assign", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to bulk assign roles");
      }

      const result = await response.json();
      return result.data;
    },
    onSuccess: (data, variables) => {
      // Invalidate all related queries
      queryClient.invalidateQueries({
        queryKey: userRoleQueryKeys.all,
      });
    },
  });
}
