import { ReactNode } from "react";

interface DevLayoutProps {
  children: ReactNode;
}

export default function DevLayout({ children }: DevLayoutProps) {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Development Environment Banner */}
      <div className="bg-yellow-500 text-black px-4 py-2 text-center text-sm font-medium">
        🚧 Development Environment - Testing & Debug Pages
      </div>
      
      {/* Main Content */}
      <main>{children}</main>
    </div>
  );
}
