import { NextRequest, NextResponse } from "next/server";
import { RoleService } from "@/lib/services/role.service";

// Comprehensive test endpoint to verify all CRUD operations work
export async function POST(request: NextRequest) {
  try {
    console.log("🧪 Testing complete CRUD operations...");
    
    const testResults = {
      create: { success: false, error: null, data: null },
      read: { success: false, error: null, data: null },
      update: { success: false, error: null, data: null },
      delete: { success: false, error: null, data: null }
    };

    // Test 1: CREATE
    console.log("\n1️⃣ Testing CREATE operation...");
    try {
      const createData = {
        name: `crud_test_${Date.now()}`,
        display_name: "CRUD Test Role",
        description: "Role created for comprehensive CRUD testing",
        is_system_role: false,
        hierarchy_level: 75,
        tenantId: 1,
        permissions: []
      };

      const createdRole = await RoleService.create(createData);
      testResults.create.success = true;
      testResults.create.data = {
        id: createdRole.id,
        name: createdRole.name,
        display_name: createdRole.display_name
      };
      console.log("✅ CREATE successful:", testResults.create.data);

      // Test 2: READ
      console.log("\n2️⃣ Testing READ operation...");
      try {
        const readRole = await RoleService.getById(createdRole.id);
        if (readRole) {
          testResults.read.success = true;
          testResults.read.data = {
            id: readRole.id,
            name: readRole.name,
            display_name: readRole.display_name,
            is_active: readRole.is_active
          };
          console.log("✅ READ successful:", testResults.read.data);

          // Test 3: UPDATE
          console.log("\n3️⃣ Testing UPDATE operation...");
          try {
            const updateData = {
              display_name: "CRUD Test Role UPDATED",
              description: "Role updated during comprehensive CRUD testing"
            };

            const updatedRole = await RoleService.update(createdRole.id, updateData);
            testResults.update.success = true;
            testResults.update.data = {
              id: updatedRole.id,
              name: updatedRole.name,
              display_name: updatedRole.display_name,
              description: updatedRole.description
            };
            console.log("✅ UPDATE successful:", testResults.update.data);

            // Test 4: DELETE
            console.log("\n4️⃣ Testing DELETE operation...");
            try {
              await RoleService.delete(createdRole.id);
              
              // Verify soft delete
              const deletedRole = await RoleService.getById(createdRole.id);
              if (deletedRole && !deletedRole.is_active) {
                testResults.delete.success = true;
                testResults.delete.data = {
                  id: deletedRole.id,
                  name: deletedRole.name,
                  is_active: deletedRole.is_active,
                  softDeleted: true
                };
                console.log("✅ DELETE successful (soft delete):", testResults.delete.data);
              } else {
                throw new Error("Soft delete verification failed");
              }
            } catch (deleteError) {
              testResults.delete.error = deleteError instanceof Error ? deleteError.message : "Unknown delete error";
              console.log("❌ DELETE failed:", testResults.delete.error);
            }
          } catch (updateError) {
            testResults.update.error = updateError instanceof Error ? updateError.message : "Unknown update error";
            console.log("❌ UPDATE failed:", testResults.update.error);
          }
        } else {
          throw new Error("Role not found after creation");
        }
      } catch (readError) {
        testResults.read.error = readError instanceof Error ? readError.message : "Unknown read error";
        console.log("❌ READ failed:", testResults.read.error);
      }
    } catch (createError) {
      testResults.create.error = createError instanceof Error ? createError.message : "Unknown create error";
      console.log("❌ CREATE failed:", testResults.create.error);
    }

    // Summary
    const allSuccess = Object.values(testResults).every(result => result.success);
    const successCount = Object.values(testResults).filter(result => result.success).length;

    console.log(`\n📊 CRUD Test Summary: ${successCount}/4 operations successful`);

    return NextResponse.json({
      success: allSuccess,
      message: allSuccess ? "All CRUD operations working perfectly!" : `${successCount}/4 CRUD operations working`,
      data: {
        summary: {
          allOperationsWorking: allSuccess,
          successfulOperations: successCount,
          totalOperations: 4
        },
        results: testResults,
        operations: {
          create: testResults.create.success ? "✅ WORKING" : "❌ FAILED",
          read: testResults.read.success ? "✅ WORKING" : "❌ FAILED", 
          update: testResults.update.success ? "✅ WORKING" : "❌ FAILED",
          delete: testResults.delete.success ? "✅ WORKING" : "❌ FAILED"
        }
      }
    });
  } catch (error) {
    console.error("❌ Comprehensive CRUD test failed:", error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}
