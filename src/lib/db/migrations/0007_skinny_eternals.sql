CREATE TABLE "class_levels" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"tenant_id" integer,
	"name" varchar(255),
	"description" varchar(255),
	"created_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updated_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL
);
--> statement-breakpoint
ALTER TABLE "class_levels" ADD CONSTRAINT "class_levels_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;