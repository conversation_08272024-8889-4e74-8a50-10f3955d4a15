"use client";

import { useState, useEffect, useCallback } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Plus } from "lucide-react";
import { Checkbox } from "@/components/ui/checkbox";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { CalendarIcon, Users, MapPin, AlertTriangle, CheckCircle, Clock, XCircle } from "lucide-react";
import { useClassesByTenant, useCreateClass } from "@/lib/hooks/queries/use-class-queries";
import { useLocation } from "@/lib/hooks/queries/use-location-queries";
import { useFacilitiesByTenant } from "@/lib/hooks/queries/use-facility-queries";
import type { ClassScheduleFormData } from "@/lib/hooks/queries/use-class-schedule-queries";
import type { ClassSchedule } from "@/lib/db/schema";
import {
  formatDateTimeForInput,
  formatDateForInput,
  combineDateAndTime
} from "@/lib/utils/datetime";
import { ClassForm } from "./class-form";
import { mapToSelectOptions, SelectWithInlineCreation } from "../ui/select-with-inline-creation";

/**
 * Class Schedule Form Component
 *
 * Form untuk create dan edit class schedules.
 * Mengikuti pattern yang sama dengan form components lain yang sudah ada.
 *
 * Features:
 * - Validation dengan Zod schema
 * - Dropdown relationships (class, location, facility, instructor)
 * - Combined datetime-local inputs untuk better UX
 * - Two-column layout untuk form yang tidak terlalu panjang
 * - Proper error handling dan loading states
 * - Real-time conflict detection
 *
 * Kenapa pakai pattern ini?
 * - Konsisten dengan form components lain
 * - Type safety dengan TypeScript
 * - Automatic validation dengan react-hook-form + Zod
 * - Reusable untuk create dan edit
 * - Simplified datetime input (combined date & time)
 */

// Schema untuk validasi form
const classScheduleFormSchema = z.object({
  class_id: z.string().min(1, "Class is required"),
  name: z.string().max(255).optional(),
  description: z.string().optional(),
  location_id: z.string().optional(),
  facility_id: z.string().optional(),
  staff_id: z.string().optional(),
  start_time: z.string().min(1, "Start date and time is required"),
  end_time: z.string().min(1, "End date and time is required"),
  duration: z.number().int().positive("Duration must be positive"),
  calender_color: z.string().optional(),
  repeat_rule: z.enum(["none", "daily", "weekly", "monthly"]).optional(),
  pax: z.number().int().positive("Capacity must be positive").optional(),
  waitlist: z.number().int().positive("Waitlist must be positive").optional(),
  allow_classpass: z.boolean().optional(),
  is_private: z.boolean().optional(),
  publish_now: z.boolean().optional(),
  publish_at: z.string().optional(),
  auto_cancel_if_minimum_not_met: z.boolean().optional(),
  booking_window_start: z.string().optional(),
  booking_window_end: z.string().optional(),
  check_in_window_start: z.string().optional(),
  check_in_window_end: z.string().optional(),
  late_cancellation_rule: z.string().optional(),
});

type ClassScheduleFormValues = z.infer<typeof classScheduleFormSchema>;

interface ClassScheduleFormProps {
  tenantId: number;
  initialData?: ClassSchedule;
  selectedDate?: Date; // Tanggal yang dipilih dari calendar
  selectedTime?: string; // Waktu yang dipilih dari calendar (format HH:mm)
  onSubmit: (data: ClassScheduleFormData) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

export function ClassScheduleForm({
  tenantId,
  initialData,
  selectedDate,
  selectedTime,
  onSubmit,
  onCancel,
  isLoading = false,
}: ClassScheduleFormProps) {

  // FAANG-Level Real-time Conflict Detection State
  const [conflictCheckState, setConflictCheckState] = useState<{
    isChecking: boolean;
    hasConflicts: boolean;
    canProceed: boolean;
    conflicts: Array<{
      id: string;
      type: string;
      severity: string;
      message: string;
      timeOverlap: {
        start: string;
        end: string;
        durationMinutes: number;
      };
    }>;
    suggestions: Array<{
      startTime: string;
      endTime: string;
      reason: string;
      confidence: number;
    }>;
    lastChecked: Date | null;
  }>({
    isChecking: false,
    hasConflicts: false,
    canProceed: true,
    conflicts: [],
    suggestions: [],
    lastChecked: null,
  });


  //State Management
  const [isAddClassesModalOpen, setIsAddClassesModalOpen] = useState(false);

  // Fetch dropdown data
  const { data: classes = [], refetch: refetchClasses, isLoading: isLoadingClasses } = useClassesByTenant(tenantId);
  const { data: locations = [], refetch: refetchLocations, isLoading: isLoadingLocations } = useLocation(tenantId);
  const { data: facilities = [], refetch: refetchFacilities, isLoading: isLoadingFacilities } = useFacilitiesByTenant(tenantId);


  //mutation hook creation
  const createClassMutation = useCreateClass();

  // For now, show all facilities since they're not directly linked to locations
  // In the future, you might want to add a location_id field to facilities table
  const filteredFacilities = facilities;

  // Helper functions menggunakan utility yang konsisten

  // Helper function untuk format start time dari calendar selection atau initial data
  const getDefaultStartTime = () => {
    if (initialData?.start_time) {
      return formatDateTimeForInput(initialData.start_time);
    }
    if (selectedDate && selectedTime) {
      // Use timezone-safe date formatting
      return combineDateAndTime(selectedDate, selectedTime);
    }
    if (selectedDate) {
      // If only date is selected, use current time
      const dateStr = formatDateForInput(selectedDate);
      const now = new Date();
      const timeStr = now.toTimeString().slice(0, 5); // HH:mm format
      return `${dateStr}T${timeStr}`;
    }
    return "";
  };

  // Helper function untuk format end time (default to 1 hour after start time)
  const getDefaultEndTime = () => {
    if (initialData?.end_time) {
      return formatDateTimeForInput(initialData.end_time);
    }
    const startTime = getDefaultStartTime();
    if (startTime) {
      const startDate = new Date(startTime);
      const endDate = new Date(startDate.getTime() + 60 * 60 * 1000); // Add 1 hour
      return formatDateTimeForInput(endDate);
    }
    return "";
  };

  // Form setup
  const form = useForm<ClassScheduleFormValues>({
    resolver: zodResolver(classScheduleFormSchema),
    defaultValues: {
      class_id: initialData?.class_id || "",
      name: initialData?.name || "",
      description: initialData?.description || "",
      location_id: initialData?.location_id || "",
      facility_id: initialData?.facility_id || "",
      staff_id: initialData?.staff_id || "",
      start_time: getDefaultStartTime(), // Combined date and time
      end_time: getDefaultEndTime(), // Combined date and time
      duration: initialData?.duration || 60,
      calender_color: initialData?.calender_color || "#3b82f6",
      repeat_rule: (initialData?.repeat_rule as "none" | "daily" | "weekly" | "monthly") || "none",
      pax: initialData?.pax || 1,
      waitlist: initialData?.waitlist || 1,
      allow_classpass: initialData?.allow_classpass || false,
      is_private: initialData?.is_private || false,
      publish_now: initialData?.publish_now || false,
      publish_at: initialData?.publish_at ? formatDateTimeForInput(initialData.publish_at) : "",
      auto_cancel_if_minimum_not_met: initialData?.auto_cancel_if_minimum_not_met || false,
      booking_window_start: initialData?.booking_window_start ? formatDateTimeForInput(initialData.booking_window_start) : "",
      booking_window_end: initialData?.booking_window_end ? formatDateTimeForInput(initialData.booking_window_end) : "",
      check_in_window_start: initialData?.check_in_window_start ? formatDateTimeForInput(initialData.check_in_window_start) : "",
      check_in_window_end: initialData?.check_in_window_end ? formatDateTimeForInput(initialData.check_in_window_end) : "",
      late_cancellation_rule: initialData?.late_cancellation_rule || "",
    },
  });

  // FAANG-Level Real-time Conflict Detection
  const checkScheduleConflicts = useCallback(async (
    startTime: string,
    endTime: string,
    locationId?: string,
    facilityId?: string,
    staffId?: string
  ) => {
    if (!startTime || !endTime) return;

    setConflictCheckState(prev => ({ ...prev, isChecking: true }));

    try {
      const response = await fetch('/api/class-schedules/check-conflicts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          tenantId,
          startTime,
          endTime,
          locationId: locationId && locationId !== "none" ? locationId : undefined,
          facilityId: facilityId && facilityId !== "none" ? facilityId : undefined,
          staffId: staffId || undefined,
          excludeScheduleId: initialData?.id, // Exclude current schedule for updates
        }),
      });

      if (response.ok) {
        const result = await response.json();
        const conflictData = result.data;

        setConflictCheckState({
          isChecking: false,
          hasConflicts: conflictData.hasConflicts,
          canProceed: conflictData.canProceed,
          conflicts: conflictData.conflicts || [],
          suggestions: conflictData.suggestions || [],
          lastChecked: new Date(),
        });
      } else {
        console.error('Failed to check conflicts:', response.statusText);
        setConflictCheckState(prev => ({
          ...prev,
          isChecking: false,
          lastChecked: new Date(),
        }));
      }
    } catch (error) {
      console.error('Error checking conflicts:', error);
      setConflictCheckState(prev => ({
        ...prev,
        isChecking: false,
        lastChecked: new Date(),
      }));
    }
  }, [tenantId, initialData?.id]);

  // Watch for changes in critical fields dan trigger conflict check
  const watchedStartTime = form.watch("start_time");
  const watchedEndTime = form.watch("end_time");
  const watchedLocationId = form.watch("location_id");
  const watchedFacilityId = form.watch("facility_id");
  const watchedStaffId = form.watch("staff_id");

  // Debounced conflict checking
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (watchedStartTime && watchedEndTime) {
        checkScheduleConflicts(
          watchedStartTime,
          watchedEndTime,
          watchedLocationId,
          watchedFacilityId,
          watchedStaffId
        );
      }
    }, 1000); // 1 second debounce

    return () => clearTimeout(timeoutId);
  }, [
    watchedStartTime,
    watchedEndTime,
    watchedLocationId,
    watchedFacilityId,
    watchedStaffId,
    checkScheduleConflicts
  ]);


  // const handleCreateClass = async (data: any) => {
  //   try {
  //     const newClass = await createClassMutation.mutateAsync(data);
  //     await refetchClasses();
  //     setIsAddClassesModalOpen(false);
  //     form.setValue("class_id", newClass.id);
  //   } catch (error) {
  //     console.error("Error creating class:", error);
  //   }
  // };



  // Handle form submission
  const handleSubmit = async (values: ClassScheduleFormValues) => {
    try {
      // FAANG-Level Conflict Validation: Check for conflicts before submission
      if (conflictCheckState.hasConflicts && !conflictCheckState.canProceed) {
        // Show error message for blocking conflicts
        const blockingConflicts = conflictCheckState.conflicts.filter(c => c.severity === 'BLOCKING');
        if (blockingConflicts.length > 0) {
          const conflictMessages = blockingConflicts.map(c => c.message).join(', ');
          form.setError('root', {
            type: 'manual',
            message: `Cannot create schedule due to conflicts: ${conflictMessages}. Please resolve these conflicts before proceeding.`
          });
          return; // Block submission
        }
      }

      // If we have warnings but can proceed, show confirmation
      if (conflictCheckState.hasConflicts && conflictCheckState.canProceed) {
        const warningConflicts = conflictCheckState.conflicts.filter(c => c.severity === 'WARNING');
        if (warningConflicts.length > 0) {
          const proceed = window.confirm(
            `Warning: ${warningConflicts.length} potential conflict(s) detected. Do you want to proceed anyway?\n\n` +
            warningConflicts.map(c => `• ${c.message}`).join('\n')
          );
          if (!proceed) {
            return; // User chose not to proceed
          }
        }
      }

      console.log("📝 Form values before submit:", { name: values.name, description: values.description });

      const formData: ClassScheduleFormData = {
        tenant_id: tenantId,
        class_id: values.class_id,
        name: values.name,
        description: values.description || undefined,
        location_id: values.location_id && values.location_id !== "none" ? values.location_id : undefined,
        facility_id: values.facility_id && values.facility_id !== "none" ? values.facility_id : undefined,
        staff_id: values.staff_id || undefined,
        start_time: values.start_time,
        end_time: values.end_time,
        duration: values.duration,
        calender_color: values.calender_color || undefined,
        repeat_rule: values.repeat_rule,
        pax: values.pax,
        waitlist: values.waitlist,
        allow_classpass: values.allow_classpass,
        is_private: values.is_private,
        publish_now: values.publish_now,
        publish_at: values.publish_at || undefined,
        auto_cancel_if_minimum_not_met: values.auto_cancel_if_minimum_not_met,
        booking_window_start: values.booking_window_start || undefined,
        booking_window_end: values.booking_window_end || undefined,
        check_in_window_start: values.check_in_window_start || undefined,
        check_in_window_end: values.check_in_window_end || undefined,
        late_cancellation_rule: values.late_cancellation_rule || undefined,
      };

      console.log("🚀 Final form data being submitted:", {
        name: formData.name,
        description: formData.description,
        fullData: formData
      });

      await onSubmit(formData);
    } catch (error) {
      console.error("Error submitting form:", error);
      // Show error in form
      form.setError('root', {
        type: 'manual',
        message: error instanceof Error ? error.message : 'An unexpected error occurred'
      });
    }
  };

  return (
    <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CalendarIcon className="h-5 w-5" />
            Basic Information
          </CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Class Selection */}
          {/* Instructor/Staff */}
          <div className="space-y-2">
            <Label htmlFor="name">Name</Label>
            <Input
              id="name"
              {...form.register("name")}
              placeholder="Name"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Input
              id="description"
              {...form.register("description")}
              placeholder="Description"
            />
          </div>

          
          <div className="space-y-2">
            <Label htmlFor="class_id">Class *</Label>
            <SelectWithInlineCreation
              value={form.watch("class_id")}
              onValueChange={(value) => form.setValue("class_id", value)}
              options={mapToSelectOptions(classes)}
              entityType="class"
              tenantId={tenantId}
              placeholder="Select a class"
              disabled={isLoadingClasses}
              onEntityCreated={(newClass) => {
                form.setValue("class_id", newClass.id);
              }}
              refetchData={refetchClasses}
            />
            {form.formState.errors.class_id && (
              <p className="text-sm text-red-500">{form.formState.errors.class_id.message}</p>
            )}
          </div>

          {/* Duration */}
          <div className="space-y-2">
            <Label htmlFor="duration">Duration (minutes) *</Label>
            <Input
              id="duration"
              type="number"
              min="1"
              {...form.register("duration", { valueAsNumber: true })}
              placeholder="60"
            />
            {form.formState.errors.duration && (
              <p className="text-sm text-red-500">{form.formState.errors.duration.message}</p>
            )}
          </div>

          {/* Start Date & Time */}
          <div className="space-y-2">
            <Label htmlFor="start_time">Start Date & Time *</Label>
            <Input
              id="start_time"
              type="datetime-local"
              {...form.register("start_time")}
            />
            {form.formState.errors.start_time && (
              <p className="text-sm text-red-500">{form.formState.errors.start_time.message}</p>
            )}
          </div>

          {/* End Date & Time */}
          <div className="space-y-2">
            <Label htmlFor="end_time">End Date & Time *</Label>
            <Input
              id="end_time"
              type="datetime-local"
              {...form.register("end_time")}
            />
            {form.formState.errors.end_time && (
              <p className="text-sm text-red-500">{form.formState.errors.end_time.message}</p>
            )}
          </div>

          {/* FAANG-Level Real-time Conflict Detection Display */}
          {(conflictCheckState.isChecking || conflictCheckState.hasConflicts || conflictCheckState.suggestions.length > 0) && (
            <div className="col-span-2 space-y-3">
              {/* Checking Status */}
              {conflictCheckState.isChecking && (
                <Alert>
                  <Clock className="h-4 w-4 animate-spin" />
                  <AlertDescription>
                    Checking for schedule conflicts...
                  </AlertDescription>
                </Alert>
              )}

              {/* Conflict Results */}
              {!conflictCheckState.isChecking && conflictCheckState.hasConflicts && (
                <Alert variant={conflictCheckState.canProceed ? "default" : "destructive"}>
                  {conflictCheckState.canProceed ? (
                    <AlertTriangle className="h-4 w-4" />
                  ) : (
                    <XCircle className="h-4 w-4" />
                  )}
                  <AlertDescription>
                    <div className="space-y-2">
                      <p className="font-medium">
                        {conflictCheckState.canProceed
                          ? "Schedule conflicts detected (warnings)"
                          : "Schedule conflicts detected (blocking)"}
                      </p>
                      <div className="space-y-1">
                        {conflictCheckState.conflicts.map((conflict, index) => (
                          <div key={index} className="text-sm">
                            <Badge variant={conflict.severity === 'BLOCKING' ? 'destructive' : 'secondary'} className="mr-2">
                              {conflict.severity}
                            </Badge>
                            {conflict.message}
                          </div>
                        ))}
                      </div>
                    </div>
                  </AlertDescription>
                </Alert>
              )}

              {/* No Conflicts - Success */}
              {!conflictCheckState.isChecking && !conflictCheckState.hasConflicts && conflictCheckState.lastChecked && (
                <Alert>
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <AlertDescription className="text-green-600">
                    No schedule conflicts detected. This time slot is available!
                  </AlertDescription>
                </Alert>
              )}

              {/* Alternative Suggestions */}
              {conflictCheckState.suggestions.length > 0 && (
                <Alert>
                  <Clock className="h-4 w-4" />
                  <AlertDescription>
                    <div className="space-y-2">
                      <p className="font-medium">Alternative time suggestions:</p>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        {conflictCheckState.suggestions.slice(0, 4).map((suggestion, index) => (
                          <div
                            key={index}
                            className="p-2 bg-blue-50 dark:bg-blue-900/20 rounded border cursor-pointer hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
                            onClick={() => {
                              // Auto-fill suggested time
                              form.setValue("start_time", suggestion.startTime.slice(0, 16));
                              form.setValue("end_time", suggestion.endTime.slice(0, 16));
                            }}
                          >
                            <div className="text-sm font-medium">
                              {new Date(suggestion.startTime).toLocaleTimeString()} - {new Date(suggestion.endTime).toLocaleTimeString()}
                            </div>
                            <div className="text-xs text-gray-600 dark:text-gray-400">
                              {suggestion.reason}
                            </div>
                            <Badge variant="outline" className="text-xs mt-1">
                              {suggestion.confidence}% confidence
                            </Badge>
                          </div>
                        ))}
                      </div>
                    </div>
                  </AlertDescription>
                </Alert>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Location & Facility */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5" />
            Location & Facility
          </CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Location Selection */}
          <div className="space-y-2">
            <Label htmlFor="location_id">Location</Label>
            <SelectWithInlineCreation 
              value={form.watch("location_id") || "not_set"}
              onValueChange={(value) => form.setValue("location_id", value === "not_set" ? undefined : value)}
              options={mapToSelectOptions(locations)}
              entityType="location"
              tenantId={tenantId}
              placeholder="Select location"
              emptyOption={{ value: "not_set", label: "No specific location" }}
              disabled={isLoadingLocations}
              onEntityCreated={(newLocation) => {
                form.setValue("location_id", newLocation.id);
              }}
              refetchData={refetchLocations}
            />
            {form.formState.errors.location_id && (
              <p className="text-sm text-red-500">{form.formState.errors.location_id.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="facility_id">Facility</Label>
            <SelectWithInlineCreation 
              value={form.watch("facility_id") || "not_set"}
              onValueChange={(value) => form.setValue("facility_id", value === "not_set" ? undefined : value)}
              options={mapToSelectOptions(facilities)}
              entityType="facility"
              tenantId={tenantId}
              placeholder="Select facility"
              emptyOption={{ value: "not_set", label: "No specific facility" }}
              disabled={isLoadingFacilities}
              onEntityCreated={(newFacility) => {
                form.setValue("facility_id", newFacility.id);
              }}
              refetchData={refetchFacilities}
            />
            {form.formState.errors.facility_id && (
              <p className="text-sm text-red-500">{form.formState.errors.facility_id.message}</p>
            )}
          </div>

          {/* Instructor/Staff */}
          <div className="space-y-2">
            <Label htmlFor="staff_id">Instructor</Label>
            <Input
              id="staff_id"
              {...form.register("staff_id")}
              placeholder="Instructor ID (optional)"
            />
          </div>

          {/* Calendar Color */}
          <div className="space-y-2">
            <Label htmlFor="calender_color">Calendar Color</Label>
            <Input
              id="calender_color"
              type="color"
              {...form.register("calender_color")}
            />
          </div>
        </CardContent>
      </Card>

      {/* Capacity & Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Capacity & Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Capacity */}
          <div className="space-y-2">
            <Label htmlFor="pax">Capacity *</Label>
            <Input
              id="pax"
              type="number"
              min="1"
              {...form.register("pax", { valueAsNumber: true })}
              placeholder="1"
            />
            {form.formState.errors.pax && (
              <p className="text-sm text-red-500">{form.formState.errors.pax.message}</p>
            )}
          </div>

          {/* Waitlist */}
          <div className="space-y-2">
            <Label htmlFor="waitlist">Waitlist Capacity *</Label>
            <Input
              id="waitlist"
              type="number"
              min="1"
              {...form.register("waitlist", { valueAsNumber: true })}
              placeholder="1"
            />
            {form.formState.errors.waitlist && (
              <p className="text-sm text-red-500">{form.formState.errors.waitlist.message}</p>
            )}
          </div>

          {/* Repeat Rule */}
          <div className="space-y-2">
            <Label htmlFor="repeat_rule">Repeat Rule</Label>
            <Select
              value={form.watch("repeat_rule")}
              onValueChange={(value) => form.setValue("repeat_rule", value as "none" | "daily" | "weekly" | "monthly")}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select repeat rule" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">No Repeat</SelectItem>
                <SelectItem value="daily">Daily</SelectItem>
                <SelectItem value="weekly">Weekly</SelectItem>
                <SelectItem value="monthly">Monthly</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Late Cancellation Rule */}
          <div className="space-y-2">
            <Label htmlFor="late_cancellation_rule">Late Cancellation Rule</Label>
            <Input
              id="late_cancellation_rule"
              {...form.register("late_cancellation_rule")}
              placeholder="e.g., 24 hours before class"
            />
          </div>

          {/* Checkboxes */}
          <div className="space-y-4 md:col-span-2">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="allow_classpass"
                checked={form.watch("allow_classpass")}
                onCheckedChange={(checked) => form.setValue("allow_classpass", !!checked)}
              />
              <Label htmlFor="allow_classpass">Allow Class Pass Payment</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="is_private"
                checked={form.watch("is_private")}
                onCheckedChange={(checked) => form.setValue("is_private", !!checked)}
              />
              <Label htmlFor="is_private">Private Class</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="publish_now"
                checked={form.watch("publish_now")}
                onCheckedChange={(checked) => form.setValue("publish_now", !!checked)}
              />
              <Label htmlFor="publish_now">Publish Now</Label>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox
                id="auto_cancel_if_minimum_not_met"
                checked={form.watch("auto_cancel_if_minimum_not_met")}
                onCheckedChange={(checked) => form.setValue("auto_cancel_if_minimum_not_met", !!checked)}
              />
              <Label htmlFor="auto_cancel_if_minimum_not_met">Auto Cancel if Minimum Not Met</Label>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Form-level Error Display */}
      {form.formState.errors.root && (
        <Alert variant="destructive" className="mt-4">
          <XCircle className="h-4 w-4" />
          <AlertDescription>
            {form.formState.errors.root.message}
          </AlertDescription>
        </Alert>
      )}

      {/* Form Actions */}
      <div className="flex justify-end space-x-4">
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isLoading}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={
            isLoading ||
            conflictCheckState.isChecking ||
            (!conflictCheckState.canProceed && conflictCheckState.hasConflicts)
          }
          className={
            !conflictCheckState.canProceed && conflictCheckState.hasConflicts
              ? "bg-red-600 hover:bg-red-700"
              : ""
          }
        >
          {isLoading ? "Saving..." :
           conflictCheckState.isChecking ? "Checking conflicts..." :
           !conflictCheckState.canProceed && conflictCheckState.hasConflicts ? "Cannot save - conflicts detected" :
           initialData ? "Update Schedule" : "Create Schedule"}
        </Button>
      </div>

      {/* Conflict Warning for Submit */}
      {!conflictCheckState.canProceed && conflictCheckState.hasConflicts && (
        <Alert variant="destructive" className="mt-4">
          <XCircle className="h-4 w-4" />
          <AlertDescription>
            Cannot save schedule due to blocking conflicts. Please resolve the conflicts above or choose an alternative time slot.
          </AlertDescription>
        </Alert>
      )}
    </form>
  );
}
