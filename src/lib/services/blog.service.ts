import { db } from "@/lib/db";
import { blogPosts, blogCategories, users, type BlogPost, type NewBlogPost, type BlogCategory, type NewBlogCategory } from "@/lib/db/schema";
import { eq, and, or, desc, asc, ilike, isNull, sql, count } from "drizzle-orm";
import { createId } from "@paralleldrive/cuid2";

// Types for service operations
export interface BlogPostFilters {
  search?: string;
  category_id?: string;
  status?: "draft" | "published";
  is_featured?: boolean;
  author_id?: string;
  tags?: string[];
}

export interface BlogCategoryFilters {
  search?: string;
  is_active?: boolean;
}

export interface CreateBlogPostData {
  tenantId: number;
  title: string;
  slug: string;
  content: string;
  excerpt?: string;
  featured_image?: string;
  category_id?: string;
  tags?: string[];
  status?: "draft" | "published";
  is_featured?: boolean;
  published_at?: Date;
  author_id: string;
  seo_title?: string;
  seo_description?: string;
}

export interface UpdateBlogPostData {
  title?: string;
  slug?: string;
  content?: string;
  excerpt?: string;
  featured_image?: string;
  category_id?: string;
  tags?: string[];
  status?: "draft" | "published";
  is_featured?: boolean;
  published_at?: Date;
  seo_title?: string;
  seo_description?: string;
}

export interface CreateBlogCategoryData {
  tenantId: number;
  name: string;
  slug: string;
  description?: string;
  color?: string;
  is_active?: boolean;
}

export interface UpdateBlogCategoryData {
  name?: string;
  slug?: string;
  description?: string;
  color?: string;
  is_active?: boolean;
}

export interface BlogPostWithAuthor extends BlogPost {
  author: {
    id: string;
    name: string;
    email: string;
  };
  category?: BlogCategory;
}

export interface PaginationOptions {
  limit?: number;
  offset?: number;
}

export class BlogService {
  // Blog Posts CRUD Operations
  static async create(data: CreateBlogPostData): Promise<BlogPost> {
    return await db.transaction(async (tx) => {
      // Generate slug if not provided
      let slug = data.slug;
      if (!slug) {
        slug = data.title.toLowerCase()
          .replace(/[^a-z0-9\s-]/g, '')
          .replace(/\s+/g, '-')
          .trim();
      }

      // Check for duplicate slug within tenant
      const existingPost = await tx
        .select()
        .from(blogPosts)
        .where(
          and(
            eq(blogPosts.tenantId, data.tenantId),
            eq(blogPosts.slug, slug)
          )
        )
        .limit(1);

      if (existingPost.length > 0) {
        // Append timestamp to make unique
        slug = `${slug}-${Date.now()}`;
      }

      const postData: NewBlogPost = {
        id: createId(),
        tenantId: data.tenantId,
        title: data.title,
        slug,
        content: data.content,
        excerpt: data.excerpt || null,
        featured_image: data.featured_image || null,
        category_id: data.category_id || null,
        tags: data.tags || [],
        status: data.status || "draft",
        is_featured: data.is_featured || false,
        published_at: data.status === "published" ? (data.published_at || new Date()) : null,
        author_id: data.author_id,
        seo_title: data.seo_title || null,
        seo_description: data.seo_description || null,
        view_count: 0,
      };

      const result = await tx.insert(blogPosts).values(postData).returning();
      
      if (!result[0]) {
        throw new Error("Failed to create blog post");
      }

      return result[0];
    });
  }

  static async getById(id: string): Promise<BlogPostWithAuthor | null> {
    const result = await db
      .select({
        post: blogPosts,
        author: {
          id: users.id,
          name: users.name,
          email: users.email,
        },
        category: blogCategories,
      })
      .from(blogPosts)
      .leftJoin(users, eq(blogPosts.author_id, users.id))
      .leftJoin(blogCategories, eq(blogPosts.category_id, blogCategories.id))
      .where(eq(blogPosts.id, id))
      .limit(1);

    if (!result[0]) return null;

    return {
      ...result[0].post,
      author: result[0].author,
      category: result[0].category || undefined,
    };
  }

  static async getBySlug(tenantId: number, slug: string): Promise<BlogPostWithAuthor | null> {
    const result = await db
      .select({
        post: blogPosts,
        author: {
          id: users.id,
          name: users.name,
          email: users.email,
        },
        category: blogCategories,
      })
      .from(blogPosts)
      .leftJoin(users, eq(blogPosts.author_id, users.id))
      .leftJoin(blogCategories, eq(blogPosts.category_id, blogCategories.id))
      .where(
        and(
          eq(blogPosts.tenantId, tenantId),
          eq(blogPosts.slug, slug)
        )
      )
      .limit(1);

    if (!result[0]) return null;

    return {
      ...result[0].post,
      author: result[0].author,
      category: result[0].category || undefined,
    };
  }

  static async getByTenantWithFilters(
    tenantId: number,
    filters: BlogPostFilters = {},
    pagination: PaginationOptions = {}
  ): Promise<BlogPostWithAuthor[]> {
    let query = db
      .select({
        post: blogPosts,
        author: {
          id: users.id,
          name: users.name,
          email: users.email,
        },
        category: blogCategories,
      })
      .from(blogPosts)
      .leftJoin(users, eq(blogPosts.author_id, users.id))
      .leftJoin(blogCategories, eq(blogPosts.category_id, blogCategories.id))
      .where(eq(blogPosts.tenantId, tenantId));

    // Apply filters
    const conditions = [eq(blogPosts.tenantId, tenantId)];

    if (filters.search) {
      conditions.push(
        or(
          ilike(blogPosts.title, `%${filters.search}%`),
          ilike(blogPosts.content, `%${filters.search}%`),
          ilike(blogPosts.excerpt, `%${filters.search}%`)
        )!
      );
    }

    if (filters.category_id) {
      conditions.push(eq(blogPosts.category_id, filters.category_id));
    }

    if (filters.status) {
      conditions.push(eq(blogPosts.status, filters.status));
    }

    if (filters.is_featured !== undefined) {
      conditions.push(eq(blogPosts.is_featured, filters.is_featured));
    }

    if (filters.author_id) {
      conditions.push(eq(blogPosts.author_id, filters.author_id));
    }

    if (filters.tags && filters.tags.length > 0) {
      conditions.push(
        sql`${blogPosts.tags} && ${JSON.stringify(filters.tags)}`
      );
    }

    query = query.where(and(...conditions));

    // Apply pagination
    if (pagination.limit) {
      query = query.limit(pagination.limit);
    }

    if (pagination.offset) {
      query = query.offset(pagination.offset);
    }

    // Order by published_at desc for published posts, created_at desc for drafts
    query = query.orderBy(
      desc(blogPosts.published_at),
      desc(blogPosts.created_at)
    );

    const results = await query;

    return results.map(result => ({
      ...result.post,
      author: result.author,
      category: result.category || undefined,
    }));
  }

  static async getPublishedPosts(
    tenantId: number,
    filters: Omit<BlogPostFilters, 'status'> = {},
    pagination: PaginationOptions = {}
  ): Promise<BlogPostWithAuthor[]> {
    return this.getByTenantWithFilters(
      tenantId,
      { ...filters, status: "published" },
      pagination
    );
  }

  static async getFeaturedPosts(tenantId: number, limit: number = 5): Promise<BlogPostWithAuthor[]> {
    return this.getByTenantWithFilters(
      tenantId,
      { status: "published", is_featured: true },
      { limit }
    );
  }

  static async update(id: string, data: UpdateBlogPostData): Promise<BlogPost> {
    const updateData: Partial<NewBlogPost> = {
      ...data,
      updated_at: new Date(),
    };

    // If status is being changed to published and no published_at date, set it now
    if (data.status === "published" && !data.published_at) {
      updateData.published_at = new Date();
    }

    // If status is being changed to draft, clear published_at
    if (data.status === "draft") {
      updateData.published_at = null;
    }

    const result = await db
      .update(blogPosts)
      .set(updateData)
      .where(eq(blogPosts.id, id))
      .returning();

    if (!result[0]) {
      throw new Error("Blog post not found");
    }

    return result[0];
  }

  static async delete(id: string): Promise<void> {
    const result = await db
      .delete(blogPosts)
      .where(eq(blogPosts.id, id))
      .returning();

    if (!result[0]) {
      throw new Error("Blog post not found");
    }
  }

  static async incrementViewCount(id: string): Promise<void> {
    await db
      .update(blogPosts)
      .set({
        view_count: sql`${blogPosts.view_count} + 1`,
      })
      .where(eq(blogPosts.id, id));
  }

  // Blog Categories CRUD Operations
  static async createCategory(data: CreateBlogCategoryData): Promise<BlogCategory> {
    return await db.transaction(async (tx) => {
      // Check for duplicate slug within tenant
      const existingCategory = await tx
        .select()
        .from(blogCategories)
        .where(
          and(
            eq(blogCategories.tenantId, data.tenantId),
            eq(blogCategories.slug, data.slug)
          )
        )
        .limit(1);

      if (existingCategory.length > 0) {
        throw new Error(`Category slug "${data.slug}" already exists for this tenant`);
      }

      const categoryData: NewBlogCategory = {
        id: createId(),
        tenantId: data.tenantId,
        name: data.name,
        slug: data.slug,
        description: data.description || null,
        color: data.color || null,
        is_active: data.is_active ?? true,
      };

      const result = await tx.insert(blogCategories).values(categoryData).returning();
      
      if (!result[0]) {
        throw new Error("Failed to create blog category");
      }

      return result[0];
    });
  }

  static async getCategoryById(id: string): Promise<BlogCategory | null> {
    const result = await db
      .select()
      .from(blogCategories)
      .where(eq(blogCategories.id, id))
      .limit(1);

    return result[0] || null;
  }

  static async getCategoriesByTenant(
    tenantId: number,
    filters: BlogCategoryFilters = {}
  ): Promise<BlogCategory[]> {
    let query = db
      .select()
      .from(blogCategories)
      .where(eq(blogCategories.tenantId, tenantId));

    const conditions = [eq(blogCategories.tenantId, tenantId)];

    if (filters.search) {
      conditions.push(
        or(
          ilike(blogCategories.name, `%${filters.search}%`),
          ilike(blogCategories.description, `%${filters.search}%`)
        )!
      );
    }

    if (filters.is_active !== undefined) {
      conditions.push(eq(blogCategories.is_active, filters.is_active));
    }

    query = query.where(and(...conditions));
    query = query.orderBy(asc(blogCategories.name));

    return await query;
  }

  static async updateCategory(id: string, data: UpdateBlogCategoryData): Promise<BlogCategory> {
    const updateData: Partial<NewBlogCategory> = {
      ...data,
      updated_at: new Date(),
    };

    const result = await db
      .update(blogCategories)
      .set(updateData)
      .where(eq(blogCategories.id, id))
      .returning();

    if (!result[0]) {
      throw new Error("Blog category not found");
    }

    return result[0];
  }

  static async deleteCategory(id: string): Promise<void> {
    const result = await db
      .delete(blogCategories)
      .where(eq(blogCategories.id, id))
      .returning();

    if (!result[0]) {
      throw new Error("Blog category not found");
    }
  }

  // Statistics and Analytics
  static async getPostStats(tenantId: number): Promise<{
    total: number;
    published: number;
    draft: number;
    featured: number;
    totalViews: number;
  }> {
    const stats = await db
      .select({
        total: count(),
        published: sql<number>`count(case when status = 'published' then 1 end)`,
        draft: sql<number>`count(case when status = 'draft' then 1 end)`,
        featured: sql<number>`count(case when is_featured = true then 1 end)`,
        totalViews: sql<number>`sum(view_count)`,
      })
      .from(blogPosts)
      .where(eq(blogPosts.tenantId, tenantId));

    return {
      total: stats[0]?.total || 0,
      published: Number(stats[0]?.published) || 0,
      draft: Number(stats[0]?.draft) || 0,
      featured: Number(stats[0]?.featured) || 0,
      totalViews: Number(stats[0]?.totalViews) || 0,
    };
  }

  // Utility methods
  static generateSlug(title: string): string {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .trim();
  }

  static async isSlugAvailable(tenantId: number, slug: string, excludeId?: string): Promise<boolean> {
    let query = db
      .select()
      .from(blogPosts)
      .where(
        and(
          eq(blogPosts.tenantId, tenantId),
          eq(blogPosts.slug, slug)
        )
      );

    if (excludeId) {
      query = query.where(
        and(
          eq(blogPosts.tenantId, tenantId),
          eq(blogPosts.slug, slug),
          sql`${blogPosts.id} != ${excludeId}`
        )
      );
    }

    const result = await query.limit(1);
    return result.length === 0;
  }
}
