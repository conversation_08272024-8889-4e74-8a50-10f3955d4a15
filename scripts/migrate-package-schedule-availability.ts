// Migration script to add schedule_availability JSONB field to packages table
// and migrate existing package_schedule_availability data
// Run with: npx tsx scripts/migrate-package-schedule-availability.ts

import { db } from "../src/lib/db";
import { packages, package_schedule_availability } from "../src/lib/db/schema";
import { eq } from "drizzle-orm";
import { sql } from "drizzle-orm";

async function migratePackageScheduleAvailability() {
  try {
    console.log("🚀 Starting migration: Add schedule_availability JSONB field to packages table");

    // Step 1: Add the new JSONB column to packages table
    console.log("📝 Adding schedule_availability column to packages table...");
    
    await db.execute(sql`
      ALTER TABLE package 
      ADD COLUMN IF NOT EXISTS schedule_availability JSONB DEFAULT '[]'::jsonb
    `);

    console.log("✅ Column added successfully");

    // Step 2: Migrate existing data from package_schedule_availability to packages.schedule_availability
    console.log("📦 Migrating existing package_schedule_availability data...");

    // Get all existing package schedule availability grouped by package_id
    const existingSchedules = await db
      .select()
      .from(package_schedule_availability);

    console.log(`📋 Found ${existingSchedules.length} existing schedules to migrate`);

    // Group schedules by package_id
    const schedulesByPackage = new Map<string, any[]>();
    
    for (const schedule of existingSchedules) {
      if (!schedulesByPackage.has(schedule.package_id)) {
        schedulesByPackage.set(schedule.package_id, []);
      }
      
      schedulesByPackage.get(schedule.package_id)!.push({
        id: `schedule_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        start_date: schedule.start_date ? schedule.start_date.toISOString().split('T')[0] : undefined,
        start_time: schedule.start_time ? schedule.start_time.toISOString() : undefined,
        end_date: schedule.end_date ? schedule.end_date.toISOString().split('T')[0] : undefined,
        end_time: schedule.end_time ? schedule.end_time.toISOString() : undefined,
      });
    }

    console.log(`🔄 Updating ${schedulesByPackage.size} packages with their schedule availability...`);

    // Update each package with its schedules
    let updatedCount = 0;
    for (const [packageId, schedules] of schedulesByPackage) {
      try {
        await db
          .update(packages)
          .set({ 
            schedule_availability: schedules,
            updatedAt: new Date()
          })
          .where(eq(packages.id, packageId));
        
        updatedCount++;
        console.log(`✅ Updated package ${packageId} with ${schedules.length} schedule availability records`);
      } catch (error) {
        console.error(`❌ Failed to update package ${packageId}:`, error);
      }
    }

    console.log(`🎉 Migration completed! Updated ${updatedCount} packages`);

    // Step 3: Verify migration
    console.log("🔍 Verifying migration...");
    
    const packagesWithSchedules = await db
      .select({
        id: packages.id,
        name: packages.name,
        schedule_availability: packages.schedule_availability
      })
      .from(packages)
      .where(sql`jsonb_array_length(schedule_availability) > 0`);

    console.log(`✅ Verification: ${packagesWithSchedules.length} packages have schedule_availability data`);

    // Show sample data
    if (packagesWithSchedules.length > 0) {
      console.log("\n📋 Sample migrated data:");
      packagesWithSchedules.slice(0, 3).forEach((pkg, index) => {
        console.log(`${index + 1}. Package: ${pkg.name}`);
        console.log(`   Schedules: ${JSON.stringify(pkg.schedule_availability, null, 2)}`);
      });
    }

    console.log("\n⚠️  IMPORTANT: After verifying the migration is successful,");
    console.log("   you can optionally drop the old package_schedule_availability table:");
    console.log("   DROP TABLE package_schedule_availability;");
    console.log("\n   But keep it for now as backup until you're 100% sure migration worked correctly.");

  } catch (error) {
    console.error("💥 Migration failed:", error);
    throw error;
  }
}

// Run the migration
migratePackageScheduleAvailability()
  .then(() => {
    console.log("✅ Migration script completed successfully");
    process.exit(0);
  })
  .catch((error) => {
    console.error("💥 Migration script failed:", error);
    process.exit(1);
  });
