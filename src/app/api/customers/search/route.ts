import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { z } from "zod";
import { individualCustomerSelectionService } from "@/lib/services/individual-customer-selection.service";

// Validation schema for customer search
const customerSearchSchema = z.object({
  tenantId: z.string().transform((val) => parseInt(val)),
  search: z.string().optional(),
  isActive: z.string().optional().transform((val) => val ? val === 'true' : undefined),
  pricingGroupId: z.string().optional(),
  locationId: z.string().optional(),
  limit: z.string().optional().transform((val) => val ? parseInt(val) : undefined),
  offset: z.string().optional().transform((val) => val ? parseInt(val) : undefined),
});

/**
 * GET /api/customers/search
 * Search customers with advanced filtering and pagination
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());

    // Validate query parameters
    const validatedParams = customerSearchSchema.parse(queryParams);

    const { tenantId, ...filters } = validatedParams;

    if (!tenantId || tenantId <= 0) {
      return NextResponse.json(
        { error: "Valid tenant ID is required" },
        { status: 400 }
      );
    }

    console.log(`👤 [GET /api/customers/search] Searching customers for tenant ${tenantId}:`, filters);

    const searchFilters = {
      tenantId,
      ...filters,
    };

    const result = await individualCustomerSelectionService.searchCustomers(searchFilters);

    return NextResponse.json({
      success: true,
      data: result,
      meta: {
        tenantId,
        filters,
        total: result.total,
        returned: result.customers.length,
        hasMore: result.hasMore,
      }
    });
  } catch (error) {
    console.error("👤 [GET /api/customers/search] Error:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid query parameters", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to search customers" },
      { status: 500 }
    );
  }
}
