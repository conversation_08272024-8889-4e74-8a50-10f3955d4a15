import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Permission, RolePermission } from '@/lib/db/schema';

/**
 * Permission Queries Hooks
 * 
 * Hooks untuk manage state dan data fetching permissions menggunakan TanStack Query.
 * Pattern ini mengikuti use-role-queries.ts yang sudah established.
 */

// Query Keys
export const permissionKeys = {
  all: ['permissions'] as const,
  lists: () => [...permissionKeys.all, 'list'] as const,
  list: (filters: Record<string, any>) => [...permissionKeys.lists(), { filters }] as const,
  details: () => [...permissionKeys.all, 'detail'] as const,
  detail: (id: string) => [...permissionKeys.details(), id] as const,
  byModule: (module: string) => [...permissionKeys.all, 'module', module] as const,
  grouped: () => [...permissionKeys.all, 'grouped'] as const,
  search: (params: any) => [...permissionKeys.all, 'search', params] as const,
  forRole: (roleId: string) => [...permissionKeys.all, 'role', roleId] as const,
  forUser: (userId: string, tenantId?: number | null) => [...permissionKeys.all, 'user', userId, tenantId] as const,
};

// API Functions
const permissionApi = {
  search: async (
    module?: string,
    action?: string,
    search?: string,
    limit = 20,
    offset = 0
  ) => {
    const params = new URLSearchParams({
      limit: limit.toString(),
      offset: offset.toString(),
    });
    
    if (module) {
      params.append('module', module);
    }
    if (action) {
      params.append('action', action);
    }
    if (search) {
      params.append('search', search);
    }

    const response = await fetch(`/api/permissions?${params}`);
    if (!response.ok) {
      throw new Error(`Failed to search permissions: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data;
  },

  getById: async (id: string): Promise<Permission> => {
    const response = await fetch(`/api/permissions/${id}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch permission: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data;
  },

  getAllGrouped: async (): Promise<Record<string, Permission[]>> => {
    const response = await fetch('/api/permissions/grouped');
    if (!response.ok) {
      throw new Error(`Failed to fetch grouped permissions: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data;
  },

  create: async (data: {
    module: string;
    action: string;
    resource?: string;
    display_name: string;
    description?: string;
    is_system_permission?: boolean;
  }): Promise<Permission> => {
    const response = await fetch('/api/permissions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      throw new Error(`Failed to create permission: ${response.statusText}`);
    }
    const result = await response.json();
    return result.data;
  },

  update: async (id: string, data: {
    module?: string;
    action?: string;
    resource?: string;
    display_name?: string;
    description?: string;
  }): Promise<Permission> => {
    const response = await fetch(`/api/permissions/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      throw new Error(`Failed to update permission: ${response.statusText}`);
    }
    const result = await response.json();
    return result.data;
  },

  delete: async (id: string): Promise<void> => {
    const response = await fetch(`/api/permissions/${id}`, {
      method: 'DELETE',
    });
    if (!response.ok) {
      throw new Error(`Failed to delete permission: ${response.statusText}`);
    }
  },

  getPermissionsForRole: async (roleId: string): Promise<Permission[]> => {
    const response = await fetch(`/api/permissions/role/${roleId}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch permissions for role: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data;
  },

  getUserPermissions: async (userId: string, tenantId?: number | null): Promise<Permission[]> => {
    const params = new URLSearchParams({
      userId,
    });
    
    if (tenantId !== undefined) {
      params.append('tenantId', tenantId?.toString() || 'null');
    }

    const response = await fetch(`/api/permissions/user?${params}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch user permissions: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data;
  },

  assignPermissionToRole: async (data: {
    roleId: string;
    permissionId: string;
    conditions?: any;
  }): Promise<RolePermission> => {
    const response = await fetch('/api/permissions/assign-to-role', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      throw new Error(`Failed to assign permission to role: ${response.statusText}`);
    }
    const result = await response.json();
    return result.data;
  },

  revokePermissionFromRole: async (data: {
    roleId: string;
    permissionId: string;
  }): Promise<void> => {
    const response = await fetch('/api/permissions/revoke-from-role', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      throw new Error(`Failed to revoke permission from role: ${response.statusText}`);
    }
  },

  checkUserPermission: async (
    userId: string,
    module: string,
    action: string,
    tenantId?: number | null
  ): Promise<boolean> => {
    const params = new URLSearchParams({
      userId,
      module,
      action,
    });
    
    if (tenantId !== undefined) {
      params.append('tenantId', tenantId?.toString() || 'null');
    }

    const response = await fetch(`/api/permissions/check?${params}`);
    if (!response.ok) {
      throw new Error(`Failed to check user permission: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data.hasPermission;
  },
};

// Query Hooks
export function usePermissions(module?: string, action?: string) {
  return useQuery({
    queryKey: permissionKeys.search({ module, action }),
    queryFn: () => permissionApi.search(module, action),
    staleTime: 10 * 60 * 1000, // 10 minutes - permissions jarang berubah
    retry: false,
  });
}

export function usePermissionSearch(
  module?: string,
  action?: string,
  search?: string,
  limit = 20,
  offset = 0
) {
  return useQuery({
    queryKey: permissionKeys.search({ module, action, search, limit, offset }),
    queryFn: () => permissionApi.search(module, action, search, limit, offset),
    staleTime: 5 * 60 * 1000,
  });
}

export function usePermission(id: string) {
  return useQuery({
    queryKey: permissionKeys.detail(id),
    queryFn: () => permissionApi.getById(id),
    enabled: !!id,
    staleTime: 10 * 60 * 1000,
  });
}

export function useGroupedPermissions() {
  return useQuery({
    queryKey: permissionKeys.grouped(),
    queryFn: permissionApi.getAllGrouped,
    staleTime: 10 * 60 * 1000,
  });
}

export function usePermissionsForRole(roleId: string) {
  return useQuery({
    queryKey: permissionKeys.forRole(roleId),
    queryFn: () => permissionApi.getPermissionsForRole(roleId),
    enabled: !!roleId,
    staleTime: 5 * 60 * 1000,
  });
}

export function useUserPermissions(userId: string, tenantId?: number | null) {
  return useQuery({
    queryKey: permissionKeys.forUser(userId, tenantId),
    queryFn: () => permissionApi.getUserPermissions(userId, tenantId),
    enabled: !!userId,
    staleTime: 5 * 60 * 1000,
  });
}

export function useCheckUserPermission(
  userId: string,
  module: string,
  action: string,
  tenantId?: number | null
) {
  return useQuery({
    queryKey: [...permissionKeys.forUser(userId, tenantId), 'check', module, action],
    queryFn: () => permissionApi.checkUserPermission(userId, module, action, tenantId),
    enabled: !!userId && !!module && !!action,
    staleTime: 2 * 60 * 1000,
  });
}



export function useAssignPermissionToRole() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: permissionApi.assignPermissionToRole,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: permissionKeys.all });
    },
  });
}

export function useRevokePermissionFromRole() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: permissionApi.revokePermissionFromRole,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: permissionKeys.all });
    },
  });
}

// Export types
export interface PermissionSearchParams {
  module?: string;
  action?: string;
  search?: string;
  limit?: number;
  offset?: number;
}

export interface PermissionFormData {
  module: string;
  action: string;
  resource?: string;
  display_name: string;
  description?: string;
  is_system_permission?: boolean;
}

// Create permission mutation
export function useCreatePermission() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: PermissionFormData) => {
      const response = await fetch("/api/permissions", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to create permission");
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["permissions"] });
      queryClient.invalidateQueries({ queryKey: ["grouped-permissions"] });
    },
  });
}

// Update permission mutation
export function useUpdatePermission() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: Partial<PermissionFormData> }) => {
      const response = await fetch(`/api/permissions/${id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to update permission");
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["permissions"] });
      queryClient.invalidateQueries({ queryKey: ["grouped-permissions"] });
    },
  });
}

// Delete permission mutation
export function useDeletePermission() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const response = await fetch(`/api/permissions/${id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to delete permission");
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["permissions"] });
      queryClient.invalidateQueries({ queryKey: ["grouped-permissions"] });
    },
  });
}


