"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Plus, Search, Edit, Trash2, Building, Filter, ToggleLeft, ToggleRight, ImageIcon } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { FacilityForm } from "@/components/forms/facility-form";
import {
  useFacilitiesByTenant,
  useCreateFacility,
  useUpdateFacility,
  useDeleteFacility,
  useToggleFacilityActive,
  type FacilityFormData,
} from "@/lib/hooks/queries/use-facility-queries";
import { type Facility } from "@/lib/db/schema";
import { SuccessToast } from "@/components/ui/success-toast";

/**
 * FacilitiesManagement Component
 * 
 * Component untuk manage facilities dengan full CRUD functionality.
 * Mengikuti pattern yang sama dengan membership plans management dan components lainnya.
 * 
 * Features:
 * - Search facilities by name
 * - Filter by active status
 * - Create, edit, delete facilities
 * - Toggle active status
 * - Image display
 * - Responsive grid layout
 * - Success feedback dengan SuccessToast
 * - Professional animations
 */

interface FacilitiesManagementProps {
  tenantId: number;
}

export function FacilitiesManagement({ tenantId }: FacilitiesManagementProps) {
  // State management
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingFacility, setEditingFacility] = useState<Facility | null>(null);
  const [deletingFacility, setDeletingFacility] = useState<Facility | null>(null);
  const [showSuccessToast, setShowSuccessToast] = useState(false);
  const [createdFacility, setCreatedFacility] = useState<Facility | null>(null);

  // Queries
  const {
    data: facilities = [],
    isLoading,
    error,
    refetch,
  } = useFacilitiesByTenant(tenantId, {
    filters: {
      search: searchTerm,
      isActive: statusFilter === "all" ? undefined : statusFilter === "active",
    },
  });

  // Mutations
  const createMutation = useCreateFacility();
  const updateMutation = useUpdateFacility();
  const deleteMutation = useDeleteFacility();
  const toggleActiveMutation = useToggleFacilityActive();

  // Filter facilities based on search and status
  const filteredFacilities = facilities.filter((facility) => {
    const matchesSearch = !searchTerm || 
      facility.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      facility.description?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === "all" || 
      (statusFilter === "active" && facility.isActive) ||
      (statusFilter === "inactive" && !facility.isActive);
    
    return matchesSearch && matchesStatus;
  });

  // Handlers
  const handleCreateFacility = async (data: FacilityFormData) => {
    try {
      const newFacility = await createMutation.mutateAsync(data);

      setIsCreateDialogOpen(false);
      setCreatedFacility(newFacility);
      setShowSuccessToast(true);

      // Hide success toast after duration
      setTimeout(() => {
        setShowSuccessToast(false);
      }, 4000);
    } catch (error) {
      console.error("Create facility error:", error);
    }
  };

  const handleEditFacility = async (data: FacilityFormData) => {
    if (!editingFacility) return;

    try {
      const updatedFacility = await updateMutation.mutateAsync({
        id: editingFacility.id,
        data,
      });

      setEditingFacility(null);
      setCreatedFacility(updatedFacility);
      setShowSuccessToast(true);

      setTimeout(() => {
        setShowSuccessToast(false);
      }, 4000);
    } catch (error) {
      console.error("Update facility error:", error);
    }
  };

  const handleDeleteFacility = async () => {
    if (!deletingFacility) return;

    try {
      await deleteMutation.mutateAsync(deletingFacility.id);
      setDeletingFacility(null);
      
      setCreatedFacility(deletingFacility);
      setShowSuccessToast(true);

      setTimeout(() => {
        setShowSuccessToast(false);
      }, 4000);
    } catch (error) {
      console.error("Delete facility error:", error);
    }
  };

  const handleToggleActive = async (facility: Facility) => {
    try {
      await toggleActiveMutation.mutateAsync(facility.id);
    } catch (error) {
      console.error("Toggle facility status error:", error);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center p-8">
        <p className="text-red-600">Error loading facilities: {error.message}</p>
        <Button onClick={() => refetch()} className="mt-4">
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Facilities</h2>
          <p className="text-muted-foreground">
            Manage your facility spaces and amenities
          </p>
        </div>
        
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Add Facility
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Add New Facility</DialogTitle>
            </DialogHeader>
            <FacilityForm
              tenantId={tenantId}
              onSubmit={handleCreateFacility}
              onCancel={() => setIsCreateDialogOpen(false)}
              isLoading={createMutation.isPending}
            />
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search facilities..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-full sm:w-[180px]">
            <Filter className="h-4 w-4 mr-2" />
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Facilities</SelectItem>
            <SelectItem value="active">Active Only</SelectItem>
            <SelectItem value="inactive">Inactive Only</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Facilities Grid */}
      {filteredFacilities.length === 0 ? (
        <div className="text-center py-12">
          <Building className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium mb-2">No facilities found</h3>
          <p className="text-muted-foreground mb-4">
            {searchTerm || statusFilter !== "all" 
              ? "Try adjusting your search or filters"
              : "Get started by adding your first facility"
            }
          </p>
          <Button onClick={() => setIsCreateDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Add Facility
          </Button>
        </div>
      ) : (
        <motion.div 
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          <AnimatePresence>
            {filteredFacilities.map((facility: Facility) => (
              <motion.div
                key={facility.id}
                layout
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                transition={{ duration: 0.2 }}
              >
                <Card className="h-full hover:shadow-lg transition-shadow">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-lg flex items-center gap-2">
                          <Building className="h-5 w-5" />
                          {facility.name}
                        </CardTitle>
                        {facility.description && (
                          <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                            {facility.description}
                          </p>
                        )}
                      </div>
                      <Badge variant={facility.isActive ? "default" : "secondary"}>
                        {facility.isActive ? "Active" : "Inactive"}
                      </Badge>
                    </div>
                  </CardHeader>
                  
                  <CardContent className="pt-0">
                    {/* Images Preview */}
                    {facility.images && facility.images.length > 0 && (
                      <div className="mb-4">
                        <div className="flex items-center gap-2 mb-2">
                          <ImageIcon className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm text-muted-foreground">
                            {facility.images.length} image{facility.images.length !== 1 ? 's' : ''}
                          </span>
                        </div>
                        <div className="grid grid-cols-3 gap-2">
                          {facility.images.slice(0, 3).map((imageUrl, index) => (
                            <div key={index} className="aspect-square rounded-md overflow-hidden bg-muted">
                              <img
                                src={imageUrl}
                                alt="Facility"
                                className="w-full h-full object-cover"
                                onError={(e) => {
                                  const target = e.target as HTMLImageElement;
                                  target.src = "/images/placeholder-image.svg";
                                }}
                              />
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Actions */}
                    <div className="flex items-center justify-between pt-4 border-t">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleToggleActive(facility)}
                        disabled={toggleActiveMutation.isPending}
                        className="flex items-center gap-2"
                      >
                        {facility.isActive ? (
                          <ToggleRight className="h-4 w-4 text-green-600" />
                        ) : (
                          <ToggleLeft className="h-4 w-4 text-gray-400" />
                        )}
                        {facility.isActive ? "Active" : "Inactive"}
                      </Button>

                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setEditingFacility(facility)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setDeletingFacility(facility)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </AnimatePresence>
        </motion.div>
      )}

      {/* Edit Dialog */}
      <Dialog open={!!editingFacility} onOpenChange={() => setEditingFacility(null)}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Facility</DialogTitle>
          </DialogHeader>
          {editingFacility && (
            <FacilityForm
              facility={editingFacility}
              tenantId={tenantId}
              onSubmit={handleEditFacility}
              onCancel={() => setEditingFacility(null)}
              isLoading={updateMutation.isPending}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deletingFacility} onOpenChange={() => setDeletingFacility(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Facility</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{deletingFacility?.name}"? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteFacility}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Success Toast */}
      <SuccessToast
        isVisible={showSuccessToast}
        onClose={() => setShowSuccessToast(false)}
        title={createdFacility ? `Facility "${createdFacility.name}" processed successfully!` : "Success!"}
        description="The facility operation was completed successfully."
      />
    </div>
  );
}
