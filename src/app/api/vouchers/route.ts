import { NextRequest, NextResponse } from "next/server";
import { VoucherService } from "@/lib/services/voucher.service";
import { z } from "zod";
import { withRBAC } from "@/lib/middleware/rbac-middleware";

// Validation schema for creating vouchers
const createVoucherSchema = z.object({
  tenantId: z.number().int().positive(),
  code: z.string().min(1).max(50),
  name: z.string().min(1).max(255),
  description: z.string().max(1000).optional(),
  type: z.enum(["percentage", "fixed_amount", "free_shipping", "buy_x_get_y"]),
  value: z.number().positive(),
  currency: z.string().length(3).default("USD"),
  usage_limit: z.number().int().positive().optional(),
  usage_limit_per_customer: z.number().int().positive().default(1),
  valid_from: z.string().transform((str) => new Date(str)),
  valid_until: z.string().transform((str) => new Date(str)),
  is_active: z.boolean().default(true),
  is_public: z.boolean().default(true),
  auto_apply: z.boolean().default(false),
  restrictions: z.object({
    min_purchase_amount: z.number().positive().optional(),
    max_discount_amount: z.number().positive().optional(),
    applicable_locations: z.array(z.string()).optional(),
    applicable_packages: z.array(z.string()).optional(),
    applicable_classes: z.array(z.string()).optional(),
    applicable_customer_groups: z.array(z.string()).optional(),
    first_time_customers_only: z.boolean().optional(),
    existing_customers_only: z.boolean().optional(),
  }).optional(),
  created_by: z.string().optional(),
});

// GET /api/vouchers - List vouchers with filters
export async function GET(request: NextRequest) {
  return withRBAC(
    async () => {
      try {
        const { searchParams } = new URL(request.url);
        
        // Parse query parameters
        const tenantId = searchParams.get("tenantId");
        const limit = searchParams.get("limit");
        const offset = searchParams.get("offset");
        const search = searchParams.get("search");
        const type = searchParams.get("type");
        const isActive = searchParams.get("isActive");
        const isPublic = searchParams.get("isPublic");
        const validOnly = searchParams.get("validOnly");

        if (!tenantId) {
          return NextResponse.json(
            { error: "Tenant ID is required" },
            { status: 400 }
          );
        }

        const filters = {
          tenantId: parseInt(tenantId),
          ...(limit && { limit: parseInt(limit) }),
          ...(offset && { offset: parseInt(offset) }),
          filters: {
            ...(search && { search }),
            ...(type && { type }),
            ...(isActive !== null && { is_active: isActive === "true" }),
            ...(isPublic !== null && { is_public: isPublic === "true" }),
            ...(validOnly === "true" && { valid_only: true }),
          },
        };

        const vouchers = await VoucherService.getAll(filters);

        return NextResponse.json({
          success: true,
          data: vouchers,
          count: vouchers.length,
        });
      } catch (error) {
        console.error("Error fetching vouchers:", error);
        return NextResponse.json(
          { 
            error: error instanceof Error ? error.message : "Failed to fetch vouchers",
            success: false 
          },
          { status: 500 }
        );
      }
    },
    { module: "vouchers", action: "read" }
  )(request);
}

// POST /api/vouchers - Create new voucher
export async function POST(request: NextRequest) {
  return withRBAC(
    async () => {
      try {
        const body = await request.json();
        
        // Validate request body
        const validatedData = createVoucherSchema.parse(body);

        // Create voucher
        const voucher = await VoucherService.create(validatedData);

        return NextResponse.json({
          success: true,
          data: voucher,
          message: "Voucher created successfully",
        }, { status: 201 });
      } catch (error) {
        console.error("Error creating voucher:", error);
        
        if (error instanceof z.ZodError) {
          return NextResponse.json(
            { 
              error: "Validation failed",
              details: error.errors,
              success: false 
            },
            { status: 400 }
          );
        }

        return NextResponse.json(
          { 
            error: error instanceof Error ? error.message : "Failed to create voucher",
            success: false 
          },
          { status: 500 }
        );
      }
    },
    { module: "vouchers", action: "create" }
  )(request);
}

// PUT /api/vouchers/bulk - Bulk operations
export async function PUT(request: NextRequest) {
  return withRBAC(
    async () => {
      try {
        const body = await request.json();
        const { operation, ids, data } = body;

        if (!operation || !ids || !Array.isArray(ids)) {
          return NextResponse.json(
            { error: "Invalid bulk operation request" },
            { status: 400 }
          );
        }

        let results = [];

        switch (operation) {
          case "delete":
            for (const id of ids) {
              try {
                await VoucherService.delete(id);
                results.push({ id, success: true });
              } catch (error) {
                results.push({ 
                  id, 
                  success: false, 
                  error: error instanceof Error ? error.message : "Delete failed" 
                });
              }
            }
            break;

          case "toggle_active":
            for (const id of ids) {
              try {
                const voucher = await VoucherService.getById(id);
                if (voucher) {
                  await VoucherService.update(id, { is_active: !voucher.is_active });
                  results.push({ id, success: true });
                } else {
                  results.push({ id, success: false, error: "Voucher not found" });
                }
              } catch (error) {
                results.push({ 
                  id, 
                  success: false, 
                  error: error instanceof Error ? error.message : "Update failed" 
                });
              }
            }
            break;

          case "update":
            if (!data) {
              return NextResponse.json(
                { error: "Update data is required for bulk update" },
                { status: 400 }
              );
            }
            for (const id of ids) {
              try {
                await VoucherService.update(id, data);
                results.push({ id, success: true });
              } catch (error) {
                results.push({ 
                  id, 
                  success: false, 
                  error: error instanceof Error ? error.message : "Update failed" 
                });
              }
            }
            break;

          default:
            return NextResponse.json(
              { error: `Unsupported bulk operation: ${operation}` },
              { status: 400 }
            );
        }

        const successCount = results.filter(r => r.success).length;
        const failureCount = results.filter(r => !r.success).length;

        return NextResponse.json({
          success: true,
          data: results,
          summary: {
            total: ids.length,
            successful: successCount,
            failed: failureCount,
          },
          message: `Bulk ${operation} completed: ${successCount} successful, ${failureCount} failed`,
        });
      } catch (error) {
        console.error("Error in bulk operation:", error);
        return NextResponse.json(
          { 
            error: error instanceof Error ? error.message : "Bulk operation failed",
            success: false 
          },
          { status: 500 }
        );
      }
    },
    { module: "vouchers", action: "manage" }
  )(request);
}
