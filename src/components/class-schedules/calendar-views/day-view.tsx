"use client";

import { useMemo } from "react";
import { motion } from "framer-motion";
import { Plus, Clock, Users, MapPin, Edit, Trash2, Calendar } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { type ClassSchedule } from "@/lib/db/schema";

/**
 * DayView Component
 * 
 * Komponen untuk menampilkan calendar dalam view harian.
 * Menampilkan detail lengkap dengan timeline yang jelas.
 */

interface CalendarEvent extends ClassSchedule {
  class_name?: string;
  location_name?: string;
  instructor_name?: string;
}

interface DayViewProps {
  currentDate: Date;
  schedulesByDate: Record<string, CalendarEvent[]>;
  onDateClick: (date: Date, time?: string) => void;
  onEventClick: (event: CalendarEvent) => void;
  onEditEvent: (event: CalendarEvent) => void;
  onDeleteEvent: (event: CalendarEvent) => void;
  getClassName: (classId: string) => string;
  getLocationName: (locationId: string | null) => string;
  formatTime: (time: Date | string | null) => string;
}

export function DayView({
  currentDate,
  schedulesByDate,
  onDateClick,
  onEventClick,
  onEditEvent,
  onDeleteEvent,
  getClassName,
  getLocationName,
  formatTime,
}: DayViewProps) {
  const dateString = currentDate.toISOString().split('T')[0];
  const daySchedules = schedulesByDate[dateString] || [];

  // Generate time slots (6 AM to 10 PM)
  const timeSlots = useMemo(() => {
    const slots = [];
    for (let hour = 6; hour <= 22; hour++) {
      for (let minute = 0; minute < 60; minute += 30) {
        slots.push({
          hour,
          minute,
          time: `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`,
          displayTime: new Date(0, 0, 0, hour, minute).toLocaleTimeString('en-US', { 
            hour: 'numeric',
            minute: '2-digit',
            hour12: true 
          }),
          isHourStart: minute === 0,
        });
      }
    }
    return slots;
  }, []);

  const isToday = currentDate.toDateString() === new Date().toDateString();

  return (
    <TooltipProvider>
      <div className="day-view">
        {/* Day Header */}
        <Card className="mb-4">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Calendar className="h-5 w-5" />
                <div>
                  <CardTitle className="text-lg">
                    {currentDate.toLocaleDateString('en-US', { 
                      weekday: 'long',
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </CardTitle>
                  {isToday && (
                    <Badge variant="default" className="mt-1">
                      Today
                    </Badge>
                  )}
                </div>
              </div>
              
              <div className="flex items-center gap-2">
                <div className="text-sm text-muted-foreground">
                  {daySchedules.length} schedule{daySchedules.length !== 1 ? 's' : ''}
                </div>
                <Button
                  onClick={() => onDateClick(currentDate)}
                  size="sm"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Schedule
                </Button>
              </div>
            </div>
          </CardHeader>
        </Card>

        {/* Timeline */}
        <Card>
          <CardContent className="p-0">
            <div className="relative">
              {/* Current time indicator for today */}
              {isToday && (
                <div 
                  className="absolute left-0 right-0 z-10 border-t-2 border-red-500"
                  style={{
                    top: `${((new Date().getHours() - 6) * 2 + (new Date().getMinutes() / 30)) * 60}px`,
                  }}
                >
                  <div className="absolute -left-2 -top-2 w-4 h-4 bg-red-500 rounded-full"></div>
                  <div className="absolute left-4 -top-3 bg-red-500 text-white text-xs px-2 py-1 rounded">
                    Now
                  </div>
                </div>
              )}

              {/* Time Grid */}
              {timeSlots.map((slot, slotIndex) => {
                const slotSchedules = daySchedules.filter(schedule => {
                  if (!schedule.start_time) return false;
                  const scheduleTime = new Date(schedule.start_time);
                  return scheduleTime.getHours() === slot.hour && 
                         Math.floor(scheduleTime.getMinutes() / 30) * 30 === slot.minute;
                });

                return (
                  <motion.div
                    key={`${slot.hour}-${slot.minute}`}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.1, delay: slotIndex * 0.01 }}
                    className={`
                      flex border-b min-h-[60px] relative cursor-pointer
                      hover:bg-muted/30 transition-colors
                      ${slot.isHourStart ? 'border-b-2' : 'border-b border-muted'}
                      ${slotSchedules.length > 0 ? (
                        slotSchedules.length > 1 ? 'bg-red-50' :
                        'bg-green-50'
                      ) : ''}
                    `}
                    onClick={() => onDateClick(currentDate, slot.time)}
                  >
                    {/* Time Label */}
                    <div className="w-20 p-2 border-r bg-muted/20 text-xs text-muted-foreground text-right">
                      {slot.isHourStart && slot.displayTime}
                    </div>
                    
                    {/* Content Area */}
                    <div className="flex-1 p-2 relative">
                      {/* Quick Add Button */}
                      <Button
                        variant="ghost"
                        size="sm"
                        className="absolute top-1 right-1 h-6 w-6 p-0 opacity-0 hover:opacity-100 transition-opacity"
                        onClick={(e) => {
                          e.stopPropagation();
                          onDateClick(currentDate, slot.time);
                        }}
                      >
                        <Plus className="h-3 w-3" />
                      </Button>

                      {/* Schedules */}
                      <div className="space-y-2">
                        {slotSchedules.map((schedule, scheduleIndex) => (
                          <Tooltip key={schedule.id}>
                            <TooltipTrigger asChild>
                              <motion.div
                                initial={{ opacity: 0, x: -20 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ duration: 0.3, delay: scheduleIndex * 0.1 }}
                                className={`
                                  p-3 rounded-lg cursor-pointer
                                  hover:shadow-lg transition-all
                                  group relative border-l-4
                                `}
                                style={{
                                  backgroundColor: `${schedule.calender_color || '#3b82f6'}15`,
                                  borderLeftColor: schedule.calender_color || '#3b82f6',
                                }}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  onEventClick(schedule);
                                }}
                              >
                                <div className="flex items-start justify-between">
                                  <div className="flex-1 space-y-2">
                                    <div className="font-semibold text-sm">
                                      {schedule.class_name || getClassName(schedule.class_id)}
                                    </div>
                                    
                                    <div className="grid grid-cols-2 gap-4 text-xs text-muted-foreground">
                                      <div className="flex items-center gap-1">
                                        <Clock className="h-3 w-3" />
                                        {formatTime(schedule.start_time)} - {formatTime(schedule.end_time)}
                                      </div>
                                      
                                      {schedule.location_id && (
                                        <div className="flex items-center gap-1">
                                          <MapPin className="h-3 w-3" />
                                          {getLocationName(schedule.location_id)}
                                        </div>
                                      )}
                                      
                                      <div className="flex items-center gap-1">
                                        <Users className="h-3 w-3" />
                                        Capacity: {schedule.pax}
                                      </div>
                                      
                                      <div className="flex items-center gap-1">
                                        <Users className="h-3 w-3" />
                                        Waitlist: {schedule.waitlist}
                                      </div>
                                    </div>

                                    <div className="flex gap-2">
                                      {schedule.is_private && (
                                        <Badge variant="secondary" className="text-xs">
                                          Private
                                        </Badge>
                                      )}
                                      {schedule.allow_classpass && (
                                        <Badge variant="outline" className="text-xs">
                                          Class Pass
                                        </Badge>
                                      )}
                                      {schedule.repeat_rule && schedule.repeat_rule !== "none" && (
                                        <Badge variant="outline" className="text-xs">
                                          {schedule.repeat_rule}
                                        </Badge>
                                      )}
                                    </div>
                                  </div>
                                  
                                  {/* Quick Actions */}
                                  <div className="opacity-0 group-hover:opacity-100 transition-opacity flex gap-1 ml-2">
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      className="h-8 w-8 p-0"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        onEditEvent(schedule);
                                      }}
                                    >
                                      <Edit className="h-4 w-4" />
                                    </Button>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      className="h-8 w-8 p-0 text-red-500 hover:text-red-700"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        onDeleteEvent(schedule);
                                      }}
                                    >
                                      <Trash2 className="h-4 w-4" />
                                    </Button>
                                  </div>
                                </div>
                              </motion.div>
                            </TooltipTrigger>
                            <TooltipContent side="right" className="max-w-xs">
                              <div className="space-y-2">
                                <div className="font-medium">
                                  {schedule.class_name || getClassName(schedule.class_id)}
                                </div>
                                <div className="text-xs space-y-1">
                                  <div>Duration: {schedule.duration} minutes</div>
                                  {schedule.late_cancellation_rule && (
                                    <div>Cancellation: {schedule.late_cancellation_rule}</div>
                                  )}
                                  {schedule.booking_window_start && (
                                    <div>Booking opens: {formatTime(schedule.booking_window_start)}</div>
                                  )}
                                </div>
                              </div>
                            </TooltipContent>
                          </Tooltip>
                        ))}
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      </div>
    </TooltipProvider>
  );
}
