"use client";

import { useEffect, useState } from "react";
import { usePathname } from "next/navigation";
import { getDictionary, type Dictionary } from "@/lib/i18n/dictionaries";
import { getLocaleFromPathname, defaultLocale, type Locale } from "@/lib/i18n/config";

export function useDictionary() {
  const pathname = usePathname();
  const locale = getLocaleFromPathname(pathname);
  const [dictionary, setDictionary] = useState<Dictionary | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadDictionary = async () => {
      setIsLoading(true);
      try {
        const dict = await getDictionary(locale);
        setDictionary(dict);
      } catch (error) {
        console.error("Failed to load dictionary:", error);
        // Fallback to default locale
        const fallbackDict = await getDictionary(defaultLocale);
        setDictionary(fallbackDict);
      } finally {
        setIsLoading(false);
      }
    };

    loadDictionary();
  }, [locale]);

  return {
    dictionary,
    locale,
    isLoading,
    t: dictionary || {},
  };
}

// Helper function to get nested translation values
export function getNestedValue(obj: any, path: string): string {
  return path.split('.').reduce((current, key) => current?.[key], obj) || path;
}

// Translation function with dot notation support
export function useTranslation() {
  const { dictionary, locale, isLoading } = useDictionary();

  const t = (key: string, fallback?: string): string => {
    if (!dictionary) return fallback || key;
    
    const value = getNestedValue(dictionary, key);
    return typeof value === 'string' ? value : fallback || key;
  };

  return {
    t,
    locale,
    isLoading,
    dictionary,
  };
}
