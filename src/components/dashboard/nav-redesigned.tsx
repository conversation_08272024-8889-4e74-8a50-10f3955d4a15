"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";
import { useRBAC } from "@/lib/hooks/use-rbac";
import {
  LayoutDashboard,
  CreditCard,
  Settings,
  Users,
  BarChart3,
  FileText,
  Shield,
  Zap,
  MapPin,
  Wrench,
  Layers,
  GraduationCap,
  DollarSign,
  User,
  Tag,
  Package,
  BookOpen,
  FolderOpen,
  Grid3X3,
  Building,
  MapPinCheck,
  Calendar,
  ChevronDown,
  ChevronRight,
  TicketPercent,
  Menu,
  X,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import type { AuthUser } from "@/types/auth";

interface DashboardNavProps {
  user: AuthUser;
}

interface NavItem {
  title: string;
  href: string;
  icon: any;
  badge?: string;
  requiredPermission?: {
    module: string;
    action: string;
  };
  children?: NavItem[];
}

// Reorganized navigation structure following FAANG patterns
const navItems: NavItem[] = [
  {
    title: "Dashboard",
    href: "/dashboard",
    icon: LayoutDashboard,
  },
  {
    title: "Class Management",
    href: "/classes",
    icon: BookOpen,
    requiredPermission: { module: "classes", action: "manage" },
    children: [
      {
        title: "Schedules",
        href: "/class-schedules",
        icon: Calendar,
        requiredPermission: { module: "class-schedules", action: "manage" },
      },
      {
        title: "Bookings",
        href: "/class-bookings",
        icon: MapPinCheck,
        requiredPermission: { module: "bookings", action: "manage" },
      },
      {
        title: "Classes",
        href: "/classes",
        icon: BookOpen,
        requiredPermission: { module: "classes", action: "manage" },
      },
      {
        title: "Categories",
        href: "/class-categories",
        icon: FolderOpen,
        requiredPermission: { module: "classes", action: "manage" },
      },
      {
        title: "Subcategories",
        href: "/class-subcategories",
        icon: Grid3X3,
        requiredPermission: { module: "classes", action: "manage" },
      },
      {
        title: "Levels",
        href: "/class-levels",
        icon: GraduationCap,
        requiredPermission: { module: "class-level", action: "manage" },
      },
    ],
  },
  {
    title: "Package Management",
    href: "/packages",
    icon: Package,
    requiredPermission: { module: "packages", action: "manage" },
    children: [
      {
        title: "Packages",
        href: "/packages",
        icon: Package,
        requiredPermission: { module: "packages", action: "manage" },
      },
      {
        title: "Package Locations",
        href: "/package-locations",
        icon: MapPin,
        requiredPermission: { module: "packages", action: "manage" },
      },
      {
        title: "Categories",
        href: "/package-categories",
        icon: FolderOpen,
        requiredPermission: { module: "packages", action: "manage" },
      },
      {
        title: "Pricing Groups",
        href: "/pricing-groups",
        icon: DollarSign,
        requiredPermission: { module: "packages", action: "manage" },
      },
      {
        title: "Package Pricing",
        href: "/package-pricing",
        icon: DollarSign,
        requiredPermission: { module: "packages", action: "manage" },
      },
      {
        title: "Vouchers",
        href: "/vouchers",
        icon: TicketPercent,
        requiredPermission: { module: "vouchers", action: "manage" },
      },
    ],
  },
  {
    title: "Customers",
    href: "/customers",
    icon: Users,
    requiredPermission: { module: "customers", action: "manage" },
  },
  {
    title: "Membership Plans",
    href: "/membership-plans",
    icon: CreditCard,
    requiredPermission: { module: "packages", action: "manage" },
  },
  {
    title: "Master Data",
    href: "/locations",
    icon: Building,
    requiredPermission: { module: "locations", action: "manage" },
    children: [
      {
        title: "Locations",
        href: "/locations",
        icon: MapPin,
        requiredPermission: { module: "locations", action: "manage" },
      },
      {
        title: "Facilities",
        href: "/facilities",
        icon: Building,
        requiredPermission: { module: "facilities", action: "manage" },
      },
      {
        title: "Equipment",
        href: "/equipment",
        icon: Wrench,
        requiredPermission: { module: "equipment", action: "manage" },
      },
      {
        title: "Equipment Instances",
        href: "/equipment-instances",
        icon: Layers,
        requiredPermission: { module: "equipment-instances", action: "manage" },
      },
      {
        title: "Tags",
        href: "/tags",
        icon: Tag,
        requiredPermission: { module: "tags", action: "manage" },
      },
    ],
  },
  {
    title: "Administration",
    href: "/admin",
    icon: Shield,
    requiredPermission: { module: "admin", action: "access" },
    children: [
      {
        title: "Role Management",
        href: "/role-management",
        icon: Shield,
        requiredPermission: { module: "role-management", action: "manage" },
      },
      {
        title: "API Keys",
        href: "/api-keys",
        icon: Settings,
        requiredPermission: { module: "admin", action: "manage" },
      },
      {
        title: "Analytics",
        href: "/analytics",
        icon: BarChart3,
        requiredPermission: { module: "reports", action: "read" },
      },
      {
        title: "Waiver Forms",
        href: "/waiver-forms",
        icon: FileText,
        requiredPermission: { module: "customers", action: "manage" },
      },
      {
        title: "Blog Management",
        href: "/blog-management",
        icon: FileText,
        requiredPermission: { module: "blog", action: "manage" },
      },
    ],
  },
  {
    title: "Settings",
    href: "/settings",
    icon: Settings,
    children: [
      {
        title: "Business Settings",
        href: "/business",
        icon: Zap,
      },
      {
        title: "Billing",
        href: "/billing",
        icon: CreditCard,
        requiredPermission: { module: "admin", action: "manage" },
      },
      {
        title: "Documentation",
        href: "/docs",
        icon: FileText,
      },
      {
        title: "RBAC Test",
        href: "/rbac-test",
        icon: Shield,
      },
      {
        title: "Animation Demo",
        href: "/animation-demo",
        icon: Zap,
      },
      {
        title: "TanStack Demo",
        href: "/business-tanstack",
        icon: Layers,
      },
    ],
  },
];

// Animation variants
const sidebarVariants = {
  open: {
    width: "280px",
    transition: {
      type: "spring",
      stiffness: 300,
      damping: 30,
    },
  },
  closed: {
    width: "80px",
    transition: {
      type: "spring",
      stiffness: 300,
      damping: 30,
    },
  },
};

const itemVariants = {
  open: {
    opacity: 1,
    x: 0,
    transition: {
      type: "spring",
      stiffness: 300,
      damping: 30,
    },
  },
  closed: {
    opacity: 0,
    x: -20,
    transition: {
      type: "spring",
      stiffness: 300,
      damping: 30,
    },
  },
};

const childrenVariants = {
  open: {
    opacity: 1,
    height: "auto",
    transition: {
      type: "spring",
      stiffness: 300,
      damping: 30,
      staggerChildren: 0.05,
    },
  },
  closed: {
    opacity: 0,
    height: 0,
    transition: {
      type: "spring",
      stiffness: 300,
      damping: 30,
    },
  },
};

export function DashboardNavRedesigned({ user }: DashboardNavProps) {
  const pathname = usePathname();
  const { hasPermission, isLoading, permissions, roles } = useRBAC();
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isMobileOpen, setIsMobileOpen] = useState(false);
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(
    new Set(["Class Management", "Package Management"])
  );

  // Close mobile menu on route change
  useEffect(() => {
    setIsMobileOpen(false);
  }, [pathname]);

  const toggleGroup = (groupTitle: string) => {
    if (isCollapsed) return;
    
    setExpandedGroups((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(groupTitle)) {
        newSet.delete(groupTitle);
      } else {
        newSet.add(groupTitle);
      }
      return newSet;
    });
  };

  const isGroupExpanded = (groupTitle: string) =>
    !isCollapsed && expandedGroups.has(groupTitle);

  const isGroupActive = (item: NavItem) => {
    if (item.children) {
      return item.children.some(
        (child) =>
          pathname === child.href ||
          (child.href !== "/dashboard" && pathname.startsWith(child.href))
      );
    }
    return (
      pathname === item.href ||
      (item.href !== "/dashboard" && pathname.startsWith(item.href))
    );
  };

  // Filter nav items based on RBAC permissions
  const filteredNavItems = navItems.filter((item) => {
    // Handle grouped items
    if (item.children) {
      // Filter children first
      const filteredChildren = item.children.filter((child) => {
        if (!child.requiredPermission) {
          return true;
        }

        if (user?.email === "<EMAIL>") {
          return true;
        }

        const permissionString = `${child.requiredPermission.module}.${child.requiredPermission.action}`;
        return hasPermission(permissionString);
      });

      // Show group if it has any accessible children
      if (filteredChildren.length > 0) {
        item.children = filteredChildren;
        return true;
      }
      return false;
    }

    // Handle regular items
    if (!item.requiredPermission) {
      return true;
    }

    if (user?.email === "<EMAIL>") {
      return true;
    }

    const permissionString = `${item.requiredPermission.module}.${item.requiredPermission.action}`;
    return hasPermission(permissionString);
  });

  // Loading state component
  const LoadingNav = () => (
    <motion.nav
      className="fixed left-0 top-16 z-40 h-[calc(100vh-4rem)] w-80 border-r border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60"
      initial={{ x: -320 }}
      animate={{ x: 0 }}
      transition={{ type: "spring", stiffness: 300, damping: 30 }}
    >
      <div className="flex h-full flex-col">
        <div className="flex items-center justify-between p-6 border-b border-border/40">
          <div className="h-6 w-32 bg-muted animate-pulse rounded" />
          <div className="h-8 w-8 bg-muted animate-pulse rounded" />
        </div>
        <ScrollArea className="flex-1 px-4">
          <div className="space-y-2 py-4">
            {Array.from({ length: 8 }).map((_, i) => (
              <div key={i} className="flex items-center space-x-3 rounded-lg px-3 py-2">
                <div className="h-5 w-5 bg-muted animate-pulse rounded" />
                <div className="h-4 w-24 bg-muted animate-pulse rounded" />
              </div>
            ))}
          </div>
        </ScrollArea>
      </div>
    </motion.nav>
  );

  // Mobile overlay
  const MobileOverlay = () => (
    <AnimatePresence>
      {isMobileOpen && (
        <motion.div
          className="fixed inset-0 z-30 bg-black/50 lg:hidden"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          onClick={() => setIsMobileOpen(false)}
        />
      )}
    </AnimatePresence>
  );

  // Navigation item component
  const NavItemComponent = ({ item, level = 0 }: { item: NavItem; level?: number }) => {
    const isActive = pathname === item.href ||
      (item.href !== "/dashboard" && pathname.startsWith(item.href));
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = hasChildren && isGroupExpanded(item.title);
    const isGroupActiveState = hasChildren && isGroupActive(item);

    if (hasChildren) {
      return (
        <div key={item.title}>
          <button
            onClick={() => toggleGroup(item.title)}
            className={cn(
              "group flex w-full items-center justify-between rounded-xl px-3 py-2.5 text-sm font-medium transition-all duration-200",
              "hover:bg-accent/50 focus:bg-accent/50 focus:outline-none focus:ring-2 focus:ring-primary/20",
              isGroupActiveState
                ? "bg-primary/10 text-primary shadow-sm"
                : "text-muted-foreground hover:text-foreground",
              level > 0 && "ml-4"
            )}
            aria-expanded={isExpanded}
            aria-controls={`nav-group-${item.title}`}
          >
            <div className="flex items-center space-x-3">
              <item.icon
                className={cn(
                  "h-5 w-5 transition-colors duration-200",
                  isGroupActiveState ? "text-primary" : "text-muted-foreground group-hover:text-foreground"
                )}
              />
              {!isCollapsed && (
                <motion.span
                  variants={itemVariants}
                  className="truncate"
                >
                  {item.title}
                </motion.span>
              )}
              {item.badge && !isCollapsed && (
                <Badge variant="secondary" className="ml-auto text-xs">
                  {item.badge}
                </Badge>
              )}
            </div>
            {!isCollapsed && hasChildren && (
              <motion.div
                animate={{ rotate: isExpanded ? 180 : 0 }}
                transition={{ duration: 0.2 }}
              >
                <ChevronDown className="h-4 w-4" />
              </motion.div>
            )}
          </button>

          <AnimatePresence>
            {isExpanded && (
              <motion.div
                id={`nav-group-${item.title}`}
                variants={childrenVariants}
                initial="closed"
                animate="open"
                exit="closed"
                className="overflow-hidden"
              >
                <div className="ml-6 mt-1 space-y-1 border-l border-border/40 pl-4">
                  {item.children?.map((child) => (
                    <NavItemComponent key={child.href} item={child} level={level + 1} />
                  ))}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      );
    }

    return (
      <Link
        key={item.href}
        href={item.href}
        className={cn(
          "group flex items-center space-x-3 rounded-xl px-3 py-2.5 text-sm font-medium transition-all duration-200",
          "hover:bg-accent/50 focus:bg-accent/50 focus:outline-none focus:ring-2 focus:ring-primary/20",
          isActive
            ? "bg-primary text-primary-foreground shadow-sm"
            : "text-muted-foreground hover:text-foreground",
          level > 0 && "ml-4"
        )}
        aria-current={isActive ? "page" : undefined}
      >
        <item.icon
          className={cn(
            "h-5 w-5 transition-colors duration-200",
            isActive ? "text-primary-foreground" : "text-muted-foreground group-hover:text-foreground"
          )}
        />
        {!isCollapsed && (
          <motion.span
            variants={itemVariants}
            className="truncate"
          >
            {item.title}
          </motion.span>
        )}
        {item.badge && !isCollapsed && (
          <Badge variant="secondary" className="ml-auto text-xs">
            {item.badge}
          </Badge>
        )}
      </Link>
    );
  };

  // Show loading state while RBAC data is loading
  if (isLoading || permissions.length === 0) {
    return (
      <>
        <LoadingNav />
        <MobileOverlay />
      </>
    );
  }

  return (
    <>
      {/* Mobile Menu Button */}
      <Button
        variant="ghost"
        size="icon"
        className="fixed top-4 left-4 z-50 lg:hidden"
        onClick={() => setIsMobileOpen(!isMobileOpen)}
        aria-label="Toggle navigation menu"
      >
        {isMobileOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
      </Button>

      {/* Mobile Overlay */}
      <MobileOverlay />

      {/* Navigation Sidebar */}
      <motion.nav
        variants={sidebarVariants}
        animate={isCollapsed ? "closed" : "open"}
        className={cn(
          "fixed left-0 top-16 z-40 h-[calc(100vh-4rem)] border-r border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60",
          "lg:translate-x-0",
          isMobileOpen ? "translate-x-0" : "-translate-x-full lg:translate-x-0"
        )}
        initial={false}
        transition={{ type: "spring", stiffness: 300, damping: 30 }}
      >
        <div className="flex h-full flex-col">
          {/* Navigation Header */}
          <div className="flex items-center justify-between p-6 border-b border-border/40">
            {!isCollapsed && (
              <motion.div
                variants={itemVariants}
                className="flex items-center space-x-2"
              >
                <div className="h-8 w-8 bg-gradient-to-br from-primary to-primary/60 rounded-lg flex items-center justify-center">
                  <LayoutDashboard className="h-4 w-4 text-primary-foreground" />
                </div>
                <span className="text-lg font-semibold bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
                  Navigation
                </span>
              </motion.div>
            )}

            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="hidden lg:flex h-8 w-8 rounded-lg"
              aria-label={isCollapsed ? "Expand navigation" : "Collapse navigation"}
            >
              <motion.div
                animate={{ rotate: isCollapsed ? 180 : 0 }}
                transition={{ duration: 0.2 }}
              >
                <ChevronRight className="h-4 w-4" />
              </motion.div>
            </Button>
          </div>

          {/* Navigation Content */}
          <ScrollArea className="flex-1 px-4">
            <motion.div
              className="space-y-2 py-4"
              variants={{
                open: {
                  transition: { staggerChildren: 0.05 }
                }
              }}
              initial="closed"
              animate="open"
            >
              {filteredNavItems.map((item) => (
                <motion.div
                  key={item.title}
                  variants={itemVariants}
                >
                  <NavItemComponent item={item} />
                </motion.div>
              ))}
            </motion.div>
          </ScrollArea>

          {/* Navigation Footer */}
          {!isCollapsed && (
            <motion.div
              variants={itemVariants}
              className="border-t border-border/40 p-4"
            >
              <div className="flex items-center space-x-3 rounded-lg bg-muted/50 px-3 py-2">
                <div className="h-8 w-8 rounded-full bg-primary/10 flex items-center justify-center">
                  <User className="h-4 w-4 text-primary" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-foreground truncate">
                    {user.name || "User"}
                  </p>
                  <p className="text-xs text-muted-foreground truncate">
                    {user.email}
                  </p>
                </div>
              </div>
            </motion.div>
          )}
        </div>
      </motion.nav>
    </>
  );
}
