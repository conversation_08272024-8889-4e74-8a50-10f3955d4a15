import { sql, eq, and, ilike, SQL } from "drizzle-orm";
import { packages, type Package, type NewPackage, type PackageScheduleAvailabilityData, type PackageIncludedClass } from "@/lib/db/schema";
import { BaseService, type CreateEntityData, type UpdateEntityData, type QueryOptions } from "@/lib/core/base-service";
import { db } from "@/lib/db";
import { createId } from "@paralleldrive/cuid2";

// Package data transfer objects
export interface CreatePackageData extends CreateEntityData {
  tenantId: number;
  name: string;
  description?: string;
  isActive?: boolean;
  is_private?: boolean;
  validity_date?: string;
  validity_duration?: number;
  schedule_availability?: PackageScheduleAvailabilityData[];
  included_classes?: PackageIncludedClass[];
}

export interface UpdatePackageData extends UpdateEntityData {
  tenantId?: number;
  name?: string;
  description?: string;
  isActive?: boolean;
  is_private?: boolean;
  validity_date?: string;
  validity_duration?: number;
  schedule_availability?: PackageScheduleAvailabilityData[];
  included_classes?: PackageIncludedClass[];
}

export interface PackageFilters {
  search?: string;
  tenantId?: number;
  isActive?: boolean;
  is_private?: boolean;
}

export interface PackageStats {
  total: number;
  active: number;
  private: number;
  byTenant: Record<number, number>;
}

class PackageService extends BaseService<Package, CreatePackageData, UpdatePackageData> {
  constructor() {
    super(packages, "PackageService");
  }

  /**
   * Create a new package
   */
  async create(data: CreatePackageData): Promise<Package> {
    console.log(`📦 [PackageService] Creating package:`, data);

    this.validateCreateData(data);

    // Check if package name already exists for this tenant
    const existing = await this.getByNameAndTenant(data.name, data.tenantId);
    if (existing) {
      throw new Error("Package with this name already exists for this tenant");
    }

    const packageData: NewPackage = {
      id: createId(),
      tenantId: data.tenantId,
      name: data.name,
      description: data.description || null,
      isActive: data.isActive ?? true,
      is_private: data.is_private ?? false,
      validity_date: data.validity_date || null,
      validity_duration: data.validity_duration || null,
      schedule_availability: data.schedule_availability || [],
      included_classes: data.included_classes || [],
    };

    const [pkg] = await db.insert(packages).values(packageData).returning();
    console.log(`✅ [PackageService] Package created:`, pkg);
    return pkg;
  }

  /**
   * Update package
   */
  async update(id: string, data: UpdatePackageData): Promise<Package> {
    console.log(`📦 [PackageService] Updating package ${id}:`, data);

    this.validateUpdateData(data);

    const updateData: Partial<NewPackage> = {};
    if (data.tenantId !== undefined) updateData.tenantId = data.tenantId;
    if (data.name !== undefined) updateData.name = data.name;
    if (data.description !== undefined) updateData.description = data.description;
    if (data.isActive !== undefined) updateData.isActive = data.isActive;
    if (data.is_private !== undefined) updateData.is_private = data.is_private;
    if (data.validity_date !== undefined) updateData.validity_date = data.validity_date;
    if (data.validity_duration !== undefined) updateData.validity_duration = data.validity_duration;
    if (data.schedule_availability !== undefined) updateData.schedule_availability = data.schedule_availability;
    if (data.included_classes !== undefined) updateData.included_classes = data.included_classes;

    // Check for duplicate name if name is being changed
    if (data.name) {
      const current = await this.getById(id);
      if (!current) {
        throw new Error("Package not found");
      }

      if (data.name !== current.name) {
        const tenantId = data.tenantId || current.tenantId;
        const existing = await this.getByNameAndTenant(data.name, tenantId);
        if (existing && existing.id !== id) {
          throw new Error("Package with this name already exists for this tenant");
        }
      }
    }

    updateData.updatedAt = new Date();

    const [updatedPackage] = await db
      .update(packages)
      .set(updateData)
      .where(eq(packages.id, id))
      .returning();

    if (!updatedPackage) {
      throw new Error("Package not found");
    }

    console.log(`✅ [PackageService] Package updated:`, updatedPackage);
    return updatedPackage;
  }

  /**
   * Get package by name and tenant
   */
  async getByNameAndTenant(name: string, tenantId: number): Promise<Package | null> {
    const [result] = await db
      .select()
      .from(packages)
      .where(
        and(
          eq(packages.name, name),
          eq(packages.tenantId, tenantId)
        )
      )
      .limit(1);

    return result || null;
  }

  /**
   * Get packages by tenant with filters
   */
  async getByTenant(tenantId: number, filters: PackageFilters = {}): Promise<Package[]> {
    console.log(`📦 [PackageService] Getting packages for tenant ${tenantId}:`, filters);

    const conditions: SQL[] = [];

    // Only add tenant filter if tenantId > 0
    if (tenantId > 0) {
      conditions.push(eq(packages.tenantId, tenantId));
    }

    if (filters.isActive !== undefined) {
      conditions.push(eq(packages.isActive, filters.isActive));
    }

    if (filters.is_private !== undefined) {
      conditions.push(eq(packages.is_private, filters.is_private));
    }

    if (filters.search) {
      conditions.push(
        sql`(${ilike(packages.name, `%${filters.search}%`)} OR ${ilike(packages.description, `%${filters.search}%`)})`
      );
    }

    const result = await db
      .select()
      .from(packages)
      .where(conditions.length > 0 ? and(...conditions) : undefined)
      .orderBy(packages.name);

    console.log(`✅ [PackageService] Found ${result.length} packages`);
    return result;
  }

  /**
   * Get packages statistics
   */
  async getStats(tenantId?: number): Promise<PackageStats> {
    const conditions: SQL[] = [];
    if (tenantId) {
      conditions.push(eq(packages.tenantId, tenantId));
    }

    const allPackages = await db
      .select()
      .from(packages)
      .where(conditions.length > 0 ? and(...conditions) : undefined);

    const stats: PackageStats = {
      total: allPackages.length,
      active: allPackages.filter(p => p.isActive).length,
      private: allPackages.filter(p => p.is_private).length,
      byTenant: {},
    };

    // Group by tenant
    allPackages.forEach(pkg => {
      stats.byTenant[pkg.tenantId] = (stats.byTenant[pkg.tenantId] || 0) + 1;
    });

    return stats;
  }

  /**
   * Validate create data
   */
  protected validateCreateData(data: CreatePackageData): void {
    if (!data.name?.trim()) {
      throw new Error("Package name is required");
    }
    if (data.name.length > 255) {
      throw new Error("Package name must be less than 255 characters");
    }
    if (!data.tenantId || data.tenantId <= 0) {
      throw new Error("Valid tenant ID is required");
    }
    if (data.validity_duration !== undefined && data.validity_duration < 0) {
      throw new Error("Validity duration cannot be negative");
    }
  }

  /**
   * Validate update data
   */
  protected validateUpdateData(data: UpdatePackageData): void {
    if (data.name !== undefined && !data.name?.trim()) {
      throw new Error("Package name cannot be empty");
    }
    if (data.name && data.name.length > 255) {
      throw new Error("Package name must be less than 255 characters");
    }
    if (data.tenantId !== undefined && data.tenantId <= 0) {
      throw new Error("Valid tenant ID is required");
    }
    if (data.validity_duration !== undefined && data.validity_duration < 0) {
      throw new Error("Validity duration cannot be negative");
    }
  }

  /**
   * Build filter conditions for queries
   */
  protected buildFilterConditions(filters: PackageFilters): SQL[] {
    const conditions: SQL[] = [];

    if (filters.search) {
      conditions.push(
        sql`(${ilike(packages.name, `%${filters.search}%`)} OR ${ilike(packages.description, `%${filters.search}%`)})`
      );
    }

    if (filters.tenantId !== undefined) {
      conditions.push(eq(packages.tenantId, filters.tenantId));
    }

    if (filters.isActive !== undefined) {
      conditions.push(eq(packages.isActive, filters.isActive));
    }

    if (filters.is_private !== undefined) {
      conditions.push(eq(packages.is_private, filters.is_private));
    }

    return conditions;
  }
}

// Export singleton instance
export const packageService = new PackageService();

// Export types for external use
export type { CreatePackageData, UpdatePackageData, PackageFilters, PackageStats };
