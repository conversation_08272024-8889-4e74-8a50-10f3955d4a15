CREATE TABLE "customer_auth_logs" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"customer_id" varchar(255),
	"tenant_id" integer NOT NULL,
	"event" varchar(50) NOT NULL,
	"method" varchar(50) NOT NULL,
	"status" varchar(20) NOT NULL,
	"email" varchar(255),
	"ip_address" varchar(45) NOT NULL,
	"user_agent" text,
	"device_type" varchar(50),
	"device_id" varchar(255),
	"oauth_provider" varchar(50),
	"oauth_account_id" varchar(255),
	"error_code" varchar(50),
	"error_message" text,
	"risk_score" integer,
	"is_blocked" boolean DEFAULT false NOT NULL,
	"block_reason" varchar(100),
	"session_id" varchar(255),
	"location" varchar(255),
	"timezone" varchar(50),
	"metadata" jsonb,
	"created_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL
);
--> statement-breakpoint
CREATE TABLE "customer_oauth_challenges" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"tenant_id" integer NOT NULL,
	"code_challenge" varchar(255) NOT NULL,
	"code_challenge_method" varchar(10) NOT NULL,
	"state" varchar(255) NOT NULL,
	"redirect_uri" text NOT NULL,
	"scope" varchar(255) DEFAULT 'openid email profile' NOT NULL,
	"client_type" varchar(20) NOT NULL,
	"ip_address" varchar(45) NOT NULL,
	"user_agent" text,
	"device_id" varchar(255),
	"is_used" boolean DEFAULT false NOT NULL,
	"used_at" timestamp,
	"expires_at" timestamp NOT NULL,
	"created_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT "customer_oauth_challenges_state_unique" UNIQUE("state")
);
--> statement-breakpoint
CREATE TABLE "customer_sessions" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"customer_id" varchar(255) NOT NULL,
	"tenant_id" integer NOT NULL,
	"jti" varchar(255) NOT NULL,
	"token_hash" varchar(255) NOT NULL,
	"refresh_token_hash" varchar(255),
	"device_type" varchar(50),
	"device_id" varchar(255),
	"user_agent" text,
	"ip_address" varchar(45),
	"location" varchar(255),
	"oauth_provider" varchar(50),
	"oauth_account_id" varchar(255),
	"issued_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"expires_at" timestamp NOT NULL,
	"refresh_expires_at" timestamp,
	"last_used_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"revoked_at" timestamp,
	"revoked_by" varchar(255),
	"revoked_reason" varchar(100),
	"is_active" boolean DEFAULT true NOT NULL,
	"is_suspicious" boolean DEFAULT false NOT NULL,
	"created_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updated_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT "customer_sessions_jti_unique" UNIQUE("jti")
);
--> statement-breakpoint
ALTER TABLE "customer_auth_logs" ADD CONSTRAINT "customer_auth_logs_customer_id_customers_id_fk" FOREIGN KEY ("customer_id") REFERENCES "public"."customers"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "customer_auth_logs" ADD CONSTRAINT "customer_auth_logs_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "customer_auth_logs" ADD CONSTRAINT "customer_auth_logs_session_id_customer_sessions_id_fk" FOREIGN KEY ("session_id") REFERENCES "public"."customer_sessions"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "customer_oauth_challenges" ADD CONSTRAINT "customer_oauth_challenges_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "customer_sessions" ADD CONSTRAINT "customer_sessions_customer_id_customers_id_fk" FOREIGN KEY ("customer_id") REFERENCES "public"."customers"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "customer_sessions" ADD CONSTRAINT "customer_sessions_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "customer_auth_logs_customer_id_idx" ON "customer_auth_logs" USING btree ("customer_id");--> statement-breakpoint
CREATE INDEX "customer_auth_logs_tenant_id_idx" ON "customer_auth_logs" USING btree ("tenant_id");--> statement-breakpoint
CREATE INDEX "customer_auth_logs_event_idx" ON "customer_auth_logs" USING btree ("event");--> statement-breakpoint
CREATE INDEX "customer_auth_logs_status_idx" ON "customer_auth_logs" USING btree ("status");--> statement-breakpoint
CREATE INDEX "customer_auth_logs_ip_address_idx" ON "customer_auth_logs" USING btree ("ip_address");--> statement-breakpoint
CREATE INDEX "customer_auth_logs_created_at_idx" ON "customer_auth_logs" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "customer_auth_logs_email_idx" ON "customer_auth_logs" USING btree ("email");--> statement-breakpoint
CREATE INDEX "customer_oauth_challenges_state_idx" ON "customer_oauth_challenges" USING btree ("state");--> statement-breakpoint
CREATE INDEX "customer_oauth_challenges_tenant_id_idx" ON "customer_oauth_challenges" USING btree ("tenant_id");--> statement-breakpoint
CREATE INDEX "customer_oauth_challenges_expires_at_idx" ON "customer_oauth_challenges" USING btree ("expires_at");--> statement-breakpoint
CREATE INDEX "customer_sessions_customer_id_idx" ON "customer_sessions" USING btree ("customer_id");--> statement-breakpoint
CREATE INDEX "customer_sessions_tenant_id_idx" ON "customer_sessions" USING btree ("tenant_id");--> statement-breakpoint
CREATE INDEX "customer_sessions_jti_idx" ON "customer_sessions" USING btree ("jti");--> statement-breakpoint
CREATE INDEX "customer_sessions_expires_at_idx" ON "customer_sessions" USING btree ("expires_at");--> statement-breakpoint
CREATE INDEX "customer_sessions_device_id_idx" ON "customer_sessions" USING btree ("device_id");