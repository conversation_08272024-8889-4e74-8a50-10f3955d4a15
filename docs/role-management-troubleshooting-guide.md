# 🚨 Role Management Troubleshooting Guide

> **Panduan <PERSON>kap <PERSON>ah Role Management System**

---

## 📋 Table of Contents

1. [Common Errors](#-common-errors)
2. [Authentication Issues](#-authentication-issues)
3. [Database Problems](#-database-problems)
4. [Frontend Issues](#-frontend-issues)
5. [Performance Problems](#-performance-problems)
6. [Development Environment](#-development-environment)

---

## ❌ Common Errors

### 1. RBAC_INTERNAL_ERROR

**Symptoms:**
```json
{
  "success": false,
  "error": "Internal server error",
  "code": "RBAC_INTERNAL_ERROR"
}
```

**Root Cause:**
Function signature mismatch antara middleware dan handler

**Solution:**
```typescript
// ❌ Wrong - untuk dynamic routes
export const PUT = withRBAC(
  async (request, context, { params }) => { ... }
)

// ✅ Correct - gunakan withRBACParams
export const PUT = withRBACParams(
  async (request, context, { params }) => { ... }
)
```

**Prevention:**
- <PERSON><PERSON><PERSON> gun<PERSON> `withRBACParams` untuk routes dengan `[id]`, `[slug]`, dll
- <PERSON><PERSON><PERSON> `withRBAC` hanya untuk static routes

### 2. RBAC_ACCESS_DENIED

**Symptoms:**
```json
{
  "success": false,
  "error": "Permission denied: roles.update",
  "code": "RBAC_ACCESS_DENIED"
}
```

**Root Cause:**
User tidak punya permission yang diperlukan

**Solution:**
```typescript
// Check user permissions
console.log("User permissions:", session?.user?.permissions);

// Ensure admin bypass working
if (userEmail === "<EMAIL>") {
  return { allowed: true, context };
}
```

**Prevention:**
- <NAME_EMAIL> untuk testing
- Assign proper permissions ke user roles
- Check RBAC configuration

### 3. Validation Failed

**Symptoms:**
```json
{
  "success": false,
  "error": "Validation failed",
  "details": [
    {
      "code": "too_small",
      "minimum": 1,
      "type": "string",
      "inclusive": true,
      "exact": false,
      "message": "Role name is required",
      "path": ["name"]
    }
  ]
}
```

**Root Cause:**
Input data tidak sesuai dengan Zod schema

**Solution:**
```typescript
// Check schema requirements
const createRoleSchema = z.object({
  name: z.string().min(1, "Name is required").max(100, "Name too long"),
  display_name: z.string().min(1, "Display name is required"),
  // ... other fields
});

// Ensure all required fields provided
const validData = {
  name: "custom_role",           // Required, lowercase with underscores
  display_name: "Custom Role",   // Required
  description: "Optional desc",  // Optional
  hierarchy_level: 50,          // Optional, default 50
  tenantId: 1                   // Optional for tenant-specific roles
};
```

---

## 🔐 Authentication Issues

### 1. Redirect to Login Page

**Symptoms:**
- API calls redirect to `/auth/signin`
- Browser shows login page instead of JSON response

**Root Cause:**
- User not authenticated
- Session expired
- Middleware configuration issue

**Solution:**
```bash
# 1. Login as admin
http://localhost:3002/auth/signin
Email: <EMAIL>
Password: password

# 2. Check session in browser DevTools
# Application > Cookies > next-auth.session-token

# 3. Test with session cookie
curl -X GET "http://localhost:3002/api/roles" \
  -H "Cookie: next-auth.session-token=YOUR_SESSION_TOKEN"
```

**Prevention:**
- Always login before testing
- Use test endpoints that bypass auth for development
- Check middleware public routes configuration

### 2. Session Token Issues

**Symptoms:**
- Login successful but API calls still fail
- Inconsistent authentication behavior

**Debugging:**
```typescript
// Add to API endpoint for debugging


//
// 5459 5459 2998 1248
// CVV

// 344
// Valid thru

// 07/27

const token = await getToken({ 
  req: request,
  secret: process.env.NEXTAUTH_SECRET 
});

console.log("🔍 Token Debug:", {
  hasToken: !!token,
  userId: token?.sub,
  email: token?.email,
  roles: token?.roles,
  permissions: token?.permissions
});
```

**Solution:**
```bash
# 1. Clear browser cookies
# 2. Restart development server
npm run dev

# 3. Login again
# 4. Check environment variables
echo $NEXTAUTH_SECRET
echo $NEXTAUTH_URL
```

---

## 🗄️ Database Problems

### 1. Role Not Found After Creation

**Symptoms:**
- CREATE returns success
- Role doesn't appear in list
- GET by ID returns 404

**Root Cause:**
- Transaction not committed
- Soft delete filter hiding role
- Database connection issue

**Debugging:**
```sql
-- Check if role exists in database
SELECT id, name, display_name, is_active, created_at 
FROM roles 
WHERE name LIKE '%test%' 
ORDER BY created_at DESC;

-- Check transaction isolation
SHOW TRANSACTION ISOLATION LEVEL;
```

**Solution:**
```typescript
// ✅ Use explicit transactions
static async create(data: CreateRoleData): Promise<Role> {
  return await db.transaction(async (tx) => {
    const result = await tx.insert(roles).values(roleData).returning();
    
    // Verify within transaction
    const verification = await tx
      .select()
      .from(roles)
      .where(eq(roles.id, result[0].id))
      .limit(1);
      
    if (verification.length === 0) {
      throw new Error("Role verification failed");
    }
    
    return result[0];
  });
}
```

### 2. Duplicate Key Errors

**Symptoms:**
```
ERROR: duplicate key value violates unique constraint "roles_name_tenant_unique"
```

**Root Cause:**
- Role name already exists for tenant
- Unique constraint violation

**Solution:**
```typescript
// Check for existing role before create
const existingRole = await db
  .select()
  .from(roles)
  .where(
    and(
      eq(roles.name, data.name),
      eq(roles.tenantId, data.tenantId || null),
      eq(roles.is_active, true)
    )
  )
  .limit(1);

if (existingRole.length > 0) {
  throw new Error(`Role ${data.name} already exists for this tenant`);
}
```

### 3. Connection Pool Exhausted

**Symptoms:**
- Intermittent database errors
- "Connection pool exhausted" messages
- Slow response times

**Solution:**
```typescript
// Check database configuration
// drizzle.config.ts
export default {
  schema: "./src/lib/db/schema.ts",
  out: "./drizzle",
  driver: "pg",
  dbCredentials: {
    connectionString: process.env.DATABASE_URL,
    // Add connection pool settings
    max: 20,
    idleTimeoutMillis: 30000,
    connectionTimeoutMillis: 2000,
  },
};
```

---

## 🎨 Frontend Issues

### 1. Data Not Displaying

**Symptoms:**
- Loading state never ends
- Empty list despite data in database
- Console errors about undefined properties

**Root Cause:**
- Incorrect data access pattern
- React Query configuration issue
- API response structure mismatch

**Solution:**
```typescript
// ✅ Correct data access
const {
  data: rolesData,
  isLoading,
  error,
} = useRoleSearch(undefined, undefined, undefined, 100, 0);

// ✅ Proper data extraction
const roles = rolesData?.roles || [];  // NOT rolesData?.data?.roles

// ✅ Handle loading and error states
if (isLoading) return <div>Loading...</div>;
if (error) return <div>Error: {error.message}</div>;
if (roles.length === 0) return <div>No roles found</div>;
```

### 2. Form Submission Errors

**Symptoms:**
- Form submits but nothing happens
- Validation errors not showing
- Success message but data not updated

**Debugging:**
```typescript
// Add debugging to form submission
const handleSubmit = async (data: RoleFormData) => {
  console.log("🔍 Form submission:", data);
  
  try {
    if (role) {
      console.log("🔄 Updating role:", role.id);
      await updateMutation.mutateAsync({ id: role.id, data });
    } else {
      console.log("➕ Creating role");
      await createMutation.mutateAsync(data);
    }
    
    console.log("✅ Mutation successful");
    onSuccess?.();
  } catch (error) {
    console.error("❌ Mutation failed:", error);
    setSubmitError(error.message);
  }
};
```

**Solution:**
```typescript
// ✅ Proper mutation handling
const createMutation = useCreateRole();
const updateMutation = useUpdateRole();

// ✅ Error state management
const [submitError, setSubmitError] = useState<string | null>(null);

// ✅ Success callback
const handleSuccess = async () => {
  setSuccessMessage("Operation successful!");
  await forceRefresh(); // Refresh data
  onSuccess?.(); // Close modal/form
};
```

### 3. Cache Not Updating

**Symptoms:**
- CRUD operations succeed but UI doesn't update
- Need to refresh page to see changes
- Stale data displayed

**Solution:**
```typescript
// ✅ Proper cache invalidation
const forceRefresh = async () => {
  // Remove cached data
  queryClient.removeQueries({ queryKey: roleKeys.all });
  
  // Invalidate and refetch
  queryClient.invalidateQueries({ queryKey: roleKeys.all });
  
  // Force refetch
  await refetch();
};

// ✅ Call after mutations
const handleCreateSuccess = async () => {
  await forceRefresh();
  setIsCreateDialogOpen(false);
};
```

---

## ⚡ Performance Problems

### 1. Slow API Responses

**Symptoms:**
- API calls take >2 seconds
- Database queries timing out
- High CPU usage

**Debugging:**
```typescript
// Add timing to API endpoints
const startTime = Date.now();

try {
  const result = await RoleService.searchRoles(...);
  const duration = Date.now() - startTime;
  
  console.log(`⏱️ Query took ${duration}ms`);
  
  return NextResponse.json({ success: true, data: result });
} catch (error) {
  const duration = Date.now() - startTime;
  console.error(`❌ Query failed after ${duration}ms:`, error);
}
```

**Solution:**
```sql
-- Add database indexes
CREATE INDEX idx_roles_tenant_active ON roles(tenant_id, is_active);
CREATE INDEX idx_roles_name ON roles(name);
CREATE INDEX idx_roles_hierarchy ON roles(hierarchy_level);

-- Analyze query performance
EXPLAIN ANALYZE SELECT * FROM roles WHERE tenant_id = 1 AND is_active = true;
```

### 2. Memory Leaks

**Symptoms:**
- Browser tab becomes slow over time
- High memory usage in DevTools
- React warnings about memory leaks

**Solution:**
```typescript
// ✅ Cleanup subscriptions
useEffect(() => {
  const subscription = someObservable.subscribe();
  
  return () => {
    subscription.unsubscribe();
  };
}, []);

// ✅ Cancel pending requests
useEffect(() => {
  const controller = new AbortController();
  
  fetch('/api/roles', { signal: controller.signal });
  
  return () => {
    controller.abort();
  };
}, []);
```

---

## 🛠️ Development Environment

### 1. Server Won't Start

**Symptoms:**
```bash
Error: Cannot find module '@/lib/db'
Port 3000 already in use
```

**Solution:**
```bash
# 1. Install dependencies
npm install

# 2. Check environment variables
cp .env.example .env.local
# Edit .env.local with correct values

# 3. Kill existing processes
lsof -ti:3000 | xargs kill -9

# 4. Clear Next.js cache
rm -rf .next
npm run dev
```

### 2. Database Connection Issues

**Symptoms:**
```bash
Error: connect ECONNREFUSED 127.0.0.1:5432
```

**Solution:**
```bash
# 1. Start PostgreSQL
brew services start postgresql
# or
sudo systemctl start postgresql

# 2. Check connection
psql -h 127.0.0.1 -p 5433 -U citizix_user -d saas_app

# 3. Run migrations
npm run db:generate
npm run db:migrate
```

### 3. TypeScript Errors

**Symptoms:**
```bash
Type 'unknown' is not assignable to type 'Role'
Property 'id' does not exist on type 'never'
```

**Solution:**
```bash
# 1. Regenerate types
npm run db:generate

# 2. Restart TypeScript server in VS Code
Cmd+Shift+P > "TypeScript: Restart TS Server"

# 3. Check imports
import { type Role } from "@/lib/db/schema";
```

---

## 🆘 Emergency Procedures

### System Down - Quick Recovery

1. **Check server status**
   ```bash
   curl http://localhost:3002/api/health
   ```

2. **Restart everything**
   ```bash
   # Kill all Node processes
   pkill -f node
   
   # Restart database
   brew services restart postgresql
   
   # Start fresh
   npm run dev
   ```

3. **Verify core functionality**
   ```bash
   curl -X POST "http://localhost:3002/api/test-crud-complete"
   ```

### Data Corruption Recovery

1. **Backup current state**
   ```bash
   pg_dump -h 127.0.0.1 -p 5433 -U citizix_user saas_app > backup_$(date +%Y%m%d_%H%M%S).sql
   ```

2. **Check data integrity**
   ```sql
   SELECT COUNT(*) FROM roles WHERE is_active = true;
   SELECT COUNT(*) FROM roles WHERE name IS NULL OR display_name IS NULL;
   ```

3. **Fix common issues**
   ```sql
   -- Fix null names
   UPDATE roles SET name = LOWER(REPLACE(display_name, ' ', '_')) WHERE name IS NULL;
   
   -- Fix inactive system roles
   UPDATE roles SET is_active = true WHERE is_system_role = true AND is_active = false;
   ```

---

*Troubleshooting Guide v1.0.0 - Last Updated: 2025-01-19*
