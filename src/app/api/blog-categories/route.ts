import { NextRequest, NextResponse } from "next/server";
import { BlogService } from "@/lib/services/blog.service";
import { createBlogCategorySchema, updateBlogCategorySchema } from "@/lib/validations/blog";
import { withRBAC } from "@/lib/middleware/rbac-middleware";
import { z } from "zod";

// GET /api/blog-categories - List blog categories with filters
export async function GET(request: NextRequest) {
  return withRBAC(
    async () => {
      try {
        const { searchParams } = new URL(request.url);
        
        const tenantId = searchParams.get("tenantId");
        const search = searchParams.get("search");
        const is_active = searchParams.get("is_active");

        if (!tenantId) {
          return NextResponse.json(
            { error: "Tenant ID is required" },
            { status: 400 }
          );
        }

        const filters = {
          ...(search && { search }),
          ...(is_active !== null && { is_active: is_active === "true" }),
        };

        const categories = await BlogService.getCategoriesByTenant(
          parseInt(tenantId),
          filters
        );

        return NextResponse.json({
          success: true,
          data: categories,
          count: categories.length,
        });
      } catch (error) {
        console.error("Error fetching blog categories:", error);
        return NextResponse.json(
          { 
            error: error instanceof Error ? error.message : "Failed to fetch blog categories",
            success: false 
          },
          { status: 500 }
        );
      }
    },
    { module: "blog", action: "read" }
  )(request);
}

// POST /api/blog-categories - Create new blog category
export async function POST(request: NextRequest) {
  return withRBAC(
    async () => {
      try {
        const body = await request.json();
        
        // Validate request body
        const validatedData = createBlogCategorySchema.parse(body);

        // Create blog category
        const category = await BlogService.createCategory(validatedData);

        return NextResponse.json({
          success: true,
          data: category,
          message: "Blog category created successfully",
        }, { status: 201 });
      } catch (error) {
        console.error("Error creating blog category:", error);
        
        if (error instanceof z.ZodError) {
          return NextResponse.json(
            { 
              error: "Validation failed",
              details: error.errors,
              success: false 
            },
            { status: 400 }
          );
        }

        return NextResponse.json(
          { 
            error: error instanceof Error ? error.message : "Failed to create blog category",
            success: false 
          },
          { status: 500 }
        );
      }
    },
    { module: "blog", action: "create" }
  )(request);
}

// PUT /api/blog-categories/bulk - Bulk operations
export async function PUT(request: NextRequest) {
  return withRBAC(
    async () => {
      try {
        const body = await request.json();
        const { operation, ids, data } = body;

        if (!operation || !ids || !Array.isArray(ids)) {
          return NextResponse.json(
            { error: "Invalid bulk operation request" },
            { status: 400 }
          );
        }

        let results = [];

        switch (operation) {
          case "delete":
            for (const id of ids) {
              await BlogService.deleteCategory(id);
            }
            results = ids.map((id: string) => ({ id, status: "deleted" }));
            break;

          case "activate":
            for (const id of ids) {
              const updated = await BlogService.updateCategory(id, { is_active: true });
              results.push(updated);
            }
            break;

          case "deactivate":
            for (const id of ids) {
              const updated = await BlogService.updateCategory(id, { is_active: false });
              results.push(updated);
            }
            break;

          case "update":
            if (!data) {
              return NextResponse.json(
                { error: "Update data is required for bulk update" },
                { status: 400 }
              );
            }
            
            const validatedUpdateData = updateBlogCategorySchema.parse(data);
            
            for (const id of ids) {
              const updated = await BlogService.updateCategory(id, validatedUpdateData);
              results.push(updated);
            }
            break;

          default:
            return NextResponse.json(
              { error: `Unknown operation: ${operation}` },
              { status: 400 }
            );
        }

        return NextResponse.json({
          success: true,
          data: results,
          message: `Bulk ${operation} completed successfully`,
        });
      } catch (error) {
        console.error("Error in bulk operation:", error);
        
        if (error instanceof z.ZodError) {
          return NextResponse.json(
            { 
              error: "Validation failed",
              details: error.errors,
              success: false 
            },
            { status: 400 }
          );
        }

        return NextResponse.json(
          { 
            error: error instanceof Error ? error.message : "Failed to perform bulk operation",
            success: false 
          },
          { status: 500 }
        );
      }
    },
    { module: "blog", action: "update" }
  )(request);
}
