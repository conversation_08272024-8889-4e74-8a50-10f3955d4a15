import { useMutation, useQuery, useQueryClient, UseQueryOptions, UseMutationOptions } from '@tanstack/react-query';
import { type ClassBooking } from '@/lib/db/schema';
import { createEntityHooks, type BaseApiInterface } from '@/lib/core/base-query-hooks';

// Types (imported from service but defined here to avoid server imports)
export interface CreateClassBookingData {
  tenantId: number;
  scheduleId: string;
  classId: string;
  customerId: string;
  bookedByUserId?: string;
  bookedByCustomerId?: string;
  status?: string;
  isWaitlist?: boolean;
  waitlistPosition?: number;
  paymentStatus?: string;
  paymentMethod?: string;
  creditsUsed?: number;
  notes?: string;
}

export interface UpdateClassBookingData {
  scheduleId?: string;
  classId?: string;
  customerId?: string;
  bookedByUserId?: string;
  bookedByCustomerId?: string;
  status?: string;
  isWaitlist?: boolean;
  waitlistPosition?: number;
  paymentStatus?: string;
  paymentMethod?: string;
  creditsUsed?: number;
  checkInTime?: Date;
  cancelTime?: Date;
  cancellationReason?: string;
  notes?: string;
}

export interface ClassBookingFilters {
  tenantId?: number;
  scheduleId?: string;
  classId?: string;
  customerId?: string;
  status?: string;
  isWaitlist?: boolean;
  paymentStatus?: string;
  search?: string;
  dateFrom?: string;
  dateTo?: string;
}

export interface ClassBookingWithRelations extends ClassBooking {
  className?: string;
  customerName?: string;
  customerEmail?: string;
  bookedByUserName?: string;
  scheduleStartTime?: Date;
  scheduleEndTime?: Date;
  locationName?: string;
}

export interface ClassBookingSearchResult {
  bookings: ClassBookingWithRelations[];
  total: number;
  hasMore: boolean;
}

export interface ClassBookingStats {
  total: number;
  booked: number;
  checkedIn: number;
  cancelled: number;
  waitlisted: number;
  noShow: number;
  completed: number;
  byStatus: Record<string, number>;
  byPaymentStatus: Record<string, number>;
  byTenant: Record<number, { total: number; booked: number; checkedIn: number; cancelled: number; }>;
}

// Define QueryOptions locally since it's not exported from base-query-hooks
interface QueryOptions {
  tenantId?: number;
  limit?: number;
  offset?: number;
  filters?: Record<string, any>;
}

/**
 * Class Booking API Interface
 * 
 * Ini adalah adapter yang menghubungkan ClassBookingService dengan createEntityHooks factory.
 * Pattern ini sama seperti yang digunakan di pricing-groups, customers, facilities, dll.
 */
const classBookingApi: BaseApiInterface<ClassBookingWithRelations, CreateClassBookingData, UpdateClassBookingData> = {
  async getAll(options?: QueryOptions): Promise<ClassBookingWithRelations[]> {
    const params = new URLSearchParams();
    if (options?.tenantId) params.append('tenantId', options.tenantId.toString());
    if (options?.limit) params.append('limit', options.limit.toString());
    if (options?.offset) params.append('offset', options.offset.toString());

    const response = await fetch(`/api/class-bookings?${params.toString()}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch class bookings: ${response.statusText}`);
    }
    const result = await response.json();
    return result.data || [];
  },

  async getById(id: string): Promise<ClassBookingWithRelations> {
    const response = await fetch(`/api/class-bookings/${id}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch class booking: ${response.statusText}`);
    }
    const result = await response.json();
    return result.data;
  },

  async getByTenant(tenantId: number): Promise<ClassBookingWithRelations[]> {
    const params = new URLSearchParams({ tenantId: tenantId.toString() });
    const response = await fetch(`/api/class-bookings?${params.toString()}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch class bookings: ${response.statusText}`);
    }
    const result = await response.json();
    return result.data || [];
  },

  async create(data: CreateClassBookingData): Promise<ClassBookingWithRelations> {
    const response = await fetch('/api/class-bookings', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      throw new Error(`Failed to create class booking: ${response.statusText}`);
    }
    const result = await response.json();
    return result.data;
  },

  async update({ id, data }: { id: string; data: UpdateClassBookingData }): Promise<ClassBookingWithRelations> {
    const response = await fetch(`/api/class-bookings/${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      throw new Error(`Failed to update class booking: ${response.statusText}`);
    }
    const result = await response.json();
    return result.data;
  },

  async delete(id: string): Promise<ClassBookingWithRelations> {
    const response = await fetch(`/api/class-bookings/${id}`, {
      method: 'DELETE',
    });
    if (!response.ok) {
      throw new Error(`Failed to delete class booking: ${response.statusText}`);
    }
    const result = await response.json();
    return result.data;
  },

  async bulkOperation(): Promise<ClassBookingWithRelations[]> {
    // Implement bulk operations sesuai kebutuhan
    throw new Error('Bulk operations not implemented yet');
  },

  async getStats(tenantId?: number): Promise<ClassBookingStats> {
    const params = new URLSearchParams();
    if (tenantId) params.append('tenantId', tenantId.toString());

    const response = await fetch(`/api/class-bookings/stats?${params.toString()}`);
    if (!response.ok) {
      throw new Error('Failed to fetch class booking statistics');
    }
    const result = await response.json();
    return result.data;
  },

  async search(query: string, options?: QueryOptions): Promise<ClassBookingWithRelations[]> {
    const params = new URLSearchParams();
    params.append('search', query);
    if (options?.tenantId) params.append('tenantId', options.tenantId.toString());
    if (options?.limit) params.append('limit', options.limit.toString());
    if (options?.offset) params.append('offset', options.offset.toString());

    const response = await fetch(`/api/class-bookings?${params.toString()}`);
    if (!response.ok) {
      throw new Error(`Failed to search class bookings: ${response.statusText}`);
    }
    const result = await response.json();
    return result.data || [];
  },
};

// Create base hooks using the factory
const baseHooks = createEntityHooks('class-bookings', classBookingApi);

// Export base hooks with class booking-specific names
export const useClassBookings = baseHooks.useEntities;
export const useClassBooking = baseHooks.useEntity;
export const useClassBookingsByTenant = baseHooks.useEntitiesByTenant;
export const useClassBookingStats = baseHooks.useEntityStats;
export const useClassBookingSearch = baseHooks.useEntitySearch;
export const useCreateClassBooking = baseHooks.useCreateEntity;
export const useUpdateClassBooking = baseHooks.useUpdateEntity;
export const useDeleteClassBooking = baseHooks.useDeleteEntity;
export const useBulkClassBookingOperation = baseHooks.useBulkOperation;

/**
 * Custom hooks untuk class booking-specific functionality
 * 
 * Ini adalah hooks tambahan yang spesifik untuk class bookings,
 * di luar yang disediakan oleh factory pattern.
 */

// Hook untuk search bookings dengan pagination (advanced search)
export function useClassBookingSearchAdvanced(
  filters: ClassBookingFilters,
  options?: {
    limit?: number;
    offset?: number;
    queryOptions?: Omit<UseQueryOptions<ClassBookingSearchResult>, 'queryKey' | 'queryFn'>;
  }
) {
  const { limit = 20, offset = 0, queryOptions } = options || {};

  return useQuery({
    queryKey: ['class-bookings', 'search-advanced', { filters, limit, offset }],
    queryFn: async () => {
      const params = new URLSearchParams();
      if (filters.tenantId) params.append('tenantId', filters.tenantId.toString());
      if (filters.scheduleId) params.append('scheduleId', filters.scheduleId);
      if (filters.classId) params.append('classId', filters.classId);
      if (filters.customerId) params.append('customerId', filters.customerId);
      if (filters.status) params.append('status', filters.status);
      if (filters.isWaitlist !== undefined) params.append('isWaitlist', filters.isWaitlist.toString());
      if (filters.paymentStatus) params.append('paymentStatus', filters.paymentStatus);
      if (filters.search) params.append('search', filters.search);
      if (filters.dateFrom) params.append('dateFrom', filters.dateFrom);
      if (filters.dateTo) params.append('dateTo', filters.dateTo);
      params.append('limit', limit.toString());
      params.append('offset', offset.toString());

      const response = await fetch(`/api/class-bookings?${params.toString()}`);
      if (!response.ok) {
        throw new Error(`Failed to search class bookings: ${response.statusText}`);
      }
      const result = await response.json();
      return {
        bookings: result.data || [],
        total: result.meta?.total || 0,
        hasMore: result.meta?.hasMore || false,
      };
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: false,
    ...queryOptions,
  });
}

// Hook untuk get bookings by schedule ID
export function useClassBookingsBySchedule(
  scheduleId: string,
  tenantId: number,
  options?: Omit<UseQueryOptions<ClassBookingWithRelations[]>, 'queryKey' | 'queryFn'>
) {
  return useQuery({
    queryKey: ['class-bookings', 'by-schedule', scheduleId, tenantId],
    queryFn: async () => {
      const params = new URLSearchParams({
        scheduleId,
        tenantId: tenantId.toString(),
      });
      const response = await fetch(`/api/class-bookings?${params.toString()}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch bookings by schedule: ${response.statusText}`);
      }
      const result = await response.json();
      return result.data || [];
    },
    enabled: !!scheduleId && !!tenantId,
    staleTime: 1 * 60 * 1000, // 1 minute (lebih fresh untuk real-time booking)
    ...options,
  });
}

// Hook untuk get bookings by customer ID
export function useClassBookingsByCustomer(
  customerId: string,
  tenantId: number,
  options?: Omit<UseQueryOptions<ClassBookingWithRelations[]>, 'queryKey' | 'queryFn'>
) {
  return useQuery({
    queryKey: ['class-bookings', 'by-customer', customerId, tenantId],
    queryFn: async () => {
      const params = new URLSearchParams({
        customerId,
        tenantId: tenantId.toString(),
      });
      const response = await fetch(`/api/class-bookings?${params.toString()}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch bookings by customer: ${response.statusText}`);
      }
      const result = await response.json();
      return result.data || [];
    },
    enabled: !!customerId && !!tenantId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    ...options,
  });
}

/**
 * Mutation hooks untuk class booking-specific actions
 */

// Hook untuk check-in customer
export function useCheckInBooking(
  options?: UseMutationOptions<ClassBooking | null, Error, { id: string; tenantId: number }>
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, tenantId }) => {
      const response = await fetch('/api/class-bookings/actions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'check-in',
          id,
          tenantId,
        }),
      });
      if (!response.ok) {
        throw new Error(`Failed to check in booking: ${response.statusText}`);
      }
      const result = await response.json();
      return result.data;
    },
    onSuccess: (updatedBooking, { id, tenantId }) => {
      if (updatedBooking) {
        // Update cache dengan booking yang sudah di-check-in
        queryClient.setQueryData(['class-bookings', 'detail', id], updatedBooking);

        // Invalidate related queries
        queryClient.invalidateQueries({ queryKey: ['class-bookings', 'list'] });
        queryClient.invalidateQueries({ queryKey: ['class-bookings', 'tenant', tenantId] });
        queryClient.invalidateQueries({ queryKey: ['class-bookings', 'stats'] });

        // Invalidate schedule-specific queries jika ada
        if (updatedBooking.schedule_id) {
          queryClient.invalidateQueries({
            queryKey: ['class-bookings', 'by-schedule', updatedBooking.schedule_id]
          });
        }
      }
    },
    ...options,
  });
}

// Hook untuk cancel booking
export function useCancelBooking(
  options?: UseMutationOptions<ClassBooking | null, Error, { id: string; tenantId: number; reason?: string }>
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, tenantId, reason }) => {
      const response = await fetch('/api/class-bookings/actions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'cancel',
          id,
          tenantId,
          reason,
        }),
      });
      if (!response.ok) {
        throw new Error(`Failed to cancel booking: ${response.statusText}`);
      }
      const result = await response.json();
      return result.data;
    },
    onSuccess: (updatedBooking, { id, tenantId }) => {
      if (updatedBooking) {
        // Update cache dengan booking yang sudah di-cancel
        queryClient.setQueryData(['class-bookings', 'detail', id], updatedBooking);

        // Invalidate related queries
        queryClient.invalidateQueries({ queryKey: ['class-bookings', 'list'] });
        queryClient.invalidateQueries({ queryKey: ['class-bookings', 'tenant', tenantId] });
        queryClient.invalidateQueries({ queryKey: ['class-bookings', 'stats'] });

        // Invalidate schedule-specific queries jika ada
        if (updatedBooking.schedule_id) {
          queryClient.invalidateQueries({
            queryKey: ['class-bookings', 'by-schedule', updatedBooking.schedule_id]
          });
        }
      }
    },
    ...options,
  });
}

// Hook untuk bulk update status
export function useBulkUpdateBookingStatus(
  options?: UseMutationOptions<ClassBooking[], Error, { ids: string[]; status: string; tenantId: number }>
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ ids, status, tenantId }) => {
      const response = await fetch('/api/class-bookings/actions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'bulk-update-status',
          ids,
          status,
          tenantId,
        }),
      });
      if (!response.ok) {
        throw new Error(`Failed to bulk update booking status: ${response.statusText}`);
      }
      const result = await response.json();
      return result.data;
    },
    onSuccess: () => {
      // Invalidate all related queries karena bulk operation
      queryClient.invalidateQueries({ queryKey: ['class-bookings'] });
    },
    ...options,
  });
}

// Hook untuk bulk delete bookings
export function useBulkDeleteBookings(
  options?: UseMutationOptions<ClassBooking[], Error, { ids: string[]; tenantId: number }>
) {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ ids, tenantId }) => {
      const response = await fetch('/api/class-bookings', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ ids, tenantId }),
      });
      if (!response.ok) {
        throw new Error(`Failed to bulk delete bookings: ${response.statusText}`);
      }
      const result = await response.json();
      return result.data;
    },
    onSuccess: () => {
      // Invalidate all related queries karena bulk operation
      queryClient.invalidateQueries({ queryKey: ['class-bookings'] });
    },
    ...options,
  });
}

// Export query keys untuk external use
export const classBookingQueryKeys = baseHooks.queryKeys;
