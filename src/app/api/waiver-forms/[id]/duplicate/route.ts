import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { z } from "zod";
import { WaiverFormService } from "@/lib/services/waiver-forms.service";

const duplicateSchema = z.object({
  name: z.string().min(1).max(255).optional(),
});

const waiverFormService = new WaiverFormService();

// POST /api/waiver-forms/[id]/duplicate - Duplicate waiver form
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id: waiverFormId } = await params;
    if (!waiverFormId) {
      return NextResponse.json(
        { error: "Waiver form ID is required" },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { name } = duplicateSchema.parse(body);

    const duplicatedWaiverForm = await waiverFormService.duplicate(waiverFormId, name);

    return NextResponse.json(
      {
        message: "Waiver form duplicated successfully",
        data: duplicatedWaiverForm
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error duplicating waiver form:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid data", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to duplicate waiver form" },
      { status: 500 }
    );
  }
}
