// Script to test package pricing bulk operations
// Run with: npx tsx scripts/test-package-pricing-bulk.ts

import { db } from "../src/lib/db";
import { packages, pricing_groups } from "../src/lib/db/schema";
import { eq } from "drizzle-orm";

async function testBulkOperations() {
  try {
    console.log("🧪 Testing Package Pricing Bulk Operations...");

    // Get existing packages and pricing groups for tenant 1
    const existingPackages = await db
      .select()
      .from(packages)
      .where(eq(packages.tenantId, 1))
      .limit(2);

    const existingPricingGroups = await db
      .select()
      .from(pricing_groups)
      .where(eq(pricing_groups.tenantId, 1))
      .limit(2);

    console.log(`📦 Found ${existingPackages.length} packages`);
    console.log(`💰 Found ${existingPricingGroups.length} pricing groups`);

    if (existingPackages.length === 0 || existingPricingGroups.length === 0) {
      console.log("❌ Need at least 1 package and 1 pricing group to test");
      return;
    }

    // Test 1: Bulk Create
    console.log("\n🔧 Test 1: Bulk Create Package Pricing");
    const createData = [
      {
        packageId: existingPackages[0].id,
        pricingGroupId: existingPricingGroups[0].id,
        price: 75000,
        creditAmount: 15,
        currency: "IDR"
      }
    ];

    if (existingPackages.length > 1 && existingPricingGroups.length > 1) {
      createData.push({
        packageId: existingPackages[1].id,
        pricingGroupId: existingPricingGroups[1].id,
        price: 125000,
        creditAmount: 25,
        currency: "IDR"
      });
    }

    const createResponse = await fetch('http://localhost:3000/api/package-pricing/bulk', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        action: 'create',
        data: createData
      }),
    });

    if (createResponse.ok) {
      const createResult = await createResponse.json();
      console.log(`✅ Created ${createResult.data.length} package pricing records`);
      
      // Test 2: Bulk Delete
      console.log("\n🔧 Test 2: Bulk Delete Package Pricing");
      const idsToDelete = createResult.data.map((item: any) => item.id);
      
      const deleteResponse = await fetch('http://localhost:3000/api/package-pricing/bulk', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'delete',
          ids: idsToDelete
        }),
      });

      if (deleteResponse.ok) {
        const deleteResult = await deleteResponse.json();
        console.log(`✅ Deleted ${deleteResult.data.length} package pricing records`);
      } else {
        const deleteError = await deleteResponse.json();
        console.log(`❌ Delete failed:`, deleteError.error);
      }
    } else {
      const createError = await createResponse.json();
      console.log(`❌ Create failed:`, createError.error);
    }

    // Test 3: Get Templates
    console.log("\n🔧 Test 3: Get Bulk Operation Templates");
    const templateResponse = await fetch('http://localhost:3000/api/package-pricing/bulk?action=template');
    
    if (templateResponse.ok) {
      const templateResult = await templateResponse.json();
      console.log("✅ Templates retrieved successfully");
      console.log("📋 Available actions:", Object.keys(templateResult.data));
    } else {
      console.log("❌ Failed to get templates");
    }

    console.log("\n🎉 Bulk operations testing completed!");

  } catch (error) {
    console.error("❌ Error testing bulk operations:", error);
    throw error;
  }
}

// Run the test
testBulkOperations()
  .then(() => {
    console.log("✅ Bulk operations test finished");
    process.exit(0);
  })
  .catch((error) => {
    console.error("💥 Bulk operations test failed:", error);
    process.exit(1);
  });
