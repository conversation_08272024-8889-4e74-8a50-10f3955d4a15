"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Package,
  Edit,
  Trash2,
  Plus,
  Search,
  AlertCircle,
  Copy,
  MoreHorizontal,
  FileText,
  Building,
  Calendar
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { PackageCategory } from "@/lib/db/schema";
import { 
  usePackageCategories, 
  useDeletePackageCategory, 
  useBulkPackageCategoryOperation,
  useDuplicatePackageCategory
} from "@/lib/hooks/queries/use-package-category-queries";
import { DeleteConfirmationDialog } from "@/components/ui/delete-confirmation-dialog";
import { DeleteSuccessToast } from "@/components/ui/delete-success-toast";

interface PackageCategoryListProps {
  onEdit?: (packageCategory: PackageCategory) => void;
  onAdd?: () => void;
  showActions?: boolean;
  tenantId?: number;
}

export function PackageCategoryList({
  onEdit,
  onAdd,
  showActions = true,
  tenantId,
}: PackageCategoryListProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [deleteError, setDeleteError] = useState<string | null>(null);
  const [deleteConfirmation, setDeleteConfirmation] = useState<{
    isOpen: boolean;
    packageCategory: PackageCategory | null;
  }>({ isOpen: false, packageCategory: null });
  const [showDeleteSuccess, setShowDeleteSuccess] = useState(false);
  const [deletedCategoryName, setDeletedCategoryName] = useState("");

  const { data: packageCategories = [], isLoading, error } = usePackageCategories({
    tenantId: tenantId || 1,
    filters: searchQuery ? { search: searchQuery } : undefined,
  });
  const deletePackageCategoryMutation = useDeletePackageCategory();
  const bulkOperationMutation = useBulkPackageCategoryOperation();
  const duplicateMutation = useDuplicatePackageCategory();

  const handleDeleteClick = (packageCategory: PackageCategory) => {
    setDeleteConfirmation({ isOpen: true, packageCategory });
  };

  const handleDeleteConfirm = async () => {
    if (!deleteConfirmation.packageCategory) return;

    try {
      await deletePackageCategoryMutation.mutateAsync(deleteConfirmation.packageCategory.id);
      setDeletedCategoryName(deleteConfirmation.packageCategory.name || "Package Category");
      setShowDeleteSuccess(true);
      setDeleteConfirmation({ isOpen: false, packageCategory: null });
    } catch (error) {
      setDeleteError(error instanceof Error ? error.message : "Failed to delete package category");
    }
  };

  const handleDuplicate = async (packageCategory: PackageCategory) => {
    try {
      await duplicateMutation.mutateAsync({ 
        id: packageCategory.id, 
        name: `${packageCategory.name} (Copy)` 
      });
    } catch (error) {
      setDeleteError(error instanceof Error ? error.message : "Failed to duplicate package category");
    }
  };

  const handleSelectItem = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedItems([...selectedItems, id]);
    } else {
      setSelectedItems(selectedItems.filter(item => item !== id));
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedItems(packageCategories.map(cat => cat.id));
    } else {
      setSelectedItems([]);
    }
  };

  const handleBulkOperation = async (action: 'delete') => {
    if (selectedItems.length === 0) return;

    try {
      await bulkOperationMutation.mutateAsync({ ids: selectedItems, action });
      setSelectedItems([]);
    } catch (error) {
      setDeleteError(error instanceof Error ? error.message : `Failed to ${action} package categories`);
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Error loading package categories: {error.message}
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Package Categories
            </CardTitle>
            <CardDescription>
              Manage package categories and organize your offerings
            </CardDescription>
          </div>
          {showActions && onAdd && (
            <Button onClick={onAdd}>
              <Plus className="h-4 w-4 mr-2" />
              Add Category
            </Button>
          )}
        </div>

        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search package categories..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Bulk Actions */}
        {selectedItems.length > 0 && (
          <div className="flex items-center gap-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <span className="text-sm text-blue-700 dark:text-blue-300">
              {selectedItems.length} selected
            </span>
            <Button
              variant="destructive"
              size="sm"
              onClick={() => handleBulkOperation('delete')}
            >
              <Trash2 className="h-4 w-4 mr-1" />
              Delete
            </Button>
          </div>
        )}
      </CardHeader>

      <CardContent>
        {deleteError && (
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{deleteError}</AlertDescription>
          </Alert>
        )}

        {/* Package Categories List */}
        {packageCategories.length === 0 ? (
          <div className="text-center py-8">
            <Package className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              {searchQuery ? "No categories found" : "No package categories yet"}
            </h3>
            <p className="text-gray-500 dark:text-gray-400 mb-4">
              {searchQuery
                ? "Try adjusting your search terms."
                : "Get started by creating your first package category."
              }
            </p>
            {showActions && onAdd && !searchQuery && (
              <Button onClick={onAdd}>
                <Plus className="h-4 w-4 mr-2" />
                Add Package Category
              </Button>
            )}
          </div>
        ) : (
          <div className="space-y-2">
            {/* Select All */}
            <div className="flex items-center gap-2 p-2 border-b">
              <Checkbox
                checked={selectedItems.length === packageCategories.length}
                onCheckedChange={handleSelectAll}
              />
              <span className="text-sm text-gray-600">Select All</span>
            </div>

            {packageCategories.map((packageCategory) => (
              <div
                key={packageCategory.id}
                className="border rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
              >
                <div className="flex items-center gap-3">
                  {/* Checkbox */}
                  <Checkbox
                    checked={selectedItems.includes(packageCategory.id)}
                    onCheckedChange={(checked) => handleSelectItem(packageCategory.id, checked as boolean)}
                  />

                  {/* Content */}
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <Package className="h-4 w-4 text-gray-500" />
                      <h4 className="font-medium text-gray-900 dark:text-gray-100">
                        {packageCategory.name}
                      </h4>
                    </div>
                    
                    {packageCategory.description && (
                      <div className="flex items-center gap-1 text-sm text-gray-600 dark:text-gray-400 mb-2">
                        <FileText className="h-3 w-3" />
                        {packageCategory.description}
                      </div>
                    )}

                    <div className="flex items-center gap-4 text-xs text-gray-500">
                      <div className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        Created {new Date(packageCategory.createdAt).toLocaleDateString()}
                      </div>
                      <div className="flex items-center gap-1">
                        <Building className="h-3 w-3" />
                        Tenant {packageCategory.tenantId}
                      </div>
                    </div>
                  </div>

                  {/* Actions */}
                  {showActions && (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        {onEdit && (
                          <DropdownMenuItem onClick={() => onEdit(packageCategory)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuItem onClick={() => handleDuplicate(packageCategory)}>
                          <Copy className="h-4 w-4 mr-2" />
                          Duplicate
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => handleDeleteClick(packageCategory)}
                          className="text-red-600"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationDialog
        isOpen={deleteConfirmation.isOpen}
        onClose={() => setDeleteConfirmation({ isOpen: false, packageCategory: null })}
        onConfirm={handleDeleteConfirm}
        title="Delete Package Category"
        description={`Are you sure you want to delete "${deleteConfirmation.packageCategory?.name}"? This action cannot be undone.`}
        isLoading={deletePackageCategoryMutation.isPending}
      />

      {/* Delete Success Toast */}
      <DeleteSuccessToast
        isOpen={showDeleteSuccess}
        onClose={() => setShowDeleteSuccess(false)}
        entityName="Package Category"
        deletedName={deletedCategoryName}
      />
    </Card>
  );
}
