"use client";

import { useSession } from "next-auth/react";
import { useMemo } from "react";
import { useQuery } from "@tanstack/react-query";

/**
 * RBAC Hook - menggunakan API route untuk load RBAC data
 */
export function useRBAC() {
  const { data: session, status } = useSession();

  // Load RBAC data from API route
  const { data: rbacApiData, isLoading: rbacLoading, error: rbacError } = useQuery({
    queryKey: ["rbac", session?.user?.id],
    queryFn: async () => {
      const response = await fetch("/api/auth/rbac");
      if (!response.ok) {
        throw new Error("Failed to load RBAC data");
      }
      return response.json();
    },
    enabled: !!session?.user?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 3,
  });

  const rbacData = useMemo(() => {
    if (!session?.user) {
      return {
        roles: [],
        permissions: [],
        accessibleLocations: [],
        isLoading: status === "loading",
        error: null,
      };
    }

    if (rbacLoading || !rbacApiData) {
      return {
        roles: [],
        permissions: [],
        accessibleLocations: [],
        isLoading: true,
        error: rbacError,
      };
    }

    return {
      roles: rbacApiData.roles || [],
      permissions: rbacApiData.permissions || [],
      accessibleLocations: rbacApiData.accessibleLocations || [],
      isLoading: false,
      error: rbacError,
    };
  }, [session, status, rbacApiData, rbacLoading, rbacError]);

  // Helper functions
  const hasRole = (role: string): boolean => {
    return rbacData.roles.includes(role);
  };

  const hasPermission = (permission: string): boolean => {
    return rbacData.permissions.includes(permission);
  };

  const hasAnyRole = (roles: string[]): boolean => {
    return roles.some(role => rbacData.roles.includes(role));
  };

  const hasAnyPermission = (permissions: string[]): boolean => {
    return permissions.some(permission => rbacData.permissions.includes(permission));
  };

  const hasAllRoles = (roles: string[]): boolean => {
    return roles.every(role => rbacData.roles.includes(role));
  };

  const hasAllPermissions = (permissions: string[]): boolean => {
    return permissions.every(permission => rbacData.permissions.includes(permission));
  };

  const canAccessLocation = (locationId: string): boolean => {
    // Super admin can access all locations
    if (rbacData.roles.includes("super_admin")) return true;

    // Check if user has access to specific location
    return rbacData.accessibleLocations.includes(locationId);
  };

  const isSuperAdmin = (): boolean => {
    return rbacData.roles.includes("super_admin");
  };

  const isTenantAdmin = (): boolean => {
    return rbacData.roles.includes("tenant_admin");
  };

  const isInstructor = (): boolean => {
    return rbacData.roles.includes("instructor");
  };

  const isCustomer = (): boolean => {
    return rbacData.roles.includes("customer");
  };

  return {
    ...rbacData,
    hasRole,
    hasPermission,
    hasAnyRole,
    hasAnyPermission,
    hasAllRoles,
    hasAllPermissions,
    canAccessLocation,
    isSuperAdmin,
    isTenantAdmin,
    isInstructor,
    isCustomer,
  };
}

/**
 * Hook untuk check specific permission
 *
 * Ini useful untuk conditional rendering yang simple.
 */
export function usePermission(permission: string) {
  const { hasPermission, isLoading } = useRBAC();

  return {
    hasPermission: hasPermission(permission),
    isLoading,
  };
}

/**
 * Hook untuk check specific role
 * 
 * Ini useful untuk role-based conditional rendering.
 */
export function useRole(roleName: string) {
  const { hasRole, isLoading } = useRBAC();
  
  return {
    hasRole: hasRole(roleName),
    isLoading,
  };
}

/**
 * Hook untuk location access check
 * 
 * Ini useful untuk location-based features.
 */
export function useLocationAccess(locationId?: string) {
  const { canAccessLocation, getAccessibleLocations, hasLocationRestrictions, isLoading } = useRBAC();
  
  return {
    canAccess: locationId ? canAccessLocation(locationId) : true,
    accessibleLocations: getAccessibleLocations(),
    hasRestrictions: hasLocationRestrictions(),
    isLoading,
  };
}
