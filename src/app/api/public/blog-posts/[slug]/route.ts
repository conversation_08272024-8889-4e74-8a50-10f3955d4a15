import { NextRequest, NextResponse } from "next/server";
import { BlogService } from "@/lib/services/blog.service";

interface RouteParams {
  params: Promise<{ slug: string }>;
}

// GET /api/public/blog-posts/[slug] - Public endpoint for single blog post by slug
export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const { slug } = await params;
    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get("tenantId");

    if (!slug) {
      return NextResponse.json(
        { error: "Blog post slug is required" },
        { status: 400 }
      );
    }

    if (!tenantId) {
      return NextResponse.json(
        { error: "Tenant ID is required" },
        { status: 400 }
      );
    }

    const blogPost = await BlogService.getBySlug(parseInt(tenantId), slug);

    if (!blogPost) {
      return NextResponse.json(
        { error: "Blog post not found" },
        { status: 404 }
      );
    }

    // Only return published posts for public API
    if (blogPost.status !== "published") {
      return NextResponse.json(
        { error: "Blog post not found" },
        { status: 404 }
      );
    }

    // Increment view count
    await BlogService.incrementViewCount(blogPost.id);

    return NextResponse.json({
      success: true,
      data: blogPost,
    });
  } catch (error) {
    console.error("Error fetching public blog post:", error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : "Failed to fetch blog post",
        success: false 
      },
      { status: 500 }
    );
  }
}
