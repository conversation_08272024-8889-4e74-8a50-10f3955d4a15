import { NextRequest } from "next/server";
import { z } from "zod";
import { 
  withCustomerAuth,
  createAuthenticatedResponse, 
  createErrorResponse,
  CustomerAuthContext 
} from "@/lib/middleware/customer-auth.middleware";
import { customerAuthService } from "@/lib/services/customer-auth.service";
import { rateLimitConfig } from "@/lib/security/rate-limit.config";

/**
 * Customer Logout
 * 
 * POST /api/auth/customer/logout
 * 
 * Revokes customer session and invalidates tokens
 */

const logoutSchema = z.object({
  logoutAll: z.boolean().default(false), // Logout from all devices
  reason: z.string().default("logout"),
});

async function logoutHandler(
  request: NextRequest,
  context: CustomerAuthContext
) {
  try {
    // Parse request body
    const body = await request.json().catch(() => ({}));
    const validatedData = logoutSchema.parse(body);

    // Logout from current session
    const logoutSuccess = await customerAuthService.logout(
      context.sessionId,
      context.customer.id,
      validatedData.reason
    );

    if (!logoutSuccess) {
      return createErrorResponse(
        "Failed to logout",
        "LOGOUT_ERROR",
        500
      );
    }

    // TODO: If logoutAll is true, revoke all sessions for this customer
    // This would require additional implementation in the JWT service

    const responseData = {
      success: true,
      message: "Logged out successfully",
      loggedOutAll: validatedData.logoutAll,
    };

    return createAuthenticatedResponse(responseData, 200);

  } catch (error) {
    console.error("Logout error:", error);

    if (error instanceof z.ZodError) {
      return createErrorResponse(
        "Invalid request data",
        "INVALID_INPUT",
        400
      );
    }

    return createErrorResponse(
      "Logout failed",
      "LOGOUT_ERROR",
      500
    );
  }
}

export const POST = withCustomerAuth(logoutHandler, {
  requireAuth: true,
  rateLimit: rateLimitConfig.auth.logout,
});
