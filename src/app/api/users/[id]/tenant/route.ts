import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { UserService } from "@/lib/services/user.service";
import { z } from "zod";

const assignTenantSchema = z.object({
  tenantId: z.number().int().positive(),
});

// PUT /api/users/[id]/tenant - Assign user to tenant
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Only admin or organization owner can assign users to tenants
    if (session.user.role !== "admin") {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const userId = params.id;
    const body = await request.json();
    const { tenantId } = assignTenantSchema.parse(body);

    const user = await UserService.assignUserToTenant(userId, tenantId);

    return NextResponse.json({ user });
  } catch (error) {
    console.error("Error assigning user to tenant:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to assign user to tenant" },
      { status: 500 }
    );
  }
}

// DELETE /api/users/[id]/tenant - Remove user from tenant
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Only admin or organization owner can remove users from tenants
    if (session.user.role !== "admin") {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const userId = params.id;
    const user = await UserService.removeUserFromTenant(userId);

    return NextResponse.json({ user });
  } catch (error) {
    console.error("Error removing user from tenant:", error);
    return NextResponse.json(
      { error: "Failed to remove user from tenant" },
      { status: 500 }
    );
  }
}

// GET /api/users/[id]/tenant - Get user's tenant context
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const userId = params.id;

    // Users can only access their own tenant context, unless they're admin
    if (session.user.id !== userId && session.user.role !== "admin") {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const context = await UserService.getUserTenantContext(userId);

    if (!context) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    return NextResponse.json({ context });
  } catch (error) {
    console.error("Error getting user tenant context:", error);
    return NextResponse.json(
      { error: "Failed to get user tenant context" },
      { status: 500 }
    );
  }
}
