"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Package,
  Edit,
  Trash2,
  Plus,
  Search,
  MapPin,
  AlertCircle,
  Hash,
  Tag
} from "lucide-react";
import { EquipmentInstanceWithRelations } from "@/lib/services/equipment-instances.service";
import { useEquipmentInstances, useDeleteEquipmentInstance } from "@/lib/hooks/queries/use-equipment-instance-queries";
import { DeleteConfirmationDialog } from "@/components/ui/delete-confirmation-dialog";
import { DeleteSuccessToast } from "@/components/ui/delete-success-toast";

interface EquipmentInstanceListProps {
  onEdit?: (instance: EquipmentInstanceWithRelations) => void;
  onAdd?: () => void;
  showActions?: boolean;
  equipmentId?: string;
  locationId?: string;
}

export function EquipmentInstanceList({
  onEdit,
  onAdd,
  showActions = true,
  equipmentId,
  locationId,
}: EquipmentInstanceListProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [deleteError, setDeleteError] = useState<string | null>(null);
  const [deleteConfirmation, setDeleteConfirmation] = useState<{
    isOpen: boolean;
    instance: EquipmentInstanceWithRelations | null;
  }>({ isOpen: false, instance: null });
  const [showDeleteSuccess, setShowDeleteSuccess] = useState(false);
  const [deletedInstanceName, setDeletedInstanceName] = useState("");

  const { data: instances = [], isLoading, error } = useEquipmentInstances();
  const deleteInstanceMutation = useDeleteEquipmentInstance();

  // Filter instances based on props and search query
  const filteredInstances = instances.filter((instance) => {
    // Filter by equipment if specified
    if (equipmentId && instance.equipmentId !== equipmentId) {
      return false;
    }

    // Filter by location if specified
    if (locationId && instance.locationId !== locationId) {
      return false;
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      const equipmentName = instance.equipment?.name?.toLowerCase() || "";
      const locationAddress = instance.location?.addressLine1?.toLowerCase() || "";
      const displayName = instance.displayName?.toLowerCase() || "";
      
      return (
        equipmentName.includes(query) ||
        locationAddress.includes(query) ||
        displayName.includes(query)
      );
    }

    return true;
  });

  const handleDeleteClick = (instance: EquipmentInstanceWithRelations) => {
    setDeleteConfirmation({ isOpen: true, instance });
  };

  const handleDeleteConfirm = async () => {
    if (!deleteConfirmation.instance) return;

    const instanceName = deleteConfirmation.instance.displayName || 
                        deleteConfirmation.instance.equipment?.name || 
                        "Unknown instance";

    try {
      setDeleteError(null);
      await deleteInstanceMutation.mutateAsync(deleteConfirmation.instance.id);

      // Close confirmation dialog
      setDeleteConfirmation({ isOpen: false, instance: null });

      // Show success toast
      setDeletedInstanceName(instanceName);
      setShowDeleteSuccess(true);

    } catch (error) {
      setDeleteError(error instanceof Error ? error.message : "Failed to delete equipment instance");
    }
  };

  const handleDeleteCancel = () => {
    setDeleteConfirmation({ isOpen: false, instance: null });
  };

  const formatInstanceInfo = (instance: EquipmentInstanceWithRelations) => {
    const parts = [
      instance.equipment?.name,
      `at ${instance.location?.addressLine1}`,
      `Qty: ${instance.quantity}`
    ].filter(Boolean);

    return parts.join(" • ");
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="py-8">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Failed to load equipment instances. Please try again.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              Equipment Instances
            </CardTitle>
            <CardDescription>
              Manage equipment instances and their locations
            </CardDescription>
          </div>
          {showActions && onAdd && (
            <Button onClick={onAdd}>
              <Plus className="h-4 w-4 mr-2" />
              Add Instance
            </Button>
          )}
        </div>

        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search instances..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Delete Error */}
        {deleteError && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{deleteError}</AlertDescription>
          </Alert>
        )}
      </CardHeader>

      <CardContent>
        {/* Equipment Instance List */}
        {filteredInstances.length === 0 ? (
          <div className="text-center py-8">
            <Package className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              {searchQuery ? "No instances found" : "No equipment instances yet"}
            </h3>
            <p className="text-gray-500 dark:text-gray-400 mb-4">
              {searchQuery 
                ? "Try adjusting your search terms."
                : "Get started by adding your first equipment instance."
              }
            </p>
            {showActions && onAdd && !searchQuery && (
              <Button onClick={onAdd}>
                <Plus className="h-4 w-4 mr-2" />
                Add Instance
              </Button>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            {filteredInstances.map((instance) => (
              <div
                key={instance.id}
                className="border rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <Package className="h-4 w-4 text-gray-500" />
                      <h4 className="font-medium text-gray-900 dark:text-gray-100">
                        {instance.displayName || instance.equipment?.name || "Unknown Equipment"}
                      </h4>
                      <Badge variant="secondary" className="text-xs">
                        <Hash className="h-3 w-3 mr-1" />
                        {instance.quantity}
                      </Badge>
                    </div>
                    
                    <div className="space-y-1 text-sm text-gray-600 dark:text-gray-400">
                      {instance.equipment && (
                        <div className="flex items-center gap-2">
                          <Package className="h-3 w-3" />
                          Equipment: {instance.equipment.name}
                          {instance.equipment.default_display_name && (
                            <span className="text-gray-500">
                              ({instance.equipment.default_display_name})
                            </span>
                          )}
                        </div>
                      )}

                      {instance.location && (
                        <div className="flex items-center gap-2">
                          <MapPin className="h-3 w-3" />
                          Location: {instance.location.addressLine1}
                          {instance.location.city && `, ${instance.location.city}`}
                        </div>
                      )}

                      {instance.displayName && (
                        <div className="flex items-center gap-2">
                          <Tag className="h-3 w-3" />
                          Display: {instance.displayName}
                        </div>
                      )}
                    </div>

                    <div className="flex items-center gap-2 mt-2">
                      <Badge variant="outline" className="text-xs">
                        ID: {instance.id.slice(-8)}
                      </Badge>
                    </div>
                  </div>

                  {showActions && (
                    <div className="flex items-center gap-2 ml-4">
                      {onEdit && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onEdit(instance)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                      )}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteClick(instance)}
                        disabled={deleteInstanceMutation.isPending}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Summary */}
        {filteredInstances.length > 0 && (
          <div className="mt-4 pt-4 border-t text-sm text-gray-500 dark:text-gray-400">
            Showing {filteredInstances.length} of {instances.length} equipment instances
          </div>
        )}
      </CardContent>

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationDialog
        isOpen={deleteConfirmation.isOpen}
        onClose={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        title="Delete Equipment Instance"
        description="Are you sure you want to delete this equipment instance? This action cannot be undone."
        itemName={deleteConfirmation.instance?.displayName || 
                  deleteConfirmation.instance?.equipment?.name || ""}
        isLoading={deleteInstanceMutation.isPending}
      />

      {/* Delete Success Toast */}
      <DeleteSuccessToast
        isVisible={showDeleteSuccess}
        onClose={() => setShowDeleteSuccess(false)}
        itemName={deletedInstanceName}
      />
    </Card>
  );
}
