import { ClassRepository } from '../../domain/ClassRepository';
import { Class } from '../../domain/Class';

const mockData: Class[] = [
  new Class('1', 'Morning Yoga', 'Relaxing yoga for all', 'Wellness', new Date('2025-07-27T07:00:00Z'), new Date('2025-07-27T08:00:00Z'), true, new Date(), new Date()),
  new Class('2', 'Evening Pilates', 'Pilates for strength', 'Fitness', new Date('2025-07-27T18:00:00Z'), new Date('2025-07-27T19:00:00Z'), true, new Date(), new Date()),
];

export class InMemoryClassRepository implements ClassRepository {
  async findById(id: string) {
    return mockData.find(c => c.id === id) || null;
  }
  async findAll() {
    return mockData;
  }
  async create(cls: Class) {
    mockData.push(cls);
    return cls;
  }
  async update(cls: Class) {
    const idx = mockData.findIndex(c => c.id === cls.id);
    if (idx >= 0) mockData[idx] = cls;
    return cls;
  }
  async delete(id: string) {
    const idx = mockData.findIndex(c => c.id === id);
    if (idx >= 0) mockData.splice(idx, 1);
  }
}
