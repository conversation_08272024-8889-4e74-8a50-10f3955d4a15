import { NextRequest, NextResponse } from "next/server";
import { ClassScheduleService } from "@/lib/services/class-schedule.service";
import { auth } from "@/lib/auth/config";
import { z } from "zod";

/**
 * API Route untuk Schedule Conflict Checking
 * 
 * Dedicated endpoint untuk real-time conflict detection.
 * Digunakan oleh frontend untuk immediate feedback saat user input.
 * 
 * Features:
 * - Real-time conflict detection
 * - Alternative time slot suggestions
 * - Detailed conflict information
 * - Performance optimized untuk quick responses
 */

// Schema untuk validasi conflict check request
const conflictCheckSchema = z.object({
  tenantId: z.number().int().positive("Tenant ID is required"),
  startTime: z.string().min(1, "Start time is required"),
  endTime: z.string().min(1, "End time is required"),
  locationId: z.string().optional(),
  facilityId: z.string().optional(),
  staffId: z.string().optional(),
  excludeScheduleId: z.string().optional(), // For updates
  bufferMinutes: z.number().int().min(0).max(120).optional(),
});

/**
 * POST /api/class-schedules/check-conflicts
 * 
 * Check for schedule conflicts dengan detailed analysis.
 * Returns conflict information dan suggestions.
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Parse dan validate request body
    const body = await request.json();
    console.log('🔍 Conflict check request:', JSON.stringify(body, null, 2));
    
    const validatedData = conflictCheckSchema.parse(body);

    // Convert datetime strings to Date objects
    const conflictParams = {
      tenantId: validatedData.tenantId,
      startTime: new Date(validatedData.startTime),
      endTime: new Date(validatedData.endTime),
      locationId: validatedData.locationId,
      facilityId: validatedData.facilityId,
      staffId: validatedData.staffId,
      excludeScheduleId: validatedData.excludeScheduleId,
      bufferMinutes: validatedData.bufferMinutes,
    };

    // Validate time range
    if (conflictParams.startTime >= conflictParams.endTime) {
      return NextResponse.json(
        { error: "Start time must be before end time" },
        { status: 400 }
      );
    }

    // Check for conflicts
    const conflictResult = await ClassScheduleService.checkScheduleConflicts(conflictParams);

    // Format response dengan detailed information
    const response = {
      success: true,
      data: {
        hasConflicts: conflictResult.hasConflicts,
        canProceed: conflictResult.canProceed,
        conflicts: conflictResult.conflicts.map(conflict => ({
          id: conflict.conflictingSchedule.id,
          type: conflict.conflictType,
          severity: conflict.severity,
          conflictingClass: conflict.conflictingSchedule.className || 'Unknown Class',
          conflictingLocation: conflict.conflictDetails.resourceConflict.location,
          conflictingFacility: conflict.conflictDetails.resourceConflict.facility,
          timeOverlap: {
            start: conflict.conflictDetails.timeOverlap.start.toISOString(),
            end: conflict.conflictDetails.timeOverlap.end.toISOString(),
            durationMinutes: conflict.conflictDetails.timeOverlap.durationMinutes,
          },
          message: formatConflictMessage(conflict),
        })),
        suggestions: conflictResult.suggestions?.map(suggestion => ({
          startTime: suggestion.suggestedStartTime.toISOString(),
          endTime: suggestion.suggestedEndTime.toISOString(),
          location: suggestion.availableLocation,
          facility: suggestion.availableFacility,
          reason: suggestion.reason,
          confidence: suggestion.confidence,
        })) || [],
        warnings: conflictResult.warnings || [],
      },
      meta: {
        checkTime: new Date().toISOString(),
        requestParams: {
          timeRange: `${conflictParams.startTime.toISOString()} - ${conflictParams.endTime.toISOString()}`,
          location: validatedData.locationId,
          facility: validatedData.facilityId,
          staff: validatedData.staffId,
        },
      },
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error checking schedule conflicts:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          error: "Validation failed", 
          details: error.errors 
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      // Handle specific service errors
      if (error.message.includes("not found")) {
        return NextResponse.json(
          { error: error.message },
          { status: 400 }
        );
      }

      if (error.message.includes("invalid")) {
        return NextResponse.json(
          { error: error.message },
          { status: 400 }
        );
      }
    }

    return NextResponse.json(
      { error: "Failed to check schedule conflicts" },
      { status: 500 }
    );
  }
}

/**
 * Format conflict message untuk user-friendly display
 */
function formatConflictMessage(conflict: any): string {
    const { conflictType, conflictDetails } = conflict;
    const resource = conflictDetails.resourceConflict.location || 
                    conflictDetails.resourceConflict.facility || 
                    'staff member';
    
    const timeStr = `${conflictDetails.timeOverlap.start.toLocaleTimeString()} - ${conflictDetails.timeOverlap.end.toLocaleTimeString()}`;
    
    switch (conflictType) {
      case 'EXACT_TIME_LOCATION':
        return `Exact time conflict with existing class at ${resource} during ${timeStr}`;
      case 'EXACT_TIME_FACILITY':
        return `Exact time conflict with existing class using ${resource} during ${timeStr}`;
      case 'PARTIAL_TIME_LOCATION':
        return `Partial time overlap with existing class at ${resource} during ${timeStr}`;
      case 'PARTIAL_TIME_FACILITY':
        return `Partial time overlap with existing class using ${resource} during ${timeStr}`;
      case 'STAFF_DOUBLE_BOOKING':
        return `Staff member is already assigned to another class during ${timeStr}`;
      default:
        return `Schedule conflict detected during ${timeStr}`;
    }
}

/**
 * GET /api/class-schedules/check-conflicts
 * 
 * Get available time slots untuk specific date/location/facility.
 * Useful untuk suggesting alternative times.
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get('tenantId');
    const date = searchParams.get('date');
    const locationId = searchParams.get('locationId');
    const facilityId = searchParams.get('facilityId');
    const duration = searchParams.get('duration');

    if (!tenantId || !date) {
      return NextResponse.json(
        { error: "Tenant ID and date are required" },
        { status: 400 }
      );
    }

    // Generate available time slots untuk the day
    const targetDate = new Date(date);
    const durationMinutes = duration ? parseInt(duration) : 60;
    
    // Generate available time slots untuk the day
    const availableSlots = await generateAvailableTimeSlots(
      parseInt(tenantId),
      targetDate,
      durationMinutes,
      locationId || undefined,
      facilityId || undefined
    );

    return NextResponse.json({
      success: true,
      data: {
        date: targetDate.toISOString().split('T')[0],
        availableSlots,
        totalSlots: availableSlots.length,
      },
    });
  } catch (error) {
    console.error("Error getting available time slots:", error);
    return NextResponse.json(
      { error: "Failed to get available time slots" },
      { status: 500 }
    );
  }

}

/**
 * Generate available time slots untuk specific date
 */
async function generateAvailableTimeSlots(
    tenantId: number,
    date: Date,
    durationMinutes: number,
    locationId?: string,
    facilityId?: string
  ) {
    const slots = [];
    const startHour = 6; // 6 AM
    const endHour = 22; // 10 PM

    for (let hour = startHour; hour <= endHour - Math.ceil(durationMinutes / 60); hour++) {
      for (let minute = 0; minute < 60; minute += 30) {
        const startTime = new Date(date);
        startTime.setHours(hour, minute, 0, 0);
        
        const endTime = new Date(startTime);
        endTime.setMinutes(endTime.getMinutes() + durationMinutes);

        // Check if this slot is available
        const conflictResult = await ClassScheduleService.checkScheduleConflicts({
          tenantId,
          startTime,
          endTime,
          locationId,
          facilityId,
        });

        slots.push({
          startTime: startTime.toISOString(),
          endTime: endTime.toISOString(),
          available: conflictResult.canProceed,
          conflictCount: conflictResult.conflicts.length,
          timeSlot: `${startTime.toLocaleTimeString()} - ${endTime.toLocaleTimeString()}`,
        });
      }
    }

    return slots;
  }
