# 🔐 Panduan Lengkap Implementasi RBAC (Role-Based Access Control) di Next.js SaaS

> **"Keamanan aplikasi itu seperti kunci rumah - tanpa sistem yang tepat, siapa saja bisa masuk ke ruangan yang seharusnya tidak boleh mereka akses!"** - Sand<PERSON>h Style 😊

## 📚 Table of Contents

1. [Pengenalan RBAC](#1-pengenalan-rbac)
2. [Arsitektur Sistem](#2-arsitektur-sistem)
3. [Database Schema](#3-database-schema)
4. [Setup Environment](#4-setup-environment)
5. [Implementasi Backend](#5-implementasi-backend)
6. [Implementasi Frontend](#6-implementasi-frontend)
7. [Testing & Verification](#7-testing--verification)
8. [Troubleshooting](#8-troubleshooting)
9. [Security Best Practices](#9-security-best-practices)
10. [Performance Optimization](#10-performance-optimization)

---

## 1. Pengenalan RBAC

### 🤔 Apa itu RBAC?

RBAC (Role-Based Access Control) adalah sistem keamanan yang mengatur siapa boleh mengakses apa dalam aplikasi kita. Bayangkan seperti sistem kunci di hotel:

- **User** = Tamu hotel
- **Role** = Jenis kartu akses (VIP, Standard, Staff)
- **Permission** = Ruangan yang bisa diakses (Kamar, Gym, Pool, Office)

### 🎯 Mengapa RBAC Penting?

1. **Keamanan Berlapis**: Mencegah akses tidak sah ke fitur sensitif
2. **Skalabilitas**: Mudah menambah role baru tanpa mengubah kode
3. **Maintainability**: Centralized permission management
4. **Compliance**: Memenuhi standar keamanan enterprise

### 🏗️ Komponen Utama RBAC

```
User ←→ Role ←→ Permission ←→ Resource
```

- **User**: Pengguna aplikasi (<EMAIL>)
- **Role**: Jabatan/posisi (Super Admin, Tenant Admin, Instructor)
- **Permission**: Hak akses spesifik (locations.manage, users.read)
- **Resource**: Fitur/data yang diproteksi (Dashboard, User Management)

---

## 2. Arsitektur Sistem

### 🏛️ Overview Arsitektur

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Middleware    │    │   Database      │
│                 │    │                 │    │                 │
│ • Navigation    │◄──►│ • Auth Check    │◄──►│ • Users         │
│ • Components    │    │ • RBAC Guard    │    │ • Roles         │
│ • Hooks         │    │ • Session       │    │ • Permissions   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 🔄 Data Flow

1. **Login**: User authenticate → Session created with RBAC data
2. **Navigation**: Frontend filters menu based on permissions
3. **API Calls**: Middleware validates permissions before processing
4. **Components**: Conditional rendering based on user permissions

---

## 3. Database Schema

### 📊 Tabel Utama

#### Users Table
```sql
CREATE TABLE users (
  id VARCHAR PRIMARY KEY,
  email VARCHAR UNIQUE NOT NULL,
  name VARCHAR,
  password VARCHAR NOT NULL,
  tenant_id VARCHAR,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### Roles Table
```sql
CREATE TABLE roles (
  id VARCHAR PRIMARY KEY,
  name VARCHAR UNIQUE NOT NULL,
  display_name VARCHAR NOT NULL,
  description TEXT,
  is_system_role BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### Permissions Table
```sql
CREATE TABLE permissions (
  id VARCHAR PRIMARY KEY,
  module VARCHAR NOT NULL,           -- 'users', 'locations', 'classes'
  action VARCHAR NOT NULL,           -- 'read', 'create', 'update', 'delete', 'manage'
  resource VARCHAR,                  -- optional: specific resource
  display_name VARCHAR NOT NULL,
  description TEXT,
  is_system_permission BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### Junction Tables
```sql
-- Many-to-Many: Users ↔ Roles
CREATE TABLE user_roles (
  id VARCHAR PRIMARY KEY,
  user_id VARCHAR REFERENCES users(id) ON DELETE CASCADE,
  role_id VARCHAR REFERENCES roles(id) ON DELETE CASCADE,
  assigned_by VARCHAR REFERENCES users(id),
  assigned_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(user_id, role_id)
);

-- Many-to-Many: Roles ↔ Permissions
CREATE TABLE role_permissions (
  id VARCHAR PRIMARY KEY,
  role_id VARCHAR REFERENCES roles(id) ON DELETE CASCADE,
  permission_id VARCHAR REFERENCES permissions(id) ON DELETE CASCADE,
  granted_by VARCHAR REFERENCES users(id),
  granted_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(role_id, permission_id)
);
```

### 🎯 Permission Naming Convention

Format: `{module}.{action}`

**Contoh:**
- `users.read` - Melihat daftar user
- `users.create` - Membuat user baru
- `users.manage` - Full access ke user management
- `locations.manage` - Full access ke location management
- `admin.access` - Akses ke admin panel

---

## 4. Setup Environment

### 📦 Dependencies

```bash
# Core dependencies (sudah ada di Next.js project)
npm install next-auth@beta
npm install drizzle-orm
npm install @neondatabase/serverless
npm install bcryptjs
npm install zod

# Development dependencies
npm install @types/bcryptjs --save-dev
```

### ⚙️ Environment Variables

```env
# .env.local
NEXTAUTH_SECRET=your-super-secret-key-here
NEXTAUTH_URL=http://localhost:3000

# Database
DATABASE_URL=postgresql://username:password@localhost:5432/database_name

# Debug (optional)
NEXTAUTH_DEBUG=true
```

### ✅ Checklist Setup

- [ ] Dependencies installed
- [ ] Environment variables configured
- [ ] Database connection working
- [ ] NextAuth configured

---

## 5. Implementasi Backend

### 🔧 NextAuth Configuration

**File: `src/lib/auth/config.ts`**

```typescript
import { NextAuthConfig } from "next-auth";
import CredentialsProvider from "next-auth/providers/credentials";
import { compare } from "bcryptjs";
import { getUserByEmail, getUserRBACData } from "@/lib/services/user-service";

export const authConfig: NextAuthConfig = {
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) return null;
        
        const user = await getUserByEmail(credentials.email as string);
        if (!user) return null;
        
        const isValid = await compare(credentials.password as string, user.password);
        if (!isValid) return null;
        
        return {
          id: user.id,
          email: user.email,
          name: user.name,
        };
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
        
        // Load RBAC data dan simpan di JWT
        console.log(`Loading RBAC data for user: ${user.id}`);
        const rbacData = await getUserRBACData(user.id);
        console.log(`RBAC data loaded:`, { 
          roles: rbacData.roles, 
          permissions: rbacData.permissions.length 
        });
        
        token.roles = rbacData.roles;
        token.permissions = rbacData.permissions;
        token.accessibleLocations = rbacData.accessibleLocations;
      }
      return token;
    },
    async session({ session, token }) {
      if (token && session.user) {
        session.user.id = token.id as string;
        session.user.roles = token.roles as string[];
        session.user.permissions = token.permissions as string[];
        session.user.accessibleLocations = token.accessibleLocations as string[];
      }
      return session;
    },
  },
  pages: {
    signIn: "/auth/signin",
  },
  session: {
    strategy: "jwt",
  },
};
```

### 🏗️ RBAC Service Layer

**File: `src/lib/services/rbac-service.ts`**

```typescript
import { db } from "@/lib/db";
import { users, roles, permissions, userRoles, rolePermissions } from "@/lib/db/schema";
import { eq, inArray } from "drizzle-orm";

export class RBACService {
  /**
   * Mendapatkan semua data RBAC untuk user
   * Ini adalah fungsi inti yang mengumpulkan roles dan permissions
   */
  static async getUserRBACData(userId: string) {
    try {
      // 1. Ambil roles user
      const userRoleData = await db
        .select({
          roleName: roles.name,
          roleDisplayName: roles.displayName,
        })
        .from(userRoles)
        .innerJoin(roles, eq(userRoles.roleId, roles.id))
        .where(eq(userRoles.userId, userId));

      const roleNames = userRoleData.map(r => r.roleName);
      
      if (roleNames.length === 0) {
        return {
          roles: [],
          permissions: [],
          accessibleLocations: [],
        };
      }

      // 2. Ambil permissions dari roles
      const roleIds = await db
        .select({ id: roles.id })
        .from(roles)
        .where(inArray(roles.name, roleNames));

      const permissionData = await db
        .select({
          module: permissions.module,
          action: permissions.action,
        })
        .from(rolePermissions)
        .innerJoin(permissions, eq(rolePermissions.permissionId, permissions.id))
        .where(inArray(rolePermissions.roleId, roleIds.map(r => r.id)));

      // 3. Format permissions ke string "module.action"
      const permissionStrings = permissionData.map(p => `${p.module}.${p.action}`);

      return {
        roles: roleNames,
        permissions: permissionStrings,
        accessibleLocations: [], // TODO: Implement location restrictions
      };
    } catch (error) {
      console.error("Error getting user RBAC data:", error);
      return {
        roles: [],
        permissions: [],
        accessibleLocations: [],
      };
    }
  }

  /**
   * Check apakah user punya permission tertentu
   */
  static async userHasPermission(userId: string, permission: string): Promise<boolean> {
    const rbacData = await this.getUserRBACData(userId);
    return rbacData.permissions.includes(permission);
  }

  /**
   * Check apakah user punya role tertentu
   */
  static async userHasRole(userId: string, roleName: string): Promise<boolean> {
    const rbacData = await this.getUserRBACData(userId);
    return rbacData.roles.includes(roleName);
  }
}
```

### 🛡️ API Middleware Protection

**File: `src/lib/middleware/rbac-middleware.ts`**

```typescript
import { NextRequest, NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";

export function withRBAC(requiredPermission: string) {
  return function (handler: Function) {
    return async function (req: NextRequest, context: any) {
      try {
        // 1. Ambil token dari request
        const token = await getToken({ 
          req, 
          secret: process.env.NEXTAUTH_SECRET 
        });

        if (!token) {
          return NextResponse.json(
            { error: "Unauthorized - No token" }, 
            { status: 401 }
          );
        }

        // 2. Check permission
        const userPermissions = token.permissions as string[] || [];
        
        if (!userPermissions.includes(requiredPermission)) {
          return NextResponse.json(
            { 
              error: "Forbidden - Insufficient permissions",
              required: requiredPermission,
              userPermissions 
            }, 
            { status: 403 }
          );
        }

        // 3. Permission valid, lanjutkan ke handler
        return handler(req, context);
      } catch (error) {
        console.error("RBAC Middleware Error:", error);
        return NextResponse.json(
          { error: "Internal server error" }, 
          { status: 500 }
        );
      }
    };
  };
}
```

**Contoh penggunaan di API route:**

```typescript
// src/app/api/users/route.ts
import { withRBAC } from "@/lib/middleware/rbac-middleware";

async function getUsersHandler(req: NextRequest) {
  // Logic untuk get users
  return NextResponse.json({ users: [] });
}

// Protect endpoint dengan permission "users.read"
export const GET = withRBAC("users.read")(getUsersHandler);
```

---

## 6. Implementasi Frontend

### 🎣 RBAC Hooks

**File: `src/lib/hooks/use-rbac.ts`**

```typescript
"use client";

import { useSession } from "next-auth/react";
import { useMemo } from "react";

/**
 * Hook utama untuk RBAC di frontend
 * Ini seperti "remote control" untuk mengecek permissions
 */
export function useRBAC() {
  const { data: session, status } = useSession();

  const rbacData = useMemo(() => {
    if (!session?.user) {
      return {
        roles: [],
        permissions: [],
        accessibleLocations: [],
        isLoading: status === "loading",
        error: null,
      };
    }

    return {
      roles: session.user.roles || [],
      permissions: session.user.permissions || [],
      accessibleLocations: session.user.accessibleLocations || [],
      isLoading: false,
      error: null,
    };
  }, [session, status]);

  // Helper functions - ini yang akan sering kita pakai
  const hasPermission = (permission: string): boolean => {
    return rbacData.permissions.includes(permission);
  };

  const hasRole = (role: string): boolean => {
    return rbacData.roles.includes(role);
  };

  const isSuperAdmin = (): boolean => {
    return rbacData.roles.includes("super_admin");
  };

  return {
    ...rbacData,
    hasPermission,
    hasRole,
    isSuperAdmin,
  };
}
```

### 🛡️ Permission Guard Components

**File: `src/components/rbac/permission-guard.tsx`**

```typescript
"use client";

import { useRBAC } from "@/lib/hooks/use-rbac";
import { ReactNode } from "react";

interface PermissionGuardProps {
  permission: string;
  children: ReactNode;
  fallback?: ReactNode;
  requireAll?: boolean; // untuk multiple permissions
}

/**
 * Component untuk conditional rendering berdasarkan permission
 * Seperti "bouncer" di club yang cek ID sebelum masuk
 */
export function PermissionGuard({ 
  permission, 
  children, 
  fallback = null 
}: PermissionGuardProps) {
  const { hasPermission, isLoading } = useRBAC();

  // Tampilkan loading state
  if (isLoading) {
    return <div className="animate-pulse bg-gray-200 h-8 rounded" />;
  }

  // Check permission
  if (!hasPermission(permission)) {
    return <>{fallback}</>;
  }

  return <>{children}</>;
}

/**
 * Contoh penggunaan:
 * 
 * <PermissionGuard permission="users.manage">
 *   <UserManagementButton />
 * </PermissionGuard>
 */
```

### 🧭 Navigation dengan RBAC

**File: `src/components/dashboard/nav.tsx`**

```typescript
"use client";

import { useRBAC } from "@/lib/hooks/use-rbac";
import Link from "next/link";

interface NavItem {
  title: string;
  href: string;
  icon: any;
  requiredPermission?: {
    module: string;
    action: string;
  };
}

const navItems: NavItem[] = [
  {
    title: "Dashboard",
    href: "/dashboard",
    icon: LayoutDashboard,
    // No permission required - semua user bisa akses
  },
  {
    title: "Locations",
    href: "/locations",
    icon: MapPin,
    requiredPermission: { module: "locations", action: "manage" },
  },
  {
    title: "Users",
    href: "/users",
    icon: Users,
    requiredPermission: { module: "users", action: "manage" },
  },
  // ... more items
];

export function DashboardNav() {
  const { hasPermission, isLoading, permissions } = useRBAC();

  // Filter navigation berdasarkan permissions
  const filteredNavItems = navItems.filter((item) => {
    if (!item.requiredPermission) return true;
    
    const permissionString = `${item.requiredPermission.module}.${item.requiredPermission.action}`;
    return hasPermission(permissionString);
  });

  // Loading state
  if (isLoading || permissions.length === 0) {
    return <NavigationSkeleton />;
  }

  return (
    <nav className="w-64 border-r bg-background p-6">
      <div className="space-y-2">
        {filteredNavItems.map((item) => (
          <Link
            key={item.href}
            href={item.href}
            className="flex items-center space-x-3 rounded-lg px-3 py-2"
          >
            <item.icon className="h-4 w-4" />
            <span>{item.title}</span>
          </Link>
        ))}
      </div>
    </nav>
  );
}
```

---

## 7. Testing & Verification

### 🧪 Testing Checklist

#### Database Testing
```bash
# 1. Verify tables created
psql -d your_database -c "\dt"

# 2. Check sample data
psql -d your_database -c "
SELECT u.email, r.name as role, p.module || '.' || p.action as permission
FROM users u
JOIN user_roles ur ON u.id = ur.user_id
JOIN roles r ON ur.role_id = r.id
JOIN role_permissions rp ON r.id = rp.role_id
JOIN permissions p ON rp.permission_id = p.id
WHERE u.email = '<EMAIL>';
"
```

#### Frontend Testing
1. **Login sebagai Super Admin**
   - [ ] Semua navigation items terlihat
   - [ ] Bisa akses semua halaman
   - [ ] Role Management accessible

2. **Login sebagai Limited User**
   - [ ] Hanya navigation items yang sesuai permission terlihat
   - [ ] Direct URL access ke restricted pages di-block
   - [ ] API calls return 403 untuk unauthorized actions

#### API Testing
```bash
# Test protected endpoint
curl -X GET http://localhost:3000/api/users \
  -H "Cookie: next-auth.session-token=your-session-token"

# Should return 403 for users without users.read permission
```

### 🔍 Debug Tools

**Console Debugging:**
```typescript
// Tambahkan di component untuk debug
const { permissions, roles, isLoading } = useRBAC();
console.log("RBAC Debug:", { permissions, roles, isLoading });
```

**Network Tab Monitoring:**
- Check `/api/auth/session` response
- Verify JWT token contains RBAC data
- Monitor API calls for 403 responses

---

## 8. Troubleshooting

### ❌ Common Issues & Solutions

#### Issue 1: Navigation Items Tidak Muncul
**Gejala:** Semua navigation items hilang, bahkan untuk super admin

**Penyebab:** Race condition - navigation render sebelum RBAC data loaded

**Solusi:**
```typescript
// Pastikan check loading state dengan benar
if (isLoading || permissions.length === 0) {
  return <LoadingSkeleton />;
}
```

#### Issue 2: Permission Check Selalu False
**Gejala:** `hasPermission()` selalu return false

**Debug Steps:**
1. Check session data: `console.log(session.user.permissions)`
2. Verify permission format: `"module.action"` bukan `{module, action}`
3. Check database: pastikan role_permissions table terisi

**Solusi:**
```typescript
// Pastikan permission string format benar
const permissionString = `${module}.${action}`;
return hasPermission(permissionString);
```

#### Issue 3: API Returns 403 Unexpectedly
**Penyebab:** Middleware tidak mendapat token atau permission salah

**Debug:**
```typescript
// Tambahkan logging di middleware
console.log("Token:", token);
console.log("Required permission:", requiredPermission);
console.log("User permissions:", token.permissions);
```

#### Issue 4: Session Tidak Ter-update Setelah Role Change
**Penyebab:** JWT token di-cache, perlu refresh

**Solusi:**
```typescript
// Force session refresh
import { signOut, signIn } from "next-auth/react";

// Atau implement session refresh
await update(); // NextAuth v5
```

### 🚨 Emergency Fixes

**Jika Terkunci dari Admin Panel:**
1. Direct database access:
```sql
-- Tambahkan super admin permission ke user
INSERT INTO user_roles (id, user_id, role_id) 
SELECT gen_random_uuid(), 'user-id', r.id 
FROM roles r WHERE r.name = 'super_admin';
```

2. Bypass RBAC sementara:
```typescript
// Tambahkan di middleware untuk emergency access
if (token.email === '<EMAIL>') {
  return handler(req, context);
}
```

---

## 9. Security Best Practices

### 🔒 Security Checklist

#### JWT Security
- [ ] `NEXTAUTH_SECRET` menggunakan strong random string
- [ ] JWT expiration time reasonable (24 hours)
- [ ] Sensitive data tidak disimpan di JWT

#### Permission Design
- [ ] Principle of least privilege - berikan minimum permission yang dibutuhkan
- [ ] Granular permissions - hindari "god mode" permissions
- [ ] Regular permission audit

#### Database Security
- [ ] Foreign key constraints untuk data integrity
- [ ] Audit trail untuk role/permission changes
- [ ] Regular backup database

#### Frontend Security
- [ ] Sensitive UI elements hidden dengan PermissionGuard
- [ ] Client-side checks sebagai UX enhancement, bukan security
- [ ] Always validate di backend

### 🛡️ Defense in Depth

```
┌─────────────────┐
│   Frontend      │ ← UX Layer (hide buttons, navigation)
├─────────────────┤
│   Middleware    │ ← API Protection (check permissions)
├─────────────────┤
│   Database      │ ← Data Layer (foreign keys, constraints)
└─────────────────┘
```

**Ingat:** Frontend security adalah untuk UX, backend security adalah untuk actual protection!

---

## 10. Performance Optimization

### ⚡ Optimization Strategies

#### Database Optimization
```sql
-- Index untuk faster queries
CREATE INDEX idx_user_roles_user_id ON user_roles(user_id);
CREATE INDEX idx_role_permissions_role_id ON role_permissions(role_id);
CREATE INDEX idx_permissions_module_action ON permissions(module, action);
```

#### Caching Strategy
```typescript
// Cache RBAC data di session untuk mengurangi DB calls
// Data di-refresh hanya saat login atau role change

// Redis caching untuk production
import Redis from 'ioredis';
const redis = new Redis(process.env.REDIS_URL);

async function getCachedRBACData(userId: string) {
  const cached = await redis.get(`rbac:${userId}`);
  if (cached) return JSON.parse(cached);
  
  const data = await RBACService.getUserRBACData(userId);
  await redis.setex(`rbac:${userId}`, 3600, JSON.stringify(data)); // 1 hour cache
  return data;
}
```

#### Frontend Optimization
```typescript
// Memoize permission checks
const memoizedPermissions = useMemo(() => {
  return permissions.reduce((acc, permission) => {
    acc[permission] = true;
    return acc;
  }, {} as Record<string, boolean>);
}, [permissions]);

const hasPermission = useCallback((permission: string) => {
  return !!memoizedPermissions[permission];
}, [memoizedPermissions]);
```

### 📊 Monitoring & Metrics

**Key Metrics to Track:**
- Permission check latency
- Failed authorization attempts
- Session refresh frequency
- Database query performance

**Logging Strategy:**
```typescript
// Structured logging untuk RBAC events
logger.info("RBAC_CHECK", {
  userId,
  permission,
  granted: hasAccess,
  timestamp: new Date().toISOString(),
  userAgent: req.headers['user-agent']
});
```

---

## 🎉 Kesimpulan

Selamat! Kamu telah berhasil mengimplementasikan sistem RBAC yang robust dan scalable. Sistem ini memberikan:

✅ **Security**: Multi-layer protection dari frontend hingga database
✅ **Scalability**: Mudah menambah roles dan permissions baru
✅ **Maintainability**: Clean architecture dengan separation of concerns
✅ **Performance**: Optimized queries dan caching strategy
✅ **User Experience**: Smooth navigation dengan proper loading states

### 🚀 Next Steps

1. **Implement Audit Logging**: Track semua RBAC changes
2. **Add Location-based Restrictions**: Extend untuk multi-tenant scenarios
3. **Role Hierarchy**: Implement parent-child role relationships
4. **Permission Groups**: Bundle related permissions
5. **API Rate Limiting**: Protect against abuse

### 💡 Tips untuk Developer

> **"Ingat, RBAC itu seperti membangun rumah - fondasinya harus kuat dari awal. Lebih baik spend extra time di design phase daripada refactor nanti!"**

**Best Practices:**
- Always test dengan multiple user roles
- Document setiap permission dengan jelas
- Regular security audit
- Keep permissions granular tapi tidak over-complicated
- Monitor performance metrics

**Happy Coding! 🚀**

---

---

## 📋 Appendix

### A. Database Seeding Script

**File: `scripts/seed-rbac.ts`**

```typescript
import { db } from "@/lib/db";
import { roles, permissions, rolePermissions } from "@/lib/db/schema";
import { generateId } from "@/lib/utils";

export async function seedRBACData() {
  console.log("🌱 Seeding RBAC data...");

  // 1. Create Roles
  const roleData = [
    {
      id: generateId(),
      name: "super_admin",
      displayName: "Super Administrator",
      description: "Full system access",
      isSystemRole: true,
    },
    {
      id: generateId(),
      name: "tenant_admin",
      displayName: "Tenant Administrator",
      description: "Full access within tenant",
      isSystemRole: true,
    },
    {
      id: generateId(),
      name: "instructor",
      displayName: "Instructor/Trainer",
      description: "Manage classes and customers",
      isSystemRole: true,
    },
    {
      id: generateId(),
      name: "customer",
      displayName: "Customer",
      description: "Basic customer access",
      isSystemRole: true,
    },
  ];

  await db.insert(roles).values(roleData);
  console.log("✅ Roles created");

  // 2. Create Permissions
  const permissionData = [
    // Admin permissions
    { module: "admin", action: "access", displayName: "Access Admin Panel" },
    { module: "system", action: "manage", displayName: "Manage System" },

    // User management
    { module: "users", action: "read", displayName: "View Users" },
    { module: "users", action: "manage", displayName: "Manage Users" },

    // Role management
    { module: "roles", action: "read", displayName: "View Roles" },
    { module: "roles", action: "create", displayName: "Create Roles" },
    { module: "roles", action: "update", displayName: "Update Roles" },
    { module: "roles", action: "delete", displayName: "Delete Roles" },
    { module: "roles", action: "assign", displayName: "Assign Roles" },
    { module: "roles", action: "revoke", displayName: "Revoke Roles" },

    // Business modules
    { module: "locations", action: "manage", displayName: "Manage Locations" },
    { module: "equipment", action: "manage", displayName: "Manage Equipment" },
    { module: "classes", action: "manage", displayName: "Manage Classes" },
    { module: "customers", action: "manage", displayName: "Manage Customers" },
    { module: "packages", action: "manage", displayName: "Manage Packages" },
    { module: "bookings", action: "manage", displayName: "Manage Bookings" },

    // Reports
    { module: "reports", action: "read", displayName: "View Reports" },
    { module: "reports", action: "export", displayName: "Export Reports" },

    // Tenant management
    { module: "tenant", action: "read", displayName: "View Tenant Info" },
    { module: "tenant", action: "manage", displayName: "Manage Tenant" },
  ];

  const permissionInserts = permissionData.map(p => ({
    id: generateId(),
    module: p.module,
    action: p.action,
    displayName: p.displayName,
    description: p.displayName,
    isSystemPermission: true,
  }));

  await db.insert(permissions).values(permissionInserts);
  console.log("✅ Permissions created");

  // 3. Assign Permissions to Roles
  const superAdminRole = await db.select().from(roles).where(eq(roles.name, "super_admin"));
  const allPermissions = await db.select().from(permissions);

  // Super Admin gets all permissions
  const superAdminPermissions = allPermissions.map(p => ({
    id: generateId(),
    roleId: superAdminRole[0].id,
    permissionId: p.id,
  }));

  await db.insert(rolePermissions).values(superAdminPermissions);
  console.log("✅ Super Admin permissions assigned");

  console.log("🎉 RBAC seeding completed!");
}
```

### B. Migration Scripts

**File: `migrations/001_create_rbac_tables.sql`**

```sql
-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create roles table
CREATE TABLE IF NOT EXISTS roles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name VARCHAR(50) UNIQUE NOT NULL,
  display_name VARCHAR(100) NOT NULL,
  description TEXT,
  is_system_role BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create permissions table
CREATE TABLE IF NOT EXISTS permissions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  module VARCHAR(50) NOT NULL,
  action VARCHAR(50) NOT NULL,
  resource VARCHAR(100),
  display_name VARCHAR(100) NOT NULL,
  description TEXT,
  is_system_permission BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(module, action, resource)
);

-- Create user_roles junction table
CREATE TABLE IF NOT EXISTS user_roles (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
  assigned_by UUID REFERENCES users(id),
  assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, role_id)
);

-- Create role_permissions junction table
CREATE TABLE IF NOT EXISTS role_permissions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
  permission_id UUID NOT NULL REFERENCES permissions(id) ON DELETE CASCADE,
  granted_by UUID REFERENCES users(id),
  granted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(role_id, permission_id)
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_roles_user_id ON user_roles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_roles_role_id ON user_roles(role_id);
CREATE INDEX IF NOT EXISTS idx_role_permissions_role_id ON role_permissions(role_id);
CREATE INDEX IF NOT EXISTS idx_role_permissions_permission_id ON role_permissions(permission_id);
CREATE INDEX IF NOT EXISTS idx_permissions_module_action ON permissions(module, action);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_roles_updated_at BEFORE UPDATE ON roles
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_permissions_updated_at BEFORE UPDATE ON permissions
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
```

### C. Environment Setup Script

**File: `scripts/setup-rbac.sh`**

```bash
#!/bin/bash

echo "🚀 Setting up RBAC system..."

# 1. Install dependencies
echo "📦 Installing dependencies..."
npm install next-auth@beta drizzle-orm @neondatabase/serverless bcryptjs zod
npm install --save-dev @types/bcryptjs

# 2. Create directories
echo "📁 Creating directories..."
mkdir -p src/lib/auth
mkdir -p src/lib/services
mkdir -p src/lib/hooks
mkdir -p src/lib/middleware
mkdir -p src/components/rbac
mkdir -p docs
mkdir -p scripts
mkdir -p migrations

# 3. Generate NextAuth secret
echo "🔑 Generating NextAuth secret..."
NEXTAUTH_SECRET=$(openssl rand -base64 32)
echo "NEXTAUTH_SECRET=$NEXTAUTH_SECRET" >> .env.local

# 4. Run database migrations
echo "🗄️ Running database migrations..."
psql $DATABASE_URL -f migrations/001_create_rbac_tables.sql

# 5. Seed initial data
echo "🌱 Seeding RBAC data..."
npm run seed:rbac

echo "✅ RBAC setup completed!"
echo "🔗 Next steps:"
echo "  1. Update your NextAuth configuration"
echo "  2. Implement RBAC hooks and components"
echo "  3. Add middleware to protect API routes"
echo "  4. Test with different user roles"
```

### D. Testing Utilities

**File: `tests/rbac-test-utils.ts`**

```typescript
import { db } from "@/lib/db";
import { users, userRoles, roles } from "@/lib/db/schema";
import { generateId } from "@/lib/utils";
import { hash } from "bcryptjs";

export class RBACTestUtils {
  /**
   * Create test user dengan role tertentu
   */
  static async createTestUser(email: string, roleName: string, password = "password123") {
    // 1. Create user
    const hashedPassword = await hash(password, 12);
    const userId = generateId();

    await db.insert(users).values({
      id: userId,
      email,
      name: email.split("@")[0],
      password: hashedPassword,
    });

    // 2. Assign role
    const role = await db.select().from(roles).where(eq(roles.name, roleName));
    if (role.length > 0) {
      await db.insert(userRoles).values({
        id: generateId(),
        userId,
        roleId: role[0].id,
      });
    }

    return userId;
  }

  /**
   * Cleanup test data
   */
  static async cleanup() {
    await db.delete(userRoles).where(like(users.email, "test%"));
    await db.delete(users).where(like(users.email, "test%"));
  }

  /**
   * Verify user permissions
   */
  static async verifyUserPermissions(userId: string, expectedPermissions: string[]) {
    const rbacData = await RBACService.getUserRBACData(userId);

    for (const permission of expectedPermissions) {
      if (!rbacData.permissions.includes(permission)) {
        throw new Error(`User missing permission: ${permission}`);
      }
    }

    return true;
  }
}

// Jest test example
describe("RBAC System", () => {
  beforeEach(async () => {
    await RBACTestUtils.cleanup();
  });

  test("Super admin should have all permissions", async () => {
    const userId = await RBACTestUtils.createTestUser("<EMAIL>", "super_admin");

    const rbacData = await RBACService.getUserRBACData(userId);

    expect(rbacData.roles).toContain("super_admin");
    expect(rbacData.permissions.length).toBeGreaterThan(10);
    expect(rbacData.permissions).toContain("admin.access");
    expect(rbacData.permissions).toContain("users.manage");
  });

  test("Customer should have limited permissions", async () => {
    const userId = await RBACTestUtils.createTestUser("<EMAIL>", "customer");

    const rbacData = await RBACService.getUserRBACData(userId);

    expect(rbacData.roles).toContain("customer");
    expect(rbacData.permissions).not.toContain("admin.access");
    expect(rbacData.permissions).not.toContain("users.manage");
  });
});
```

### E. Real-World Scenarios & Solutions

#### Scenario 1: Multi-Tenant Gym Management

**Business Requirements:**
- Gym A admin tidak boleh lihat data Gym B
- Instructor hanya bisa manage class di location yang assigned
- Customer hanya bisa book class di gym mereka

**Implementation:**

```typescript
// Extended RBAC dengan location restrictions
export interface LocationRestrictedRBAC {
  roles: string[];
  permissions: string[];
  accessibleLocations: string[]; // Location IDs yang bisa diakses
  tenantId: string;
}

// Location-aware permission check
export function hasLocationPermission(
  permission: string,
  locationId: string,
  rbacData: LocationRestrictedRBAC
): boolean {
  // 1. Check basic permission
  if (!rbacData.permissions.includes(permission)) return false;

  // 2. Check location access (kecuali super admin)
  if (!rbacData.roles.includes("super_admin")) {
    if (!rbacData.accessibleLocations.includes(locationId)) return false;
  }

  return true;
}

// Usage in API
export async function GET(req: NextRequest) {
  const { locationId } = await req.json();
  const rbacData = await getRBACFromToken(req);

  if (!hasLocationPermission("classes.read", locationId, rbacData)) {
    return NextResponse.json({ error: "Access denied to this location" }, { status: 403 });
  }

  // Proceed with location-specific data
}
```

#### Scenario 2: Temporary Role Assignment

**Business Case:** Instructor sakit, perlu temporary admin access untuk hari ini

```typescript
// Extended user_roles dengan expiry
interface TemporaryRole {
  userId: string;
  roleId: string;
  expiresAt: Date;
  grantedBy: string;
  reason: string;
}

// Check role dengan expiry
export async function getUserActiveRoles(userId: string) {
  const roles = await db
    .select()
    .from(userRoles)
    .innerJoin(roles, eq(userRoles.roleId, roles.id))
    .where(
      and(
        eq(userRoles.userId, userId),
        or(
          isNull(userRoles.expiresAt),
          gt(userRoles.expiresAt, new Date())
        )
      )
    );

  return roles;
}
```

#### Scenario 3: Audit Trail Implementation

**Requirement:** Track semua RBAC changes untuk compliance

```typescript
// Audit log table
CREATE TABLE rbac_audit_log (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  action VARCHAR(50) NOT NULL, -- 'ROLE_ASSIGNED', 'PERMISSION_GRANTED', etc
  target_user_id UUID REFERENCES users(id),
  target_role_id UUID REFERENCES roles(id),
  target_permission_id UUID REFERENCES permissions(id),
  performed_by UUID REFERENCES users(id),
  old_value JSONB,
  new_value JSONB,
  reason TEXT,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

// Audit service
export class RBACAuditService {
  static async logRoleAssignment(
    targetUserId: string,
    roleId: string,
    performedBy: string,
    reason: string,
    req: NextRequest
  ) {
    await db.insert(rbacAuditLog).values({
      id: generateId(),
      action: "ROLE_ASSIGNED",
      targetUserId,
      targetRoleId: roleId,
      performedBy,
      reason,
      ipAddress: req.ip,
      userAgent: req.headers.get("user-agent"),
    });
  }
}
```

### F. Production Deployment Checklist

#### Pre-Deployment
- [ ] **Database Backup**: Full backup sebelum migration
- [ ] **Environment Variables**: Semua env vars configured
- [ ] **SSL Certificates**: HTTPS enabled untuk production
- [ ] **Rate Limiting**: API rate limits configured
- [ ] **Monitoring**: Error tracking dan performance monitoring setup

#### Security Hardening
```typescript
// Production security config
export const productionAuthConfig = {
  ...authConfig,
  cookies: {
    sessionToken: {
      name: `__Secure-next-auth.session-token`,
      options: {
        httpOnly: true,
        sameSite: 'strict',
        path: '/',
        secure: true, // HTTPS only
        domain: process.env.COOKIE_DOMAIN,
      },
    },
  },
  session: {
    strategy: "jwt",
    maxAge: 24 * 60 * 60, // 24 hours
    updateAge: 60 * 60, // 1 hour
  },
};

// Rate limiting middleware
import rateLimit from 'express-rate-limit';

const authRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 attempts per window
  message: 'Too many authentication attempts',
  standardHeaders: true,
  legacyHeaders: false,
});
```

#### Monitoring & Alerting
```typescript
// RBAC metrics untuk monitoring
export const RBACMetrics = {
  // Track failed authorization attempts
  trackFailedAuth: (userId: string, permission: string, resource: string) => {
    metrics.increment('rbac.auth.failed', {
      userId,
      permission,
      resource,
    });
  },

  // Track permission checks
  trackPermissionCheck: (permission: string, granted: boolean) => {
    metrics.increment('rbac.permission.check', {
      permission,
      granted: granted.toString(),
    });
  },

  // Track role assignments
  trackRoleAssignment: (roleId: string, assignedBy: string) => {
    metrics.increment('rbac.role.assigned', {
      roleId,
      assignedBy,
    });
  },
};

// Alert rules (example for DataDog/New Relic)
const alertRules = {
  // Alert jika banyak failed auth attempts
  failedAuthSpike: {
    metric: 'rbac.auth.failed',
    threshold: 10,
    timeWindow: '5m',
    action: 'notify_security_team',
  },

  // Alert jika ada role assignment di luar jam kerja
  suspiciousRoleAssignment: {
    metric: 'rbac.role.assigned',
    condition: 'outside_business_hours',
    action: 'notify_admin',
  },
};
```

### G. Migration Guide dari Sistem Lama

#### Dari Simple Role Check ke RBAC

**Before (Simple):**
```typescript
// Old way - hardcoded role checks
if (user.role === 'admin') {
  // Show admin features
}
```

**After (RBAC):**
```typescript
// New way - permission-based
const { hasPermission } = useRBAC();
if (hasPermission('admin.access')) {
  // Show admin features
}
```

#### Migration Script
```typescript
export async function migrateFromSimpleRoles() {
  console.log("🔄 Migrating from simple roles to RBAC...");

  // 1. Map old roles to new roles
  const roleMapping = {
    'admin': 'super_admin',
    'manager': 'tenant_admin',
    'staff': 'instructor',
    'user': 'customer',
  };

  // 2. Get all users with old role system
  const usersToMigrate = await db
    .select()
    .from(users)
    .where(isNotNull(users.oldRole)); // assuming old role field exists

  // 3. Create user_roles entries
  for (const user of usersToMigrate) {
    const newRoleName = roleMapping[user.oldRole];
    if (newRoleName) {
      const role = await db.select().from(roles).where(eq(roles.name, newRoleName));
      if (role.length > 0) {
        await db.insert(userRoles).values({
          id: generateId(),
          userId: user.id,
          roleId: role[0].id,
        });
      }
    }
  }

  // 4. Remove old role column (after verification)
  // ALTER TABLE users DROP COLUMN old_role;

  console.log("✅ Migration completed!");
}
```

### H. FAQ & Common Questions

#### Q: Apakah RBAC ini scalable untuk 10,000+ users?
**A:** Ya! Dengan proper indexing dan caching:
- Database indexes pada junction tables
- Redis caching untuk RBAC data
- Pagination untuk role management UI
- Background jobs untuk bulk operations

#### Q: Bagaimana handle permission inheritance?
**A:** Implement role hierarchy:
```typescript
// Role hierarchy: super_admin > tenant_admin > instructor > customer
const roleHierarchy = {
  'super_admin': ['tenant_admin', 'instructor', 'customer'],
  'tenant_admin': ['instructor', 'customer'],
  'instructor': ['customer'],
  'customer': [],
};

function getInheritedPermissions(userRoles: string[]) {
  const allRoles = new Set(userRoles);

  for (const role of userRoles) {
    const inheritedRoles = roleHierarchy[role] || [];
    inheritedRoles.forEach(r => allRoles.add(r));
  }

  return Array.from(allRoles);
}
```

#### Q: Bagaimana handle dynamic permissions?
**A:** Gunakan context-aware permissions:
```typescript
// Dynamic permission dengan context
interface PermissionContext {
  resourceId?: string;
  tenantId?: string;
  locationId?: string;
}

function hasContextualPermission(
  permission: string,
  context: PermissionContext,
  rbacData: RBACData
): boolean {
  // Base permission check
  if (!rbacData.permissions.includes(permission)) return false;

  // Context-specific checks
  if (context.tenantId && rbacData.tenantId !== context.tenantId) return false;
  if (context.locationId && !rbacData.accessibleLocations.includes(context.locationId)) return false;

  return true;
}
```

#### Q: Performance impact dari RBAC checks?
**A:** Minimal dengan optimization:
- JWT contains RBAC data (no DB calls per request)
- Frontend caching dengan React Query
- Database indexes untuk fast lookups
- Typical overhead: <5ms per request

---

## 🎓 Learning Resources

### 📚 Recommended Reading
1. **"RBAC: Role-Based Access Control"** - NIST Standard
2. **"OAuth 2.0 Security Best Practices"** - IETF RFC
3. **"Next.js Authentication Patterns"** - Vercel Docs

### 🎥 Video Tutorials (Sandika Galih Style)
1. **"Memahami RBAC dari Nol"** - Konsep fundamental
2. **"Implementasi RBAC di Next.js"** - Step-by-step tutorial
3. **"Security Best Practices"** - Production-ready tips

### 🛠️ Tools & Extensions
- **VS Code Extensions**:
  - Thunder Client (API testing)
  - Database Client (PostgreSQL management)
  - GitLens (Code history tracking)
- **Browser Extensions**:
  - React Developer Tools
  - Redux DevTools (untuk state debugging)

---

## 🤝 Contributing & Support

### 🐛 Bug Reports
Jika menemukan bug atau issue:
1. Check existing issues di GitHub
2. Buat detailed bug report dengan:
   - Steps to reproduce
   - Expected vs actual behavior
   - Environment details
   - Code snippets

### 💡 Feature Requests
Untuk request fitur baru:
1. Diskusikan di GitHub Discussions
2. Jelaskan use case dan business value
3. Provide mockups atau examples jika ada

### 📞 Getting Help
- **GitHub Issues**: Technical problems
- **Discord Community**: Real-time chat
- **Stack Overflow**: Tag dengan `nextjs-rbac`

---

## 📄 License & Credits

**License:** MIT License - bebas digunakan untuk project komersial dan open source

**Credits:**
- NextAuth.js team untuk authentication framework
- Drizzle ORM team untuk type-safe database operations
- Vercel team untuk Next.js platform
- Community contributors yang telah membantu improve dokumentasi ini

**Special Thanks:**
- Sandika Galih untuk inspirasi teaching style
- Indonesian developer community untuk feedback dan testing

---

*Dokumentasi ini dibuat dengan ❤️ menggunakan gaya mengajar Sandika Galih untuk membantu developer Indonesia membangun aplikasi yang aman dan scalable.*

**Last Updated:** 2025-01-05
**Version:** 1.0.0
**Maintainer:** [Your Name/Team]
