"use client";

import { useVirtualizer } from "@tanstack/react-virtual";
import { useRef, useMemo, useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { MapPin, Search, Check, Clock, Edit, Trash2 } from "lucide-react";
import { Address } from "@/lib/db/schema";
import { useInfiniteAddresses } from "@/lib/hooks/queries/use-address-queries";

interface VirtualAddressListProps {
  tenantId: number | null;
  searchQuery?: string;
  onEdit?: (address: Address) => void;
  onDelete?: (address: Address) => void;
  onAccept?: (address: Address) => void;
}

export function VirtualAddressList({
  tenantId,
  searchQuery = "",
  onEdit,
  onDelete,
  onAccept,
}: VirtualAddressListProps) {
  const parentRef = useRef<HTMLDivElement>(null);

  // Use infinite query for large datasets
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
    isLoading,
    error,
  } = useInfiniteAddresses(tenantId, {
    search: searchQuery || undefined,
  });

  // Flatten all pages into a single array
  const allAddresses = useMemo(() => {
    return data?.pages?.flatMap((page) => page.addresses) ?? [];
  }, [data]);

  // Filter addresses based on search query (client-side filtering for demo)
  const filteredAddresses = useMemo(() => {
    if (!searchQuery) return allAddresses;
    
    return allAddresses.filter((address) => {
      const searchText = [
        address.addressLine1,
        address.addressLine2,
        address.city,
        address.state,
        address.country,
        address.postalCode,
      ]
        .filter(Boolean)
        .join(" ")
        .toLowerCase();
      
      return searchText.includes(searchQuery.toLowerCase());
    });
  }, [allAddresses, searchQuery]);

  // Create virtualizer
  const virtualizer = useVirtualizer({
    count: hasNextPage ? filteredAddresses.length + 1 : filteredAddresses.length,
    getScrollElement: () => parentRef.current,
    estimateSize: () => 120, // Estimated height of each item
    overscan: 5, // Render 5 extra items outside of the visible area
  });

  // Format address for display
  const formatAddress = (address: Address) => {
    const parts = [
      address.addressLine1,
      address.addressLine2,
      address.city,
      address.state,
      address.postalCode,
      address.country,
    ].filter(Boolean);
    
    return parts.join(", ");
  };

  if (error) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <div className="text-destructive">Error loading addresses: {error.message}</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MapPin className="h-5 w-5" />
          Virtual Address List
        </CardTitle>
        <CardDescription>
          Efficiently rendering {filteredAddresses.length} addresses using virtualization
        </CardDescription>
      </CardHeader>
      <CardContent>
        {/* Virtual List Container */}
        <div
          ref={parentRef}
          className="h-[600px] overflow-auto border rounded-lg"
          style={{
            contain: "strict",
          }}
        >
          <div
            style={{
              height: `${virtualizer.getTotalSize()}px`,
              width: "100%",
              position: "relative",
            }}
          >
            {isLoading ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-muted-foreground">Loading addresses...</div>
              </div>
            ) : (
              virtualizer.getVirtualItems().map((virtualItem) => {
                const isLoaderRow = virtualItem.index > filteredAddresses.length - 1;
                const address = filteredAddresses[virtualItem.index];

                return (
                  <div
                    key={virtualItem.index}
                    style={{
                      position: "absolute",
                      top: 0,
                      left: 0,
                      width: "100%",
                      height: `${virtualItem.size}px`,
                      transform: `translateY(${virtualItem.start}px)`,
                    }}
                  >
                    {isLoaderRow ? (
                      hasNextPage ? (
                        <div className="flex items-center justify-center h-full">
                          <Button
                            variant="outline"
                            onClick={() => fetchNextPage()}
                            disabled={isFetchingNextPage}
                          >
                            {isFetchingNextPage ? "Loading..." : "Load More"}
                          </Button>
                        </div>
                      ) : (
                        <div className="flex items-center justify-center h-full text-muted-foreground">
                          No more addresses to load
                        </div>
                      )
                    ) : (
                      <AddressItem
                        address={address}
                        onEdit={onEdit}
                        onDelete={onDelete}
                        onAccept={onAccept}
                      />
                    )}
                  </div>
                );
              })
            )}
          </div>
        </div>

        {/* Stats */}
        <div className="mt-4 flex items-center justify-between text-sm text-muted-foreground">
          <div>
            Showing {filteredAddresses.length} addresses
            {searchQuery && ` matching "${searchQuery}"`}
          </div>
          <div>
            Virtual items rendered: {virtualizer.getVirtualItems().length}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

// Individual address item component
interface AddressItemProps {
  address: Address;
  onEdit?: (address: Address) => void;
  onDelete?: (address: Address) => void;
  onAccept?: (address: Address) => void;
}

function AddressItem({ address, onEdit, onDelete, onAccept }: AddressItemProps) {
  const formatAddress = (address: Address) => {
    const parts = [
      address.addressLine1,
      address.addressLine2,
      address.city,
      address.state,
      address.postalCode,
      address.country,
    ].filter(Boolean);
    
    return parts.join(", ");
  };

  return (
    <div className="flex items-center justify-between p-4 border-b hover:bg-muted/50 transition-colors">
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-3">
          <MapPin className="h-4 w-4 text-muted-foreground flex-shrink-0" />
          <div className="min-w-0 flex-1">
            <div className="font-medium truncate">
              {address.addressLine1 || "No address line 1"}
            </div>
            <div className="text-sm text-muted-foreground truncate">
              {formatAddress(address)}
            </div>
            <div className="flex items-center gap-2 mt-1">
              <Badge variant={address.acceptedAt ? "default" : "secondary"} className="text-xs">
                {address.acceptedAt ? (
                  <>
                    <Check className="h-3 w-3 mr-1" />
                    Accepted
                  </>
                ) : (
                  <>
                    <Clock className="h-3 w-3 mr-1" />
                    Pending
                  </>
                )}
              </Badge>
              <span className="text-xs text-muted-foreground">
                {new Date(address.createdAt).toLocaleDateString()}
              </span>
            </div>
          </div>
        </div>
      </div>

      <div className="flex items-center gap-2 ml-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onEdit?.(address)}
          className="h-8 w-8 p-0"
        >
          <Edit className="h-4 w-4" />
        </Button>

        {!address.acceptedAt && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onAccept?.(address)}
            className="h-8 w-8 p-0"
          >
            <Check className="h-4 w-4" />
          </Button>
        )}

        <Button
          variant="ghost"
          size="sm"
          onClick={() => onDelete?.(address)}
          className="h-8 w-8 p-0 text-destructive hover:text-destructive"
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}

// Search wrapper component
interface VirtualAddressListWithSearchProps extends Omit<VirtualAddressListProps, 'searchQuery'> {}

export function VirtualAddressListWithSearch(props: VirtualAddressListWithSearchProps) {
  const [searchQuery, setSearchQuery] = useState("");

  return (
    <div className="space-y-4">
      <div className="relative">
        <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search addresses..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10"
        />
      </div>
      
      <VirtualAddressList {...props} searchQuery={searchQuery} />
    </div>
  );
}
