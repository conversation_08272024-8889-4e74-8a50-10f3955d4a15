"use client";

import { useEffect, useState } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { 
  User, 
  Plus, 
  Building, 
  BarChart3,
  AlertCircle,
  CheckCircle,
  Eye,
  EyeOff,
  MapPin,
  DollarSign
} from "lucide-react";
import { CustomerForm } from "@/components/forms/customer-form";
import { CustomerList } from "@/components/customers/customer-list";
import { SuccessToast } from "@/components/ui/success-toast";
import { SuccessModal } from "@/components/ui/success-modal";
import { Confetti } from "@/components/ui/confetti";
import { Customer, NewCustomer } from "@/lib/db/schema";
import {
  useCustomers,
  useCreateCustomer,
  useUpdateCustomer,
  useCustomerStats,
  useCustomer
} from "@/lib/hooks/queries/use-customer-queries";
import { useTenants } from "@/lib/hooks/queries/use-tenant-queries";

export default function CustomersPage() {
  const searchParams = useSearchParams();
  const router = useRouter();

  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingCustomer, setEditingCustomer] = useState<Customer | null>(null);
  const [selectedTenantId, setSelectedTenantId] = useState<number | null>(null);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [submitSuccess, setSubmitSuccess] = useState<string | null>(null);

  // Animation states
  const [showSuccessToast, setShowSuccessToast] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showConfetti, setShowConfetti] = useState(false);

  // Debug editingCustomer state changes
  // Berikut isi dari dari form package harganya, ada 3 step untuk mengisi form ini, tidak dimunculkan sekaligus, jadi awalnya bagian detail dulu
  // bagian detail: ada form nama package, description, list package kategorynya(polih dari list), location access for customer(data dari location(outlet) dari tenant admin, tmapilannya list data), lallu ada date validiti packagenya dengan 2 pilihan(Date of purchase dan Date of first booking)
  // bagian pricing: ada form pricing_group(jadi ambil dari table pricing_group yang sudah ada), lalu ada form creditnya ada berapa(dalam bentuk number)
  // bagian options: seperti digambar 
  // bagian classsed to include: ini adalah list dari clas yang dapat diakses dari package ini

  useEffect(() => {
    console.log('🔍 editingCustomer state changed:', editingCustomer);
  }, [editingCustomer]);

  const [createdCustomer, setCreatedCustomer] = useState<Customer | null>(null);

  const { data: customers = [], isLoading: customersLoading } = useCustomers();
  const { data: tenantsData, isLoading: tenantsLoading } = useTenants();
  const { data: stats, isLoading: statsLoading } = useCustomerStats();
  const tenants = tenantsData?.tenants || [];
  const createCustomerMutation = useCreateCustomer();
  const updateCustomerMutation = useUpdateCustomer();

  // Handle URL parameter for edit
  useEffect(() => {
    const editId = searchParams.get('edit');
    console.log('🔍 [CustomerPage] URL edit parameter:', editId);
    console.log('🔍 [CustomerPage] Customers loaded:', customers.length);

    if (editId && customers.length > 0) {
      console.log('🔍 [CustomerPage] Looking for customer with ID:', editId);
      const customerToEdit = customers.find(c => c.id === editId);
      if (customerToEdit) {
        console.log('✅ [CustomerPage] Customer found for edit:', customerToEdit);
        console.log('🏠 [CustomerPage] Customer address fields:', {
          addressLine1: (customerToEdit as any).addressLine1,
          addressLine2: (customerToEdit as any).addressLine2,
          city: (customerToEdit as any).city,
          state: (customerToEdit as any).state,
          zip: (customerToEdit as any).zip,
          country: (customerToEdit as any).country,
        });
        setEditingCustomer(customerToEdit);
        // Remove edit parameter from URL
        const newUrl = new URL(window.location.href);
        newUrl.searchParams.delete('edit');
        router.replace(newUrl.pathname + newUrl.search);
      } else {
        console.log('❌ [CustomerPage] Customer not found for edit ID:', editId);
        console.log('🔍 [CustomerPage] Available customer IDs:', customers.map(c => c.id));
      }
    }
  }, [searchParams, customers, router]);

  // Calculate stats by tenant
  const statsByTenant = tenants.reduce((acc, tenant) => {
    const tenantCustomers = customers.filter(c => c.tenantId === tenant.id);
    acc[tenant.id] = {
      total: tenantCustomers.length,
      active: tenantCustomers.filter(c => c.isActive).length,
    };
    return acc;
  }, {} as Record<number, { total: number; active: number }>);

  const handleCreateCustomer = async (data: Omit<NewCustomer, "id" | "createdAt" | "updatedAt">) => {
    try {
      setSubmitError(null);
      const newCustomer = await createCustomerMutation.mutateAsync(data);
      
      setCreatedCustomer(newCustomer);
      setIsFormOpen(false);
      
      // Start celebration sequence
      setTimeout(() => {
        setShowConfetti(true);
        setShowSuccessToast(true);
        setShowSuccessModal(true);
      }, 300);
      
      setTimeout(() => setShowConfetti(false), 4000);
      
    } catch (error) {
      setSubmitError(error instanceof Error ? error.message : "Failed to create customer");
    }
  };

  const handleUpdateCustomer = async (data: Omit<NewCustomer, "id" | "createdAt" | "updatedAt">) => {
    if (!editingCustomer) return;

    try {
      setSubmitError(null);
      await updateCustomerMutation.mutateAsync({ 
        id: editingCustomer.id, 
        data: data
      });
      setSubmitSuccess("Customer updated successfully!");
      setEditingCustomer(null);
      setTimeout(() => setSubmitSuccess(null), 3000);
    } catch (error) {
      setSubmitError(error instanceof Error ? error.message : "Failed to update customer");
    }
  };

  const handleEdit = (customer: Customer) => {
    console.log('🔍 Customer to edit:', customer);
    setEditingCustomer(customer);
    console.log('🔍 editingCustomer state after set:', customer);
  };

  const handleAdd = () => {
    setEditingCustomer(null);
    setIsFormOpen(true);
  };

  const handleCloseForm = () => {
    setIsFormOpen(false);
    setEditingCustomer(null);
    setSubmitError(null);
  };

  const handleCloseSuccessAnimations = () => {
    setShowSuccessToast(false);
    setShowSuccessModal(false);
    setShowConfetti(false);
    setCreatedCustomer(null);
  };

  const handleViewCustomer = () => {
    handleCloseSuccessAnimations();
  };

  const handleAddAnother = () => {
    handleCloseSuccessAnimations();
    setIsFormOpen(true);
  };

  if (customersLoading || tenantsLoading || statsLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Customers</h1>
          <p className="text-gray-500 dark:text-gray-400">
            Manage customer information and settings
          </p>
        </div>
        <Button onClick={handleAdd}>
          <Plus className="h-4 w-4 mr-2" />
          Add Customer
        </Button>
      </div>

      {/* Success/Error Messages */}
      {submitSuccess && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>{submitSuccess}</AlertDescription>
        </Alert>
      )}

      {submitError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{submitError}</AlertDescription>
        </Alert>
      )}

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Customers</CardTitle>
            <User className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.total || 0}</div>
            <p className="text-xs text-muted-foreground">
              Across all tenants
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Customers</CardTitle>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.active || 0}</div>
            <p className="text-xs text-muted-foreground">
              Currently active
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Inactive Customers</CardTitle>
            <EyeOff className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.inactive || 0}</div>
            <p className="text-xs text-muted-foreground">
              Currently inactive
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Locations</CardTitle>
            <MapPin className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{Object.keys(stats?.byLocation || {}).length}</div>
            <p className="text-xs text-muted-foreground">
              With customers
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="all" className="space-y-4">
        <TabsList>
          <TabsTrigger value="all">All Customers</TabsTrigger>
          {tenants.map((tenant) => {
            const tenantStats = statsByTenant[tenant.id];
            return (
              <TabsTrigger key={tenant.id} value={`tenant-${tenant.id}`}>
                {tenant.name}
                <Badge variant="secondary" className="ml-2">
                  {tenantStats?.total || 0}
                </Badge>
              </TabsTrigger>
            );
          })}
        </TabsList>

        <TabsContent value="all">
          <CustomerList
            onEdit={handleEdit}
            onAdd={handleAdd}
            showActions={true}
          />
        </TabsContent>

        {tenants.map((tenant) => (
          <TabsContent key={tenant.id} value={`tenant-${tenant.id}`}>
            <CustomerList
              tenantId={tenant.id}
              onEdit={handleEdit}
              onAdd={() => {
                setSelectedTenantId(tenant.id);
                handleAdd();
              }}
              showActions={true}
            />
          </TabsContent>
        ))}
      </Tabs>

      {/* Create/Edit Dialog */}
      <Dialog open={isFormOpen || !!editingCustomer} onOpenChange={handleCloseForm}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editingCustomer ? "Edit Customer" : "Add New Customer"}
            </DialogTitle>
          </DialogHeader>
          {(isFormOpen || editingCustomer) && (
            <CustomerForm
              key={editingCustomer?.id || 'new'}
              entity={editingCustomer || undefined}
              tenantId={selectedTenantId || editingCustomer?.tenantId || (tenants[0]?.id ?? 1)}
              onSubmit={editingCustomer ? handleUpdateCustomer : handleCreateCustomer}
              onCancel={handleCloseForm}
              className="border-0 shadow-none"
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Success Animations */}
      <Confetti isActive={showConfetti} />
      
      <SuccessToast
        isVisible={showSuccessToast}
        onClose={() => setShowSuccessToast(false)}
        title="Customer Created Successfully!"
        description={createdCustomer ? 
          `${createdCustomer.firstName} ${createdCustomer.lastName || ''} has been added to your customers.`.trim() : 
          undefined
        }
        type="general"
        action={{
          label: "View All Customers",
          onClick: handleViewCustomer
        }}
      />

      <SuccessModal
        isOpen={showSuccessModal}
        onClose={handleCloseSuccessAnimations}
        title="🎉 Customer Created Successfully!"
        description="Your new customer has been saved and is now available in the system."
        locationData={createdCustomer ? {
          address: `${createdCustomer.firstName} ${createdCustomer.lastName || ''}`.trim(),
          city: createdCustomer.email,
          country: `Status: ${createdCustomer.isActive ? 'Active' : 'Inactive'}`
        } : undefined}
        actions={{
          primary: {
            label: "Add Another Customer",
            onClick: handleAddAnother
          },
          secondary: {
            label: "View All Customers",
            onClick: handleViewCustomer
          }
        }}
      />
    </div>
  );
}
