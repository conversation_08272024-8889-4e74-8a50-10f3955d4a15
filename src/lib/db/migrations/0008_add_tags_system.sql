-- Migration: Add Simple Tags System
-- Description: Create simple tags table for customer tagging

-- Drop existing tags table if it exists (since we're redesigning it)
DROP TABLE IF EXISTS tags CASCADE;

-- Create simple tags table
CREATE TABLE tags (
  id VARCHAR(255) PRIMARY KEY,
  tenant_id INTEGER NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
  customer_id TEXT REFERENCES customers(id) ON DELETE CASCADE,
  name VA<PERSON><PERSON><PERSON>(255),
  description VARCHAR(255),
  custom_color VARCHAR(255),
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL
);

-- Create indexes for tags table
CREATE INDEX tags_tenant_id_idx ON tags(tenant_id);
CREATE INDEX tags_customer_id_idx ON tags(customer_id);
CREATE INDEX tags_name_idx ON tags(name);

-- Create trigger to update updated_at timestamp for tags
CREATE OR REPLACE FUNCTION update_tags_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = CURRENT_TIMESTAMP;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER tags_updated_at_trigger
  BEFORE UPDATE ON tags
  FOR EACH ROW
  EXECUTE FUNCTION update_tags_updated_at();

-- Insert sample tags for testing (optional)
INSERT INTO tags (id, tenant_id, customer_id, name, description, custom_color) VALUES
  ('tag_sample_1', 1, NULL, 'Important', 'Important items', '#EF4444'),
  ('tag_sample_2', 1, NULL, 'Urgent', 'Urgent priority', '#F59E0B'),
  ('tag_sample_3', 1, NULL, 'VIP Customer', 'VIP customer tag', '#8B5CF6'),
  ('tag_sample_4', 1, NULL, 'New Customer', 'New customer tag', '#10B981');

-- Add comments for documentation
COMMENT ON TABLE tags IS 'Simple tagging system for customers and general use';
COMMENT ON COLUMN tags.customer_id IS 'Optional customer ID for customer-specific tags';
COMMENT ON COLUMN tags.custom_color IS 'Hex color code for UI display';
