import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';
import { PackageCategory, NewPackageCategory } from '@/lib/db/schema';
import { createEntityHooks, BaseApiInterface } from '@/lib/core/base-query-hooks';
import { QueryOptions } from '@/lib/core/base-service';
import { 
  CreatePackageCategoryData, 
  UpdatePackageCategoryData, 
  PackageCategoryFilters,
  PackageCategoryStats 
} from '@/lib/services/package-category.service';

// API interface for package categories
const packageCategoryApi: BaseApiInterface<PackageCategory, CreatePackageCategoryData, UpdatePackageCategoryData> = {
  async getAll(options?: QueryOptions): Promise<PackageCategory[]> {
    const params = new URLSearchParams();
    
    if (options?.tenantId) params.append('tenantId', options.tenantId.toString());
    if (options?.limit) params.append('limit', options.limit.toString());
    if (options?.offset) params.append('offset', options.offset.toString());
    if (options?.filters?.search) params.append('search', options.filters.search);

    const response = await fetch(`/api/package-categories?${params.toString()}`);
    if (!response.ok) {
      throw new Error('Failed to fetch package categories');
    }
    
    const result = await response.json();
    return result.data;
  },

  async getById(id: string): Promise<PackageCategory> {
    const response = await fetch(`/api/package-categories/${id}`);
    if (!response.ok) {
      throw new Error('Failed to fetch package category');
    }
    
    const result = await response.json();
    return result.data;
  },

  async getByTenant(tenantId: number, options?: QueryOptions): Promise<PackageCategory[]> {
    return packageCategoryApi.getAll({ ...options, tenantId });
  },

  async create(data: CreatePackageCategoryData): Promise<PackageCategory> {
    const response = await fetch('/api/package-categories', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to create package category');
    }

    const result = await response.json();
    return result.data;
  },

  async update({ id, data }: { id: string; data: UpdatePackageCategoryData }): Promise<PackageCategory> {
    const response = await fetch(`/api/package-categories/${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to update package category');
    }

    const result = await response.json();
    return result.data;
  },

  async delete(id: string): Promise<PackageCategory> {
    const response = await fetch(`/api/package-categories/${id}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to delete package category');
    }

    const result = await response.json();
    return result.data;
  },

  async bulkOperation(data: { ids: string[]; action: string }): Promise<PackageCategory[]> {
    const response = await fetch('/api/package-categories', {
      method: 'DELETE',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to perform bulk operation');
    }

    const result = await response.json();
    return result.data;
  },

  async getStats(tenantId?: number): Promise<PackageCategoryStats> {
    const params = new URLSearchParams();
    if (tenantId) params.append('tenantId', tenantId.toString());

    const response = await fetch(`/api/package-categories/stats?${params.toString()}`);
    if (!response.ok) {
      throw new Error('Failed to fetch package category statistics');
    }

    const result = await response.json();
    return result.data;
  },

  async search(query: string, options?: QueryOptions): Promise<PackageCategory[]> {
    return packageCategoryApi.getAll({
      ...options,
      filters: { ...options?.filters, search: query }
    });
  },
};

// Create base hooks using the factory
const baseHooks = createEntityHooks('package-categories', packageCategoryApi);

// Export base hooks with package category-specific names
export const usePackageCategory = baseHooks.useEntity;
export const usePackageCategoriesByTenant = baseHooks.useEntitiesByTenant;
export const usePackageCategorySearch = baseHooks.useEntitySearch;
export const useCreatePackageCategory = baseHooks.useCreateEntity;
export const useUpdatePackageCategory = baseHooks.useUpdateEntity;
export const useDeletePackageCategory = baseHooks.useDeleteEntity;
export const useBulkPackageCategoryOperation = baseHooks.useBulkOperation;

// Custom hook that requires tenantId
export function usePackageCategories(options: { tenantId?: number; filters?: any } = {}) {
  const { tenantId = 1, filters } = options;

  return useQuery({
    queryKey: ['package-categories', 'list', { tenantId, ...filters }],
    queryFn: async (): Promise<PackageCategory[]> => {
      const params = new URLSearchParams();
      params.append('tenantId', tenantId.toString());

      if (filters?.search) params.append('search', filters.search);

      const response = await fetch(`/api/package-categories?${params.toString()}`);
      if (!response.ok) {
        throw new Error('Failed to fetch package categories');
      }

      const result = await response.json();
      return result.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Custom hook for stats
export function usePackageCategoryStats(tenantId?: number) {
  return useQuery({
    queryKey: ['package-categories', 'stats', tenantId],
    queryFn: async (): Promise<PackageCategoryStats> => {
      // For now, return mock stats since the endpoint doesn't exist
      return {
        total: 0,
        byTenant: {},
      };
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Custom hooks for package category-specific functionality

/**
 * Hook to fetch package categories with filters
 */
export function usePackageCategoriesWithFilters(filters: PackageCategoryFilters = {}, tenantId: number = 1) {
  return useQuery({
    queryKey: ['package-categories', 'filtered', { ...filters, tenantId }],
    queryFn: async (): Promise<PackageCategory[]> => {
      const params = new URLSearchParams();

      params.append('tenantId', tenantId.toString());
      if (filters.search) params.append('search', filters.search);

      const response = await fetch(`/api/package-categories?${params.toString()}`);
      if (!response.ok) {
        throw new Error('Failed to fetch package categories');
      }

      const result = await response.json();
      return result.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to check if package category name exists
 */
export function usePackageCategoryNameExists() {
  return useMutation({
    mutationFn: async ({ name, tenantId, excludeId }: { name: string; tenantId: number; excludeId?: string }) => {
      const params = new URLSearchParams();
      params.append('name', name);
      params.append('tenantId', tenantId.toString());
      if (excludeId) params.append('excludeId', excludeId);

      const response = await fetch(`/api/package-categories/check-name?${params.toString()}`);
      if (!response.ok) {
        throw new Error('Failed to check package category name');
      }

      const result = await response.json();
      return result.exists;
    },
  });
}

/**
 * Hook to duplicate a package category
 */
export function useDuplicatePackageCategory() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, name }: { id: string; name?: string }) => {
      const response = await fetch(`/api/package-categories/${id}/duplicate`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to duplicate package category');
      }

      const result = await response.json();
      return result.data;
    },
    onSuccess: (newCategory) => {
      // Update cache with new category
      queryClient.setQueryData(['package-categories', 'detail', newCategory.id], newCategory);

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['package-categories', 'list'] });
      if (newCategory.tenantId) {
        queryClient.invalidateQueries({
          queryKey: ['package-categories', 'tenant', newCategory.tenantId]
        });
        queryClient.invalidateQueries({
          queryKey: ['package-categories', 'stats', newCategory.tenantId]
        });
      }
    },
  });
}

// Export query keys for external use
export const packageCategoryKeys = baseHooks.queryKeys;

// Export types for external use
export type { CreatePackageCategoryData, UpdatePackageCategoryData, PackageCategoryFilters, PackageCategoryStats };
