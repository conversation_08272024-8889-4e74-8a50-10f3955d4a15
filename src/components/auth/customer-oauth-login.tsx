"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";

import { Button } from "@/components/ui/button";

interface CustomerOAuthLoginProps {
  tenantId: number;
  redirectPath?: string;
  className?: string;
}

/**
 * Customer OAuth Login Component
 * 
 * Menggunakan sistem Customer OAuth yang baru (bukan NextAuth)
 * Mendukung PKCE untuk mobile dan web authentication
 */
export function CustomerOAuthLogin({ 
  tenantId, 
  redirectPath = "/dashboard",
  className = "w-full"
}: CustomerOAuthLoginProps) {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const generateDeviceId = (): string => {
    return `web-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  };

  const handleGoogleOAuth = async () => {
    setIsLoading(true);
    
    try {
      console.log('🚀 Initiating Customer Google OAuth flow...');
      
      // Initialize OAuth flow dengan schema yang benar
      const response = await fetch('/api/auth/customer/google/init', {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify({
          tenantId,
          clientType: 'web', // ← Menggunakan clientType (bukan deviceType)
          deviceId: generateDeviceId(),
          redirectUri: `${window.location.origin}/auth/customer/callback`
        })
      });

      console.log('📡 OAuth init response status:', response.status);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ OAuth init failed:', errorText);
        
        try {
          const errorData = JSON.parse(errorText);
          toast.error(errorData.error || 'Failed to initialize OAuth');
        } catch {
          toast.error('Failed to initialize OAuth');
        }
        return;
      }

      const data = await response.json();
      console.log('✅ OAuth init successful:', data);

      if (data.success && data.authUrl) {
        // Store state untuk validation di callback
        sessionStorage.setItem('oauth_state', data.state);
        sessionStorage.setItem('oauth_tenant_id', tenantId.toString());
        sessionStorage.setItem('oauth_redirect_path', redirectPath);
        
        console.log('🔄 Redirecting to Google OAuth...');
        // Redirect ke Google OAuth
        window.location.href = data.authUrl;
      } else {
        console.error('❌ Invalid OAuth response:', data);
        toast.error('Invalid OAuth response');
      }
    } catch (error) {
      console.error('❌ OAuth error:', error);
      toast.error('Failed to start OAuth flow');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button
      type="button"
      variant="outline"
      className={className}
      onClick={handleGoogleOAuth}
      disabled={isLoading}
    >
      {isLoading ? (
        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
      ) : (
        <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24">
          <path
            d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
            fill="#4285F4"
          />
          <path
            d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
            fill="#34A853"
          />
          <path
            d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
            fill="#FBBC05"
          />
          <path
            d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
            fill="#EA4335"
          />
        </svg>
      )}
      {isLoading ? 'Connecting...' : 'Continue with Google'}
    </Button>
  );
}

/**
 * Hook untuk menggunakan Customer OAuth
 */
export function useCustomerOAuth() {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const initiateOAuth = async (tenantId: number, clientType: 'web' | 'mobile' = 'web') => {
    setIsLoading(true);
    
    try {
      const response = await fetch('/api/auth/customer/google/init', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          tenantId,
          clientType, // ← Correct field name
          deviceId: `${clientType}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          redirectUri: `${window.location.origin}/auth/customer/callback`
        })
      });

      if (!response.ok) {
        throw new Error(`OAuth init failed: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.success && data.authUrl) {
        sessionStorage.setItem('oauth_state', data.state);
        sessionStorage.setItem('oauth_tenant_id', tenantId.toString());
        
        return {
          success: true,
          authUrl: data.authUrl,
          state: data.state
        };
      } else {
        throw new Error(data.error || 'OAuth initialization failed');
      }
    } catch (error) {
      console.error('OAuth error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    } finally {
      setIsLoading(false);
    }
  };

  return {
    initiateOAuth,
    isLoading
  };
}
