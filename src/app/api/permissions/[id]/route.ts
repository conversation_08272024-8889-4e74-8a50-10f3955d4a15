import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { PermissionService } from "@/lib/services/permission.service";
import { withRBAC } from "@/lib/middleware/rbac-middleware";

// Validation schemas
const updatePermissionSchema = z.object({
  module: z.string().min(1, "Module is required").max(100, "Module too long").optional(),
  action: z.string().min(1, "Action is required").max(100, "Action too long").optional(),
  display_name: z.string().min(1, "Display name is required").max(255, "Display name too long").optional(),
  description: z.string().max(255, "Description too long").optional(),
  is_system_permission: z.boolean().optional(),
});

/**
 * GET /api/permissions/[id]
 * Get permission by ID
 */
export const GET = withRBAC(
  async (request: NextRequest, { params }: { params: { id: string } }) => {
    try {
      const permission = await PermissionService.getById(params.id);

      if (!permission) {
        return NextResponse.json(
          { success: false, error: "Permission not found" },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        data: permission,
      });
    } catch (error) {
      console.error("GET /api/permissions/[id] error:", error);
      return NextResponse.json(
        { success: false, error: "Internal server error" },
        { status: 500 }
      );
    }
  },
  { module: "permissions", action: "read" }
);

/**
 * PUT /api/permissions/[id]
 * Update permission
 */
export const PUT = withRBAC(
  async (request: NextRequest, { params }: { params: { id: string } }) => {
    try {
      // Parse dan validate request body
      const body = await request.json();
      const validatedData = updatePermissionSchema.parse(body);

      // Check if permission exists
      const existingPermission = await PermissionService.getById(params.id);
      if (!existingPermission) {
        return NextResponse.json(
          { success: false, error: "Permission not found" },
          { status: 404 }
        );
      }

      // Check if trying to update module/action and it conflicts with existing
      if (validatedData.module || validatedData.action) {
        const module = validatedData.module || existingPermission.module;
        const action = validatedData.action || existingPermission.action;
        
        const conflictingPermission = await PermissionService.getByModuleAndAction(module, action);
        if (conflictingPermission && conflictingPermission.id !== params.id) {
          return NextResponse.json(
            { 
              success: false, 
              error: `Permission ${module}.${action} already exists` 
            },
            { status: 409 }
          );
        }
      }

      // Update permission
      const updatedPermission = await PermissionService.update(params.id, validatedData);

      return NextResponse.json({
        success: true,
        data: updatedPermission,
      });
    } catch (error: any) {
      console.error("PUT /api/permissions/[id] error:", error);
      
      if (error.name === "ZodError") {
        return NextResponse.json(
          { success: false, error: "Validation failed", details: error.errors },
          { status: 400 }
        );
      }

      return NextResponse.json(
        { success: false, error: "Internal server error" },
        { status: 500 }
      );
    }
  },
  { module: "permissions", action: "update" }
);

/**
 * DELETE /api/permissions/[id]
 * Delete permission
 */
export const DELETE = withRBAC(
  async (request: NextRequest, { params }: { params: { id: string } }) => {
    try {
      // Check if permission exists
      const existingPermission = await PermissionService.getById(params.id);
      if (!existingPermission) {
        return NextResponse.json(
          { success: false, error: "Permission not found" },
          { status: 404 }
        );
      }

      // Prevent deletion of system permissions
      if (existingPermission.is_system_permission) {
        return NextResponse.json(
          { success: false, error: "Cannot delete system permissions" },
          { status: 403 }
        );
      }

      // Delete permission
      await PermissionService.delete(params.id);

      return NextResponse.json({
        success: true,
        message: "Permission deleted successfully",
      });
    } catch (error) {
      console.error("DELETE /api/permissions/[id] error:", error);
      return NextResponse.json(
        { success: false, error: "Internal server error" },
        { status: 500 }
      );
    }
  },
  { module: "permissions", action: "delete" }
);
