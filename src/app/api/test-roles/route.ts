import { NextRequest, NextResponse } from "next/server";
import { RoleService } from "@/lib/services/role.service";

// Test endpoint without authentication to verify role system
export async function GET(request: NextRequest) {
  try {
    console.log("🧪 Testing role system...");
    
    // Test role search
    const result = await RoleService.searchRoles(null, undefined, undefined, 10, 0);
    
    console.log("✅ Role search successful:", {
      totalRoles: result.roles.length,
      hasMore: result.hasMore,
      roles: result.roles.map(r => ({ id: r.id, name: r.name, display_name: r.display_name }))
    });

    return NextResponse.json({
      success: true,
      message: "Role system is working!",
      data: {
        totalRoles: result.roles.length,
        hasMore: result.hasMore,
        roles: result.roles.map(r => ({
          id: r.id,
          name: r.name,
          display_name: r.display_name,
          is_system_role: r.is_system_role,
          hierarchy_level: r.hierarchy_level
        }))
      }
    });
  } catch (error) {
    console.error("❌ Role system test failed:", error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}
