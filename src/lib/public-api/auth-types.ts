/**
 * Authentication Types for Public API
 * 
 * Comprehensive authentication and authorization type definitions
 */

// ============================================================================
// Authentication Method Types
// ============================================================================

export type AuthMethod = 'api-key' | 'jwt' | 'oauth2' | 'mtls';

export interface AuthConfig {
  method: AuthMethod;
  apiKey?: APIKeyConfig;
  jwt?: JWTConfig;
  oauth2?: OAuth2Config;
  mtls?: MTLSConfig;
}

// ============================================================================
// API Key Authentication
// ============================================================================

export interface APIKeyConfig {
  headerName: string; // e.g., 'X-API-Key'
  keyPrefix?: string; // e.g., 'pk_'
  keyLength: number;
  hashAlgorithm: 'sha256' | 'sha512';
  rotationPolicy: {
    enabled: boolean;
    intervalDays: number;
    gracePeriodDays: number;
  };
}

export interface APIKeyValidationResult {
  isValid: boolean;
  apiKey?: APIKey;
  error?: string;
  rateLimitStatus?: {
    remaining: number;
    resetTime: Date;
    isBlocked: boolean;
  };
}

// ============================================================================
// JWT Authentication
// ============================================================================

export interface JWTConfig {
  algorithm: 'HS256' | 'HS512' | 'RS256' | 'RS512';
  secret?: string; // For HMAC algorithms
  publicKey?: string; // For RSA algorithms
  privateKey?: string; // For RSA algorithms
  expiresIn: string; // e.g., '1h', '7d'
  issuer: string;
  audience: string;
  clockTolerance: number; // seconds
}

export interface JWTPayload {
  sub: string; // Subject (user ID)
  iss: string; // Issuer
  aud: string; // Audience
  exp: number; // Expiration time
  iat: number; // Issued at
  jti: string; // JWT ID
  tenantId: number;
  permissions: APIPermission[];
  scope: string[];
}

export interface JWTValidationResult {
  isValid: boolean;
  payload?: JWTPayload;
  error?: string;
  isExpired?: boolean;
  isRevoked?: boolean;
}

// ============================================================================
// OAuth 2.0 Authentication
// ============================================================================

export interface OAuth2Config {
  authorizationUrl: string;
  tokenUrl: string;
  clientId: string;
  clientSecret: string;
  scopes: string[];
  redirectUri: string;
  pkce: boolean; // Proof Key for Code Exchange
}

export interface OAuth2Token {
  accessToken: string;
  tokenType: string;
  expiresIn: number;
  refreshToken?: string;
  scope: string;
  issuedAt: number;
}

export interface OAuth2ValidationResult {
  isValid: boolean;
  token?: OAuth2Token;
  userInfo?: {
    sub: string;
    email?: string;
    name?: string;
    tenantId?: number;
  };
  error?: string;
}

// ============================================================================
// mTLS Authentication
// ============================================================================

export interface MTLSConfig {
  caCertificate: string;
  requireClientCert: boolean;
  verifyClientCert: boolean;
  allowedCertificates?: string[]; // Certificate fingerprints
  certificateValidation: {
    checkExpiration: boolean;
    checkRevocation: boolean;
    allowSelfSigned: boolean;
  };
}

export interface ClientCertificate {
  subject: string;
  issuer: string;
  serialNumber: string;
  fingerprint: string;
  validFrom: Date;
  validTo: Date;
  isValid: boolean;
}

export interface MTLSValidationResult {
  isValid: boolean;
  certificate?: ClientCertificate;
  error?: string;
  tenantId?: number;
}

// ============================================================================
// Multi-Factor Authentication
// ============================================================================

export interface MFAConfig {
  enabled: boolean;
  methods: MFAMethod[];
  gracePeriodDays: number;
  backupCodes: {
    enabled: boolean;
    count: number;
    length: number;
  };
}

export type MFAMethod = 'totp' | 'sms' | 'email' | 'hardware-key';

export interface MFAChallenge {
  id: string;
  method: MFAMethod;
  expiresAt: Date;
  attempts: number;
  maxAttempts: number;
}

export interface MFAValidationResult {
  isValid: boolean;
  challenge?: MFAChallenge;
  error?: string;
  remainingAttempts?: number;
}

// ============================================================================
// Session Management
// ============================================================================

export interface SessionConfig {
  cookieName: string;
  maxAge: number; // seconds
  secure: boolean;
  httpOnly: boolean;
  sameSite: 'strict' | 'lax' | 'none';
  domain?: string;
  path: string;
}

export interface Session {
  id: string;
  userId: string;
  tenantId: number;
  apiKeyId?: string;
  permissions: APIPermission[];
  createdAt: Date;
  expiresAt: Date;
  lastAccessedAt: Date;
  ipAddress: string;
  userAgent: string;
  isActive: boolean;
}

// ============================================================================
// Authorization Types
// ============================================================================

export interface AuthorizationContext {
  userId?: string;
  tenantId: number;
  apiKeyId?: string;
  permissions: APIPermission[];
  session?: Session;
  ipAddress: string;
  userAgent: string;
  requestId: string;
}

export interface PermissionCheck {
  resource: string;
  action: string;
  conditions?: Record<string, any>;
}

export interface AuthorizationResult {
  isAuthorized: boolean;
  missingPermissions?: PermissionCheck[];
  error?: string;
  context?: AuthorizationContext;
}

// ============================================================================
// Security Headers
// ============================================================================

export interface SecurityHeaders {
  'X-Content-Type-Options': 'nosniff';
  'X-Frame-Options': 'DENY' | 'SAMEORIGIN';
  'X-XSS-Protection': '1; mode=block';
  'Strict-Transport-Security': string;
  'Content-Security-Policy': string;
  'Referrer-Policy': 'no-referrer' | 'strict-origin-when-cross-origin';
  'Permissions-Policy': string;
}

// ============================================================================
// Rate Limiting
// ============================================================================

export interface RateLimitRule {
  id: string;
  name: string;
  windowMs: number; // Time window in milliseconds
  maxRequests: number; // Max requests per window
  keyGenerator: (req: any) => string; // Function to generate rate limit key
  skipIf?: (req: any) => boolean; // Function to skip rate limiting
  onLimitReached?: (req: any, res: any) => void;
}

export interface RateLimitStatus {
  limit: number;
  remaining: number;
  resetTime: Date;
  retryAfter?: number; // seconds
}

// ============================================================================
// Audit and Compliance
// ============================================================================

export interface ComplianceConfig {
  gdpr: {
    enabled: boolean;
    dataRetentionDays: number;
    anonymizeAfterDays: number;
  };
  hipaa: {
    enabled: boolean;
    auditLevel: 'basic' | 'detailed';
    encryptionRequired: boolean;
  };
  sox: {
    enabled: boolean;
    auditRetentionYears: number;
    segregationOfDuties: boolean;
  };
}

export interface DataClassification {
  level: 'public' | 'internal' | 'confidential' | 'restricted';
  categories: string[]; // e.g., ['pii', 'financial', 'health']
  retention: {
    days: number;
    deleteAfter: boolean;
    archiveAfter?: number;
  };
}

// ============================================================================
// Error Types
// ============================================================================

export interface AuthError extends Error {
  code: string;
  statusCode: number;
  details?: Record<string, any>;
  retryable: boolean;
}

export class APIKeyError extends Error implements AuthError {
  code: string;
  statusCode: number;
  retryable: boolean;

  constructor(message: string, code: string, statusCode: number = 401, retryable: boolean = false) {
    super(message);
    this.name = 'APIKeyError';
    this.code = code;
    this.statusCode = statusCode;
    this.retryable = retryable;
  }
}

export class JWTError extends Error implements AuthError {
  code: string;
  statusCode: number;
  retryable: boolean;

  constructor(message: string, code: string, statusCode: number = 401, retryable: boolean = false) {
    super(message);
    this.name = 'JWTError';
    this.code = code;
    this.statusCode = statusCode;
    this.retryable = retryable;
  }
}

export class RateLimitError extends Error implements AuthError {
  code: string;
  statusCode: number;
  retryable: boolean;
  retryAfter: number;

  constructor(message: string, retryAfter: number) {
    super(message);
    this.name = 'RateLimitError';
    this.code = 'RATE_LIMIT_EXCEEDED';
    this.statusCode = 429;
    this.retryable = true;
    this.retryAfter = retryAfter;
  }
}
