import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { z } from "zod";
import { ClassLevelService } from "@/lib/services/class-levels.service";

// Validation schema for updating class level
const updateClassLevelSchema = z.object({
  tenantId: z.number().int().positive().optional(),
  name: z.string().min(1).max(255).optional(),
  description: z.string().max(500).optional(),
  sortOrder: z.number().int().min(0).optional(),
  isActive: z.boolean().optional(),
});

// PUT /api/class-levels/[id] - Update class level
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id: classLevelId } = await params;
    if (!classLevelId) {
      return NextResponse.json(
        { error: "Class level ID is required" },
        { status: 400 }
      );
    }

    const body = await request.json();
    const validatedData = updateClassLevelSchema.parse(body);

    const updatedClassLevel = await ClassLevelService.update(classLevelId, validatedData);

    return NextResponse.json(
      { 
        message: "Class level updated successfully", 
        data: updatedClassLevel 
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error updating class level:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid data", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to update class level" },
      { status: 500 }
    );
  }
}

// DELETE /api/class-levels/[id] - Delete class level
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id: classLevelId } = await params;
    if (!classLevelId) {
      return NextResponse.json(
        { error: "Class level ID is required" },
        { status: 400 }
      );
    }

    const deletedClassLevel = await ClassLevelService.delete(classLevelId);

    return NextResponse.json(
      {
        message: "Class level deleted successfully",
        data: deletedClassLevel
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error deleting class level:", error);
    return NextResponse.json(
      { error: "Failed to delete class level" },
      { status: 500 }
    );
  }
}

// GET /api/class-levels/[id] - Get single class level
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id: classLevelId } = await params;
    if (!classLevelId) {
      return NextResponse.json(
        { error: "Class level ID is required" },
        { status: 400 }
      );
    }

    const classLevel = await ClassLevelService.getById(classLevelId);

    if (!classLevel) {
      return NextResponse.json(
        { error: "Class level not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { data: classLevel },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error fetching class level:", error);
    return NextResponse.json(
      { error: "Failed to fetch class level" },
      { status: 500 }
    );
  }
}

// PATCH /api/class-levels/[id] - Toggle active status
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id: classLevelId } = await params;
    if (!classLevelId) {
      return NextResponse.json(
        { error: "Class level ID is required" },
        { status: 400 }
      );
    }

    const updatedClassLevel = await ClassLevelService.toggleActive(classLevelId);

    return NextResponse.json(
      {
        message: `Class level ${updatedClassLevel.isActive ? 'activated' : 'deactivated'} successfully`,
        data: updatedClassLevel
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error toggling class level status:", error);
    return NextResponse.json(
      { error: "Failed to toggle class level status" },
      { status: 500 }
    );
  }
}
