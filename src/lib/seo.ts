import { Metadata, Viewport } from "next";
import { getAppUrl } from "./env";

interface SEOProps {
  title?: string;
  description?: string;
  keywords?: string[];
  image?: string;
  url?: string;
  type?: "website" | "article" | "profile";
  publishedTime?: string;
  modifiedTime?: string;
  authors?: string[];
  section?: string;
  tags?: string[];
  noIndex?: boolean;
  canonical?: string;
}

const defaultMetadata = {
  title: "Your SaaS App - Modern SaaS Template",
  description: "A comprehensive Next.js 15 SaaS template with authentication, billing, multi-tenancy, and everything you need to launch your SaaS product.",
  keywords: [
    "SaaS",
    "Next.js",
    "React",
    "TypeScript",
    "Tailwind CSS",
    "Authentication",
    "Billing",
    "Multi-tenancy",
    "Stripe",
    "Cloudflare",
    "Template",
    "Starter",
  ],
  image: "/og-image.png",
  type: "website" as const,
};

export function generateMetadata({
  title,
  description,
  keywords = [],
  image,
  url,
  type = "website",
  publishedTime,
  modifiedTime,
  authors,
  section,
  tags,
  noIndex = false,
  canonical,
}: SEOProps = {}): Metadata {
  const appUrl = getAppUrl();
  const fullTitle = title 
    ? `${title} | Your SaaS App`
    : defaultMetadata.title;
  
  const metaDescription = description || defaultMetadata.description;
  const metaKeywords = [...defaultMetadata.keywords, ...keywords];
  const metaImage = image ? `${appUrl}${image}` : `${appUrl}${defaultMetadata.image}`;
  const metaUrl = url ? `${appUrl}${url}` : appUrl;

  const metadata: Metadata = {
    title: fullTitle,
    description: metaDescription,
    keywords: metaKeywords,
    authors: authors?.map(name => ({ name })) || [{ name: "Your SaaS App Team" }],
    creator: "Your SaaS App",
    publisher: "Your SaaS App",
    robots: noIndex 
      ? { index: false, follow: false }
      : { index: true, follow: true, googleBot: { index: true, follow: true } },
    
    // Open Graph
    openGraph: {
      type,
      locale: "en_US",
      url: metaUrl,
      title: fullTitle,
      description: metaDescription,
      siteName: "Your SaaS App",
      images: [
        {
          url: metaImage,
          width: 1200,
          height: 630,
          alt: fullTitle,
        },
      ],
      ...(publishedTime && { publishedTime }),
      ...(modifiedTime && { modifiedTime }),
      ...(authors && { authors }),
      ...(section && { section }),
      ...(tags && { tags }),
    },

    // Twitter
    twitter: {
      card: "summary_large_image",
      title: fullTitle,
      description: metaDescription,
      creator: "@yourcompany",
      site: "@yourcompany",
      images: [metaImage],
    },

    // Additional metadata
    metadataBase: new URL(appUrl),
    alternates: {
      canonical: canonical || metaUrl,
      languages: {
        "en-US": "/",
        "id-ID": "/id",
      },
    },
    
    // App-specific metadata
    applicationName: "Your SaaS App",
    referrer: "origin-when-cross-origin",
    
    // Verification
    verification: {
      google: "your-google-verification-code",
      yandex: "your-yandex-verification-code",
      yahoo: "your-yahoo-verification-code",
    },

    // App links
    appLinks: {
      ios: {
        app_store_id: "your-ios-app-id",
        url: "your-app://",
      },
      android: {
        package: "com.yourcompany.yourapp",
        url: "your-app://",
      },
    },

    // Category
    category: "technology",
  };

  return metadata;
}

// Generate viewport configuration
export function generateViewport(): Viewport {
  return {
    colorScheme: "light dark",
    themeColor: [
      { media: "(prefers-color-scheme: light)", color: "#ffffff" },
      { media: "(prefers-color-scheme: dark)", color: "#000000" },
    ],
    width: "device-width",
    initialScale: 1,
    maximumScale: 1,
    userScalable: false,
  };
}

// Predefined metadata for common pages
export const homeMetadata = generateMetadata({
  title: "Home",
  description: "Build your SaaS faster than ever with our comprehensive Next.js 15 template featuring authentication, billing, and multi-tenancy.",
  keywords: ["SaaS template", "Next.js starter", "React template"],
});

export const pricingMetadata = generateMetadata({
  title: "Pricing",
  description: "Choose the perfect plan for your needs. Flexible credit-based pricing with no hidden fees.",
  keywords: ["pricing", "plans", "credits", "billing"],
  url: "/pricing",
});

export const docsMetadata = generateMetadata({
  title: "Documentation",
  description: "Learn how to use our SaaS template with comprehensive guides and API documentation.",
  keywords: ["documentation", "guides", "API", "tutorial"],
  url: "/docs",
});

export const aboutMetadata = generateMetadata({
  title: "About",
  description: "Learn more about our mission to help developers build amazing SaaS products faster.",
  keywords: ["about", "company", "mission", "team"],
  url: "/about",
});

export const contactMetadata = generateMetadata({
  title: "Contact",
  description: "Get in touch with our team. We're here to help you succeed with your SaaS project.",
  keywords: ["contact", "support", "help", "team"],
  url: "/contact",
});

// JSON-LD structured data generators
export function generateOrganizationSchema() {
  const appUrl = getAppUrl();
  
  return {
    "@context": "https://schema.org",
    "@type": "Organization",
    name: "Your SaaS App",
    url: appUrl,
    logo: `${appUrl}/logo.png`,
    description: defaultMetadata.description,
    foundingDate: "2024",
    founders: [
      {
        "@type": "Person",
        name: "Your Name",
      },
    ],
    address: {
      "@type": "PostalAddress",
      addressCountry: "US",
    },
    contactPoint: {
      "@type": "ContactPoint",
      contactType: "customer service",
      email: "<EMAIL>",
    },
    sameAs: [
      "https://twitter.com/yourcompany",
      "https://linkedin.com/company/yourcompany",
      "https://github.com/yourcompany",
    ],
  };
}

export function generateWebsiteSchema() {
  const appUrl = getAppUrl();
  
  return {
    "@context": "https://schema.org",
    "@type": "WebSite",
    name: "Your SaaS App",
    url: appUrl,
    description: defaultMetadata.description,
    potentialAction: {
      "@type": "SearchAction",
      target: `${appUrl}/search?q={search_term_string}`,
      "query-input": "required name=search_term_string",
    },
  };
}

export function generateSoftwareApplicationSchema() {
  const appUrl = getAppUrl();
  
  return {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    name: "Your SaaS App",
    url: appUrl,
    description: defaultMetadata.description,
    applicationCategory: "BusinessApplication",
    operatingSystem: "Web",
    offers: {
      "@type": "Offer",
      price: "0",
      priceCurrency: "USD",
      priceValidUntil: "2025-12-31",
      availability: "https://schema.org/InStock",
    },
    aggregateRating: {
      "@type": "AggregateRating",
      ratingValue: "4.8",
      ratingCount: "150",
      bestRating: "5",
      worstRating: "1",
    },
  };
}
