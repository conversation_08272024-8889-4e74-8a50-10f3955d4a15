# 📅 Class Bookings Implementation Guide

> **Panduan Lengkap Implementasi Class Bookings**  
> Ditulis dengan gaya mengajar Sandika Galih yang ramah untuk programmer pemula dan profesional

## 🎯 Apa itu Class Bookings?

Halo teman-teman! Kali ini kita akan belajar tentang implementasi **Class Bookings** - fitur untuk manage booking kelas, check-in customer, dan tracking attendance. 

Bayangkan kalian punya gym atau studio yoga, dan customer bisa booking kelas online. Nah, fitur ini yang akan handle semua proses booking tersebut!

## 🏗️ Arsitektur Modular yang Konsisten

### Mengapa Pakai Arsitektur Modular?

Seperti yang selalu saya tekankan di channel YouTube, **konsistensi itu kunci!** 

Kita tidak invent pattern baru, tapi mengikuti pattern yang sudah terbukti berhasil di fitur lain seperti:
- ✅ Locations Management
- ✅ Equipment Management  
- ✅ Package Management
- ✅ Customer Management

**Keuntungan pattern ini:**
1. **Predictable** - Developer baru bisa langsung paham
2. **Maintainable** - Mudah di-maintain karena strukturnya sama
3. **Scalable** - Bisa ditambah fitur baru dengan mudah
4. **Bug-free** - Pattern sudah teruji di fitur lain

## 📁 Struktur File yang Diimplementasikan

```
src/
├── lib/
│   ├── services/
│   │   └── class-booking.service.ts          # ✅ Business logic
│   └── hooks/
│       └── queries/
│           └── use-class-booking-queries.ts  # ✅ TanStack Query hooks
├── components/
│   ├── forms/
│   │   └── class-booking-form.tsx            # ✅ Reusable form
│   └── class-bookings/
│       └── class-booking-management.tsx      # ✅ Management component
├── app/
│   ├── api/
│   │   └── class-bookings/
│   │       ├── route.ts                      # ✅ Main API routes
│   │       ├── [id]/route.ts                 # ✅ Individual booking routes
│   │       ├── actions/route.ts              # ✅ Special actions (check-in, cancel)
│   │       └── stats/route.ts                # ✅ Statistics endpoint
│   └── (dashboard)/
│       └── class-bookings/
│           └── page.tsx                      # ✅ Main page
└── lib/db/schema.ts                          # ✅ Type exports added
```

## 🔧 Layer 1: Database Schema & Types

### Schema yang Sudah Ada

Kita menggunakan tabel `class_bookings` yang sudah ada di database. Yang kita tambahkan hanya type exports:

```typescript
// Type definitions for class bookings
export type ClassBooking = typeof class_bookings.$inferSelect;
export type NewClassBooking = typeof class_bookings.$inferInsert;
```

**Kenapa tidak ubah schema?** 
- Schema sudah perfect untuk kebutuhan booking
- Mengikuti prinsip "don't fix what's not broken"
- Fokus ke implementasi business logic yang solid

## 🎯 Layer 2: Service Layer (Business Logic)

### ClassBookingService - The Heart of Everything

File: `src/lib/services/class-booking.service.ts`

**Mengapa pakai static methods?**
- Simple dan straightforward
- Tidak perlu instantiate class
- Pattern yang sama dengan LocationService dan EquipmentService
- Proven track record tanpa bug

**Key Methods yang Diimplementasikan:**

```typescript
// CRUD Operations
static async getByTenantId(tenantId: number)     // Get all bookings
static async getById(id: string)                 // Get single booking
static async create(data: CreateClassBookingData) // Create new booking
static async update(id: string, data: UpdateData) // Update booking
static async delete(id: string)                  // Delete booking

// Search & Filtering
static async searchBookings(filters, limit, offset) // Advanced search

// Business Logic
static async checkIn(id: string, tenantId: number)  // Check-in customer
static async cancel(id: string, tenantId: number)   // Cancel booking

// Analytics
static async getStats(tenantId?: number)         // Get statistics

// Bulk Operations
static async bulkUpdateStatus(ids, status, tenantId)
static async bulkDelete(ids, tenantId)

// Relationship Queries
static async getByScheduleId(scheduleId, tenantId)  // Bookings per schedule
static async getByCustomerId(customerId, tenantId) // Customer history
```

**Fitur Unggulan:**

1. **Tenant Isolation** - Setiap query pasti include tenantId
2. **Rich Relations** - Join dengan tables customers, classes, schedules, locations
3. **Flexible Search** - Search by customer name, class name, notes
4. **Status Management** - Handle berbagai status booking
5. **Waitlist Support** - Manage waitlist dengan position
6. **Payment Tracking** - Track payment status dan method

## 🔄 Layer 3: TanStack Query Hooks

### Factory Pattern + Custom Hooks

File: `src/lib/hooks/queries/use-class-booking-queries.ts`

**Strategi Hybrid:**
1. **Base hooks** dari `createEntityHooks` factory
2. **Custom hooks** untuk functionality spesifik

```typescript
// Base hooks (dari factory)
export const useClassBookings = baseHooks.useEntities;
export const useClassBooking = baseHooks.useEntity;
export const useCreateClassBooking = baseHooks.useCreateEntity;
export const useUpdateClassBooking = baseHooks.useUpdateEntity;
export const useDeleteClassBooking = baseHooks.useDeleteEntity;

// Custom hooks (spesifik class bookings)
export function useClassBookingSearch(filters, options)
export function useClassBookingsBySchedule(scheduleId, tenantId)
export function useClassBookingsByCustomer(customerId, tenantId)
export function useCheckInBooking(options)
export function useCancelBooking(options)
export function useBulkUpdateBookingStatus(options)
```

**Keunggulan Approach Ini:**
- **Consistency** - Base functionality sama dengan fitur lain
- **Flexibility** - Custom hooks untuk kebutuhan spesifik
- **Caching** - Automatic caching dan invalidation
- **Optimistic Updates** - UI update langsung, rollback jika error
- **Error Handling** - Centralized error handling

## 🌐 Layer 4: API Routes

### RESTful API dengan RBAC

**Main Routes:**
- `GET /api/class-bookings` - List dengan filtering
- `POST /api/class-bookings` - Create booking
- `DELETE /api/class-bookings` - Bulk delete

**Individual Routes:**
- `GET /api/class-bookings/[id]` - Get single booking
- `PUT /api/class-bookings/[id]` - Update booking  
- `DELETE /api/class-bookings/[id]` - Delete single booking

**Special Actions:**
- `POST /api/class-bookings/actions` - Check-in, cancel, bulk operations

**Statistics:**
- `GET /api/class-bookings/stats` - Analytics data

**Security Features:**
1. **Authentication** - Semua endpoint require valid session
2. **RBAC** - Check permissions `bookings.manage` atau `bookings.read`
3. **Tenant Isolation** - User hanya bisa akses data tenant mereka
4. **Input Validation** - Zod schema validation
5. **Error Handling** - Consistent error responses

## 📝 Layer 5: Form Components

### ClassBookingForm - Reusable & Powerful

File: `src/components/forms/class-booking-form.tsx`

**Features yang Diimplementasikan:**

1. **Two-Column Layout** - Tidak terlalu panjang vertikal
2. **Smart Dropdowns** - Auto-populate berdasarkan relasi
3. **Conditional Fields** - Waitlist position muncul jika waitlist = true
4. **Real-time Validation** - Zod + react-hook-form
5. **Success Feedback** - Toast notification
6. **Loading States** - Disable form saat submit

**Smart Logic:**
```typescript
// Auto-set classId ketika schedule dipilih
useEffect(() => {
  if (watchedScheduleId) {
    const selectedSchedule = schedules.find(s => s.id === watchedScheduleId);
    if (selectedSchedule && selectedSchedule.classId !== selectedClassId) {
      setSelectedClassId(selectedSchedule.classId);
      setValue("classId", selectedSchedule.classId);
    }
  }
}, [watchedScheduleId, schedules, selectedClassId, setValue]);
```

**Form Fields:**
- **Basic Info**: Class, Schedule, Customer, Status
- **Waitlist**: Toggle + position
- **Payment**: Status, method, credits used
- **Additional**: Notes

## 🎛️ Layer 6: Management Component

### ClassBookingManagement - The Complete Interface

File: `src/components/class-bookings/class-booking-management.tsx`

**UI/UX Features:**

1. **List View** - Card-based layout dengan informasi lengkap
2. **Search & Filter** - Real-time search + status filter
3. **Quick Actions** - Check-in, cancel, edit, delete
4. **Status Badges** - Visual status indicators
5. **Success Animations** - SuccessToast untuk feedback
6. **Empty States** - Helpful messages ketika no data

**Action Buttons:**
```typescript
// Check-in (hanya untuk status "booked")
{booking.status === "booked" && (
  <Button onClick={() => handleCheckIn(booking)}>
    <CheckCircle className="h-4 w-4" />
  </Button>
)}

// Cancel (untuk "booked" atau "waitlisted")
{(booking.status === "booked" || booking.status === "waitlisted") && (
  <Button onClick={() => handleCancel(booking)}>
    <XCircle className="h-4 w-4" />
  </Button>
)}
```

## 🧭 Layer 7: Navigation Integration

### Seamless Integration

**Navigation Entry:**
```typescript
{
  title: "Class Bookings",
  href: "/class-bookings",
  icon: Calendar,
  requiredPermission: { module: "bookings", action: "manage" },
}
```

**Page Implementation:**
- Authentication check
- RBAC permission check  
- Tenant isolation
- Error handling untuk edge cases

## 🎯 Common Pitfalls & Solutions

### 1. **Tenant Isolation Lupa**
❌ **Wrong:**
```typescript
const bookings = await db.select().from(class_bookings);
```

✅ **Correct:**
```typescript
const bookings = await db
  .select()
  .from(class_bookings)
  .where(eq(class_bookings.tenant_id, tenantId));
```

### 2. **Relations Tidak Di-join**
❌ **Wrong:** Ambil data booking, lalu query terpisah untuk customer name

✅ **Correct:** Join semua relations dalam satu query

### 3. **Error Handling Kurang**
❌ **Wrong:** Assume semua request berhasil

✅ **Correct:** Handle semua kemungkinan error dengan user-friendly messages

### 4. **Cache Invalidation Lupa**
❌ **Wrong:** Update data tapi cache tidak di-invalidate

✅ **Correct:** Invalidate related queries setelah mutation

## 🚀 Testing Checklist

Sebelum deploy, pastikan test semua functionality:

### ✅ CRUD Operations
- [ ] Create booking baru
- [ ] Edit booking existing  
- [ ] Delete booking
- [ ] View booking details

### ✅ Business Logic
- [ ] Check-in customer
- [ ] Cancel booking
- [ ] Waitlist management
- [ ] Payment status update

### ✅ Search & Filter
- [ ] Search by customer name
- [ ] Search by class name
- [ ] Filter by status
- [ ] Pagination works

### ✅ Permissions
- [ ] User tanpa permission tidak bisa akses
- [ ] Tenant isolation works
- [ ] Admin bisa akses semua tenant

### ✅ UI/UX
- [ ] Form validation works
- [ ] Success toast muncul
- [ ] Loading states proper
- [ ] Error messages helpful

## 🎉 Kesimpulan

Congratulations! Kalian sudah berhasil implement Class Bookings dengan:

1. **Arsitektur Modular** yang konsisten
2. **Full CRUD** functionality
3. **Advanced Search** dan filtering
4. **RBAC Integration** yang proper
5. **Professional UI/UX** dengan animations
6. **Comprehensive Error Handling**
7. **Tenant Isolation** yang secure

**Key Takeaways:**
- Konsistensi pattern lebih penting dari innovation
- Reusable components menghemat waktu development
- Proper error handling = better user experience
- TanStack Query = automatic caching dan optimistic updates

**Next Steps:**
- Add unit tests untuk service layer
- Implement real-time updates dengan WebSocket
- Add export functionality untuk reports
- Optimize performance dengan virtual scrolling

Keep coding, keep learning! 🚀

---

*Dokumentasi ini ditulis dengan ❤️ menggunakan gaya mengajar Sandika Galih*
