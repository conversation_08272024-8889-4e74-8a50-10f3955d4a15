"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Plus, Search, Edit, Trash2, Shield, Filter, Eye } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { PermissionForm } from "@/components/forms/permission-form";
import {
  useGroupedPermissions,
  useDeletePermission,
  type PermissionFormData,
} from "@/lib/hooks/queries/use-permission-queries";
import { type Permission } from "@/lib/db/schema";
import { SuccessToast } from "@/components/ui/success-toast";

interface PermissionManagementProps {
  onPermissionSelect?: (permission: Permission) => void;
  selectedPermissions?: string[];
  selectionMode?: boolean;
}

export function PermissionManagement({
  onPermissionSelect,
  selectedPermissions = [],
  selectionMode = false,
}: PermissionManagementProps) {
  // State management
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedModule, setSelectedModule] = useState<string>("all");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingPermission, setEditingPermission] = useState<Permission | null>(null);
  const [deletingPermission, setDeletingPermission] = useState<Permission | null>(null);
  const [showSuccessToast, setShowSuccessToast] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");

  // Data fetching
  const {
    data: groupedPermissions = {},
    isLoading,
    error,
    refetch,
  } = useGroupedPermissions();

  // Mutations
  const deleteMutation = useDeletePermission();

  // Filter permissions
  const filteredGroupedPermissions = Object.entries(groupedPermissions).reduce(
    (acc, [module, permissions]) => {
      if (selectedModule !== "all" && module !== selectedModule) return acc;
      
      const filtered = permissions.filter(permission =>
        permission.display_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        permission.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
        permission.description?.toLowerCase().includes(searchTerm.toLowerCase())
      );
      
      if (filtered.length > 0) {
        acc[module] = filtered;
      }
      
      return acc;
    },
    {} as Record<string, Permission[]>
  );

  // Get all modules for filter
  const allModules = Object.keys(groupedPermissions);

  // Handlers
  const handleCreateSuccess = () => {
    setSuccessMessage("Permission created successfully!");
    setShowSuccessToast(true);
    setIsCreateDialogOpen(false);
    refetch();
  };

  const handleUpdateSuccess = () => {
    setSuccessMessage("Permission updated successfully!");
    setShowSuccessToast(true);
    setEditingPermission(null);
    refetch();
  };

  const handleDeletePermission = async () => {
    if (!deletingPermission) return;

    try {
      await deleteMutation.mutateAsync(deletingPermission.id);
      setSuccessMessage("Permission deleted successfully!");
      setShowSuccessToast(true);
      setDeletingPermission(null);
      refetch();
    } catch (error) {
      console.error("Error deleting permission:", error);
    }
  };

  const canDeletePermission = (permission: Permission) => {
    return !permission.is_system_permission;
  };

  const getModuleColor = (module: string) => {
    const colors = {
      system: "bg-red-100 text-red-800",
      tenant: "bg-blue-100 text-blue-800",
      users: "bg-green-100 text-green-800",
      roles: "bg-purple-100 text-purple-800",
      classes: "bg-orange-100 text-orange-800",
      "class-schedules": "bg-yellow-100 text-yellow-800",
      customers: "bg-pink-100 text-pink-800",
      packages: "bg-indigo-100 text-indigo-800",
      locations: "bg-teal-100 text-teal-800",
      equipment: "bg-cyan-100 text-cyan-800",
      facilities: "bg-lime-100 text-lime-800",
      bookings: "bg-amber-100 text-amber-800",
      reports: "bg-slate-100 text-slate-800",
      admin: "bg-gray-100 text-gray-800",
    };
    return colors[module as keyof typeof colors] || "bg-gray-100 text-gray-800";
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(6)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-muted rounded w-1/2"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <p className="text-red-500">Error loading permissions: {error.message}</p>
          <Button onClick={() => refetch()} className="mt-2" variant="outline">
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Permission Management</h2>
          <p className="text-muted-foreground">
            Manage system permissions and access controls
          </p>
        </div>
        {!selectionMode && (
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add Permission
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Create New Permission</DialogTitle>
              </DialogHeader>
              <PermissionForm
                onSubmit={handleCreateSuccess}
                onCancel={() => setIsCreateDialogOpen(false)}
              />
            </DialogContent>
          </Dialog>
        )}
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search */}
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search permissions..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Module Filter */}
            <div className="w-full sm:w-48">
              <Select value={selectedModule} onValueChange={setSelectedModule}>
                <SelectTrigger>
                  <Filter className="h-4 w-4 mr-2" />
                  <SelectValue placeholder="All Modules" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Modules</SelectItem>
                  {allModules.map((module) => (
                    <SelectItem key={module} value={module}>
                      {module.charAt(0).toUpperCase() + module.slice(1)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Permissions by Module */}
      <div className="space-y-6">
        <AnimatePresence>
          {Object.entries(filteredGroupedPermissions).map(([module, permissions]) => (
            <motion.div
              key={module}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.2 }}
            >
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <Shield className="h-5 w-5" />
                      <CardTitle className="text-lg">
                        {module.charAt(0).toUpperCase() + module.slice(1)} Module
                      </CardTitle>
                      <Badge className={getModuleColor(module)}>
                        {permissions.length} permission{permissions.length !== 1 ? 's' : ''}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {permissions.map((permission) => (
                      <motion.div
                        key={permission.id}
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ duration: 0.2 }}
                        className={`
                          p-4 border rounded-lg cursor-pointer transition-all
                          ${selectionMode && selectedPermissions.includes(permission.id)
                            ? 'border-primary bg-primary/5'
                            : 'hover:border-primary/50 hover:shadow-sm'
                          }
                        `}
                        onClick={() => selectionMode && onPermissionSelect?.(permission)}
                      >
                        <div className="space-y-2">
                          <div className="flex items-start justify-between">
                            <h4 className="font-medium text-sm">{permission.display_name}</h4>
                            {!selectionMode && (
                              <div className="flex gap-1">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-6 w-6 p-0"
                                  onClick={() => setEditingPermission(permission)}
                                >
                                  <Edit className="h-3 w-3" />
                                </Button>
                                {canDeletePermission(permission) && (
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                                    onClick={() => setDeletingPermission(permission)}
                                  >
                                    <Trash2 className="h-3 w-3" />
                                  </Button>
                                )}
                              </div>
                            )}
                          </div>
                          
                          <div className="text-xs text-muted-foreground">
                            <code className="bg-muted px-1 py-0.5 rounded">
                              {permission.module}.{permission.action}
                            </code>
                          </div>
                          
                          {permission.description && (
                            <p className="text-xs text-muted-foreground">
                              {permission.description}
                            </p>
                          )}
                          
                          <div className="flex gap-1">
                            {permission.is_system_permission && (
                              <Badge variant="secondary" className="text-xs">
                                System
                              </Badge>
                            )}
                            <Badge variant="outline" className="text-xs">
                              {permission.action}
                            </Badge>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {/* Edit Dialog */}
      <Dialog open={!!editingPermission} onOpenChange={(open) => {
        if (!open) setEditingPermission(null);
      }}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Edit Permission</DialogTitle>
          </DialogHeader>
          {editingPermission && (
            <PermissionForm
              permission={editingPermission}
              onSubmit={handleUpdateSuccess}
              onCancel={() => setEditingPermission(null)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deletingPermission} onOpenChange={(open) => {
        if (!open) setDeletingPermission(null);
      }}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Permission</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete the permission "{deletingPermission?.display_name}"? 
              This action cannot be undone and may affect users with roles that have this permission.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeletePermission}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Success Toast */}
      <SuccessToast
        open={showSuccessToast}
        onOpenChange={setShowSuccessToast}
        title="Success!"
        description={successMessage}
      />
    </div>
  );
}
