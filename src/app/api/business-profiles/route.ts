import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { BusinessProfileService } from "@/lib/services/business-profile.service";
import { z } from "zod";

// Validation schema
const createBusinessProfileSchema = z.object({
  tenantId: z.number().int().positive(),
  business_name: z.string().min(1, "Business name is required").max(255),
  business_logo: z.string().url().optional(),
  company_registered_name: z.string().max(255).optional(),
  business_description: z.string().optional(),
  business_website: z.string().url().optional(),
  whatsapp_number: z.string().max(20).optional(),
  show_whatsapp_floating: z.boolean().default(false),
});

const updateBusinessProfileSchema = createBusinessProfileSchema.partial().omit({ tenantId: true });

// GET /api/business-profiles - Get business profiles
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get("tenantId");

    if (tenantId) {
      // Get specific business profile
      const profile = await BusinessProfileService.getByTenantId(parseInt(tenantId));
      return NextResponse.json({ profile });
    } else {
      // Get all profiles (admin only)
      if (session.user.role !== "admin") {
        return NextResponse.json({ error: "Forbidden" }, { status: 403 });
      }
      
      const profiles = await BusinessProfileService.getAll();
      return NextResponse.json({ profiles });
    }
  } catch (error) {
    console.error("Error fetching business profiles:", error);
    return NextResponse.json(
      { error: "Failed to fetch business profiles" },
      { status: 500 }
    );
  }
}

// POST /api/business-profiles - Create business profile
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = createBusinessProfileSchema.parse(body);

    // Check if user has permission to create profile for this tenant
    // This would typically check if user belongs to the tenant or is admin
    if (session.user.role !== "admin") {
      // Add tenant ownership check here
      // const userTenant = await getUserTenant(session.user.id);
      // if (userTenant?.id !== validatedData.tenantId) {
      //   return NextResponse.json({ error: "Forbidden" }, { status: 403 });
      // }
    }

    const profile = await BusinessProfileService.create(validatedData);

    return NextResponse.json({ profile }, { status: 201 });
  } catch (error) {
    console.error("Error creating business profile:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to create business profile" },
      { status: 500 }
    );
  }
}
