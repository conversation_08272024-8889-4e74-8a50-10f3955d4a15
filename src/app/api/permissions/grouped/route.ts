import { NextRequest, NextResponse } from "next/server";
import { PermissionService } from "@/lib/services/permission.service";
import { withRB<PERSON> } from "@/lib/middleware/rbac-middleware";

/**
 * API Routes untuk Grouped Permissions
 */

/**
 * GET /api/permissions/grouped
 * Get all permissions grouped by module
 */
export const GET = withRBAC(
  async (request: NextRequest) => {
    try {
      // Call service untuk get grouped permissions
      const groupedPermissions = await PermissionService.getAllGroupedByModule();

      return NextResponse.json({
        success: true,
        data: groupedPermissions,
      });
    } catch (error) {
      console.error("GET /api/permissions/grouped error:", error);
      return NextResponse.json(
        { success: false, error: "Internal server error" },
        { status: 500 }
      );
    }
  },
  { module: "permissions", action: "read" }
);
