"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Ticket,
  Building,
  Type,
  DollarSign,
  Calendar,
  Users,
  Settings,
  Hash,
  FileText,
  UserCheck,
  ToggleLeft,
  Loader2,
  AlertCircle,
  CheckCircle2
} from "lucide-react";
import { Voucher } from "@/lib/db/schema";
import { useCreateVoucher, useUpdateVoucher } from "@/lib/hooks/queries/use-voucher-queries";
import { useTenants } from "@/lib/hooks/queries/use-tenant-queries";
import { useLocations } from "@/lib/hooks/queries/use-location-queries";
import { usePackages } from "@/lib/hooks/queries/use-package-queries";
import { useClasses } from "@/lib/hooks/queries/use-class-queries";
import { usePricingGroups } from "@/lib/hooks/queries/use-pricing-group-queries";
import { toast } from "sonner";

// Form validation schema
const voucherFormSchema = z.object({
  tenantId: z.number().int().positive("Tenant is required"),
  code: z.string()
    .min(1, "Voucher code is required")
    .max(50, "Code must be less than 50 characters")
    .regex(/^[A-Z0-9_-]+$/, "Code must contain only uppercase letters, numbers, underscores, and hyphens"),
  name: z.string()
    .min(1, "Name is required")
    .max(255, "Name must be less than 255 characters"),
  description: z.string().max(1000, "Description must be less than 1000 characters").optional(),
  type: z.enum(["percentage", "fixed_amount", "free_shipping", "buy_x_get_y"], {
    required_error: "Voucher type is required",
  }),
  value: z.number().positive("Value must be greater than 0"),
  currency: z.string().length(3, "Currency must be 3 characters").default("USD"),
  usage_limit: z.number().int().positive().optional(),
  usage_limit_per_customer: z.number().int().positive().default(1),
  valid_from: z.string().min(1, "Valid from date is required"),
  valid_until: z.string().min(1, "Valid until date is required"),
  is_active: z.boolean().default(true),
  is_public: z.boolean().default(true),
  auto_apply: z.boolean().default(false),
  // Restrictions
  min_purchase_amount: z.number().positive().optional(),
  max_discount_amount: z.number().positive().optional(),
  first_time_customers_only: z.boolean().default(false),
  existing_customers_only: z.boolean().default(false),
});

type VoucherFormData = z.infer<typeof voucherFormSchema>;

// Form props interface
//saat didelete terdapat errors seperti ini
interface VoucherFormProps {
  open: boolean;
  onClose: () => void;
  title?: string;
  description?: string;
  voucher?: Voucher;
  tenantId?: number;
}

// Get tenants for dropdown
function useTenantOptions() {
  const { data: tenantsData, isLoading } = useTenants();
  const tenants = tenantsData?.tenants || [];

  return {
    options: tenants.map(tenant => ({
      value: tenant.id.toString(),
      label: tenant.name,
    })),
    isLoading,
  };
}

// Get locations for dropdown
function useLocationOptions(tenantId?: number) {
  const { data: locations = [], isLoading } = useLocations({
    tenantId,
  });

  return {
    options: locations.map(location => ({
      value: location.id,
      label: location.name || `Location ${location.id}`,
    })),
    isLoading,
  };
}

// Main voucher form component
export function VoucherForm({
  open,
  onClose,
  title = "Add New Voucher",
  description = "Create a new voucher with discount settings and restrictions",
  voucher,
  tenantId = 1
}: VoucherFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const createVoucherMutation = useCreateVoucher();
  const updateVoucherMutation = useUpdateVoucher();
  const { options: tenantOptions } = useTenantOptions();
  const { options: locationOptions } = useLocationOptions(tenantId);

  const isEditing = !!voucher;

  // Form setup
  const form = useForm<VoucherFormData>({
    resolver: zodResolver(voucherFormSchema),
    defaultValues: {
      tenantId: voucher?.tenantId || tenantId,
      code: voucher?.code || "",
      name: voucher?.name || "",
      description: voucher?.description || "",
      type: voucher?.type || "percentage",
      value: voucher?.value || 10,
      currency: voucher?.currency || "USD",
      usage_limit: voucher?.usage_limit || undefined,
      usage_limit_per_customer: voucher?.usage_limit_per_customer || 1,
      valid_from: voucher?.valid_from ? new Date(voucher.valid_from).toISOString().split('T')[0] : new Date().toISOString().split('T')[0],
      valid_until: voucher?.valid_until ? new Date(voucher.valid_until).toISOString().split('T')[0] : new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      is_active: voucher?.is_active ?? true,
      is_public: voucher?.is_public ?? true,
      auto_apply: voucher?.auto_apply ?? false,
      min_purchase_amount: voucher?.restrictions?.min_purchase_amount || undefined,
      max_discount_amount: voucher?.restrictions?.max_discount_amount || undefined,
      first_time_customers_only: voucher?.restrictions?.first_time_customers_only ?? false,
      existing_customers_only: voucher?.restrictions?.existing_customers_only ?? false,
    },
  });

  // Handle form submission
  const onSubmit = async (data: VoucherFormData) => {
    setIsSubmitting(true);
    try {
      const voucherData = {
        ...data,
        valid_from: new Date(data.valid_from),
        valid_until: new Date(data.valid_until),
        restrictions: {
          min_purchase_amount: data.min_purchase_amount,
          max_discount_amount: data.max_discount_amount,
          first_time_customers_only: data.first_time_customers_only,
          existing_customers_only: data.existing_customers_only,
        },
      };

      if (isEditing && voucher) {
        await updateVoucherMutation.mutateAsync({
          id: voucher.id,
          data: voucherData,
        });
        toast.success("Voucher updated successfully!");
      } else {
        await createVoucherMutation.mutateAsync(voucherData);
        toast.success("Voucher created successfully!");
      }

      form.reset();
      onClose();
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to save voucher");
    } finally {
      setIsSubmitting(false);
    }
  };

  const watchedType = form.watch("type");
  const watchedValue = form.watch("value");

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Ticket className="h-5 w-5" />
            {title}
          </DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>

        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Basic Information Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                Basic Information
              </CardTitle>
              <CardDescription>
                General voucher details and identification
              </CardDescription>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Tenant Selection */}
              <div className="space-y-2">
                <Label htmlFor="tenantId" className="flex items-center gap-2">
                  <Building className="h-4 w-4" />
                  Tenant *
                </Label>
                <Select
                  value={form.watch("tenantId")?.toString()}
                  onValueChange={(value) => form.setValue("tenantId", parseInt(value))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select tenant" />
                  </SelectTrigger>
                  <SelectContent>
                    {tenantOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {form.formState.errors.tenantId && (
                  <p className="text-sm text-red-500">{form.formState.errors.tenantId.message}</p>
                )}
              </div>

              {/* Voucher Code */}
              <div className="space-y-2">
                <Label htmlFor="code" className="flex items-center gap-2">
                  <Hash className="h-4 w-4" />
                  Voucher Code *
                </Label>
                <Input
                  id="code"
                  placeholder="e.g., SAVE20, WELCOME10"
                  {...form.register("code")}
                />
                {form.formState.errors.code && (
                  <p className="text-sm text-red-500">{form.formState.errors.code.message}</p>
                )}
                <p className="text-xs text-gray-500">
                  Use uppercase letters, numbers, underscores, and hyphens only
                </p>
              </div>

              {/* Voucher Name */}
              <div className="space-y-2">
                <Label htmlFor="name" className="flex items-center gap-2">
                  <Type className="h-4 w-4" />
                  Voucher Name *
                </Label>
                <Input
                  id="name"
                  placeholder="e.g., Summer Sale 20% Off"
                  {...form.register("name")}
                />
                {form.formState.errors.name && (
                  <p className="text-sm text-red-500">{form.formState.errors.name.message}</p>
                )}
              </div>

              {/* Description */}
              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="description" className="flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  Description
                </Label>
                <Textarea
                  id="description"
                  placeholder="Describe what this voucher offers..."
                  rows={3}
                  {...form.register("description")}
                />
                {form.formState.errors.description && (
                  <p className="text-sm text-red-500">{form.formState.errors.description.message}</p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Discount Configuration Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                Discount Configuration
              </CardTitle>
              <CardDescription>
                Type and value of the discount
              </CardDescription>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Voucher Type */}
              <div className="space-y-2">
                <Label htmlFor="type" className="flex items-center gap-2">
                  <Ticket className="h-4 w-4" />
                  Voucher Type *
                </Label>
                <Select
                  value={form.watch("type")}
                  onValueChange={(value) => form.setValue("type", value as any)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select voucher type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="percentage">Percentage Discount (e.g., 20% off)</SelectItem>
                    <SelectItem value="fixed_amount">Fixed Amount Discount (e.g., $10 off)</SelectItem>
                    <SelectItem value="free_shipping">Free Shipping</SelectItem>
                    <SelectItem value="buy_x_get_y" disabled>Buy X Get Y (Coming Soon)</SelectItem>
                  </SelectContent>
                </Select>
                {form.formState.errors.type && (
                  <p className="text-sm text-red-500">{form.formState.errors.type.message}</p>
                )}
              </div>

              {/* Discount Value */}
              <div className="space-y-2">
                <Label htmlFor="value" className="flex items-center gap-2">
                  <DollarSign className="h-4 w-4" />
                  Discount Value *
                </Label>
                <Input
                  id="value"
                  type="number"
                  placeholder={watchedType === "percentage" ? "e.g., 20 for 20%" : "e.g., 1000 for $10.00"}
                  {...form.register("value", { valueAsNumber: true })}
                />
                {form.formState.errors.value && (
                  <p className="text-sm text-red-500">{form.formState.errors.value.message}</p>
                )}
                <p className="text-xs text-gray-500">
                  {watchedType === "percentage"
                    ? "Enter 1-100 for percentage discount"
                    : "Enter amount in cents (e.g., 1000 = $10.00)"
                  }
                </p>
                {watchedType === "percentage" && watchedValue && (
                  <Badge variant="outline" className="text-green-600">
                    {watchedValue}% discount
                  </Badge>
                )}
                {watchedType === "fixed_amount" && watchedValue && (
                  <Badge variant="outline" className="text-green-600">
                    ${(watchedValue / 100).toFixed(2)} discount
                  </Badge>
                )}
              </div>

              {/* Currency */}
              <div className="space-y-2">
                <Label htmlFor="currency" className="flex items-center gap-2">
                  <DollarSign className="h-4 w-4" />
                  Currency
                </Label>
                <Input
                  id="currency"
                  placeholder="USD"
                  maxLength={3}
                  {...form.register("currency")}
                />
                {form.formState.errors.currency && (
                  <p className="text-sm text-red-500">{form.formState.errors.currency.message}</p>
                )}
                <p className="text-xs text-gray-500">3-letter currency code</p>
              </div>
            </CardContent>
          </Card>

          {/* Usage & Validity Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Usage & Validity
              </CardTitle>
              <CardDescription>
                Usage limits and validity period
              </CardDescription>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Total Usage Limit */}
              <div className="space-y-2">
                <Label htmlFor="usage_limit" className="flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  Total Usage Limit
                </Label>
                <Input
                  id="usage_limit"
                  type="number"
                  placeholder="Leave empty for unlimited"
                  {...form.register("usage_limit", { valueAsNumber: true })}
                />
                {form.formState.errors.usage_limit && (
                  <p className="text-sm text-red-500">{form.formState.errors.usage_limit.message}</p>
                )}
                <p className="text-xs text-gray-500">
                  Maximum number of times this voucher can be used across all customers
                </p>
              </div>

              {/* Usage Limit Per Customer */}
              <div className="space-y-2">
                <Label htmlFor="usage_limit_per_customer" className="flex items-center gap-2">
                  <UserCheck className="h-4 w-4" />
                  Usage Limit Per Customer *
                </Label>
                <Input
                  id="usage_limit_per_customer"
                  type="number"
                  placeholder="1"
                  {...form.register("usage_limit_per_customer", { valueAsNumber: true })}
                />
                {form.formState.errors.usage_limit_per_customer && (
                  <p className="text-sm text-red-500">{form.formState.errors.usage_limit_per_customer.message}</p>
                )}
                <p className="text-xs text-gray-500">
                  How many times one customer can use this voucher
                </p>
              </div>

              {/* Valid From */}
              <div className="space-y-2">
                <Label htmlFor="valid_from" className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  Valid From *
                </Label>
                <Input
                  id="valid_from"
                  type="date"
                  {...form.register("valid_from")}
                />
                {form.formState.errors.valid_from && (
                  <p className="text-sm text-red-500">{form.formState.errors.valid_from.message}</p>
                )}
              </div>

              {/* Valid Until */}
              <div className="space-y-2">
                <Label htmlFor="valid_until" className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  Valid Until *
                </Label>
                <Input
                  id="valid_until"
                  type="date"
                  {...form.register("valid_until")}
                />
                {form.formState.errors.valid_until && (
                  <p className="text-sm text-red-500">{form.formState.errors.valid_until.message}</p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Status & Behavior Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-4 w-4" />
                Status & Behavior
              </CardTitle>
              <CardDescription>
                Voucher status and behavior settings
              </CardDescription>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Active Status */}
              <div className="space-y-2">
                <Label htmlFor="is_active" className="flex items-center gap-2">
                  <ToggleLeft className="h-4 w-4" />
                  Active
                </Label>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="is_active"
                    checked={form.watch("is_active")}
                    onCheckedChange={(checked) => form.setValue("is_active", checked)}
                  />
                  <span className="text-sm text-gray-600">
                    {form.watch("is_active") ? "Active" : "Inactive"}
                  </span>
                </div>
                <p className="text-xs text-gray-500">
                  Whether this voucher is currently active
                </p>
              </div>

              {/* Public Voucher */}
              <div className="space-y-2">
                <Label htmlFor="is_public" className="flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  Public Voucher
                </Label>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="is_public"
                    checked={form.watch("is_public")}
                    onCheckedChange={(checked) => form.setValue("is_public", checked)}
                  />
                  <span className="text-sm text-gray-600">
                    {form.watch("is_public") ? "Public" : "Private"}
                  </span>
                </div>
                <p className="text-xs text-gray-500">
                  If disabled, voucher must be assigned to specific customers
                </p>
              </div>

              {/* Auto Apply */}
              <div className="space-y-2">
                <Label htmlFor="auto_apply" className="flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  Auto Apply
                </Label>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="auto_apply"
                    checked={form.watch("auto_apply")}
                    onCheckedChange={(checked) => form.setValue("auto_apply", checked)}
                    disabled
                  />
                  <span className="text-sm text-gray-400">
                    Coming Soon
                  </span>
                </div>
                <p className="text-xs text-gray-500">
                  Automatically apply if conditions are met
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Restrictions Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertCircle className="h-4 w-4" />
                Purchase & Customer Restrictions
              </CardTitle>
              <CardDescription>
                Optional restrictions to control voucher usage
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Purchase Amount Restrictions */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="min_purchase_amount" className="flex items-center gap-2">
                    <DollarSign className="h-4 w-4" />
                    Minimum Purchase Amount
                  </Label>
                  <Input
                    id="min_purchase_amount"
                    type="number"
                    placeholder="e.g., 5000 for $50.00 minimum"
                    {...form.register("min_purchase_amount", { valueAsNumber: true })}
                  />
                  {form.formState.errors.min_purchase_amount && (
                    <p className="text-sm text-red-500">{form.formState.errors.min_purchase_amount.message}</p>
                  )}
                  <p className="text-xs text-gray-500">
                    Minimum purchase amount required (in cents). Leave empty for no minimum.
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="max_discount_amount" className="flex items-center gap-2">
                    <DollarSign className="h-4 w-4" />
                    Maximum Discount Amount
                  </Label>
                  <Input
                    id="max_discount_amount"
                    type="number"
                    placeholder="e.g., 2000 for $20.00 maximum"
                    {...form.register("max_discount_amount", { valueAsNumber: true })}
                  />
                  {form.formState.errors.max_discount_amount && (
                    <p className="text-sm text-red-500">{form.formState.errors.max_discount_amount.message}</p>
                  )}
                  <p className="text-xs text-gray-500">
                    Maximum discount amount for percentage vouchers (in cents). Leave empty for no limit.
                  </p>
                </div>
              </div>

              <Separator />

              {/* Customer Type Restrictions */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="first_time_customers_only" className="flex items-center gap-2">
                    <UserCheck className="h-4 w-4" />
                    First-Time Customers Only
                  </Label>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="first_time_customers_only"
                      checked={form.watch("first_time_customers_only")}
                      onCheckedChange={(checked) => {
                        form.setValue("first_time_customers_only", checked);
                        if (checked) {
                          form.setValue("existing_customers_only", false);
                        }
                      }}
                    />
                    <span className="text-sm text-gray-600">
                      {form.watch("first_time_customers_only") ? "Enabled" : "Disabled"}
                    </span>
                  </div>
                  <p className="text-xs text-gray-500">
                    Only allow first-time customers to use this voucher
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="existing_customers_only" className="flex items-center gap-2">
                    <Users className="h-4 w-4" />
                    Existing Customers Only
                  </Label>
                  <div className="flex items-center space-x-2">
                    <Switch
                      id="existing_customers_only"
                      checked={form.watch("existing_customers_only")}
                      onCheckedChange={(checked) => {
                        form.setValue("existing_customers_only", checked);
                        if (checked) {
                          form.setValue("first_time_customers_only", false);
                        }
                      }}
                    />
                    <span className="text-sm text-gray-600">
                      {form.watch("existing_customers_only") ? "Enabled" : "Disabled"}
                    </span>
                  </div>
                  <p className="text-xs text-gray-500">
                    Only allow existing customers to use this voucher
                  </p>
                </div>
              </div>

              {/* Validation Alert */}
              {form.watch("first_time_customers_only") && form.watch("existing_customers_only") && (
                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    Cannot enable both first-time and existing customers restrictions at the same time.
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>

          {/* Form Actions */}
          <div className="flex justify-end space-x-4 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="min-w-[120px]"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  {isEditing ? "Updating..." : "Creating..."}
                </>
              ) : (
                <>
                  <CheckCircle2 className="mr-2 h-4 w-4" />
                  {isEditing ? "Update Voucher" : "Create Voucher"}
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
