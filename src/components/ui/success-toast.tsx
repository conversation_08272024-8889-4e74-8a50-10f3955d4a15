"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { CheckCircle, MapPin, X, Sparkles } from "lucide-react";
import { Button } from "@/components/ui/button";

interface SuccessToastProps {
  isVisible: boolean;
  onClose: () => void;
  title: string;
  description?: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  autoClose?: boolean;
  duration?: number;
  type?: "location" | "general";
}

export function SuccessToast({
  isVisible,
  onClose,
  title,
  description,
  action,
  autoClose = true,
  duration = 4000,
  type = "general"
}: SuccessToastProps) {
  const [progress, setProgress] = useState(100);

  useEffect(() => {
    if (!isVisible || !autoClose) return;

    const interval = setInterval(() => {
      setProgress((prev) => {
        if (prev <= 0) {
          onClose();
          return 0;
        }
        return prev - (100 / (duration / 100));
      });
    }, 100);

    return () => clearInterval(interval);
  }, [isVisible, autoClose, duration, onClose]);

  useEffect(() => {
    if (isVisible) {
      setProgress(100);
    }
  }, [isVisible]);

  const getIcon = () => {
    switch (type) {
      case "location":
        return <MapPin className="h-6 w-6 text-green-600" />;
      default:
        return <CheckCircle className="h-6 w-6 text-green-600" />;
    }
  };

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: -100, scale: 0.9 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, y: -100, scale: 0.9 }}
          transition={{
            type: "spring",
            stiffness: 300,
            damping: 30,
            duration: 0.4
          }}
          className="fixed top-4 right-4 z-50 w-96 max-w-sm"
        >
          <div className="relative bg-white dark:bg-gray-800 rounded-lg shadow-2xl border border-green-200 dark:border-green-800 overflow-hidden">
            {/* Success glow effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20" />
            
            {/* Progress bar */}
            {autoClose && (
              <motion.div
                className="absolute top-0 left-0 h-1 bg-gradient-to-r from-green-500 to-emerald-500"
                initial={{ width: "100%" }}
                animate={{ width: `${progress}%` }}
                transition={{ duration: 0.1, ease: "linear" }}
              />
            )}

            <div className="relative p-4">
              <div className="flex items-start gap-3">
                {/* Animated icon */}
                <motion.div
                  initial={{ scale: 0, rotate: -180 }}
                  animate={{ scale: 1, rotate: 0 }}
                  transition={{
                    type: "spring",
                    stiffness: 400,
                    damping: 20,
                    delay: 0.2
                  }}
                  className="flex-shrink-0 relative"
                >
                  {getIcon()}
                  
                  {/* Sparkle animation */}
                  <motion.div
                    initial={{ opacity: 0, scale: 0 }}
                    animate={{ opacity: [0, 1, 0], scale: [0, 1.2, 0] }}
                    transition={{
                      duration: 1.5,
                      delay: 0.3,
                      repeat: 1
                    }}
                    className="absolute -top-1 -right-1"
                  >
                    <Sparkles className="h-3 w-3 text-yellow-400" />
                  </motion.div>
                </motion.div>

                <div className="flex-1 min-w-0">
                  <motion.h4
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.1 }}
                    className="text-sm font-semibold text-gray-900 dark:text-gray-100"
                  >
                    {title}
                  </motion.h4>
                  
                  {description && (
                    <motion.p
                      initial={{ opacity: 0, x: 20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.2 }}
                      className="text-sm text-gray-600 dark:text-gray-400 mt-1"
                    >
                      {description}
                    </motion.p>
                  )}

                  {action && (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3 }}
                      className="mt-3"
                    >
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={action.onClick}
                        className="text-green-700 border-green-300 hover:bg-green-50 dark:text-green-400 dark:border-green-600 dark:hover:bg-green-900/20"
                      >
                        {action.label}
                      </Button>
                    </motion.div>
                  )}
                </div>

                {/* Close button */}
                <motion.div
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.4 }}
                >
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onClose}
                    className="h-6 w-6 p-0 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </motion.div>
              </div>
            </div>

            {/* Floating particles effect */}
            <div className="absolute inset-0 pointer-events-none overflow-hidden">
              {[...Array(6)].map((_, i) => (
                <motion.div
                  key={i}
                  initial={{ 
                    opacity: 0, 
                    y: 20, 
                    x: Math.random() * 100 - 50,
                    scale: 0 
                  }}
                  animate={{ 
                    opacity: [0, 1, 0], 
                    y: -30, 
                    scale: [0, 1, 0] 
                  }}
                  transition={{
                    duration: 2,
                    delay: 0.5 + i * 0.1,
                    ease: "easeOut"
                  }}
                  className="absolute bottom-4 left-1/2 w-1 h-1 bg-green-400 rounded-full"
                  style={{
                    left: `${20 + i * 12}%`
                  }}
                />
              ))}
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
