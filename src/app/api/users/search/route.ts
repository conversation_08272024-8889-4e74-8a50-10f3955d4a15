import { NextRequest, NextResponse } from "next/server";
import { UserService } from "@/lib/services/user.service";
import { withRB<PERSON> } from "@/lib/middleware/rbac-middleware";

/**
 * API Routes untuk User Search
 * 
 * Endpoint ini digunakan untuk search users dalam konteks role assignment.
 * Mengi<PERSON><PERSON> pola yang sama dengan permission search API.
 */

/**
 * GET /api/users/search
 * Search users for role assignment
 */
export const GET = withRBAC(
  async (request: NextRequest) => {
    try {
      const { searchParams } = new URL(request.url);
      const query = searchParams.get("query") || "";
      const tenantId = searchParams.get("tenantId");
      const limit = parseInt(searchParams.get("limit") || "20");
      const offset = parseInt(searchParams.get("offset") || "0");

      // Parse tenantId - bisa null untuk superadmin
      let parsedTenantId: number | null | undefined = undefined;
      if (tenantId === "null") {
        parsedTenantId = null;
      } else if (tenantId) {
        parsedTenantId = parseInt(tenantId);
      }

      // Call service untuk search users
      const result = await UserService.searchUsersForRoleAssignment(
        query,
        parsedTenantId,
        limit,
        offset
      );

      return NextResponse.json({
        success: true,
        data: result,
      });
    } catch (error) {
      console.error("GET /api/users/search error:", error);
      return NextResponse.json(
        { success: false, error: "Internal server error" },
        { status: 500 }
      );
    }
  },
  { module: "users", action: "read" }
);
