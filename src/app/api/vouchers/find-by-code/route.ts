import { NextRequest, NextResponse } from "next/server";
import { VoucherService } from "@/lib/services/voucher.service";
import { withRBAC } from "@/lib/middleware/rbac-middleware";

// GET /api/vouchers/find-by-code - Find voucher by code
export async function GET(request: NextRequest) {
  return withRBAC(
    async () => {
      try {
        const { searchParams } = new URL(request.url);
        
        const code = searchParams.get("code");
        const tenantId = searchParams.get("tenantId");

        if (!code) {
          return NextResponse.json(
            { error: "Voucher code is required" },
            { status: 400 }
          );
        }

        if (!tenantId) {
          return NextResponse.json(
            { error: "Tenant ID is required" },
            { status: 400 }
          );
        }

        // Find voucher by code
        const voucher = await VoucherService.findByCode(code, parseInt(tenantId));

        if (!voucher) {
          return NextResponse.json(
            { error: "Voucher not found" },
            { status: 404 }
          );
        }

        return NextResponse.json({
          success: true,
          data: voucher,
        });
      } catch (error) {
        console.error("Error finding voucher by code:", error);
        return NextResponse.json(
          { 
            error: error instanceof Error ? error.message : "Failed to find voucher",
            success: false 
          },
          { status: 500 }
        );
      }
    },
    { module: "vouchers", action: "read" }
  )(request);
}
