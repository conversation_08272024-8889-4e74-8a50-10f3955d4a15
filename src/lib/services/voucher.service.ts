import { sql, eq, and, ilike, SQL, gte, lte, lt, gt } from "drizzle-orm";
import { 
  vouchers, 
  voucher_usage, 
  voucher_customer_assignments,
  customers,
  type Voucher, 
  type NewVoucher,
  type VoucherUsage,
  type NewVoucherUsage,
  type VoucherCustomerAssignment,
  type NewVoucherCustomerAssignment,
  type VoucherRestrictionsData
} from "@/lib/db/schema";
import { BaseService, CreateEntityData, UpdateEntityData, QueryOptions } from "@/lib/core/base-service";
import { createId } from "@paralleldrive/cuid2";
import { db } from "@/lib/db";

// Voucher data transfer objects
export interface CreateVoucherData extends CreateEntityData {
  tenantId: number;
  code: string;
  name: string;
  description?: string;
  type: "percentage" | "fixed_amount" | "free_shipping" | "buy_x_get_y";
  value: number;
  currency?: string;
  usage_limit?: number;
  usage_limit_per_customer?: number;
  valid_from: Date;
  valid_until: Date;
  is_active?: boolean;
  is_public?: boolean;
  auto_apply?: boolean;
  restrictions?: VoucherRestrictionsData;
  created_by?: string;
}

export interface UpdateVoucherData extends UpdateEntityData {
  code?: string;
  name?: string;
  description?: string;
  type?: "percentage" | "fixed_amount" | "free_shipping" | "buy_x_get_y";
  value?: number;
  currency?: string;
  usage_limit?: number;
  usage_limit_per_customer?: number;
  valid_from?: Date;
  valid_until?: Date;
  is_active?: boolean;
  is_public?: boolean;
  auto_apply?: boolean;
  restrictions?: VoucherRestrictionsData;
}

export interface VoucherFilters {
  search?: string;
  tenantId?: number;
  type?: string;
  is_active?: boolean;
  is_public?: boolean;
  valid_only?: boolean; // Only return vouchers that are currently valid
  location_id?: string;
  customer_id?: string;
}

export interface VoucherUsageData {
  voucher_id: string;
  customer_id: string;
  order_id?: string;
  package_id?: string;
  class_id?: string;
  location_id?: string;
  original_amount: number;
  discount_amount: number;
  final_amount: number;
  currency?: string;
  ip_address?: string;
  user_agent?: string;
}

export interface VoucherValidationResult {
  is_valid: boolean;
  error_message?: string;
  discount_amount?: number;
  final_amount?: number;
  voucher?: Voucher;
}

class VoucherServiceClass extends BaseService<Voucher, CreateVoucherData, UpdateVoucherData> {
  constructor() {
    super(vouchers, "Voucher");
  }

  // Build filter conditions for search and filtering
  protected buildFilterConditions(filters: Record<string, any>): SQL[] {
    const conditions: SQL[] = [];

    if (filters.search) {
      conditions.push(
        sql`(${vouchers.code} ILIKE ${`%${filters.search}%`} OR ${vouchers.name} ILIKE ${`%${filters.search}%`} OR ${vouchers.description} ILIKE ${`%${filters.search}%`})`
      );
    }

    if (filters.type) {
      conditions.push(eq(vouchers.type, filters.type));
    }

    if (filters.is_active !== undefined) {
      conditions.push(eq(vouchers.is_active, filters.is_active));
    }

    if (filters.is_public !== undefined) {
      conditions.push(eq(vouchers.is_public, filters.is_public));
    }

    if (filters.valid_only) {
      const now = new Date();
      conditions.push(
        and(
          lte(vouchers.valid_from, now),
          gte(vouchers.valid_until, now),
          eq(vouchers.is_active, true)
        )!
      );
    }

    return conditions;
  }

  // Validate voucher creation data
  protected validateCreateData(data: CreateVoucherData): void {
    if (!data.code?.trim()) {
      throw new Error("Voucher code is required");
    }
    if (!data.name?.trim()) {
      throw new Error("Voucher name is required");
    }
    if (!data.type) {
      throw new Error("Voucher type is required");
    }
    if (!data.value || data.value <= 0) {
      throw new Error("Voucher value must be greater than 0");
    }
    if (data.type === "percentage" && (data.value < 1 || data.value > 100)) {
      throw new Error("Percentage voucher value must be between 1 and 100");
    }
    if (!data.valid_from || !data.valid_until) {
      throw new Error("Valid from and valid until dates are required");
    }
    if (data.valid_from >= data.valid_until) {
      throw new Error("Valid from date must be before valid until date");
    }
    if (data.usage_limit_per_customer && data.usage_limit_per_customer <= 0) {
      throw new Error("Usage limit per customer must be greater than 0");
    }
  }

  // Validate voucher update data
  protected validateUpdateData(data: UpdateVoucherData): void {
    if (data.code !== undefined && !data.code?.trim()) {
      throw new Error("Voucher code cannot be empty");
    }
    if (data.name !== undefined && !data.name?.trim()) {
      throw new Error("Voucher name cannot be empty");
    }
    if (data.value !== undefined && data.value <= 0) {
      throw new Error("Voucher value must be greater than 0");
    }
    if (data.type === "percentage" && data.value !== undefined && (data.value < 1 || data.value > 100)) {
      throw new Error("Percentage voucher value must be between 1 and 100");
    }
    if (data.valid_from && data.valid_until && data.valid_from >= data.valid_until) {
      throw new Error("Valid from date must be before valid until date");
    }
    if (data.usage_limit_per_customer !== undefined && data.usage_limit_per_customer <= 0) {
      throw new Error("Usage limit per customer must be greater than 0");
    }
  }

  // Check if voucher code is unique within tenant
  async isCodeUnique(code: string, tenantId: number, excludeId?: string): Promise<boolean> {
    let query = db
      .select({ id: vouchers.id })
      .from(vouchers)
      .where(and(eq(vouchers.tenantId, tenantId), eq(vouchers.code, code)));

    if (excludeId) {
      query = query.where(sql`${vouchers.id} != ${excludeId}`);
    }

    const [existing] = await query;
    return !existing;
  }

  // Override create to check code uniqueness
  async create(data: CreateVoucherData): Promise<Voucher> {
    this.validateCreateData(data);

    // Check code uniqueness
    const isUnique = await this.isCodeUnique(data.code, data.tenantId);
    if (!isUnique) {
      throw new Error(`Voucher code "${data.code}" already exists for this tenant`);
    }

    return super.create(data);
  }

  // Override update to check code uniqueness
  async update(id: string, data: UpdateVoucherData): Promise<Voucher> {
    this.validateUpdateData(data);

    if (data.code) {
      const voucher = await this.getById(id);
      if (!voucher) {
        throw new Error("Voucher not found");
      }

      const isUnique = await this.isCodeUnique(data.code, voucher.tenantId, id);
      if (!isUnique) {
        throw new Error(`Voucher code "${data.code}" already exists for this tenant`);
      }
    }

    return super.update(id, data);
  }

  // Get vouchers by tenant with filters
  async getByTenantWithFilters(tenantId: number, filters: VoucherFilters = {}): Promise<Voucher[]> {
    return this.getAll({ ...filters, tenantId });
  }

  // Find voucher by code and tenant
  async findByCode(code: string, tenantId: number): Promise<Voucher | null> {
    const [voucher] = await db
      .select()
      .from(vouchers)
      .where(and(eq(vouchers.code, code), eq(vouchers.tenantId, tenantId)));

    return voucher || null;
  }

  // Get active vouchers for a customer (including public and assigned private vouchers)
  async getAvailableVouchersForCustomer(customerId: string, tenantId: number): Promise<Voucher[]> {
    const now = new Date();

    // Get public vouchers that are active and valid
    const publicVouchers = await db
      .select()
      .from(vouchers)
      .where(
        and(
          eq(vouchers.tenantId, tenantId),
          eq(vouchers.is_active, true),
          eq(vouchers.is_public, true),
          lte(vouchers.valid_from, now),
          gte(vouchers.valid_until, now)
        )
      );

    // Get private vouchers assigned to this customer
    const privateVouchers = await db
      .select({
        id: vouchers.id,
        tenantId: vouchers.tenantId,
        code: vouchers.code,
        name: vouchers.name,
        description: vouchers.description,
        type: vouchers.type,
        value: vouchers.value,
        currency: vouchers.currency,
        usage_limit: vouchers.usage_limit,
        usage_limit_per_customer: vouchers.usage_limit_per_customer,
        current_usage_count: vouchers.current_usage_count,
        valid_from: vouchers.valid_from,
        valid_until: vouchers.valid_until,
        is_active: vouchers.is_active,
        is_public: vouchers.is_public,
        auto_apply: vouchers.auto_apply,
        restrictions: vouchers.restrictions,
        created_by: vouchers.created_by,
        createdAt: vouchers.createdAt,
        updatedAt: vouchers.updatedAt,
      })
      .from(vouchers)
      .innerJoin(voucher_customer_assignments, eq(vouchers.id, voucher_customer_assignments.voucher_id))
      .where(
        and(
          eq(vouchers.tenantId, tenantId),
          eq(vouchers.is_active, true),
          eq(vouchers.is_public, false),
          eq(voucher_customer_assignments.customer_id, customerId),
          eq(voucher_customer_assignments.is_active, true),
          lte(vouchers.valid_from, now),
          gte(vouchers.valid_until, now)
        )
      );

    return [...publicVouchers, ...privateVouchers];
  }

  // Validate voucher for use
  async validateVoucherForUse(
    code: string,
    customerId: string,
    tenantId: number,
    orderAmount: number,
    context: {
      package_id?: string;
      class_id?: string;
      location_id?: string;
    } = {}
  ): Promise<VoucherValidationResult> {
    // Find voucher by code
    const voucher = await this.findByCode(code, tenantId);
    if (!voucher) {
      return { is_valid: false, error_message: "Voucher not found" };
    }

    // Check if voucher is active
    if (!voucher.is_active) {
      return { is_valid: false, error_message: "Voucher is not active" };
    }

    // Check validity period
    const now = new Date();
    if (now < voucher.valid_from || now > voucher.valid_until) {
      return { is_valid: false, error_message: "Voucher has expired or is not yet valid" };
    }

    // Check usage limits
    if (voucher.usage_limit && voucher.current_usage_count >= voucher.usage_limit) {
      return { is_valid: false, error_message: "Voucher usage limit exceeded" };
    }

    // Check per-customer usage limit
    const customerUsageCount = await this.getCustomerUsageCount(voucher.id, customerId);
    if (voucher.usage_limit_per_customer && customerUsageCount >= voucher.usage_limit_per_customer) {
      return { is_valid: false, error_message: "You have already used this voucher the maximum number of times" };
    }

    // Check if customer has access to private voucher
    if (!voucher.is_public) {
      const hasAccess = await this.customerHasAccessToVoucher(voucher.id, customerId);
      if (!hasAccess) {
        return { is_valid: false, error_message: "You do not have access to this voucher" };
      }
    }

    // Validate restrictions
    const restrictionValidation = await this.validateRestrictions(voucher, orderAmount, context, customerId);
    if (!restrictionValidation.is_valid) {
      return restrictionValidation;
    }

    // Calculate discount
    const discountAmount = this.calculateDiscount(voucher, orderAmount);
    const finalAmount = Math.max(0, orderAmount - discountAmount);

    return {
      is_valid: true,
      discount_amount: discountAmount,
      final_amount: finalAmount,
      voucher
    };
  }

  // Calculate discount amount based on voucher type and value
  private calculateDiscount(voucher: Voucher, orderAmount: number): number {
    switch (voucher.type) {
      case "percentage":
        const percentageDiscount = Math.round((orderAmount * voucher.value) / 100);
        // Apply max discount limit if set
        if (voucher.restrictions?.max_discount_amount) {
          return Math.min(percentageDiscount, voucher.restrictions.max_discount_amount);
        }
        return percentageDiscount;

      case "fixed_amount":
        return Math.min(voucher.value, orderAmount);

      case "free_shipping":
        // This would need to be implemented based on shipping calculation logic
        return 0; // Placeholder

      case "buy_x_get_y":
        // This would need more complex logic based on cart items
        return 0; // Placeholder

      default:
        return 0;
    }
  }

  // Validate voucher restrictions
  private async validateRestrictions(
    voucher: Voucher,
    orderAmount: number,
    context: { package_id?: string; class_id?: string; location_id?: string },
    customerId: string
  ): Promise<VoucherValidationResult> {
    const restrictions = voucher.restrictions || {};

    // Check minimum purchase amount
    if (restrictions.min_purchase_amount && orderAmount < restrictions.min_purchase_amount) {
      return {
        is_valid: false,
        error_message: `Minimum purchase amount of ${restrictions.min_purchase_amount} required`
      };
    }

    // Check location restrictions
    if (restrictions.applicable_locations && restrictions.applicable_locations.length > 0) {
      if (!context.location_id || !restrictions.applicable_locations.includes(context.location_id)) {
        return { is_valid: false, error_message: "Voucher not valid for this location" };
      }
    }

    // Check package restrictions
    if (restrictions.applicable_packages && restrictions.applicable_packages.length > 0) {
      if (!context.package_id || !restrictions.applicable_packages.includes(context.package_id)) {
        return { is_valid: false, error_message: "Voucher not valid for this package" };
      }
    }

    // Check excluded packages
    if (restrictions.exclude_packages && restrictions.exclude_packages.length > 0) {
      if (context.package_id && restrictions.exclude_packages.includes(context.package_id)) {
        return { is_valid: false, error_message: "Voucher cannot be used with this package" };
      }
    }

    // Check class restrictions
    if (restrictions.applicable_classes && restrictions.applicable_classes.length > 0) {
      if (!context.class_id || !restrictions.applicable_classes.includes(context.class_id)) {
        return { is_valid: false, error_message: "Voucher not valid for this class" };
      }
    }

    // Check excluded classes
    if (restrictions.exclude_classes && restrictions.exclude_classes.length > 0) {
      if (context.class_id && restrictions.exclude_classes.includes(context.class_id)) {
        return { is_valid: false, error_message: "Voucher cannot be used with this class" };
      }
    }

    // Check customer group restrictions
    if (restrictions.applicable_customer_groups && restrictions.applicable_customer_groups.length > 0) {
      const customer = await db
        .select({ pricingGroupId: customers.pricingGroupId })
        .from(customers)
        .where(eq(customers.id, customerId));

      if (!customer[0]?.pricingGroupId || !restrictions.applicable_customer_groups.includes(customer[0].pricingGroupId)) {
        return { is_valid: false, error_message: "Voucher not valid for your customer group" };
      }
    }

    // Check first-time customer restriction
    if (restrictions.first_time_customers_only) {
      const isFirstTime = await this.isFirstTimeCustomer(customerId);
      if (!isFirstTime) {
        return { is_valid: false, error_message: "Voucher only valid for first-time customers" };
      }
    }

    // Check existing customer restriction
    if (restrictions.existing_customers_only) {
      const isFirstTime = await this.isFirstTimeCustomer(customerId);
      if (isFirstTime) {
        return { is_valid: false, error_message: "Voucher only valid for existing customers" };
      }
    }

    return { is_valid: true };
  }

  // Record voucher usage
  async recordVoucherUsage(data: VoucherUsageData): Promise<VoucherUsage> {
    const usageId = createId();

    // Start transaction to update usage count and record usage
    return await db.transaction(async (tx) => {
      // Record the usage
      const [usage] = await tx
        .insert(voucher_usage)
        .values({
          id: usageId,
          ...data,
          used_at: new Date(),
        })
        .returning();

      // Increment usage count
      await tx
        .update(vouchers)
        .set({
          current_usage_count: sql`${vouchers.current_usage_count} + 1`,
          updatedAt: new Date(),
        })
        .where(eq(vouchers.id, data.voucher_id));

      return usage;
    });
  }

  // Get customer usage count for a voucher
  async getCustomerUsageCount(voucherId: string, customerId: string): Promise<number> {
    const [result] = await db
      .select({ count: sql<number>`count(*)` })
      .from(voucher_usage)
      .where(and(eq(voucher_usage.voucher_id, voucherId), eq(voucher_usage.customer_id, customerId)));

    return result.count;
  }

  // Check if customer has access to private voucher
  async customerHasAccessToVoucher(voucherId: string, customerId: string): Promise<boolean> {
    const [assignment] = await db
      .select({ id: voucher_customer_assignments.id })
      .from(voucher_customer_assignments)
      .where(
        and(
          eq(voucher_customer_assignments.voucher_id, voucherId),
          eq(voucher_customer_assignments.customer_id, customerId),
          eq(voucher_customer_assignments.is_active, true)
        )
      );

    return !!assignment;
  }

  // Check if customer is first-time customer
  async isFirstTimeCustomer(customerId: string): Promise<boolean> {
    // This would need to be implemented based on order/booking history
    // For now, return false as placeholder
    const [usage] = await db
      .select({ id: voucher_usage.id })
      .from(voucher_usage)
      .where(eq(voucher_usage.customer_id, customerId))
      .limit(1);

    return !usage;
  }

  // Assign voucher to customer (for private vouchers)
  async assignVoucherToCustomer(
    voucherId: string,
    customerId: string,
    assignedBy: string,
    expiresAt?: Date
  ): Promise<VoucherCustomerAssignment> {
    const assignmentId = createId();

    const [assignment] = await db
      .insert(voucher_customer_assignments)
      .values({
        id: assignmentId,
        voucher_id: voucherId,
        customer_id: customerId,
        assigned_by: assignedBy,
        expires_at: expiresAt,
        assigned_at: new Date(),
      })
      .returning();

    return assignment;
  }

  // Get voucher usage statistics
  async getVoucherStats(voucherId: string): Promise<{
    total_usage: number;
    unique_customers: number;
    total_discount_given: number;
    average_discount: number;
  }> {
    const [stats] = await db
      .select({
        total_usage: sql<number>`count(*)`,
        unique_customers: sql<number>`count(distinct ${voucher_usage.customer_id})`,
        total_discount_given: sql<number>`sum(${voucher_usage.discount_amount})`,
        average_discount: sql<number>`avg(${voucher_usage.discount_amount})`,
      })
      .from(voucher_usage)
      .where(eq(voucher_usage.voucher_id, voucherId));

    return {
      total_usage: stats.total_usage || 0,
      unique_customers: stats.unique_customers || 0,
      total_discount_given: stats.total_discount_given || 0,
      average_discount: Math.round(stats.average_discount || 0),
    };
  }

  // Search vouchers
  async searchVouchers(query: string, tenantId?: number): Promise<Voucher[]> {
    return super.search(query, ['code', 'name', 'description'], { tenantId });
  }
}


// Export singleton instance
export const VoucherService = new VoucherServiceClass();