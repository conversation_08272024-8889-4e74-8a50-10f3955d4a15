"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Plus, 
  Search, 
  Filter, 
  More<PERSON><PERSON><PERSON><PERSON>, 
  Edit, 
  Trash2, 
  Eye, 
  Copy,
  Ticket,
  TrendingUp,
  Users,
  DollarSign,
  Calendar,
  ToggleLeft,
  ToggleRight,
  Loader2,
  AlertCircle,
  CheckCircle2
} from "lucide-react";
import { VoucherForm } from "@/components/forms/voucher-form";
import { 
  useVouchersByTenant, 
  useDeleteVoucher, 
  useToggleVoucherActive,
  useVoucherStats 
} from "@/lib/hooks/queries/use-voucher-queries";
import { Voucher } from "@/lib/db/schema";
import { toast } from "sonner";
import { format } from "date-fns";
// import { useTenant } from "@/lib/hooks/use-tenants";

export default function VouchersPage() {
  // Hardcode tenantId untuk testing - nanti bisa diganti dengan proper tenant context
  const tenantId = 1;
  // const { tenant } = useTenant(tenantId); // Tidak digunakan untuk sementara
  const [searchQuery, setSearchQuery] = useState("");
  const [typeFilter, setTypeFilter] = useState<string>("all");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [selectedVoucher, setSelectedVoucher] = useState<Voucher | undefined>();
  const [isStatsOpen, setIsStatsOpen] = useState(false);
  const [selectedVoucherForStats, setSelectedVoucherForStats] = useState<string>("");

  // Queries
  const {
    data: vouchers = [],
    isLoading,
    error
  } = useVouchersByTenant(tenantId, {
    filters: {
      search: searchQuery || undefined,
      type: typeFilter !== "all" ? typeFilter : undefined,
      is_active: statusFilter === "active" ? true : statusFilter === "inactive" ? false : undefined,
    }
  });

  const { data: voucherStats } = useVoucherStats(tenantId);
  const deleteVoucherMutation = useDeleteVoucher();
  const toggleActiveMutation = useToggleVoucherActive();

  // Handlers
  const handleCreateVoucher = () => {
    setSelectedVoucher(undefined);
    setIsFormOpen(true);
  };

  const handleEditVoucher = (voucher: Voucher) => {
    setSelectedVoucher(voucher);
    setIsFormOpen(true);
  };

  const handleDeleteVoucher = async (voucher: Voucher) => {
    if (confirm(`Are you sure you want to delete voucher "${voucher.code}"?`)) {
      try {
        await deleteVoucherMutation.mutateAsync(voucher.id);
        toast.success("Voucher deleted successfully!");
      } catch (error) {
        toast.error("Failed to delete voucher");
      }
    }
  };

  const handleToggleActive = async (voucher: Voucher) => {
    try {
      await toggleActiveMutation.mutateAsync(voucher.id);
      toast.success(`Voucher ${voucher.is_active ? 'deactivated' : 'activated'} successfully!`);
    } catch (error) {
      toast.error("Failed to update voucher status");
    }
  };

  const handleCopyCode = (code: string) => {
    navigator.clipboard.writeText(code);
    toast.success("Voucher code copied to clipboard!");
  };

  const handleViewStats = (voucherId: string) => {
    setSelectedVoucherForStats(voucherId);
    setIsStatsOpen(true);
  };

  // Helper functions
  const getVoucherTypeLabel = (type: string) => {
    switch (type) {
      case "percentage": return "Percentage";
      case "fixed_amount": return "Fixed Amount";
      case "free_shipping": return "Free Shipping";
      case "buy_x_get_y": return "Buy X Get Y";
      default: return type;
    }
  };

  const getVoucherTypeBadgeColor = (type: string) => {
    switch (type) {
      case "percentage": return "bg-blue-100 text-blue-800";
      case "fixed_amount": return "bg-green-100 text-green-800";
      case "free_shipping": return "bg-purple-100 text-purple-800";
      case "buy_x_get_y": return "bg-orange-100 text-orange-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const formatVoucherValue = (voucher: Voucher) => {
    switch (voucher.type) {
      case "percentage":
        return `${voucher.value}%`;
      case "fixed_amount":
        return `$${(voucher.value / 100).toFixed(2)}`;
      case "free_shipping":
        return "Free Shipping";
      default:
        return voucher.value.toString();
    }
  };

  const isVoucherExpired = (voucher: Voucher) => {
    return new Date() > new Date(voucher.valid_until);
  };

  const isVoucherNotYetValid = (voucher: Voucher) => {
    return new Date() < new Date(voucher.valid_from);
  };



  if (error) {
    return (
      <div className="container mx-auto py-6">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Failed to load vouchers. Please try again later.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <Ticket className="h-8 w-8" />
            Vouchers
          </h1>
          <p className="text-muted-foreground">
            Manage discount vouchers and promotional codes
          </p>
        </div>
        <Button onClick={handleCreateVoucher} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Add Voucher
        </Button>
      </div>

      {/* Stats Cards */}
      {voucherStats && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Vouchers</CardTitle>
              <Ticket className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{voucherStats.total || 0}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Vouchers</CardTitle>
              <CheckCircle2 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{voucherStats.active || 0}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Usage</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{voucherStats.total_usage || 0}</div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Savings</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                ${((voucherStats.total_discount || 0) / 100).toFixed(2)}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-4 w-4" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search vouchers by code, name, or description..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            {/* Type Filter */}
            <Select value={typeFilter} onValueChange={setTypeFilter}>
              <SelectTrigger className="w-full md:w-[200px]">
                <SelectValue placeholder="Filter by type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="percentage">Percentage</SelectItem>
                <SelectItem value="fixed_amount">Fixed Amount</SelectItem>
                <SelectItem value="free_shipping">Free Shipping</SelectItem>
                <SelectItem value="buy_x_get_y">Buy X Get Y</SelectItem>
              </SelectContent>
            </Select>

            {/* Status Filter */}
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full md:w-[200px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Vouchers Table */}
      <Card>
        <CardHeader>
          <CardTitle>Vouchers</CardTitle>
          <CardDescription>
            {vouchers.length} voucher{vouchers.length !== 1 ? 's' : ''} found
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">Loading vouchers...</span>
            </div>
          ) : vouchers.length === 0 ? (
            <div className="text-center py-8">
              <Ticket className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No vouchers found</h3>
              <p className="text-gray-500 mb-4">
                {searchQuery || typeFilter !== "all" || statusFilter !== "all"
                  ? "Try adjusting your filters or search terms."
                  : "Get started by creating your first voucher."}
              </p>
              {!searchQuery && typeFilter === "all" && statusFilter === "all" && (
                <Button onClick={handleCreateVoucher}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Your First Voucher
                </Button>
              )}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Code</TableHead>
                    <TableHead>Name</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Value</TableHead>
                    <TableHead>Usage</TableHead>
                    <TableHead>Valid Period</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {vouchers.map((voucher) => (
                    <TableRow key={voucher.id}>
                      {/* Code */}
                      <TableCell className="font-mono font-medium">
                        <div className="flex items-center gap-2">
                          <span>{voucher.code}</span>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleCopyCode(voucher.code)}
                            className="h-6 w-6 p-0"
                          >
                            <Copy className="h-3 w-3" />
                          </Button>
                        </div>
                      </TableCell>

                      {/* Name */}
                      <TableCell>
                        <div>
                          <div className="font-medium">{voucher.name}</div>
                          {voucher.description && (
                            <div className="text-sm text-gray-500 truncate max-w-[200px]">
                              {voucher.description}
                            </div>
                          )}
                        </div>
                      </TableCell>

                      {/* Type */}
                      <TableCell>
                        <Badge className={getVoucherTypeBadgeColor(voucher.type)}>
                          {getVoucherTypeLabel(voucher.type)}
                        </Badge>
                      </TableCell>

                      {/* Value */}
                      <TableCell className="font-medium">
                        {formatVoucherValue(voucher)}
                      </TableCell>

                      {/* Usage */}
                      <TableCell>
                        <div className="text-sm">
                          <div>{voucher.current_usage_count || 0} used</div>
                          <div className="text-gray-500">
                            {voucher.usage_limit ? `of ${voucher.usage_limit}` : 'unlimited'}
                          </div>
                        </div>
                      </TableCell>

                      {/* Valid Period */}
                      <TableCell>
                        <div className="text-sm">
                          <div className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            {format(new Date(voucher.valid_from), 'MMM dd')} - {format(new Date(voucher.valid_until), 'MMM dd, yyyy')}
                          </div>
                          {isVoucherExpired(voucher) && (
                            <Badge variant="destructive" className="text-xs mt-1">
                              Expired
                            </Badge>
                          )}
                          {isVoucherNotYetValid(voucher) && (
                            <Badge variant="secondary" className="text-xs mt-1">
                              Not Yet Active
                            </Badge>
                          )}
                        </div>
                      </TableCell>

                      {/* Status */}
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {voucher.is_active ? (
                            <Badge variant="default" className="bg-green-100 text-green-800">
                              <CheckCircle2 className="h-3 w-3 mr-1" />
                              Active
                            </Badge>
                          ) : (
                            <Badge variant="secondary">
                              <AlertCircle className="h-3 w-3 mr-1" />
                              Inactive
                            </Badge>
                          )}
                          {!voucher.is_public && (
                            <Badge variant="outline" className="text-xs">
                              Private
                            </Badge>
                          )}
                        </div>
                      </TableCell>

                      {/* Actions */}
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => handleViewStats(voucher.id)}>
                              <Eye className="mr-2 h-4 w-4" />
                              View Stats
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleEditVoucher(voucher)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleToggleActive(voucher)}>
                              {voucher.is_active ? (
                                <>
                                  <ToggleLeft className="mr-2 h-4 w-4" />
                                  Deactivate
                                </>
                              ) : (
                                <>
                                  <ToggleRight className="mr-2 h-4 w-4" />
                                  Activate
                                </>
                              )}
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => handleDeleteVoucher(voucher)}
                              className="text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Voucher Form Dialog */}
      <VoucherForm
        open={isFormOpen}
        onClose={() => {
          setIsFormOpen(false);
          setSelectedVoucher(undefined);
        }}
        title={selectedVoucher ? "Edit Voucher" : "Add New Voucher"}
        description={selectedVoucher ? "Update voucher details and settings" : "Create a new voucher with discount settings and restrictions"}
        voucher={selectedVoucher}
        tenantId={tenantId}
      />

      {/* Voucher Stats Dialog */}
      <VoucherStatsDialog
        open={isStatsOpen}
        onClose={() => {
          setIsStatsOpen(false);
          setSelectedVoucherForStats("");
        }}
        voucherId={selectedVoucherForStats}
      />
    </div>
  );
}

// Voucher Stats Dialog Component
function VoucherStatsDialog({
  open,
  onClose,
  voucherId
}: {
  open: boolean;
  onClose: () => void;
  voucherId: string;
}) {
  const { data: stats, isLoading } = useVoucherStats(1); // Hardcode tenantId
  const voucher = stats?.voucher;

  // Helper functions untuk dialog
  const getVoucherTypeBadgeColor = (type: string) => {
    switch (type) {
      case "percentage": return "bg-blue-100 text-blue-800";
      case "fixed_amount": return "bg-green-100 text-green-800";
      case "free_shipping": return "bg-purple-100 text-purple-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const getVoucherTypeLabel = (type: string) => {
    switch (type) {
      case "percentage": return "Percentage";
      case "fixed_amount": return "Fixed Amount";
      case "free_shipping": return "Free Shipping";
      default: return type;
    }
  };

  const formatVoucherValue = (voucher: any) => {
    switch (voucher.type) {
      case "percentage": return `${voucher.value}%`;
      case "fixed_amount": return `$${(voucher.value / 100).toFixed(2)}`;
      case "free_shipping": return "Free Shipping";
      default: return voucher.value.toString();
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Voucher Statistics
          </DialogTitle>
          <DialogDescription>
            Usage statistics and performance metrics
          </DialogDescription>
        </DialogHeader>

        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Loading statistics...</span>
          </div>
        ) : stats ? (
          <div className="space-y-6">
            {/* Voucher Info */}
            {voucher && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">{voucher.name}</CardTitle>
                  <CardDescription>
                    Code: <span className="font-mono font-medium">{voucher.code}</span>
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <div className="text-sm text-gray-500">Type</div>
                      <Badge className={getVoucherTypeBadgeColor(voucher.type)}>
                        {getVoucherTypeLabel(voucher.type)}
                      </Badge>
                    </div>
                    <div>
                      <div className="text-sm text-gray-500">Value</div>
                      <div className="font-medium">{formatVoucherValue(voucher)}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Usage Statistics */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Total Usage</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.total_usage}</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Unique Customers</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{stats.unique_customers}</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Total Discount</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    ${(stats.total_discount_given / 100).toFixed(2)}
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Avg. Discount</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    ${(stats.average_discount / 100).toFixed(2)}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Usage Progress */}
            {voucher?.usage_limit && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Usage Progress</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Used: {stats.total_usage}</span>
                      <span>Limit: {voucher.usage_limit}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full"
                        style={{
                          width: `${Math.min((stats.total_usage / voucher.usage_limit) * 100, 100)}%`
                        }}
                      ></div>
                    </div>
                    <div className="text-xs text-gray-500">
                      {((stats.total_usage / voucher.usage_limit) * 100).toFixed(1)}% used
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        ) : (
          <div className="text-center py-8">
            <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No statistics available</h3>
            <p className="text-gray-500">
              Statistics will appear once the voucher has been used.
            </p>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
