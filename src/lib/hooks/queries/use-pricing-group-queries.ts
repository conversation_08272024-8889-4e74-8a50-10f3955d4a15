import { useMutation, useQueryClient } from '@tanstack/react-query';
import { PricingGroup, NewPricingGroup } from '@/lib/db/schema';
import { createEntityHooks, BaseApiInterface } from '@/lib/core/base-query-hooks';
import { QueryOptions } from '@/lib/core/base-service';

// API interface implementation
const pricingGroupApi: BaseApiInterface<PricingGroup, Omit<NewPricingGroup, "id" | "createdAt" | "updatedAt">, Partial<NewPricingGroup>> = {
  getAll: async (options?: QueryOptions): Promise<PricingGroup[]> => {
    const params = new URLSearchParams();
    if (options?.tenantId) params.append('tenantId', options.tenantId.toString());
    if (options?.limit) params.append('limit', options.limit.toString());
    if (options?.offset) params.append('offset', options.offset.toString());
    if (options?.filters?.search) params.append('search', options.filters.search);
    if (options?.filters?.isActive !== undefined) params.append('activeOnly', options.filters.isActive.toString());
    if (options?.filters?.isDefault !== undefined) params.append('defaultOnly', options.filters.isDefault.toString());
    if (options?.filters?.hasDiscount !== undefined) params.append('hasDiscount', options.filters.hasDiscount.toString());

    const response = await fetch(`/api/pricing-groups?${params}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch pricing groups: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data || [];
  },

  getById: async (id: string): Promise<PricingGroup> => {
    const response = await fetch(`/api/pricing-groups/${id}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch pricing group: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data;
  },

  getByTenant: async (tenantId: number, options?: QueryOptions): Promise<PricingGroup[]> => {
    return pricingGroupApi.getAll({ ...options, tenantId });
  },

  create: async (data: Omit<NewPricingGroup, "id" | "createdAt" | "updatedAt">): Promise<PricingGroup> => {
    const response = await fetch('/api/pricing-groups', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to create pricing group');
    }
    const result = await response.json();
    return result.data;
  },

  update: async ({ id, data }: { id: string; data: Partial<NewPricingGroup> }): Promise<PricingGroup> => {
    const response = await fetch(`/api/pricing-groups/${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to update pricing group');
    }
    const result = await response.json();
    return result.data;
  },

  delete: async (id: string): Promise<PricingGroup> => {
    const response = await fetch(`/api/pricing-groups/${id}`, {
      method: 'DELETE',
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to delete pricing group');
    }
    const result = await response.json();
    return result.data;
  },

  bulkOperation: async (data: { ids: string[]; action: string }): Promise<PricingGroup[]> => {
    const response = await fetch('/api/pricing-groups', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to perform bulk operation');
    }
    const result = await response.json();
    return result.data;
  },

  search: async (query: string, options?: QueryOptions): Promise<PricingGroup[]> => {
    return pricingGroupApi.getAll({ ...options, filters: { search: query } });
  },

  getStats: async (tenantId?: number) => {
    const params = tenantId ? `?tenantId=${tenantId}` : '';
    const response = await fetch(`/api/pricing-groups/stats${params}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch pricing group stats: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data;
  },
};

// Create base hooks using the factory
const baseHooks = createEntityHooks('pricing-groups', pricingGroupApi);

// Export base hooks with pricing group-specific names
export const usePricingGroups = baseHooks.useEntities;
export const usePricingGroup = baseHooks.useEntity;
export const usePricingGroupsByTenant = baseHooks.useEntitiesByTenant;
export const usePricingGroupStats = baseHooks.useEntityStats;
export const usePricingGroupSearch = baseHooks.useEntitySearch;
export const useCreatePricingGroup = baseHooks.useCreateEntity;
export const useUpdatePricingGroup = baseHooks.useUpdateEntity;
export const useDeletePricingGroup = baseHooks.useDeleteEntity;
export const useBulkPricingGroupOperation = baseHooks.useBulkOperation;

// Additional pricing group-specific hooks
export function useTogglePricingGroupActive() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string): Promise<PricingGroup> => {
      const response = await fetch(`/api/pricing-groups/${id}?action=toggle-active`, {
        method: 'PATCH',
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to toggle pricing group status');
      }
      const result = await response.json();
      return result.data;
    },
    onSuccess: (updatedPricingGroup) => {
      // Update cache with updated pricing group
      queryClient.setQueryData(baseHooks.queryKeys.detail(updatedPricingGroup.id), updatedPricingGroup);

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: baseHooks.queryKeys.lists() });
      if (updatedPricingGroup.tenantId) {
        queryClient.invalidateQueries({
          queryKey: baseHooks.queryKeys.byTenant(updatedPricingGroup.tenantId)
        });
        queryClient.invalidateQueries({
          queryKey: baseHooks.queryKeys.stats(updatedPricingGroup.tenantId)
        });
      }
      queryClient.invalidateQueries({ queryKey: baseHooks.queryKeys.stats() });
    },
  });
}

export function useTogglePricingGroupDefault() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string): Promise<PricingGroup> => {
      const response = await fetch(`/api/pricing-groups/${id}?action=toggle-default`, {
        method: 'PATCH',
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to toggle pricing group default status');
      }
      const result = await response.json();
      return result.data;
    },
    onSuccess: (updatedPricingGroup) => {
      // Update cache with updated pricing group
      queryClient.setQueryData(baseHooks.queryKeys.detail(updatedPricingGroup.id), updatedPricingGroup);

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: baseHooks.queryKeys.lists() });
      if (updatedPricingGroup.tenantId) {
        queryClient.invalidateQueries({
          queryKey: baseHooks.queryKeys.byTenant(updatedPricingGroup.tenantId)
        });
        queryClient.invalidateQueries({
          queryKey: baseHooks.queryKeys.stats(updatedPricingGroup.tenantId)
        });
      }
      queryClient.invalidateQueries({ queryKey: baseHooks.queryKeys.stats() });
    },
  });
}

export function useReorderPricingGroups() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ tenantId, items }: { tenantId: number; items: { id: string; sortOrder: number }[] }): Promise<PricingGroup[]> => {
      const response = await fetch('/api/pricing-groups/reorder', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ tenantId, items }),
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to reorder pricing groups');
      }
      const result = await response.json();
      return result.data;
    },
    onSuccess: (updatedPricingGroups, { tenantId }) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: baseHooks.queryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: baseHooks.queryKeys.byTenant(tenantId) });
    },
  });
}

export function useDuplicatePricingGroup() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, name }: { id: string; name?: string }): Promise<PricingGroup> => {
      const response = await fetch(`/api/pricing-groups/${id}/duplicate`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name }),
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to duplicate pricing group');
      }
      const result = await response.json();
      return result.data;
    },
    onSuccess: (newPricingGroup) => {
      // Update cache with new pricing group
      queryClient.setQueryData(baseHooks.queryKeys.detail(newPricingGroup.id), newPricingGroup);

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: baseHooks.queryKeys.lists() });
      if (newPricingGroup.tenantId) {
        queryClient.invalidateQueries({
          queryKey: baseHooks.queryKeys.byTenant(newPricingGroup.tenantId)
        });
        queryClient.invalidateQueries({
          queryKey: baseHooks.queryKeys.stats(newPricingGroup.tenantId)
        });
      }
      queryClient.invalidateQueries({ queryKey: baseHooks.queryKeys.stats() });
    },
  });
}

// Export query keys for external use
export const pricingGroupQueryKeys = baseHooks.queryKeys;
