# 🔧 CORS Troubleshooting & Configuration Guide untuk Next.js 15 SaaS

> **Panduan Lengkap**: Mengatasi masalah CORS di API publik dengan pendekatan yang mudah dipahami dan praktis

## 📋 Daftar Isi

1. [Root Cause Analysis](#root-cause-analysis)
2. [Konfigurasi CORS yang Benar](#konfigurasi-cors-yang-benar)
3. [Environment-specific Settings](#environment-specific-settings)
4. [Common CORS Headers](#common-cors-headers)
5. [Testing & Debugging](#testing--debugging)
6. [Best Practices](#best-practices)
7. [Troubleshooting Checklist](#troubleshooting-checklist)

---

## 🔍 Root Cause Analysis

### Mengapa Masalah CORS Terjadi?

CORS (Cross-Origin Resource Sharing) adalah mekanisme keamanan browser yang mencegah website dari domain berbeda mengakses resource tanpa izin. Di project Next.js 15 SaaS kita, masalah CORS terjadi karena **konflik antara multiple CORS configurations**:

<!-- 

 -->
#### 🚨 **Problem 1: Hardcoded Origins**
```typescript
// ❌ SALAH - Hardcoded di next.config.ts
{ key: "Access-Control-Allow-Origin", value: "http://localhost:3002" }

// Frontend berjalan di localhost:3001, tapi server hanya allow localhost:3002
```

#### 🚨 **Problem 2: Missing Headers**
```typescript
// ❌ SALAH - Headers tidak lengkap
"X-CSRF-Token, X-Requested-With, Accept, Content-Type"

// Missing: x-api-key, Cache-Control, Authorization, dll
```

#### 🚨 **Problem 3: Conflicting CORS Layers**
```
Browser Request → Next.js Config CORS → Custom withCORS Middleware → API Route
                     ↑                        ↑
                 Override headers         Override lagi
```

**Mengapa ini bermasalah?** Karena ada 2 layer CORS yang saling override, menyebabkan headers yang tidak konsisten dan preflight requests yang gagal.

---

## ⚙️ Konfigurasi CORS yang Benar

### 1. **Setup next.config.ts**

```typescript
// next.config.ts
import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  async headers() {
    return [
      {
        source: "/(.*)",
        headers: [
          { key: "Access-Control-Allow-Credentials", value: "true" },
          { 
            key: "Access-Control-Allow-Origin", 
            value: process.env.FRONTEND_URL || "http://localhost:3001" // ✅ Dynamic origin
          },
          {
            key: "Access-Control-Allow-Methods",
            value: "GET,DELETE,PATCH,POST,PUT,OPTIONS", // ✅ Include OPTIONS
          },
          {
            key: "Access-Control-Allow-Headers",
            value: [
              "X-CSRF-Token", "X-Requested-With", "Accept", "Accept-Version",
              "Content-Length", "Content-MD5", "Content-Type", "Date", "X-Api-Version",
              "Authorization", "X-API-Key", "x-api-key", // ✅ API authentication
              "Cache-Control", "cache-control", "Pragma", "Origin" // ✅ Caching headers
            ].join(", ")
          },
          // Security headers
          { key: "X-Frame-Options", value: "DENY" },
          { key: "X-Content-Type-Options", value: "nosniff" },
          { key: "Referrer-Policy", value: "strict-origin-when-cross-origin" },
          { key: "Permissions-Policy", value: "camera=(), microphone=(), geolocation=()" },
        ],
      },
    ];
  },
};

export default nextConfig;
```

**Mengapa konfigurasi ini bagus?**
- ✅ **Dynamic Origin**: Menggunakan environment variable, tidak hardcoded
- ✅ **Complete Headers**: Semua headers yang diperlukan untuk API publik
- ✅ **Security First**: Tetap menjaga security headers yang penting

### 2. **Konfigurasi src/lib/security/cors.config.ts**

```typescript
// src/lib/security/cors.config.ts
const defaultCORSConfig: CORSOptions = {
  origin: (origin: string) => {
    const allowedOrigins = [
      process.env.FRONTEND_URL,
      process.env.ADMIN_URL,
      ...(process.env.NODE_ENV === 'development' ? [
        'http://localhost:3000',
        'http://localhost:3001', // ✅ Frontend utama
        'http://localhost:3002', // ✅ Admin panel
        'http://localhost:3003', // ✅ Testing environment
        'https://localhost:3000',
        'https://localhost:3001',
        'https://localhost:3002',
        'https://localhost:3003'
      ] : [])
    ].filter(Boolean);

    if (!origin) return true; // Allow mobile apps, Postman, etc.
    return allowedOrigins.includes(origin);
  },
  methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
  allowedHeaders: [
    "Content-Type", "Authorization", "X-Tenant-ID", "X-Device-ID",
    "X-Client-Version", "X-Request-ID", "X-Forwarded-For", "User-Agent",
    "Accept", "Accept-Language", "Accept-Encoding",
    "X-API-Key", "x-api-key", // ✅ API keys
    "Cache-Control", "cache-control", "Pragma", "Expires", // ✅ Caching
    "Origin", "X-Requested-With" // ✅ CORS essentials
  ],
  exposedHeaders: [
    "X-Request-ID", "X-RateLimit-Limit", "X-RateLimit-Remaining",
    "X-RateLimit-Reset", "X-Total-Count", "X-Page-Count"
  ],
  credentials: true,
  maxAge: 86400, // 24 hours
};
```

### 3. **Implementasi OPTIONS Handler di API Routes**

```typescript
// src/app/api/public/v1/classes/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { withCORS } from '@/lib/security/cors.config';

// ✅ Handle CORS preflight requests
export const OPTIONS = withCORS(async (req: NextRequest) => {
  return new NextResponse(null, { status: 200 });
});

// ✅ Main API endpoint
export const GET = withCORS(async (req: NextRequest) => {
  // API logic here
});
```

**Mengapa perlu OPTIONS handler?** Browser mengirim preflight request (OPTIONS) sebelum actual request untuk memverifikasi CORS permissions.

---

## 🌍 Environment-specific Settings

### Development Environment (.env.local)

```bash
# .env.local
FRONTEND_URL=http://localhost:3001
ADMIN_URL=http://localhost:3002
NODE_ENV=development

# API Keys
NEXT_PUBLIC_CLASSES_API_KEY=pk_9b4d8a5f3e9ec298be21ac0589d0346d0289f717d9ca3841093498ef4530c64b
```

### Production Environment (.env.production)

```bash
# .env.production
FRONTEND_URL=https://yourdomain.com
ADMIN_URL=https://admin.yourdomain.com
NODE_ENV=production

# API Keys (gunakan yang berbeda untuk production)
NEXT_PUBLIC_CLASSES_API_KEY=pk_production_key_here
```

### Dynamic CORS Configuration

```typescript
// utils/cors-config.ts
export const getCORSOrigins = () => {
  const baseOrigins = [
    process.env.FRONTEND_URL,
    process.env.ADMIN_URL,
  ].filter(Boolean);

  if (process.env.NODE_ENV === 'development') {
    return [
      ...baseOrigins,
      'http://localhost:3000',
      'http://localhost:3001',
      'http://localhost:3002',
      'http://localhost:3003',
    ];
  }

  return baseOrigins;
};
```

**Mengapa pisahkan environment?** Karena development butuh flexibility (multiple localhost ports), sedangkan production butuh security yang ketat (specific domains only).

---

## 📝 Common CORS Headers

### Headers yang Wajib Ada

| Header | Purpose | Example |
|--------|---------|---------|
| `Access-Control-Allow-Origin` | Domain yang diizinkan | `http://localhost:3001` |
| `Access-Control-Allow-Methods` | HTTP methods yang diizinkan | `GET,POST,PUT,DELETE,OPTIONS` |
| `Access-Control-Allow-Headers` | Request headers yang diizinkan | `Content-Type,Authorization,x-api-key` |
| `Access-Control-Allow-Credentials` | Izinkan cookies/credentials | `true` |
| `Access-Control-Max-Age` | Cache preflight response | `86400` (24 hours) |

### Headers untuk API Authentication

```typescript
const apiHeaders = [
  "Authorization",        // Bearer tokens
  "X-API-Key",           // API key (uppercase)
  "x-api-key",           // API key (lowercase) - some clients use this
  "X-Tenant-ID",         // Multi-tenant identification
  "X-Client-Version",    // Client version tracking
];
```

### Headers untuk Caching & Performance

```typescript
const cachingHeaders = [
  "Cache-Control",       // Browser caching directives
  "cache-control",       // Lowercase variant
  "Pragma",             // HTTP/1.0 compatibility
  "Expires",            // Expiration date
  "If-None-Match",      // ETag validation
  "If-Modified-Since",  // Last-Modified validation
];
```

**Mengapa perlu kedua case (uppercase/lowercase)?** Karena different HTTP clients dan browsers kadang mengirim headers dengan case yang berbeda.

---

## 🧪 Testing & Debugging

### 1. **Testing dengan cURL Commands**

#### Test Preflight Request (OPTIONS)
```bash
curl -X OPTIONS \
  -H "Origin: http://localhost:3001" \
  -H "Access-Control-Request-Method: GET" \
  -H "Access-Control-Request-Headers: cache-control,x-api-key" \
  -v http://localhost:3000/api/public/v1/classes
```

#### Test Actual Request (GET)
```bash
curl -X GET \
  -H "Origin: http://localhost:3001" \
  -H "x-api-key: pk_9b4d8a5f3e9ec298be21ac0589d0346d0289f717d9ca3841093498ef4530c64b" \
  -H "Cache-Control: no-cache" \
  -v "http://localhost:3000/api/public/v1/classes?tenantId=1&limit=12"
```

#### Expected Response Headers
```
✅ Access-Control-Allow-Origin: http://localhost:3001
✅ Access-Control-Allow-Methods: GET,DELETE,PATCH,POST,PUT,OPTIONS
✅ Access-Control-Allow-Headers: ...,x-api-key,Cache-Control,...
✅ Access-Control-Allow-Credentials: true
```

### 2. **Debugging di Browser DevTools**

#### Step 1: Buka Network Tab
1. Buka DevTools (F12)
2. Go to Network tab
3. Clear existing requests
4. Make your API call

#### Step 2: Check Preflight Request
```
Request Method: OPTIONS
Status Code: 200 OK
Request Headers:
  Origin: http://localhost:3001
  Access-Control-Request-Method: GET
  Access-Control-Request-Headers: cache-control,x-api-key

Response Headers:
  Access-Control-Allow-Origin: http://localhost:3001 ✅
  Access-Control-Allow-Headers: ...,cache-control,x-api-key,... ✅
```

#### Step 3: Check Actual Request
```
Request Method: GET
Status Code: 200 OK
Request Headers:
  Origin: http://localhost:3001
  x-api-key: pk_...
  Cache-Control: no-cache

Response Headers:
  Access-Control-Allow-Origin: http://localhost:3001 ✅
  Content-Type: application/json ✅
```

### 3. **Common Error Messages & Solutions**

#### Error: "Access to fetch... has been blocked by CORS policy"
```
❌ Error: Response to preflight request doesn't pass access control check: 
   The 'Access-Control-Allow-Origin' header has a value 'http://localhost:3002' 
   that is not equal to the supplied origin.

✅ Solution: Update next.config.ts origin to match your frontend URL
```

#### Error: "Request header field x-api-key is not allowed"
```
❌ Error: Request header field x-api-key is not allowed by 
   Access-Control-Allow-Headers in preflight response.

✅ Solution: Add 'x-api-key' to allowedHeaders in CORS config
```

---

## 🎯 Best Practices

### 1. **Environment Variables untuk Origins**

```typescript
// ✅ GOOD - Dynamic dan flexible
const allowedOrigin = process.env.FRONTEND_URL || "http://localhost:3001";

// ❌ BAD - Hardcoded dan tidak flexible
const allowedOrigin = "http://localhost:3002";
```

### 2. **Security Considerations untuk Production**

```typescript
// Production CORS config
const productionCORS = {
  origin: [
    "https://yourdomain.com",
    "https://admin.yourdomain.com"
  ], // ✅ Specific domains only
  credentials: true, // ✅ Allow cookies
  maxAge: 86400, // ✅ Cache preflight for 24 hours
};

// ❌ JANGAN gunakan wildcard di production
const badProductionCORS = {
  origin: "*", // ❌ Security risk!
  credentials: true, // ❌ Tidak bisa kombinasi dengan wildcard
};
```

### 3. **Logging untuk Debugging**

```typescript
// Add logging to CORS middleware
export const withCORS = (handler: Function) => {
  return async (req: NextRequest) => {
    const origin = req.headers.get('origin');
    
    console.log('🔍 CORS Request:', {
      method: req.method,
      origin,
      headers: Object.fromEntries(req.headers.entries())
    });

    const response = await handler(req);
    
    console.log('🔍 CORS Response Headers:', {
      'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
      'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers'),
    });

    return response;
  };
};
```

### 4. **Centralized CORS Configuration**

```typescript
// lib/cors/config.ts
export const CORS_CONFIG = {
  development: {
    origins: ['http://localhost:3001', 'http://localhost:3002', 'http://localhost:3003'],
    credentials: true,
    maxAge: 300, // 5 minutes for faster development
  },
  production: {
    origins: [process.env.FRONTEND_URL, process.env.ADMIN_URL].filter(Boolean),
    credentials: true,
    maxAge: 86400, // 24 hours for better performance
  }
};
```

---

## ✅ Troubleshooting Checklist

### Step 1: Verify Environment
- [ ] Check `NODE_ENV` value
- [ ] Verify `FRONTEND_URL` environment variable
- [ ] Confirm frontend is running on expected port

### Step 2: Check CORS Configuration
- [ ] Verify `next.config.ts` has correct origin
- [ ] Check `cors.config.ts` includes all required headers
- [ ] Ensure API route has OPTIONS handler

### Step 3: Test with cURL
- [ ] Test OPTIONS preflight request
- [ ] Test actual GET request with headers
- [ ] Verify response headers are correct

### Step 4: Browser DevTools Check
- [ ] Open Network tab and clear requests
- [ ] Make API call from frontend
- [ ] Check preflight request (OPTIONS) status
- [ ] Verify actual request headers and response

### Step 5: Common Fixes
- [ ] Restart development server after config changes
- [ ] Clear browser cache and cookies
- [ ] Check for typos in header names (case-sensitive)
- [ ] Verify API key is correct and not expired

### Step 6: Advanced Debugging
- [ ] Add console.log to CORS middleware
- [ ] Check middleware execution order
- [ ] Verify no conflicting CORS libraries
- [ ] Test with different browsers

---

## 🚀 Quick Fix Commands

### Restart Development Server
```bash
# Kill existing process
pkill -f "next dev"

# Start fresh
npm run dev
```

### Test CORS Headers
```bash
# Quick CORS test
curl -H "Origin: http://localhost:3001" -I http://localhost:3000/api/public/v1/classes
```

### Clear Browser Cache
```javascript
// Run in browser console
localStorage.clear();
sessionStorage.clear();
location.reload(true);
```

---

## 📚 Resources & References

- [MDN CORS Documentation](https://developer.mozilla.org/en-US/docs/Web/HTTP/CORS)
- [Next.js Headers Configuration](https://nextjs.org/docs/api-reference/next.config.js/headers)
- [CORS Best Practices](https://web.dev/cross-origin-resource-sharing/)

---

**💡 Pro Tip**: Selalu test CORS configuration di multiple browsers (Chrome, Firefox, Safari) karena implementasi CORS bisa sedikit berbeda antar browser.

**🎯 Remember**: CORS adalah fitur keamanan, bukan bug. Pahami mengapa CORS ada dan konfigurasi dengan bijak untuk balance antara security dan functionality.
