import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { db } from "@/lib/db";
import { users, organizations, organizationMembers } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { hash } from "bcryptjs";
import { createId } from "@paralleldrive/cuid2";
import { withRateLimit } from "@/lib/auth/rate-limit";
import { generateEmailVerificationToken } from "@/lib/auth/utils";
import { sendVerificationEmail } from "@/lib/email/send";

// Schema untuk validasi registrasi
const registerSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(8, "Password must be at least 8 characters"),
  name: z.string().min(2, "Name must be at least 2 characters"),
  organizationName: z.string().min(2, "Organization name must be at least 2 characters").optional(),
});

export async function POST(request: NextRequest) {
  try {
    // Check rate limit
    const rateLimitResponse = await withRateLimit("register")(request);
    if (rateLimitResponse) {
      return rateLimitResponse;
    }

    const body = await request.json();
    const { email, password, name, organizationName } = registerSchema.parse(body);

    // Check if user already exists
    const [existingUser] = await db
      .select()
      .from(users)
      .where(eq(users.email, email))
      .limit(1);

    if (existingUser) {
      return NextResponse.json(
        { error: "User with this email already exists" },
        { status: 400 }
      );
    }

    // Hash password
    const hashedPassword = await hash(password, 12);

    // Create organization if provided
    let organizationId: string | undefined;
    if (organizationName) {
      // Generate unique slug
      const slug = organizationName
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, "-")
        .replace(/(^-|-$)/g, "");

      // Check if slug exists
      const [existingOrg] = await db
        .select()
        .from(organizations)
        .where(eq(organizations.slug, slug))
        .limit(1);

      if (existingOrg) {
        return NextResponse.json(
          { error: "Organization with this name already exists" },
          { status: 400 }
        );
      }

      // Create organization
      const [newOrg] = await db
        .insert(organizations)
        .values({
          id: createId(),
          name: organizationName,
          slug,
          description: `${organizationName} organization`,
        })
        .returning();

      organizationId = newOrg.id;
    }

    // Create user
    const [newUser] = await db
      .insert(users)
      .values({
        id: createId(),
        email,
        password: hashedPassword,
        name,
        organizationId,
        role: "user",
      })
      .returning();

    // Add user to organization if created
    if (organizationId) {
      await db
        .insert(organizationMembers)
        .values({
          id: createId(),
          organizationId,
          userId: newUser.id,
          role: "owner",
        });
    }

    // Generate email verification token
    const verificationToken = await generateEmailVerificationToken(email);

    // Send verification email
    try {
      await sendVerificationEmail(email, name, verificationToken);
    } catch (emailError) {
      console.error("Failed to send verification email:", emailError);
      // Don't fail registration if email fails
    }

    return NextResponse.json(
      {
        message: "User registered successfully",
        user: {
          id: newUser.id,
          email: newUser.email,
          name: newUser.name,
          role: newUser.role,
          organizationId: newUser.organizationId,
        },
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Registration error:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
