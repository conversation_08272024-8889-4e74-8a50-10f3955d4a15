"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { motion } from "framer-motion";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, MapPin, Phone, Building, CheckCircle } from "lucide-react";
import { Location, NewLocation } from "@/lib/db/schema";

// Validation schema
const locationSchema = z.object({
  tenantId: z.number().int().positive(),
  name: z.string().min(1, "name is required").max(255),
  addressLine1: z.string().min(1, "Address line 1 is required").max(255),
  addressLine2: z.string().max(255).optional(),
  city: z.string().min(1, "City is required").max(255),
  state: z.string().min(1, "State is required").max(255),
  country: z.string().min(1, "Country is required").max(255),
  postalCode: z.string().min(1, "Postal code is required").max(20),
  phoneNumber: z.string().max(30).optional(),
});

type LocationFormData = z.infer<typeof locationSchema>;

interface LocationFormProps {
  location?: Location;
  tenantId: number;
  onSubmit: (data: Omit<NewLocation, "id" | "createdAt" | "updatedAt">) => Promise<void>;
  onCancel?: () => void;
  isLoading?: boolean;
  className?: string;
}

export function LocationForm({
  location,
  tenantId,
  onSubmit,
  onCancel,
  isLoading = false,
  className = "",
}: LocationFormProps) {
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [isSuccess, setIsSuccess] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<LocationFormData>({
    resolver: zodResolver(locationSchema),
    defaultValues: {
      tenantId,
      name: location?.name || "",
      addressLine1: location?.addressLine1 || "",
      addressLine2: location?.addressLine2 || "",
      city: location?.city || "",
      state: location?.state || "",
      country: location?.country || "",
      postalCode: location?.postalCode || "",
      phoneNumber: location?.phoneNumber || "",
    },
  });

  const handleFormSubmit = async (data: LocationFormData) => {
    try {
      setSubmitError(null);
      setIsSuccess(false);

      await onSubmit(data);

      // Show success state briefly
      setIsSuccess(true);
      setTimeout(() => setIsSuccess(false), 2000);

      if (!location) {
        reset(); // Reset form only for new locations
      }
    } catch (error) {
      setSubmitError(error instanceof Error ? error.message : "Failed to save location");
      setIsSuccess(false);
    }
  };



  const isFormLoading = isLoading || isSubmitting;

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MapPin className="h-5 w-5" />
          {location ? "Edit Location" : "Add New Location"}
        </CardTitle>
        <CardDescription>
          {location 
            ? "Update the location information below." 
            : "Enter the details for the new location."
          }
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          {submitError && (
            <Alert variant="destructive">
              <AlertDescription>{submitError}</AlertDescription>
            </Alert>
          )}

          {/* Address Section */}
          <div className="space-y-4">
            <div className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300">
              <Building className="h-4 w-4" />
               Detail Information
            </div>
            
            <div className="grid grid-cols-1 gap-4">
              <div>
                <Label htmlFor="name">Name *</Label>
                <Input
                  id="name"
                  {...register("name")}
                  placeholder="123 Main Street"
                  disabled={isFormLoading}
                />
                {errors.name && (
                  <p className="text-sm text-red-600 mt-1">{errors.name.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="addressLine1">Address Line 1 *</Label>
                <Input
                  id="addressLine1"
                  {...register("addressLine1")}
                  placeholder="123 Main Street"
                  disabled={isFormLoading}
                />
                {errors.addressLine1 && (
                  <p className="text-sm text-red-600 mt-1">{errors.addressLine1.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="addressLine2">Address Line 2</Label>
                <Input
                  id="addressLine2"
                  {...register("addressLine2")}
                  placeholder="Apartment, suite, etc. (optional)"
                  disabled={isFormLoading}
                />
                {errors.addressLine2 && (
                  <p className="text-sm text-red-600 mt-1">{errors.addressLine2.message}</p>
                )}
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="city">City *</Label>
                  <Input
                    id="city"
                    {...register("city")}
                    placeholder="New York"
                    disabled={isFormLoading}
                  />
                  {errors.city && (
                    <p className="text-sm text-red-600 mt-1">{errors.city.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="state">State *</Label>
                  <Input
                    id="state"
                    {...register("state")}
                    placeholder="NY"
                    disabled={isFormLoading}
                  />
                  {errors.state && (
                    <p className="text-sm text-red-600 mt-1">{errors.state.message}</p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="country">Country *</Label>
                  <Input
                    id="country"
                    {...register("country")}
                    placeholder="United States"
                    disabled={isFormLoading}
                  />
                  {errors.country && (
                    <p className="text-sm text-red-600 mt-1">{errors.country.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="postalCode">Postal Code *</Label>
                  <Input
                    id="postalCode"
                    {...register("postalCode")}
                    placeholder="10001"
                    disabled={isFormLoading}
                  />
                  {errors.postalCode && (
                    <p className="text-sm text-red-600 mt-1">{errors.postalCode.message}</p>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Contact Section */}
          <div className="space-y-4">
            <div className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300">
              <Phone className="h-4 w-4" />
              Contact Information
            </div>
            
            <div>
              <Label htmlFor="phoneNumber">Phone Number</Label>
              <Input
                id="phoneNumber"
                {...register("phoneNumber")}
                placeholder="+****************"
                disabled={isFormLoading}
              />
              {errors.phoneNumber && (
                <p className="text-sm text-red-600 mt-1">{errors.phoneNumber.message}</p>
              )}
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex gap-3 pt-4">
            <motion.div
              className="flex-1"
              whileTap={{ scale: 0.98 }}
              animate={isSuccess ? { scale: [1, 1.02, 1] } : {}}
              transition={{ duration: 0.2 }}
            >
              <Button
                type="submit"
                disabled={isFormLoading}
                className={`w-full transition-all duration-300 ${
                  isSuccess
                    ? "bg-green-600 hover:bg-green-700 text-white"
                    : ""
                }`}
              >
                {isFormLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isSuccess && <CheckCircle className="mr-2 h-4 w-4" />}
                {isSuccess
                  ? "✓ Success!"
                  : location
                    ? "Update Location"
                    : "Create Location"
                }
              </Button>
            </motion.div>
            
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isFormLoading}
              >
                Cancel
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
