"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Plus, Search, Edit, Trash2, BookO<PERSON>, Filter } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { ClassForm } from "@/components/forms/class-form";
import {
  useClassSearch,
  useCreateClass,
  useUpdateClass,
  useDeleteClass,
  useClass,
  type ClassFormData,
} from "@/lib/hooks/queries/use-class-queries";
import { useClassCategoriesByTenant } from "@/lib/hooks/queries/use-class-category-queries";
import { useClassSubcategoriesByCategory } from "@/lib/hooks/queries/use-class-subcategory-queries";
import { type Class } from "@/lib/db/schema";
import { SuccessToast } from "@/components/ui/success-toast";

/**
 * ClassesManagement Component
 * 
 * Component untuk manage classes dengan full CRUD functionality.
 * Mengikuti pattern yang sama dengan class categories dan subcategories management.
 * 
 * Features:
 * - Search classes by name
 * - Filter by category and subcategory
 * - Create, edit, delete classes
 * - Responsive grid layout
 * - Success feedback dengan SuccessToast
 */

interface ClassesManagementProps {
  tenantId: number;
}

export function ClassesManagement({ tenantId }: ClassesManagementProps) {
  // State management
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategoryId, setSelectedCategoryId] = useState<string>("all");
  const [selectedSubcategoryId, setSelectedSubcategoryId] = useState<string>("all");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingClass, setEditingClass] = useState<Class | null>(null);
  const [editingClassId, setEditingClassId] = useState<string | null>(null);
  const [deletingClass, setDeletingClass] = useState<Class | null>(null);

  const [showSuccessToast, setShowSuccessToast] = useState(false);
  const [createdClass, setCreatedClass] = useState<Class | null>(null);

  // Get categories and subcategories for filters
  const { data: categories = [] } = useClassCategoriesByTenant(tenantId);
  const { data: subcategories = [] } = useClassSubcategoriesByCategory(
    selectedCategoryId === "all" ? "" : selectedCategoryId,
    tenantId
  );

  // Queries
  const {
    data: searchResult,
    isLoading,
    error,
    refetch,
  } = useClassSearch(
    tenantId,
    selectedCategoryId === "all" ? undefined : selectedCategoryId,
    selectedSubcategoryId === "all" ? undefined : selectedSubcategoryId,
    searchTerm,
    50,
    0
  );

  // Fetch detailed class data for editing (includes package pricing relationships)
  const { data: detailedClassData, isLoading: isLoadingDetailedClass } = useClass(editingClassId || "");

  // Mutations
  const createMutation = useCreateClass();
  const updateMutation = useUpdateClass();
  const deleteMutation = useDeleteClass();

  // Handlers
  const handleCreateClass = async (data: ClassFormData) => {
    try {
      const newClass = await createMutation.mutateAsync(data);

      setIsCreateDialogOpen(false);
      setCreatedClass(newClass);
      setShowSuccessToast(true);

      // Hide success toast after duration
      setTimeout(() => {
        setShowSuccessToast(false);
      }, 4000);

      return newClass;
    } catch (error) {
      console.error("Create class error:", error);
      throw error;
    }
  };

  const handleEditClass = async (data: ClassFormData) => {
    if (!editingClass) return;

    try {
      const updatedClass = await updateMutation.mutateAsync({
        id: editingClass.id,
        tenantId: editingClass.tenantId,
        data: {
          name: data.name,
          description: data.description,
          categoryId: data.categoryId,
          subcategoryId: data.subcategoryId,
          duration_value: data.duration_value,
          duration_unit: data.duration_unit,
          level_id: data.level_id,
          delivery_mode: data.delivery_mode,
          is_private: data.is_private,
          custom_cancellation_policy: data.custom_cancellation_policy,
          cancellation_policy_description: data.cancellation_policy_description,
          is_active: data.is_active,
          location_id: data.location_id,
        },
      });

      setEditingClass(null);
      setCreatedClass(updatedClass);
      setShowSuccessToast(true);

      // Hide success toast after duration
      setTimeout(() => {
        setShowSuccessToast(false);
      }, 4000);

      return updatedClass;
    } catch (error) {
      console.error("Update class error:", error);
      throw error;
    }
  };

  const handleDeleteClass = async () => {
    if (!deletingClass) return;

    try {
      await deleteMutation.mutateAsync({
        id: deletingClass.id,
        tenantId: deletingClass.tenantId,
      });

      setDeletingClass(null);
      setShowSuccessToast(true);

      // Hide success toast after duration
      setTimeout(() => {
        setShowSuccessToast(false);
      }, 4000);
    } catch (error) {
      console.error("Delete class error:", error);
    }
  };

  const handleCategoryChange = (categoryId: string) => {
    setSelectedCategoryId(categoryId);
    setSelectedSubcategoryId("all"); // Reset subcategory when category changes
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading classes...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-red-600 mb-4">Error loading classes: {String(error)}</p>
          <Button onClick={() => refetch()} variant="outline">
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  const classes = searchResult?.classes || [];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Classes</h1>
          <p className="text-muted-foreground">
            Manage your classes and organize them by categories
          </p>
        </div>
        
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Add Class
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Create Class</DialogTitle>
            </DialogHeader>
            <ClassForm
              tenantId={tenantId}
              onSubmit={handleCreateClass}
              onCancel={() => setIsCreateDialogOpen(false)}
              isLoading={createMutation.isPending}
            />
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        {/* Search */}
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search classes..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Category Filter */}
        <Select value={selectedCategoryId} onValueChange={handleCategoryChange}>
          <SelectTrigger className="w-full sm:w-[200px]">
            <Filter className="h-4 w-4 mr-2" />
            <SelectValue placeholder="All Categories" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Categories</SelectItem>
            {categories.map((category) => (
              <SelectItem key={category.id} value={category.id}>
                {category.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* Subcategory Filter */}
        <Select
          value={selectedSubcategoryId}
          onValueChange={setSelectedSubcategoryId}
          disabled={selectedCategoryId === "all"}
        >
          <SelectTrigger className="w-full sm:w-[200px]">
            <SelectValue placeholder="All Subcategories" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Subcategories</SelectItem>
            {subcategories.map((subcategory) => (
              <SelectItem key={subcategory.id} value={subcategory.id}>
                {subcategory.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Classes Grid */}
      {classes.length === 0 ? (
        <div className="text-center py-12">
          <BookOpen className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium mb-2">No classes found</h3>
          <p className="text-muted-foreground mb-4">
            {searchTerm || selectedCategoryId ? "Try adjusting your search or filters" : "Get started by creating your first class"}
          </p>
          {!searchTerm && selectedCategoryId === "all" && (
            <Button onClick={() => setIsCreateDialogOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Class
            </Button>
          )}
        </div>
      ) : (
        <motion.div 
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          <AnimatePresence>
            {classes.map((classItem: Class) => (
              <motion.div
                key={classItem.id}
                layout
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                transition={{ duration: 0.2 }}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Card className="hover:shadow-lg transition-all duration-200">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-lg font-medium mb-2">
                          {classItem.name}
                        </CardTitle>
                        <div className="flex flex-wrap gap-1 mb-2">
                          <Badge variant="secondary" className="text-xs">
                            {classItem.duration_value ? `${classItem.duration_value}min` : 'No duration'}
                          </Badge>
                          {classItem.delivery_mode && (
                            <Badge variant="outline" className="text-xs">
                              {classItem.delivery_mode}
                            </Badge>
                          )}
                          {classItem.is_private && (
                            <Badge variant="destructive" className="text-xs">
                              Private
                            </Badge>
                          )}
                          {!classItem.is_active && (
                            <Badge variant="secondary" className="text-xs">
                              Inactive
                            </Badge>
                          )}
                        </div>
                      </div>
                      <div className="flex gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            setEditingClass(classItem);
                            setEditingClassId(classItem.id);
                          }}
                          title="Edit Class"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setDeletingClass(classItem)}
                          title="Delete Class"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    {classItem.description && (
                      <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                        {classItem.description}
                      </p>
                    )}
                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <span>Created {new Date(classItem.createdAt).toLocaleDateString()}</span>
                      <Badge variant="secondary" className="text-xs">
                        ID: {classItem.id.slice(-8)}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </AnimatePresence>
        </motion.div>
      )}

      {/* Edit Dialog */}
      <Dialog open={!!editingClass} onOpenChange={() => {
        setEditingClass(null);
        setEditingClassId(null);
      }}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Class</DialogTitle>
          </DialogHeader>
          {editingClass && (
            <ClassForm
              class={detailedClassData || editingClass}
              tenantId={tenantId}
              onSubmit={handleEditClass}
              onCancel={() => {
                setEditingClass(null);
                setEditingClassId(null);
              }}
              isLoading={updateMutation.isPending || isLoadingDetailedClass}
            />
          )}
        </DialogContent>
      </Dialog>



      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deletingClass} onOpenChange={() => setDeletingClass(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Class</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{deletingClass?.name}"? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteClass}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Success Animations */}
      <SuccessToast
        isVisible={showSuccessToast}
        onClose={() => setShowSuccessToast(false)}
        title="Class Success!"
        description={createdClass ? 
          `Class "${createdClass.name}" has been processed successfully.` : 
          "Your class operation completed successfully."
        }
        type="general"
        action={{
          label: "View All Classes",
          onClick: () => setShowSuccessToast(false)
        }}
      />
    </div>
  );
}
