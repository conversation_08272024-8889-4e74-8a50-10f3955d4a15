import { Facility, facilities, NewFacility, tenants } from "./../db/schema";
import { db } from "../db";
import { and, desc, eq, like, ilike } from "drizzle-orm";
import { createId } from "@paralleldrive/cuid2";

/**
 * FacilityService
 * 
 * Service untuk manage facilities dengan full CRUD operations.
 * Mengikuti pola yang sama dengan LocationService dan EquipmentService.
 * 
 * Features:
 * - Tenant isolation untuk semua operations
 * - Full CRUD operations (Create, Read, Update, Delete)
 * - Search dan filtering capabilities
 * - Bulk operations support
 * - Statistics dan reporting
 */

export class FacilityService {
  // Get facilities by tenant ID
  static async getByTenantId(tenantId: number): Promise<Facility[]> {
    return await db
      .select()
      .from(facilities)
      .where(eq(facilities.tenantId, tenantId))
      .orderBy(desc(facilities.createdAt));
  }

  // Get facility by ID
  static async getById(id: string): Promise<Facility | null> {
    const [facility] = await db
      .select()
      .from(facilities)
      .where(eq(facilities.id, id))
      .limit(1);
    return facility || null;
  }

  // Create facility
  static async create(
    data: Omit<NewFacility, "id" | "createdAt" | "updatedAt">
  ): Promise<Facility> {
    const [facility] = await db
      .insert(facilities)
      .values({
        ...data,
        id: createId(),
      })
      .returning();
    return facility;
  }

  // Update facility
  static async update(
    id: string,
    data: Partial<Omit<NewFacility, "id" | "tenantId" | "createdAt">>
  ): Promise<Facility | null> {
    const [facility] = await db
      .update(facilities)
      .set({
        ...data,
        updatedAt: new Date(),
      })
      .where(eq(facilities.id, id))
      .returning();
    return facility || null;
  }

  // Delete facility
  static async delete(id: string): Promise<Facility> {
    // First check if facility exists
    const existingFacility = await this.getById(id);
    if (!existingFacility) {
      throw new Error("Facility not found");
    }

    // Delete the facility and return the deleted record
    const [deletedFacility] = await db
      .delete(facilities)
      .where(eq(facilities.id, id))
      .returning();

    if (!deletedFacility) {
      throw new Error("Failed to delete facility");
    }

    return deletedFacility;
  }

  // Get facility with tenant info
  static async getWithTenant(id: string) {
    const result = await db
      .select({
        facility: facilities,
        tenant: tenants,
      })
      .from(facilities)
      .leftJoin(tenants, eq(facilities.tenantId, tenants.id))
      .where(eq(facilities.id, id))
      .limit(1);
    return result[0] || null;
  }

  // Get all facilities (admin only)
  static async getAll(): Promise<Facility[]> {
    return await db.select().from(facilities).orderBy(desc(facilities.createdAt));
  }

  // Search facilities
  static async searchFacilities(query: string, tenantId?: number): Promise<Facility[]> {
    const conditions = [
      ilike(facilities.name, `%${query}%`)
    ];

    if (tenantId) {
      conditions.push(eq(facilities.tenantId, tenantId));
    }

    return await db
      .select()
      .from(facilities)
      .where(and(...conditions))
      .orderBy(desc(facilities.createdAt));
  }

  // Get facilities by name
  static async getFacilitiesByName(name: string, tenantId?: number): Promise<Facility[]> {
    const conditions = [eq(facilities.name, name)];
    
    if (tenantId) {
      conditions.push(eq(facilities.tenantId, tenantId));
    }

    return await db
      .select()
      .from(facilities)
      .where(and(...conditions))
      .orderBy(desc(facilities.createdAt));
  }

  // Get active facilities by tenant
  static async getActiveFacilitiesByTenant(tenantId: number): Promise<Facility[]> {
    return await db
      .select()
      .from(facilities)
      .where(and(
        eq(facilities.tenantId, tenantId),
        eq(facilities.isActive, true)
      ))
      .orderBy(desc(facilities.createdAt));
  }

  // Bulk create facilities
  static async bulkCreate(
    facilitiesData: Omit<NewFacility, "id" | "createdAt" | "updatedAt">[]
  ): Promise<Facility[]> {
    const facilitiesWithIds = facilitiesData.map((data) => ({
      ...data,
      id: createId(),
    }));

    return await db.insert(facilities).values(facilitiesWithIds).returning();
  }

  // Get facility statistics
  static async getStats(tenantId: number) {
    const [totalFacilities] = await db
      .select({ count: facilities.id })
      .from(facilities)
      .where(eq(facilities.tenantId, tenantId));

    const [activeFacilities] = await db
      .select({ count: facilities.id })
      .from(facilities)
      .where(and(
        eq(facilities.tenantId, tenantId),
        eq(facilities.isActive, true)
      ));

    const [inactiveFacilities] = await db
      .select({ count: facilities.id })
      .from(facilities)
      .where(and(
        eq(facilities.tenantId, tenantId),
        eq(facilities.isActive, false)
      ));

    return {
      totalFacilities: totalFacilities?.count || 0,
      activeFacilities: activeFacilities?.count || 0,
      inactiveFacilities: inactiveFacilities?.count || 0,
    };
  }

  // Toggle facility active status
  static async toggleActiveStatus(id: string): Promise<Facility | null> {
    const facility = await this.getById(id);
    if (!facility) {
      throw new Error("Facility not found");
    }

    return await this.update(id, {
      isActive: !facility.isActive,
    });
  }

  // Bulk update facility status
  static async bulkUpdateStatus(
    ids: string[],
    isActive: boolean
  ): Promise<Facility[]> {
    const updatedFacilities = [];
    
    for (const id of ids) {
      const facility = await this.update(id, { isActive });
      if (facility) {
        updatedFacilities.push(facility);
      }
    }

    return updatedFacilities;
  }

  // Format facility for display
  static formatFacility(facility: Facility): string {
    const parts = [
      facility.name,
      facility.description,
    ].filter(Boolean);

    return parts.join(" - ");
  }

  // Validate facility data
  static validateFacilityData(data: Partial<NewFacility>): string[] {
    const errors: string[] = [];

    if (data.name && data.name.length > 255) {
      errors.push("Facility name must be 255 characters or less");
    }

    if (data.description && data.description.length > 500) {
      errors.push("Facility description must be 500 characters or less");
    }

    return errors;
  }
}
