import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { CustomerService } from "@/lib/services/customer.service";

const customerService = new CustomerService();

// GET /api/customers/stats - Get customer statistics
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const tenantIdParam = searchParams.get("tenantId");

    let tenantId: number | undefined;
    if (tenantIdParam) {
      const tenantIdNum = parseInt(tenantIdParam);
      if (isNaN(tenantIdNum)) {
        return NextResponse.json(
          { error: "Invalid tenant ID" },
          { status: 400 }
        );
      }
      tenantId = tenantIdNum;
    }

    const stats = await customerService.getCustomerStats(tenantId);

    return NextResponse.json(
      { data: stats },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error fetching customer stats:", error);
    return NextResponse.json(
      { error: "Failed to fetch customer stats" },
      { status: 500 }
    );
  }
}
