"use client";

import { useQuery, useMutation, useQueryClient, useInfiniteQuery } from "@tanstack/react-query";
import { User, NewUser } from "@/lib/db/schema";

// Import global query keys
import { userKeys, invalidationPatterns } from "./query-keys";

// API Functions
const userApi = {
  getProfile: async (): Promise<User> => {
    const response = await fetch('/api/user/profile');
    if (!response.ok) {
      throw new Error(`Failed to fetch user profile: ${response.statusText}`);
    }
    return response.json();
  },

  getById: async (id: string): Promise<User> => {
    const response = await fetch(`/api/users/${id}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch user: ${response.statusText}`);
    }
    return response.json();
  },

  getAll: async (filters?: {
    role?: string;
    organizationId?: number;
    tenantId?: number;
    search?: string;
    page?: number;
    limit?: number;
  }): Promise<{ users: User[]; total: number; page: number; limit: number }> => {
    const params = new URLSearchParams();
    if (filters?.role) params.append('role', filters.role);
    if (filters?.organizationId) params.append('organizationId', filters.organizationId.toString());
    if (filters?.tenantId) params.append('tenantId', filters.tenantId.toString());
    if (filters?.search) params.append('search', filters.search);
    if (filters?.page) params.append('page', filters.page.toString());
    if (filters?.limit) params.append('limit', filters.limit.toString());

    const response = await fetch(`/api/users?${params}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch users: ${response.statusText}`);
    }
    return response.json();
  },

  create: async (data: Omit<NewUser, "id" | "createdAt" | "updatedAt">): Promise<User> => {
    const response = await fetch('/api/users', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to create user');
    }
    return response.json();
  },

  update: async ({ id, data }: { id: string; data: Partial<NewUser> }): Promise<User> => {
    const response = await fetch(`/api/users/${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to update user');
    }
    return response.json();
  },

  delete: async (id: string): Promise<void> => {
    const response = await fetch(`/api/users/${id}`, {
      method: 'DELETE',
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to delete user');
    }
  },

  updateProfile: async (data: Partial<NewUser>): Promise<User> => {
    const response = await fetch('/api/user/profile', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to update profile');
    }
    return response.json();
  },

  getUserOrganizations: async (userId: string) => {
    const response = await fetch(`/api/users/${userId}/organizations`);
    if (!response.ok) {
      throw new Error('Failed to fetch user organizations');
    }
    return response.json();
  },

  getUserTenants: async (userId: string) => {
    const response = await fetch(`/api/users/${userId}/tenants`);
    if (!response.ok) {
      throw new Error('Failed to fetch user tenants');
    }
    return response.json();
  },

  getStats: async (): Promise<{
    totalUsers: number;
    activeUsers: number;
    adminUsers: number;
    recentSignups: number;
  }> => {
    const response = await fetch('/api/users/stats');
    if (!response.ok) {
      throw new Error('Failed to fetch user stats');
    }
    return response.json();
  },

  search: async (query: string): Promise<User[]> => {
    const response = await fetch(`/api/users/search?q=${encodeURIComponent(query)}`);
    if (!response.ok) {
      throw new Error('Failed to search users');
    }
    return response.json();
  },
};

// Query Hooks
export function useUserProfile() {
  return useQuery({
    queryKey: userKeys.profile(),
    queryFn: userApi.getProfile,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error: any) => {
      if (error?.status === 401) return false;
      return failureCount < 3;
    },
  });
}

export function useUser(id: string) {
  return useQuery({
    queryKey: userKeys.detail(id),
    queryFn: () => userApi.getById(id),
    enabled: !!id,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

export function useUsers(filters?: {
  role?: string;
  organizationId?: number;
  tenantId?: number;
  search?: string;
}) {
  return useQuery({
    queryKey: userKeys.list(filters || {}),
    queryFn: () => userApi.getAll(filters),
    staleTime: 1 * 60 * 1000, // 1 minute
  });
}

export function useInfiniteUsers(filters?: {
  role?: string;
  organizationId?: number;
  tenantId?: number;
  search?: string;
}) {
  return useInfiniteQuery({
    queryKey: [...userKeys.list(filters || {}), 'infinite'],
    queryFn: ({ pageParam = 1 }) => userApi.getAll({ ...filters, page: pageParam, limit: 20 }),
    getNextPageParam: (lastPage) => {
      const hasMore = lastPage.users.length === lastPage.limit;
      return hasMore ? lastPage.page + 1 : undefined;
    },
    initialPageParam: 1,
  });
}

export function useUserOrganizations(userId: string) {
  return useQuery({
    queryKey: userKeys.organizations(userId),
    queryFn: () => userApi.getUserOrganizations(userId),
    enabled: !!userId,
    staleTime: 5 * 60 * 1000,
  });
}

export function useUserTenants(userId: string) {
  return useQuery({
    queryKey: userKeys.tenants(userId),
    queryFn: () => userApi.getUserTenants(userId),
    enabled: !!userId,
    staleTime: 5 * 60 * 1000,
  });
}

export function useUserStats() {
  return useQuery({
    queryKey: userKeys.stats(),
    queryFn: userApi.getStats,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

export function useUserSearch(query: string) {
  return useQuery({
    queryKey: userKeys.search(query),
    queryFn: () => userApi.search(query),
    enabled: query.length >= 2,
    staleTime: 30 * 1000, // 30 seconds
  });
}

// Mutation Hooks
export function useCreateUser() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: userApi.create,
    onSuccess: (newUser) => {
      // Invalidate and refetch
      queryClient.invalidateQueries({ queryKey: userKeys.all });
      
      // Add to cache
      queryClient.setQueryData(userKeys.detail(newUser.id), newUser);
    },
  });
}

export function useUpdateUser() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: userApi.update,
    onMutate: async ({ id, data }) => {
      await queryClient.cancelQueries({ queryKey: userKeys.detail(id) });
      const previousUser = queryClient.getQueryData(userKeys.detail(id));
      
      queryClient.setQueryData(userKeys.detail(id), (old: User | undefined) => {
        if (!old) return old;
        return { ...old, ...data, updatedAt: new Date() };
      });

      return { previousUser, id };
    },
    onError: (_err, _variables, context) => {
      if (context?.previousUser) {
        queryClient.setQueryData(userKeys.detail(context.id), context.previousUser);
      }
    },
    onSettled: (_data, _error, variables) => {
      queryClient.invalidateQueries({ queryKey: userKeys.detail(variables.id) });
      queryClient.invalidateQueries({ queryKey: userKeys.lists() });
    },
  });
}

export function useUpdateUserProfile() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: userApi.updateProfile,
    onMutate: async (data) => {
      await queryClient.cancelQueries({ queryKey: userKeys.profile() });
      const previousProfile = queryClient.getQueryData(userKeys.profile());
      
      queryClient.setQueryData(userKeys.profile(), (old: User | undefined) => {
        if (!old) return old;
        return { ...old, ...data, updatedAt: new Date() };
      });

      return { previousProfile };
    },
    onError: (_err, _variables, context) => {
      if (context?.previousProfile) {
        queryClient.setQueryData(userKeys.profile(), context.previousProfile);
      }
    },
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: userKeys.profile() });
    },
  });
}

export function useDeleteUser() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: userApi.delete,
    onSuccess: (_, id) => {
      queryClient.removeQueries({ queryKey: userKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: userKeys.lists() });
      queryClient.invalidateQueries({ queryKey: userKeys.stats() });
    },
  });
}
