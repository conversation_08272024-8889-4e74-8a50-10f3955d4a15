# 🛠️ **<PERSON><PERSON><PERSON>: Google OAuth "Bad Request" Error**

## 🔍 **Root Cause Analysis**

### **<PERSON><PERSON><PERSON> yang <PERSON>temukan:**

1. **Konflik Routing NextAuth vs Custom OAuth**
   - NextAuth mencoba handle endpoint `/api/auth/google` 
   - Custom Customer OAuth menggunakan `/api/auth/customer/google/init`
   - Ada konflik di middleware dan routing

2. **Wrong Endpoint Calls dari Frontend**
   - ❌ `/api/auth/google` (NextAuth endpoint)
   - ❌ `/api/customer/auth/google` (endpoint tidak ada)
   - ✅ `/api/auth/customer/google/init` (endpoint yang benar)

3. **API Key pada OAuth Endpoints**
   - Request mengirim API key `pk_9b4d8a5f3e9ec298be21ac0589d0346d0289f717d9ca3841093498ef4530c64b`
   - OAuth endpoints tidak boleh menggunakan API key authentication
   - Ini men<PERSON> konflik authentication method

4. **Schema Inconsistency**
   - <PERSON><PERSON>apa endpoint menggunakan `deviceType`
   - Beberapa menggunakan `clientType`
   - <PERSON><PERSON> konsistensi schema

## 🛠️ **Solusi yang Diimplementasikan**

### **1. Middleware Protection (`src/middleware.ts`)**

```typescript
// Handle Customer OAuth routes - prevent NextAuth from intercepting
if (pathname.startsWith('/api/auth/customer/')) {
  console.log(`🔄 [Middleware] Customer OAuth route: ${pathname}`);
  
  // Check if request has API key (which is wrong for OAuth)
  const authHeader = req.headers.get('Authorization');
  const apiKeyHeader = req.headers.get('X-API-Key');
  
  if (authHeader?.startsWith('Bearer pk_') || apiKeyHeader?.startsWith('pk_')) {
    console.log('❌ [Middleware] API key detected on OAuth endpoint - blocking request');
    return new NextResponse(
      JSON.stringify({
        success: false,
        error: 'API keys are not allowed on OAuth endpoints. Use proper OAuth flow instead.',
        errorCode: 'INVALID_AUTH_METHOD',
        hint: 'Remove Authorization/X-API-Key headers and use OAuth flow'
      }),
      {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
  
  // Allow Customer OAuth routes to proceed without NextAuth interference
  return NextResponse.next();
}

// Handle wrong OAuth endpoint calls
if (pathname === '/api/auth/google' || pathname === '/api/customer/auth/google') {
  console.log(`❌ [Middleware] Wrong OAuth endpoint called: ${pathname}`);
  
  return new NextResponse(
    JSON.stringify({
      success: false,
      error: 'Wrong OAuth endpoint',
      errorCode: 'WRONG_ENDPOINT',
      correctEndpoint: '/api/auth/customer/google/init',
      hint: 'Use /api/auth/customer/google/init for Customer OAuth initialization'
    }),
    {
      status: 404,
      headers: { 'Content-Type': 'application/json' }
    }
  );
}
```

**Fungsi Middleware:**
- ✅ Blokir API key pada OAuth endpoints
- ✅ Redirect wrong endpoint calls dengan error message yang jelas
- ✅ Prevent NextAuth dari interfere dengan Customer OAuth routes
- ✅ Memberikan hint yang jelas untuk debugging

### **2. Customer Signup Form (`src/components/auth/customer-signup-form.tsx`)**

```typescript
// Call Customer Registration API (not NextAuth)
const response = await fetch("/api/auth/customer/register", {
  method: "POST",
  headers: {
    "Content-Type": "application/json",
  },
  body: JSON.stringify({
    firstName: data.firstName,
    lastName: data.lastName,
    email: data.email,
    password: data.password,
    tenantId,
  }),
});
```

**Features:**
- ✅ Menggunakan Customer OAuth system (bukan NextAuth)
- ✅ Integrasi dengan `CustomerOAuthLogin` component
- ✅ Proper error handling dan user feedback
- ✅ Two-column layout untuk better UX

### **3. Customer Registration API (`src/app/api/auth/customer/register/route.ts`)**

```typescript
// Create customer account
const [newCustomer] = await db
  .insert(customers)
  .values({
    firstName: validatedData.firstName,
    lastName: validatedData.lastName,
    email: validatedData.email,
    password: hashedPassword,
    tenantId: validatedData.tenantId,
    displayName: `${validatedData.firstName} ${validatedData.lastName}`,
    isActive: true,
    emailVerified: null, // Will be set when email is verified
    membershipType: 'basic',
    preferences: {},
    createdAt: new Date(),
    updatedAt: new Date(),
  })
  .returning({
    id: customers.id,
    email: customers.email,
    firstName: customers.firstName,
    lastName: customers.lastName,
    displayName: customers.displayName,
    tenantId: customers.tenantId,
    createdAt: customers.createdAt,
  });
```

**Features:**
- ✅ Proper validation dengan Zod schema
- ✅ Rate limiting untuk security
- ✅ Tenant-aware customer creation
- ✅ Comprehensive error handling

### **4. Updated Signup Page (`src/app/(auth)/auth/signup/page.tsx`)**

```typescript
import { CustomerSignupForm } from "@/components/auth/customer-signup-form";

export default function SignUpPage() {
  return (
    // ... layout code
    <CustomerSignupForm tenantId={1} redirectPath="/dashboard" />
    // ... layout code
  );
}
```

**Changes:**
- ✅ Menggunakan `CustomerSignupForm` instead of `SignupForm`
- ✅ Proper tenant ID configuration
- ✅ Consistent dengan Customer OAuth system

## 🧪 **Testing & Verification**

### **Test Pages:**
1. **Signup Page**: `http://localhost:3003/auth/signup`
   - ✅ Google OAuth button menggunakan correct endpoints
   - ✅ Email/password registration menggunakan Customer API
   - ✅ No API key headers sent to OAuth

2. **OAuth Testing**: `http://localhost:3003/test-oauth`
   - ✅ Test correct OAuth flow
   - ✅ Test wrong endpoints (should fail with proper errors)
   - ✅ Test API key on OAuth (should fail)

### **Expected Behavior:**
1. **Correct OAuth Flow:**
   ```
   User clicks "Sign up with Google" 
   → POST /api/auth/customer/google/init
   → Redirect to Google OAuth
   → Callback to /api/auth/customer/google/callback
   → JWT tokens generated
   → User authenticated
   ```

2. **Wrong Endpoint Protection:**
   ```
   POST /api/auth/google → 404 with helpful error
   POST /api/customer/auth/google → 404 with helpful error
   OAuth + API key → 400 with clear explanation
   ```

## 🎯 **Key Improvements**

1. **Clear Separation of Concerns:**
   - NextAuth untuk admin/staff authentication
   - Custom OAuth untuk customer authentication
   - No conflicts between systems

2. **Better Error Messages:**
   - Specific error codes (`WRONG_ENDPOINT`, `INVALID_AUTH_METHOD`)
   - Helpful hints untuk debugging
   - Clear next steps untuk developers

3. **Security Enhancements:**
   - API key protection pada OAuth endpoints
   - Rate limiting pada registration
   - Proper middleware validation

4. **Developer Experience:**
   - Comprehensive logging
   - Test pages untuk verification
   - Clear documentation

## 🚀 **Next Steps**

1. **Test the complete flow:**
   - Try signup dengan Google OAuth
   - Verify JWT tokens are generated
   - Check customer data in database

2. **Monitor logs:**
   - Check middleware logs untuk blocked requests
   - Verify OAuth flow logs
   - Monitor any remaining errors

3. **Production Considerations:**
   - Update environment variables
   - Configure proper CORS settings
   - Set up monitoring dan alerting

## ✅ **Status**

- ✅ Middleware protection implemented
- ✅ Customer signup form created
- ✅ Customer registration API created
- ✅ Signup page updated
- ✅ Testing infrastructure ready
- ✅ Documentation completed

**Ready for testing!** 🎉
