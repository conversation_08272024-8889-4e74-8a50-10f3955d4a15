import { NextRequest, NextResponse } from "next/server";
import { RoleService } from "@/lib/services/role.service";

// Test endpoint to verify role delete works without auth
export async function POST(request: NextRequest) {
  try {
    console.log("🧪 Testing role delete...");
    
    const body = await request.json();
    const { roleId } = body;
    
    if (!roleId) {
      return NextResponse.json({
        success: false,
        error: "Role ID is required"
      }, { status: 400 });
    }

    console.log(`🗑️ Attempting to delete role: ${roleId}`);
    
    // Get role first to verify it exists
    const existingRole = await RoleService.getById(roleId);
    if (!existingRole) {
      return NextResponse.json({
        success: false,
        error: "Role not found"
      }, { status: 404 });
    }

    console.log(`📋 Role found: ${existingRole.display_name} (${existingRole.name})`);
    
    // Test delete
    await RoleService.delete(roleId);
    
    console.log("✅ Role delete successful");

    // Verify deletion (should be soft delete - is_active = false)
    const deletedRole = await RoleService.getById(roleId);
    
    return NextResponse.json({
      success: true,
      message: "Role delete test successful!",
      data: {
        roleId,
        originalRole: {
          id: existingRole.id,
          name: existingRole.name,
          display_name: existingRole.display_name,
          is_active: existingRole.is_active
        },
        afterDelete: deletedRole ? {
          id: deletedRole.id,
          name: deletedRole.name,
          display_name: deletedRole.display_name,
          is_active: deletedRole.is_active
        } : null,
        softDeleted: !deletedRole || !deletedRole.is_active
      }
    });
  } catch (error) {
    console.error("❌ Role delete test failed:", error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}
