"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { ImageUpload } from "@/components/ui/image-upload";
import { Switch } from "@/components/ui/switch";
import { Loader2, Building, CheckCircle, ImageIcon, Trash2 } from "lucide-react";
import { Facility, NewFacility } from "@/lib/db/schema";

/**
 * Validation schema untuk facility form
 * 
 * Schema ini define rules untuk validasi form:
 * - tenantId: wajib, harus positive number
 * - name: wajib, max 255 karakter
 * - description: optional, max 500 karakter
 * - isActive: boolean, default true
 * - images: array of image URLs
 */
const facilitySchema = z.object({
  tenantId: z.number().int().positive("Tenant ID must be positive"),
  name: z.string().min(1, "Facility name is required").max(255, "Name too long"),
  description: z.string().max(500, "Description too long").optional(),
  isActive: z.boolean().optional(),
  images: z.array(z.string()).optional(),
});

type FacilityFormData = z.infer<typeof facilitySchema>;

interface FacilityFormProps {
  facility?: Facility;
  tenantId: number;
  onSubmit: (data: Omit<NewFacility, "id" | "createdAt" | "updatedAt">) => Promise<void>;
  onCancel?: () => void;
  isLoading?: boolean;
  className?: string;
}

/**
 * FacilityForm Component
 * 
 * Reusable form component untuk create dan edit facilities.
 * Mengikuti pola yang sama dengan LocationForm dan EquipmentForm.
 * 
 * Features:
 * - Form validation dengan Zod
 * - Image upload capability
 * - Success animations
 * - Error handling
 * - Responsive design
 */
export function FacilityForm({
  facility,
  tenantId,
  onSubmit,
  onCancel,
  isLoading = false,
  className = "",
}: FacilityFormProps) {
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [isSuccess, setIsSuccess] = useState(false);
  const [facilityImages, setFacilityImages] = useState<string[]>(facility?.images || []);

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    setValue,
    watch,
  } = useForm<FacilityFormData>({
    resolver: zodResolver(facilitySchema),
    defaultValues: {
      tenantId,
      name: facility?.name || "",
      description: facility?.description || "",
      isActive: facility?.isActive !== undefined ? facility.isActive : true,
      images: facility?.images || [],
    },
  });

  // Update images when facility data changes
  useEffect(() => {
    if (facility?.images) {
      setFacilityImages(facility.images);
    }
  }, [facility]);

  // Handle image upload
  const handleImageUpload = (url: string) => {
    setFacilityImages(prev => [...prev, url]);
    setValue("images", [...facilityImages, url]);
  };

  // Handle image deletion
  const handleImageDelete = (imageUrl: string) => {
    const updatedImages = facilityImages.filter(url => url !== imageUrl);
    setFacilityImages(updatedImages);
    setValue("images", updatedImages);
  };

  const handleFormSubmit = async (data: FacilityFormData) => {
    try {
      setSubmitError(null);
      setIsSuccess(false);

      // Include images in the submission data
      const submissionData = {
        ...data,
        images: facilityImages,
      };

      await onSubmit(submissionData);

      // Show success state briefly
      setIsSuccess(true);
      setTimeout(() => setIsSuccess(false), 2000);

      if (!facility) {
        reset(); // Reset form only for new facility
        setFacilityImages([]); // Reset images
      }
    } catch (error) {
      setSubmitError(error instanceof Error ? error.message : "Failed to save facility");
      setIsSuccess(false);
    }
  };

  const isFormLoading = isLoading || isSubmitting;
  const watchedIsActive = watch("isActive");

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Building className="h-5 w-5" />
          {facility ? "Edit Facility" : "Add New Facility"}
        </CardTitle>
        <CardDescription>
          {facility 
            ? "Update the facility information below." 
            : "Enter the details for the new facility."
          }
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          {submitError && (
            <Alert variant="destructive">
              <AlertDescription>{submitError}</AlertDescription>
            </Alert>
          )}

          {/* Facility Information Section */}
          <div className="space-y-4">
            <div className="flex items-center gap-2 text-sm font-medium text-gray-700 dark:text-gray-300">
              <Building className="h-4 w-4" />
              Facility Information
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Facility Name *</Label>
                <Input
                  id="name"
                  {...register("name")}
                  placeholder="e.g., Main Gym, Conference Room A, Workshop"
                  disabled={isFormLoading}
                />
                {errors.name && (
                  <p className="text-sm text-red-600 mt-1">{errors.name.message}</p>
                )}
              </div>

              <div className="flex items-center space-x-2">
                <Switch
                  id="isActive"
                  checked={watchedIsActive}
                  onCheckedChange={(checked) => setValue("isActive", checked)}
                  disabled={isFormLoading}
                />
                <Label htmlFor="isActive" className="text-sm font-medium">
                  Active Facility
                </Label>
              </div>
            </div>

            <div>
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                {...register("description")}
                placeholder="Describe the facility, its purpose, capacity, or special features..."
                rows={3}
                disabled={isFormLoading}
              />
              {errors.description && (
                <p className="text-sm text-red-600 mt-1">{errors.description.message}</p>
              )}
            </div>
          </div>

          {/* Facility Images Section */}
          <div className="border-t pt-6 mt-6">
            <div className="flex items-center gap-2 mb-4">
              <ImageIcon className="h-5 w-5 text-blue-600" />
              <h3 className="text-lg font-semibold">Facility Images</h3>
              {facilityImages.length > 0 && (
                <Badge variant="secondary" className="text-xs">
                  {facilityImages.length} image{facilityImages.length !== 1 ? 's' : ''}
                </Badge>
              )}
            </div>

            <div className="space-y-4">
              <ImageUpload
                onImageUpload={handleImageUpload}
                disabled={isFormLoading}
                placeholder="Upload facility images"
                autoUpload={true}
                showUrlInput={false}
              />

              {/* Display Current Images */}
              {facilityImages.length > 0 && (
                <div className="space-y-3">
                  <Label className="text-sm font-medium">Current Images:</Label>
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                    {facilityImages.map((imageUrl, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.9 }}
                        className="relative group"
                      >
                        <div className="aspect-square rounded-lg overflow-hidden bg-muted">
                          <img
                            src={imageUrl}
                            alt="Facility image"
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.src = "/images/placeholder-image.svg";
                            }}
                          />
                        </div>
                        <Button
                          type="button"
                          variant="destructive"
                          size="sm"
                          className="absolute top-1 right-1 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                          onClick={() => handleImageDelete(imageUrl)}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </motion.div>
                    ))}
                  </div>
                </div>
              )}

              <p className="text-xs text-muted-foreground">
                Images will be saved with the facility data when you submit the form.
              </p>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex gap-3 pt-4">
            <motion.div
              className="flex-1"
              whileTap={{ scale: 0.98 }}
              animate={isSuccess ? { scale: [1, 1.02, 1] } : {}}
              transition={{ duration: 0.2 }}
            >
              <Button
                type="submit"
                disabled={isFormLoading}
                className={`w-full transition-all duration-200 ${
                  isSuccess
                    ? "bg-green-600 hover:bg-green-700 text-white"
                    : ""
                }`}
              >
                {isFormLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isSuccess && <CheckCircle className="mr-2 h-4 w-4" />}
                {isSuccess
                  ? "✓ Success!"
                  : facility
                    ? "Update Facility"
                    : "Create Facility"
                }
              </Button>
            </motion.div>
            
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isFormLoading}
              >
                Cancel
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
