"use client";

import React, { useState, useEffect } from 'react';
import { Shield, FileText, CheckCircle, AlertCircle } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { useWaiverForm } from '@/lib/hooks/queries/use-waiver-form-queries';

interface WaiverFormField {
  id: string;
  type: 'text' | 'textarea' | 'checkbox' | 'signature';
  label: string;
  required: boolean;
  placeholder?: string;
  description?: string;
}

interface WaiverFormContent {
  title: string;
  description: string;
  fields: WaiverFormField[];
  terms: string;
}

interface DynamicWaiverFormsProps {
  selectedWaiverFormIds: string[];
  waiverResponses: Record<string, any>;
  onWaiverResponseChange: (waiverFormId: string, fieldId: string, value: any) => void;
  onWaiverComplete: (waiverFormId: string, isComplete: boolean) => void;
}

export function DynamicWaiverForms({
  selectedWaiverFormIds,
  waiverResponses,
  onWaiverResponseChange,
  onWaiverComplete
}: DynamicWaiverFormsProps) {
  console.log('🔍 [DynamicWaiverForms] Rendering with:', {
    selectedWaiverFormIds,
    waiverResponses
  });

  if (!selectedWaiverFormIds || selectedWaiverFormIds.length === 0) {
    return (
      <div className="text-center py-8 text-gray-500">
        <Shield className="mx-auto h-12 w-12 mb-4 opacity-50" />
        <p>Select waiver forms above to display their fields</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {selectedWaiverFormIds.map((waiverFormId) => (
        <WaiverFormCard
          key={waiverFormId}
          waiverFormId={waiverFormId}
          responses={waiverResponses[waiverFormId] || {}}
          onResponseChange={(fieldId, value) => onWaiverResponseChange(waiverFormId, fieldId, value)}
          onComplete={(isComplete) => onWaiverComplete(waiverFormId, isComplete)}
        />
      ))}
    </div>
  );
}

interface WaiverFormCardProps {
  waiverFormId: string;
  responses: Record<string, any>;
  onResponseChange: (fieldId: string, value: any) => void;
  onComplete: (isComplete: boolean) => void;
}

function WaiverFormCard({
  waiverFormId,
  responses,
  onResponseChange,
  onComplete
}: WaiverFormCardProps) {
  const { data: waiverForm, isLoading } = useWaiverForm(waiverFormId);
  const [isComplete, setIsComplete] = useState(false);

  // Parse waiver form content
  const waiverContent: WaiverFormContent | null = React.useMemo(() => {
    if (!waiverForm?.content) return null;
    
    try {
      // Try to parse as JSON first
      return JSON.parse(waiverForm.content);
    } catch {
      // If not JSON, create a simple form structure
      return {
        title: waiverForm.name,
        description: waiverForm.description || '',
        fields: [
          {
            id: 'fullName',
            type: 'text',
            label: 'Full Name',
            required: true,
            placeholder: 'Enter your full name'
          },
          {
            id: 'signature',
            type: 'text',
            label: 'Digital Signature',
            required: true,
            placeholder: 'Type your full name as digital signature'
          },
          {
            id: 'agreement',
            type: 'checkbox',
            label: 'I agree to the terms and conditions',
            required: true
          }
        ],
        terms: waiverForm.content
      };
    }
  }, [waiverForm]);

  // Check if form is complete
  useEffect(() => {
    if (!waiverContent) return;

    const requiredFields = waiverContent.fields.filter(field => field.required);
    const completedFields = requiredFields.filter(field => {
      const value = responses[field.id];
      if (field.type === 'checkbox') {
        return value === true;
      }
      return value && value.toString().trim() !== '';
    });

    const complete = completedFields.length === requiredFields.length;
    setIsComplete(complete);
    onComplete(complete);
  }, [responses, waiverContent, onComplete]);

  if (isLoading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
            <div className="h-3 bg-gray-200 rounded w-1/2"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!waiverContent) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-red-500">
            <AlertCircle className="mx-auto h-8 w-8 mb-2" />
            <p>Error loading waiver form content</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`transition-all duration-200 ${isComplete ? 'ring-2 ring-green-500' : ''}`}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <CardTitle className="text-lg">{waiverContent.title}</CardTitle>
          </div>
          {isComplete && (
            <CheckCircle className="h-5 w-5 text-green-500" />
          )}
        </div>
        {waiverContent.description && (
          <CardDescription>{waiverContent.description}</CardDescription>
        )}
      </CardHeader>
      <CardContent className="space-y-4">
        {waiverContent.fields.map((field) => (
          <WaiverFormField
            key={field.id}
            field={field}
            value={responses[field.id]}
            onChange={(value) => onResponseChange(field.id, value)}
          />
        ))}
        
        {waiverContent.terms && (
          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <h4 className="font-medium mb-2">Terms and Conditions:</h4>
            <div className="text-sm text-gray-600 whitespace-pre-wrap">
              {waiverContent.terms}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

interface WaiverFormFieldProps {
  field: WaiverFormField;
  value: any;
  onChange: (value: any) => void;
}

function WaiverFormField({ field, value, onChange }: WaiverFormFieldProps) {
  const fieldId = `waiver-field-${field.id}`;

  switch (field.type) {
    case 'text':
      return (
        <div className="space-y-2">
          <Label htmlFor={fieldId}>
            {field.label}
            {field.required && <span className="text-red-500 ml-1">*</span>}
          </Label>
          <Input
            id={fieldId}
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder={field.placeholder}
            required={field.required}
          />
          {field.description && (
            <p className="text-sm text-gray-500">{field.description}</p>
          )}
        </div>
      );

    case 'textarea':
      return (
        <div className="space-y-2">
          <Label htmlFor={fieldId}>
            {field.label}
            {field.required && <span className="text-red-500 ml-1">*</span>}
          </Label>
          <Textarea
            id={fieldId}
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder={field.placeholder}
            required={field.required}
            rows={3}
          />
          {field.description && (
            <p className="text-sm text-gray-500">{field.description}</p>
          )}
        </div>
      );

    case 'checkbox':
      return (
        <div className="flex items-center space-x-2">
          <Checkbox
            id={fieldId}
            checked={value === true}
            onCheckedChange={(checked) => onChange(checked)}
            required={field.required}
          />
          <Label htmlFor={fieldId} className="text-sm">
            {field.label}
            {field.required && <span className="text-red-500 ml-1">*</span>}
          </Label>
          {field.description && (
            <p className="text-sm text-gray-500 ml-6">{field.description}</p>
          )}
        </div>
      );

    case 'signature':
      return (
        <div className="space-y-2">
          <Label htmlFor={fieldId}>
            {field.label}
            {field.required && <span className="text-red-500 ml-1">*</span>}
          </Label>
          <Input
            id={fieldId}
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder={field.placeholder || 'Type your full name as signature'}
            required={field.required}
            className="font-cursive"
          />
          {field.description && (
            <p className="text-sm text-gray-500">{field.description}</p>
          )}
        </div>
      );

    default:
      return null;
  }
}
