"use client";

import { useState } from "react";
import { useRB<PERSON> } from "@/lib/hooks/use-rbac";
import { useAPIClient } from "@/lib/api/rbac-client";
import { useRBACErrorHandler } from "@/lib/utils/rbac-error-handler";
import { PermissionGuard, RoleGuard } from "@/components/rbac/permission-guard";
import { RBACForm, RBACButton } from "@/components/rbac/rbac-form";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Users, Settings, BarChart3, MapPin, Shield } from "lucide-react";
import { toast } from "sonner";

/**
 * Example Dashboard Component dengan RBAC Integration
 * 
 * Component ini show real-world usage dari semua RBAC features:
 * - Permission-based conditional rendering
 * - Role-based access control
 * - Location-based restrictions
 * - RBAC-aware forms
 * - Error handling
 */

export function RBACDashboard() {
  const {
    user,
    roles,
    permissions,
    accessibleLocations,
    isSuperAdmin,
    isTenantAdmin,
    isInstructor,
    canManageUsers,
    canManageClasses,
    canViewReports,
    getRoleDisplayName,
    hasLocationRestrictions,
  } = useRBAC();

  const apiClient = useAPIClient();
  const { handleError, handlePermissionError } = useRBACErrorHandler();
  
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    className: "",
    description: "",
    locationId: "",
  });

  // Handle create class
  const handleCreateClass = async (event: React.FormEvent) => {
    event.preventDefault();
    
    try {
      setIsLoading(true);
      
      await apiClient.post("/classes", formData, {
        locationId: formData.locationId,
      });
      
      // Reset form
      setFormData({ className: "", description: "", locationId: "" });
      
      // Show success message
      toast.success("Class berhasil dibuat!");
      
    } catch (error) {
      handleError(error, {
        module: "classes",
        action: "create",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle delete user (example)
  const handleDeleteUser = async (userId: string) => {
    if (!canManageUsers()) {
      handlePermissionError("users", "delete");
      return;
    }

    try {
      await apiClient.delete(`/users/${userId}`);
      toast.success("User berhasil dihapus!");
    } catch (error) {
      handleError(error, {
        module: "users",
        action: "delete",
      });
    }
  };

  return (
    <div className="space-y-6">
      {/* Header dengan User Info */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Dashboard</h1>
          <p className="text-gray-600">
            Welcome back, {user?.name}!
          </p>
        </div>
        
        <div className="flex items-center space-x-2">
          <Badge variant="secondary">
            {getRoleDisplayName()}
          </Badge>
          
          {isSuperAdmin() && (
            <Badge variant="destructive">
              <Shield className="w-3 h-3 mr-1" />
              Super Admin
            </Badge>
          )}
        </div>
      </div>

      {/* Stats Cards dengan Permission Guards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Users Stats - Only for admins */}
        <PermissionGuard module="users" action="read" hideIfNoAccess>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Users</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">1,234</div>
              <p className="text-xs text-muted-foreground">
                +10% from last month
              </p>
            </CardContent>
          </Card>
        </PermissionGuard>

        {/* Classes Stats - For instructors and admins */}
        <PermissionGuard module="classes" action="read" hideIfNoAccess>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Classes</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">56</div>
              <p className="text-xs text-muted-foreground">
                +2 new this week
              </p>
            </CardContent>
          </Card>
        </PermissionGuard>

        {/* Locations Stats - Show accessible locations */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {hasLocationRestrictions() ? "Accessible Locations" : "All Locations"}
            </CardTitle>
            <MapPin className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {hasLocationRestrictions() ? accessibleLocations.length : "All"}
            </div>
            <p className="text-xs text-muted-foreground">
              {hasLocationRestrictions() ? "Limited access" : "Full access"}
            </p>
          </CardContent>
        </Card>

        {/* Reports Stats - Only for those who can view reports */}
        <PermissionGuard module="reports" action="read" hideIfNoAccess>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Reports</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">12</div>
              <p className="text-xs text-muted-foreground">
                Available reports
              </p>
            </CardContent>
          </Card>
        </PermissionGuard>
      </div>

      {/* Quick Actions dengan Role Guards */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Create Class Form - For instructors and admins */}
        <PermissionGuard module="classes" action="create" hideIfNoAccess>
          <Card>
            <CardHeader>
              <CardTitle>Create New Class</CardTitle>
              <CardDescription>
                Add a new class to your schedule
              </CardDescription>
            </CardHeader>
            <CardContent>
              <RBACForm
                submitPermission={{ module: "classes", action: "create" }}
                onSubmit={handleCreateClass}
                isLoading={isLoading}
                submitText="Create Class"
              >
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="className">Class Name</Label>
                    <Input
                      id="className"
                      value={formData.className}
                      onChange={(e) => setFormData(prev => ({ ...prev, className: e.target.value }))}
                      placeholder="Enter class name"
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="description">Description</Label>
                    <Input
                      id="description"
                      value={formData.description}
                      onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                      placeholder="Enter description"
                    />
                  </div>
                  
                  {/* Location selector - only show accessible locations */}
                  {hasLocationRestrictions() && (
                    <div>
                      <Label htmlFor="locationId">Location</Label>
                      <select
                        id="locationId"
                        value={formData.locationId}
                        onChange={(e) => setFormData(prev => ({ ...prev, locationId: e.target.value }))}
                        className="w-full p-2 border rounded"
                      >
                        <option value="">Select location</option>
                        {accessibleLocations.map(locationId => (
                          <option key={locationId} value={locationId}>
                            Location {locationId}
                          </option>
                        ))}
                      </select>
                    </div>
                  )}
                </div>
              </RBACForm>
            </CardContent>
          </Card>
        </PermissionGuard>

        {/* Admin Actions - Only for admins */}
        <RoleGuard role="tenant_admin" hideIfNoAccess>
          <Card>
            <CardHeader>
              <CardTitle>Admin Actions</CardTitle>
              <CardDescription>
                Administrative functions
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <RBACButton
                permission={{ module: "users", action: "manage" }}
                onClick={() => console.log("Manage users")}
                className="w-full"
              >
                <Users className="mr-2 h-4 w-4" />
                Manage Users
              </RBACButton>
              
              <RBACButton
                permission={{ module: "settings", action: "manage" }}
                onClick={() => console.log("System settings")}
                variant="outline"
                className="w-full"
              >
                <Settings className="mr-2 h-4 w-4" />
                System Settings
              </RBACButton>
              
              <RBACButton
                permission={{ module: "reports", action: "read" }}
                onClick={() => console.log("View reports")}
                variant="secondary"
                className="w-full"
              >
                <BarChart3 className="mr-2 h-4 w-4" />
                View Reports
              </RBACButton>
            </CardContent>
          </Card>
        </RoleGuard>
      </div>

      {/* User Permissions Display */}
      <Card>
        <CardHeader>
          <CardTitle>Your Permissions</CardTitle>
          <CardDescription>
            Current roles and permissions assigned to your account
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">Roles:</h4>
              <div className="flex flex-wrap gap-2">
                {roles.map(role => (
                  <Badge key={role.id} variant="outline">
                    {role.displayName}
                  </Badge>
                ))}
              </div>
            </div>
            
            <div>
              <h4 className="font-medium mb-2">Key Permissions:</h4>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2 text-sm">
                {canManageUsers() && <span className="text-green-600">✓ Manage Users</span>}
                {canManageClasses() && <span className="text-green-600">✓ Manage Classes</span>}
                {canViewReports() && <span className="text-green-600">✓ View Reports</span>}
                {isSuperAdmin() && <span className="text-red-600">✓ Super Admin Access</span>}
              </div>
            </div>
            
            {hasLocationRestrictions() && (
              <div>
                <h4 className="font-medium mb-2">Accessible Locations:</h4>
                <div className="flex flex-wrap gap-2">
                  {accessibleLocations.map(locationId => (
                    <Badge key={locationId} variant="secondary">
                      Location {locationId}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
