"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  Search, 
  Calendar, 
  Eye, 
  User, 
  Tag,
  Star,
  ArrowRight,
  Filter
} from "lucide-react";
import { format } from "date-fns";
import Link from "next/link";
import Image from "next/image";

// Mock data for public blog page (will be replaced with API calls)
const mockBlogPosts = [
  {
    id: "1",
    title: "Getting Started with Our Fitness Programs",
    slug: "getting-started-fitness-programs",
    excerpt: "Discover the perfect fitness program for your goals. From beginner-friendly routines to advanced training, we have something for everyone.",
    content: "",
    featured_image: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&h=400&fit=crop",
    category: {
      id: "1",
      name: "Fitness",
      slug: "fitness",
      color: "#3B82F6"
    },
    tags: ["fitness", "beginner", "workout"],
    status: "published" as const,
    is_featured: true,
    published_at: new Date("2024-01-15"),
    author: {
      id: "1",
      name: "Sarah Johnson",
      email: "<EMAIL>"
    },
    view_count: 1250,
    tenantId: 1,
    created_at: new Date("2024-01-15"),
    updated_at: new Date("2024-01-15")
  },
  {
    id: "2",
    title: "Nutrition Tips for Better Performance",
    slug: "nutrition-tips-better-performance",
    excerpt: "Learn how proper nutrition can enhance your workout performance and help you achieve your fitness goals faster.",
    content: "",
    featured_image: "https://images.unsplash.com/photo-1490645935967-10de6ba17061?w=800&h=400&fit=crop",
    category: {
      id: "2",
      name: "Nutrition",
      slug: "nutrition",
      color: "#10B981"
    },
    tags: ["nutrition", "performance", "health"],
    status: "published" as const,
    is_featured: false,
    published_at: new Date("2024-01-10"),
    author: {
      id: "2",
      name: "Mike Chen",
      email: "<EMAIL>"
    },
    view_count: 890,
    tenantId: 1,
    created_at: new Date("2024-01-10"),
    updated_at: new Date("2024-01-10")
  },
  {
    id: "3",
    title: "New Equipment Available: State-of-the-Art Cardio Machines",
    slug: "new-equipment-cardio-machines",
    excerpt: "We're excited to announce the arrival of our new cardio equipment featuring the latest technology for an enhanced workout experience.",
    content: "",
    featured_image: "https://images.unsplash.com/photo-1534438327276-14e5300c3a48?w=800&h=400&fit=crop",
    category: {
      id: "3",
      name: "Equipment",
      slug: "equipment",
      color: "#F59E0B"
    },
    tags: ["equipment", "cardio", "announcement"],
    status: "published" as const,
    is_featured: true,
    published_at: new Date("2024-01-08"),
    author: {
      id: "1",
      name: "Sarah Johnson",
      email: "<EMAIL>"
    },
    view_count: 567,
    tenantId: 1,
    created_at: new Date("2024-01-08"),
    updated_at: new Date("2024-01-08")
  }
];

const mockCategories = [
  { id: "1", name: "Fitness", slug: "fitness", color: "#3B82F6" },
  { id: "2", name: "Nutrition", slug: "nutrition", color: "#10B981" },
  { id: "3", name: "Equipment", slug: "equipment", color: "#F59E0B" },
  { id: "4", name: "Announcements", slug: "announcements", color: "#8B5CF6" }
];

export default function PublicBlogPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [categoryFilter, setCategoryFilter] = useState<string>("all");
  const [filteredPosts, setFilteredPosts] = useState(mockBlogPosts);

  // Filter posts based on search and category
  useEffect(() => {
    let filtered = mockBlogPosts;

    if (searchQuery) {
      filtered = filtered.filter(post =>
        post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        post.excerpt.toLowerCase().includes(searchQuery.toLowerCase()) ||
        post.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    if (categoryFilter !== "all") {
      filtered = filtered.filter(post => post.category?.id === categoryFilter);
    }

    setFilteredPosts(filtered);
  }, [searchQuery, categoryFilter]);

  const featuredPosts = mockBlogPosts.filter(post => post.is_featured);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">Blog & News</h1>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Stay updated with the latest fitness tips, nutrition advice, and gym announcements
            </p>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        {/* Featured Posts Section */}
        {featuredPosts.length > 0 && (
          <section className="mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center gap-2">
              <Star className="h-6 w-6 text-yellow-500 fill-current" />
              Featured Posts
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {featuredPosts.map((post) => (
                <Card key={post.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                  {post.featured_image && (
                    <div className="relative h-48 w-full">
                      <Image
                        src={post.featured_image}
                        alt={post.title}
                        fill
                        className="object-cover"
                      />
                      <div className="absolute top-4 left-4">
                        <Badge className="bg-yellow-500 text-white">
                          <Star className="h-3 w-3 mr-1 fill-current" />
                          Featured
                        </Badge>
                      </div>
                    </div>
                  )}
                  <CardHeader>
                    <div className="flex items-center gap-2 mb-2">
                      {post.category && (
                        <Badge 
                          variant="outline"
                          style={{ 
                            backgroundColor: `${post.category.color}20`,
                            borderColor: post.category.color,
                            color: post.category.color
                          }}
                        >
                          {post.category.name}
                        </Badge>
                      )}
                      <span className="text-sm text-gray-500 flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        {format(post.published_at, 'MMM dd, yyyy')}
                      </span>
                    </div>
                    <CardTitle className="line-clamp-2 hover:text-blue-600 transition-colors">
                      <Link href={`/blog/${post.slug}`}>
                        {post.title}
                      </Link>
                    </CardTitle>
                    <CardDescription className="line-clamp-3">
                      {post.excerpt}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4 text-sm text-gray-500">
                        <span className="flex items-center gap-1">
                          <User className="h-3 w-3" />
                          {post.author.name}
                        </span>
                        <span className="flex items-center gap-1">
                          <Eye className="h-3 w-3" />
                          {post.view_count}
                        </span>
                      </div>
                      <Link href={`/blog/${post.slug}`}>
                        <Button variant="ghost" size="sm">
                          Read More
                          <ArrowRight className="h-3 w-3 ml-1" />
                        </Button>
                      </Link>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </section>
        )}

        {/* Search and Filter Section */}
        <section className="mb-8">
          <Card>
            <CardContent className="pt-6">
              <div className="flex flex-col sm:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search blog posts..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                  <SelectTrigger className="w-full sm:w-48">
                    <SelectValue placeholder="All Categories" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    {mockCategories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>
        </section>

        {/* All Posts Section */}
        <section>
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-gray-900">
              {categoryFilter !== "all" 
                ? `${mockCategories.find(c => c.id === categoryFilter)?.name} Posts`
                : "All Posts"
              }
            </h2>
            <span className="text-gray-500">
              {filteredPosts.length} post{filteredPosts.length !== 1 ? 's' : ''} found
            </span>
          </div>

          {filteredPosts.length === 0 ? (
            <Card>
              <CardContent className="text-center py-12">
                <Search className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No posts found</h3>
                <p className="text-gray-500">
                  Try adjusting your search terms or category filter.
                </p>
              </CardContent>
            </Card>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredPosts.map((post) => (
                <Card key={post.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                  {post.featured_image && (
                    <div className="relative h-48 w-full">
                      <Image
                        src={post.featured_image}
                        alt={post.title}
                        fill
                        className="object-cover"
                      />
                    </div>
                  )}
                  <CardHeader>
                    <div className="flex items-center gap-2 mb-2">
                      {post.category && (
                        <Badge 
                          variant="outline"
                          style={{ 
                            backgroundColor: `${post.category.color}20`,
                            borderColor: post.category.color,
                            color: post.category.color
                          }}
                        >
                          {post.category.name}
                        </Badge>
                      )}
                      <span className="text-sm text-gray-500 flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        {format(post.published_at, 'MMM dd, yyyy')}
                      </span>
                    </div>
                    <CardTitle className="line-clamp-2 hover:text-blue-600 transition-colors">
                      <Link href={`/blog/${post.slug}`}>
                        {post.title}
                      </Link>
                    </CardTitle>
                    <CardDescription className="line-clamp-3">
                      {post.excerpt}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-1 mb-4">
                      {post.tags.slice(0, 3).map((tag) => (
                        <Badge key={tag} variant="secondary" className="text-xs">
                          <Tag className="h-2 w-2 mr-1" />
                          {tag}
                        </Badge>
                      ))}
                      {post.tags.length > 3 && (
                        <Badge variant="secondary" className="text-xs">
                          +{post.tags.length - 3}
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4 text-sm text-gray-500">
                        <span className="flex items-center gap-1">
                          <User className="h-3 w-3" />
                          {post.author.name}
                        </span>
                        <span className="flex items-center gap-1">
                          <Eye className="h-3 w-3" />
                          {post.view_count}
                        </span>
                      </div>
                      <Link href={`/blog/${post.slug}`}>
                        <Button variant="ghost" size="sm">
                          Read More
                          <ArrowRight className="h-3 w-3 ml-1" />
                        </Button>
                      </Link>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </section>
      </div>
    </div>
  );
}
