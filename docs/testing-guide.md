# Customer OAuth Testing Guide

## Overview

Comprehensive testing guide for the Customer Google OAuth authentication system with examples for unit tests, integration tests, and end-to-end testing.

## Test Environment Setup

### Environment Variables

```bash
# .env.test
DATABASE_URL="postgresql://test_user:test_pass@localhost:5433/test_db"
JWT_SECRET="test-jwt-secret-minimum-32-characters"
GOOGLE_CLIENT_ID="test-google-client-id"
GOOGLE_CLIENT_SECRET="test-google-client-secret"
NEXTAUTH_URL="http://localhost:3000"
FRONTEND_URL="http://localhost:3000"
NODE_ENV="test"
```

### Test Database Setup

```bash
# Create test database
npm run db:generate
npm run db:migrate
npm run db:seed:test
```

## Unit Tests

### Testing OAuth Service

```typescript
// __tests__/services/google-oauth.service.test.ts
import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { googleOAuthService } from '@/lib/services/google-oauth.service';
import { db } from '@/lib/db';

describe('GoogleOAuthService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('generatePKCEChallenge', () => {
    it('should generate valid PKCE challenge', () => {
      const { codeVerifier, codeChallenge } = googleOAuthService.generatePKCEChallenge();
      
      expect(codeVerifier).toHaveLength(128);
      expect(codeChallenge).toMatch(/^[A-Za-z0-9_-]+$/);
      expect(codeVerifier).toMatch(/^[A-Za-z0-9._~-]+$/);
    });

    it('should generate unique challenges', () => {
      const challenge1 = googleOAuthService.generatePKCEChallenge();
      const challenge2 = googleOAuthService.generatePKCEChallenge();
      
      expect(challenge1.codeVerifier).not.toBe(challenge2.codeVerifier);
      expect(challenge1.codeChallenge).not.toBe(challenge2.codeChallenge);
    });
  });

  describe('initializeOAuthFlow', () => {
    it('should initialize web OAuth flow', async () => {
      const params = {
        tenantId: 1,
        clientType: 'web' as const,
        redirectUri: 'http://localhost:3000/callback',
        ipAddress: '127.0.0.1',
      };

      const result = await googleOAuthService.initializeOAuthFlow(params);

      expect(result.authUrl).toContain('accounts.google.com');
      expect(result.authUrl).toContain('client_id=');
      expect(result.authUrl).toContain('redirect_uri=');
      expect(result.state).toHaveLength(32);
    });

    it('should initialize mobile OAuth flow with PKCE', async () => {
      const params = {
        tenantId: 1,
        clientType: 'mobile' as const,
        redirectUri: 'myapp://oauth/callback',
        ipAddress: '127.0.0.1',
      };

      const result = await googleOAuthService.initializeOAuthFlow(params);

      expect(result.authUrl).toContain('code_challenge=');
      expect(result.authUrl).toContain('code_challenge_method=S256');
      expect(result.codeChallenge).toBeDefined();
    });
  });

  describe('validatePKCEChallenge', () => {
    it('should validate correct PKCE challenge', async () => {
      const { codeVerifier, codeChallenge } = googleOAuthService.generatePKCEChallenge();
      
      // Store challenge in database
      await googleOAuthService.storePKCEChallenge({
        state: 'test-state',
        codeChallenge,
        tenantId: 1,
        expiresAt: new Date(Date.now() + 10 * 60 * 1000),
      });

      const isValid = await googleOAuthService.validatePKCEChallenge(
        'test-state',
        codeVerifier
      );

      expect(isValid).toBe(true);
    });

    it('should reject invalid PKCE challenge', async () => {
      const { codeChallenge } = googleOAuthService.generatePKCEChallenge();
      
      await googleOAuthService.storePKCEChallenge({
        state: 'test-state',
        codeChallenge,
        tenantId: 1,
        expiresAt: new Date(Date.now() + 10 * 60 * 1000),
      });

      const isValid = await googleOAuthService.validatePKCEChallenge(
        'test-state',
        'invalid-verifier'
      );

      expect(isValid).toBe(false);
    });
  });
});
```

### Testing JWT Service

```typescript
// __tests__/services/customer-jwt.service.test.ts
import { describe, it, expect, beforeEach } from '@jest/globals';
import { customerJWTService } from '@/lib/services/customer-jwt.service';

describe('CustomerJWTService', () => {
  const mockCustomer = {
    id: 'cust_123',
    email: '<EMAIL>',
    firstName: 'John',
    lastName: 'Doe',
    displayName: 'John Doe',
    image: null,
    tenantId: 1,
    membershipType: 'basic',
    isEmailVerified: true,
  };

  const mockSessionParams = {
    customerId: 'cust_123',
    tenantId: 1,
    ipAddress: '127.0.0.1',
    userAgent: 'test-agent',
    deviceType: 'web' as const,
  };

  describe('generateTokenPair', () => {
    it('should generate valid JWT token pair', async () => {
      const tokens = await customerJWTService.generateTokenPair(
        mockCustomer,
        mockSessionParams
      );

      expect(tokens.accessToken).toBeDefined();
      expect(tokens.refreshToken).toBeDefined();
      expect(tokens.tokenType).toBe('Bearer');
      expect(tokens.expiresIn).toBeGreaterThan(0);
    });

    it('should generate tokens with correct payload', async () => {
      const tokens = await customerJWTService.generateTokenPair(
        mockCustomer,
        mockSessionParams
      );

      const validation = await customerJWTService.validateToken({
        token: tokens.accessToken,
        tenantId: 1,
      });

      expect(validation.valid).toBe(true);
      expect(validation.payload?.sub).toBe(mockCustomer.id);
      expect(validation.payload?.email).toBe(mockCustomer.email);
      expect(validation.payload?.tenantId).toBe(mockCustomer.tenantId);
    });
  });

  describe('validateToken', () => {
    it('should validate valid token', async () => {
      const tokens = await customerJWTService.generateTokenPair(
        mockCustomer,
        mockSessionParams
      );

      const validation = await customerJWTService.validateToken({
        token: tokens.accessToken,
        tenantId: 1,
      });

      expect(validation.valid).toBe(true);
      expect(validation.payload).toBeDefined();
    });

    it('should reject invalid token', async () => {
      const validation = await customerJWTService.validateToken({
        token: 'invalid-token',
        tenantId: 1,
      });

      expect(validation.valid).toBe(false);
      expect(validation.errorCode).toBe('TOKEN_INVALID');
    });

    it('should reject token with wrong tenant', async () => {
      const tokens = await customerJWTService.generateTokenPair(
        mockCustomer,
        mockSessionParams
      );

      const validation = await customerJWTService.validateToken({
        token: tokens.accessToken,
        tenantId: 999, // Wrong tenant
      });

      expect(validation.valid).toBe(false);
      expect(validation.errorCode).toBe('TENANT_MISMATCH');
    });
  });
});
```

## Integration Tests

### Testing API Endpoints

```typescript
// __tests__/api/auth/customer/google/init.test.ts
import { describe, it, expect } from '@jest/globals';
import { POST } from '@/app/api/auth/customer/google/init/route';
import { NextRequest } from 'next/server';

describe('/api/auth/customer/google/init', () => {
  it('should initialize OAuth flow for web client', async () => {
    const request = new NextRequest('http://localhost:3000/api/auth/customer/google/init', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        tenantId: 1,
        clientType: 'web',
        redirectUri: 'http://localhost:3000/callback',
      }),
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.success).toBe(true);
    expect(data.authUrl).toContain('accounts.google.com');
    expect(data.state).toBeDefined();
    expect(data.clientType).toBe('web');
  });

  it('should initialize OAuth flow for mobile client with PKCE', async () => {
    const request = new NextRequest('http://localhost:3000/api/auth/customer/google/init', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        tenantId: 1,
        clientType: 'mobile',
        deviceId: 'test-device',
      }),
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(200);
    expect(data.success).toBe(true);
    expect(data.codeChallenge).toBeDefined();
    expect(data.clientType).toBe('mobile');
  });

  it('should reject invalid tenant ID', async () => {
    const request = new NextRequest('http://localhost:3000/api/auth/customer/google/init', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        tenantId: -1, // Invalid
        clientType: 'web',
      }),
    });

    const response = await POST(request);
    const data = await response.json();

    expect(response.status).toBe(400);
    expect(data.success).toBe(false);
    expect(data.error.code).toBe('INVALID_INPUT');
  });

  it('should apply rate limiting', async () => {
    const requests = Array.from({ length: 12 }, () =>
      new NextRequest('http://localhost:3000/api/auth/customer/google/init', {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          'X-Forwarded-For': '127.0.0.1',
        },
        body: JSON.stringify({
          tenantId: 1,
          clientType: 'web',
        }),
      })
    );

    // Make requests rapidly
    const responses = await Promise.all(
      requests.map(request => POST(request))
    );

    // Should have at least one rate limited response
    const rateLimited = responses.some(response => response.status === 429);
    expect(rateLimited).toBe(true);
  });
});
```

## End-to-End Tests

### OAuth Flow Test

```typescript
// __tests__/e2e/oauth-flow.test.ts
import { test, expect } from '@playwright/test';

test.describe('Google OAuth Flow', () => {
  test('should complete OAuth flow for web client', async ({ page, context }) => {
    // Navigate to login page
    await page.goto('/login');

    // Click Google OAuth button
    await page.click('[data-testid="google-oauth-button"]');

    // Should redirect to OAuth init endpoint and then to Google
    await page.waitForURL(/accounts\.google\.com/);

    // Mock Google OAuth response
    await page.route('**/oauth/authorize**', async route => {
      const url = new URL(route.request().url());
      const state = url.searchParams.get('state');
      const redirectUri = url.searchParams.get('redirect_uri');
      
      // Simulate successful OAuth
      await route.fulfill({
        status: 302,
        headers: {
          'Location': `${redirectUri}?code=test-auth-code&state=${state}`,
        },
      });
    });

    // Mock OAuth callback API
    await page.route('**/api/auth/customer/google/callback', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          customer: {
            id: 'cust_123',
            email: '<EMAIL>',
            firstName: 'John',
            lastName: 'Doe',
            displayName: 'John Doe',
            tenantId: 1,
            isEmailVerified: true,
            membershipType: 'basic',
          },
          tokens: {
            accessToken: 'mock-access-token',
            refreshToken: 'mock-refresh-token',
            tokenType: 'Bearer',
            expiresIn: 3600,
          },
          isNewCustomer: false,
        }),
      });
    });

    // Should redirect back to callback page
    await page.waitForURL('**/auth/callback**');

    // Should eventually redirect to dashboard
    await page.waitForURL('**/dashboard**');

    // Verify user is logged in
    await expect(page.locator('[data-testid="user-menu"]')).toBeVisible();
  });
});
```

## Performance Tests

### Load Testing

```typescript
// __tests__/performance/auth-load.test.ts
import { describe, it, expect } from '@jest/globals';

describe('Authentication Load Tests', () => {
  it('should handle concurrent OAuth initializations', async () => {
    const concurrentRequests = 50;
    const startTime = Date.now();

    const requests = Array.from({ length: concurrentRequests }, (_, i) =>
      fetch('http://localhost:3000/api/auth/customer/google/init', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          tenantId: 1,
          clientType: 'web',
          deviceId: `device-${i}`,
        }),
      })
    );

    const responses = await Promise.all(requests);
    const endTime = Date.now();

    // All requests should complete within reasonable time
    expect(endTime - startTime).toBeLessThan(5000); // 5 seconds

    // Most requests should succeed (some may be rate limited)
    const successCount = responses.filter(r => r.status === 200).length;
    expect(successCount).toBeGreaterThan(concurrentRequests * 0.8); // 80% success rate
  });

  it('should handle JWT validation performance', async () => {
    // Generate test tokens
    const tokens = await Promise.all(
      Array.from({ length: 100 }, () =>
        customerJWTService.generateTokenPair(mockCustomer, mockSessionParams)
      )
    );

    const startTime = Date.now();

    // Validate all tokens concurrently
    const validations = await Promise.all(
      tokens.map(token =>
        customerJWTService.validateToken({
          token: token.accessToken,
          tenantId: 1,
        })
      )
    );

    const endTime = Date.now();

    // Should validate 100 tokens in under 1 second
    expect(endTime - startTime).toBeLessThan(1000);

    // All validations should succeed
    expect(validations.every(v => v.valid)).toBe(true);
  });
});
```

## Test Commands

```bash
# Run all tests
npm test

# Run unit tests only
npm run test:unit

# Run integration tests
npm run test:integration

# Run e2e tests
npm run test:e2e

# Run performance tests
npm run test:performance

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch
```

## Test Data Management

### Test Fixtures

```typescript
// __tests__/fixtures/customers.ts
export const testCustomers = {
  basic: {
    id: 'cust_basic_123',
    email: '<EMAIL>',
    firstName: 'Basic',
    lastName: 'User',
    displayName: 'Basic User',
    tenantId: 1,
    membershipType: 'basic',
    isEmailVerified: true,
  },
  premium: {
    id: 'cust_premium_123',
    email: '<EMAIL>',
    firstName: 'Premium',
    lastName: 'User',
    displayName: 'Premium User',
    tenantId: 1,
    membershipType: 'premium',
    isEmailVerified: true,
  },
  unverified: {
    id: 'cust_unverified_123',
    email: '<EMAIL>',
    firstName: 'Unverified',
    lastName: 'User',
    displayName: 'Unverified User',
    tenantId: 1,
    membershipType: 'basic',
    isEmailVerified: false,
  },
};
```

### Database Seeding

```typescript
// scripts/seed-test-data.ts
import { db } from '@/lib/db';
import { customers } from '@/lib/db/schema';
import { testCustomers } from '../__tests__/fixtures/customers';

export async function seedTestData() {
  // Clear existing test data
  await db.delete(customers).where(eq(customers.email, like('%@example.com')));

  // Insert test customers
  await db.insert(customers).values(Object.values(testCustomers));

  console.log('Test data seeded successfully');
}
```
