import { auth } from "./config";
import { db } from "../db";
import { users, verificationTokens } from "../db/schema";
import { eq, and, gt } from "drizzle-orm";
import { hash } from "bcryptjs";
import { createId } from "@paralleldrive/cuid2";
import { nanoid } from "nanoid";
import type { AuthUser, UserRole } from "@/types/auth";

// Get current session
export async function getCurrentUser(): Promise<AuthUser | null> {
  try {
    const session = await auth();

    if (!session?.user?.id) {
      return null;
    }

    const [user] = await db
      .select()
      .from(users)
      .where(eq(users.id, session.user.id))
      .limit(1);

    if (!user) {
      return null;
    }

    return {
      id: user.id,
      email: user.email,
      name: user.name || undefined,
      image: user.image || undefined,
      role: user.role as UserRole,
      organizationId: user.organizationId || undefined,
      tenantId: user.tenantId || undefined,
      emailVerified: user.emailVerified ? new Date(user.emailVerified) : undefined,
    };
  } catch (error) {
    console.error("Error fetching current user:", error);
    return null;
  }
}

// Check if user has specific role
export async function hasRole(role: UserRole): Promise<boolean> {
  const user = await getCurrentUser();
  return user?.role === role;
}

// Check if user is admin
export async function isAdmin(): Promise<boolean> {
  return hasRole("admin");
}

// Check if user is authenticated
export async function isAuthenticated(): Promise<boolean> {
  const session = await auth();
  return !!session?.user;
}

// Require authentication (throw error if not authenticated)
export async function requireAuth(): Promise<AuthUser> {
  const user = await getCurrentUser();
  if (!user) {
    throw new Error("Authentication required");
  }
  return user;
}

// Require admin role (throw error if not admin)
export async function requireAdmin(): Promise<AuthUser> {
  const user = await requireAuth();
  if (user.role !== "admin") {
    throw new Error("Admin access required");
  }
  return user;
}

// Create user with hashed password
export async function createUser(data: {
  email: string;
  password: string;
  name: string;
  organizationId?: string;
}): Promise<AuthUser> {
  const hashedPassword = await hash(data.password, 12);
  
  const [user] = await db
    .insert(users)
    .values({
      id: createId(),
      email: data.email,
      password: hashedPassword,
      name: data.name,
      organizationId: data.organizationId,
      role: "user",
    })
    .returning();

  return {
    id: user.id,
    email: user.email,
    name: user.name,
    image: user.image,
    role: user.role as UserRole,
    organizationId: user.organizationId,
  };
}

// Generate email verification token
export async function generateEmailVerificationToken(email: string): Promise<string> {
  const token = nanoid(32);
  const expires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

  // Delete existing tokens for this email
  await db
    .delete(verificationTokens)
    .where(eq(verificationTokens.identifier, email));

  // Create new token
  await db
    .insert(verificationTokens)
    .values({
      identifier: email,
      token,
      expires,
    });

  return token;
}

// Verify email with token
export async function verifyEmailToken(token: string): Promise<boolean> {
  try {
    const [verificationToken] = await db
      .select()
      .from(verificationTokens)
      .where(
        and(
          eq(verificationTokens.token, token),
          gt(verificationTokens.expires, new Date())
        )
      )
      .limit(1);

    if (!verificationToken) {
      return false;
    }

    // Update user email verification status
    await db
      .update(users)
      .set({ emailVerified: new Date() })
      .where(eq(users.email, verificationToken.identifier));

    // Delete used token
    await db
      .delete(verificationTokens)
      .where(eq(verificationTokens.token, token));

    return true;
  } catch (error) {
    console.error("Error verifying email token:", error);
    return false;
  }
}

// Generate password reset token
export async function generatePasswordResetToken(email: string): Promise<string | null> {
  // Check if user exists
  const [user] = await db
    .select()
    .from(users)
    .where(eq(users.email, email))
    .limit(1);

  if (!user) {
    return null;
  }

  const token = nanoid(32);
  const expires = new Date(Date.now() + 60 * 60 * 1000); // 1 hour

  // Delete existing tokens for this email (with reset prefix)
  await db
    .delete(verificationTokens)
    .where(eq(verificationTokens.identifier, `reset:${email}`));

  // Create new token
  await db
    .insert(verificationTokens)
    .values({
      identifier: `reset:${email}`,
      token,
      expires,
    });

  return token;
}

// Reset password with token
export async function resetPasswordWithToken(
  token: string,
  newPassword: string
): Promise<boolean> {
  try {
    const [resetToken] = await db
      .select()
      .from(verificationTokens)
      .where(
        and(
          eq(verificationTokens.token, token),
          gt(verificationTokens.expires, new Date())
        )
      )
      .limit(1);

    if (!resetToken || !resetToken.identifier.startsWith('reset:')) {
      return false;
    }

    // Extract email from identifier
    const email = resetToken.identifier.replace('reset:', '');

    // Hash new password
    const hashedPassword = await hash(newPassword, 12);

    // Update user password
    await db
      .update(users)
      .set({ password: hashedPassword })
      .where(eq(users.email, email));

    // Delete used token
    await db
      .delete(verificationTokens)
      .where(eq(verificationTokens.token, token));

    return true;
  } catch (error) {
    console.error("Error resetting password:", error);
    return false;
  }
}

// Change user password
export async function changePassword(
  userId: string,
  currentPassword: string,
  newPassword: string
): Promise<boolean> {
  try {
    const [user] = await db
      .select()
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);

    if (!user || !user.password) {
      return false;
    }

    // Verify current password
    const { compare } = await import("bcryptjs");
    const isCurrentPasswordValid = await compare(currentPassword, user.password);
    
    if (!isCurrentPasswordValid) {
      return false;
    }

    // Hash new password
    const hashedPassword = await hash(newPassword, 12);

    // Update password
    await db
      .update(users)
      .set({ password: hashedPassword })
      .where(eq(users.id, userId));

    return true;
  } catch (error) {
    console.error("Error changing password:", error);
    return false;
  }
}
