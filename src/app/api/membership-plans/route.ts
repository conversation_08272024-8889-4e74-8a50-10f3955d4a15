import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { MembershipPlanService } from "@/lib/services/membership-plan.service";

/**
 * API Routes untuk Membership Plans
 * 
 * <PERSON>gi<PERSON><PERSON> pattern yang sama dengan classes, class-categories, dan class-subcategories.
 * Semua validation menggunakan Zod, error handling yang proper.
 */

// Validation schemas
const searchParamsSchema = z.object({
  tenantId: z.string().transform((val) => parseInt(val, 10)),
  search: z.string().optional(),
  is_active: z.string().transform((val) => val === 'true').optional(),
  limit: z.string().transform((val) => parseInt(val, 10)).optional(),
  offset: z.string().transform((val) => parseInt(val, 10)).optional(),
});

const createMembershipPlanSchema = z.object({
  tenantId: z.number().int().positive("Tenant ID must be positive"),
  name: z.string().min(1, "Name is required").max(255, "Name too long"),
  description: z.string().max(255, "Description too long").optional(),
  price: z.number().int().min(0, "Price must be non-negative").optional(),
  currency: z.string().max(255, "Currency too long").optional(),
  duration_value: z.number().int().positive("Duration must be positive").optional(),
  duration_unit: z.string().max(50, "Duration unit too long").optional(),
  is_active: z.boolean().optional(),
});

/**
 * GET /api/membership-plans
 * Search membership plans with filtering and pagination
 * 
 * Query parameters:
 * - tenantId: required untuk tenant isolation
 * - search: optional search by name
 * - is_active: optional filter by active status
 * - limit: optional pagination limit (default 20)
 * - offset: optional pagination offset (default 0)
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Parse and validate query parameters
    const rawParams = {
      tenantId: searchParams.get("tenantId"),
      search: searchParams.get("search"),
      is_active: searchParams.get("is_active"),
      limit: searchParams.get("limit"),
      offset: searchParams.get("offset"),
    };

    // Remove null values
    const cleanParams = Object.fromEntries(
      Object.entries(rawParams).filter(([_, value]) => value !== null)
    );

    const validatedParams = searchParamsSchema.parse(cleanParams);

    // Call service
    const result = await MembershipPlanService.searchPlans(
      validatedParams.tenantId,
      validatedParams.search,
      validatedParams.is_active,
      validatedParams.limit || 20,
      validatedParams.offset || 0
    );

    return NextResponse.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error("GET /api/membership-plans error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: "Invalid parameters", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/membership-plans
 * Create a new membership plan
 * 
 * Body harus contain:
 * - tenantId: untuk tenant isolation
 * - name: nama plan
 * - description: deskripsi (optional)
 * - price: harga (optional)
 * - currency: mata uang (optional)
 * - duration_value: durasi value (optional)
 * - duration_unit: durasi unit (optional)
 * - is_active: status aktif (optional, default true)
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate request body
    const validatedData = createMembershipPlanSchema.parse(body);

    // Call service untuk create
    const planResult = await MembershipPlanService.create(validatedData);

    return NextResponse.json({
      success: true,
      data: planResult,
      message: "Membership plan created successfully",
    }, { status: 201 });
  } catch (error) {
    console.error("POST /api/membership-plans error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      if (error.message.includes("Tenant with ID") && error.message.includes("not found")) {
        return NextResponse.json(
          { success: false, error: "Tenant not found" },
          { status: 404 }
        );
      }
    }

    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}
