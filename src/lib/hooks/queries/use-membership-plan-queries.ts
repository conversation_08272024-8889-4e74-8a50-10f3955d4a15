import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { MembershipPlan } from '@/lib/db/schema';

/**
 * Membership Plan Queries Hooks
 * 
 * Hooks untuk manage state dan data fetching membership plans menggunakan TanStack Query.
 * Pattern ini mengikuti use-class-queries.ts yang sudah established dan proven.
 * 
 * Ini kayak "jembatan" antara UI components dan service layer.
 * Semua caching, error handling, dan optimistic updates dihandle di sini.
 */

// Query Keys - ini penting untuk caching strategy
export const membershipPlanKeys = {
  all: ['membership-plans'] as const,
  lists: () => [...membershipPlanKeys.all, 'list'] as const,
  list: (filters: Record<string, any>) => [...membershipPlanKeys.lists(), { filters }] as const,
  details: () => [...membershipPlanKeys.all, 'detail'] as const,
  detail: (id: string) => [...membershipPlanKeys.details(), id] as const,
  byTenant: (tenantId: number) => [...membershipPlanKeys.all, 'tenant', tenantId] as const,
  active: (tenantId: number) => [...membershipPlanKeys.all, 'active', tenantId] as const,
  search: (params: any) => [...membershipPlanKeys.all, 'search', params] as const,
  stats: (tenantId: number) => [...membershipPlanKeys.all, 'stats', tenantId] as const,
};

// API Functions - ini yang handle komunikasi dengan backend
const membershipPlanApi = {
  getByTenant: async (tenantId: number): Promise<MembershipPlan[]> => {
    const params = new URLSearchParams({
      tenantId: tenantId.toString(),
    });
    const response = await fetch(`/api/membership-plans?${params}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch membership plans: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data?.plans || [];
  },

  getActive: async (tenantId: number): Promise<MembershipPlan[]> => {
    const params = new URLSearchParams({
      tenantId: tenantId.toString(),
      is_active: 'true',
    });
    const response = await fetch(`/api/membership-plans?${params}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch active membership plans: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data?.plans || [];
  },

  search: async (
    tenantId: number, 
    searchTerm?: string,
    is_active?: boolean,
    limit = 20, 
    offset = 0
  ) => {
    const params = new URLSearchParams({
      tenantId: tenantId.toString(),
      limit: limit.toString(),
      offset: offset.toString(),
    });
    if (searchTerm) {
      params.append('search', searchTerm);
    }
    if (is_active !== undefined) {
      params.append('is_active', is_active.toString());
    }
    const response = await fetch(`/api/membership-plans?${params}`);
    if (!response.ok) {
      throw new Error(`Failed to search membership plans: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data;
  },

  getById: async (id: string): Promise<MembershipPlan> => {
    const response = await fetch(`/api/membership-plans/${id}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch membership plan: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data;
  },

  create: async (data: {
    tenantId: number;
    name: string;
    description?: string;
    price?: number;
    currency?: string;
    duration_value?: number;
    duration_unit?: string;
    is_active?: boolean;
  }): Promise<MembershipPlan> => {
    const response = await fetch('/api/membership-plans', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      throw new Error(`Failed to create membership plan: ${response.statusText}`);
    }
    const result = await response.json();
    return result.data;
  },

  update: async (id: string, tenantId: number, data: {
    name?: string;
    description?: string;
    price?: number;
    currency?: string;
    duration_value?: number;
    duration_unit?: string;
    is_active?: boolean;
  }): Promise<MembershipPlan> => {
    const response = await fetch(`/api/membership-plans/${id}?tenantId=${tenantId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      throw new Error(`Failed to update membership plan: ${response.statusText}`);
    }
    const result = await response.json();
    return result.data;
  },

  delete: async (id: string, tenantId: number): Promise<void> => {
    const response = await fetch(`/api/membership-plans/${id}?tenantId=${tenantId}`, {
      method: 'DELETE',
    });
    if (!response.ok) {
      throw new Error(`Failed to delete membership plan: ${response.statusText}`);
    }
  },

  getStats: async (tenantId: number) => {
    const response = await fetch(`/api/membership-plans/stats?tenantId=${tenantId}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch plan stats: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data;
  },
};

// Query Hooks - ini yang dipake di components
export function useMembershipPlans(tenantId: number) {
  return useQuery({
    queryKey: membershipPlanKeys.byTenant(tenantId),
    queryFn: () => membershipPlanApi.getByTenant(tenantId),
    enabled: !!tenantId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useActiveMembershipPlans(tenantId: number) {
  return useQuery({
    queryKey: membershipPlanKeys.active(tenantId),
    queryFn: () => membershipPlanApi.getActive(tenantId),
    enabled: !!tenantId,
    staleTime: 5 * 60 * 1000,
  });
}

export function useMembershipPlan(id: string) {
  return useQuery({
    queryKey: membershipPlanKeys.detail(id),
    queryFn: () => membershipPlanApi.getById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
}

export function useMembershipPlanSearch(
  tenantId: number, 
  searchTerm?: string,
  is_active?: boolean,
  limit = 20, 
  offset = 0
) {
  return useQuery({
    queryKey: membershipPlanKeys.search({ tenantId, searchTerm, is_active, limit, offset }),
    queryFn: () => membershipPlanApi.search(tenantId, searchTerm, is_active, limit, offset),
    enabled: !!tenantId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

export function useMembershipPlanStats(tenantId: number) {
  return useQuery({
    queryKey: membershipPlanKeys.stats(tenantId),
    queryFn: () => membershipPlanApi.getStats(tenantId),
    enabled: !!tenantId,
    staleTime: 2 * 60 * 1000,
  });
}

// Mutation Hooks - ini untuk create, update, delete
export function useCreateMembershipPlan() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: membershipPlanApi.create,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: membershipPlanKeys.all });
    },
  });
}

export function useUpdateMembershipPlan() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, tenantId, data }: { 
      id: string; 
      tenantId: number; 
      data: {
        name?: string;
        description?: string;
        price?: number;
        currency?: string;
        duration_value?: number;
        duration_unit?: string;
        is_active?: boolean;
      }
    }) => {
      return membershipPlanApi.update(id, tenantId, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: membershipPlanKeys.all });
    },
  });
}

export function useDeleteMembershipPlan() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, tenantId }: { id: string; tenantId: number }) => {
      return membershipPlanApi.delete(id, tenantId);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: membershipPlanKeys.all });
    },
  });
}

// Export types
export interface MembershipPlanFormData {
  tenantId: number;
  name: string;
  description?: string;
  price?: number;
  currency?: string;
  duration_value?: number;
  duration_unit?: string;
  is_active?: boolean;
}

export interface MembershipPlanSearchParams {
  tenantId: number;
  search?: string;
  is_active?: boolean;
  limit?: number;
  offset?: number;
}
