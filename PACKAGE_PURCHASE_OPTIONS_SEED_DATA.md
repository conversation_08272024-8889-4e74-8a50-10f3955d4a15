# 📦 Package Purchase Options Seed Data Documentation

## 🎯 Overview

Dokumentasi ini menjelaskan seed data yang telah dibuat untuk tabel `package_purchase_options`. Seed data ini dirancang untuk mendemonstrasikan semua fitur yang telah diimplementasikan dengan skenario bisnis yang realistis.

## 🗂️ Structure Seed Data

### 📦 **Sample Packages**

Jika belum ada packages di database, script akan membuat 8 sample packages:

1. **Basic Fitness Membership** - Package dasar untuk gym
2. **Premium VIP Package** - Package premium eksklusif
3. **Trial Package** - Package trial 7 hari
4. **Student Discount Package** - Package diskon untuk mahasiswa
5. **Corporate Wellness Package** - Package untuk perusahaan
6. **Yoga & Meditation Package** - Package khusus yoga
7. **Family Package** - Package untuk keluarga
8. **Senior Citizen Package** - Package untuk lansia

### 📍 **Sample Locations**

Jika belum ada locations di database, script akan membuat 4 sample locations:

1. **Downtown Fitness Center** - Pusat kota New York
2. **Uptown Wellness Hub** - Uptown New York
3. **Brooklyn Sports Complex** - Brooklyn
4. **Queens Community Center** - Queens

## 🛒 **Package Purchase Options Scenarios**

### 1. **Basic Fitness Membership**
```typescript
{
  package_id: "akow8xayijki2t9ijx42k0dq",
  purchase_limit: null,           // ✅ Unlimited
  restrict_to: "all",            // ✅ Semua customer
  transferable: true,            // ✅ Bisa dipindahtangankan
  specify_sold_at_location: false, // ✅ Semua lokasi
  sold_at_location_id: null,
  class_booking_limit: null,     // ✅ Unlimited classes
  show_online: true,             // ✅ Tampil online
}
```
**Skenario**: Package membership dasar yang fleksibel dan mudah diakses semua orang.

### 2. **Premium VIP Package**
```typescript
{
  package_id: "rgw47s1kb9qcexcniwedl7z1",
  purchase_limit: 50,            // ✅ Terbatas 50 customer
  restrict_to: "member_only",    // ✅ Member saja
  transferable: false,           // ✅ Tidak bisa dipindahtangankan
  specify_sold_at_location: true, // ✅ Lokasi spesifik
  sold_at_location_id: "downtown_center_id",
  class_booking_limit: 20,       // ✅ Maksimal 20 kelas
  show_online: true,             // ✅ Tampil online
}
```
**Skenario**: Package eksklusif dengan pembatasan ketat untuk menjaga eksklusivitas.

### 3. **Trial Package**
```typescript
{
  package_id: "trial_package_id",
  purchase_limit: 100,           // ✅ Terbatas 100 customer
  restrict_to: "new_customers",  // ✅ Customer baru saja
  transferable: false,           // ✅ Tidak bisa dipindahtangankan
  specify_sold_at_location: false, // ✅ Semua lokasi
  sold_at_location_id: null,
  class_booking_limit: 3,        // ✅ Hanya 3 kelas trial
  show_online: true,             // ✅ Tampil online
}
```
**Skenario**: Package trial untuk menarik customer baru dengan pembatasan yang wajar.

### 4. **Student Discount Package**
```typescript
{
  package_id: "student_package_id",
  purchase_limit: 200,           // ✅ Terbatas 200 mahasiswa
  restrict_to: "new_customers",  // ✅ Mahasiswa baru
  transferable: true,            // ✅ Bisa dipindahtangankan
  specify_sold_at_location: false, // ✅ Semua lokasi
  sold_at_location_id: null,
  class_booking_limit: 15,       // ✅ 15 kelas per bulan
  show_online: true,             // ✅ Tampil online
}
```
**Skenario**: Package khusus mahasiswa dengan harga diskon dan fleksibilitas transfer.

### 5. **Corporate Wellness Package**
```typescript
{
  package_id: "corporate_package_id",
  purchase_limit: 10,            // ✅ Hanya 10 perusahaan
  restrict_to: "existing_customers", // ✅ Klien korporat existing
  transferable: false,           // ✅ Tidak bisa dipindahtangankan
  specify_sold_at_location: false, // ✅ Semua lokasi
  sold_at_location_id: null,
  class_booking_limit: null,     // ✅ Unlimited untuk korporat
  show_online: false,            // ✅ Private package
}
```
**Skenario**: Package B2B yang private dan eksklusif untuk klien korporat.

### 6. **Yoga & Meditation Package**
```typescript
{
  package_id: "yoga_package_id",
  purchase_limit: 75,            // ✅ Terbatas 75 customer
  restrict_to: "all",            // ✅ Semua customer
  transferable: true,            // ✅ Bisa dipindahtangankan
  specify_sold_at_location: true, // ✅ Lokasi khusus yoga
  sold_at_location_id: "uptown_wellness_id",
  class_booking_limit: 12,       // ✅ 12 sesi yoga
  show_online: true,             // ✅ Tampil online
}
```
**Skenario**: Package spesialisasi yang hanya tersedia di lokasi dengan fasilitas yoga.

### 7. **Family Package**
```typescript
{
  package_id: "family_package_id",
  purchase_limit: 150,           // ✅ 150 keluarga
  restrict_to: "all",            // ✅ Semua customer
  transferable: true,            // ✅ Transfer antar anggota keluarga
  specify_sold_at_location: false, // ✅ Semua lokasi
  sold_at_location_id: null,
  class_booking_limit: 40,       // ✅ 40 kelas untuk sekeluarga
  show_online: true,             // ✅ Tampil online
}
```
**Skenario**: Package keluarga dengan kuota besar dan fleksibilitas tinggi.

### 8. **Senior Citizen Package**
```typescript
{
  package_id: "senior_package_id",
  purchase_limit: 80,            // ✅ 80 lansia
  restrict_to: "existing_customers", // ✅ Lansia yang sudah terverifikasi
  transferable: false,           // ✅ Personal, tidak bisa transfer
  specify_sold_at_location: true, // ✅ Lokasi dengan fasilitas senior
  sold_at_location_id: "brooklyn_complex_id",
  class_booking_limit: 8,        // ✅ 8 kelas gentle
  show_online: true,             // ✅ Tampil online
}
```
**Skenario**: Package khusus lansia dengan pembatasan untuk keamanan dan kenyamanan.

## 🎯 **Features Demonstrated**

### ✅ **Purchase Limits**
- **Unlimited**: Basic Fitness, Corporate Wellness
- **Small Limits**: Trial (100), Corporate (10)
- **Medium Limits**: Premium VIP (50), Yoga (75), Senior (80)
- **Large Limits**: Student (200), Family (150)

### ✅ **Restrict To Options**
- **"all"**: Basic Fitness, Yoga, Family
- **"member_only"**: Premium VIP
- **"new_customers"**: Trial, Student
- **"existing_customers"**: Corporate, Senior

### ✅ **Transferable Options**
- **True**: Basic Fitness, Student, Yoga, Family
- **False**: Premium VIP, Trial, Corporate, Senior

### ✅ **Location Specific**
- **All Locations**: Basic Fitness, Trial, Student, Corporate, Family
- **Specific Locations**: 
  - Premium VIP → Downtown Fitness Center
  - Yoga → Uptown Wellness Hub
  - Senior → Brooklyn Sports Complex

### ✅ **Class Booking Limits**
- **Unlimited**: Basic Fitness, Corporate
- **Small Limits**: Trial (3), Senior (8)
- **Medium Limits**: Yoga (12), Student (15), Premium VIP (20)
- **Large Limits**: Family (40)

### ✅ **Show Online**
- **True**: Semua package kecuali Corporate
- **False**: Corporate Wellness (private package)

## 🚀 **How to Run Seed**

### **Method 1: Direct Script**
```bash
npx tsx scripts/seed-package-purchase-options.ts
```

### **Method 2: Using Runner Script**
```bash
npx tsx scripts/run-seed-package-purchase-options.ts
```

### **Method 3: Add to package.json**
```json
{
  "scripts": {
    "seed:package-purchase-options": "tsx scripts/seed-package-purchase-options.ts"
  }
}
```

Then run:
```bash
npm run seed:package-purchase-options
```

## 🔍 **Verification**

Setelah menjalankan seed, Anda dapat memverifikasi data dengan:

### **1. Check Database**
```sql
SELECT 
  ppo.*,
  p.name as package_name,
  l.name as location_name
FROM package_purchase_options ppo
LEFT JOIN package p ON ppo.package_id = p.id
LEFT JOIN locations l ON ppo.sold_at_location_id = l.id;
```

### **2. Test API Endpoints**
```bash
# Get all package purchase options
curl http://localhost:3000/api/package-purchase-options

# Get by package ID
curl http://localhost:3000/api/package-purchase-options/by-package/akow8xayijki2t9ijx42k0dq

# Get with details
curl http://localhost:3000/api/package-purchase-options/with-details/akow8xayijki2t9ijx42k0dq

# Get stats
curl http://localhost:3000/api/package-purchase-options/stats
```

### **3. Test UI**
1. Buka `/packages` di browser
2. Klik "Create Package" atau edit package existing
3. Scroll ke section "Purchase Options"
4. Verify form fields dan dropdown locations

## 📊 **Expected Results**

Setelah seeding berhasil, Anda akan memiliki:

- ✅ **8 packages** dengan berbagai karakteristik
- ✅ **4 locations** di area New York
- ✅ **8 package purchase options** dengan skenario realistis
- ✅ **Semua fitur** yang diimplementasikan terdemonstrasikan
- ✅ **Data yang konsisten** dan valid secara relasional

## 🔧 **Troubleshooting**

### **Issue: Foreign Key Constraint Error**
**Solution**: Pastikan packages dan locations sudah ada sebelum membuat purchase options.

### **Issue: Duplicate Key Error**
**Solution**: Script sudah handle existing data, tapi jika ada konflik, hapus data existing terlebih dahulu.

### **Issue: Location Not Found**
**Solution**: Pastikan locations table sudah di-seed atau gunakan location IDs yang valid.

---

## 📝 **Summary**

Seed data ini memberikan foundation yang solid untuk testing dan development fitur Package Purchase Options. Dengan 8 skenario bisnis yang berbeda, semua fitur yang diimplementasikan dapat ditest secara comprehensive.

**Key Benefits:**
- ✅ Realistic business scenarios
- ✅ Complete feature coverage
- ✅ Valid relational data
- ✅ Easy to understand and modify
- ✅ Production-ready examples
