import { getSession } from "next-auth/react";

/**
 * API Client dengan RBAC Context
 * 
 * Client ini automatically include RBAC headers dalam API calls.
 * Ini ensure backend bisa validate permissions dengan benar.
 */

interface APIOptions extends RequestInit {
  /** Include RBAC headers (default: true) */
  includeRBACHeaders?: boolean;
  /** Custom tenant ID (override session tenant) */
  tenantId?: number;
  /** Custom location ID untuk location-based operations */
  locationId?: string;
}

class RBACAPIClient {
  private baseURL: string;

  constructor(baseURL: string = "/api") {
    this.baseURL = baseURL;
  }

  /**
   * Get RBAC headers dari session
   */
  private async getRBACHeaders(): Promise<Record<string, string>> {
    const session = await getSession();
    
    if (!session) {
      return {};
    }

    return {
      "x-user-id": session.user.id,
      "x-tenant-id": session.user.tenantId?.toString() || "",
      "x-user-roles": JSON.stringify(session.user.roles || []),
      "x-user-permissions": JSON.stringify(session.user.permissions || []),
      "x-accessible-locations": JSON.stringify(session.user.accessibleLocations || []),
    };
  }

  /**
   * Generic fetch dengan RBAC headers
   */
  private async fetchWithRBAC(
    endpoint: string,
    options: APIOptions = {}
  ): Promise<Response> {
    const {
      includeRBACHeaders = true,
      tenantId,
      locationId,
      headers = {},
      ...fetchOptions
    } = options;

    const url = `${this.baseURL}${endpoint}`;
    
    let finalHeaders: Record<string, string> = {
      "Content-Type": "application/json",
      ...headers,
    };

    // Include RBAC headers
    if (includeRBACHeaders) {
      const rbacHeaders = await this.getRBACHeaders();
      finalHeaders = { ...finalHeaders, ...rbacHeaders };
    }

    // Override tenant ID kalau ada
    if (tenantId) {
      finalHeaders["x-tenant-id"] = tenantId.toString();
    }

    // Include location ID kalau ada
    if (locationId) {
      finalHeaders["x-location-id"] = locationId;
    }

    const response = await fetch(url, {
      ...fetchOptions,
      headers: finalHeaders,
    });

    // Handle RBAC errors
    if (response.status === 403) {
      throw new Error("Insufficient permissions");
    }

    if (response.status === 401) {
      throw new Error("Authentication required");
    }

    return response;
  }

  /**
   * GET request dengan RBAC
   */
  async get<T>(endpoint: string, options: APIOptions = {}): Promise<T> {
    const response = await this.fetchWithRBAC(endpoint, {
      ...options,
      method: "GET",
    });

    if (!response.ok) {
      throw new Error(`GET ${endpoint} failed: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * POST request dengan RBAC
   */
  async post<T>(
    endpoint: string,
    data?: any,
    options: APIOptions = {}
  ): Promise<T> {
    const response = await this.fetchWithRBAC(endpoint, {
      ...options,
      method: "POST",
      body: data ? JSON.stringify(data) : undefined,
    });

    if (!response.ok) {
      throw new Error(`POST ${endpoint} failed: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * PUT request dengan RBAC
   */
  async put<T>(
    endpoint: string,
    data?: any,
    options: APIOptions = {}
  ): Promise<T> {
    const response = await this.fetchWithRBAC(endpoint, {
      ...options,
      method: "PUT",
      body: data ? JSON.stringify(data) : undefined,
    });

    if (!response.ok) {
      throw new Error(`PUT ${endpoint} failed: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * DELETE request dengan RBAC
   */
  async delete<T>(endpoint: string, options: APIOptions = {}): Promise<T> {
    const response = await this.fetchWithRBAC(endpoint, {
      ...options,
      method: "DELETE",
    });

    if (!response.ok) {
      throw new Error(`DELETE ${endpoint} failed: ${response.statusText}`);
    }

    return response.json();
  }

  /**
   * PATCH request dengan RBAC
   */
  async patch<T>(
    endpoint: string,
    data?: any,
    options: APIOptions = {}
  ): Promise<T> {
    const response = await this.fetchWithRBAC(endpoint, {
      ...options,
      method: "PATCH",
      body: data ? JSON.stringify(data) : undefined,
    });

    if (!response.ok) {
      throw new Error(`PATCH ${endpoint} failed: ${response.statusText}`);
    }

    return response.json();
  }
}

// Export singleton instance
export const apiClient = new RBACAPIClient();

/**
 * Hook untuk API calls dengan RBAC
 * 
 * Ini provide easy access ke API client dalam React components.
 */
export function useAPIClient() {
  return apiClient;
}

/**
 * Utility functions untuk common API patterns
 */
export const APIUtils = {
  /**
   * Handle API errors dengan user-friendly messages
   */
  handleError(error: any): string {
    if (error.message === "Insufficient permissions") {
      return "Anda tidak memiliki permission untuk melakukan operasi ini";
    }
    
    if (error.message === "Authentication required") {
      return "Silakan login terlebih dahulu";
    }
    
    if (error.message.includes("failed")) {
      return "Terjadi kesalahan saat memproses permintaan";
    }
    
    return error.message || "Terjadi kesalahan yang tidak diketahui";
  },

  /**
   * Build query string dengan tenant isolation
   */
  buildQuery(params: Record<string, any>, tenantId?: number): string {
    const query = new URLSearchParams();
    
    // Add tenant ID untuk isolation
    if (tenantId) {
      query.set("tenantId", tenantId.toString());
    }
    
    // Add other params
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        query.set(key, value.toString());
      }
    });
    
    return query.toString();
  },
};
