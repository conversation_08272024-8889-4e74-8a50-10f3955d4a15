"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { DashboardNavRedesigned } from "./nav-redesigned";
import { DashboardHeaderRedesigned } from "./header-redesigned";
import type { AuthUser } from "@/types/auth";

interface DashboardLayoutRedesignedProps {
  children: React.ReactNode;
  user: AuthUser;
}

export function DashboardLayoutRedesigned({ children, user }: DashboardLayoutRedesignedProps) {
  const [isNavCollapsed, setIsNavCollapsed] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <DashboardHeaderRedesigned user={user} />
      
      {/* Navigation */}
      <DashboardNavRedesigned user={user} />
      
      {/* Main Content */}
      <motion.main
        className="pt-16 transition-all duration-300 ease-in-out"
        style={{
          marginLeft: isMobile ? 0 : isNavCollapsed ? "80px" : "280px",
        }}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <div className="container mx-auto p-6 max-w-7xl">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.1, duration: 0.3 }}
          >
            {children}
          </motion.div>
        </div>
      </motion.main>
    </div>
  );
}
