"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { 
  DollarSign, 
  Plus, 
  Building, 
  BarChart3,
  AlertCircle,
  CheckCircle,
  Eye,
  EyeOff,
  Star,
  StarOff,
  Percent
} from "lucide-react";
import { PricingGroupForm } from "@/components/forms/pricing-group-form";
import { PricingGroupList } from "@/components/pricing-groups/pricing-group-list";
import { SuccessToast } from "@/components/ui/success-toast";
import { SuccessModal } from "@/components/ui/success-modal";
import { Confetti } from "@/components/ui/confetti";
import { PricingGroup, NewPricingGroup } from "@/lib/db/schema";
import { 
  usePricingGroups, 
  useCreatePricingGroup, 
  useUpdatePricingGroup,
  usePricingGroupStats
} from "@/lib/hooks/queries/use-pricing-group-queries";
import { useTenants } from "@/lib/hooks/queries/use-tenant-queries";

export default function PricingGroupsPage() {
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingPricingGroup, setEditingPricingGroup] = useState<PricingGroup | null>(null);
  const [selectedTenantId, setSelectedTenantId] = useState<number | null>(null);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [submitSuccess, setSubmitSuccess] = useState<string | null>(null);
  
  // Animation states
  const [showSuccessToast, setShowSuccessToast] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showConfetti, setShowConfetti] = useState(false);
  const [createdPricingGroup, setCreatedPricingGroup] = useState<PricingGroup | null>(null);

  const { data: pricingGroups = [], isLoading: pricingGroupsLoading } = usePricingGroups();
  const { data: tenantsData, isLoading: tenantsLoading } = useTenants();
  const { data: stats, isLoading: statsLoading } = usePricingGroupStats();
  const tenants = tenantsData?.tenants || [];
  const createPricingGroupMutation = useCreatePricingGroup();
  const updatePricingGroupMutation = useUpdatePricingGroup();

  // Calculate stats by tenant
  const statsByTenant = tenants.reduce((acc, tenant) => {
    const tenantPricingGroups = pricingGroups.filter(pg => pg.tenantId === tenant.id);
    acc[tenant.id] = {
      total: tenantPricingGroups.length,
      active: tenantPricingGroups.filter(pg => pg.isActive).length,
      default: tenantPricingGroups.filter(pg => pg.isDefault).length,
    };
    return acc;
  }, {} as Record<number, { total: number; active: number; default: number }>);

  const handleCreatePricingGroup = async (data: Omit<NewPricingGroup, "id" | "createdAt" | "updatedAt">) => {
    try {
      setSubmitError(null);
      const newPricingGroup = await createPricingGroupMutation.mutateAsync(data);
      
      setCreatedPricingGroup(newPricingGroup);
      setIsFormOpen(false);
      
      // Start celebration sequence
      setTimeout(() => {
        setShowConfetti(true);
        setShowSuccessToast(true);
        setShowSuccessModal(true);
      }, 300);
      
      setTimeout(() => setShowConfetti(false), 4000);
      
    } catch (error) {
      setSubmitError(error instanceof Error ? error.message : "Failed to create pricing group");
    }
  };

  const handleUpdatePricingGroup = async (data: Omit<NewPricingGroup, "id" | "createdAt" | "updatedAt">) => {
    if (!editingPricingGroup) return;

    try {
      setSubmitError(null);
      await updatePricingGroupMutation.mutateAsync({ 
        id: editingPricingGroup.id, 
        data: data
      });
      setSubmitSuccess("Pricing group updated successfully!");
      setEditingPricingGroup(null);
      setTimeout(() => setSubmitSuccess(null), 3000);
    } catch (error) {
      setSubmitError(error instanceof Error ? error.message : "Failed to update pricing group");
    }
  };

  const handleEdit = (pricingGroup: PricingGroup) => {
    setEditingPricingGroup(pricingGroup);
  };

  const handleAdd = () => {
    setEditingPricingGroup(null);
    setIsFormOpen(true);
  };

  const handleCloseForm = () => {
    setIsFormOpen(false);
    setEditingPricingGroup(null);
    setSubmitError(null);
  };

  const handleCloseSuccessAnimations = () => {
    setShowSuccessToast(false);
    setShowSuccessModal(false);
    setShowConfetti(false);
    setCreatedPricingGroup(null);
  };

  const handleViewPricingGroup = () => {
    handleCloseSuccessAnimations();
  };

  const handleAddAnother = () => {
    handleCloseSuccessAnimations();
    setIsFormOpen(true);
  };

  if (pricingGroupsLoading || tenantsLoading || statsLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Pricing Groups</h1>
          <p className="text-gray-500 dark:text-gray-400">
            Manage pricing groups and their discount settings
          </p>
        </div>
        <Button onClick={handleAdd}>
          <Plus className="h-4 w-4 mr-2" />
          Add Pricing Group
        </Button>
      </div>

      {/* Success/Error Messages */}
      {submitSuccess && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>{submitSuccess}</AlertDescription>
        </Alert>
      )}

      {submitError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{submitError}</AlertDescription>
        </Alert>
      )}

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-6 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Groups</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.total || 0}</div>
            <p className="text-xs text-muted-foreground">
              Across all tenants
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Groups</CardTitle>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.active || 0}</div>
            <p className="text-xs text-muted-foreground">
              Currently available
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Inactive Groups</CardTitle>
            <EyeOff className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.inactive || 0}</div>
            <p className="text-xs text-muted-foreground">
              Currently hidden
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Default Groups</CardTitle>
            <Star className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.default || 0}</div>
            <p className="text-xs text-muted-foreground">
              Default for tenants
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">With Discounts</CardTitle>
            <Percent className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.withDiscount || 0}</div>
            <p className="text-xs text-muted-foreground">
              Have discount rates
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Discount</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.averageDiscount || 0}%</div>
            <p className="text-xs text-muted-foreground">
              Average discount rate
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="all" className="space-y-4">
        <TabsList>
          <TabsTrigger value="all">All Pricing Groups</TabsTrigger>
          {tenants.map((tenant) => {
            const tenantStats = statsByTenant[tenant.id];
            return (
              <TabsTrigger key={tenant.id} value={`tenant-${tenant.id}`}>
                {tenant.name}
                <Badge variant="secondary" className="ml-2">
                  {tenantStats?.total || 0}
                </Badge>
              </TabsTrigger>
            );
          })}
        </TabsList>

        <TabsContent value="all">
          <PricingGroupList
            onEdit={handleEdit}
            onAdd={handleAdd}
            showActions={true}
          />
        </TabsContent>

        {tenants.map((tenant) => (
          <TabsContent key={tenant.id} value={`tenant-${tenant.id}`}>
            <PricingGroupList
              tenantId={tenant.id}
              onEdit={handleEdit}
              onAdd={() => {
                setSelectedTenantId(tenant.id);
                handleAdd();
              }}
              showActions={true}
            />
          </TabsContent>
        ))}
      </Tabs>

      {/* Create/Edit Dialog */}
      <Dialog open={isFormOpen || !!editingPricingGroup} onOpenChange={handleCloseForm}>
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editingPricingGroup ? "Edit Pricing Group" : "Add New Pricing Group"}
            </DialogTitle>
          </DialogHeader>
          <PricingGroupForm
            entity={editingPricingGroup || undefined}
            tenantId={selectedTenantId || editingPricingGroup?.tenantId || (tenants[0]?.id ?? 1)}
            onSubmit={editingPricingGroup ? handleUpdatePricingGroup : handleCreatePricingGroup}
            onCancel={handleCloseForm}
            className="border-0 shadow-none"
          />
        </DialogContent>
      </Dialog>

      {/* Success Animations */}
      <Confetti isActive={showConfetti} />
      
      <SuccessToast
        isVisible={showSuccessToast}
        onClose={() => setShowSuccessToast(false)}
        title="Pricing Group Created Successfully!"
        description={createdPricingGroup ? 
          `${createdPricingGroup.name} has been added to your pricing groups.` : 
          undefined
        }
        type="general"
        action={{
          label: "View All Pricing Groups",
          onClick: handleViewPricingGroup
        }}
      />

      <SuccessModal
        isOpen={showSuccessModal}
        onClose={handleCloseSuccessAnimations}
        title="🎉 Pricing Group Created Successfully!"
        description="Your new pricing group has been saved and is now available for use."
        locationData={createdPricingGroup ? {
          address: createdPricingGroup.name || "",
          city: createdPricingGroup.description || "Pricing Group",
          country: `Discount: ${createdPricingGroup.discountPercentage || 0}% | Status: ${createdPricingGroup.isActive ? 'Active' : 'Inactive'}`
        } : undefined}
        actions={{
          primary: {
            label: "Add Another Pricing Group",
            onClick: handleAddAnother
          },
          secondary: {
            label: "View All Pricing Groups",
            onClick: handleViewPricingGroup
          }
        }}
      />
    </div>
  );
}
