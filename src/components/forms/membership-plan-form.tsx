"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { motion } from "framer-motion";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Loader2, CreditCard, CheckCircle } from "lucide-react";
import { MembershipPlan } from "@/lib/db/schema";

/**
 * Validation schema untuk membership plan form
 * 
 * Ini define rules untuk validasi form:
 * - name: wajib, max 255 karakter
 * - description: optional, max 255 karakter
 * - price: optional, non-negative number
 * - currency: optional, max 255 karakter
 * - duration_value: optional, positive number
 * - duration_unit: optional, max 50 karakter
 * - is_active: optional boolean
 */
const membershipPlanSchema = z.object({
  tenantId: z.number().int().positive(),
  name: z.string().min(1, "Name is required").max(255),
  description: z.string().max(255).optional(),
  price: z.number().int().min(0, "Price must be non-negative").optional(),
  currency: z.string().max(255).optional(),
  duration_value: z.number().int().positive("Duration must be positive").optional(),
  duration_unit: z.string().max(50).optional(),
  is_active: z.boolean().optional(),
});

type MembershipPlanFormData = z.infer<typeof membershipPlanSchema>;

interface MembershipPlanFormProps {
  membershipPlan?: MembershipPlan;
  tenantId: number;
  onSubmit: (data: MembershipPlanFormData) => Promise<void>;
  onCancel?: () => void;
  isLoading?: boolean;
  className?: string;
}

/**
 * MembershipPlanForm Component
 * 
 * Form untuk create/edit membership plan dengan:
 * - Basic plan info (name, description)
 * - Pricing settings (price, currency)
 * - Duration settings (value, unit)
 * - Active status
 * 
 * Pattern ini sama dengan ClassForm dan form components lainnya.
 */
export function MembershipPlanForm({ 
  membershipPlan, 
  tenantId, 
  onSubmit, 
  onCancel, 
  isLoading = false,
  className = "" 
}: MembershipPlanFormProps) {
  const [isSuccess, setIsSuccess] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  const form = useForm<MembershipPlanFormData>({
    resolver: zodResolver(membershipPlanSchema),
    defaultValues: {
      tenantId,
      name: membershipPlan?.name || "",
      description: membershipPlan?.description || undefined,
      price: membershipPlan?.price || undefined,
      currency: membershipPlan?.currency || undefined,
      duration_value: membershipPlan?.duration_value || undefined,
      duration_unit: membershipPlan?.duration_unit || undefined,
      is_active: membershipPlan?.is_active !== undefined ? membershipPlan.is_active : true,
    },
  });

  const handleSubmit = async (data: MembershipPlanFormData) => {
    try {
      setSubmitError(null);
      await onSubmit(data);
      setIsSuccess(true);
      setTimeout(() => setIsSuccess(false), 2000);
    } catch (error) {
      console.error("Form submission error:", error);
      
      // Handle specific validation errors
      let errorMessage = "An error occurred";
      if (error instanceof Error) {
        if (error.message.includes("Tenant") && error.message.includes("not found")) {
          errorMessage = "Invalid tenant. Please contact support.";
        } else {
          errorMessage = error.message;
        }
      }
      
      setSubmitError(errorMessage);
    }
  };

  const isFormLoading = isLoading || form.formState.isSubmitting;

  return (
    <Card className={`w-full max-w-2xl mx-auto ${className}`}>
      <CardHeader className="text-center">
        <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
          <CreditCard className="h-6 w-6 text-blue-600" />
        </div>
        <CardTitle className="text-xl font-semibold">
          {membershipPlan ? "Edit Membership Plan" : "Create Membership Plan"}
        </CardTitle>
        <CardDescription>
          {membershipPlan ? "Update the membership plan details" : "Add a new membership plan to your offerings"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          {/* Success Alert */}
          {isSuccess && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="mb-4"
            >
              <Alert className="border-green-200 bg-green-50">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <AlertDescription className="text-green-800">
                  Membership plan {membershipPlan ? "updated" : "created"} successfully!
                </AlertDescription>
              </Alert>
            </motion.div>
          )}

          {/* Error Alert */}
          {submitError && (
            <Alert variant="destructive" className="mb-4">
              <AlertDescription>{submitError}</AlertDescription>
            </Alert>
          )}

          {/* Two Column Layout */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Left Column */}
            <div className="space-y-4">
              {/* Plan Name */}
              <div className="space-y-2">
                <Label htmlFor="name">Plan Name</Label>
                <Input
                  id="name"
                  placeholder="Enter plan name"
                  {...form.register("name")}
                  className={form.formState.errors.name ? "border-red-500" : ""}
                  disabled={isFormLoading}
                />
                {form.formState.errors.name && (
                  <p className="text-sm text-red-500">
                    {form.formState.errors.name.message}
                  </p>
                )}
              </div>

              {/* Price */}
              <div className="space-y-2">
                <Label htmlFor="price">Price</Label>
                <Input
                  id="price"
                  type="number"
                  placeholder="0"
                  {...form.register("price", { valueAsNumber: true })}
                  className={form.formState.errors.price ? "border-red-500" : ""}
                  disabled={isFormLoading}
                />
                {form.formState.errors.price && (
                  <p className="text-sm text-red-500">
                    {form.formState.errors.price.message}
                  </p>
                )}
              </div>

              {/* Currency */}
              <div className="space-y-2">
                <Label htmlFor="currency">Currency</Label>
                <Select
                  value={form.watch("currency") || "not_set"}
                  onValueChange={(value) => form.setValue("currency", value === "not_set" ? undefined : value)}
                  disabled={isFormLoading}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select currency" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="not_set">Not set</SelectItem>
                    <SelectItem value="USD">USD</SelectItem>
                    <SelectItem value="EUR">EUR</SelectItem>
                    <SelectItem value="IDR">IDR</SelectItem>
                    <SelectItem value="SGD">SGD</SelectItem>
                    <SelectItem value="MYR">MYR</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Duration Value */}
              <div className="space-y-2">
                <Label htmlFor="duration_value">Duration Value</Label>
                <Input
                  id="duration_value"
                  type="number"
                  placeholder="1"
                  {...form.register("duration_value", { valueAsNumber: true })}
                  className={form.formState.errors.duration_value ? "border-red-500" : ""}
                  disabled={isFormLoading}
                />
                {form.formState.errors.duration_value && (
                  <p className="text-sm text-red-500">
                    {form.formState.errors.duration_value.message}
                  </p>
                )}
              </div>
            </div>

            {/* Right Column */}
            <div className="space-y-4">
              {/* Description */}
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  placeholder="Enter plan description"
                  rows={4}
                  {...form.register("description")}
                  className={form.formState.errors.description ? "border-red-500" : ""}
                  disabled={isFormLoading}
                />
                {form.formState.errors.description && (
                  <p className="text-sm text-red-500">
                    {form.formState.errors.description.message}
                  </p>
                )}
              </div>

              {/* Duration Unit */}
              <div className="space-y-2">
                <Label htmlFor="duration_unit">Duration Unit</Label>
                <Select
                  value={form.watch("duration_unit") || "not_set"}
                  onValueChange={(value) => form.setValue("duration_unit", value === "not_set" ? undefined : value)}
                  disabled={isFormLoading}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select duration unit" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="not_set">Not set</SelectItem>
                    <SelectItem value="days">Days</SelectItem>
                    <SelectItem value="weeks">Weeks</SelectItem>
                    <SelectItem value="months">Months</SelectItem>
                    <SelectItem value="years">Years</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Active Status */}
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="is_active"
                    checked={form.watch("is_active")}
                    onCheckedChange={(checked) => form.setValue("is_active", !!checked)}
                    disabled={isFormLoading}
                  />
                  <Label htmlFor="is_active">Active Plan</Label>
                </div>
              </div>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex gap-3 pt-4">
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isFormLoading}
                className="flex-1"
              >
                Cancel
              </Button>
            )}
            <Button
              type="submit"
              disabled={isFormLoading}
              className="flex-1"
            >
              {isFormLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {membershipPlan ? "Update" : "Create"} Plan
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
