"use client";

import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Package, Edit, Trash2, Eye, EyeOff, Calendar, Clock, MoreHorizontal } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { Package as PackageType } from "@/lib/db/schema";
import { useDeletePackage } from "@/lib/hooks/queries/use-package-queries";
import { toast } from "sonner";

interface PackageListProps {
  packages: PackageType[];
  onEdit: (pkg: PackageType) => void;
  onDuplicate?: (pkg: PackageType) => void;
  className?: string;
}

export function PackageList({ packages, onEdit, onDuplicate, className = "" }: PackageListProps) {
  const [deleteConfirmation, setDeleteConfirmation] = useState<{
    isOpen: boolean;
    package: PackageType | null;
  }>({ isOpen: false, package: null });

  const [deleteError, setDeleteError] = useState<string | null>(null);

  const deletePackageMutation = useDeletePackage();

  const handleDeleteClick = (pkg: PackageType) => {
    setDeleteConfirmation({ isOpen: true, package: pkg });
    setDeleteError(null);
  };

  const handleDeleteConfirm = async () => {
    if (!deleteConfirmation.package) return;

    try {
      const packageName = deleteConfirmation.package.name;
      await deletePackageMutation.mutateAsync(deleteConfirmation.package.id);
      setDeleteConfirmation({ isOpen: false, package: null });
      toast.success(`Package "${packageName}" deleted successfully!`);
    } catch (error) {
      setDeleteError(error instanceof Error ? error.message : "Failed to delete package");
    }
  };

  const formatValidityDuration = (duration: number | null) => {
    if (!duration) return "Unlimited";
    if (duration === 1) return "1 day";
    return `${duration} days`;
  };

  const formatValidityDate = (date: string | null) => {
    if (!date) return null;
    return new Date(date).toLocaleDateString();
  };

  if (packages.length === 0) {
    return (
      <div className="text-center py-12">
        <Package className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-gray-100">No packages</h3>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Get started by creating a new package.
        </p>
      </div>
    );
  }

  return (
    <>
      <div className={`grid gap-4 md:grid-cols-2 lg:grid-cols-3 ${className}`}>
        <AnimatePresence>
          {packages.map((pkg) => (
            <motion.div
              key={pkg.id}
              layout
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              transition={{ duration: 0.2 }}
            >
              <Card className="h-full hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-2">
                      <Package className="h-5 w-5 text-blue-500" />
                      <h3 className="font-semibold text-gray-900 dark:text-gray-100 line-clamp-1">
                        {pkg.name}
                      </h3>
                    </div>
                    
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => onEdit(pkg)}>
                          <Edit className="mr-2 h-4 w-4" />
                          Edit
                        </DropdownMenuItem>
                        {onDuplicate && (
                          <DropdownMenuItem onClick={() => onDuplicate(pkg)}>
                            <Package className="mr-2 h-4 w-4" />
                            Duplicate
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuItem 
                          onClick={() => handleDeleteClick(pkg)}
                          className="text-destructive"
                        >
                          <Trash2 className="mr-2 h-4 w-4" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>

                  {/* Package Description */}
                  {pkg.description && (
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
                      {pkg.description}
                    </p>
                  )}

                  {/* Package Status Badges */}
                  <div className="flex flex-wrap gap-2 mb-3">
                    <Badge variant={pkg.isActive ? "default" : "secondary"}>
                      {pkg.isActive ? (
                        <>
                          <Eye className="mr-1 h-3 w-3" />
                          Active
                        </>
                      ) : (
                        <>
                          <EyeOff className="mr-1 h-3 w-3" />
                          Inactive
                        </>
                      )}
                    </Badge>
                    
                    {pkg.is_private && (
                      <Badge variant="outline">
                        <EyeOff className="mr-1 h-3 w-3" />
                        Private
                      </Badge>
                    )}
                  </div>

                  {/* Validity Information */}
                  <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                    {pkg.validity_duration && (
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4" />
                        <span>Valid for {formatValidityDuration(pkg.validity_duration)}</span>
                      </div>
                    )}
                    
                    {pkg.validity_date && (
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        <span>Valid until {formatValidityDate(pkg.validity_date)}</span>
                      </div>
                    )}
                  </div>

                  {/* Package Metadata */}
                  <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-700">
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      Created {new Date(pkg.createdAt).toLocaleDateString()}
                      {pkg.updatedAt !== pkg.createdAt && (
                        <span className="ml-2">
                          • Updated {new Date(pkg.updatedAt).toLocaleDateString()}
                        </span>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {/* Delete Confirmation Dialog */}
      <AlertDialog 
        open={deleteConfirmation.isOpen} 
        onOpenChange={(open) => {
          if (!open) {
            setDeleteConfirmation({ isOpen: false, package: null });
            setDeleteError(null);
          }
        }}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Package</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{deleteConfirmation.package?.name}"? This action cannot be undone.
              {deleteError && (
                <div className="mt-2 text-sm text-destructive">
                  Error: {deleteError}
                </div>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              disabled={deletePackageMutation.isPending}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {deletePackageMutation.isPending ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>


    </>
  );
}
