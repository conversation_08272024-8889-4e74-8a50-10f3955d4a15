import { z } from "zod";
import { createId } from "@paralleldrive/cuid2";
import { customerOAuthChallenges, customerOAuthAccounts, customers, db } from "@/lib/db";
import { eq, and, lt } from "drizzle-orm";
import { createHash, randomBytes } from "crypto";

/**
 * Google OAuth Service for Customer Authentication
 * 
 * Implements Google OAuth 2.0 flow with PKCE for mobile security
 * FAANG-level implementation with proper error handling and security
 */

// Environment validation
const googleOAuthConfig = z.object({
  clientId: z.string().min(1, "GOOGLE_CLIENT_ID is required"),
  clientSecret: z.string().min(1, "GOOGLE_CLIENT_SECRET is required"),
  redirectUri: z.string().url("Invalid GOOGLE_REDIRECT_URI"),
}).parse({
  clientId: process.env.GOOGLE_CLIENT_ID,
  clientSecret: process.env.GOOGLE_CLIENT_SECRET,
  redirectUri: process.env.GOOGLE_REDIRECT_URI || "http://localhost:3000/api/auth/customer/google/callback",
});

// Validation schemas
export const pkceInitSchema = z.object({
  tenantId: z.number().int().positive(),
  clientType: z.enum(["web", "mobile"]),
  redirectUri: z.string().url(),
  deviceId: z.string().optional(),
  userAgent: z.string().optional(),
  ipAddress: z.string().ip(),
});

export const oauthCallbackSchema = z.object({
  code: z.string().min(1),
  state: z.string().min(1),
  codeVerifier: z.string().min(43).max(128).optional(), // Required for mobile PKCE
  tenantId: z.number().int().positive(),
  ipAddress: z.string().ip(),
  userAgent: z.string().optional(),
});

export interface GoogleUserProfile {
  id: string;
  email: string;
  verified_email: boolean;
  name: string;
  given_name: string;
  family_name: string;
  picture: string;
  locale: string;
}

export interface PKCEChallenge {
  codeChallenge: string;
  codeChallengeMethod: "S256";
  state: string;
  redirectUri: string;
  expiresAt: Date;
}

export interface OAuthResult {
  success: boolean;
  customer?: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    displayName: string;
    image?: string | null;
    tenantId: number;
    isEmailVerified: boolean;
    isNewCustomer: boolean;
  };
  error?: string;
  errorCode?: string;
}

class GoogleOAuthService {
  private readonly baseUrl = "https://accounts.google.com/o/oauth2/v2";
  private readonly tokenUrl = "https://oauth2.googleapis.com/token";
  private readonly userInfoUrl = "https://www.googleapis.com/oauth2/v2/userinfo";

  /**
   * Generate PKCE challenge for secure OAuth flow (especially mobile)
   */
  generatePKCEChallenge(): { codeVerifier: string; codeChallenge: string; codeChallengeMethod: "S256" } {
    const codeVerifier = randomBytes(32).toString("base64url");
    const codeChallenge = createHash("sha256").update(codeVerifier).digest("base64url");
    
    return {
      codeVerifier,
      codeChallenge,
      codeChallengeMethod: "S256",
    };
  }

  /**
   * Initialize OAuth flow with PKCE support
   */
  async initializeOAuthFlow(params: z.infer<typeof pkceInitSchema>): Promise<{
    authUrl: string;
    state: string;
    codeChallenge?: string;
    codeVerifier?: string;
  }> {
    const validated = pkceInitSchema.parse(params);
    
    // Generate state and PKCE challenge
    const state = createId();
    const pkce = this.generatePKCEChallenge();
    
    // Store PKCE challenge in database for mobile clients
    if (validated.clientType === "mobile") {
      await this.storePKCEChallenge({
        state,
        codeChallenge: pkce.codeChallenge,
        codeChallengeMethod: pkce.codeChallengeMethod,
        redirectUri: validated.redirectUri,
        tenantId: validated.tenantId,
        clientType: validated.clientType,
        ipAddress: validated.ipAddress,
        userAgent: validated.userAgent,
        deviceId: validated.deviceId,
      });
    }

    // Build OAuth URL
    const authUrl = new URL(`${this.baseUrl}/auth`);
    authUrl.searchParams.set("client_id", googleOAuthConfig.clientId);
    authUrl.searchParams.set("redirect_uri", validated.redirectUri);
    authUrl.searchParams.set("response_type", "code");
    authUrl.searchParams.set("scope", "openid email profile");
    authUrl.searchParams.set("state", state);
    authUrl.searchParams.set("access_type", "offline");
    authUrl.searchParams.set("prompt", "consent");

    // Add PKCE parameters for mobile
    if (validated.clientType === "mobile") {
      authUrl.searchParams.set("code_challenge", pkce.codeChallenge);
      authUrl.searchParams.set("code_challenge_method", pkce.codeChallengeMethod);
    }

    return {
      authUrl: authUrl.toString(),
      state,
      ...(validated.clientType === "mobile" && {
        codeChallenge: pkce.codeChallenge,
        codeVerifier: pkce.codeVerifier,
      }),
    };
  }

  /**
   * Store PKCE challenge for mobile OAuth flow
   */
  private async storePKCEChallenge(params: {
    state: string;
    codeChallenge: string;
    codeChallengeMethod: string;
    redirectUri: string;
    tenantId: number;
    clientType: string;
    ipAddress: string;
    userAgent?: string;
    deviceId?: string;
  }): Promise<void> {
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    await db.insert(customerOAuthChallenges).values({
      id: createId(),
      tenantId: params.tenantId,
      codeChallenge: params.codeChallenge,
      codeChallengeMethod: params.codeChallengeMethod,
      state: params.state,
      redirectUri: params.redirectUri,
      clientType: params.clientType,
      ipAddress: params.ipAddress,
      userAgent: params.userAgent,
      deviceId: params.deviceId,
      expiresAt,
    });
  }

  /**
   * Handle OAuth callback and exchange code for tokens
   */
  async handleCallback(params: z.infer<typeof oauthCallbackSchema>): Promise<OAuthResult> {
    try {
      const validated = oauthCallbackSchema.parse(params);
      
      // Verify state and get PKCE challenge for mobile
      let codeVerifier: string | undefined;
      if (validated.codeVerifier) {
        const challenge = await this.validateAndConsumePKCEChallenge(
          validated.state,
          validated.tenantId
        );
        if (!challenge) {
          return {
            success: false,
            error: "Invalid or expired OAuth state",
            errorCode: "INVALID_STATE",
          };
        }
        codeVerifier = validated.codeVerifier;
      }

      // Exchange authorization code for tokens
      const tokens = await this.exchangeCodeForTokens({
        code: validated.code,
        redirectUri: validated.codeVerifier ? 
          (await this.getPKCERedirectUri(validated.state)) : 
          googleOAuthConfig.redirectUri,
        codeVerifier,
      });

      // Get user profile from Google
      const profile = await this.getUserProfile(tokens.access_token);

      // Create or update customer
      const result = await this.createOrUpdateCustomer({
        profile,
        tokens,
        tenantId: validated.tenantId,
        ipAddress: validated.ipAddress,
        userAgent: validated.userAgent,
      });

      return result;

    } catch (error) {
      console.error("OAuth callback error:", error);
      return {
        success: false,
        error: "OAuth authentication failed",
        errorCode: "OAUTH_ERROR",
      };
    }
  }

  /**
   * Validate and consume PKCE challenge
   */
  private async validateAndConsumePKCEChallenge(
    state: string,
    tenantId: number
  ): Promise<boolean> {
    const [challenge] = await db
      .select()
      .from(customerOAuthChallenges)
      .where(and(
        eq(customerOAuthChallenges.state, state),
        eq(customerOAuthChallenges.tenantId, tenantId),
        eq(customerOAuthChallenges.isUsed, false),
        lt(customerOAuthChallenges.expiresAt, new Date())
      ))
      .limit(1);

    if (!challenge) {
      return false;
    }

    // Mark as used
    await db
      .update(customerOAuthChallenges)
      .set({
        isUsed: true,
        usedAt: new Date(),
      })
      .where(eq(customerOAuthChallenges.id, challenge.id));

    return true;
  }

  /**
   * Get redirect URI from PKCE challenge
   */
  private async getPKCERedirectUri(state: string): Promise<string> {
    const [challenge] = await db
      .select({ redirectUri: customerOAuthChallenges.redirectUri })
      .from(customerOAuthChallenges)
      .where(eq(customerOAuthChallenges.state, state))
      .limit(1);

    return challenge?.redirectUri || googleOAuthConfig.redirectUri;
  }

  /**
   * Exchange authorization code for access tokens
   */
  private async exchangeCodeForTokens(params: {
    code: string;
    redirectUri: string;
    codeVerifier?: string;
  }): Promise<{
    access_token: string;
    refresh_token?: string;
    id_token?: string;
    expires_in: number;
  }> {
    const body = new URLSearchParams({
      client_id: googleOAuthConfig.clientId,
      client_secret: googleOAuthConfig.clientSecret,
      code: params.code,
      grant_type: "authorization_code",
      redirect_uri: params.redirectUri,
    });

    // Add PKCE code verifier for mobile
    if (params.codeVerifier) {
      body.set("code_verifier", params.codeVerifier);
    }

    const response = await fetch(this.tokenUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        "Accept": "application/json",
      },
      body,
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`Token exchange failed: ${error}`);
    }

    return response.json();
  }

  /**
   * Get user profile from Google
   */
  private async getUserProfile(accessToken: string): Promise<GoogleUserProfile> {
    const response = await fetch(this.userInfoUrl, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
        Accept: "application/json",
      },
    });

    if (!response.ok) {
      throw new Error("Failed to fetch user profile");
    }

    return response.json();
  }

  /**
   * Create or update customer from OAuth profile
   */
  private async createOrUpdateCustomer(params: {
    profile: GoogleUserProfile;
    tokens: any;
    tenantId: number;
    ipAddress: string;
    userAgent?: string;
  }): Promise<OAuthResult> {
    const { profile, tokens, tenantId, ipAddress, userAgent } = params;

    try {
      // Check if customer already exists by email
      const [existingCustomer] = await db
        .select()
        .from(customers)
        .where(and(
          eq(customers.email, profile.email),
          eq(customers.tenantId, tenantId)
        ))
        .limit(1);

      let customer;
      let isNewCustomer = false;

      if (existingCustomer) {
        // Update existing customer with Google ID if not set
        if (!existingCustomer.googleId) {
          await db
            .update(customers)
            .set({
              googleId: profile.id,
              emailVerified: profile.verified_email ? new Date() : null,
              isEmailVerified: profile.verified_email,
              image: profile.picture,
              lastLoginAt: new Date(),
              lastLoginIP: ipAddress,
              failedLoginAttempts: 0, // Reset failed attempts on successful OAuth
              lockedAt: null,
              lockedUntil: null,
              updatedAt: new Date(),
            })
            .where(eq(customers.id, existingCustomer.id));
        } else {
          // Just update login info
          await db
            .update(customers)
            .set({
              lastLoginAt: new Date(),
              lastLoginIP: ipAddress,
              failedLoginAttempts: 0,
              lockedAt: null,
              lockedUntil: null,
              updatedAt: new Date(),
            })
            .where(eq(customers.id, existingCustomer.id));
        }

        customer = { ...existingCustomer, googleId: profile.id };
      } else {
        // Create new customer
        const newCustomerId = createId();
        const displayName = profile.name || `${profile.given_name} ${profile.family_name}`.trim();

        await db.insert(customers).values({
          id: newCustomerId,
          tenantId,
          email: profile.email,
          firstName: profile.given_name || profile.name.split(' ')[0] || 'User',
          lastName: profile.family_name || profile.name.split(' ').slice(1).join(' ') || '',
          displayName,
          googleId: profile.id,
          emailVerified: profile.verified_email ? new Date() : null,
          isEmailVerified: profile.verified_email,
          image: profile.picture,
          isActive: true,
          membershipType: "basic",
          lastLoginAt: new Date(),
          lastLoginIP: ipAddress,
          preferences: {
            language: profile.locale || "en",
            notifications: {
              email: true,
              sms: false,
              push: true,
            },
          },
          termsAcceptedAt: new Date(), // Auto-accept for OAuth users
          termsVersion: "1.0",
          privacyAcceptedAt: new Date(),
          privacyVersion: "1.0",
        });

        customer = {
          id: newCustomerId,
          tenantId,
          email: profile.email,
          firstName: profile.given_name || 'User',
          lastName: profile.family_name || '',
          displayName,
          googleId: profile.id,
          emailVerified: new Date(),
          isEmailVerified: true,
          image: profile.picture,
          isActive: true,
          membershipType: "basic",
        };

        isNewCustomer = true;
      }

      // Create or update OAuth account record
      await this.upsertOAuthAccount({
        customerId: customer.id,
        tenantId,
        profile,
        tokens,
      });

      return {
        success: true,
        customer: {
          id: customer.id,
          email: customer.email,
          firstName: customer.firstName,
          lastName: customer.lastName,
          displayName: customer.displayName,
          image: customer.image,
          tenantId: customer.tenantId,
          isEmailVerified: customer.isEmailVerified,
          isNewCustomer,
        },
      };

    } catch (error) {
      console.error("Error creating/updating customer:", error);
      return {
        success: false,
        error: "Failed to create customer account",
        errorCode: "CUSTOMER_CREATION_ERROR",
      };
    }
  }

  /**
   * Create or update OAuth account record
   */
  private async upsertOAuthAccount(params: {
    customerId: string;
    tenantId: number;
    profile: GoogleUserProfile;
    tokens: any;
  }): Promise<void> {
    const { customerId, tenantId, profile, tokens } = params;

    // Check if OAuth account already exists
    const [existingAccount] = await db
      .select()
      .from(customerOAuthAccounts)
      .where(and(
        eq(customerOAuthAccounts.customerId, customerId),
        eq(customerOAuthAccounts.provider, "google"),
        eq(customerOAuthAccounts.providerAccountId, profile.id)
      ))
      .limit(1);

    const accountData = {
      customerId,
      tenantId,
      provider: "google",
      providerAccountId: profile.id,
      type: "oauth",
      accessToken: tokens.access_token,
      refreshToken: tokens.refresh_token,
      idToken: tokens.id_token,
      expiresAt: tokens.expires_in ? Math.floor(Date.now() / 1000) + tokens.expires_in : null,
      tokenType: "Bearer",
      scope: "openid email profile",
      providerProfile: {
        name: profile.name,
        email: profile.email,
        picture: profile.picture,
        locale: profile.locale,
        emailVerified: profile.verified_email,
      },
      isActive: true,
      lastUsed: new Date(),
      updatedAt: new Date(),
    };

    if (existingAccount) {
      // Update existing account
      await db
        .update(customerOAuthAccounts)
        .set(accountData)
        .where(eq(customerOAuthAccounts.id, existingAccount.id));
    } else {
      // Create new account
      await db.insert(customerOAuthAccounts).values({
        id: createId(),
        ...accountData,
      });
    }
  }

  /**
   * Clean up expired PKCE challenges (should be run periodically)
   */
  async cleanupExpiredChallenges(): Promise<number> {
    const result = await db
      .delete(customerOAuthChallenges)
      .where(lt(customerOAuthChallenges.expiresAt, new Date()));

    return result.rowCount || 0;
  }
}

export const googleOAuthService = new GoogleOAuthService();
