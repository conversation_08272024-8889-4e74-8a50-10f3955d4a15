import { NextRequest, NextResponse } from "next/server";

/**
 * CORS Configuration for Customer Authentication API
 * 
 * FAANG-level CORS implementation with security best practices
 */

export interface CORSOptions {
  origin?: string | string[] | ((origin: string) => boolean);
  methods?: string[];
  allowedHeaders?: string[];
  exposedHeaders?: string[];
  credentials?: boolean;
  maxAge?: number;
  preflightContinue?: boolean;
  optionsSuccessStatus?: number;
}

/**
 * Default CORS configuration for customer API
 */
const defaultCORSConfig: CORSOptions = {
  origin: (origin: string) => {
    // Allow requests from configured frontend URLs
    const allowedOrigins = [
      process.env.FRONTEND_URL,
      process.env.ADMIN_URL,
      ...(process.env.NODE_ENV === 'development' ? [
        'http://localhost:3000',
        'http://localhost:3001',
        'http://localhost:3002',
        'http://localhost:3003',
        'https://localhost:3000',
        'https://localhost:3001',
        'https://localhost:3002',
        'https://localhost:3003'
      ] : [])
    ].filter(Boolean);

    // Allow requests with no origin (mobile apps, Postman, etc.)
    if (!origin) return true;
    
    // Check if origin is in allowed list
    return allowedOrigins.includes(origin);
  },
  methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
  allowedHeaders: [
    "Content-Type",
    "Authorization",
    "X-Tenant-ID",
    "X-Device-ID",
    "X-Client-Version",
    "X-Request-ID",
    "X-Forwarded-For",
    "User-Agent",
    "Accept",
    "Accept-Language",
    "Accept-Encoding",
    "X-API-Key",
    "x-api-key",
    "Cache-Control",
    "Pragma",
  ],
  exposedHeaders: [
    "X-Request-ID",
    "X-RateLimit-Limit",
    "X-RateLimit-Remaining",
    "X-RateLimit-Reset",
    "X-Total-Count",
    "X-Page-Count",
  ],
  credentials: true,
  maxAge: 86400, // 24 hours
  optionsSuccessStatus: 200,
};

/**
 * Validate origin against allowed origins
 */
function isOriginAllowed(origin: string | undefined, allowedOrigin: CORSOptions["origin"]): boolean {
  if (!allowedOrigin) return false;

  if (typeof allowedOrigin === "string") {
    return origin === allowedOrigin;
  }

  if (Array.isArray(allowedOrigin)) {
    return origin ? allowedOrigin.includes(origin) : false;
  }

  if (typeof allowedOrigin === "function") {
    return allowedOrigin(origin || "");
  }

  return false;
}

/**
 * Apply CORS headers to response
 */
/**
 * Apply CORS headers to response
 */
export function applyCORSHeaders(
  request: NextRequest,
  response: NextResponse,
  options: CORSOptions = defaultCORSConfig
): NextResponse {
  const origin = request.headers.get("origin");
  const requestMethod = request.headers.get("access-control-request-method");
  const requestHeaders = request.headers.get("access-control-request-headers");

  let allowOrigin = false;
  let useWildcard = false;

  // Check if origin is allowed
  if (origin && isOriginAllowed(origin, options.origin)) {
    response.headers.set("Access-Control-Allow-Origin", origin);
    allowOrigin = true;
  } else if (!origin) {
    // For requests with no origin, only use wildcard if credentials is false
    if (!options.credentials) {
      response.headers.set("Access-Control-Allow-Origin", "*");
      useWildcard = true;
    } else {
      // If credentials is true, we need a specific origin
      // Use the actual origin if it's in allowed list, otherwise use default
      const allowedOrigins = [
        process.env.FRONTEND_URL,
        process.env.ADMIN_URL,
        ...(process.env.NODE_ENV === 'development' ? [
          'http://localhost:3000',
          'http://localhost:3001',
          'http://localhost:3002',
          'http://localhost:3003',
          'https://localhost:3000',
          'https://localhost:3001',
          'https://localhost:3002',
          'https://localhost:3003'
        ] : [])
      ].filter(Boolean);

      const originToUse = origin && allowedOrigins.includes(origin)
        ? origin
        : (process.env.NODE_ENV === 'development'
          ? "http://localhost:3001"  // Default to 3001 for development
          : process.env.FRONTEND_URL || "http://localhost:3001");

      response.headers.set("Access-Control-Allow-Origin", originToUse);
      allowOrigin = true;
    }
  } else {
    // Origin not allowed - don't set CORS headers
    console.warn(`🚫 Origin not allowed: ${origin}`);
    return response;
  }

  // Set credentials header only if we're not using wildcard
  if (options.credentials && !useWildcard && allowOrigin) {
    response.headers.set("Access-Control-Allow-Credentials", "true");
  }

  // Set allowed methods
  if (options.methods && options.methods.length > 0) {
    response.headers.set("Access-Control-Allow-Methods", options.methods.join(", "));
  }

  // Set allowed headers
  if (request.method === "OPTIONS") {
    // On preflight, always set comprehensive allowed headers
    const baseAllowedHeaders = options.allowedHeaders || [];
    const essentialHeaders = [
      "Authorization",
      "X-API-Key",
      "x-api-key",
      "Content-Type",
      "Accept",
      "Origin",
      "X-Requested-With",
      "Cache-Control",
      "cache-control",
      "Pragma"
    ];
    
    // Combine and deduplicate headers (case-insensitive)
    const allHeaders = [...baseAllowedHeaders];
    essentialHeaders.forEach(header => {
      if (!allHeaders.some(h => h.toLowerCase() === header.toLowerCase())) {
        allHeaders.push(header);
      }
    });
    
    // If client requested specific headers, include those too
    if (requestHeaders) {
      const requestedHeaders = requestHeaders.split(',').map(h => h.trim());
      requestedHeaders.forEach(header => {
        if (!allHeaders.some(h => h.toLowerCase() === header.toLowerCase())) {
          allHeaders.push(header);
        }
      });
    }
    
    response.headers.set("Access-Control-Allow-Headers", allHeaders.join(", "));
    
    console.log('🔍 CORS Allowed Headers set:', allHeaders.join(", "));
  } else if (options.allowedHeaders && options.allowedHeaders.length > 0) {
    // For non-preflight, set from config
    let allowedHeaders = [...options.allowedHeaders];
    const essentialHeaders = ["Authorization", "X-API-Key", "x-api-key"];
    
    essentialHeaders.forEach(header => {
      if (!allowedHeaders.some(h => h.toLowerCase() === header.toLowerCase())) {
        allowedHeaders.push(header);
      }
    });
    
    response.headers.set("Access-Control-Allow-Headers", allowedHeaders.join(", "));
  }

  // Set exposed headers
  if (options.exposedHeaders && options.exposedHeaders.length > 0) {
    response.headers.set("Access-Control-Expose-Headers", options.exposedHeaders.join(", "));
  }

  // Set max age for preflight cache
  if (options.maxAge) {
    response.headers.set("Access-Control-Max-Age", options.maxAge.toString());
  }

  // Handle preflight request
  if (request.method === "OPTIONS") {
    if (requestMethod) {
      response.headers.set("Access-Control-Allow-Methods", requestMethod);
    }
    // Allow-Headers sudah di-set di atas
  }

  return response;
}

/**
 * Create CORS preflight response
 */
export function createCORSPreflightResponse(
  request: NextRequest,
  options: CORSOptions = defaultCORSConfig
): NextResponse {
  const response = new NextResponse(null, {
    status: options.optionsSuccessStatus || 200,
  });

  return applyCORSHeaders(request, response, options);
}

/**
 * CORS middleware for API routes
 */
export function withCORS(
  handler: (request: NextRequest) => Promise<NextResponse> | NextResponse,
  options: CORSOptions = defaultCORSConfig
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    // Handle preflight OPTIONS request
    if (request.method === "OPTIONS") {
      return createCORSPreflightResponse(request, options);
    }

    // Process the actual request
    const response = await handler(request);

    // Apply CORS headers to the response
    return applyCORSHeaders(request, response, options);
  };
}

/**
 * Security headers for API responses
 */
export function addSecurityHeaders(response: NextResponse): NextResponse {
  // Content Security Policy
  response.headers.set(
    "Content-Security-Policy",
    "default-src 'none'; frame-ancestors 'none'; base-uri 'self'; form-action 'self';"
  );

  // Prevent MIME type sniffing
  response.headers.set("X-Content-Type-Options", "nosniff");

  // Prevent clickjacking
  response.headers.set("X-Frame-Options", "DENY");

  // XSS protection
  response.headers.set("X-XSS-Protection", "1; mode=block");

  // Referrer policy
  response.headers.set("Referrer-Policy", "strict-origin-when-cross-origin");

  // Permissions policy
  response.headers.set(
    "Permissions-Policy",
    "camera=(), microphone=(), geolocation=(), payment=(), usb=(), magnetometer=(), gyroscope=(), accelerometer=()"
  );

  // HSTS (only in production with HTTPS)
  if (process.env.NODE_ENV === "production") {
    response.headers.set(
      "Strict-Transport-Security",
      "max-age=31536000; includeSubDomains; preload"
    );
  }

  // Prevent caching of sensitive responses
  response.headers.set("Cache-Control", "no-store, no-cache, must-revalidate, proxy-revalidate");
  response.headers.set("Pragma", "no-cache");
  response.headers.set("Expires", "0");

  return response;
}

/**
 * Environment-specific CORS configuration
 */
export const corsConfigs = {
  development: {
    ...defaultCORSConfig,
    origin: (origin: string) => {
      // Allow common development origins
      const devOrigins = [
        "http://localhost:3000",
        "http://localhost:3001", 
        "http://localhost:3002",
        "http://localhost:3003",
        "https://localhost:3000",
        "https://localhost:3001",
        "https://localhost:3002",
        "https://localhost:3003",
      ];
      
      // If no origin (mobile apps, etc.), allow it
      if (!origin) return true;
      
      // Check if origin is in allowed list
      return devOrigins.includes(origin);
    },
    credentials: true, // Keep credentials enabled
  },
  // ... rest of configs
};

/**
 * Get CORS configuration based on environment
 */
export function getCORSConfig(): CORSOptions {
  const env = process.env.NODE_ENV || "development";
  return corsConfigs[env as keyof typeof corsConfigs] || corsConfigs.development;
}

/**
 * Validate request origin for additional security
 */
export function validateRequestOrigin(request: NextRequest): {
  valid: boolean;
  reason?: string;
} {
  const origin = request.headers.get("origin");
  const referer = request.headers.get("referer");
  const host = request.headers.get("host");

  // Allow requests with no origin (mobile apps, server-to-server)
  if (!origin && !referer) {
    return { valid: true };
  }

  // Check if origin matches allowed origins
  const corsConfig = getCORSConfig();
  if (origin && !isOriginAllowed(origin, corsConfig.origin)) {
    return {
      valid: false,
      reason: `Origin ${origin} not allowed`,
    };
  }

  // Additional validation for referer if present
  if (referer) {
    try {
      const refererUrl = new URL(referer);
      const allowedOrigins = Array.isArray(corsConfig.origin) 
        ? corsConfig.origin 
        : typeof corsConfig.origin === "string" 
        ? [corsConfig.origin] 
        : [];

      const refererOrigin = `${refererUrl.protocol}//${refererUrl.host}`;
      if (allowedOrigins.length > 0 && !allowedOrigins.includes(refererOrigin)) {
        return {
          valid: false,
          reason: `Referer ${refererOrigin} not allowed`,
        };
      }
    } catch (error) {
      return {
        valid: false,
        reason: "Invalid referer URL",
      };
    }
  }

  return { valid: true };
}