// <PERSON>ript to check class schedules in database
import { db } from "../src/lib/db";
import { class_schedules, classes, locations } from "../src/lib/db/schema";
import { eq } from "drizzle-orm";

async function checkClassSchedules() {
  console.log('📅 Checking class schedules in database...');
  
  try {
    // Check total schedules
    const allSchedules = await db.select().from(class_schedules);
    console.log(`Found ${allSchedules.length} class schedules in database:`);
    
    if (allSchedules.length === 0) {
      console.log('❌ No class schedules found in database!');
      console.log('💡 This explains why calendar view is empty.');
      
      // Check if we have classes and locations to create schedules
      const allClasses = await db.select().from(classes);
      const allLocations = await db.select().from(locations);
      
      console.log(`📚 Found ${allClasses.length} classes`);
      console.log(`📍 Found ${allLocations.length} locations`);
      
      if (allClasses.length > 0 && allLocations.length > 0) {
        console.log('✅ You have classes and locations. You can create schedules!');
      }
      
      return;
    }
    
    // Show sample schedules
    console.log('\n📋 Sample schedules:');
    allSchedules.slice(0, 5).forEach((schedule, index) => {
      console.log(`${index + 1}. Schedule ID: ${schedule.id}`);
      console.log(`   Class ID: ${schedule.class_id}`);
      console.log(`   Tenant ID: ${schedule.tenant_id}`);
      console.log(`   Start Date: ${schedule.start_date}`);
      console.log(`   Start Time: ${schedule.start_time}`);
      console.log(`   End Date: ${schedule.end_date}`);
      console.log(`   End Time: ${schedule.end_time}`);
      console.log(`   Duration: ${schedule.duration} minutes`);
      console.log(`   Location ID: ${schedule.location_id}`);
      console.log(`   ---`);
    });
    
    // Check schedules by tenant
    const tenant1Schedules = await db
      .select()
      .from(class_schedules)
      .where(eq(class_schedules.tenant_id, 1));
      
    console.log(`\n🏢 Tenant 1 has ${tenant1Schedules.length} schedules`);
    
    // Check data quality
    const schedulesWithoutStartDate = allSchedules.filter(s => !s.start_date);
    const schedulesWithoutStartTime = allSchedules.filter(s => !s.start_time);
    
    console.log(`\n🔍 Data Quality Check:`);
    console.log(`   Schedules without start_date: ${schedulesWithoutStartDate.length}`);
    console.log(`   Schedules without start_time: ${schedulesWithoutStartTime.length}`);
    
    if (schedulesWithoutStartDate.length > 0) {
      console.log('⚠️  Some schedules missing start_date - this might cause calendar issues');
    }
    
    if (schedulesWithoutStartTime.length > 0) {
      console.log('⚠️  Some schedules missing start_time - this might cause calendar issues');
    }
    
  } catch (error) {
    console.error('❌ Error checking class schedules:', error);
  }
}

checkClassSchedules().catch(console.error);
