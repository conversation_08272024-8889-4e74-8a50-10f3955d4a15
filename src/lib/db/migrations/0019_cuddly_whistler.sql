CREATE TABLE "class_package_pricing" (
	"class_id" varchar(255) NOT NULL,
	"package_pricing_id" varchar(255) NOT NULL,
	"created_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updated_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT "class_package_pricing_class_id_package_pricing_id_pk" PRIMARY KEY("class_id","package_pricing_id")
);
--> statement-breakpoint
DROP TABLE "class_package_plans" CASCADE;--> statement-breakpoint
ALTER TABLE "class_package_pricing" ADD CONSTRAINT "class_package_pricing_class_id_classes_id_fk" FOREIGN KEY ("class_id") REFERENCES "public"."classes"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "class_package_pricing" ADD CONSTRAINT "class_package_pricing_package_pricing_id_package_pricing_id_fk" FOREIGN KEY ("package_pricing_id") REFERENCES "public"."package_pricing"("id") ON DELETE cascade ON UPDATE no action;