import { NextRequest } from "next/server";
import { z } from "zod";
import { googleOAuthService } from "@/lib/services/google-oauth.service";
import { 
  createAuthenticatedResponse, 
  createErrorResponse,
  rateLimit,
  addSecurityHeaders 
} from "@/lib/middleware/customer-auth.middleware";
import { rateLimitConfig } from "@/lib/security/rate-limit.config";

/**
 * Initialize Google OAuth Flow for Customer Authentication
 * 
 * POST /api/auth/customer/google/init
 * 
 * Supports both web and mobile clients with PKCE
 */

const initOAuthSchema = z.object({
  tenantId: z.number().int().positive(),
  clientType: z.enum(["web", "mobile"]).default("web"),
  redirectUri: z.string().url().optional(),
  deviceId: z.string().optional(),
});

export async function POST(request: NextRequest) {
  try {
    // Apply rate limiting
    const clientIP = request.headers.get("x-forwarded-for")?.split(",")[0] || 
                    request.headers.get("x-real-ip") || 
                    request.ip || "unknown";

    const rateLimitResult = rateLimit({
      windowMs: rateLimitConfig.auth.oauth.windowMs,
      max: rateLimitConfig.auth.oauth.max,
      keyGenerator: () => `oauth-init:${clientIP}`,
    })(request);

    if (!rateLimitResult.allowed) {
      return createErrorResponse(
        "Too many OAuth initialization attempts",
        "RATE_LIMIT_EXCEEDED",
        429,
        rateLimitResult.headers
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = initOAuthSchema.parse(body);

    // Get user agent and device info
    const userAgent = request.headers.get("user-agent") || undefined;
    
    // Set default redirect URI based on client type
    const redirectUri = validatedData.redirectUri || 
      (validatedData.clientType === "mobile" 
        ? `${process.env.MOBILE_DEEP_LINK_SCHEME}://oauth/callback`
        : `${process.env.NEXTAUTH_URL}/api/auth/customer/google/callback`
      );

    // Initialize OAuth flow
    const oauthFlow = await googleOAuthService.initializeOAuthFlow({
      tenantId: validatedData.tenantId,
      clientType: validatedData.clientType,
      redirectUri,
      deviceId: validatedData.deviceId,
      userAgent,
      ipAddress: clientIP,
    });

    // Prepare response data
    const responseData = {
      success: true,
      authUrl: oauthFlow.authUrl,
      state: oauthFlow.state,
      clientType: validatedData.clientType,
      expiresIn: 600, // 10 minutes
      ...(validatedData.clientType === "mobile" && {
        codeChallenge: oauthFlow.codeChallenge,
        // Note: codeVerifier should be stored securely on mobile client
      }),
    };

    return createAuthenticatedResponse(responseData, 200, rateLimitResult.headers);

  } catch (error) {
    console.error("OAuth initialization error:", error);

    if (error instanceof z.ZodError) {
      return createErrorResponse(
        "Invalid request data",
        "INVALID_INPUT",
        400
      );
    }

    return createErrorResponse(
      "Failed to initialize OAuth flow",
      "OAUTH_INIT_ERROR",
      500
    );
  }
}

// Handle preflight OPTIONS request
export async function OPTIONS(request: NextRequest) {
  const response = new Response(null, { status: 200 });
  return addSecurityHeaders(response);
}
