"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import { Search, X, Shield, Users, Check } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { useRoleSearch } from "@/lib/hooks/queries/use-role-queries";
import { Role } from "@/lib/db/schema";

interface RoleSelectorProps {
  tenantId?: number | null;
  selectedRoles: Array<{
    id: string;
    name: string;
    display_name: string;
    assigned_at: string;
  }>;
  onSelectionChange: (roles: Array<{
    id: string;
    name: string;
    display_name: string;
    assigned_at: string;
  }>) => void;
  maxSelections?: number;
  placeholder?: string;
  includeSystemRoles?: boolean;
}

export function RoleSelector({
  tenantId,
  selectedRoles,
  onSelectionChange,
  maxSelections = 10,
  placeholder = "Search and select roles...",
  includeSystemRoles = true
}: RoleSelectorProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [isOpen, setIsOpen] = useState(false);

  // Fetch roles untuk tenant ini
  const { data: searchResult, isLoading, error } = useRoleSearch(
    tenantId,
    searchQuery || undefined,
    undefined, // isSystemRole - ambil semua
    50 // limit
  );

  // Extract roles array dari search result
  const roles = searchResult?.roles || [];

  // Filter roles yang belum dipilih dan sesuai kriteria
  const availableRoles = roles.filter((role: Role) => {
    // Skip jika sudah dipilih
    if (selectedRoles.some(selected => selected.id === role.id)) {
      return false;
    }
    
    // Filter system roles jika tidak diinginkan
    if (!includeSystemRoles && role.is_system_role) {
      return false;
    }
    
    return true;
  });

  // Handle role selection
  const handleSelectRole = (roleItem: Role) => {
    if (selectedRoles.length >= maxSelections) {
      return; // Sudah mencapai limit
    }

    const newRole = {
      id: roleItem.id,
      name: roleItem.name,
      display_name: roleItem.display_name,
      assigned_at: new Date().toISOString(),
    };

    onSelectionChange([...selectedRoles, newRole]);
    setSearchQuery(""); // Clear search setelah select
  };

  // Handle remove role
  const handleRemoveRole = (roleId: string) => {
    onSelectionChange(selectedRoles.filter(role => role.id !== roleId));
  };

  // Handle select all roles
  const handleSelectAll = () => {
    const remainingSlots = maxSelections - selectedRoles.length;
    const rolesToAdd = availableRoles.slice(0, remainingSlots).map((role: Role) => ({
      id: role.id,
      name: role.name,
      display_name: role.display_name,
      assigned_at: new Date().toISOString(),
    }));

    onSelectionChange([...selectedRoles, ...rolesToAdd]);
  };

  return (
    <div className="space-y-4">
      {/* Header dengan info */}
      <div className="flex items-center justify-between">
        <Label className="flex items-center gap-2">
          <Shield className="h-4 w-4" />
          Assigned Roles
          {selectedRoles.length > 0 && (
            <Badge variant="secondary" className="text-xs">
              {selectedRoles.length} selected
            </Badge>
          )}
        </Label>
        
        {availableRoles.length > 0 && selectedRoles.length < maxSelections && (
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={handleSelectAll}
            className="text-xs"
          >
            <Users className="h-3 w-3 mr-1" />
            Select All ({Math.min(availableRoles.length, maxSelections - selectedRoles.length)})
          </Button>
        )}
      </div>

      {/* Search input */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        <Input
          type="text"
          placeholder={placeholder}
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          onFocus={() => setIsOpen(true)}
          className="pl-10"
        />
      </div>

      {/* Selected roles */}
      {selectedRoles.length > 0 && (
        <div className="space-y-2">
          <Label className="text-sm text-gray-600">Selected Roles:</Label>
          <div className="flex flex-wrap gap-2">
            {selectedRoles.map((role) => (
              <motion.div
                key={role.id}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                className="flex items-center gap-1"
              >
                <Badge variant="default" className="flex items-center gap-1">
                  <Check className="h-3 w-3" />
                  {role.display_name}
                  <button
                    type="button"
                    onClick={() => handleRemoveRole(role.id)}
                    className="ml-1 hover:bg-red-500 hover:text-white rounded-full p-0.5 transition-colors"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {/* Available roles dropdown */}
      {isOpen && (searchQuery || availableRoles.length > 0) && (
        <Card className="border shadow-lg">
          <CardContent className="p-2 max-h-60 overflow-y-auto">
            {isLoading ? (
              <div className="flex items-center justify-center py-4">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                <span className="ml-2 text-sm text-gray-600">Loading roles...</span>
              </div>
            ) : availableRoles.length === 0 ? (
              <div className="text-center py-4 text-sm text-gray-500">
                {searchQuery ? "No roles found matching your search" : "No available roles"}
              </div>
            ) : (
              <div className="space-y-1">
                {availableRoles.map((role: Role) => (
                  <button
                    key={role.id}
                    type="button"
                    onClick={() => handleSelectRole(role)}
                    disabled={selectedRoles.length >= maxSelections}
                    className="w-full text-left p-2 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-medium text-sm">{role.display_name}</div>
                        <div className="text-xs text-gray-500">
                          {role.name} {role.is_system_role && "(System Role)"}
                        </div>
                        {role.description && (
                          <div className="text-xs text-gray-400 mt-1">{role.description}</div>
                        )}
                      </div>
                      <Shield className="h-4 w-4 text-gray-400" />
                    </div>
                  </button>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Click outside to close */}
      {isOpen && (
        <div
          className="fixed inset-0 z-10"
          onClick={() => setIsOpen(false)}
        />
      )}

      {/* Info text */}
      <p className="text-xs text-gray-500">
        Select roles to assign to this user. 
        {maxSelections && ` Maximum ${maxSelections} roles allowed.`}
        {selectedRoles.length >= maxSelections && (
          <span className="text-orange-600 font-medium"> Limit reached!</span>
        )}
      </p>
    </div>
  );
}
