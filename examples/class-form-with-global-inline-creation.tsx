/**
 * Example: Class Form dengan Global Inline Creation
 * 
 * Ini adalah contoh bagaimana menggunakan global inline creation system
 * untuk menggantikan implementasi individual di setiap form.
 */

"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { SelectWithInlineCreation, mapToSelectOptions } from "@/components/ui/select-with-inline-creation";
import { useClassCategoriesByTenant } from "@/lib/hooks/queries/use-class-category-queries";
import { useClassLevelsByTenant } from "@/lib/hooks/queries/use-class-level-queries";
import { useLocations } from "@/lib/hooks/queries/use-location-queries";

const classSchema = z.object({
  tenantId: z.number().int().positive(),
  name: z.string().min(1, "Name is required").max(255),
  description: z.string().max(255).optional(),
  categoryId: z.string().min(1, "Category is required"),
  level_id: z.string().optional(),
  location_id: z.string().optional(),
  is_active: z.boolean().optional(),
});

type ClassFormData = z.infer<typeof classSchema>;

interface ClassFormProps {
  tenantId: number;
  onSubmit: (data: ClassFormData) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}

export function ClassFormExample({ 
  tenantId, 
  onSubmit, 
  onCancel, 
  isLoading = false 
}: ClassFormProps) {
  const [submitError, setSubmitError] = useState<string | null>(null);

  // Data queries dengan refetch capabilities
  const { 
    data: categories = [], 
    refetch: refetchCategories 
  } = useClassCategoriesByTenant(tenantId);
  
  const { 
    data: classLevels = [], 
    refetch: refetchClassLevels 
  } = useClassLevelsByTenant(tenantId, true);
  
  const { 
    data: locations = [], 
    refetch: refetchLocations 
  } = useLocations({ tenantId });

  const form = useForm<ClassFormData>({
    resolver: zodResolver(classSchema),
    defaultValues: {
      tenantId,
      name: "",
      description: "",
      categoryId: "",
      level_id: undefined,
      location_id: undefined,
      is_active: true,
    },
    mode: "onChange",
  });

  const handleFormSubmit = async (data: ClassFormData) => {
    try {
      setSubmitError(null);
      await onSubmit(data);
    } catch (error) {
      setSubmitError(error instanceof Error ? error.message : "An error occurred");
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>Add New Class</CardTitle>
        <CardDescription>
          Create a new class with inline creation for related entities.
        </CardDescription>
      </CardHeader>

      <CardContent>
        <form onSubmit={form.handleSubmit(handleFormSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Left Column */}
            <div className="space-y-4">
              {/* Class Name */}
              <div className="space-y-2">
                <Label htmlFor="name">Class Name *</Label>
                <Input
                  id="name"
                  {...form.register("name")}
                  placeholder="Enter class name"
                  disabled={isLoading}
                />
                {form.formState.errors.name && (
                  <p className="text-sm text-red-600">{form.formState.errors.name.message}</p>
                )}
              </div>

              {/* Class Category dengan Inline Creation */}
              <div className="space-y-2">
                <Label htmlFor="categoryId">Class Category *</Label>
                <SelectWithInlineCreation
                  value={form.watch("categoryId")}
                  onValueChange={(value) => form.setValue("categoryId", value)}
                  options={mapToSelectOptions(categories)}
                  entityType="class-category"
                  tenantId={tenantId}
                  placeholder="Select class category"
                  disabled={isLoading}
                  onEntityCreated={(newCategory) => {
                    // Auto-select newly created category
                    form.setValue("categoryId", newCategory.id);
                  }}
                  refetchData={refetchCategories}
                />
                {form.formState.errors.categoryId && (
                  <p className="text-sm text-red-600">{form.formState.errors.categoryId.message}</p>
                )}
              </div>

              {/* Class Level dengan Inline Creation */}
              <div className="space-y-2">
                <Label htmlFor="level_id">Class Level (Optional)</Label>
                <SelectWithInlineCreation
                  value={form.watch("level_id") || "not_set"}
                  onValueChange={(value) => form.setValue("level_id", value === "not_set" ? undefined : value)}
                  options={mapToSelectOptions(classLevels)}
                  entityType="class-level"
                  tenantId={tenantId}
                  placeholder="Select class level"
                  emptyOption={{ value: "not_set", label: "No level set" }}
                  disabled={isLoading}
                  onEntityCreated={(newLevel) => {
                    // Auto-select newly created level
                    form.setValue("level_id", newLevel.id);
                  }}
                  refetchData={refetchClassLevels}
                />
              </div>
            </div>

            {/* Right Column */}
            <div className="space-y-4">
              {/* Description */}
              <div className="space-y-2">
                <Label htmlFor="description">Description (Optional)</Label>
                <Input
                  id="description"
                  {...form.register("description")}
                  placeholder="Enter class description"
                  disabled={isLoading}
                />
                {form.formState.errors.description && (
                  <p className="text-sm text-red-600">{form.formState.errors.description.message}</p>
                )}
              </div>

              {/* Location dengan Inline Creation */}
              <div className="space-y-2">
                <Label htmlFor="location_id">Location (Optional)</Label>
                <SelectWithInlineCreation
                  value={form.watch("location_id") || "not_set"}
                  onValueChange={(value) => form.setValue("location_id", value === "not_set" ? undefined : value)}
                  options={mapToSelectOptions(locations)}
                  entityType="location"
                  tenantId={tenantId}
                  placeholder="Select location"
                  emptyOption={{ value: "not_set", label: "No location set" }}
                  disabled={isLoading}
                  onEntityCreated={(newLocation) => {
                    // Auto-select newly created location
                    form.setValue("location_id", newLocation.id);
                  }}
                  refetchData={refetchLocations}
                />
              </div>
            </div>
          </div>

          {/* Error Display */}
          {submitError && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600">{submitError}</p>
            </div>
          )}

          {/* Form Actions */}
          <div className="flex gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isLoading}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading || !form.formState.isValid}
              className="flex-1"
            >
              {isLoading ? "Creating..." : "Create Class"}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}

/**
 * Comparison: Before vs After
 * 
 * BEFORE (Individual Implementation):
 * - 50+ lines of modal state management
 * - 30+ lines of creation handlers
 * - 40+ lines of modal JSX
 * - Duplicate logic in every form
 * - Total: ~120 extra lines per form
 * 
 * AFTER (Global Implementation):
 * - 0 lines of modal state management
 * - 0 lines of creation handlers  
 * - 0 lines of modal JSX
 * - Reusable SelectWithInlineCreation component
 * - Total: ~0 extra lines per form
 * 
 * RESULT: 120 lines → 0 lines = 100% reduction!
 */
