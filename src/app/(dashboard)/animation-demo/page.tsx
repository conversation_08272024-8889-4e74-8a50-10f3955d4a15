"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { SuccessToast } from "@/components/ui/success-toast";
import { SuccessModal } from "@/components/ui/success-modal";
import { Confetti } from "@/components/ui/confetti";
import { Sparkles, MapPin, Zap } from "lucide-react";

export default function AnimationDemoPage() {
  const [showToast, setShowToast] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [showConfetti, setShowConfetti] = useState(false);
  const [showAll, setShowAll] = useState(false);

  const handleShowToast = () => {
    setShowToast(true);
  };

  const handleShowModal = () => {
    setShowModal(true);
  };

  const handleShowConfetti = () => {
    setShowConfetti(true);
    setTimeout(() => setShowConfetti(false), 4000);
  };

  const handleShowAll = () => {
    setShowAll(true);
    setShowConfetti(true);
    setShowToast(true);
    setShowModal(true);
    
    // Auto-hide confetti
    setTimeout(() => setShowConfetti(false), 4000);
  };

  const handleCloseAll = () => {
    setShowToast(false);
    setShowModal(false);
    setShowConfetti(false);
    setShowAll(false);
  };

  const sampleLocationData = {
    address: "123 Innovation Street",
    city: "San Francisco",
    country: "United States"
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold tracking-tight bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
          🎉 Success Animation Demo
        </h1>
        <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
          Experience the delightful animations that celebrate user achievements. 
          As a UI/UX designer with 20 years of experience, these animations are crafted to provide 
          meaningful feedback and create memorable moments.
        </p>
      </div>

      {/* Demo Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Toast Demo */}
        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mb-2">
              <Sparkles className="w-6 h-6 text-green-600 dark:text-green-400" />
            </div>
            <CardTitle className="text-lg">Success Toast</CardTitle>
            <CardDescription>
              Elegant slide-in notification with progress bar and sparkle effects
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={handleShowToast} 
              className="w-full bg-green-600 hover:bg-green-700"
            >
              Show Toast
            </Button>
          </CardContent>
        </Card>

        {/* Modal Demo */}
        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mb-2">
              <MapPin className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
            <CardTitle className="text-lg">Success Modal</CardTitle>
            <CardDescription>
              Full-featured modal with location details and action buttons
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={handleShowModal} 
              className="w-full bg-blue-600 hover:bg-blue-700"
            >
              Show Modal
            </Button>
          </CardContent>
        </Card>

        {/* Confetti Demo */}
        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-yellow-100 dark:bg-yellow-900/30 rounded-full flex items-center justify-center mb-2">
              <Zap className="w-6 h-6 text-yellow-600 dark:text-yellow-400" />
            </div>
            <CardTitle className="text-lg">Confetti Rain</CardTitle>
            <CardDescription>
              Celebratory particle animation with physics-based movement
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={handleShowConfetti} 
              className="w-full bg-yellow-600 hover:bg-yellow-700"
            >
              Show Confetti
            </Button>
          </CardContent>
        </Card>

        {/* All Together Demo */}
        <Card className="hover:shadow-lg transition-shadow border-2 border-gradient-to-r from-green-500 to-emerald-500">
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-gradient-to-r from-green-100 to-emerald-100 dark:from-green-900/30 dark:to-emerald-900/30 rounded-full flex items-center justify-center mb-2">
              <div className="text-2xl">🎊</div>
            </div>
            <CardTitle className="text-lg bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
              Full Experience
            </CardTitle>
            <CardDescription>
              Complete celebration sequence - the real location creation experience
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={handleShowAll} 
              className="w-full bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white"
            >
              🚀 Create Location
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Design Philosophy */}
      <Card className="mt-8">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Sparkles className="w-5 h-5 text-yellow-500" />
            Design Philosophy
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-2xl">🎯</span>
              </div>
              <h3 className="font-semibold mb-2">Purposeful Animation</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Every animation serves a purpose - providing feedback, guiding attention, and celebrating achievements.
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-2xl">⚡</span>
              </div>
              <h3 className="font-semibold mb-2">Performance First</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Optimized animations using Framer Motion with hardware acceleration and smooth 60fps performance.
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center mx-auto mb-3">
                <span className="text-2xl">💫</span>
              </div>
              <h3 className="font-semibold mb-2">Emotional Connection</h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Delightful micro-interactions that create positive emotional responses and memorable experiences.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Control Panel */}
      {showAll && (
        <Card className="border-red-200 dark:border-red-800">
          <CardHeader>
            <CardTitle className="text-red-600 dark:text-red-400">Demo Control Panel</CardTitle>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={handleCloseAll} 
              variant="destructive" 
              className="w-full"
            >
              Close All Animations
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Animation Components */}
      <Confetti isActive={showConfetti} />
      
      <SuccessToast
        isVisible={showToast}
        onClose={() => setShowToast(false)}
        title="Location Created Successfully!"
        description="Your new location has been added to the system."
        type="location"
        action={{
          label: "View Locations",
          onClick: () => {
            setShowToast(false);
            // Navigate to locations
          }
        }}
      />

      <SuccessModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        title="🎉 Location Added Successfully!"
        description="Your new location has been saved and is now available in your locations list."
        locationData={sampleLocationData}
        actions={{
          primary: {
            label: "Add Another Location",
            onClick: () => {
              setShowModal(false);
              // Add another location
            }
          },
          secondary: {
            label: "View All Locations",
            onClick: () => {
              setShowModal(false);
              // View all locations
            }
          }
        }}
      />
    </div>
  );
}
