"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Plus, Search, Edit, Trash2, Users, Shield, Filter, Eye, Settings, RefreshCw } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { EnhancedRoleForm } from "@/components/forms/enhanced-role-form";
import { PermissionManagement } from "./permission-management";
import {
  useRoles,
  useRoleSearch,
  useDeleteRole,
  roleKeys,
} from "@/lib/hooks/queries/use-role-queries";
import { useQueryClient } from "@tanstack/react-query";
import { type Role } from "@/lib/db/schema";
import { SuccessToast } from "@/components/ui/success-toast";

interface EnhancedRoleManagementProps {
  tenantId?: number | null;
}

export function EnhancedRoleManagement({ tenantId }: EnhancedRoleManagementProps) {
  // React Query client for cache invalidation
  const queryClient = useQueryClient();

  // State management
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedType, setSelectedType] = useState<string>("all");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingRole, setEditingRole] = useState<Role | null>(null);
  const [deletingRole, setDeletingRole] = useState<Role | null>(null);
  const [showSuccessToast, setShowSuccessToast] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [activeTab, setActiveTab] = useState("roles");
  const [refreshKey, setRefreshKey] = useState(0); // Force refresh key

  // Data fetching - use proper React Query hooks
  const {
    data: rolesData,
    isLoading,
    error,
    refetch,
  } = useRoleSearch(undefined, undefined, undefined, 100, 0);

  // Extract roles array from the response
  const roles = rolesData?.roles || [];

  // Mutations
  const deleteMutation = useDeleteRole();

  // Deduplicate roles by ID to prevent duplicate display
  const uniqueRoles = roles.filter((role, index, self) =>
    index === self.findIndex(r => r.id === role.id)
  );

  // Filter roles
  const filteredRoles = uniqueRoles.filter(role => {
    const matchesSearch = role.display_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         role.description?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesType = selectedType === "all" ||
                       (selectedType === "system" && role.is_system_role) ||
                       (selectedType === "tenant" && !role.is_system_role);

    return matchesSearch && matchesType;
  });

  // Force refresh function
  const forceRefresh = async () => {
    console.log("🔄 Force refreshing role data...");

    // Clear all role-related cache
    queryClient.removeQueries({ queryKey: roleKeys.all });
    queryClient.invalidateQueries({ queryKey: roleKeys.all });

    // Force refetch
    await refetch();
    console.log("✅ Role data refreshed");
  };

  // Handlers
  const handleCreateSuccess = async () => {
    setSuccessMessage("Role created successfully!");
    setShowSuccessToast(true);
    setIsCreateDialogOpen(false);

    // Force refresh data
    await forceRefresh();
  };

  const handleUpdateSuccess = async () => {
    setSuccessMessage("Role updated successfully!");
    setShowSuccessToast(true);
    setEditingRole(null);

    // Force refresh data
    await forceRefresh();
  };

  const handleDeleteRole = async () => {
    if (!deletingRole) return;

    try {
      await deleteMutation.mutateAsync(deletingRole.id);
      setSuccessMessage("Role deleted successfully!");
      setShowSuccessToast(true);
      setDeletingRole(null);

      // Force refresh data
      await forceRefresh();
    } catch (error) {
      console.error("Error deleting role:", error);
    }
  };

  const canDeleteRole = (role: Role) => {
    return !role.is_system_role;
  };

  const getRoleTypeColor = (role: Role) => {
    if (role.is_system_role) {
      return "bg-blue-100 text-blue-800";
    }
    return "bg-green-100 text-green-800";
  };

  const getHierarchyColor = (level: number) => {
    if (level <= 10) return "bg-red-100 text-red-800";
    if (level <= 30) return "bg-orange-100 text-orange-800";
    if (level <= 60) return "bg-yellow-100 text-yellow-800";
    return "bg-gray-100 text-gray-800";
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(6)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-muted rounded w-1/2"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <p className="text-red-500">Error loading roles: {error.message}</p>
          <Button onClick={() => refetch()} className="mt-2" variant="outline">
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Role & Permission Management</h1>
          <p className="text-muted-foreground">
            Manage roles, permissions, and access controls
          </p>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="roles" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Role Management
          </TabsTrigger>
          <TabsTrigger value="permissions" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Permission Management
          </TabsTrigger>
        </TabsList>

        <TabsContent value="roles" className="space-y-6">
          {/* Role Management Header */}
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-2xl font-bold">Roles</h2>
              <p className="text-muted-foreground">
                Create and manage user roles
              </p>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" onClick={forceRefresh}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
              <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
                <DialogTrigger asChild>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Role
                  </Button>
                </DialogTrigger>
              <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>Create New Role</DialogTitle>
                </DialogHeader>
                <EnhancedRoleForm
                  tenantId={tenantId}
                  onSubmit={handleCreateSuccess}
                  onCancel={() => setIsCreateDialogOpen(false)}
                />
              </DialogContent>
            </Dialog>
            </div>
          </div>

          {/* Filters */}
          <Card>
            <CardContent className="p-4">
              <div className="flex flex-col sm:flex-row gap-4">
                {/* Search */}
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                  <Input
                    placeholder="Search roles..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>

                {/* Type Filter */}
                <div className="w-full sm:w-48">
                  <Select value={selectedType} onValueChange={setSelectedType}>
                    <SelectTrigger>
                      <Filter className="h-4 w-4 mr-2" />
                      <SelectValue placeholder="All Types" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Types</SelectItem>
                      <SelectItem value="system">System Roles</SelectItem>
                      <SelectItem value="tenant">Tenant Roles</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Roles List */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <AnimatePresence>
              {filteredRoles.map((role) => (
                <motion.div
                  key={role.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.2 }}
                >
                  <Card className="hover:shadow-md transition-shadow">
                    <CardContent className="p-6">
                      <div className="space-y-4">
                        {/* Header */}
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h3 className="font-semibold text-lg mb-1">
                              {role.display_name}
                            </h3>
                            <code className="text-xs bg-muted px-2 py-1 rounded">
                              {role.name}
                            </code>
                          </div>
                          <div className="flex gap-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                              onClick={() => setEditingRole(role)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            {canDeleteRole(role) && (
                              <Button
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0 text-red-500 hover:text-red-700"
                                onClick={() => setDeletingRole(role)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </div>

                        {/* Description */}
                        {role.description && (
                          <p className="text-sm text-muted-foreground">
                            {role.description}
                          </p>
                        )}

                        {/* Badges */}
                        <div className="flex flex-wrap gap-2">
                          <Badge className={getRoleTypeColor(role)}>
                            {role.is_system_role ? "System" : "Tenant"}
                          </Badge>
                          <Badge className={getHierarchyColor(role.hierarchy_level)}>
                            Level {role.hierarchy_level}
                          </Badge>
                          {role.permissions && (
                            <Badge variant="outline">
                              {role.permissions.length} permission{role.permissions.length !== 1 ? 's' : ''}
                            </Badge>
                          )}
                        </div>

                        {/* Permissions Preview */}
                        {role.permissions && role.permissions.length > 0 && (
                          <div className="space-y-2">
                            <div className="text-sm font-medium">Permissions:</div>
                            <div className="flex flex-wrap gap-1">
                              {role.permissions.slice(0, 3).map((permission) => (
                                <Badge key={permission.id} variant="secondary" className="text-xs">
                                  {permission.action}
                                </Badge>
                              ))}
                              {role.permissions.length > 3 && (
                                <Badge variant="secondary" className="text-xs">
                                  +{role.permissions.length - 3} more
                                </Badge>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </AnimatePresence>
          </div>

          {filteredRoles.length === 0 && (
            <Card>
              <CardContent className="p-6 text-center">
                <p className="text-muted-foreground">No roles found matching your criteria.</p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="permissions">
          <PermissionManagement />
        </TabsContent>
      </Tabs>

      {/* Edit Dialog */}
      <Dialog open={!!editingRole} onOpenChange={(open) => {
        if (!open) setEditingRole(null);
      }}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Role</DialogTitle>
          </DialogHeader>
          {editingRole && (
            <EnhancedRoleForm
              role={editingRole}
              tenantId={tenantId}
              onSubmit={handleUpdateSuccess}
              onCancel={() => setEditingRole(null)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deletingRole} onOpenChange={(open) => {
        if (!open) setDeletingRole(null);
      }}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Role</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete the role "{deletingRole?.display_name}"? 
              This action cannot be undone and may affect users assigned to this role.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteRole}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Success Toast */}
      <SuccessToast
        open={showSuccessToast}
        onOpenChange={setShowSuccessToast}
        title="Success!"
        description={successMessage}
      />
    </div>
  );
}
