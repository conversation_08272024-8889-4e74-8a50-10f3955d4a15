import { NextRequest, NextResponse } from "next/server";
import { customerAuthService } from "@/lib/services/customer-auth.service";
import { z } from "zod";

/**
 * Customer Authentication Middleware
 * 
 * Handles JWT token validation for customer API endpoints
 * FAANG-level security with proper error handling and rate limiting
 */

export interface CustomerAuthContext {
  customer: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    displayName: string;
    image?: string;
    tenantId: number;
    isEmailVerified: boolean;
    membershipType: string;
  };
  sessionId: string;
  isAuthenticated: true;
}

export interface CustomerAuthError {
  isAuthenticated: false;
  error: string;
  errorCode: string;
  statusCode: number;
}

export type CustomerAuthResult = CustomerAuthContext | CustomerAuthError;

/**
 * Extract Bearer token from Authorization header
 */
function extractBearerToken(request: NextRequest): string | null {
  const authHeader = request.headers.get("authorization");
  
  if (!authHeader) {
    return null;
  }

  const match = authHeader.match(/^Bearer\s+(.+)$/);
  return match ? match[1] : null;
}

/**
 * Get client IP address with proxy support
 */
function getClientIP(request: NextRequest): string {
  // Check for forwarded IP (behind proxy/load balancer)
  const forwarded = request.headers.get("x-forwarded-for");
  if (forwarded) {
    return forwarded.split(",")[0].trim();
  }

  // Check for real IP (some proxies use this)
  const realIP = request.headers.get("x-real-ip");
  if (realIP) {
    return realIP;
  }

  // Fallback to connection IP
  return request.ip || "unknown";
}

/**
 * Rate limiting store (in-memory for demo, use Redis in production)
 */
class RateLimitStore {
  private store = new Map<string, { count: number; resetTime: number }>();

  check(key: string, limit: number, windowMs: number): { allowed: boolean; remaining: number; resetTime: number } {
    const now = Date.now();
    const entry = this.store.get(key);

    if (!entry || now > entry.resetTime) {
      // New window or expired
      const resetTime = now + windowMs;
      this.store.set(key, { count: 1, resetTime });
      return { allowed: true, remaining: limit - 1, resetTime };
    }

    if (entry.count >= limit) {
      // Rate limit exceeded
      return { allowed: false, remaining: 0, resetTime: entry.resetTime };
    }

    // Increment count
    entry.count++;
    this.store.set(key, entry);
    return { allowed: true, remaining: limit - entry.count, resetTime: entry.resetTime };
  }

  cleanup(): void {
    const now = Date.now();
    for (const [key, entry] of this.store.entries()) {
      if (now > entry.resetTime) {
        this.store.delete(key);
      }
    }
  }
}

const rateLimitStore = new RateLimitStore();

// Cleanup expired entries every 5 minutes
setInterval(() => rateLimitStore.cleanup(), 5 * 60 * 1000);

/**
 * Rate limiting middleware
 */
export function rateLimit(options: {
  windowMs: number;
  max: number;
  keyGenerator?: (request: NextRequest) => string;
  skipSuccessfulRequests?: boolean;
}) {
  return (request: NextRequest): { allowed: boolean; headers: Record<string, string> } => {
    const key = options.keyGenerator ? options.keyGenerator(request) : getClientIP(request);
    const result = rateLimitStore.check(key, options.max, options.windowMs);

    const headers = {
      "X-RateLimit-Limit": options.max.toString(),
      "X-RateLimit-Remaining": result.remaining.toString(),
      "X-RateLimit-Reset": new Date(result.resetTime).toISOString(),
    };

    return { allowed: result.allowed, headers };
  };
}

/**
 * Security headers middleware
 */
export function addSecurityHeaders(response: NextResponse): NextResponse {
  // CORS headers for customer API
  response.headers.set("Access-Control-Allow-Origin", process.env.FRONTEND_URL || "*");
  response.headers.set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
  response.headers.set("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Tenant-ID");
  response.headers.set("Access-Control-Allow-Credentials", "true");
  response.headers.set("Access-Control-Max-Age", "86400");

  // Security headers
  response.headers.set("X-Content-Type-Options", "nosniff");
  response.headers.set("X-Frame-Options", "DENY");
  response.headers.set("X-XSS-Protection", "1; mode=block");
  response.headers.set("Referrer-Policy", "strict-origin-when-cross-origin");
  response.headers.set("Permissions-Policy", "camera=(), microphone=(), geolocation=()");
  
  // Content Security Policy for API responses
  response.headers.set(
    "Content-Security-Policy",
    "default-src 'none'; frame-ancestors 'none';"
  );

  return response;
}

/**
 * Main customer authentication middleware
 */
export async function customerAuthMiddleware(
  request: NextRequest,
  options: {
    requireAuth?: boolean;
    tenantId?: number;
    rateLimit?: {
      windowMs: number;
      max: number;
    };
  } = {}
): Promise<CustomerAuthResult> {
  try {
    // Apply rate limiting if configured
    if (options.rateLimit) {
      const rateLimitResult = rateLimit({
        windowMs: options.rateLimit.windowMs,
        max: options.rateLimit.max,
        keyGenerator: (req) => `${getClientIP(req)}:${req.nextUrl.pathname}`,
      })(request);

      if (!rateLimitResult.allowed) {
        return {
          isAuthenticated: false,
          error: "Too many requests",
          errorCode: "RATE_LIMIT_EXCEEDED",
          statusCode: 429,
        };
      }
    }

    // Extract tenant ID from header or URL
    const tenantId = options.tenantId || 
      parseInt(request.headers.get("x-tenant-id") || "") ||
      parseInt(request.nextUrl.searchParams.get("tenantId") || "");

    if (!tenantId) {
      return {
        isAuthenticated: false,
        error: "Tenant ID is required",
        errorCode: "MISSING_TENANT_ID",
        statusCode: 400,
      };
    }

    // Extract Bearer token
    const token = extractBearerToken(request);

    if (!token) {
      if (options.requireAuth !== false) {
        return {
          isAuthenticated: false,
          error: "Authorization token is required",
          errorCode: "MISSING_TOKEN",
          statusCode: 401,
        };
      }
      // If auth is not required and no token, return early
      return {
        isAuthenticated: false,
        error: "No token provided",
        errorCode: "NO_TOKEN",
        statusCode: 401,
      };
    }

    // Validate JWT token
    const validation = await customerAuthService.validateJWTToken(token, tenantId);

    if (!validation.valid || !validation.customer) {
      return {
        isAuthenticated: false,
        error: validation.error || "Invalid token",
        errorCode: "INVALID_TOKEN",
        statusCode: 401,
      };
    }

    // Extract session ID from token (would need to decode JWT)
    // For now, using a placeholder
    const sessionId = "session-id-placeholder";

    return {
      isAuthenticated: true,
      customer: validation.customer,
      sessionId,
    };

  } catch (error) {
    console.error("Customer auth middleware error:", error);
    return {
      isAuthenticated: false,
      error: "Authentication failed",
      errorCode: "AUTH_ERROR",
      statusCode: 500,
    };
  }
}

/**
 * Create authenticated API response with security headers
 */
export function createAuthenticatedResponse(
  data: any,
  status: number = 200,
  headers: Record<string, string> = {}
): NextResponse {
  const response = NextResponse.json(data, { status, headers });
  return addSecurityHeaders(response);
}

/**
 * Create error response with security headers
 */
export function createErrorResponse(
  error: string,
  errorCode: string,
  status: number,
  headers: Record<string, string> = {}
): NextResponse {
  const response = NextResponse.json(
    {
      success: false,
      error,
      errorCode,
      timestamp: new Date().toISOString(),
    },
    { status, headers }
  );
  return addSecurityHeaders(response);
}

/**
 * Middleware wrapper for API routes
 */
export function withCustomerAuth(
  handler: (request: NextRequest, context: CustomerAuthContext) => Promise<NextResponse>,
  options: {
    requireAuth?: boolean;
    tenantId?: number;
    rateLimit?: {
      windowMs: number;
      max: number;
    };
  } = {}
) {
  return async (request: NextRequest): Promise<NextResponse> => {
    // Handle preflight OPTIONS requests
    if (request.method === "OPTIONS") {
      const response = new NextResponse(null, { status: 200 });
      return addSecurityHeaders(response);
    }

    const authResult = await customerAuthMiddleware(request, options);

    if (!authResult.isAuthenticated) {
      return createErrorResponse(
        authResult.error,
        authResult.errorCode,
        authResult.statusCode
      );
    }

    try {
      return await handler(request, authResult);
    } catch (error) {
      console.error("API handler error:", error);
      return createErrorResponse(
        "Internal server error",
        "INTERNAL_ERROR",
        500
      );
    }
  };
}
