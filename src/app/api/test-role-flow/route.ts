import { NextRequest, NextResponse } from "next/server";
import { RoleService } from "@/lib/services/role.service";

// Test endpoint to verify complete role management flow
export async function POST(request: NextRequest) {
  try {
    console.log("🧪 Testing complete role management flow...");
    
    // Step 1: List existing roles
    console.log("\n1️⃣ Listing existing roles...");
    const initialRoles = await RoleService.searchRoles(null, undefined, undefined, 20, 0);
    console.log(`Found ${initialRoles.roles.length} existing roles`);

    // Step 2: Create a new role
    console.log("\n2️⃣ Creating new role...");
    const newRoleData = {
      name: `test_flow_${Date.now()}`,
      display_name: "Test Flow Role",
      description: "Role created to test the complete flow",
      is_system_role: false,
      hierarchy_level: 70,
      tenantId: 1,
      permissions: []
    };

    const createdRole = await RoleService.create(newRoleData);
    console.log(`✅ Role created: ${createdRole.id} - ${createdRole.display_name}`);

    // Step 3: List roles again to verify creation
    console.log("\n3️⃣ Listing roles after creation...");
    const rolesAfterCreate = await RoleService.searchRoles(null, undefined, undefined, 20, 0);
    console.log(`Now found ${rolesAfterCreate.roles.length} roles`);

    // Step 4: Update the role
    console.log("\n4️⃣ Updating the role...");
    const updatedRole = await RoleService.update(createdRole.id, {
      description: "Updated description for test flow"
    });
    console.log(`✅ Role updated: ${updatedRole.description}`);

    // Step 5: Get role by ID
    console.log("\n5️⃣ Getting role by ID...");
    const retrievedRole = await RoleService.getById(createdRole.id);
    console.log(`✅ Role retrieved: ${retrievedRole?.display_name}`);

    // Step 6: Clean up - delete the test role
    console.log("\n6️⃣ Cleaning up test role...");
    await RoleService.delete(createdRole.id);
    console.log("✅ Test role deleted");

    // Step 7: Final verification
    console.log("\n7️⃣ Final verification...");
    const finalRoles = await RoleService.searchRoles(null, undefined, undefined, 20, 0);
    console.log(`Final count: ${finalRoles.roles.length} roles`);

    return NextResponse.json({
      success: true,
      message: "Complete role management flow test successful!",
      data: {
        initialCount: initialRoles.roles.length,
        afterCreateCount: rolesAfterCreate.roles.length,
        finalCount: finalRoles.roles.length,
        testRole: {
          id: createdRole.id,
          name: createdRole.name,
          display_name: createdRole.display_name,
          created: true,
          updated: true,
          deleted: true
        }
      }
    });
  } catch (error) {
    console.error("❌ Role management flow test failed:", error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}
