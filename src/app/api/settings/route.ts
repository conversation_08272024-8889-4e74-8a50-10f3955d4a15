import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { SettingsService } from "@/lib/services/settings.service";
import { z } from "zod";

// Validation schema
const createSettingsSchema = z.object({
  tenantId: z.number().int().positive(),
  currency: z.string().length(3).optional(),
  timeZone: z.string().optional(),
});

const updateSettingsSchema = createSettingsSchema.partial().omit({ tenantId: true });

// GET /api/settings - Get settings
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get("tenantId");
    const action = searchParams.get("action");

    if (action === "currencies") {
      const currencies = SettingsService.getSupportedCurrencies();
      return NextResponse.json({ currencies });
    }

    if (action === "timezones") {
      const timezones = SettingsService.getCommonTimeZones();
      return NextResponse.json({ timezones });
    }

    if (tenantId) {
      // Get specific settings
      const settings = await SettingsService.getByTenantId(parseInt(tenantId));
      return NextResponse.json({ settings });
    } else {
      // Get all settings (admin only)
      if (session.user.role !== "admin") {
        return NextResponse.json({ error: "Forbidden" }, { status: 403 });
      }
      
      const settings = await SettingsService.getAll();
      return NextResponse.json({ settings });
    }
  } catch (error) {
    console.error("Error fetching settings:", error);
    return NextResponse.json(
      { error: "Failed to fetch settings" },
      { status: 500 }
    );
  }
}

// POST /api/settings - Create or update settings
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = createSettingsSchema.parse(body);

    // Validate currency if provided
    if (validatedData.currency && !SettingsService.validateCurrency(validatedData.currency)) {
      return NextResponse.json(
        { error: "Invalid currency code" },
        { status: 400 }
      );
    }

    // Validate timezone if provided
    if (validatedData.timeZone && !SettingsService.validateTimeZone(validatedData.timeZone)) {
      return NextResponse.json(
        { error: "Invalid timezone" },
        { status: 400 }
      );
    }

    // Check if user has permission to create/update settings for this tenant
    if (session.user.role !== "admin") {
      // Add tenant ownership check here
    }

    const settings = await SettingsService.upsert(validatedData);

    return NextResponse.json({ settings }, { status: 201 });
  } catch (error) {
    console.error("Error creating/updating settings:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to create/update settings" },
      { status: 500 }
    );
  }
}

// PUT /api/settings - Update settings
export async function PUT(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get("tenantId");

    if (!tenantId) {
      return NextResponse.json(
        { error: "tenantId parameter is required" },
        { status: 400 }
      );
    }

    const body = await request.json();
    const validatedData = updateSettingsSchema.parse(body);

    // Validate currency if provided
    if (validatedData.currency && !SettingsService.validateCurrency(validatedData.currency)) {
      return NextResponse.json(
        { error: "Invalid currency code" },
        { status: 400 }
      );
    }

    // Validate timezone if provided
    if (validatedData.timeZone && !SettingsService.validateTimeZone(validatedData.timeZone)) {
      return NextResponse.json(
        { error: "Invalid timezone" },
        { status: 400 }
      );
    }

    // Check if user has permission to update settings for this tenant
    if (session.user.role !== "admin") {
      // Add tenant ownership check here
    }

    const settings = await SettingsService.update(parseInt(tenantId), validatedData);

    if (!settings) {
      return NextResponse.json(
        { error: "Settings not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({ settings });
  } catch (error) {
    console.error("Error updating settings:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to update settings" },
      { status: 500 }
    );
  }
}

// DELETE /api/settings - Delete settings
export async function DELETE(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get("tenantId");

    if (!tenantId) {
      return NextResponse.json(
        { error: "tenantId parameter is required" },
        { status: 400 }
      );
    }

    // Check if user has permission to delete settings for this tenant
    if (session.user.role !== "admin") {
      // Add tenant ownership check here
    }

    const success = await SettingsService.delete(parseInt(tenantId));

    if (!success) {
      return NextResponse.json(
        { error: "Settings not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({ message: "Settings deleted successfully" });
  } catch (error) {
    console.error("Error deleting settings:", error);
    return NextResponse.json(
      { error: "Failed to delete settings" },
      { status: 500 }
    );
  }
}
