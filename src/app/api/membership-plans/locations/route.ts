import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { MembershipPlanLocationService } from "@/lib/services/membership-plan-location-service";
import { withRB<PERSON> } from "@/lib/middleware/rbac-middleware";

async function getMembershipPlansWithLocationsHandler(req: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.tenantId) {
      return NextResponse.json(
        { error: "Tenant ID required" },
        { status: 400 }
      );
    }

    const tenantId = parseInt(session.user.tenantId);
    const data = await MembershipPlanLocationService.getMembershipPlansWithLocations(tenantId);

    return NextResponse.json({
      success: true,
      data,
    });
  } catch (error) {
    console.error("Error fetching membership plans with locations:", error);
    return NextResponse.json(
      { error: "Failed to fetch membership plans with locations" },
      { status: 500 }
    );
  }
}

// Protect with RBAC - require packages.manage permission
export const GET = withRBAC(getMembershipPlansWithLocationsHandler, {
  module: "packages",
  action: "manage"
});
