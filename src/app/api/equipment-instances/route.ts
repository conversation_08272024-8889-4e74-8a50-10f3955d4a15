import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { z } from "zod";
import { EquipmentInstanceService } from "@/lib/services/equipment-instances.service";

// Validation schema for creating equipment instance
export const createEquipmentInstanceSchema = z.object({
  equipmentId: z.string().min(1, "Equipment ID is required"),
  locationId: z.string().min(1, "Location ID is required"),
  quantity: z.number().int().min(1, "Quantity must be at least 1"),
  displayName: z.string().max(255).optional(),
});

// GET /api/equipment-instances - Get equipment instances
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const equipmentId = searchParams.get("equipmentId");
    const locationId = searchParams.get("locationId");

    let instances;

    if (equipmentId) {
      instances = await EquipmentInstanceService.getByEquipmentId(equipmentId);
    } else if (locationId) {
      instances = await EquipmentInstanceService.getByLocationId(locationId);
    } else {
      instances = await EquipmentInstanceService.getAll();
    }

    return NextResponse.json(
      { data: instances },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error fetching equipment instances:", error);
    return NextResponse.json(
      { error: "Failed to fetch equipment instances" },
      { status: 500 }
    );
  }
}

// POST /api/equipment-instances - Create equipment instance
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = createEquipmentInstanceSchema.parse(body);

    const newInstance = await EquipmentInstanceService.create(validatedData);

    return NextResponse.json(
      { 
        message: "Equipment instance created successfully", 
        data: newInstance 
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error creating equipment instance:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid data", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to create equipment instance" },
      { status: 500 }
    );
  }
}
