import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { WaiverFormService } from "@/lib/services/waiver-forms.service";

const waiverFormService = new WaiverFormService();

// GET /api/waiver-forms/stats - Get waiver form statistics
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const tenantIdParam = searchParams.get("tenantId");

    let tenantId: number | undefined;
    if (tenantIdParam) {
      const tenantIdNum = parseInt(tenantIdParam);
      if (isNaN(tenantIdNum)) {
        return NextResponse.json(
          { error: "Invalid tenant ID" },
          { status: 400 }
        );
      }
      tenantId = tenantIdNum;
    }

    const stats = await waiverFormService.getStats(tenantId);

    return NextResponse.json(
      { data: stats },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error fetching waiver form stats:", error);
    return NextResponse.json(
      { error: "Failed to fetch waiver form stats" },
      { status: 500 }
    );
  }
}
