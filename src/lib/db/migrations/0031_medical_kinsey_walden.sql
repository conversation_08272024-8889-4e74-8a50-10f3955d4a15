CREATE TABLE "voucher_customer_assignments" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"voucher_id" varchar(255) NOT NULL,
	"customer_id" varchar(255) NOT NULL,
	"assigned_by" varchar(255),
	"assigned_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"expires_at" timestamp,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updated_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT "unique_voucher_customer" UNIQUE("voucher_id","customer_id")
);
--> statement-breakpoint
CREATE TABLE "voucher_usage" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"voucher_id" varchar(255) NOT NULL,
	"customer_id" varchar(255) NOT NULL,
	"order_id" varchar(255),
	"package_id" varchar(255),
	"class_id" varchar(255),
	"location_id" varchar(255),
	"original_amount" integer NOT NULL,
	"discount_amount" integer NOT NULL,
	"final_amount" integer NOT NULL,
	"currency" varchar(3) DEFAULT 'USD' NOT NULL,
	"used_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"ip_address" varchar(45),
	"user_agent" text,
	"created_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT "check_positive_amounts" CHECK ("voucher_usage"."original_amount" > 0 AND "voucher_usage"."discount_amount" >= 0 AND "voucher_usage"."final_amount" >= 0),
	CONSTRAINT "check_valid_discount" CHECK ("voucher_usage"."discount_amount" <= "voucher_usage"."original_amount"),
	CONSTRAINT "check_final_amount" CHECK ("voucher_usage"."final_amount" = "voucher_usage"."original_amount" - "voucher_usage"."discount_amount")
);
--> statement-breakpoint
CREATE TABLE "vouchers" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"tenant_id" integer NOT NULL,
	"code" varchar(50) NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" text,
	"type" varchar(50) NOT NULL,
	"value" integer NOT NULL,
	"currency" varchar(3) DEFAULT 'USD' NOT NULL,
	"usage_limit" integer,
	"usage_limit_per_customer" integer DEFAULT 1,
	"current_usage_count" integer DEFAULT 0 NOT NULL,
	"valid_from" timestamp NOT NULL,
	"valid_until" timestamp NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"is_public" boolean DEFAULT true NOT NULL,
	"auto_apply" boolean DEFAULT false NOT NULL,
	"restrictions" jsonb DEFAULT '{}'::jsonb,
	"created_by" varchar(255),
	"created_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updated_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT "unique_tenant_voucher_code" UNIQUE("tenant_id","code"),
	CONSTRAINT "check_valid_voucher_type" CHECK ("vouchers"."type" IN ('percentage', 'fixed_amount', 'free_shipping', 'buy_x_get_y')),
	CONSTRAINT "check_valid_percentage" CHECK ("vouchers"."type" != 'percentage' OR ("vouchers"."value" >= 1 AND "vouchers"."value" <= 100)),
	CONSTRAINT "check_positive_value" CHECK ("vouchers"."value" > 0),
	CONSTRAINT "check_valid_period" CHECK ("vouchers"."valid_from" < "vouchers"."valid_until"),
	CONSTRAINT "check_usage_limits" CHECK ("vouchers"."usage_limit" IS NULL OR "vouchers"."usage_limit" > 0),
	CONSTRAINT "check_usage_limit_per_customer" CHECK ("vouchers"."usage_limit_per_customer" > 0),
	CONSTRAINT "check_current_usage" CHECK ("vouchers"."current_usage_count" >= 0),
	CONSTRAINT "check_usage_not_exceeded" CHECK ("vouchers"."usage_limit" IS NULL OR "vouchers"."current_usage_count" <= "vouchers"."usage_limit")
);
--> statement-breakpoint
ALTER TABLE "voucher_customer_assignments" ADD CONSTRAINT "voucher_customer_assignments_voucher_id_vouchers_id_fk" FOREIGN KEY ("voucher_id") REFERENCES "public"."vouchers"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "voucher_customer_assignments" ADD CONSTRAINT "voucher_customer_assignments_customer_id_customers_id_fk" FOREIGN KEY ("customer_id") REFERENCES "public"."customers"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "voucher_customer_assignments" ADD CONSTRAINT "voucher_customer_assignments_assigned_by_users_id_fk" FOREIGN KEY ("assigned_by") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "voucher_usage" ADD CONSTRAINT "voucher_usage_voucher_id_vouchers_id_fk" FOREIGN KEY ("voucher_id") REFERENCES "public"."vouchers"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "voucher_usage" ADD CONSTRAINT "voucher_usage_customer_id_customers_id_fk" FOREIGN KEY ("customer_id") REFERENCES "public"."customers"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "voucher_usage" ADD CONSTRAINT "voucher_usage_package_id_package_id_fk" FOREIGN KEY ("package_id") REFERENCES "public"."package"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "voucher_usage" ADD CONSTRAINT "voucher_usage_class_id_classes_id_fk" FOREIGN KEY ("class_id") REFERENCES "public"."classes"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "voucher_usage" ADD CONSTRAINT "voucher_usage_location_id_locations_id_fk" FOREIGN KEY ("location_id") REFERENCES "public"."locations"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vouchers" ADD CONSTRAINT "vouchers_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vouchers" ADD CONSTRAINT "vouchers_created_by_users_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "idx_voucher_assignments_voucher" ON "voucher_customer_assignments" USING btree ("voucher_id");--> statement-breakpoint
CREATE INDEX "idx_voucher_assignments_customer" ON "voucher_customer_assignments" USING btree ("customer_id");--> statement-breakpoint
CREATE INDEX "idx_voucher_assignments_active" ON "voucher_customer_assignments" USING btree ("is_active","assigned_at");--> statement-breakpoint
CREATE INDEX "idx_voucher_usage_voucher" ON "voucher_usage" USING btree ("voucher_id");--> statement-breakpoint
CREATE INDEX "idx_voucher_usage_customer" ON "voucher_usage" USING btree ("customer_id");--> statement-breakpoint
CREATE INDEX "idx_voucher_usage_date" ON "voucher_usage" USING btree ("used_at");--> statement-breakpoint
CREATE INDEX "idx_voucher_usage_order" ON "voucher_usage" USING btree ("order_id");--> statement-breakpoint
CREATE INDEX "idx_vouchers_tenant_active" ON "vouchers" USING btree ("tenant_id","is_active");--> statement-breakpoint
CREATE INDEX "idx_vouchers_code_lookup" ON "vouchers" USING btree ("code","is_active");--> statement-breakpoint
CREATE INDEX "idx_vouchers_validity" ON "vouchers" USING btree ("valid_from","valid_until");--> statement-breakpoint
CREATE INDEX "idx_vouchers_usage" ON "vouchers" USING btree ("usage_limit","current_usage_count");