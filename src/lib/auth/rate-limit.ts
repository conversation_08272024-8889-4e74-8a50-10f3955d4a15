import { db } from "../db";
import { rateLimits } from "../db/schema";
import { eq, and, gte } from "drizzle-orm";
import { createId } from "@paralleldrive/cuid2";
import type { RateLimitConfig, RateLimitResult } from "@/types/auth";

// Default rate limit configurations
export const rateLimitConfigs: Record<string, RateLimitConfig> = {
  login: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxAttempts: 5,
    blockDurationMs: 30 * 60 * 1000, // 30 minutes
  },
  register: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxAttempts: 3,
    blockDurationMs: 60 * 60 * 1000, // 1 hour
  },
  forgotPassword: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxAttempts: 3,
    blockDurationMs: 60 * 60 * 1000, // 1 hour
  },
  emailVerification: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxAttempts: 5,
    blockDurationMs: 60 * 60 * 1000, // 1 hour
  },
  apiCall: {
    windowMs: 60 * 1000, // 1 minute
    maxAttempts: 100,
  },
  webauthn: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxAttempts: 10,
    blockDurationMs: 30 * 60 * 1000, // 30 minutes
  },
};

// Get client identifier (IP address or user ID)
export function getClientIdentifier(request: Request, userId?: string): string {
  if (userId) {
    return `user:${userId}`;
  }

  // Try to get real IP from headers (for proxies/load balancers)
  const forwarded = request.headers.get("x-forwarded-for");
  const realIp = request.headers.get("x-real-ip");
  const cfConnectingIp = request.headers.get("cf-connecting-ip");

  const ip = cfConnectingIp || realIp || forwarded?.split(",")[0] || "unknown";
  return `ip:${ip}`;
}

// Check rate limit
export async function checkRateLimit(
  identifier: string,
  action: string,
  config?: RateLimitConfig
): Promise<RateLimitResult> {
  const rateLimitConfig = config || rateLimitConfigs[action];
  
  if (!rateLimitConfig) {
    throw new Error(`Rate limit configuration not found for action: ${action}`);
  }

  const now = new Date();
  const windowStart = new Date(now.getTime() - rateLimitConfig.windowMs);

  try {
    // Clean up old rate limit records
    await db
      .delete(rateLimits)
      .where(
        and(
          eq(rateLimits.identifier, identifier),
          eq(rateLimits.action, action),
          gte(rateLimits.windowStart, windowStart)
        )
      );

    // Get current rate limit record
    const [currentLimit] = await db
      .select()
      .from(rateLimits)
      .where(
        and(
          eq(rateLimits.identifier, identifier),
          eq(rateLimits.action, action),
          gte(rateLimits.windowStart, windowStart)
        )
      )
      .limit(1);

    if (!currentLimit) {
      // Create new rate limit record
      await db
        .insert(rateLimits)
        .values({
          id: createId(),
          identifier,
          action,
          count: 1,
          windowStart: now,
        });

      return {
        success: true,
        remaining: rateLimitConfig.maxAttempts - 1,
        resetTime: new Date(now.getTime() + rateLimitConfig.windowMs),
      };
    }

    // Check if limit exceeded
    if (currentLimit.count >= rateLimitConfig.maxAttempts) {
      const resetTime = new Date(
        currentLimit.windowStart.getTime() + rateLimitConfig.windowMs
      );

      return {
        success: false,
        remaining: 0,
        resetTime,
        blocked: true,
      };
    }

    // Increment count
    await db
      .update(rateLimits)
      .set({
        count: currentLimit.count + 1,
        updatedAt: now,
      })
      .where(eq(rateLimits.id, currentLimit.id));

    return {
      success: true,
      remaining: rateLimitConfig.maxAttempts - currentLimit.count - 1,
      resetTime: new Date(
        currentLimit.windowStart.getTime() + rateLimitConfig.windowMs
      ),
    };
  } catch (error) {
    console.error("Rate limit check error:", error);
    // On error, allow the request but log the issue
    return {
      success: true,
      remaining: rateLimitConfig.maxAttempts - 1,
      resetTime: new Date(now.getTime() + rateLimitConfig.windowMs),
    };
  }
}

// Rate limit middleware for API routes
export function withRateLimit(
  action: string,
  config?: RateLimitConfig
) {
  return async function rateLimitMiddleware(
    request: Request,
    userId?: string
  ): Promise<Response | null> {
    const identifier = getClientIdentifier(request, userId);
    const result = await checkRateLimit(identifier, action, config);

    if (!result.success) {
      return new Response(
        JSON.stringify({
          error: "Rate limit exceeded",
          message: `Too many ${action} attempts. Please try again later.`,
          resetTime: result.resetTime.toISOString(),
        }),
        {
          status: 429,
          headers: {
            "Content-Type": "application/json",
            "X-RateLimit-Limit": String(config?.maxAttempts || rateLimitConfigs[action]?.maxAttempts || 100),
            "X-RateLimit-Remaining": String(result.remaining),
            "X-RateLimit-Reset": String(Math.ceil(result.resetTime.getTime() / 1000)),
            "Retry-After": String(Math.ceil((result.resetTime.getTime() - Date.now()) / 1000)),
          },
        }
      );
    }

    return null; // Continue with the request
  };
}

// Reset rate limit for a specific identifier and action
export async function resetRateLimit(
  identifier: string,
  action: string
): Promise<void> {
  try {
    await db
      .delete(rateLimits)
      .where(
        and(
          eq(rateLimits.identifier, identifier),
          eq(rateLimits.action, action)
        )
      );
  } catch (error) {
    console.error("Error resetting rate limit:", error);
  }
}

// Clean up expired rate limit records (should be run periodically)
export async function cleanupExpiredRateLimits(): Promise<void> {
  const now = new Date();
  
  try {
    // Delete records older than 24 hours
    const cutoff = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    
    await db
      .delete(rateLimits)
      .where(gte(rateLimits.createdAt, cutoff));
      
    console.log("Cleaned up expired rate limit records");
  } catch (error) {
    console.error("Error cleaning up rate limits:", error);
  }
}
