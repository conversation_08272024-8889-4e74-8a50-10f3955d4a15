"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Settings, DollarSign, Clock, Check, Loader2, Globe } from "lucide-react";
import { Setting } from "@/lib/db/schema";

const settingsSchema = z.object({
  currency: z.string().length(3, "Currency must be 3 characters"),
  timeZone: z.string().min(1, "Timezone is required"),
});

type SettingsFormData = z.infer<typeof settingsSchema>;

interface SettingsPanelProps {
  tenantId: number;
  settings?: Setting | null;
  onSettingsUpdate: (data: SettingsFormData & { tenantId: number }) => Promise<void>;
  loading?: boolean;
}

interface Currency {
  code: string;
  name: string;
  symbol: string;
}

interface TimeZone {
  value: string;
  label: string;
}

export function SettingsPanel({
  tenantId,
  settings,
  onSettingsUpdate,
  loading = false,
}: SettingsPanelProps) {
  const [currencies, setCurrencies] = useState<Currency[]>([]);
  const [timezones, setTimezones] = useState<TimeZone[]>([]);
  const [loadingData, setLoadingData] = useState(true);

  const form = useForm<SettingsFormData>({
    resolver: zodResolver(settingsSchema),
    defaultValues: {
      currency: settings?.currency || "USD",
      timeZone: settings?.timeZone || "UTC",
    },
  });

  // Load currencies and timezones
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoadingData(true);
        
        const [currenciesRes, timezonesRes] = await Promise.all([
          fetch("/api/settings?action=currencies"),
          fetch("/api/settings?action=timezones"),
        ]);

        if (currenciesRes.ok) {
          const currenciesData = await currenciesRes.json();
          setCurrencies(currenciesData.currencies);
        }

        if (timezonesRes.ok) {
          const timezonesData = await timezonesRes.json();
          setTimezones(timezonesData.timezones);
        }
      } catch (error) {
        console.error("Error loading settings data:", error);
      } finally {
        setLoadingData(false);
      }
    };

    loadData();
  }, []);

  // Update form when settings change
  useEffect(() => {
    if (settings) {
      form.reset({
        currency: settings.currency || "USD",
        timeZone: settings.timeZone || "UTC",
      });
    }
  }, [settings, form]);

  const onSubmit = async (data: SettingsFormData) => {
    try {
      await onSettingsUpdate({ ...data, tenantId });
    } catch (error) {
      console.error("Error updating settings:", error);
    }
  };

  const getCurrentCurrency = () => {
    return currencies.find(c => c.code === form.watch("currency"));
  };

  const getCurrentTimezone = () => {
    return timezones.find(tz => tz.value === form.watch("timeZone"));
  };

  if (loadingData) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin" />
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold flex items-center gap-2">
          <Settings className="h-6 w-6" />
          Settings
        </h2>
        <p className="text-muted-foreground">
          Configure your tenant preferences
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        {/* Settings Form */}
        <Card>
          <CardHeader>
            <CardTitle>General Settings</CardTitle>
            <CardDescription>
              Configure currency and timezone preferences
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                {/* Currency Selection */}
                <FormField
                  control={form.control}
                  name="currency"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <DollarSign className="h-4 w-4" />
                        Currency
                      </FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select currency" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {currencies.map((currency) => (
                            <SelectItem key={currency.code} value={currency.code}>
                              <div className="flex items-center gap-2">
                                <span className="font-mono">{currency.symbol}</span>
                                <span>{currency.code}</span>
                                <span className="text-muted-foreground">- {currency.name}</span>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        This will be used for all monetary displays
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Timezone Selection */}
                <FormField
                  control={form.control}
                  name="timeZone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-2">
                        <Clock className="h-4 w-4" />
                        Timezone
                      </FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select timezone" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {timezones.map((timezone) => (
                            <SelectItem key={timezone.value} value={timezone.value}>
                              {timezone.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        This will be used for all date and time displays
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Button type="submit" className="w-full" disabled={loading}>
                  {loading ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : null}
                  Update Settings
                </Button>
              </form>
            </Form>
          </CardContent>
        </Card>

        {/* Current Settings Display */}
        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                Current Settings
                {settings?.acceptedAt && (
                  <Badge variant="default">
                    <Check className="h-3 w-3 mr-1" />
                    Active
                  </Badge>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Currency Display */}
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-2">
                  <DollarSign className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">Currency</span>
                </div>
                <div className="text-right">
                  <div className="font-mono font-bold">
                    {getCurrentCurrency()?.symbol} {getCurrentCurrency()?.code}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {getCurrentCurrency()?.name}
                  </div>
                </div>
              </div>

              {/* Timezone Display */}
              <div className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">Timezone</span>
                </div>
                <div className="text-right">
                  <div className="font-medium">
                    {getCurrentTimezone()?.label}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {new Date().toLocaleTimeString([], {
                      timeZone: form.watch("timeZone"),
                      hour: '2-digit',
                      minute: '2-digit',
                    })}
                  </div>
                </div>
              </div>

              {/* Last Updated */}
              {settings?.createdAt && (
                <div className="text-sm text-muted-foreground text-center pt-2 border-t">
                  Last updated: {new Date(settings.createdAt).toLocaleDateString()}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Regional Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5" />
                Regional Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Date Format</span>
                <span className="font-medium">
                  {new Date().toLocaleDateString([], {
                    timeZone: form.watch("timeZone"),
                  })}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Time Format</span>
                <span className="font-medium">
                  {new Date().toLocaleTimeString([], {
                    timeZone: form.watch("timeZone"),
                  })}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Currency Symbol</span>
                <span className="font-medium font-mono">
                  {getCurrentCurrency()?.symbol}
                </span>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
