import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

// Types for different entity creation
export type EntityType = 
  | 'pricing-group'
  | 'class'
  | 'class-level'
  | 'class-category'
  | 'class-subcategory'
  | 'location'
  | 'equipment'
  | 'facility'
  | 'membership-plan'
  | 'waiver-form'
  | 'package'
  | 'package-category'
  | 'package-location'
  | 'package-pricing'
  | 'package-purchase-options'
  | 'package-customer-segments'
  | 'package-schedule-availability'
  | 'package-included-class'
  | 'package-customer-segments'
  | 'package-schedule-availability'

export interface InlineCreationConfig {
  entityType: EntityType;
  title: string;
  formComponent: React.ComponentType<any>;
  refetchHook?: () => void;
  onSuccess?: (newEntity: any) => void;
  tenantId?: number;
  additionalProps?: Record<string, any>;
}

export interface InlineCreationState {
  // Modal state
  isOpen: boolean;
  config: InlineCreationConfig | null;

  // Loading and error states
  isCreating: boolean;
  error: string | null;

  // Actions
  openModal: (config: InlineCreationConfig) => void;
  closeModal: () => void;
  setCreating: (isCreating: boolean) => void;
  setError: (error: string | null) => void;
  createEntity: (data: any, createMutation: any) => Promise<any>;
}

export const useInlineCreationStore = create<InlineCreationState>()(
  devtools(
    (set, get) => ({
      // Initial state
      isOpen: false,
      config: null,
      isCreating: false,
      error: null,

      // Open modal with configuration
      openModal: (config: InlineCreationConfig) => {
        console.log('InlineCreationStore: openModal called with config:', config);

        set({
          isOpen: true,
          config,
          error: null,
        });

        console.log('InlineCreationStore: Modal state updated, isOpen: true');
      },

      // Close modal and reset state
      closeModal: () => {
        set({
          isOpen: false,
          config: null,
          isCreating: false,
          error: null,
        });
      },

      // Set creating state
      setCreating: (isCreating: boolean) => {
        set({ isCreating });
      },

      // Set error state
      setError: (error: string | null) => {
        set({ error });
      },

      // Create entity using the provided mutation
      createEntity: async (data: any, createMutation: any) => {
        const { config } = get();
        if (!config) {
          throw new Error('No configuration found for entity creation');
        }

        try {
          set({ isCreating: true, error: null });

          // Create the entity using the provided mutation
          const newEntity = await createMutation.mutateAsync({
            ...data,
            tenantId: config.tenantId,
          });

          // Refetch data if refetch hook is provided
          if (config.refetchHook) {
            config.refetchHook();
          }

          // Call success callback if provided
          if (config.onSuccess) {
            config.onSuccess(newEntity);
          }

          // Close modal on success
          set({
            isOpen: false,
            config: null,
            isCreating: false,
            error: null,
          });

          return newEntity;
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to create entity';
          set({
            isCreating: false,
            error: errorMessage
          });
          throw error;
        }
      },
    }),
    {
      name: 'inline-creation-store',
    }
  )
);

// Utility function to get entity display name
export const getEntityDisplayName = (entityType: EntityType): string => {
  const displayNames: Record<EntityType, string> = {
    'pricing-group': 'Pricing Group',
    'class': 'Class',
    'class-level': 'Class Level',
    'class-category': 'Class Category',
    'class-subcategory': 'Class Subcategory',
    'location': 'Location',
    'equipment': 'Equipment',
    'facility': 'Facility',
    'membership-plan': 'Membership Plan',
    'waiver-form': 'Waiver Form',
    'package': 'Package',
    'package-category': 'Package Category',
    'package-location': 'Package Location',
    'package-pricing': 'Package Pricing',
    'package-purchase-options': 'Package Purchase Options',
    'package-customer-segments': 'Package Customer Segments',
    'package-schedule-availability': 'Package Schedule Availability',
    'package-included-class': 'Package Included Class',
  };
  
  return displayNames[entityType] || entityType;
};

// Utility function to get entity icon
export const getEntityIcon = (entityType: EntityType): string => {
  const icons: Record<EntityType, string> = {
    'pricing-group': 'Users',
    'class': 'Building',
    'class-level': 'GraduationCap',
    'class-category': 'FolderOpen',
    'class-subcategory': 'Grid3X3',
    'location': 'MapPin',
    'equipment': 'Wrench',
    'facility': 'Building',
    'membership-plan': 'CreditCard',
    'waiver-form': 'FileText',
    'package': 'Packages',
    'package-category': 'FolderOpen',
    'package-location': 'MapPin',
    'package-pricing': 'DollarSign',
    'package-purchase-options': 'ShoppingCart',
    'package-customer-segments': 'Users',
    'package-schedule-availability': 'Calendar',
    'package-included-class': 'MapPin'
  };
  
  return icons[entityType] || 'Plus';
};
