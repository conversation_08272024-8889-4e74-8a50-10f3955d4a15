import { db } from "../src/lib/db";
import { users } from "../src/lib/db/schema";
import { hash } from "bcryptjs";
import { createId } from "@paralleldrive/cuid2";

async function createTestUser() {
  try {
    const hashedPassword = await hash("password123", 12);
    
    const [user] = await db
      .insert(users)
      .values({
        id: createId(),
        email: "<EMAIL>",
        password: hashedPassword,
        name: "Test User",
        role: "user",
        emailVerified: new Date(),
      })
      .returning();

    console.log("Test user created successfully:");
    console.log("Email: <EMAIL>");
    console.log("Password: password123");
    console.log("User ID:", user.id);
  } catch (error) {
    console.error("Error creating test user:", error);
  }
}

createTestUser();
