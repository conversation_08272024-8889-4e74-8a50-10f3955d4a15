"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>bsTrigger } from "@/components/ui/tabs";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { CreditCard, MapPin, Settings } from "lucide-react";
import { MembershipPlansManagement } from "./membership-plans-management";
import { MembershipPlanLocationAssignment } from "./membership-plan-location-assignment";

interface MembershipPlansWithLocationsProps {
  tenantId: number;
}

export function MembershipPlansWithLocations({ tenantId }: MembershipPlansWithLocationsProps) {
  const [activeTab, setActiveTab] = useState("plans");

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Membership Plans</h1>
          <p className="text-muted-foreground">
            Manage your membership plans and location assignments
          </p>
        </div>
      </div>

      {/* Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="plans" className="flex items-center gap-2">
            <CreditCard className="h-4 w-4" />
            Plans Management
          </TabsTrigger>
          <TabsTrigger value="locations" className="flex items-center gap-2">
            <MapPin className="h-4 w-4" />
            Location Assignments
          </TabsTrigger>
        </TabsList>

        <TabsContent value="plans" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5" />
                Membership Plans
              </CardTitle>
            </CardHeader>
            <CardContent>
              <MembershipPlansManagement tenantId={tenantId} />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="locations" className="space-y-6">
          <MembershipPlanLocationAssignment tenantId={tenantId} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
