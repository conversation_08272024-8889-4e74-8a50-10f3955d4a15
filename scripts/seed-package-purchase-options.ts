// Script to seed package purchase options data
// Run with: npx tsx scripts/seed-package-purchase-options.ts

import { db } from "../src/lib/db";
import { packages, locations, package_purchase_options } from "../src/lib/db/schema";
import { createId } from "@paralleldrive/cuid2";

// Sample packages data (will be created if none exist)
const samplePackages = [
  {
    id: "akow8xayijki2t9ijx42k0dq", // Use existing ID
    tenantId: 1,
    name: "Basic Fitness Membership",
    description: "Entry-level fitness package with access to gym facilities",
    isActive: true,
    is_private: false,
    validity_duration: 30,
  },
  {
    id: "rgw47s1kb9qcexcniwedl7z1", // Use existing ID
    tenantId: 1,
    name: "Premium VIP Package",
    description: "Exclusive premium package with personal training and spa access",
    isActive: true,
    is_private: false,
    validity_duration: 90,
  },
  {
    id: createId(),
    tenantId: 1,
    name: "Trial Package",
    description: "7-day trial package for new customers",
    isActive: true,
    is_private: false,
    validity_duration: 7,
  },
  {
    id: createId(),
    tenantId: 1,
    name: "Student Discount Package",
    description: "Special discounted package for students",
    isActive: true,
    is_private: false,
    validity_duration: 30,
  },
  {
    id: createId(),
    tenantId: 1,
    name: "Corporate Wellness Package",
    description: "Bulk package for corporate wellness programs",
    isActive: true,
    is_private: true,
    validity_duration: 365,
  },
  {
    id: createId(),
    tenantId: 1,
    name: "Yoga & Meditation Package",
    description: "Specialized package for yoga and meditation classes",
    isActive: true,
    is_private: false,
    validity_duration: 60,
  },
  {
    id: createId(),
    tenantId: 1,
    name: "Family Package",
    description: "Family-friendly package for up to 4 members",
    isActive: true,
    is_private: false,
    validity_duration: 90,
  },
  {
    id: createId(),
    tenantId: 1,
    name: "Senior Citizen Package",
    description: "Special package designed for senior citizens",
    isActive: true,
    is_private: false,
    validity_duration: 30,
  }
];

// Sample locations data (will be created if none exist)
const sampleLocations = [
  {
    id: createId(),
    tenantId: 1,
    name: "Downtown Fitness Center",
    addressLine1: "123 Main Street",
    city: "New York",
    state: "NY",
    country: "United States",
    postalCode: "10001",
    phoneNumber: "******-0101",
  },
  {
    id: createId(),
    tenantId: 1,
    name: "Uptown Wellness Hub",
    addressLine1: "456 Broadway Avenue",
    city: "New York",
    state: "NY",
    country: "United States",
    postalCode: "10025",
    phoneNumber: "******-0102",
  },
  {
    id: createId(),
    tenantId: 1,
    name: "Brooklyn Sports Complex",
    addressLine1: "789 Atlantic Avenue",
    city: "Brooklyn",
    state: "NY",
    country: "United States",
    postalCode: "11217",
    phoneNumber: "******-0103",
  },
  {
    id: createId(),
    tenantId: 1,
    name: "Queens Community Center",
    addressLine1: "321 Northern Boulevard",
    city: "Queens",
    state: "NY",
    country: "United States",
    postalCode: "11354",
    phoneNumber: "******-0104",
  }
];

async function seedPackagePurchaseOptions() {
  console.log('🌱 Starting package purchase options seeding...');
  
  try {
    // Check and create packages if needed
    console.log('📦 Checking existing packages...');
    const existingPackages = await db.select().from(packages);
    
    let packageIds: string[] = [];
    
    if (existingPackages.length === 0) {
      console.log('📦 No packages found. Creating sample packages...');
      const insertedPackages = await db.insert(packages).values(samplePackages).returning();
      packageIds = insertedPackages.map(pkg => pkg.id);
      console.log(`✅ Created ${insertedPackages.length} packages`);
    } else {
      console.log(`📦 Found ${existingPackages.length} existing packages`);
      packageIds = existingPackages.map(pkg => pkg.id);
      
      // Add missing packages if we have fewer than expected
      const missingPackages = samplePackages.filter(sample => 
        !existingPackages.some(existing => existing.id === sample.id)
      );
      
      if (missingPackages.length > 0) {
        console.log(`📦 Adding ${missingPackages.length} missing packages...`);
        const insertedMissing = await db.insert(packages).values(missingPackages).returning();
        packageIds.push(...insertedMissing.map(pkg => pkg.id));
        console.log(`✅ Added ${insertedMissing.length} missing packages`);
      }
    }

    // Check and create locations if needed
    console.log('📍 Checking existing locations...');
    const existingLocations = await db.select().from(locations);
    
    let locationIds: string[] = [];
    
    if (existingLocations.length === 0) {
      console.log('📍 No locations found. Creating sample locations...');
      const insertedLocations = await db.insert(locations).values(sampleLocations).returning();
      locationIds = insertedLocations.map(loc => loc.id);
      console.log(`✅ Created ${insertedLocations.length} locations`);
    } else {
      console.log(`📍 Found ${existingLocations.length} existing locations`);
      locationIds = existingLocations.map(loc => loc.id);
      
      // Add missing locations if we have fewer than expected
      if (existingLocations.length < sampleLocations.length) {
        const missingLocations = sampleLocations.slice(existingLocations.length);
        console.log(`📍 Adding ${missingLocations.length} missing locations...`);
        const insertedMissing = await db.insert(locations).values(missingLocations).returning();
        locationIds.push(...insertedMissing.map(loc => loc.id));
        console.log(`✅ Added ${insertedMissing.length} missing locations`);
      }
    }

    // Check if package purchase options already exist
    console.log('🛒 Checking existing package purchase options...');
    const existingPurchaseOptions = await db.select().from(package_purchase_options);
    
    if (existingPurchaseOptions.length > 0) {
      console.log(`🛒 Found ${existingPurchaseOptions.length} existing package purchase options. Skipping seed.`);
      return;
    }

    // Create comprehensive package purchase options seed data
    console.log('🛒 Creating package purchase options seed data...');
    
    const packagePurchaseOptionsData = [
      // Basic Fitness Membership - Unlimited, transferable, online visible
      {
        package_id: packageIds[0], // Basic Fitness Membership
        purchase_limit: null, // Unlimited
        restrict_to: "all",
        transferable: true,
        specify_sold_at_location: false,
        sold_at_location_id: null,
        class_booking_limit: null, // Unlimited
        show_online: true,
      },
      
      // Premium VIP Package - Limited, non-transferable, location specific
      {
        package_id: packageIds[1], // Premium VIP Package
        purchase_limit: 50, // Limited to 50 customers
        restrict_to: "member_only",
        transferable: false,
        specify_sold_at_location: true,
        sold_at_location_id: locationIds[0], // Downtown Fitness Center only
        class_booking_limit: 20, // 20 classes max
        show_online: true,
      },
      
      // Trial Package - New customers only, limited quantity
      {
        package_id: packageIds[2], // Trial Package
        purchase_limit: 100, // Limited to 100 new customers
        restrict_to: "new_customers",
        transferable: false,
        specify_sold_at_location: false,
        sold_at_location_id: null,
        class_booking_limit: 3, // Only 3 trial classes
        show_online: true,
      },
      
      // Student Discount Package - Students only, transferable
      {
        package_id: packageIds[3], // Student Discount Package
        purchase_limit: 200,
        restrict_to: "new_customers", // Assuming students are new customers
        transferable: true,
        specify_sold_at_location: false,
        sold_at_location_id: null,
        class_booking_limit: 15,
        show_online: true,
      },
      
      // Corporate Wellness Package - Private, bulk purchase
      {
        package_id: packageIds[4], // Corporate Wellness Package
        purchase_limit: 10, // Only 10 corporate clients
        restrict_to: "existing_customers", // Existing corporate clients
        transferable: false,
        specify_sold_at_location: false,
        sold_at_location_id: null,
        class_booking_limit: null, // Unlimited for corporate
        show_online: false, // Private package
      },
      
      // Yoga & Meditation Package - Specific location, limited classes
      {
        package_id: packageIds[5], // Yoga & Meditation Package
        purchase_limit: 75,
        restrict_to: "all",
        transferable: true,
        specify_sold_at_location: true,
        sold_at_location_id: locationIds[1], // Uptown Wellness Hub
        class_booking_limit: 12, // 12 yoga sessions
        show_online: true,
      },
      
      // Family Package - Transferable within family, multiple locations
      {
        package_id: packageIds[6], // Family Package
        purchase_limit: 150,
        restrict_to: "all",
        transferable: true,
        specify_sold_at_location: false,
        sold_at_location_id: null,
        class_booking_limit: 40, // 40 classes for whole family
        show_online: true,
      },
      
      // Senior Citizen Package - Special pricing, specific location
      {
        package_id: packageIds[7], // Senior Citizen Package
        purchase_limit: 80,
        restrict_to: "existing_customers", // Verified seniors
        transferable: false,
        specify_sold_at_location: true,
        sold_at_location_id: locationIds[2], // Brooklyn Sports Complex
        class_booking_limit: 8, // 8 gentle classes
        show_online: true,
      },
    ];

    // Insert package purchase options
    const insertedPurchaseOptions = await db
      .insert(package_purchase_options)
      .values(packagePurchaseOptionsData)
      .returning();

    console.log(`✅ Successfully created ${insertedPurchaseOptions.length} package purchase options:`);
    
    // Display summary
    insertedPurchaseOptions.forEach((option, index) => {
      const packageName = samplePackages.find(pkg => pkg.id === option.package_id)?.name || 'Unknown Package';
      console.log(`   - ${packageName}:`);
      console.log(`     • Purchase Limit: ${option.purchase_limit || 'Unlimited'}`);
      console.log(`     • Restrict To: ${option.restrict_to}`);
      console.log(`     • Transferable: ${option.transferable ? 'Yes' : 'No'}`);
      console.log(`     • Location Specific: ${option.specify_sold_at_location ? 'Yes' : 'No'}`);
      console.log(`     • Class Booking Limit: ${option.class_booking_limit || 'Unlimited'}`);
      console.log(`     • Show Online: ${option.show_online ? 'Yes' : 'No'}`);
      console.log('');
    });

  } catch (error) {
    console.error('❌ Error seeding package purchase options:', error);
    throw error;
  }
  
  console.log('🌱 Package purchase options seeding completed successfully!');
}

// Run the seeding function
if (require.main === module) {
  seedPackagePurchaseOptions()
    .then(() => {
      console.log('✅ Seeding completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Seeding failed:', error);
      process.exit(1);
    });
}

export { seedPackagePurchaseOptions };
