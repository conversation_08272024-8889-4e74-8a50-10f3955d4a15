"use client";

import { useState, useEffect } from "react";
import { Tenant, NewTenant } from "@/lib/db/schema";

interface UseTenantOptions {
  organizationId?: string;
  search?: string;
}

interface UseTenantReturn {
  tenants: Tenant[];
  loading: boolean;
  error: string | null;
  createTenant: (data: Omit<NewTenant, "id" | "createdAt" | "updatedAt">) => Promise<Tenant>;
  updateTenant: (id: number, data: Partial<NewTenant>) => Promise<Tenant>;
  deleteTenant: (id: number) => Promise<void>;
  checkSubdomain: (subdomain: string) => Promise<{ available: boolean; suggestion?: string }>;
  refreshTenants: () => Promise<void>;
}

export function useTenants(options: UseTenantOptions = {}): UseTenantReturn {
  const [tenants, setTenants] = useState<Tenant[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchTenants = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams();
      if (options.organizationId) {
        params.append("organizationId", options.organizationId);
      }
      if (options.search) {
        params.append("search", options.search);
      }

      const response = await fetch(`/api/tenants?${params}`);
      if (!response.ok) {
        throw new Error("Failed to fetch tenants");
      }

      const data = await response.json();
      setTenants(data.tenants);
    } catch (err) {
      setError(err instanceof Error ? err.message : "An error occurred");
    } finally {
      setLoading(false);
    }
  };

  const createTenant = async (data: Omit<NewTenant, "id" | "createdAt" | "updatedAt">): Promise<Tenant> => {
    const response = await fetch("/api/tenants", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to create tenant");
    }

    const result = await response.json();
    await fetchTenants(); // Refresh the list
    return result.tenant;
  };

  const updateTenant = async (id: number, data: Partial<NewTenant>): Promise<Tenant> => {
    const response = await fetch(`/api/tenants/${id}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to update tenant");
    }

    const result = await response.json();
    await fetchTenants(); // Refresh the list
    return result.tenant;
  };

  const deleteTenant = async (id: number): Promise<void> => {
    const response = await fetch(`/api/tenants/${id}`, {
      method: "DELETE",
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || "Failed to delete tenant");
    }

    await fetchTenants(); // Refresh the list
  };

  const checkSubdomain = async (subdomain: string): Promise<{ available: boolean; suggestion?: string }> => {
    const response = await fetch(`/api/tenants/check-subdomain?subdomain=${encodeURIComponent(subdomain)}`);
    
    if (!response.ok) {
      throw new Error("Failed to check subdomain");
    }

    const data = await response.json();
    return {
      available: data.available,
      suggestion: data.suggestion,
    };
  };

  const refreshTenants = async () => {
    await fetchTenants();
  };

  useEffect(() => {
    fetchTenants();
  }, [options.organizationId, options.search]);

  return {
    tenants,
    loading,
    error,
    createTenant,
    updateTenant,
    deleteTenant,
    checkSubdomain,
    refreshTenants,
  };
}

// Hook for single tenant
export function useTenant(id: number) {
  const [tenant, setTenant] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTenant = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch(`/api/tenants/${id}`);
        if (!response.ok) {
          throw new Error("Failed to fetch tenant");
        }

        const data = await response.json();
        setTenant(data.tenant);
      } catch (err) {
        setError(err instanceof Error ? err.message : "An error occurred");
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchTenant();
    }
  }, [id]);

  return { tenant, loading, error };
}
