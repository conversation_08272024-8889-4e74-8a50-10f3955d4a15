"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { 
  GraduationCap, 
  Plus, 
  Building, 
  BarChart3,
  AlertCircle,
  CheckCircle,
  Eye,
  EyeOff
} from "lucide-react";
import { ClassLevelForm } from "@/components/forms/class-level-form";
import { ClassLevelList } from "@/components/class-levels/class-level-list";
import { SuccessToast } from "@/components/ui/success-toast";
import { SuccessModal } from "@/components/ui/success-modal";
import { Confetti } from "@/components/ui/confetti";
import { ClassLevel, NewClassLevel } from "@/lib/db/schema";
import { 
  useClassLevels, 
  useCreateClassLevel, 
  useUpdateClassLevel,
  useClassLevelStats
} from "@/lib/hooks/queries/use-class-level-queries";
import { useTenants } from "@/lib/hooks/queries/use-tenant-queries";

export default function ClassLevelsPage() {
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingClassLevel, setEditingClassLevel] = useState<ClassLevel | null>(null);
  const [selectedTenantId, setSelectedTenantId] = useState<number | null>(null);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [submitSuccess, setSubmitSuccess] = useState<string | null>(null);
  
  // Animation states
  const [showSuccessToast, setShowSuccessToast] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showConfetti, setShowConfetti] = useState(false);
  const [createdClassLevel, setCreatedClassLevel] = useState<ClassLevel | null>(null);

  const { data: classLevels = [], isLoading: classLevelsLoading } = useClassLevels();
  const { data: tenantsData, isLoading: tenantsLoading } = useTenants();
  const { data: stats, isLoading: statsLoading } = useClassLevelStats();
  const tenants = tenantsData?.tenants || [];
  const createClassLevelMutation = useCreateClassLevel();
  const updateClassLevelMutation = useUpdateClassLevel();

  // Calculate stats by tenant
  const statsByTenant = tenants.reduce((acc, tenant) => {
    const tenantClassLevels = classLevels.filter(cl => cl.tenantId === tenant.id);
    acc[tenant.id] = {
      total: tenantClassLevels.length,
      active: tenantClassLevels.filter(cl => cl.isActive).length,
      inactive: tenantClassLevels.filter(cl => !cl.isActive).length,
    };
    return acc;
  }, {} as Record<number, { total: number; active: number; inactive: number }>);

  const handleCreateClassLevel = async (data: Omit<NewClassLevel, "id" | "createdAt" | "updatedAt">) => {
    try {
      setSubmitError(null);
      const newClassLevel = await createClassLevelMutation.mutateAsync(data);
      
      // Store created class level for modal
      setCreatedClassLevel(newClassLevel);
      
      // Close form first
      setIsFormOpen(false);
      
      // Start celebration sequence
      setTimeout(() => {
        setShowConfetti(true);
        setShowSuccessToast(true);
        setShowSuccessModal(true);
      }, 300);
      
      // Auto-hide confetti
      setTimeout(() => setShowConfetti(false), 4000);
      
    } catch (error) {
      setSubmitError(error instanceof Error ? error.message : "Failed to create class level");
    }
  };

  const handleUpdateClassLevel = async (data: Omit<NewClassLevel, "id" | "createdAt" | "updatedAt">) => {
    if (!editingClassLevel) return;

    try {
      setSubmitError(null);
      await updateClassLevelMutation.mutateAsync({ 
        classLevelId: editingClassLevel.id, 
        data: data
      });
      setSubmitSuccess("Class level updated successfully!");
      setEditingClassLevel(null);
      setTimeout(() => setSubmitSuccess(null), 3000);
    } catch (error) {
      setSubmitError(error instanceof Error ? error.message : "Failed to update class level");
    }
  };

  const handleEdit = (classLevel: ClassLevel) => {
    setEditingClassLevel(classLevel);
  };

  const handleAdd = () => {
    setEditingClassLevel(null);
    setIsFormOpen(true);
  };

  const handleCloseForm = () => {
    setIsFormOpen(false);
    setEditingClassLevel(null);
    setSubmitError(null);
  };

  const handleCloseSuccessAnimations = () => {
    setShowSuccessToast(false);
    setShowSuccessModal(false);
    setShowConfetti(false);
    setCreatedClassLevel(null);
  };

  const handleViewClassLevel = () => {
    handleCloseSuccessAnimations();
    // Could navigate to class level detail or highlight in list
  };

  const handleAddAnother = () => {
    handleCloseSuccessAnimations();
    setIsFormOpen(true);
  };

  if (classLevelsLoading || tenantsLoading || statsLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Class Levels</h1>
          <p className="text-gray-500 dark:text-gray-400">
            Manage class levels and their ordering
          </p>
        </div>
        <Button onClick={handleAdd}>
          <Plus className="h-4 w-4 mr-2" />
          Add Class Level
        </Button>
      </div>

      {/* Success/Error Messages */}
      {submitSuccess && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>{submitSuccess}</AlertDescription>
        </Alert>
      )}

      {submitError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{submitError}</AlertDescription>
        </Alert>
      )}

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Class Levels</CardTitle>
            <GraduationCap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.total || 0}</div>
            <p className="text-xs text-muted-foreground">
              Across all tenants
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Levels</CardTitle>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.active || 0}</div>
            <p className="text-xs text-muted-foreground">
              Currently available
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Inactive Levels</CardTitle>
            <EyeOff className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.inactive || 0}</div>
            <p className="text-xs text-muted-foreground">
              Currently hidden
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Tenants</CardTitle>
            <Building className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Object.values(statsByTenant).filter(stat => stat.total > 0).length}
            </div>
            <p className="text-xs text-muted-foreground">
              With class levels
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="all" className="space-y-4">
        <TabsList>
          <TabsTrigger value="all">All Class Levels</TabsTrigger>
          {tenants.map((tenant) => {
            const tenantStats = statsByTenant[tenant.id];
            return (
              <TabsTrigger key={tenant.id} value={`tenant-${tenant.id}`}>
                {tenant.name}
                <Badge variant="secondary" className="ml-2">
                  {tenantStats?.total || 0}
                </Badge>
              </TabsTrigger>
            );
          })}
        </TabsList>

        <TabsContent value="all">
          <ClassLevelList
            onEdit={handleEdit}
            onAdd={handleAdd}
            showActions={true}
          />
        </TabsContent>

        {tenants.map((tenant) => (
          <TabsContent key={tenant.id} value={`tenant-${tenant.id}`}>
            <ClassLevelList
              tenantId={tenant.id}
              onEdit={handleEdit}
              onAdd={() => {
                setSelectedTenantId(tenant.id);
                handleAdd();
              }}
              showActions={true}
            />
          </TabsContent>
        ))}
      </Tabs>

      {/* Create/Edit Dialog */}
      <Dialog open={isFormOpen || !!editingClassLevel} onOpenChange={handleCloseForm}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editingClassLevel ? "Edit Class Level" : "Add New Class Level"}
            </DialogTitle>
          </DialogHeader>
          <ClassLevelForm
            classLevel={editingClassLevel || undefined}
            tenantId={selectedTenantId || editingClassLevel?.tenantId || (tenants[0]?.id ?? 1)}
            onSubmit={editingClassLevel ? handleUpdateClassLevel : handleCreateClassLevel}
            onCancel={handleCloseForm}
            className="border-0 shadow-none"
          />
        </DialogContent>
      </Dialog>

      {/* Success Animations */}
      <Confetti isActive={showConfetti} />
      
      <SuccessToast
        isVisible={showSuccessToast}
        onClose={() => setShowSuccessToast(false)}
        title="Class Level Created Successfully!"
        description={createdClassLevel ? 
          `${createdClassLevel.name} has been added to your class levels.` : 
          undefined
        }
        type="general"
        action={{
          label: "View All Class Levels",
          onClick: handleViewClassLevel
        }}
      />

      <SuccessModal
        isOpen={showSuccessModal}
        onClose={handleCloseSuccessAnimations}
        title="🎉 Class Level Created Successfully!"
        description="Your new class level has been saved and is now available for use."
        locationData={createdClassLevel ? {
          address: createdClassLevel.name || "",
          city: createdClassLevel.description || "Class Level",
          country: `Status: ${createdClassLevel.isActive ? 'Active' : 'Inactive'}`
        } : undefined}
        actions={{
          primary: {
            label: "Add Another Class Level",
            onClick: handleAddAnother
          },
          secondary: {
            label: "View All Class Levels",
            onClick: handleViewClassLevel
          }
        }}
      />
    </div>
  );
}
