"use client";

import { useQuery, useMutation, useQueryClient, useInfiniteQuery } from "@tanstack/react-query";
import { Organization, NewOrganization } from "@/lib/db/schema";

// Import global query keys
import { organizationKeys } from "./query-keys";

// API Functions
const organizationApi = {
  getById: async (id: number): Promise<Organization> => {
    const response = await fetch(`/api/organizations/${id}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch organization: ${response.statusText}`);
    }
    return response.json();
  },

  getAll: async (filters?: {
    search?: string;
    status?: 'active' | 'inactive';
    page?: number;
    limit?: number;
  }): Promise<{ organizations: Organization[]; total: number; page: number; limit: number }> => {
    const params = new URLSearchParams();
    if (filters?.search) params.append('search', filters.search);
    if (filters?.status) params.append('status', filters.status);
    if (filters?.page) params.append('page', filters.page.toString());
    if (filters?.limit) params.append('limit', filters.limit.toString());

    const response = await fetch(`/api/organizations?${params}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch organizations: ${response.statusText}`);
    }
    return response.json();
  },

  create: async (data: Omit<NewOrganization, "id" | "createdAt" | "updatedAt">): Promise<Organization> => {
    const response = await fetch('/api/organizations', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to create organization');
    }
    return response.json();
  },

  update: async ({ id, data }: { id: number; data: Partial<NewOrganization> }): Promise<Organization> => {
    const response = await fetch(`/api/organizations/${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to update organization');
    }
    return response.json();
  },

  delete: async (id: number): Promise<void> => {
    const response = await fetch(`/api/organizations/${id}`, {
      method: 'DELETE',
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to delete organization');
    }
  },

  getMembers: async (id: number) => {
    const response = await fetch(`/api/organizations/${id}/members`);
    if (!response.ok) {
      throw new Error('Failed to fetch organization members');
    }
    return response.json();
  },

  addMember: async ({ organizationId, userId, role }: { organizationId: number; userId: string; role: string }) => {
    const response = await fetch(`/api/organizations/${organizationId}/members`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ userId, role }),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to add member');
    }
    return response.json();
  },

  removeMember: async ({ organizationId, userId }: { organizationId: number; userId: string }) => {
    const response = await fetch(`/api/organizations/${organizationId}/members/${userId}`, {
      method: 'DELETE',
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to remove member');
    }
  },

  updateMemberRole: async ({ organizationId, userId, role }: { organizationId: number; userId: string; role: string }) => {
    const response = await fetch(`/api/organizations/${organizationId}/members/${userId}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ role }),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to update member role');
    }
    return response.json();
  },

  getTenants: async (id: number) => {
    const response = await fetch(`/api/organizations/${id}/tenants`);
    if (!response.ok) {
      throw new Error('Failed to fetch organization tenants');
    }
    return response.json();
  },

  getBilling: async (id: number) => {
    const response = await fetch(`/api/organizations/${id}/billing`);
    if (!response.ok) {
      throw new Error('Failed to fetch organization billing');
    }
    return response.json();
  },

  getStats: async (): Promise<{
    totalOrganizations: number;
    activeOrganizations: number;
    totalMembers: number;
    totalTenants: number;
  }> => {
    const response = await fetch('/api/organizations/stats');
    if (!response.ok) {
      throw new Error('Failed to fetch organization stats');
    }
    return response.json();
  },
};

// Query Hooks
export function useOrganization(id: number) {
  return useQuery({
    queryKey: organizationKeys.detail(id),
    queryFn: () => organizationApi.getById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useOrganizations(filters?: {
  search?: string;
  status?: 'active' | 'inactive';
}) {
  return useQuery({
    queryKey: organizationKeys.list(filters || {}),
    queryFn: () => organizationApi.getAll(filters),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

export function useInfiniteOrganizations(filters?: {
  search?: string;
  status?: 'active' | 'inactive';
}) {
  return useInfiniteQuery({
    queryKey: [...organizationKeys.list(filters || {}), 'infinite'],
    queryFn: ({ pageParam = 1 }) => organizationApi.getAll({ ...filters, page: pageParam, limit: 20 }),
    getNextPageParam: (lastPage) => {
      const hasMore = lastPage.organizations.length === lastPage.limit;
      return hasMore ? lastPage.page + 1 : undefined;
    },
    initialPageParam: 1,
  });
}

export function useOrganizationMembers(id: number) {
  return useQuery({
    queryKey: organizationKeys.members(id),
    queryFn: () => organizationApi.getMembers(id),
    enabled: !!id,
    staleTime: 2 * 60 * 1000,
  });
}

export function useOrganizationTenants(id: number) {
  return useQuery({
    queryKey: organizationKeys.tenants(id),
    queryFn: () => organizationApi.getTenants(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
}

export function useOrganizationBilling(id: number) {
  return useQuery({
    queryKey: organizationKeys.billing(id),
    queryFn: () => organizationApi.getBilling(id),
    enabled: !!id,
    staleTime: 1 * 60 * 1000, // 1 minute for billing data
  });
}

export function useOrganizationStats() {
  return useQuery({
    queryKey: organizationKeys.stats(),
    queryFn: organizationApi.getStats,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Mutation Hooks
export function useCreateOrganization() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: organizationApi.create,
    onSuccess: (newOrganization) => {
      queryClient.invalidateQueries({ queryKey: organizationKeys.all });
      queryClient.setQueryData(organizationKeys.detail(newOrganization.id), newOrganization);
    },
  });
}

export function useUpdateOrganization() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: organizationApi.update,
    onMutate: async ({ id, data }) => {
      await queryClient.cancelQueries({ queryKey: organizationKeys.detail(id) });
      const previousOrganization = queryClient.getQueryData(organizationKeys.detail(id));
      
      queryClient.setQueryData(organizationKeys.detail(id), (old: Organization | undefined) => {
        if (!old) return old;
        return { ...old, ...data, updatedAt: new Date() };
      });

      return { previousOrganization, id };
    },
    onError: (err, variables, context) => {
      if (context?.previousOrganization) {
        queryClient.setQueryData(organizationKeys.detail(context.id), context.previousOrganization);
      }
    },
    onSettled: (data, error, variables) => {
      queryClient.invalidateQueries({ queryKey: organizationKeys.detail(variables.id) });
      queryClient.invalidateQueries({ queryKey: organizationKeys.lists() });
    },
  });
}

export function useDeleteOrganization() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: organizationApi.delete,
    onSuccess: (_, id) => {
      queryClient.removeQueries({ queryKey: organizationKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: organizationKeys.lists() });
      queryClient.invalidateQueries({ queryKey: organizationKeys.stats() });
    },
  });
}

export function useAddOrganizationMember() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: organizationApi.addMember,
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: organizationKeys.members(variables.organizationId) });
      queryClient.invalidateQueries({ queryKey: organizationKeys.stats() });
    },
  });
}

export function useRemoveOrganizationMember() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: organizationApi.removeMember,
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: organizationKeys.members(variables.organizationId) });
      queryClient.invalidateQueries({ queryKey: organizationKeys.stats() });
    },
  });
}

export function useUpdateOrganizationMemberRole() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: organizationApi.updateMemberRole,
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: organizationKeys.members(variables.organizationId) });
    },
  });
}
