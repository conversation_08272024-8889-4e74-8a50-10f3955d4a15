import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { packagePurchaseOptionsService } from "@/lib/services/package-purchase-options.service";

/**
 * GET /api/package-purchase-options/with-details/[packageId]
 * Get package purchase options with package and location details
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ packageId: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { packageId } = await params;
    console.log(`📦 [GET /api/package-purchase-options/with-details/${packageId}] Fetching package purchase options with details`);

    const packagePurchaseOptionsWithDetails = await packagePurchaseOptionsService.getWithDetails(packageId);

    return NextResponse.json({
      success: true,
      data: packagePurchaseOptionsWithDetails
    });
  } catch (error) {
    console.error(`📦 [GET /api/package-purchase-options/with-details/${params}] Error:`, error);
    return NextResponse.json(
      { error: "Failed to fetch package purchase options with details" },
      { status: 500 }
    );
  }
}
