"use client";

import { useState, useEffect, memo } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { motion, AnimatePresence } from "framer-motion";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { SelectWithInlineCreation, mapToSelectOptions } from "@/components/ui/select-with-inline-creation";
import { Checkbox } from "@/components/ui/checkbox";
import { <PERSON>ader2, <PERSON><PERSON><PERSON>, CheckCircle, ImageIcon, ChevronDown, ChevronUp, Plus } from "lucide-react";
import { Class } from "@/lib/db/schema";
import { useClassCategoriesByTenant } from "@/lib/hooks/queries/use-class-category-queries";
import { useClassSubcategoriesByCategory } from "@/lib/hooks/queries/use-class-subcategory-queries";
import { useClassLevelsByTenant } from "@/lib/hooks/queries/use-class-level-queries";
import { useLocations } from "@/lib/hooks/queries/use-location-queries";
import { usePackagePricingByTenant } from "@/lib/hooks/queries/use-package-pricing-queries";
import { useMembershipPlans } from "@/lib/hooks/queries/use-membership-plan-queries";
import { Badge } from "@/components/ui/badge";
import { ImageUpload } from "@/components/ui/image-upload";
import { Trash2, Package, Youtube } from "lucide-react";
import { createId } from "@paralleldrive/cuid2";


const classSchema = z.object({
  tenantId: z.number().int().positive(),
  name: z.string().min(1, "Name is required").max(255),
  description: z.string().max(255).optional(),
  categoryId: z.string().min(1, "Category is required"),
  subcategoryId: z.string().optional(),
  duration_value: z.number().int().positive().optional(),
  duration_unit: z.string().optional(),
  level_id: z.string().optional(),
  delivery_mode: z.string().optional(),
  is_private: z.boolean().optional(),
  custom_cancellation_policy: z.boolean().optional(),
  cancellation_policy_description: z.string().max(255).optional(),
  is_active: z.boolean().optional(),
  location_id: z.string().optional(),
  images: z.array(z.string()).optional(),
  items_to_bring: z.array(z.object({
    id: z.string(),
    item_name: z.string().min(1, "Item name is required"),
    is_required: z.boolean(),
  })).optional(),
  youtube_links: z.array(z.object({
    id: z.string(),
    yt_url: z.string().url("Please enter a valid YouTube URL"),
  })).optional(),
  package_pricing_ids: z.array(z.string()).optional(),
  membership_plan_ids: z.array(z.string()).optional(),
});

type ClassFormData = z.infer<typeof classSchema>;

// Memoized Package Pricing Card Component
const PackagePricingCard = memo(({
  pricing,
  isSelected,
  onToggle
}: {
  pricing: any;
  isSelected: boolean;
  onToggle: (id: string) => void;
}) => {
  return (
    <div
      className={`border rounded-lg p-3 cursor-pointer transition-all duration-200 ${
        isSelected
          ? 'border-green-500 bg-green-50 shadow-sm'
          : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
      }`}
      onClick={(e) => {
        e.preventDefault();
        e.stopPropagation();
        onToggle(pricing.id);
      }}
    >
      <div className="flex items-center space-x-3">
        <Checkbox
          checked={isSelected}
          className="pointer-events-none"
        />
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium text-gray-900">
            {pricing.package?.name || `Package ${pricing.package_id}`}
          </p>
          <div className="flex items-center gap-2 text-xs text-gray-500">
            {pricing.price && (
              <span>{pricing.currency || 'USD'} {pricing.price}</span>
            )}
            {pricing.credit_amount && (
              <span>• {pricing.credit_amount} credits</span>
            )}
            {pricing.pricing_group?.name && (
              <span>• {pricing.pricing_group.name}</span>
            )}
          </div>
          {pricing.package?.description && (
            <p className="text-xs text-gray-400 truncate mt-1">
              {pricing.package.description}
            </p>
          )}
        </div>
      </div>
    </div>
  );
});

// Memoized Membership Plan Card Component
const MembershipPlanCard = memo(({
  plan,
  isSelected,
  onToggle
}: {
  plan: any;
  isSelected: boolean;
  onToggle: (id: string) => void;
}) => {
  return (
    <div
      className={`border rounded-lg p-3 cursor-pointer transition-all duration-200 ${
        isSelected
          ? 'border-blue-500 bg-blue-50 shadow-sm'
          : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
      }`}
      onClick={(e) => {
        e.preventDefault();
        e.stopPropagation();
        onToggle(plan.id);
      }}
    >
      <div className="flex items-center space-x-3">
        <Checkbox
          checked={isSelected}
          className="pointer-events-none"
        />
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium text-gray-900">
            {plan.name}
          </p>
          <div className="flex items-center gap-2 text-xs text-gray-500">
            {plan.price && (
              <span>{plan.currency || 'USD'} {plan.price}</span>
            )}
            {plan.duration_value && plan.duration_unit && (
              <span>• {plan.duration_value} {plan.duration_unit}</span>
            )}
          </div>
          {plan.description && (
            <p className="text-xs text-gray-400 truncate mt-1">
              {plan.description}
            </p>
          )}
        </div>
      </div>
    </div>
  );
});

interface ClassFormProps {
  class?: Class;
  tenantId: number;
  onSubmit: (data: ClassFormData) => Promise<Class | void>;
  onCancel?: () => void;
  isLoading?: boolean;
  className?: string;
}

/**
 * ClassForm Component
 * 
 * Form untuk create/edit class dengan:
 * - Category dropdown (wajib)
 * - Subcategory dropdown (optional, filtered by category)
 * - Basic class info (name, description)
 * - Duration settings
 * - Delivery mode
 * - Privacy settings
 * 
 * Pattern ini sama dengan ClassCategoryForm dan ClassSubcategoryForm.
 */
export function ClassForm({ 
  class: classData, 
  tenantId, 
  onSubmit, 
  onCancel, 
  isLoading = false,
  className = "" 
}: ClassFormProps) {
  const [isSuccess, setIsSuccess] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [selectedCategoryId, setSelectedCategoryId] = useState<string>("");
  const [classImages, setClassImages] = useState<string[]>(classData?.images || []);
  const [selectedPackagePricings, setSelectedPackagePricings] = useState<string[]>([]);
  const [selectedMembershipPlans, setSelectedMembershipPlans] = useState<string[]>(classData?.membership_plan_ids || []);
  const [itemsToBring, setItemsToBring] = useState<Array<{id: string, item_name: string, is_required: boolean}>>(
    classData?.items_to_bring || []
  );
  const [youtubeLinks, setYoutubeLinks] = useState<Array<{id: string, yt_url: string}>>(
    classData?.youtube_links || []
  );

  // Get data for dropdowns
  const { data: categories = [], refetch: refetchCategories } = useClassCategoriesByTenant(tenantId);
  const { data: subcategories = [], refetch: refetchSubcategories } = useClassSubcategoriesByCategory(selectedCategoryId, tenantId);
  const { data: classLevels = [], refetch: refetchClassLevels } = useClassLevelsByTenant(tenantId, true); // Only active levels
  const { data: locations = [], refetch: refetchLocations } = useLocations({ tenantId });
  const { data: packagePricings = [] } = usePackagePricingByTenant(tenantId);
  const { data: membershipPlans = [] } = useMembershipPlans(tenantId);





  // Update images when classData changes
  useEffect(() => {
    if (classData?.images) {
      setClassImages(classData.images);
    }
  }, [classData]);

  // Update items to bring when classData changes
  useEffect(() => {
    if (classData?.items_to_bring) {
      setItemsToBring(classData.items_to_bring);
    }
  }, [classData]);

  // Update YouTube links when classData changes
  useEffect(() => {
    if (classData?.youtube_links) {
      setYoutubeLinks(classData.youtube_links);
    }
  }, [classData]);

  // Package pricing relationships will be handled separately

  // Load existing membership plan relationships for edit mode
  useEffect(() => {
    if (classData?.membership_plan_ids) {
      setSelectedMembershipPlans(classData.membership_plan_ids);
    }
  }, [classData?.membership_plan_ids]);

  const form = useForm<ClassFormData>({
    resolver: zodResolver(classSchema),
    defaultValues: {
      tenantId,
      name: classData?.name || "",
      description: classData?.description || "",
      categoryId: classData?.categoryId || "",
      subcategoryId: classData?.subcategoryId || undefined,
      duration_value: classData?.duration_value || undefined,
      duration_unit: classData?.duration_unit || undefined,
      level_id: classData?.level_id || undefined,
      delivery_mode: classData?.delivery_mode || undefined,
      is_private: classData?.is_private || false,
      custom_cancellation_policy: classData?.custom_cancellation_policy || false,
      cancellation_policy_description: classData?.cancellation_policy_description || undefined,
      is_active: classData?.is_active !== undefined ? classData.is_active : true,
      location_id: classData?.location_id || undefined,
      images: classData?.images || [],
      items_to_bring: classData?.items_to_bring || [],
      youtube_links: classData?.youtube_links || [],
      package_pricing_ids: selectedPackagePricings,
      membership_plan_ids: selectedMembershipPlans,
    },
  });

  // Initialize selectedCategoryId from form data
  useEffect(() => {
    if (classData?.categoryId && !selectedCategoryId) {
      setSelectedCategoryId(classData.categoryId);
    }
  }, [classData?.categoryId, selectedCategoryId]);

  // Reset subcategory when category changes
  useEffect(() => {
    if (selectedCategoryId) {
      // Reset subcategory when category changes
      form.setValue("subcategoryId", undefined);
    }
  }, [selectedCategoryId, form]);

  // Reset form errors when form data changes (removed to prevent infinite loop)
  // submitError will be reset in form submission handler

  // Handle image upload
  const handleImageUpload = (url: string) => {
    setClassImages(prev => [...prev, url]);
    // Update form data
    form.setValue("images", [...classImages, url]);
  };

  // Handle image deletion
  const handleImageDelete = (imageUrl: string) => {
    const updatedImages = classImages.filter(url => url !== imageUrl);
    setClassImages(updatedImages);
    // Update form data
    form.setValue("images", updatedImages);
  };

  // Handle package pricing selection with debounce
  const [isToggling, setIsToggling] = useState(false);

  const handlePackagePricingToggle = (packagePricingId: string) => {
    if (isToggling) return;

    setIsToggling(true);

    setSelectedPackagePricings(prev => {
      const isSelected = prev.includes(packagePricingId);
      const updated = isSelected
        ? prev.filter(id => id !== packagePricingId)
        : [...prev, packagePricingId];

      // Reset toggle flag after state update
      setTimeout(() => setIsToggling(false), 100);

      return updated;
    });
  };

  // Handle membership plan selection with debounce
  const [isMembershipToggling, setIsMembershipToggling] = useState(false);

  const handleMembershipPlanToggle = (membershipPlanId: string) => {
    if (isMembershipToggling) return;

    setIsMembershipToggling(true);

    setSelectedMembershipPlans(prev => {
      const isSelected = prev.includes(membershipPlanId);
      const updated = isSelected
        ? prev.filter(id => id !== membershipPlanId)
        : [...prev, membershipPlanId];

      // Reset toggle flag after state update
      setTimeout(() => setIsMembershipToggling(false), 100);

      return updated;
    });
  };

  // Handle items to bring management
  const addItemToBring = () => {
    const newItem = {
      id: createId(), // Unique ID for new items
      item_name: "",
      is_required: false
    };
    setItemsToBring(prev => [...prev, newItem]);
  };

  const updateItemToBring = (index: number, field: 'item_name' | 'is_required', value: string | boolean) => {
    setItemsToBring(prev => prev.map((item, i) =>
      i === index ? { ...item, [field]: value } : item
    ));
  };

  // All inline creation handlers are now handled by the global Zustand store



  const removeItemToBring = (index: number) => {
    setItemsToBring(prev => prev.filter((_, i) => i !== index));
  };

  // Handle YouTube links management
  const addYoutubeLink = () => {
    const newLink = {
      id: createId(),
      yt_url: ""
    };
    setYoutubeLinks(prev => [...prev, newLink]);
  };

  const updateYoutubeLink = (index: number, value: string) => {
    setYoutubeLinks(prev => prev.map((link, i) =>
      i === index ? { ...link, yt_url: value } : link
    ));
  };

  const removeYoutubeLink = (index: number) => {
    setYoutubeLinks(prev => prev.filter((_, i) => i !== index));
  };

  const handleSubmit = async (data: ClassFormData) => {
    try {
      setSubmitError(null);

      // Include images, items to bring, YouTube links, package pricing IDs, and membership plan IDs in the submission data
      const submissionData = {
        ...data,
        images: classImages,
        items_to_bring: itemsToBring.filter(item => item.item_name.trim() !== ''), // Only include items with names
        youtube_links: youtubeLinks.filter(link => link.yt_url.trim() !== ''), // Only include links with URLs
        package_pricing_ids: selectedPackagePricings,
        membership_plan_ids: selectedMembershipPlans,
      };

      await onSubmit(submissionData);
      setIsSuccess(true);
      setTimeout(() => setIsSuccess(false), 2000);
    } catch (error) {
      console.error("Form submission error:", error);

      // Handle specific validation errors
      let errorMessage = "An error occurred";
      if (error instanceof Error) {
        if (error.message.includes("Category") && error.message.includes("not found")) {
          errorMessage = "Selected category is invalid. Please select a valid category.";
        } else if (error.message.includes("Subcategory") && error.message.includes("not found")) {
          errorMessage = "Selected subcategory is invalid. Please select a valid subcategory or leave it empty.";
        } else if (error.message.includes("Class level") && error.message.includes("not found")) {
          errorMessage = "Selected class level is invalid. Please select a valid level or leave it empty.";
        } else if (error.message.includes("Location") && error.message.includes("not found")) {
          errorMessage = "Selected location is invalid. Please select a valid location or leave it empty.";
        } else {
          errorMessage = error.message;
        }
      }

      setSubmitError(errorMessage);
    }
  };

  const isFormLoading = isLoading || form.formState.isSubmitting;

  return (
    <Card className={`w-full max-w-2xl mx-auto ${className}`}>
      <CardHeader className="text-center">
        <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
          <BookOpen className="h-6 w-6 text-blue-600" />
        </div>
        <CardTitle className="text-xl font-semibold">
          {classData ? "Edit Class" : "Create Class"}
        </CardTitle>
        <CardDescription>
          {classData ? "Update the class details" : "Add a new class to your offerings"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          {/* Success Alert */}
          {isSuccess && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="mb-4"
            >
              <Alert className="border-green-200 bg-green-50">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <AlertDescription className="text-green-800">
                  Class {classData ? "updated" : "created"} successfully!
                </AlertDescription>
              </Alert>
            </motion.div>
          )}

          {/* Error Alert */}
          {submitError && (
            <Alert variant="destructive" className="mb-4">
              <AlertDescription>{submitError}</AlertDescription>
            </Alert>
          )}

          {/* Two Column Layout */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Left Column */}
            <div className="space-y-4">
              {/* Class Name */}
              <div className="space-y-2">
                <Label htmlFor="name">Class Name</Label>
                <Input
                  id="name"
                  placeholder="Enter class name"
                  {...form.register("name")}
                  className={form.formState.errors.name ? "border-red-500" : ""}
                  disabled={isFormLoading}
                />
                {form.formState.errors.name && (
                  <p className="text-sm text-red-500">
                    {form.formState.errors.name.message}
                  </p>
                )}
              </div>

              {/* Category Selection */}
              <div className="space-y-2">
                <Label htmlFor="categoryId">Category</Label>
                <SelectWithInlineCreation
                  value={selectedCategoryId}
                  onValueChange={(value) => {
                    form.setValue("categoryId", value);
                    setSelectedCategoryId(value);
                  }}
                  options={mapToSelectOptions(categories)}
                  entityType="class-category"
                  tenantId={tenantId}
                  placeholder="Select a category"
                  disabled={isFormLoading}
                  triggerClassName={form.formState.errors.categoryId ? "border-red-500" : ""}
                  onEntityCreated={(newCategory) => {
                    form.setValue("categoryId", newCategory.id);
                    setSelectedCategoryId(newCategory.id);
                  }}
                  refetchData={refetchCategories}
                />
                {form.formState.errors.categoryId && (
                  <p className="text-sm text-red-500">
                    {form.formState.errors.categoryId.message}
                  </p>
                )}
              </div>

              {/* Subcategory Selection */}
              <div className="space-y-2">
                <Label htmlFor="subcategoryId">Subcategory (Optional)</Label>
                <SelectWithInlineCreation
                  value={form.watch("subcategoryId") || "none"}
                  onValueChange={(value) => form.setValue("subcategoryId", value === "none" ? undefined : value)}
                  options={mapToSelectOptions(subcategories)}
                  entityType="class-subcategory"
                  tenantId={tenantId}
                  placeholder="Select a subcategory"
                  emptyOption={{ value: "none", label: "No subcategory" }}
                  disabled={isFormLoading || !selectedCategoryId}
                  onEntityCreated={(newSubcategory) => {
                    form.setValue("subcategoryId", newSubcategory.id);
                  }}
                  refetchData={refetchSubcategories}
                />
              </div>

              {/* Duration */}
              <div className="space-y-2">
                <Label htmlFor="duration_value">Duration (minutes)</Label>
                <Input
                  id="duration_value"
                  type="number"
                  placeholder="60"
                  {...form.register("duration_value", { valueAsNumber: true })}
                  disabled={isFormLoading}
                />
              </div>

              {/* Class Level */}
              <div className="space-y-2">
                <Label htmlFor="level_id">Class Level (Optional)</Label>
                <SelectWithInlineCreation
                  value={form.watch("level_id") || "not_set"}
                  onValueChange={(value) => form.setValue("level_id", value === "not_set" ? undefined : value)}
                  options={mapToSelectOptions(classLevels)}
                  entityType="class-level"
                  tenantId={tenantId}
                  placeholder="Select class level"
                  emptyOption={{ value: "not_set", label: "No level set" }}
                  disabled={isFormLoading}
                  onEntityCreated={(newLevel) => {
                    form.setValue("level_id", newLevel.id);
                  }}
                  refetchData={refetchClassLevels}
                />
              </div>
            </div>

            {/* Right Column */}
            <div className="space-y-4">
              {/* Description */}
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  placeholder="Enter class description"
                  rows={4}
                  {...form.register("description")}
                  className={form.formState.errors.description ? "border-red-500" : ""}
                  disabled={isFormLoading}
                />
                {form.formState.errors.description && (
                  <p className="text-sm text-red-500">
                    {form.formState.errors.description.message}
                  </p>
                )}
              </div>

              {/* Delivery Mode */}
              <div className="space-y-2">
                <Label htmlFor="delivery_mode">Delivery Mode</Label>
                <Select
                  value={form.watch("delivery_mode") || "not_set"}
                  onValueChange={(value) => form.setValue("delivery_mode", value === "not_set" ? undefined : value)}
                  disabled={isFormLoading}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select delivery mode" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="not_set">Not set</SelectItem>
                    <SelectItem value="onsite">On-site</SelectItem>
                    <SelectItem value="online">Online</SelectItem>
                    <SelectItem value="hybrid">Hybrid</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Location */}
              <div className="space-y-2">
                <Label htmlFor="location_id">Location (Optional)</Label>
                <SelectWithInlineCreation
                  value={form.watch("location_id") || "not_set"}
                  onValueChange={(value) => form.setValue("location_id", value === "not_set" ? undefined : value)}
                  options={mapToSelectOptions(locations)}
                  entityType="location"
                  tenantId={tenantId}
                  placeholder="Select location"
                  emptyOption={{ value: "not_set", label: "No location set" }}
                  disabled={isFormLoading}
                  onEntityCreated={(newLocation) => {
                    form.setValue("location_id", newLocation.id);
                  }}
                  refetchData={refetchLocations}
                />
              </div>

              {/* Checkboxes */}
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="is_private"
                    checked={form.watch("is_private")}
                    onCheckedChange={(checked) => form.setValue("is_private", !!checked)}
                    disabled={isFormLoading}
                  />
                  <Label htmlFor="is_private">Private Class</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="custom_cancellation_policy"
                    checked={form.watch("custom_cancellation_policy")}
                    onCheckedChange={(checked) => form.setValue("custom_cancellation_policy", !!checked)}
                    disabled={isFormLoading}
                  />
                  <Label htmlFor="custom_cancellation_policy">Custom Cancellation Policy</Label>
                </div>

                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="is_active"
                    checked={form.watch("is_active")}
                    onCheckedChange={(checked) => form.setValue("is_active", !!checked)}
                    disabled={isFormLoading}
                  />
                  <Label htmlFor="is_active">Active</Label>
                </div>
              </div>

              {/* Cancellation Policy Description */}
              {form.watch("custom_cancellation_policy") && (
                <div className="space-y-2">
                  <Label htmlFor="cancellation_policy_description">Cancellation Policy Description</Label>
                  <Textarea
                    id="cancellation_policy_description"
                    placeholder="Describe the custom cancellation policy"
                    rows={3}
                    {...form.register("cancellation_policy_description")}
                    className={form.formState.errors.cancellation_policy_description ? "border-red-500" : ""}
                    disabled={isFormLoading}
                  />
                  {form.formState.errors.cancellation_policy_description && (
                    <p className="text-sm text-red-500">
                      {form.formState.errors.cancellation_policy_description.message}
                    </p>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Items to Bring Section */}
          <div className="border-t pt-6 mt-6">
            <div className="flex items-center gap-2 mb-4">
              <Package className="h-5 w-5 text-blue-600" />
              <h3 className="text-lg font-semibold">Items to Bring</h3>
              {itemsToBring.length > 0 && (
                <Badge variant="secondary" className="text-xs">
                  {itemsToBring.length} item{itemsToBring.length !== 1 ? 's' : ''}
                </Badge>
              )}
            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <p className="text-sm text-gray-600">
                  Specify items that participants should bring to this class.
                </p>
              </div>

              {/* Items List */}
              <div className="space-y-3">
                {itemsToBring.map((item, index) => (
                  <div key={item.id} className="flex items-center gap-3 p-3 border rounded-lg bg-gray-50">
                    <div className="flex-1">
                      <input
                        type="text"
                        placeholder="Item name (e.g., Yoga mat, Water bottle)"
                        value={item.item_name}
                        onChange={(e) => updateItemToBring(index, 'item_name', e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                    <div className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        id={`required-${index}`}
                        checked={item.is_required}
                        onChange={(e) => updateItemToBring(index, 'is_required', e.target.checked)}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <label htmlFor={`required-${index}`} className="text-sm text-gray-700">
                        Required
                      </label>
                    </div>
                    <button
                      type="button"
                      onClick={() => removeItemToBring(index)}
                      className="p-2 text-red-600 hover:bg-red-50 rounded-md transition-colors"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                ))}

                {/* Add Item Button */}
                <button
                  type="button"
                  onClick={addItemToBring}
                  className="w-full p-3 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-blue-400 hover:text-blue-600 transition-colors"
                >
                  + Add Item to Bring
                </button>
              </div>
            </div>
          </div>

          {/* YouTube Links Section */}
          <div className="border-t pt-6 mt-6">
            <div className="flex items-center gap-2 mb-4">
              <Youtube className="h-5 w-5 text-red-600" />
              <h3 className="text-lg font-semibold">YouTube Links</h3>
              {youtubeLinks.length > 0 && (
                <Badge variant="secondary" className="text-xs">
                  {youtubeLinks.length} link{youtubeLinks.length !== 1 ? 's' : ''}
                </Badge>
              )}
            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <p className="text-sm text-gray-600">
                  Add YouTube video links related to this class (tutorials, demonstrations, etc.).
                </p>
              </div>

              {/* YouTube Links List */}
              <div className="space-y-3">
                {youtubeLinks.map((link, index) => (
                  <div key={link.id} className="flex items-center gap-3 p-3 border rounded-lg bg-gray-50">
                    <div className="flex-1">
                      <input
                        type="url"
                        placeholder="https://www.youtube.com/watch?v=..."
                        value={link.yt_url}
                        onChange={(e) => updateYoutubeLink(index, e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500"
                      />
                    </div>
                    <button
                      type="button"
                      onClick={() => removeYoutubeLink(index)}
                      className="p-2 text-red-600 hover:bg-red-50 rounded-md transition-colors"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                ))}

                {/* Add YouTube Link Button */}
                <button
                  type="button"
                  onClick={addYoutubeLink}
                  className="w-full p-3 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-red-400 hover:text-red-600 transition-colors"
                >
                  + Add YouTube Link
                </button>
              </div>
            </div>
          </div>

          {/* Package Pricing Access Control Section */}
          <div className="border-t pt-6 mt-6">
            <div className="flex items-center gap-2 mb-4">
              <Package className="h-5 w-5 text-green-600" />
              <h3 className="text-lg font-semibold">Package Pricing Access Control</h3>
              {selectedPackagePricings.length > 0 && (
                <Badge variant="secondary" className="text-xs">
                  {selectedPackagePricings.length} pricing{selectedPackagePricings.length !== 1 ? 's' : ''}
                </Badge>
              )}

            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label className="text-sm font-medium">Select Package Pricings</Label>
                <p className="text-xs text-muted-foreground mb-3">
                  Choose which package pricings can access this class. If none selected, class will be accessible to all.
                </p>

                {packagePricings.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <Package className="mx-auto h-8 w-8 mb-2 opacity-50" />
                    <p className="text-sm">No package pricings available</p>
                    <p className="text-xs">Create package pricings first to control class access</p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 gap-3">
                    {packagePricings.map((pricing) => (
                      <PackagePricingCard
                        key={pricing.id}
                        pricing={pricing}
                        isSelected={selectedPackagePricings.includes(pricing.id)}
                        onToggle={handlePackagePricingToggle}
                      />
                    ))}
                  </div>
                )}
              </div>

              {/* Helper Text */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <div className="flex items-start space-x-2">
                  <div className="text-blue-600 mt-0.5">💡</div>
                  <div className="text-xs text-blue-800">
                    <p className="font-medium mb-1">Access Control Tips:</p>
                    <ul className="space-y-1 text-xs">
                      <li>• Select specific package pricings to restrict class access</li>
                      <li>• Leave empty to allow all package pricings to access this class</li>
                      <li>• Access is controlled at the package pricing level</li>
                      <li>• Changes take effect immediately after saving</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Membership Plan Access Control Section */}
          <div className="border-t pt-6 mt-6">
            <div className="flex items-center gap-2 mb-4">
              <Package className="h-5 w-5 text-blue-600" />
              <h3 className="text-lg font-semibold">Membership Plan Access Control</h3>
              {selectedMembershipPlans.length > 0 && (
                <Badge variant="secondary" className="text-xs">
                  {selectedMembershipPlans.length} plan{selectedMembershipPlans.length !== 1 ? 's' : ''}
                </Badge>
              )}
            </div>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label className="text-sm font-medium">Select Membership Plans</Label>
                <p className="text-xs text-muted-foreground mb-3">
                  Choose which membership plans can access this class. If none selected, class will be accessible to all.
                </p>

                {membershipPlans.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <Package className="mx-auto h-8 w-8 mb-2 opacity-50" />
                    <p className="text-sm">No membership plans available</p>
                    <p className="text-xs">Create membership plans first to control class access</p>
                  </div>
                ) : (
                  <div className="grid grid-cols-1 gap-3">
                    {membershipPlans.map((plan) => (
                      <MembershipPlanCard
                        key={plan.id}
                        plan={plan}
                        isSelected={selectedMembershipPlans.includes(plan.id)}
                        onToggle={handleMembershipPlanToggle}
                      />
                    ))}
                  </div>
                )}
              </div>

              {/* Helper Text */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                <div className="flex items-start space-x-2">
                  <div className="text-blue-600 mt-0.5">💡</div>
                  <div className="text-xs text-blue-800">
                    <p className="font-medium mb-1">Membership Plan Tips:</p>
                    <ul className="space-y-1 text-xs">
                      <li>• Select specific membership plans to restrict class access</li>
                      <li>• Leave empty to allow all membership plans to access this class</li>
                      <li>• Access is controlled at the membership plan level</li>
                      <li>• Changes take effect immediately after saving</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Class Images Section - Integrated */}
          <div className="border-t pt-6 mt-6">
            <div className="flex items-center gap-2 mb-4">
              <ImageIcon className="h-5 w-5 text-blue-600" />
              <h3 className="text-lg font-semibold">Class Images</h3>
              {classImages.length > 0 && (
                <Badge variant="secondary" className="text-xs">
                  {classImages.length} image{classImages.length !== 1 ? 's' : ''}
                </Badge>
              )}
            </div>

            {/* Image Upload */}
            <div className="space-y-4">
              <ImageUpload
                onImageUpload={handleImageUpload}
                disabled={isFormLoading}
                placeholder="Upload class images"
                autoUpload={true}
                showUrlInput={false}
              />

              {/* Display Current Images */}
              {classImages.length > 0 && (
                <div className="space-y-3">
                  <Label className="text-sm font-medium">Current Images:</Label>
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                    {classImages.map((imageUrl, index) => (
                      <motion.div
                        key={index}
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.9 }}
                        className="relative group"
                      >
                        <div className="aspect-square rounded-lg overflow-hidden bg-muted">
                          <img
                            src={imageUrl}
                            alt="Class image"
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.src = "/images/placeholder-image.svg";
                            }}
                          />
                        </div>
                        <Button
                          type="button"
                          variant="destructive"
                          size="sm"
                          className="absolute top-1 right-1 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                          onClick={() => handleImageDelete(imageUrl)}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </motion.div>
                    ))}
                  </div>
                </div>
              )}

              {/* Helper Text */}
              <p className="text-xs text-muted-foreground">
                Images will be saved with the class data when you submit the form.
              </p>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex gap-3 pt-4">
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isFormLoading}
                className="flex-1"
              >
                Cancel
              </Button>
            )}
            <Button
              type="submit"
              disabled={isFormLoading}
              className="flex-1"
            >
              {isFormLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {classData ? "Update" : "Create"} Class
            </Button>
          </div>
        </form>
      </CardContent>

      {/* All inline creation modals are now handled by the global InlineCreationModal */}
    </Card>
  );
}
