import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { EquipmentInstanceService } from "@/lib/services/equipment-instances.service";

// GET /api/equipment-instances/stats - Get equipment instance statistics
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const stats = await EquipmentInstanceService.getStats();

    return NextResponse.json(
      { data: stats },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error fetching equipment instance stats:", error);
    return NextResponse.json(
      { error: "Failed to fetch equipment instance stats" },
      { status: 500 }
    );
  }
}
