// Script to verify package purchase options data
// Run with: npx tsx scripts/verify-package-purchase-options.ts

import { db } from "../src/lib/db";
import { packages, locations, package_purchase_options } from "../src/lib/db/schema";
import { eq } from "drizzle-orm";

async function verifyPackagePurchaseOptions() {
  console.log('🔍 Verifying Package Purchase Options Data...');
  console.log('================================================');
  
  try {
    // Check packages
    console.log('📦 Checking Packages...');
    const allPackages = await db.select().from(packages);
    console.log(`   Found ${allPackages.length} packages in database`);
    
    allPackages.forEach(pkg => {
      console.log(`   - ${pkg.name} (ID: ${pkg.id})`);
    });
    console.log('');

    // Check locations
    console.log('📍 Checking Locations...');
    const allLocations = await db.select().from(locations);
    console.log(`   Found ${allLocations.length} locations in database`);
    
    allLocations.forEach(loc => {
      console.log(`   - ${loc.name} (ID: ${loc.id})`);
    });
    console.log('');

    // Check package purchase options
    console.log('🛒 Checking Package Purchase Options...');
    const allPurchaseOptions = await db.select().from(package_purchase_options);
    console.log(`   Found ${allPurchaseOptions.length} package purchase options in database`);
    console.log('');

    // Detailed verification with joins
    console.log('🔍 Detailed Package Purchase Options Analysis...');
    
    for (const option of allPurchaseOptions) {
      // Get package info
      const [packageInfo] = await db
        .select()
        .from(packages)
        .where(eq(packages.id, option.package_id))
        .limit(1);

      // Get location info if specified
      let locationInfo = null;
      if (option.sold_at_location_id) {
        const [location] = await db
          .select()
          .from(locations)
          .where(eq(locations.id, option.sold_at_location_id))
          .limit(1);
        locationInfo = location;
      }

      console.log(`📦 Package: ${packageInfo?.name || 'Unknown'}`);
      console.log(`   • Package ID: ${option.package_id}`);
      console.log(`   • Purchase Limit: ${option.purchase_limit || 'Unlimited'}`);
      console.log(`   • Restrict To: ${option.restrict_to || 'Not specified'}`);
      console.log(`   • Transferable: ${option.transferable ? 'Yes' : 'No'}`);
      console.log(`   • Location Specific: ${option.specify_sold_at_location ? 'Yes' : 'No'}`);
      if (locationInfo) {
        console.log(`   • Sold At Location: ${locationInfo.name}`);
      }
      console.log(`   • Class Booking Limit: ${option.class_booking_limit || 'Unlimited'}`);
      console.log(`   • Show Online: ${option.show_online ? 'Yes' : 'No'}`);
      console.log('');
    }

    // Statistics
    console.log('📊 Statistics...');
    const stats = {
      totalPackages: allPackages.length,
      totalLocations: allLocations.length,
      totalPurchaseOptions: allPurchaseOptions.length,
      transferableOptions: allPurchaseOptions.filter(opt => opt.transferable).length,
      locationSpecificOptions: allPurchaseOptions.filter(opt => opt.specify_sold_at_location).length,
      onlineVisibleOptions: allPurchaseOptions.filter(opt => opt.show_online).length,
      unlimitedPurchaseOptions: allPurchaseOptions.filter(opt => opt.purchase_limit === null).length,
      unlimitedClassOptions: allPurchaseOptions.filter(opt => opt.class_booking_limit === null).length,
    };

    console.log(`   • Total Packages: ${stats.totalPackages}`);
    console.log(`   • Total Locations: ${stats.totalLocations}`);
    console.log(`   • Total Purchase Options: ${stats.totalPurchaseOptions}`);
    console.log(`   • Transferable Options: ${stats.transferableOptions}`);
    console.log(`   • Location Specific Options: ${stats.locationSpecificOptions}`);
    console.log(`   • Online Visible Options: ${stats.onlineVisibleOptions}`);
    console.log(`   • Unlimited Purchase Options: ${stats.unlimitedPurchaseOptions}`);
    console.log(`   • Unlimited Class Options: ${stats.unlimitedClassOptions}`);
    console.log('');

    // Restrict To breakdown
    console.log('🎯 Restrict To Breakdown...');
    const restrictToStats = allPurchaseOptions.reduce((acc, opt) => {
      const restrictTo = opt.restrict_to || 'not_specified';
      acc[restrictTo] = (acc[restrictTo] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    Object.entries(restrictToStats).forEach(([restrictTo, count]) => {
      console.log(`   • ${restrictTo}: ${count} packages`);
    });
    console.log('');

    // Validation checks
    console.log('✅ Validation Checks...');
    let validationErrors = 0;

    // Check foreign key integrity
    for (const option of allPurchaseOptions) {
      const packageExists = allPackages.some(pkg => pkg.id === option.package_id);
      if (!packageExists) {
        console.log(`❌ Invalid package_id: ${option.package_id}`);
        validationErrors++;
      }

      if (option.sold_at_location_id) {
        const locationExists = allLocations.some(loc => loc.id === option.sold_at_location_id);
        if (!locationExists) {
          console.log(`❌ Invalid sold_at_location_id: ${option.sold_at_location_id}`);
          validationErrors++;
        }
      }

      // Check logical consistency
      if (option.specify_sold_at_location && !option.sold_at_location_id) {
        console.log(`❌ Inconsistent data: specify_sold_at_location is true but sold_at_location_id is null for package ${option.package_id}`);
        validationErrors++;
      }
    }

    if (validationErrors === 0) {
      console.log('   ✅ All validation checks passed!');
    } else {
      console.log(`   ❌ Found ${validationErrors} validation errors`);
    }

    console.log('');
    console.log('================================================');
    console.log('🎉 Package Purchase Options verification completed!');

  } catch (error) {
    console.error('❌ Error during verification:', error);
    throw error;
  }
}

// Run the verification function
if (require.main === module) {
  verifyPackagePurchaseOptions()
    .then(() => {
      console.log('✅ Verification completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Verification failed:', error);
      process.exit(1);
    });
}

export { verifyPackagePurchaseOptions };
