import { db } from "./index";
import { users, organizations, organizationMembers, tenants, business_profiles, addresses, settings } from "./schema";
import { hash } from "bcryptjs";
import { createId } from "@paralleldrive/cuid2";
import { eq } from "drizzle-orm";

async function seed() {
  console.log("🌱 Seeding database...");
  console.log("🔧 Database config:", {
    host: process.env.DB_HOST || "127.0.0.1",
    port: process.env.DB_PORT || "5433",
    user: process.env.DB_USER || "citizix_user",
    database: process.env.DB_NAME || "saas_app",
  });

  try {
    // Test database connection first
    console.log("🔌 Testing database connection...");
    const testResult = await db.select().from(users).limit(1);
    console.log("✅ Database connection successful");

    // Create default organization
    const [defaultOrg] = await db.insert(organizations).values({
      id: createId(),
      name: "Default Organization",
      slug: "default",
      description: "Default organization for new users",
      plan: "free",
      credits: 1000,
      monthlyCredits: 1000,
    }).returning();

    console.log("✅ Created default organization");

    // Create admin user
    const hashedPassword = await hash("admin123", 12);
    const [adminUser] = await db.insert(users).values({
      id: createId(),
      email: "<EMAIL>",
      name: "Admin User",
      password: hashedPassword,
      role: "admin",
      organizationId: defaultOrg.id,
      credits: 10000,
      monthlyCredits: 10000,
      emailVerified: new Date(),
    }).returning();

    console.log("✅ Created admin user");

    // Add admin to organization
    await db.insert(organizationMembers).values({
      id: createId(),
      organizationId: defaultOrg.id,
      userId: adminUser.id,
      role: "owner",
    });

    console.log("✅ Added admin to organization");

    // Create test user
    const testHashedPassword = await hash("test123", 12);
    const [testUser] = await db.insert(users).values({
      id: createId(),
      email: "<EMAIL>",
      name: "Test User",
      password: testHashedPassword,
      role: "user",
      organizationId: defaultOrg.id,
      credits: 100,
      monthlyCredits: 100,
      emailVerified: new Date(),
    }).returning();

    console.log("✅ Created test user");

    // Add test user to organization
    await db.insert(organizationMembers).values({
      id: createId(),
      organizationId: defaultOrg.id,
      userId: testUser.id,
      role: "member",
    });

    console.log("✅ Added test user to organization");

    // Create sample tenants
    const sampleTenants = [
      {
        name: "Acme Corp Portal",
        subdomain: "acme",
        organizationId: defaultOrg.id,
        settings: {
          theme: "blue",
          features: ["analytics", "reports"],
          customization: {
            logo: "/logos/acme.png",
            primaryColor: "#0066cc"
          }
        }
      },
      {
        name: "Demo Application",
        subdomain: "demo",
        organizationId: defaultOrg.id,
        settings: {
          theme: "green",
          features: ["basic"],
          customization: {
            logo: "/logos/demo.png",
            primaryColor: "#00aa44"
          }
        }
      }
    ];

    for (const tenantData of sampleTenants) {
      await db.insert(tenants).values(tenantData);
    }

    console.log("✅ Created sample tenants");

    // Assign users to tenants
    const [acmeTenant] = await db.select().from(tenants).where(eq(tenants.subdomain, "acme")).limit(1);
    const [demoTenant] = await db.select().from(tenants).where(eq(tenants.subdomain, "demo")).limit(1);

    if (acmeTenant && demoTenant) {
      // Assign admin user to acme tenant
      await db
        .update(users)
        .set({ tenantId: acmeTenant.id })
        .where(eq(users.email, "<EMAIL>"));

      // Assign test user to demo tenant
      await db
        .update(users)
        .set({ tenantId: demoTenant.id })
        .where(eq(users.email, "<EMAIL>"));

      console.log("✅ Assigned users to tenants");

      // Create business profiles for tenants
      const businessProfilesData = [
        {
          tenantId: acmeTenant.id,
          business_name: "Acme Corporation",
          business_logo: "/logos/acme-logo.png",
          company_registered_name: "Acme Corporation Ltd.",
          business_description: "Leading provider of innovative solutions for modern businesses.",
          business_website: "https://acme.example.com",
          whatsapp_number: "+**********",
          show_whatsapp_floating: true,
        },
        {
          tenantId: demoTenant.id,
          business_name: "Demo Company",
          business_logo: "/logos/demo-logo.png",
          company_registered_name: "Demo Company Inc.",
          business_description: "Demo application for testing purposes.",
          business_website: "https://demo.example.com",
          whatsapp_number: "+**********",
          show_whatsapp_floating: false,
        }
      ];

      for (const profileData of businessProfilesData) {
        await db.insert(business_profiles).values(profileData);
      }

      console.log("✅ Created business profiles");

      // Create addresses for tenants
      const addressesData = [
        {
          id: createId(),
          tenantId: acmeTenant.id,
          addressLine1: "123 Business Street",
          addressLine2: "Suite 100",
          city: "New York",
          state: "NY",
          country: "United States",
          postalCode: "10001",
          acceptedAt: new Date(),
        },
        {
          id: createId(),
          tenantId: acmeTenant.id,
          addressLine1: "456 Secondary Ave",
          city: "Los Angeles",
          state: "CA",
          country: "United States",
          postalCode: "90210",
        },
        {
          id: createId(),
          tenantId: demoTenant.id,
          addressLine1: "789 Demo Road",
          city: "San Francisco",
          state: "CA",
          country: "United States",
          postalCode: "94102",
          acceptedAt: new Date(),
        }
      ];

      for (const addressData of addressesData) {
        await db.insert(addresses).values(addressData);
      }

      console.log("✅ Created addresses");

      // Create settings for tenants
      const settingsData = [
        {
          id: createId(),
          tenantId: acmeTenant.id,
          currency: "USD",
          timeZone: "America/New_York",
          acceptedAt: new Date(),
        },
        {
          id: createId(),
          tenantId: demoTenant.id,
          currency: "EUR",
          timeZone: "Europe/London",
          acceptedAt: new Date(),
        }
      ];

      for (const settingData of settingsData) {
        await db.insert(settings).values(settingData);
      }

      console.log("✅ Created settings");
    }

    // Credit packages are now defined in stripe/config.ts as constants

    console.log("🎉 Database seeded successfully!");
    console.log("\n📧 Login credentials:");
    console.log("Admin: <EMAIL> / admin123");
    console.log("Test User: <EMAIL> / test123");

  } catch (error) {
    console.error("❌ Error seeding database:", error);
    process.exit(1);
  }
}

// Run seed if this file is executed directly
if (require.main === module) {
  seed()
    .then(() => {
      console.log("✅ Seeding completed");
      process.exit(0);
    })
    .catch((error) => {
      console.error("❌ Seeding failed:", error);
      process.exit(1);
    });
}

export { seed };
