import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { FacilityService } from "@/lib/services/facility.service";

// GET /api/facilities/stats - Get facility statistics
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get("tenantId");

    if (!tenantId || tenantId === "null" || tenantId === "undefined") {
      return NextResponse.json(
        { error: "tenantId is required" },
        { status: 400 }
      );
    }

    const tenantIdNum = parseInt(tenantId);
    if (isNaN(tenantIdNum) || tenantIdNum <= 0) {
      return NextResponse.json(
        { error: "tenantId must be a valid positive number" },
        { status: 400 }
      );
    }

    const stats = await FacilityService.getStats(tenantIdNum);

    return NextResponse.json({ stats });
  } catch (error) {
    console.error("Error fetching facility stats:", error);
    return NextResponse.json(
      { error: "Failed to fetch facility statistics" },
      { status: 500 }
    );
  }
}
