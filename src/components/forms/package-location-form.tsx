"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Package, MapPin, Plus, X } from "lucide-react";
import { usePackageList } from "@/lib/hooks/queries/use-package-queries";
import { useLocations } from "@/lib/hooks/queries/use-location-queries";
import { useTenants } from "@/lib/hooks/queries/use-tenant-queries";

const packageLocationSchema = z.object({
  packageId: z.string().min(1, "Package is required"),
  locationIds: z.array(z.string()).min(1, "At least one location is required"),
});

type PackageLocationFormData = z.infer<typeof packageLocationSchema>;

interface PackageLocationFormProps {
  onSubmit: (data: PackageLocationFormData) => void;
  onCancel: () => void;
  initialData?: {
    packageId?: string;
    locationIds?: string[];
  };
  tenantId: number;
}

export function PackageLocationForm({
  onSubmit,
  onCancel,
  initialData,
  tenantId,
}: PackageLocationFormProps) {
  const [selectedPackage, setSelectedPackage] = useState<string>(initialData?.packageId || "");

  // Fetch data
  const { data: packages = [] } = usePackageList({}, tenantId);
  const { data: locations = [] } = useLocations({ tenantId });
  const { data: tenants = [] } = useTenants();

  const form = useForm<PackageLocationFormData>({
    resolver: zodResolver(packageLocationSchema),
    defaultValues: {
      packageId: initialData?.packageId || "",
      locationIds: initialData?.locationIds || [],
    },
  });

  const watchedLocationIds = form.watch("locationIds");

  const handleSubmit = (data: PackageLocationFormData) => {
    onSubmit(data);
  };

  const toggleLocation = (locationId: string) => {
    const currentIds = form.getValues("locationIds");
    const newIds = currentIds.includes(locationId)
      ? currentIds.filter(id => id !== locationId)
      : [...currentIds, locationId];
    
    form.setValue("locationIds", newIds);
  };

  const selectAllLocations = () => {
    const allLocationIds = locations.map(location => location.id);
    form.setValue("locationIds", allLocationIds);
  };

  const clearAllLocations = () => {
    form.setValue("locationIds", []);
  };

  const selectedPackageData = packages.find(pkg => pkg.id === selectedPackage);
  const selectedLocationsData = locations.filter(location => 
    watchedLocationIds.includes(location.id)
  );

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Package className="h-5 w-5" />
          Assign Package to Locations
        </CardTitle>
        <CardDescription>
          Select a package and assign it to one or more locations
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2">
              {/* Package Selection */}
              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="packageId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Package</FormLabel>
                      <Select
                        onValueChange={(value) => {
                          field.onChange(value);
                          setSelectedPackage(value);
                        }}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a package" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {packages.map((pkg) => (
                            <SelectItem key={pkg.id} value={pkg.id}>
                              <div className="flex items-center gap-2">
                                <Package className="h-4 w-4" />
                                <span>{pkg.name}</span>
                                <Badge variant={pkg.isActive ? "default" : "secondary"}>
                                  {pkg.isActive ? "Active" : "Inactive"}
                                </Badge>
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Selected Package Info */}
                {selectedPackageData && (
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-sm">Selected Package</CardTitle>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="space-y-2">
                        <div className="flex items-center gap-2">
                          <Package className="h-4 w-4" />
                          <span className="font-medium">{selectedPackageData.name}</span>
                          <Badge variant={selectedPackageData.isActive ? "default" : "secondary"}>
                            {selectedPackageData.isActive ? "Active" : "Inactive"}
                          </Badge>
                        </div>
                        {selectedPackageData.description && (
                          <p className="text-sm text-muted-foreground">
                            {selectedPackageData.description}
                          </p>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>

              {/* Location Selection */}
              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="locationIds"
                  render={() => (
                    <FormItem>
                      <div className="flex items-center justify-between">
                        <FormLabel>Locations</FormLabel>
                        <div className="flex gap-2">
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={selectAllLocations}
                          >
                            Select All
                          </Button>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={clearAllLocations}
                          >
                            Clear All
                          </Button>
                        </div>
                      </div>
                      
                      <div className="space-y-2 max-h-64 overflow-y-auto border rounded-md p-3">
                        {locations.map((location) => (
                          <div
                            key={location.id}
                            className="flex items-center space-x-2 p-2 rounded hover:bg-accent cursor-pointer"
                            onClick={() => toggleLocation(location.id)}
                          >
                            <Checkbox
                              checked={watchedLocationIds.includes(location.id)}
                              onChange={() => toggleLocation(location.id)}
                            />
                            <MapPin className="h-4 w-4 text-muted-foreground" />
                            <div className="flex-1">
                              <div className="font-medium">{location.name}</div>
                              {location.address && (
                                <div className="text-sm text-muted-foreground">
                                  {location.address}
                                </div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Selected Locations Summary */}
                {selectedLocationsData.length > 0 && (
                  <Card>
                    <CardHeader className="pb-3">
                      <CardTitle className="text-sm">
                        Selected Locations ({selectedLocationsData.length})
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="flex flex-wrap gap-2">
                        {selectedLocationsData.map((location) => (
                          <Badge
                            key={location.id}
                            variant="secondary"
                            className="flex items-center gap-1"
                          >
                            <MapPin className="h-3 w-3" />
                            {location.name}
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              className="h-auto p-0 ml-1"
                              onClick={() => toggleLocation(location.id)}
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </Badge>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            </div>

            {/* Form Actions */}
            <div className="flex justify-end space-x-2 pt-4 border-t">
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
              <Button type="submit">
                <Plus className="mr-2 h-4 w-4" />
                Assign Package to Locations
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
