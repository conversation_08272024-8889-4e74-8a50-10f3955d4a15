import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { ClassCategory } from '@/lib/db/schema';

/**
 * Class Category Queries Hooks
 *
 * Hooks untuk manage state dan data fetching class categories menggunakan TanStack Query.
 * Ini kayak "jembatan" antara UI components dan service layer.
 *
 * Pattern ini mengikuti use-class-level-queries.ts yang sudah established.
 */

// Query Keys
export const classCategoryKeys = {
  all: ['class-categories'] as const,
  lists: () => [...classCategoryKeys.all, 'list'] as const,
  list: (filters: Record<string, any>) => [...classCategoryKeys.lists(), { filters }] as const,
  details: () => [...classCategoryKeys.all, 'detail'] as const,
  detail: (id: string) => [...classCategoryKeys.details(), id] as const,
  byTenant: (tenantId: number) => [...classCategoryKeys.all, 'tenant', tenantId] as const,
  search: (params: any) => [...classCategoryKeys.all, 'search', params] as const,
};

// API Functions
const classCategoryApi = {
  getAll: async (): Promise<ClassCategory[]> => {
    const response = await fetch('/api/class-categories');
    if (!response.ok) {
      throw new Error(`Failed to fetch class categories: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data?.categories || [];
  },

  getByTenant: async (tenantId: number): Promise<ClassCategory[]> => {
    const params = new URLSearchParams({
      tenantId: tenantId.toString(),
    });
    const response = await fetch(`/api/class-categories?${params}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch class categories: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data?.categories || [];
  },

  search: async (tenantId: number, searchTerm?: string, limit = 20, offset = 0) => {
    const params = new URLSearchParams({
      tenantId: tenantId.toString(),
      limit: limit.toString(),
      offset: offset.toString(),
    });
    if (searchTerm) {
      params.append('search', searchTerm);
    }
    const response = await fetch(`/api/class-categories?${params}`);
    if (!response.ok) {
      throw new Error(`Failed to search class categories: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data;
  },

  getById: async (id: string): Promise<ClassCategory> => {
    const response = await fetch(`/api/class-categories/${id}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch class category: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data;
  },

  create: async (data: { tenantId: number; name: string }): Promise<ClassCategory> => {
    const response = await fetch('/api/class-categories', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      throw new Error(`Failed to create class category: ${response.statusText}`);
    }
    const result = await response.json();
    return result.data;
  },

  update: async (id: string, tenantId: number, data: { name: string }): Promise<ClassCategory> => {
    const response = await fetch(`/api/class-categories/${id}?tenantId=${tenantId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      throw new Error(`Failed to update class category: ${response.statusText}`);
    }
    const result = await response.json();
    return result.data;
  },

  delete: async (id: string, tenantId: number): Promise<void> => {
    const response = await fetch(`/api/class-categories/${id}?tenantId=${tenantId}`, {
      method: 'DELETE',
    });
    if (!response.ok) {
      throw new Error(`Failed to delete class category: ${response.statusText}`);
    }
  },
};

// Query Hooks
export function useClassCategories() {
  const query = useQuery({
    queryKey: classCategoryKeys.lists(),
    queryFn: classCategoryApi.getAll,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: false,
  });

  const classCategories = Array.isArray(query.data) ? query.data : [];

  return {
    data: classCategories,
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error?.message || null,
    refetch: query.refetch,
  };
}

export function useClassCategoriesByTenant(tenantId: number) {
  return useQuery({
    queryKey: classCategoryKeys.byTenant(tenantId),
    queryFn: () => classCategoryApi.getByTenant(tenantId),
    enabled: !!tenantId,
    staleTime: 5 * 60 * 1000,
  });
}

export function useClassCategory(id: string) {
  return useQuery({
    queryKey: classCategoryKeys.detail(id),
    queryFn: () => classCategoryApi.getById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
}

export function useClassCategorySearch(tenantId: number, searchTerm?: string, limit = 20, offset = 0) {
  return useQuery({
    queryKey: classCategoryKeys.search({ tenantId, searchTerm, limit, offset }),
    queryFn: () => classCategoryApi.search(tenantId, searchTerm, limit, offset),
    enabled: !!tenantId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

// Mutation Hooks
export function useCreateClassCategory() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: classCategoryApi.create,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: classCategoryKeys.all });
    },
  });
}

export function useUpdateClassCategory() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, tenantId, data }: {
      id: string;
      tenantId: number;
      data: { name: string }
    }) => {
      return classCategoryApi.update(id, tenantId, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: classCategoryKeys.all });
    },
  });
}

export function useDeleteClassCategory() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, tenantId }: { id: string; tenantId: number }) => {
      return classCategoryApi.delete(id, tenantId);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: classCategoryKeys.all });
    },
  });
}

// Export types
export interface ClassCategorySearchParams {
  tenantId: number;
  search?: string;
  limit?: number;
  offset?: number;
}
