import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { ClassService } from "@/lib/services/class.service";

/**
 * API Routes untuk Individual Class Operations
 * 
 * Handle GET, PUT, DELETE untuk single class berdasarkan ID.
 * <PERSON><PERSON><PERSON><PERSON> pattern yang sama dengan class-categories/[id] dan class-subcategories/[id].
 */

// Validation schemas
const paramsSchema = z.object({
  id: z.string().min(1, "ID is required"),
});

const querySchema = z.object({
  tenantId: z.string().transform((val) => parseInt(val, 10)),
});

const updateClassSchema = z.object({
  name: z.string().min(1, "Name is required").max(255, "Name too long").optional(),
  description: z.string().max(255, "Description too long").optional().or(z.literal("")),
  categoryId: z.string().min(1, "Category ID is required").optional(),
  subcategoryId: z.string().optional().or(z.literal("")),
  duration_value: z.number().int().positive().optional().or(z.literal(null)),
  duration_unit: z.string().max(50).optional().or(z.literal("")),
  level_id: z.string().optional().or(z.literal("")),
  delivery_mode: z.string().max(50).optional().or(z.literal("")),
  is_private: z.boolean().optional(),
  custom_cancellation_policy: z.boolean().optional(),
  cancellation_policy_description: z.string().max(255).optional().or(z.literal("")),
  is_active: z.boolean().optional(),
  location_id: z.string().optional().or(z.literal("")),
  images: z.array(z.string()).optional(),
  items_to_bring: z.array(z.object({
    id: z.string(),
    item_name: z.string().min(1, "Item name is required"),
    is_required: z.boolean(),
  })).optional(),
  youtube_links: z.array(z.object({
    id: z.string(),
    yt_url: z.string().url("Please enter a valid YouTube URL"),
  })).optional(),
  package_pricing_ids: z.array(z.string()).optional(),
}).transform((data) => {
  // Convert empty strings to undefined for optional fields
  return {
    ...data,
    description: data.description === "" ? undefined : data.description,
    subcategoryId: data.subcategoryId === "" ? undefined : data.subcategoryId,
    duration_unit: data.duration_unit === "" ? undefined : data.duration_unit,
    level_id: data.level_id === "" ? undefined : data.level_id,
    delivery_mode: data.delivery_mode === "" ? undefined : data.delivery_mode,
    cancellation_policy_description: data.cancellation_policy_description === "" ? undefined : data.cancellation_policy_description,
    location_id: data.location_id === "" ? undefined : data.location_id,
  };
});

/**
 * GET /api/classes/[id]
 * Get a single class by ID
 * 
 * Params:
 * - id: class ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Validate params
    const resolvedParams = await params;
    const validatedParams = paramsSchema.parse(resolvedParams);

    // Get class by ID
    const classResult = await ClassService.getById(validatedParams.id);

    if (!classResult) {
      return NextResponse.json(
        { success: false, error: "Class not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: classResult,
    });
  } catch (error) {
    console.error("GET /api/classes/[id] error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: "Invalid parameters", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/classes/[id]
 * Update a class
 * 
 * Params:
 * - id: class ID
 * Query:
 * - tenantId: untuk security (pastikan class belong to tenant)
 * Body:
 * - field-field yang mau diupdate
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Validate params
    const resolvedParams = await params;
    const validatedParams = paramsSchema.parse(resolvedParams);
    
    // Validate query parameters
    const { searchParams } = new URL(request.url);
    const validatedQuery = querySchema.parse({
      tenantId: searchParams.get("tenantId"),
    });

    // Validate request body
    const body = await request.json();
    console.log("Update request body:", body);
    const validatedData = updateClassSchema.parse(body);

    // Update class
    const updatedClass = await ClassService.update(
      validatedParams.id,
      validatedData
    );

    if (!updatedClass) {
      return NextResponse.json(
        { success: false, error: "Class not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: updatedClass,
      message: "Class updated successfully",
    });
  } catch (error) {
    console.error("PUT /api/classes/[id] error:", error);
    
    if (error instanceof z.ZodError) {
      console.log("Validation error details:", error.errors);
      return NextResponse.json(
        { success: false, error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/classes/[id]
 * Delete a class
 * 
 * Params:
 * - id: class ID
 * Query:
 * - tenantId: untuk security (pastikan class belong to tenant)
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Validate params
    const resolvedParams = await params;
    const validatedParams = paramsSchema.parse(resolvedParams);
    
    // Validate query parameters
    const { searchParams } = new URL(request.url);
    const validatedQuery = querySchema.parse({
      tenantId: searchParams.get("tenantId"),
    });

    // Delete class
    await ClassService.delete(validatedParams.id);

    return NextResponse.json({
      success: true,
      message: "Class deleted successfully",
    });
  } catch (error) {
    console.error("DELETE /api/classes/[id] error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error && error.message.includes("not found")) {
      return NextResponse.json(
        { success: false, error: "Class not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}
