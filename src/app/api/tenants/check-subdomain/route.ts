import { NextRequest, NextResponse } from "next/server";
import { TenantService } from "@/lib/services/tenant.service";

// GET /api/tenants/check-subdomain?subdomain=example
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const subdomain = searchParams.get("subdomain");

    if (!subdomain) {
      return NextResponse.json(
        { error: "Subdomain parameter is required" },
        { status: 400 }
      );
    }

    const isAvailable = await TenantService.isSubdomainAvailable(subdomain);
    
    let suggestion = null;
    if (!isAvailable) {
      suggestion = await TenantService.generateSubdomainSuggestion(subdomain);
    }

    return NextResponse.json({
      subdomain,
      available: isAvailable,
      suggestion,
    });
  } catch (error) {
    console.error("Error checking subdomain:", error);
    return NextResponse.json(
      { error: "Failed to check subdomain availability" },
      { status: 500 }
    );
  }
}
