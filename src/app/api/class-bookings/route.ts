import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { z } from "zod";
import { 
  ClassBookingService, 
  type CreateClassBookingData, 
  type ClassBookingFilters 
} from "@/lib/services/class-booking.service";

// Validation schemas
const createClassBookingSchema = z.object({
  tenantId: z.number().int().positive("Tenant ID must be positive"),
  scheduleId: z.string().min(1, "Schedule ID is required"),
  classId: z.string().min(1, "Class ID is required"),
  customerId: z.string().min(1, "Customer ID is required"),
  bookedByUserId: z.string().optional(),
  bookedByCustomerId: z.string().optional(),
  status: z.string().optional(),
  isWaitlist: z.boolean().optional(),
  waitlistPosition: z.number().int().min(1).optional(),
  paymentStatus: z.string().optional(),
  paymentMethod: z.string().optional(),
  creditsUsed: z.number().int().min(0).optional(),
  notes: z.string().max(255).optional(),
});

const querySchema = z.object({
  tenantId: z.string().optional(),
  scheduleId: z.string().optional(),
  classId: z.string().optional(),
  customerId: z.string().optional(),
  status: z.string().optional(),
  isWaitlist: z.string().optional(),
  paymentStatus: z.string().optional(),
  search: z.string().optional(),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
  limit: z.string().optional(),
  offset: z.string().optional(),
});

/**
 * GET /api/class-bookings
 * Get class bookings with optional filtering and search
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());

    console.log(`📅 [GET /api/class-bookings] Query params:`, queryParams);

    const validatedQuery = querySchema.parse(queryParams);

    // Build filters
    const filters: ClassBookingFilters = {
      tenantId: validatedQuery.tenantId ? parseInt(validatedQuery.tenantId) : undefined,
      scheduleId: validatedQuery.scheduleId,
      classId: validatedQuery.classId,
      customerId: validatedQuery.customerId,
      status: validatedQuery.status,
      isWaitlist: validatedQuery.isWaitlist ? validatedQuery.isWaitlist === 'true' : undefined,
      paymentStatus: validatedQuery.paymentStatus,
      search: validatedQuery.search,
      dateFrom: validatedQuery.dateFrom,
      dateTo: validatedQuery.dateTo,
    };

    const limit = validatedQuery.limit ? parseInt(validatedQuery.limit) : 20;
    const offset = validatedQuery.offset ? parseInt(validatedQuery.offset) : 0;

    console.log(`📅 [GET /api/class-bookings] Filters:`, filters);

    // Check permissions - user harus bisa read bookings
    if (session.user.role !== "admin" && !session.user.permissions?.includes("bookings.read")) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      );
    }

    // Tenant isolation - pastikan user hanya bisa akses data tenant mereka
    if (filters.tenantId && session.user.role !== "admin") {
      // TODO: Add tenant ownership check
    }

    const result = await ClassBookingService.searchBookings(filters, limit, offset);

    return NextResponse.json({
      success: true,
      data: result.bookings,
      meta: {
        total: result.total,
        hasMore: result.hasMore,
        limit,
        offset,
        filters,
      }
    });
  } catch (error) {
    console.error("📅 [GET /api/class-bookings] Error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid query parameters", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to fetch class bookings" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/class-bookings
 * Create new class booking
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = createClassBookingSchema.parse(body);

    console.log(`📅 [POST /api/class-bookings] Creating booking:`, validatedData);

    // Check permissions - user harus bisa manage bookings
    if (session.user.role !== "admin" && !session.user.permissions?.includes("bookings.manage")) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      );
    }

    // Tenant isolation - pastikan user hanya bisa create booking untuk tenant mereka
    if (session.user.role !== "admin") {
      // TODO: Add tenant ownership check
    }

    // Set bookedByUserId jika tidak ada (self-booking by staff)
    if (!validatedData.bookedByUserId && !validatedData.bookedByCustomerId) {
      validatedData.bookedByUserId = session.user.id;
    }

    const booking = await ClassBookingService.create(validatedData);

    return NextResponse.json({
      success: true,
      data: booking,
      message: "Class booking created successfully"
    }, { status: 201 });
  } catch (error) {
    console.error("📅 [POST /api/class-bookings] Error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid booking data", details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      // Handle specific business logic errors
      if (error.message.includes("already booked")) {
        return NextResponse.json(
          { error: error.message },
          { status: 409 }
        );
      }
      
      if (error.message.includes("class full")) {
        return NextResponse.json(
          { error: error.message },
          { status: 409 }
        );
      }
    }

    return NextResponse.json(
      { error: "Failed to create class booking" },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/class-bookings
 * Bulk delete class bookings
 */
export async function DELETE(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { ids, tenantId } = body;

    if (!Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json(
        { error: "IDs array is required and cannot be empty" },
        { status: 400 }
      );
    }

    if (!tenantId) {
      return NextResponse.json(
        { error: "Tenant ID is required" },
        { status: 400 }
      );
    }

    console.log(`📅 [DELETE /api/class-bookings] Bulk deleting bookings:`, { ids, tenantId });

    // Check permissions - user harus bisa manage bookings
    if (session.user.role !== "admin" && !session.user.permissions?.includes("bookings.manage")) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      );
    }

    // Tenant isolation
    if (session.user.role !== "admin") {
      // TODO: Add tenant ownership check
    }

    const deletedBookings = await ClassBookingService.bulkDelete(ids, tenantId);

    return NextResponse.json({
      success: true,
      data: deletedBookings,
      message: `Successfully deleted ${deletedBookings.length} bookings`
    });
  } catch (error) {
    console.error("📅 [DELETE /api/class-bookings] Error:", error);

    return NextResponse.json(
      { error: "Failed to delete class bookings" },
      { status: 500 }
    );
  }
}
