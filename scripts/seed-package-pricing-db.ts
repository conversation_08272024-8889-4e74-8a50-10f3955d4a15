// Script to seed sample package pricing directly to database
// Run with: npx tsx scripts/seed-package-pricing-db.ts

import { db } from "../src/lib/db";
import { packages, pricing_groups, package_pricing } from "../src/lib/db/schema";
import { createId } from "@paralleldrive/cuid2";
import { eq } from "drizzle-orm";

async function seedPackagePricing() {
  try {
    console.log("🌱 Starting package pricing seeding...");

    // Get existing packages and pricing groups for tenant 1
    const existingPackages = await db
      .select()
      .from(packages)
      .where(eq(packages.tenantId, 1));

    const existingPricingGroups = await db
      .select()
      .from(pricing_groups)
      .where(eq(pricing_groups.tenantId, 1));

    console.log(`📦 Found ${existingPackages.length} packages`);
    console.log(`💰 Found ${existingPricingGroups.length} pricing groups`);

    if (existingPackages.length === 0) {
      console.log("❌ No packages found. Please run seed-packages-db.ts first");
      return;
    }

    if (existingPricingGroups.length === 0) {
      console.log("❌ No pricing groups found. Please create pricing groups first");
      return;
    }

    // Clear existing package pricing for tenant 1
    const existingPackageIds = existingPackages.map(p => p.id);
    await db
      .delete(package_pricing)
      .where(eq(package_pricing.package_id, existingPackageIds[0])); // This is a simplified delete

    console.log("🗑️ Cleared existing package pricing");

    // Create package pricing combinations
    const packagePricingData = [];

    for (const pkg of existingPackages) {
      for (const pricingGroup of existingPricingGroups) {
        // Create different pricing based on package and group
        let price = 0;
        let creditAmount = 0;

        if (pkg.name.includes("Basic")) {
          if (pricingGroup.name.includes("Monthly")) {
            price = 50000; // 50k IDR
            creditAmount = 10;
          } else if (pricingGroup.name.includes("Weekly")) {
            price = 15000; // 15k IDR
            creditAmount = 3;
          } else {
            price = 200000; // 200k IDR
            creditAmount = 50;
          }
        } else if (pkg.name.includes("Premium")) {
          if (pricingGroup.name.includes("Monthly")) {
            price = 150000; // 150k IDR
            creditAmount = 30;
          } else if (pricingGroup.name.includes("Weekly")) {
            price = 40000; // 40k IDR
            creditAmount = 8;
          } else {
            price = 500000; // 500k IDR
            creditAmount = 120;
          }
        } else if (pkg.name.includes("Personal Training")) {
          if (pricingGroup.name.includes("Monthly")) {
            price = 300000; // 300k IDR
            creditAmount = 20;
          } else if (pricingGroup.name.includes("Weekly")) {
            price = 80000; // 80k IDR
            creditAmount = 5;
          } else {
            price = 1000000; // 1M IDR
            creditAmount = 80;
          }
        } else {
          // Default pricing
          if (pricingGroup.name.includes("Monthly")) {
            price = 100000; // 100k IDR
            creditAmount = 20;
          } else if (pricingGroup.name.includes("Weekly")) {
            price = 30000; // 30k IDR
            creditAmount = 5;
          } else {
            price = 350000; // 350k IDR
            creditAmount = 80;
          }
        }

        packagePricingData.push({
          id: createId(),
          package_id: pkg.id,
          pricing_group_id: pricingGroup.id,
          price: price,
          credit_amount: creditAmount,
          currency: "IDR"
        });
      }
    }

    // Insert package pricing data
    if (packagePricingData.length > 0) {
      await db.insert(package_pricing).values(packagePricingData);
      console.log(`✅ Inserted ${packagePricingData.length} package pricing records`);
      
      // Log sample data
      console.log("\n📋 Sample package pricing created:");
      packagePricingData.slice(0, 5).forEach((pricing, index) => {
        const pkg = existingPackages.find(p => p.id === pricing.package_id);
        const group = existingPricingGroups.find(g => g.id === pricing.pricing_group_id);
        console.log(`${index + 1}. ${pkg?.name} - ${group?.name}: ${pricing.currency} ${pricing.price} (${pricing.credit_amount} credits)`);
      });
    }

    console.log("🎉 Package pricing seeding completed successfully!");

  } catch (error) {
    console.error("❌ Error seeding package pricing:", error);
    throw error;
  }
}

// Run the seeding
seedPackagePricing()
  .then(() => {
    console.log("✅ Package pricing seeding finished");
    process.exit(0);
  })
  .catch((error) => {
    console.error("💥 Package pricing seeding failed:", error);
    process.exit(1);
  });
