import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';
import { PackagePricing } from '@/lib/db/schema';
import { createEntityHooks, BaseApiInterface } from '@/lib/core/base-query-hooks';
import { QueryOptions } from '@/lib/core/base-service';
import { 
  CreatePackagePricingData, 
  UpdatePackagePricingData, 
  PackagePricingFilters,
  PackagePricingStats,
  PackagePricingWithDetails
} from '@/lib/services/package-pricing.service';

// API interface for package pricing
const packagePricingApi: BaseApiInterface<PackagePricing, CreatePackagePricingData, UpdatePackagePricingData> = {
  async getAll(options?: QueryOptions): Promise<PackagePricing[]> {
    const params = new URLSearchParams();
    
    if (options?.tenantId) params.append('tenantId', options.tenantId.toString());
    if (options?.limit) params.append('limit', options.limit.toString());
    if (options?.offset) params.append('offset', options.offset.toString());
    if (options?.filters?.search) params.append('search', options.filters.search);
    if (options?.filters?.packageId) params.append('packageId', options.filters.packageId);
    if (options?.filters?.pricingGroupId) params.append('pricingGroupId', options.filters.pricingGroupId);
    if (options?.filters?.minPrice) params.append('minPrice', options.filters.minPrice.toString());
    if (options?.filters?.maxPrice) params.append('maxPrice', options.filters.maxPrice.toString());
    if (options?.filters?.currency) params.append('currency', options.filters.currency);

    const response = await fetch(`/api/package-pricing?${params.toString()}`);
    if (!response.ok) {
      throw new Error('Failed to fetch package pricing');
    }
    
    const result = await response.json();
    return result.data;
  },

  async getById(id: string): Promise<PackagePricing> {
    const response = await fetch(`/api/package-pricing/${id}`);
    if (!response.ok) {
      throw new Error('Failed to fetch package pricing');
    }
    
    const result = await response.json();
    return result.data;
  },

  async getByTenant(tenantId: number, options?: QueryOptions): Promise<PackagePricing[]> {
    const params = new URLSearchParams();
    params.append('tenantId', tenantId.toString());

    if (options?.limit) params.append('limit', options.limit.toString());
    if (options?.offset) params.append('offset', options.offset.toString());
    if (options?.filters?.search) params.append('search', options.filters.search);
    if (options?.filters?.packageId) params.append('packageId', options.filters.packageId);
    if (options?.filters?.pricingGroupId) params.append('pricingGroupId', options.filters.pricingGroupId);
    if (options?.filters?.minPrice) params.append('minPrice', options.filters.minPrice.toString());
    if (options?.filters?.maxPrice) params.append('maxPrice', options.filters.maxPrice.toString());
    if (options?.filters?.currency) params.append('currency', options.filters.currency);

    const response = await fetch(`/api/package-pricing?${params.toString()}`);
    if (!response.ok) {
      throw new Error('Failed to fetch package pricing by tenant');
    }

    const result = await response.json();
    return result.data;
  },

  async create(data: CreatePackagePricingData): Promise<PackagePricing> {
    const response = await fetch('/api/package-pricing', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to create package pricing');
    }

    const result = await response.json();
    return result.data;
  },

  async update({ id, data }: { id: string; data: UpdatePackagePricingData }): Promise<PackagePricing> {
    const response = await fetch(`/api/package-pricing/${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to update package pricing');
    }

    const result = await response.json();
    return result.data;
  },

  async delete(id: string): Promise<PackagePricing> {
    const response = await fetch(`/api/package-pricing/${id}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to delete package pricing');
    }

    const result = await response.json();
    return result.data;
  },

  async bulkOperation(data: { ids: string[]; action: string }): Promise<PackagePricing[]> {
    const response = await fetch('/api/package-pricing/bulk', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to perform bulk operation');
    }

    const result = await response.json();
    return result.data;
  },

  async getStats(tenantId?: number): Promise<PackagePricingStats> {
    const params = new URLSearchParams();
    if (tenantId) params.append('tenantId', tenantId.toString());

    const response = await fetch(`/api/package-pricing/stats?${params.toString()}`);
    if (!response.ok) {
      throw new Error('Failed to fetch package pricing statistics');
    }

    const result = await response.json();
    return result.data;
  },

  async search(query: string, options?: QueryOptions): Promise<PackagePricing[]> {
    return packagePricingApi.getAll({
      ...options,
      filters: { ...options?.filters, search: query }
    });
  },
};

// Create base hooks using the factory
const baseHooks = createEntityHooks('package-pricing', packagePricingApi);

// Custom mutation hooks with proper cache invalidation
export function useCreatePackagePricing() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: packagePricingApi.create,
    onSuccess: (newPricing) => {
      // Invalidate all package pricing related queries
      queryClient.invalidateQueries({ queryKey: ['package-pricing'] });

      // Specifically invalidate the filtered queries used by the page
      queryClient.invalidateQueries({ queryKey: ['package-pricing', 'filtered'] });
      queryClient.invalidateQueries({ queryKey: ['package-pricing', 'by-package'] });
      queryClient.invalidateQueries({ queryKey: ['package-pricing', 'by-pricing-group'] });
      queryClient.invalidateQueries({ queryKey: ['package-pricing', 'stats'] });
    },
  });
}

export function useUpdatePackagePricing() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: packagePricingApi.update,
    onSuccess: (updatedPricing) => {
      // Update cache with updated pricing
      queryClient.setQueryData(['package-pricing', 'details', updatedPricing.id], updatedPricing);

      // Invalidate all package pricing related queries
      queryClient.invalidateQueries({ queryKey: ['package-pricing'] });

      // Specifically invalidate the filtered queries used by the page
      queryClient.invalidateQueries({ queryKey: ['package-pricing', 'filtered'] });
      queryClient.invalidateQueries({ queryKey: ['package-pricing', 'by-package'] });
      queryClient.invalidateQueries({ queryKey: ['package-pricing', 'by-pricing-group'] });
      queryClient.invalidateQueries({ queryKey: ['package-pricing', 'stats'] });
    },
  });
}

export function useDeletePackagePricing() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: packagePricingApi.delete,
    onSuccess: () => {
      // Invalidate all package pricing related queries
      queryClient.invalidateQueries({ queryKey: ['package-pricing'] });

      // Specifically invalidate the filtered queries used by the page
      queryClient.invalidateQueries({ queryKey: ['package-pricing', 'filtered'] });
      queryClient.invalidateQueries({ queryKey: ['package-pricing', 'by-package'] });
      queryClient.invalidateQueries({ queryKey: ['package-pricing', 'by-pricing-group'] });
      queryClient.invalidateQueries({ queryKey: ['package-pricing', 'stats'] });
    },
  });
}

// Export other base hooks with package pricing-specific names
export const usePackagePricing = baseHooks.useEntity;
export const usePackagePricingByTenant = baseHooks.useEntitiesByTenant;
export const usePackagePricingSearch = baseHooks.useEntitySearch;
export const useBulkPackagePricingOperation = baseHooks.useBulkOperation;

/**
 * Hook for bulk create package pricing
 */
export function useBulkCreatePackagePricing() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreatePackagePricingData[]) => {
      return packagePricingApi.bulkOperation({
        action: 'create',
        data: data
      });
    },
    onSuccess: () => {
      // Invalidate all package pricing related queries
      queryClient.invalidateQueries({ queryKey: ['package-pricing'] });
    },
  });
}

/**
 * Hook for bulk delete package pricing
 */
export function useBulkDeletePackagePricing() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (ids: string[]) => {
      return packagePricingApi.bulkOperation({
        action: 'delete',
        ids: ids
      });
    },
    onSuccess: () => {
      // Invalidate all package pricing related queries
      queryClient.invalidateQueries({ queryKey: ['package-pricing'] });
    },
  });
}

// Custom hooks for package pricing-specific functionality

/**
 * Hook to fetch package pricing with filters
 */
export function usePackagePricingList(filters: PackagePricingFilters = {}, tenantId: number = 1) {
  return useQuery({
    queryKey: ['package-pricing', 'filtered', { ...filters, tenantId }],
    queryFn: async (): Promise<PackagePricingWithDetails[]> => {
      const params = new URLSearchParams();

      params.append('tenantId', tenantId.toString());
      if (filters.search) params.append('search', filters.search);
      if (filters.packageId) params.append('packageId', filters.packageId);
      if (filters.pricingGroupId) params.append('pricingGroupId', filters.pricingGroupId);
      if (filters.minPrice !== undefined) params.append('minPrice', filters.minPrice.toString());
      if (filters.maxPrice !== undefined) params.append('maxPrice', filters.maxPrice.toString());
      if (filters.currency) params.append('currency', filters.currency);

      const response = await fetch(`/api/package-pricing?${params.toString()}`);
      if (!response.ok) {
        throw new Error('Failed to fetch package pricing');
      }

      const result = await response.json();
      return result.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to fetch package pricing with details
 */
export function usePackagePricingWithDetails(id: string) {
  return useQuery({
    queryKey: ['package-pricing', 'details', id],
    queryFn: async (): Promise<PackagePricingWithDetails> => {
      const response = await fetch(`/api/package-pricing/${id}`);
      if (!response.ok) {
        throw new Error('Failed to fetch package pricing details');
      }

      const result = await response.json();
      return result.data;
    },
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to fetch package pricing by package
 */
export function usePackagePricingByPackage(packageId: string) {
  return useQuery({
    queryKey: ['package-pricing', 'by-package', packageId],
    queryFn: async (): Promise<PackagePricingWithDetails[]> => {
      const params = new URLSearchParams();
      params.append('packageId', packageId);

      const response = await fetch(`/api/package-pricing?${params.toString()}`);
      if (!response.ok) {
        throw new Error('Failed to fetch package pricing by package');
      }

      const result = await response.json();
      return result.data;
    },
    enabled: !!packageId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to fetch package pricing by pricing group
 */
export function usePackagePricingByPricingGroup(pricingGroupId: string) {
  return useQuery({
    queryKey: ['package-pricing', 'by-pricing-group', pricingGroupId],
    queryFn: async (): Promise<PackagePricingWithDetails[]> => {
      const params = new URLSearchParams();
      params.append('pricingGroupId', pricingGroupId);

      const response = await fetch(`/api/package-pricing?${params.toString()}`);
      if (!response.ok) {
        throw new Error('Failed to fetch package pricing by pricing group');
      }

      const result = await response.json();
      return result.data;
    },
    enabled: !!pricingGroupId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook for package pricing statistics
 */
export function usePackagePricingStats(tenantId?: number) {
  return useQuery({
    queryKey: ['package-pricing', 'stats', tenantId],
    queryFn: async (): Promise<PackagePricingStats> => {
      // For now, return mock stats since the endpoint doesn't exist
      return {
        total: 0,
        byPackage: {},
        byPricingGroup: {},
        averagePrice: 0,
        totalRevenue: 0,
        currencies: ['USD'],
      };
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to duplicate package pricing
 */
export function useDuplicatePackagePricing() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, newPackageId, newPricingGroupId }: { 
      id: string; 
      newPackageId?: string; 
      newPricingGroupId?: string; 
    }) => {
      const response = await fetch(`/api/package-pricing/${id}/duplicate`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ newPackageId, newPricingGroupId }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to duplicate package pricing');
      }

      const result = await response.json();
      return result.data;
    },
    onSuccess: (newPricing) => {
      // Update cache with new pricing
      queryClient.setQueryData(['package-pricing', 'details', newPricing.id], newPricing);

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['package-pricing', 'filtered'] });
      queryClient.invalidateQueries({ queryKey: ['package-pricing', 'by-package'] });
      queryClient.invalidateQueries({ queryKey: ['package-pricing', 'by-pricing-group'] });
      queryClient.invalidateQueries({ queryKey: ['package-pricing', 'stats'] });
    },
  });
}

/**
 * Hook to reorder package pricing with optimistic updates
 */
export function useReorderPackagePricing() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ tenantId, items }: {
      tenantId: number;
      items: { id: string; sortOrder: number }[]
    }): Promise<PackagePricing[]> => {
      const response = await fetch('/api/package-pricing/reorder', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ tenantId, items }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to reorder package pricing');
      }

      const result = await response.json();
      return result.data;
    },
    // Optimistic update
    onMutate: async ({ tenantId, items }) => {
      // Cancel any outgoing refetches (so they don't overwrite our optimistic update)
      await queryClient.cancelQueries({ queryKey: ['package-pricing'] });

      // Snapshot the previous value
      const previousData = queryClient.getQueriesData({ queryKey: ['package-pricing'] });

      // Optimistically update all package pricing queries
      queryClient.setQueriesData(
        { queryKey: ['package-pricing'] },
        (old: PackagePricingWithDetails[] | undefined) => {
          if (!old) return old;

          // Create a map of new sort orders
          const sortOrderMap = new Map(items.map(item => [item.id, item.sortOrder]));

          // Update the sort orders and re-sort
          const updated = old.map(item => ({
            ...item,
            sortOrder: sortOrderMap.get(item.id) ?? item.sortOrder ?? 0
          }));

          // Sort by the new sort order
          return updated.sort((a, b) => (a.sortOrder ?? 0) - (b.sortOrder ?? 0));
        }
      );

      // Return a context object with the snapshotted value
      return { previousData };
    },
    // If the mutation fails, use the context returned from onMutate to roll back
    onError: (err, variables, context) => {
      if (context?.previousData) {
        // Restore all previous query data
        context.previousData.forEach(([queryKey, data]) => {
          queryClient.setQueryData(queryKey, data);
        });
      }
    },
    // Always refetch after error or success to ensure we have the latest data
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: ['package-pricing'] });
    },
  });
}

// Export query keys for external use
export const packagePricingKeys = baseHooks.queryKeys;

// Export types for external use
export type { 
  CreatePackagePricingData, 
  UpdatePackagePricingData, 
  PackagePricingFilters, 
  PackagePricingStats,
  PackagePricingWithDetails
};
