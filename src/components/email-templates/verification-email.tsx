import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  <PERSON>ing,
  Html,
  Img,
  Link,
  Preview,
  Section,
  Text,
} from "@react-email/components";
import * as React from "react";

interface VerificationEmailProps {
  name: string;
  verificationUrl: string;
}

export const VerificationEmail = ({
  name = "User",
  verificationUrl = "https://example.com/verify",
}: VerificationEmailProps) => (
  <Html>
    <Head />
    <Preview>Verify your email address</Preview>
    <Body style={main}>
      <Container style={container}>
        <Section style={logoContainer}>
          <Img
            src="https://your-app.com/logo.png"
            width="120"
            height="36"
            alt="Your App"
            style={logo}
          />
        </Section>
        <Heading style={h1}>Verify your email address</Heading>
        <Text style={heroText}>
          Hi {name}, welcome to Your SaaS App! Please verify your email address to get started.
        </Text>
        <Section style={codeBox}>
          <Text style={confirmationCodeText}>
            Click the button below to verify your email address:
          </Text>
        </Section>
        <Section style={buttonContainer}>
          <Button style={button} href={verificationUrl}>
            Verify Email Address
          </Button>
        </Section>
        <Text style={text}>
          If you didn't create an account with us, you can safely ignore this email.
        </Text>
        <Text style={text}>
          If the button doesn't work, you can copy and paste this link into your browser:
        </Text>
        <Link href={verificationUrl} style={link}>
          {verificationUrl}
        </Link>
        <Text style={footer}>
          Best regards,<br />
          The Your SaaS App Team
        </Text>
      </Container>
    </Body>
  </Html>
);

export default VerificationEmail;

const main = {
  backgroundColor: "#ffffff",
  fontFamily:
    '-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,Oxygen-Sans,Ubuntu,Cantarell,"Helvetica Neue",sans-serif',
};

const container = {
  margin: "0 auto",
  padding: "20px 0 48px",
  maxWidth: "560px",
};

const logoContainer = {
  marginTop: "32px",
};

const logo = {
  margin: "0 auto",
};

const h1 = {
  color: "#24292f",
  fontSize: "24px",
  fontWeight: "normal",
  margin: "30px 0",
  padding: "0",
  textAlign: "center" as const,
};

const heroText = {
  color: "#24292f",
  fontSize: "16px",
  lineHeight: "1.5",
  margin: "16px 0",
};

const codeBox = {
  background: "rgba(0,0,0,.05)",
  borderRadius: "4px",
  margin: "16px 0",
  padding: "16px",
};

const confirmationCodeText = {
  fontSize: "14px",
  margin: "0",
  textAlign: "center" as const,
};

const buttonContainer = {
  margin: "27px 0 40px",
};

const button = {
  backgroundColor: "#5e6ad2",
  borderRadius: "3px",
  color: "#fff",
  fontSize: "16px",
  textDecoration: "none",
  textAlign: "center" as const,
  display: "block",
  padding: "12px",
};

const text = {
  color: "#24292f",
  fontSize: "14px",
  lineHeight: "1.5",
  margin: "16px 0",
};

const link = {
  color: "#5e6ad2",
  fontSize: "14px",
  textDecoration: "underline",
};

const footer = {
  color: "#6a737d",
  fontSize: "12px",
  lineHeight: "1.5",
  marginTop: "40px",
};
