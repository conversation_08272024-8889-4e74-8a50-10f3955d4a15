import { NextRequest, NextResponse } from "next/server";
import { VoucherService } from "@/lib/services/voucher.service";
import { db } from "@/lib/db";
import { vouchers, voucher_usage } from "@/lib/db/schema";
import { eq, and, sql } from "drizzle-orm";
import { withRBAC } from "@/lib/middleware/rbac-middleware";

// GET /api/vouchers/stats - Get general voucher statistics
export async function GET(request: NextRequest) {
  return withRBAC(
    async () => {
      try {
        const { searchParams } = new URL(request.url);
        const tenantId = searchParams.get("tenantId");

        if (!tenantId) {
          return NextResponse.json(
            { error: "Tenant ID is required" },
            { status: 400 }
          );
        }

        const tenantIdNum = parseInt(tenantId);

        // Get general voucher statistics
        const [voucherStats] = await db
          .select({
            total: sql<number>`count(*)`,
            active: sql<number>`count(*) filter (where ${vouchers.is_active} = true)`,
            inactive: sql<number>`count(*) filter (where ${vouchers.is_active} = false)`,
            expired: sql<number>`count(*) filter (where ${vouchers.valid_until} < now())`,
            public: sql<number>`count(*) filter (where ${vouchers.is_public} = true)`,
            private: sql<number>`count(*) filter (where ${vouchers.is_public} = false)`,
          })
          .from(vouchers)
          .where(eq(vouchers.tenantId, tenantIdNum));

        // Get usage statistics
        const [usageStats] = await db
          .select({
            total_usage: sql<number>`count(*)`,
            unique_customers: sql<number>`count(distinct ${voucher_usage.customer_id})`,
            total_discount: sql<number>`sum(${voucher_usage.discount_amount})`,
            average_discount: sql<number>`avg(${voucher_usage.discount_amount})`,
          })
          .from(voucher_usage)
          .innerJoin(vouchers, eq(voucher_usage.voucher_id, vouchers.id))
          .where(eq(vouchers.tenantId, tenantIdNum));

        // Get voucher type breakdown
        const typeBreakdown = await db
          .select({
            type: vouchers.type,
            count: sql<number>`count(*)`,
          })
          .from(vouchers)
          .where(eq(vouchers.tenantId, tenantIdNum))
          .groupBy(vouchers.type);

        // Get top performing vouchers
        const topVouchers = await db
          .select({
            id: vouchers.id,
            code: vouchers.code,
            name: vouchers.name,
            type: vouchers.type,
            usage_count: sql<number>`count(${voucher_usage.id})`,
            total_discount: sql<number>`sum(${voucher_usage.discount_amount})`,
          })
          .from(vouchers)
          .leftJoin(voucher_usage, eq(vouchers.id, voucher_usage.voucher_id))
          .where(eq(vouchers.tenantId, tenantIdNum))
          .groupBy(vouchers.id, vouchers.code, vouchers.name, vouchers.type)
          .orderBy(sql`count(${voucher_usage.id}) desc`)
          .limit(5);

        return NextResponse.json({
          success: true,
          data: {
            overview: {
              total: voucherStats.total || 0,
              active: voucherStats.active || 0,
              inactive: voucherStats.inactive || 0,
              expired: voucherStats.expired || 0,
              public: voucherStats.public || 0,
              private: voucherStats.private || 0,
            },
            usage: {
              total_usage: usageStats.total_usage || 0,
              unique_customers: usageStats.unique_customers || 0,
              total_discount: usageStats.total_discount || 0,
              average_discount: Math.round(usageStats.average_discount || 0),
            },
            type_breakdown: typeBreakdown.map(item => ({
              type: item.type,
              count: item.count,
            })),
            top_vouchers: topVouchers.map(voucher => ({
              id: voucher.id,
              code: voucher.code,
              name: voucher.name,
              type: voucher.type,
              usage_count: voucher.usage_count || 0,
              total_discount: voucher.total_discount || 0,
            })),
          },
        });
      } catch (error) {
        console.error("Error fetching voucher statistics:", error);
        return NextResponse.json(
          { 
            error: error instanceof Error ? error.message : "Failed to fetch voucher statistics",
            success: false 
          },
          { status: 500 }
        );
      }
    },
    { module: "vouchers", action: "read" }
  )(request);
}
