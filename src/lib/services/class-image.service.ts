import { db } from "@/lib/db";
import { class_images, classes, type ClassImage, type NewClassImage } from "@/lib/db/schema";
import { eq, and, desc, sql } from "drizzle-orm";
import { createId } from "@paralleldrive/cuid2";

/**
 * Class Image Service
 * 
 * Service untuk manage class images dengan pattern simple seperti ClassService.
 * Ini kayak "manager" yang handle semua operasi CRUD untuk class images.
 * 
 * Pattern ini udah terbukti berhasil tanpa bug di classes, categories, dan subcategories.
 */

// Interface untuk search results
export interface ClassImageSearchResult {
  images: ClassImage[];
  total: number;
  hasMore: boolean;
}

/**
 * ClassImageService - Service untuk manage class images
 * 
 * Menggunakan static methods pattern yang simple dan proven.
 * Tidak pakai BaseService yang kompleks, langsung ke database dengan Drizzle ORM.
 */
export class ClassImageService {
  /**
   * Get all images for a specific class
   * 
   * Ambil semua images untuk class tertentu.
   * Ini method utama yang akan sering dipake.
   */
  static async getByClassId(classId: string): Promise<ClassImage[]> {
    return await db
      .select()
      .from(class_images)
      .where(eq(class_images.class_id, classId))
      .orderBy(desc(class_images.createdAt));
  }

  /**
   * Get class image by ID
   * 
   * Ambil single image berdasarkan ID.
   * Return null kalau tidak ditemukan.
   */
  static async getById(id: string): Promise<ClassImage | null> {
    const [imageResult] = await db
      .select()
      .from(class_images)
      .where(eq(class_images.id, id))
      .limit(1);
    return imageResult || null;
  }

  /**
   * Create class image
   * 
   * Buat image baru untuk class dengan validation.
   * ID akan di-generate otomatis dengan createId().
   */
  static async create(data: {
    class_id: string;
    image_url: string;
  }): Promise<ClassImage> {
    // Validate that class exists before creating image
    const [classExists] = await db
      .select({ id: classes.id })
      .from(classes)
      .where(eq(classes.id, data.class_id))
      .limit(1);
    
    if (!classExists) {
      throw new Error(`Class with ID ${data.class_id} not found`);
    }

    // Validate image URL format
    if (!data.image_url || data.image_url.trim().length === 0) {
      throw new Error("Image URL is required");
    }

    // Basic URL validation
    try {
      new URL(data.image_url);
    } catch {
      throw new Error("Invalid image URL format");
    }

    const [imageResult] = await db
      .insert(class_images)
      .values({
        id: createId(),
        class_id: data.class_id,
        image_url: data.image_url.trim(),
      })
      .returning();
    return imageResult;
  }

  /**
   * Update class image
   * 
   * Update image dengan data baru.
   * Hanya field yang di-provide yang akan di-update.
   */
  static async update(id: string, data: {
    image_url?: string;
  }): Promise<ClassImage | null> {
    // Validate image URL if provided
    if (data.image_url) {
      if (data.image_url.trim().length === 0) {
        throw new Error("Image URL cannot be empty");
      }

      // Basic URL validation
      try {
        new URL(data.image_url);
      } catch {
        throw new Error("Invalid image URL format");
      }
    }

    const updateData: Partial<NewClassImage> = {};
    if (data.image_url) {
      updateData.image_url = data.image_url.trim();
    }

    const [imageResult] = await db
      .update(class_images)
      .set(updateData)
      .where(eq(class_images.id, id))
      .returning();
    return imageResult || null;
  }

  /**
   * Delete class image
   * 
   * Hapus image berdasarkan ID.
   * Return image yang dihapus untuk konfirmasi.
   */
  static async delete(id: string): Promise<ClassImage> {
    // First check if image exists
    const existingImage = await this.getById(id);
    if (!existingImage) {
      throw new Error("Class image not found");
    }

    // Delete the image and return the deleted record
    const [deletedImage] = await db
      .delete(class_images)
      .where(eq(class_images.id, id))
      .returning();

    if (!deletedImage) {
      throw new Error("Failed to delete class image");
    }

    return deletedImage;
  }

  /**
   * Get images with class info
   * 
   * Ambil images beserta info class-nya.
   * Berguna untuk display yang lebih informatif.
   */
  static async getWithClassInfo(classId: string) {
    const result = await db
      .select({
        image: class_images,
        class: classes,
      })
      .from(class_images)
      .leftJoin(classes, eq(class_images.class_id, classes.id))
      .where(eq(class_images.class_id, classId))
      .orderBy(desc(class_images.createdAt));
    
    return result;
  }

  /**
   * Count images for a class
   * 
   * Hitung jumlah images untuk class tertentu.
   * Berguna untuk validation atau display info.
   */
  static async countByClassId(classId: string): Promise<number> {
    const [result] = await db
      .select({ count: sql<number>`count(*)` })
      .from(class_images)
      .where(eq(class_images.class_id, classId));
    
    return Number(result?.count || 0);
  }

  /**
   * Delete all images for a class
   * 
   * Hapus semua images untuk class tertentu.
   * Berguna ketika class dihapus atau cleanup.
   */
  static async deleteByClassId(classId: string): Promise<ClassImage[]> {
    const deletedImages = await db
      .delete(class_images)
      .where(eq(class_images.class_id, classId))
      .returning();
    
    return deletedImages;
  }

  /**
   * Validate image file
   * 
   * Validate image file sebelum upload.
   * Check file type, size, dll.
   */
  static validateImageFile(file: File): { valid: boolean; error?: string } {
    // Check file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return {
        valid: false,
        error: 'Invalid file type. Only JPEG, PNG, and WebP images are allowed.'
      };
    }

    // Check file size (max 5MB)
    const maxSize = 5 * 1024 * 1024; // 5MB in bytes
    if (file.size > maxSize) {
      return {
        valid: false,
        error: 'File size too large. Maximum size is 5MB.'
      };
    }

    return { valid: true };
  }

  /**
   * Get image stats for a class
   * 
   * Ambil statistik images untuk class.
   * Berguna untuk dashboard atau monitoring.
   */
  static async getImageStats(classId: string) {
    const images = await this.getByClassId(classId);
    
    return {
      totalImages: images.length,
      latestUpload: images.length > 0 ? images[0].createdAt : null,
      oldestUpload: images.length > 0 ? images[images.length - 1].createdAt : null,
    };
  }
}
