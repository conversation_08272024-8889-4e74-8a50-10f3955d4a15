/**
 * Validation Middleware for Public API
 * 
 * FAANG-level input validation with:
 * - Zod schema validation
 * - Request sanitization
 * - Content-type validation
 * - Size limits enforcement
 * - Comprehensive error reporting
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { ValidationError, validateInput } from '../validation-types';
import { APIError } from '../auth-types';
import { createId } from '@paralleldrive/cuid2';

interface ValidationConfig {
  bodySchema?: z.ZodSchema;
  querySchema?: z.ZodSchema;
  paramsSchema?: z.ZodSchema;
  maxBodySize?: number; // bytes
  allowedContentTypes?: string[];
  sanitizeInput?: boolean;
}

interface ValidationResult {
  isValid: boolean;
  data?: {
    body?: any;
    query?: any;
    params?: any;
  };
  errors?: ValidationError[];
  response?: NextResponse;
}

export class ValidationMiddleware {
  private readonly defaultMaxBodySize = 1024 * 1024; // 1MB
  private readonly defaultContentTypes = ['application/json'];

  /**
   * Validate request data against schemas
   */
  async validate(
    request: NextRequest,
    config: ValidationConfig
  ): Promise<ValidationResult> {
    const requestId = request.headers.get('X-Request-ID') || createId();
    const errors: ValidationError[] = [];

    try {
      // Validate content type
      if (config.allowedContentTypes || this.defaultContentTypes) {
        const contentType = request.headers.get('content-type');
        const allowedTypes = config.allowedContentTypes || this.defaultContentTypes;
        
        if (contentType && !allowedTypes.some(type => contentType.includes(type))) {
          return {
            isValid: false,
            response: this.createValidationErrorResponse(
              'Invalid content type',
              'INVALID_CONTENT_TYPE',
              requestId,
              { 
                received: contentType, 
                allowed: allowedTypes 
              }
            ),
          };
        }
      }

      // Validate body size
      const maxSize = config.maxBodySize || this.defaultMaxBodySize;
      const contentLength = request.headers.get('content-length');
      if (contentLength && parseInt(contentLength) > maxSize) {
        return {
          isValid: false,
          response: this.createValidationErrorResponse(
            'Request body too large',
            'BODY_TOO_LARGE',
            requestId,
            { 
              size: parseInt(contentLength), 
              maxSize 
            }
          ),
        };
      }

      const validatedData: any = {};

      // Validate request body
      if (config.bodySchema) {
        try {
          const body = await this.parseRequestBody(request);
          const sanitizedBody = config.sanitizeInput ? this.sanitizeObject(body) : body;
          validatedData.body = validateInput(config.bodySchema, sanitizedBody);
        } catch (error) {
          if (error instanceof ValidationError) {
            errors.push(error);
          } else {
            return {
              isValid: false,
              response: this.createValidationErrorResponse(
                'Invalid JSON in request body',
                'INVALID_JSON',
                requestId
              ),
            };
          }
        }
      }

      // Validate query parameters
      if (config.querySchema) {
        try {
          const query = this.parseQueryParams(request);
          const sanitizedQuery = config.sanitizeInput ? this.sanitizeObject(query) : query;
          validatedData.query = validateInput(config.querySchema, sanitizedQuery);
        } catch (error) {
          if (error instanceof ValidationError) {
            errors.push(error);
          }
        }
      }

      // Validate URL parameters
      if (config.paramsSchema) {
        try {
          const params = this.parseUrlParams(request);
          const sanitizedParams = config.sanitizeInput ? this.sanitizeObject(params) : params;
          validatedData.params = validateInput(config.paramsSchema, sanitizedParams);
        } catch (error) {
          if (error instanceof ValidationError) {
            errors.push(error);
          }
        }
      }

      // Return errors if any validation failed
      if (errors.length > 0) {
        return {
          isValid: false,
          errors,
          response: this.createValidationErrorResponse(
            'Validation failed',
            'VALIDATION_FAILED',
            requestId,
            this.formatValidationErrors(errors)
          ),
        };
      }

      return {
        isValid: true,
        data: validatedData,
      };

    } catch (error) {
      console.error('Validation middleware error:', error);
      
      return {
        isValid: false,
        response: this.createValidationErrorResponse(
          'Internal validation error',
          'VALIDATION_INTERNAL_ERROR',
          requestId
        ),
      };
    }
  }

  /**
   * Parse request body safely
   */
  private async parseRequestBody(request: NextRequest): Promise<any> {
    const contentType = request.headers.get('content-type') || '';
    
    if (contentType.includes('application/json')) {
      try {
        return await request.json();
      } catch (error) {
        throw new Error('Invalid JSON format');
      }
    }
    
    if (contentType.includes('application/x-www-form-urlencoded')) {
      const formData = await request.formData();
      const data: Record<string, any> = {};
      
      formData.forEach((value, key) => {
        data[key] = value;
      });
      
      return data;
    }
    
    // For other content types, return raw text
    return await request.text();
  }

  /**
   * Parse query parameters
   */
  private parseQueryParams(request: NextRequest): Record<string, any> {
    const url = new URL(request.url);
    const params: Record<string, any> = {};
    
    url.searchParams.forEach((value, key) => {
      // Handle array parameters (key[]=value1&key[]=value2)
      if (key.endsWith('[]')) {
        const arrayKey = key.slice(0, -2);
        if (!params[arrayKey]) {
          params[arrayKey] = [];
        }
        params[arrayKey].push(this.parseValue(value));
      } else {
        // Handle multiple values for same key
        if (params[key]) {
          if (Array.isArray(params[key])) {
            params[key].push(this.parseValue(value));
          } else {
            params[key] = [params[key], this.parseValue(value)];
          }
        } else {
          params[key] = this.parseValue(value);
        }
      }
    });
    
    return params;
  }

  /**
   * Parse URL parameters from dynamic routes
   */
  private parseUrlParams(request: NextRequest): Record<string, string> {
    // This would be populated by Next.js routing
    // For now, return empty object as params are handled by Next.js
    return {};
  }

  /**
   * Parse string value to appropriate type
   */
  private parseValue(value: string): any {
    // Try to parse as number
    if (/^\d+$/.test(value)) {
      return parseInt(value, 10);
    }
    
    if (/^\d*\.\d+$/.test(value)) {
      return parseFloat(value);
    }
    
    // Try to parse as boolean
    if (value === 'true') return true;
    if (value === 'false') return false;
    
    // Return as string
    return value;
  }

  /**
   * Sanitize object to prevent XSS and injection attacks
   */
  private sanitizeObject(obj: any): any {
    if (obj === null || obj === undefined) {
      return obj;
    }
    
    if (typeof obj === 'string') {
      return this.sanitizeString(obj);
    }
    
    if (Array.isArray(obj)) {
      return obj.map(item => this.sanitizeObject(item));
    }
    
    if (typeof obj === 'object') {
      const sanitized: Record<string, any> = {};
      
      Object.keys(obj).forEach(key => {
        const sanitizedKey = this.sanitizeString(key);
        sanitized[sanitizedKey] = this.sanitizeObject(obj[key]);
      });
      
      return sanitized;
    }
    
    return obj;
  }

  /**
   * Sanitize string input
   */
  private sanitizeString(str: string): string {
    return str
      .replace(/[<>]/g, '') // Remove potential HTML tags
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/on\w+=/gi, '') // Remove event handlers
      .trim();
  }

  /**
   * Format validation errors for response
   */
  private formatValidationErrors(errors: ValidationError[]): any {
    return {
      errors: errors.flatMap(error => 
        error.issues.map(issue => ({
          field: issue.path.join('.'),
          message: issue.message,
          code: issue.code,
          received: issue.received,
        }))
      ),
    };
  }

  /**
   * Create validation error response
   */
  private createValidationErrorResponse(
    message: string,
    code: string,
    requestId: string,
    details?: any
  ): NextResponse {
    const error: APIError = {
      code,
      message,
      details: {
        requestId,
        timestamp: new Date().toISOString(),
        ...details,
      },
    };

    return NextResponse.json(
      {
        success: false,
        error,
        metadata: {
          requestId,
          timestamp: new Date().toISOString(),
          version: 'v1',
          processingTime: 0,
        },
      },
      { status: 400 }
    );
  }

  /**
   * Validate pagination parameters
   */
  validatePagination(query: any): {
    page: number;
    limit: number;
    offset: number;
  } {
    const page = Math.max(1, parseInt(query.page) || 1);
    const limit = Math.min(100, Math.max(1, parseInt(query.limit) || 20));
    const offset = (page - 1) * limit;

    return { page, limit, offset };
  }

  /**
   * Validate sort parameters
   */
  validateSort(query: any, allowedFields: string[]): {
    sortField?: string;
    sortOrder: 'asc' | 'desc';
  } {
    const sortField = query.sort;
    const sortOrder = query.order === 'desc' ? 'desc' : 'asc';

    if (sortField && !allowedFields.includes(sortField)) {
      throw new ValidationError([{
        code: 'invalid_enum_value',
        message: `Sort field must be one of: ${allowedFields.join(', ')}`,
        path: ['sort'],
        received: sortField,
      }]);
    }

    return {
      sortField: allowedFields.includes(sortField) ? sortField : undefined,
      sortOrder,
    };
  }

  /**
   * Validate date range parameters
   */
  validateDateRange(query: any): {
    startDate?: Date;
    endDate?: Date;
  } {
    let startDate: Date | undefined;
    let endDate: Date | undefined;

    if (query.startDate) {
      startDate = new Date(query.startDate);
      if (isNaN(startDate.getTime())) {
        throw new ValidationError([{
          code: 'invalid_date',
          message: 'Invalid start date format',
          path: ['startDate'],
          received: query.startDate,
        }]);
      }
    }

    if (query.endDate) {
      endDate = new Date(query.endDate);
      if (isNaN(endDate.getTime())) {
        throw new ValidationError([{
          code: 'invalid_date',
          message: 'Invalid end date format',
          path: ['endDate'],
          received: query.endDate,
        }]);
      }
    }

    if (startDate && endDate && startDate > endDate) {
      throw new ValidationError([{
        code: 'invalid_date_range',
        message: 'Start date must be before end date',
        path: ['startDate'],
        received: query.startDate,
      }]);
    }

    return { startDate, endDate };
  }
}
