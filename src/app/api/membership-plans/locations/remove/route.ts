import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { MembershipPlanLocationService } from "@/lib/services/membership-plan-location-service";
import { withRBAC } from "@/lib/middleware/rbac-middleware";
import { z } from "zod";

const removeLocationSchema = z.object({
  membershipPlanId: z.string().min(1, "Membership plan ID is required"),
  locationIds: z.array(z.string()).min(1, "At least one location ID is required"),
  tenantId: z.number().int().positive("Valid tenant ID is required"),
});

async function removeFromLocationsHandler(req: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.tenantId) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await req.json();
    const validatedData = removeLocationSchema.parse(body);

    // Ensure user can only manage their own tenant's data
    const userTenantId = parseInt(session.user.tenantId);
    if (validatedData.tenantId !== userTenantId) {
      return NextResponse.json(
        { error: "Access denied to this tenant's data" },
        { status: 403 }
      );
    }

    const result = await MembershipPlanLocationService.removeFromLocations(
      validatedData.membershipPlanId,
      validatedData.locationIds,
      validatedData.tenantId
    );

    return NextResponse.json({
      success: true,
      message: "Membership plan removed from locations successfully",
      data: result,
    });
  } catch (error) {
    console.error("Error removing membership plan from locations:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to remove from locations" },
      { status: 500 }
    );
  }
}

// Protect with RBAC - require packages.manage permission
export const POST = withRBAC(removeFromLocationsHandler, {
  module: "packages",
  action: "manage"
});
