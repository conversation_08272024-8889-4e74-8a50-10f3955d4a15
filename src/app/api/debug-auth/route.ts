import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth";

// Debug endpoint to check authentication status
export async function GET(request: NextRequest) {
  try {
    console.log("🔍 Debug Auth - Checking session...");

    const session = await auth();
    
    console.log("Session data:", {
      hasSession: !!session,
      userId: session?.userId,
      email: session?.email,
      sessionData: session
    });

    return NextResponse.json({
      success: true,
      data: {
        authenticated: !!session,
        session: session,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error("❌ Debug auth failed:", error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 });
  }
}
