# 📚 Next.js SaaS Application - Complete API & Routes Documentation

This document provides a comprehensive catalog of all URLs and endpoints available in this Next.js SaaS application, organized by feature modules and functionality.

## 🔗 Table of Contents

- [Authentication & Authorization](#authentication--authorization)
- [Voucher Management](#voucher-management)
- [Customer Management](#customer-management)
- [Class Management](#class-management)
- [Package Management](#package-management)
- [Location & Facility Management](#location--facility-management)
- [User & Role Management](#user--role-management)
- [Business & Organization Management](#business--organization-management)
- [Dashboard & Frontend Routes](#dashboard--frontend-routes)
- [Utility & System Endpoints](#utility--system-endpoints)

---

## 🔐 Authentication & Authorization

### API Endpoints

| Method | Endpoint | Description | RBAC Required |
|--------|----------|-------------|---------------|
| `GET/POST` | `/api/auth/[...nextauth]` | NextAuth.js authentication handlers | None |
| `POST` | `/api/auth/register` | User registration | None |
| `POST` | `/api/auth/verify-email` | Email verification | None |
| `GET` | `/api/auth/rbac` | Load RBAC data for current user | Authenticated |

### Frontend Routes

| Route | Description | Access Level |
|-------|-------------|--------------|
| `/auth/signin` | Sign in page | Public |
| `/auth/signup` | Sign up page | Public |
| `/auth/forgot-password` | Password reset page | Public |

---

## 🎫 Voucher Management

### API Endpoints

| Method | Endpoint | Description | RBAC Required |
|--------|----------|-------------|---------------|
| `GET` | `/api/vouchers` | List vouchers with filters | `vouchers.read` |
| `POST` | `/api/vouchers` | Create new voucher | `vouchers.create` |
| `GET` | `/api/vouchers/[id]` | Get single voucher | `vouchers.read` |
| `PUT` | `/api/vouchers/[id]` | Update voucher | `vouchers.update` |
| `DELETE` | `/api/vouchers/[id]` | Delete voucher | `vouchers.delete` |
| `PATCH` | `/api/vouchers/[id]?action=toggle-active` | Toggle voucher status | `vouchers.update` |
| `GET` | `/api/vouchers/[id]/stats` | Get voucher statistics | `vouchers.read` |
| `POST` | `/api/vouchers/validate` | Validate voucher for use | `vouchers.use` |
| `POST` | `/api/vouchers/usage` | Record voucher usage | `vouchers.use` |
| `GET` | `/api/vouchers/available` | Get available vouchers for customer | `vouchers.read` |
| `GET` | `/api/vouchers/stats` | Get general voucher statistics | `vouchers.read` |
| `GET` | `/api/vouchers/find-by-code` | Find voucher by code | `vouchers.read` |

### Frontend Routes

| Route | Description | Access Level |
|-------|-------------|--------------|
| `/vouchers` | Voucher management interface | `vouchers.manage` |

### Query Parameters

**GET /api/vouchers**
- `tenantId` (required): Tenant ID for isolation
- `limit`: Maximum results to return
- `offset`: Pagination offset
- `search`: Search in code, name, description
- `type`: Filter by voucher type (`percentage`, `fixed_amount`, `free_shipping`, `buy_x_get_y`)
- `isActive`: Filter by active status (`true`/`false`)
- `isPublic`: Filter by public status (`true`/`false`)
- `validOnly`: Only return currently valid vouchers (`true`)

---

## 👥 Customer Management

### API Endpoints

| Method | Endpoint | Description | RBAC Required |
|--------|----------|-------------|---------------|
| `GET` | `/api/customers` | List customers with filters | `customers.read` |
| `POST` | `/api/customers` | Create new customer | `customers.create` |
| `GET` | `/api/customers/[id]` | Get single customer | `customers.read` |
| `PUT` | `/api/customers/[id]` | Update customer | `customers.update` |
| `DELETE` | `/api/customers/[id]` | Delete customer | `customers.delete` |
| `GET` | `/api/customer-segments/stats` | Get customer segment statistics | `customers.read` |

### Frontend Routes

| Route | Description | Access Level |
|-------|-------------|--------------|
| `/customers` | Customer management interface | `customers.manage` |

---

## 📚 Class Management

### API Endpoints

| Method | Endpoint | Description | RBAC Required |
|--------|----------|-------------|---------------|
| `GET` | `/api/classes` | List classes | `classes.read` |
| `POST` | `/api/classes` | Create new class | `classes.create` |
| `GET` | `/api/classes/[id]` | Get single class | `classes.read` |
| `PUT` | `/api/classes/[id]` | Update class | `classes.update` |
| `DELETE` | `/api/classes/[id]` | Delete class | `classes.delete` |
| `GET` | `/api/class-schedules` | List class schedules | `class-schedules.read` |
| `POST` | `/api/class-schedules` | Create class schedule | `class-schedules.create` |
| `GET` | `/api/class-bookings` | List class bookings | `bookings.read` |
| `POST` | `/api/class-bookings` | Create class booking | `bookings.create` |
| `POST` | `/api/class-bookings/actions` | Handle booking actions (check-in, cancel, bulk) | `bookings.manage` |
| `GET` | `/api/class-categories` | List class categories | `classes.read` |
| `POST` | `/api/class-categories` | Create class category | `classes.create` |
| `GET` | `/api/class-subcategories` | List class subcategories | `classes.read` |
| `POST` | `/api/class-subcategories` | Create class subcategory | `classes.create` |
| `GET` | `/api/class-levels` | List class levels | `class-level.read` |
| `POST` | `/api/class-levels` | Create class level | `class-level.create` |
| `GET` | `/api/class-images` | List class images | `classes.read` |
| `POST` | `/api/class-images` | Upload class image | `classes.update` |

### Frontend Routes

| Route | Description | Access Level |
|-------|-------------|--------------|
| `/classes` | Class management interface | `classes.manage` |
| `/class-schedules` | Class schedule management | `class-schedules.manage` |
| `/class-bookings` | Class booking management | `bookings.manage` |
| `/class-levels` | Class level management | `class-level.manage` |
| `/class-categories` | Class category management | `classes.manage` |
| `/class-subcategories` | Class subcategory management | `classes.manage` |

---

## 📦 Package Management

### API Endpoints

| Method | Endpoint | Description | RBAC Required |
|--------|----------|-------------|---------------|
| `GET` | `/api/packages` | List packages | `packages.read` |
| `POST` | `/api/packages` | Create new package | `packages.create` |
| `GET` | `/api/packages/[id]` | Get single package | `packages.read` |
| `PUT` | `/api/packages/[id]` | Update package | `packages.update` |
| `DELETE` | `/api/packages/[id]` | Delete package | `packages.delete` |
| `GET` | `/api/package-categories` | List package categories | `packages.read` |
| `POST` | `/api/package-categories` | Create package category | `packages.create` |
| `GET` | `/api/package-locations` | List package locations | `packages.read` |
| `POST` | `/api/package-locations` | Create package location | `packages.create` |
| `GET` | `/api/package-pricing` | List package pricing | `packages.read` |
| `POST` | `/api/package-pricing` | Create package pricing | `packages.create` |
| `GET` | `/api/package-purchase-options` | List purchase options | `packages.read` |
| `POST` | `/api/package-purchase-options` | Create purchase option | `packages.create` |
| `GET` | `/api/pricing-groups` | List pricing groups | `packages.read` |
| `POST` | `/api/pricing-groups` | Create pricing group | `packages.create` |
| `GET` | `/api/membership-plans` | List membership plans | `packages.read` |
| `POST` | `/api/membership-plans` | Create membership plan | `packages.create` |

### Frontend Routes

| Route | Description | Access Level |
|-------|-------------|--------------|
| `/packages` | Package management interface | `packages.manage` |
| `/package-categories` | Package category management | `packages.manage` |
| `/package-locations` | Package location management | `packages.manage` |
| `/package-pricing` | Package pricing management | `packages.manage` |
| `/pricing-groups` | Pricing group management | `packages.manage` |
| `/membership-plans` | Membership plan management | `packages.manage` |

---

## 🏢 Location & Facility Management

### API Endpoints

| Method | Endpoint | Description | RBAC Required |
|--------|----------|-------------|---------------|
| `GET` | `/api/locations` | List locations | `locations.read` |
| `POST` | `/api/locations` | Create new location | `locations.create` |
| `GET` | `/api/locations/[id]` | Get single location | `locations.read` |
| `PUT` | `/api/locations/[id]` | Update location | `locations.update` |
| `DELETE` | `/api/locations/[id]` | Delete location | `locations.delete` |
| `GET` | `/api/facilities` | List facilities | `facilities.read` |
| `POST` | `/api/facilities` | Create new facility | `facilities.create` |
| `GET` | `/api/equipments` | List equipment | `equipment.read` |
| `POST` | `/api/equipments` | Create new equipment | `equipment.create` |
| `GET` | `/api/equipment-instances` | List equipment instances | `equipment-instances.read` |
| `POST` | `/api/equipment-instances` | Create equipment instance | `equipment-instances.create` |
| `GET` | `/api/addresses` | List addresses | `locations.read` |
| `POST` | `/api/addresses` | Create new address | `locations.create` |

### Frontend Routes

| Route | Description | Access Level |
|-------|-------------|--------------|
| `/locations` | Location management interface | `locations.manage` |
| `/facilities` | Facility management interface | `facilities.manage` |
| `/equipment` | Equipment management interface | `equipment.manage` |
| `/equipment-instances` | Equipment instance management | `equipment-instances.manage` |

---

## 👤 User & Role Management

### API Endpoints

| Method | Endpoint | Description | RBAC Required |
|--------|----------|-------------|---------------|
| `GET` | `/api/users` | List all users | Admin only |
| `POST` | `/api/users` | Create new user | Admin only |
| `GET` | `/api/users/[id]` | Get single user | Admin only |
| `PUT` | `/api/users/[id]` | Update user | Admin only |
| `DELETE` | `/api/users/[id]` | Delete user | Admin only |
| `POST` | `/api/admin/users/[userId]` | User actions (reset password, verify email) | Admin only |
| `GET` | `/api/roles` | List roles | `roles.read` |
| `POST` | `/api/roles` | Create new role | `roles.create` |
| `GET` | `/api/roles/[id]` | Get single role | `roles.read` |
| `PUT` | `/api/roles/[id]` | Update role | `roles.update` |
| `DELETE` | `/api/roles/[id]` | Delete role | `roles.delete` |
| `GET` | `/api/roles/system` | Get system roles | `roles.read` |
| `GET` | `/api/permissions` | List permissions | `roles.read` |
| `POST` | `/api/permissions` | Create permission | `roles.create` |

### Frontend Routes

| Route | Description | Access Level |
|-------|-------------|--------------|
| `/admin` | Admin dashboard with user management | Admin only |
| `/role-management` | Role and permission management | `roles.manage` |
| `/rbac-test` | RBAC testing interface | Development |

---

## 🏢 Business & Organization Management

### API Endpoints

| Method | Endpoint | Description | RBAC Required |
|--------|----------|-------------|---------------|
| `GET` | `/api/organizations` | List organizations | Admin only |
| `POST` | `/api/organizations` | Create organization | Admin only |
| `GET` | `/api/organizations/[id]` | Get single organization | Admin only |
| `PUT` | `/api/organizations/[id]` | Update organization | Admin only |
| `DELETE` | `/api/organizations/[id]` | Delete organization | Admin only |
| `GET` | `/api/tenants` | List tenants | Admin only |
| `POST` | `/api/tenants` | Create tenant | Admin only |
| `GET` | `/api/tenants/check-subdomain` | Check subdomain availability | None |
| `GET` | `/api/business-profiles` | List business profiles | `business.read` |
| `POST` | `/api/business-profiles` | Create business profile | `business.create` |
| `PUT` | `/api/business-profiles/[id]` | Update business profile | `business.update` |

### Frontend Routes

| Route | Description | Access Level |
|-------|-------------|--------------|
| `/business` | Business profile management | Authenticated |
| `/business-tanstack` | TanStack demo with business data | Authenticated |

---

## 🎯 Dashboard & Frontend Routes

### Main Dashboard Routes

| Route | Description | Access Level |
|-------|-------------|--------------|
| `/` | Public landing page | Public |
| `/dashboard` | Main dashboard overview | Authenticated |

### Settings & Configuration

| Route | Description | Access Level |
|-------|-------------|--------------|
| `/settings` | Application settings | Authenticated |
| `/billing` | Billing and subscription management | `admin.manage` |
| `/api-keys` | API key management | `admin.manage` |

### Demo & Development Pages

| Route | Description | Access Level |
|-------|-------------|--------------|
| `/animation-demo` | Animation demonstration | Development |
| `/docs` | Documentation pages | Authenticated |
| `/tags` | Tag management interface | `tags.manage` |
| `/waiver-forms` | Waiver form management | Authenticated |

### Test & Debug Pages

| Route | Description | Access Level |
|-------|-------------|--------------|
| `/debug-session` | Session debugging | Development |
| `/test-modal` | Modal testing | Development |
| `/test-simple-modal` | Simple modal testing | Development |

---

## 🛠️ Utility & System Endpoints

### API Endpoints

| Method | Endpoint | Description | RBAC Required |
|--------|----------|-------------|---------------|
| `GET` | `/api/health` | System health check | None |
| `POST` | `/api/upload` | File upload handler | Authenticated |
| `GET` | `/api/tags` | List tags | `tags.read` |
| `POST` | `/api/tags` | Create tag | `tags.create` |
| `GET` | `/api/waiver-forms` | List waiver forms | Authenticated |
| `POST` | `/api/waiver-forms` | Create waiver form | Authenticated |
| `GET` | `/api/test-customer-address` | Test customer address | Development |
| `GET` | `/api/test-waiver-forms` | Test waiver forms | Development |
| `GET` | `/api/test-waiver-setup` | Test waiver setup | Development |
| `GET` | `/api/credits` | List credits | Authenticated |
| `POST` | `/api/credits` | Create credit | Authenticated |
| `GET` | `/api/settings` | Get application settings | Authenticated |
| `POST` | `/api/settings` | Update application settings | `admin.manage` |

### Stripe Integration

| Method | Endpoint | Description | RBAC Required |
|--------|----------|-------------|---------------|
| `POST` | `/api/stripe/create-checkout-session` | Create Stripe checkout session | Authenticated |
| `POST` | `/api/stripe/create-portal-session` | Create Stripe portal session | Authenticated |
| `POST` | `/api/stripe/webhooks` | Handle Stripe webhooks | None |

---

## 🔒 RBAC Permission Matrix

### Module Permissions

| Module | Actions | Description |
|--------|---------|-------------|
| `vouchers` | `read`, `create`, `update`, `delete`, `manage`, `use` | Voucher system operations |
| `customers` | `read`, `create`, `update`, `delete`, `manage` | Customer management |
| `classes` | `read`, `create`, `update`, `delete`, `manage` | Class management |
| `class-schedules` | `read`, `create`, `update`, `delete`, `manage` | Class schedule management |
| `bookings` | `read`, `create`, `update`, `delete`, `manage` | Booking management |
| `packages` | `read`, `create`, `update`, `delete`, `manage` | Package management |
| `locations` | `read`, `create`, `update`, `delete`, `manage` | Location management |
| `facilities` | `read`, `create`, `update`, `delete`, `manage` | Facility management |
| `equipment` | `read`, `create`, `update`, `delete`, `manage` | Equipment management |
| `roles` | `read`, `create`, `update`, `delete`, `manage` | Role management |
| `admin` | `access`, `manage` | Admin panel access |
| `reports` | `read`, `create` | Analytics and reporting |

### Special Access Levels

- **Public**: No authentication required
- **Authenticated**: Valid session required
- **Admin only**: Admin role required
- **Super Admin**: Super admin role required (<EMAIL>)
- **Development**: Only available in development mode

---

## 📝 Notes

### Multi-Tenant Architecture
- All API endpoints support tenant isolation via `tenantId` parameter
- Frontend routes automatically filter data based on user's tenant
- RBAC permissions are tenant-scoped

### Rate Limiting
- Authentication endpoints have rate limiting enabled
- Email verification and password reset are rate limited

### Error Handling
- All API endpoints return consistent error format:
  ```json
  {
    "success": false,
    "error": "Error message",
    "details": "Additional details (for validation errors)"
  }
  ```

### Pagination
- List endpoints support `limit` and `offset` parameters
- Default limit is typically 50 items
- Use `limit=200` for bulk operations

---

*Last updated: December 2024*
*Total API Endpoints: 100+*
*Total Frontend Routes: 50+*
