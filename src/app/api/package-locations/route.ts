import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { packageLocationService, PackageLocationFilters } from "@/lib/services/package-location.service";
import { z } from "zod";

const createPackageLocationSchema = z.object({
  packageId: z.string().min(1, "Package ID is required"),
  locationId: z.string().min(1, "Location ID is required"),
});

const bulkUpdateSchema = z.object({
  packageId: z.string().optional(),
  locationId: z.string().optional(),
  locationIds: z.array(z.string()).optional(),
  packageIds: z.array(z.string()).optional(),
});

/**
 * GET /api/package-locations
 * Get package locations with optional filtering
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());

    console.log(`📍 [GET /api/package-locations] Fetching package locations:`, queryParams);

    const filters: PackageLocationFilters = {
      packageId: queryParams.packageId || undefined,
      locationId: queryParams.locationId || undefined,
      search: queryParams.search || undefined,
    };

    const packageLocations = await packageLocationService.getWithDetails(filters);

    return NextResponse.json({
      data: packageLocations,
      count: packageLocations.length,
    });
  } catch (error) {
    console.error(`📍 [GET /api/package-locations] Error:`, error);
    return NextResponse.json(
      { error: "Failed to fetch package locations" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/package-locations
 * Create a new package location relationship
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    console.log(`📍 [POST /api/package-locations] Creating package location:`, body);

    // Validate input
    const validatedData = createPackageLocationSchema.parse(body);

    const packageLocation = await packageLocationService.addPackageToLocation(
      validatedData.packageId,
      validatedData.locationId
    );

    return NextResponse.json(packageLocation, { status: 201 });
  } catch (error) {
    console.error(`📍 [POST /api/package-locations] Error:`, error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to create package location" },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/package-locations
 * Bulk update package location relationships
 */
export async function PUT(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    console.log(`📍 [PUT /api/package-locations] Bulk updating:`, body);

    // Validate input
    const validatedData = bulkUpdateSchema.parse(body);

    let results;

    if (validatedData.packageId && validatedData.locationIds !== undefined) {
      // Update locations for a package
      results = await packageLocationService.bulkAddLocationsToPackage(
        validatedData.packageId,
        validatedData.locationIds
      );
    } else if (validatedData.locationId && validatedData.packageIds !== undefined) {
      // Update packages for a location
      results = await packageLocationService.bulkAddPackagesToLocation(
        validatedData.locationId,
        validatedData.packageIds
      );
    } else {
      return NextResponse.json(
        { error: "Either (packageId + locationIds) or (locationId + packageIds) must be provided" },
        { status: 400 }
      );
    }

    return NextResponse.json({
      data: results,
      count: results.length,
    });
  } catch (error) {
    console.error(`📍 [PUT /api/package-locations] Error:`, error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to bulk update package locations" },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/package-locations
 * Remove a package location relationship
 */
export async function DELETE(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const packageId = searchParams.get("packageId");
    const locationId = searchParams.get("locationId");

    if (!packageId || !locationId) {
      return NextResponse.json(
        { error: "Both packageId and locationId are required" },
        { status: 400 }
      );
    }

    console.log(`📍 [DELETE /api/package-locations] Removing:`, { packageId, locationId });

    await packageLocationService.removePackageFromLocation(packageId, locationId);

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error(`📍 [DELETE /api/package-locations] Error:`, error);
    return NextResponse.json(
      { error: "Failed to remove package location" },
      { status: 500 }
    );
  }
}
