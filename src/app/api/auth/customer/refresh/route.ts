import { NextRequest } from "next/server";
import { z } from "zod";
import { customerJWTService } from "@/lib/services/customer-jwt.service";
import { 
  createAuthenticatedResponse, 
  createErrorResponse,
  rateLimit,
  addSecurityHeaders 
} from "@/lib/middleware/customer-auth.middleware";
import { rateLimitConfig } from "@/lib/security/rate-limit.config";

/**
 * Refresh Customer JWT Access Token
 * 
 * POST /api/auth/customer/refresh
 * 
 * Uses refresh token to generate new access token
 */

const refreshTokenSchema = z.object({
  refreshToken: z.string().min(1, "Refresh token is required"),
  deviceType: z.enum(["web", "mobile", "tablet"]).optional(),
  deviceId: z.string().optional(),
});

export async function POST(request: NextRequest) {
  try {
    // Apply rate limiting
    const clientIP = request.headers.get("x-forwarded-for")?.split(",")[0] || 
                    request.headers.get("x-real-ip") || 
                    request.ip || "unknown";

    const rateLimitResult = rateLimit({
      windowMs: rateLimitConfig.auth.refresh.windowMs,
      max: rateLimitConfig.auth.refresh.max,
      keyGenerator: () => `refresh-token:${clientIP}`,
    })(request);

    if (!rateLimitResult.allowed) {
      return createErrorResponse(
        "Too many refresh token attempts",
        "RATE_LIMIT_EXCEEDED",
        429,
        rateLimitResult.headers
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = refreshTokenSchema.parse(body);

    // Get user agent
    const userAgent = request.headers.get("user-agent") || undefined;

    // Refresh the access token
    const newTokens = await customerJWTService.refreshToken(
      validatedData.refreshToken,
      clientIP,
      userAgent
    );

    if (!newTokens) {
      return createErrorResponse(
        "Invalid or expired refresh token",
        "REFRESH_TOKEN_INVALID",
        401
      );
    }

    // Prepare response data
    const responseData = {
      success: true,
      tokens: {
        accessToken: newTokens.accessToken,
        refreshToken: newTokens.refreshToken,
        tokenType: newTokens.tokenType,
        expiresIn: newTokens.expiresIn,
      },
    };

    return createAuthenticatedResponse(responseData, 200, rateLimitResult.headers);

  } catch (error) {
    console.error("Token refresh error:", error);

    if (error instanceof z.ZodError) {
      return createErrorResponse(
        "Invalid request data",
        "INVALID_INPUT",
        400
      );
    }

    return createErrorResponse(
      "Token refresh failed",
      "REFRESH_ERROR",
      500
    );
  }
}

// Handle preflight OPTIONS request
export async function OPTIONS(request: NextRequest) {
  const response = new Response(null, { status: 200 });
  return addSecurityHeaders(response);
}
