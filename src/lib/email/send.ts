import { render } from "@react-email/render";
import { Resend } from "resend";
import VerificationEmail from "@/components/email-templates/verification-email";
import PasswordResetEmail from "@/components/email-templates/password-reset-email";
import { getAppUrl } from "../env";

// Initialize Resend (you can also use Brevo/SendinBlue)
const resend = process.env.RESEND_API_KEY ? new Resend(process.env.RESEND_API_KEY) : null;

// Email configuration
const FROM_EMAIL = process.env.FROM_EMAIL || "<EMAIL>";
const APP_NAME = "Your SaaS App";

// Base email sending function
async function sendEmail({
  to,
  subject,
  html,
  text,
}: {
  to: string;
  subject: string;
  html: string;
  text?: string;
}) {
  if (!resend) {
    console.log("Email service not configured. Email would be sent to:", to);
    console.log("Subject:", subject);
    console.log("HTML:", html);
    return { success: true, id: "dev-mode" };
  }

  try {
    const result = await resend.emails.send({
      from: FROM_EMAIL,
      to,
      subject,
      html,
      text,
    });

    return { success: true, id: result.data?.id };
  } catch (error) {
    console.error("Failed to send email:", error);
    throw new Error("Failed to send email");
  }
}

// Send verification email
export async function sendVerificationEmail(
  email: string,
  name: string,
  token: string
) {
  const verificationUrl = `${getAppUrl()}/auth/verify-email?token=${token}`;
  
  const html = render(
    VerificationEmail({
      name,
      verificationUrl,
    })
  );

  const text = `
Hi ${name},

Welcome to ${APP_NAME}! Please verify your email address by clicking the link below:

${verificationUrl}

If you didn't create an account with us, you can safely ignore this email.

Best regards,
The ${APP_NAME} Team
  `.trim();

  return sendEmail({
    to: email,
    subject: `Verify your email address for ${APP_NAME}`,
    html,
    text,
  });
}

// Send password reset email
export async function sendPasswordResetEmail(email: string, token: string) {
  const resetUrl = `${getAppUrl()}/auth/reset-password?token=${token}`;
  
  const html = render(
    PasswordResetEmail({
      resetUrl,
    })
  );

  const text = `
Someone requested a password reset for your ${APP_NAME} account.

If this was you, click the link below to reset your password:

${resetUrl}

This link will expire in 1 hour for security reasons.

If you didn't request a password reset, you can safely ignore this email.

Best regards,
The ${APP_NAME} Team
  `.trim();

  return sendEmail({
    to: email,
    subject: `Reset your password for ${APP_NAME}`,
    html,
    text,
  });
}

// Send welcome email (after email verification)
export async function sendWelcomeEmail(email: string, name: string) {
  const dashboardUrl = `${getAppUrl()}/dashboard`;
  
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h1>Welcome to ${APP_NAME}!</h1>
      <p>Hi ${name},</p>
      <p>Your email has been verified and your account is now active. You can start using ${APP_NAME} right away!</p>
      <p>
        <a href="${dashboardUrl}" style="background-color: #5e6ad2; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">
          Go to Dashboard
        </a>
      </p>
      <p>If you have any questions, feel free to reach out to our support team.</p>
      <p>Best regards,<br>The ${APP_NAME} Team</p>
    </div>
  `;

  const text = `
Hi ${name},

Welcome to ${APP_NAME}!

Your email has been verified and your account is now active. You can start using ${APP_NAME} right away!

Visit your dashboard: ${dashboardUrl}

If you have any questions, feel free to reach out to our support team.

Best regards,
The ${APP_NAME} Team
  `.trim();

  return sendEmail({
    to: email,
    subject: `Welcome to ${APP_NAME}!`,
    html,
    text,
  });
}

// Send organization invitation email
export async function sendOrganizationInviteEmail(
  email: string,
  organizationName: string,
  inviterName: string,
  inviteToken: string
) {
  const inviteUrl = `${getAppUrl()}/auth/accept-invite?token=${inviteToken}`;
  
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h1>You've been invited to join ${organizationName}</h1>
      <p>Hi there,</p>
      <p>${inviterName} has invited you to join <strong>${organizationName}</strong> on ${APP_NAME}.</p>
      <p>
        <a href="${inviteUrl}" style="background-color: #5e6ad2; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">
          Accept Invitation
        </a>
      </p>
      <p>If you don't want to join this organization, you can safely ignore this email.</p>
      <p>Best regards,<br>The ${APP_NAME} Team</p>
    </div>
  `;

  const text = `
You've been invited to join ${organizationName}

Hi there,

${inviterName} has invited you to join ${organizationName} on ${APP_NAME}.

Accept the invitation: ${inviteUrl}

If you don't want to join this organization, you can safely ignore this email.

Best regards,
The ${APP_NAME} Team
  `.trim();

  return sendEmail({
    to: email,
    subject: `Invitation to join ${organizationName} on ${APP_NAME}`,
    html,
    text,
  });
}

// Send credit purchase confirmation email
export async function sendCreditPurchaseEmail(
  email: string,
  name: string,
  credits: number,
  amount: number
) {
  const dashboardUrl = `${getAppUrl()}/dashboard`;
  
  const html = `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h1>Credit Purchase Confirmation</h1>
      <p>Hi ${name},</p>
      <p>Thank you for your purchase! Your credits have been added to your account.</p>
      <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
        <h3>Purchase Details:</h3>
        <p><strong>Credits purchased:</strong> ${credits.toLocaleString()}</p>
        <p><strong>Amount paid:</strong> $${(amount / 100).toFixed(2)}</p>
      </div>
      <p>
        <a href="${dashboardUrl}" style="background-color: #5e6ad2; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;">
          View Dashboard
        </a>
      </p>
      <p>Best regards,<br>The ${APP_NAME} Team</p>
    </div>
  `;

  const text = `
Credit Purchase Confirmation

Hi ${name},

Thank you for your purchase! Your credits have been added to your account.

Purchase Details:
- Credits purchased: ${credits.toLocaleString()}
- Amount paid: $${(amount / 100).toFixed(2)}

View your dashboard: ${dashboardUrl}

Best regards,
The ${APP_NAME} Team
  `.trim();

  return sendEmail({
    to: email,
    subject: `Credit Purchase Confirmation - ${APP_NAME}`,
    html,
    text,
  });
}
