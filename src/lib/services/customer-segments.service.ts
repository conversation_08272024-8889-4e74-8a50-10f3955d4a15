import { sql, eq, and, ilike, SQL } from "drizzle-orm";
import { 
  customers, 
  pricing_groups, 
  locations,
  package_customer_segements,
  type Customer,
  type PricingGroup,
  type Location
} from "@/lib/db/schema";
import { BaseService, CreateEntityData, UpdateEntityData, QueryOptions } from "@/lib/core/base-service";
import { db } from "@/lib/db";

// Customer Segment types
export interface CustomerSegment {
  id: string;
  name: string;
  description: string;
  type: 'pricing_group' | 'location' | 'custom' | 'all' | 'members' | 'new_customers' | 'existing_customers';
  criteria: any; // JSON criteria for custom segments
  customerCount: number;
  tenantId: number;
}

export interface CustomerSegmentFilters {
  tenantId?: number;
  type?: string;
  search?: string;
  isActive?: boolean;
}

export interface CustomerSegmentStats {
  total: number;
  byType: Record<string, number>;
  byTenant: Record<number, number>;
  totalCustomers: number;
}

class CustomerSegmentsService {
  
  /**
   * Get all customer segments for a tenant
   */
  async getByTenant(tenantId: number, filters: CustomerSegmentFilters = {}): Promise<CustomerSegment[]> {
    console.log(`👥 [CustomerSegmentsService] Getting customer segments for tenant ${tenantId}:`, filters);

    const segments: CustomerSegment[] = [];

    // 1. Add built-in segments
    const builtInSegments = await this.getBuiltInSegments(tenantId);
    segments.push(...builtInSegments);

    // 2. Add pricing group based segments
    const pricingGroupSegments = await this.getPricingGroupSegments(tenantId);
    segments.push(...pricingGroupSegments);

    // 3. Add location based segments
    const locationSegments = await this.getLocationSegments(tenantId);
    segments.push(...locationSegments);

    // Apply search filter if provided
    let filteredSegments = segments;
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      filteredSegments = segments.filter(segment => 
        segment.name.toLowerCase().includes(searchTerm) ||
        segment.description.toLowerCase().includes(searchTerm)
      );
    }

    // Apply type filter if provided
    if (filters.type) {
      filteredSegments = filteredSegments.filter(segment => segment.type === filters.type);
    }

    console.log(`✅ [CustomerSegmentsService] Found ${filteredSegments.length} customer segments`);
    return filteredSegments;
  }

  /**
   * Get built-in customer segments
   */
  private async getBuiltInSegments(tenantId: number): Promise<CustomerSegment[]> {
    // Get customer counts for each built-in segment
    const [allCustomersCount] = await db
      .select({ count: sql<number>`count(*)` })
      .from(customers)
      .where(eq(customers.tenantId, tenantId));

    const [activeCustomersCount] = await db
      .select({ count: sql<number>`count(*)` })
      .from(customers)
      .where(and(
        eq(customers.tenantId, tenantId),
        eq(customers.isActive, true)
      ));

    // For simplicity, we'll consider "new customers" as those created in last 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const [newCustomersCount] = await db
      .select({ count: sql<number>`count(*)` })
      .from(customers)
      .where(and(
        eq(customers.tenantId, tenantId),
        sql`${customers.createdAt} >= ${thirtyDaysAgo.toISOString()}`
      ));

    const [existingCustomersCount] = await db
      .select({ count: sql<number>`count(*)` })
      .from(customers)
      .where(and(
        eq(customers.tenantId, tenantId),
        sql`${customers.createdAt} < ${thirtyDaysAgo.toISOString()}`
      ));

    return [
      {
        id: 'all_customers',
        name: 'All Customers',
        description: 'All customers in your organization',
        type: 'all',
        criteria: { type: 'all' },
        customerCount: allCustomersCount.count,
        tenantId,
      },
      {
        id: 'members_only',
        name: 'Active Members',
        description: 'Currently active customers',
        type: 'members',
        criteria: { type: 'active', isActive: true },
        customerCount: activeCustomersCount.count,
        tenantId,
      },
      {
        id: 'new_customers',
        name: 'New Customers',
        description: 'Customers who joined in the last 30 days',
        type: 'new_customers',
        criteria: { type: 'new', days: 30 },
        customerCount: newCustomersCount.count,
        tenantId,
      },
      {
        id: 'existing_customers',
        name: 'Existing Customers',
        description: 'Customers who joined more than 30 days ago',
        type: 'existing_customers',
        criteria: { type: 'existing', days: 30 },
        customerCount: existingCustomersCount.count,
        tenantId,
      },
    ];
  }

  /**
   * Get pricing group based segments
   */
  private async getPricingGroupSegments(tenantId: number): Promise<CustomerSegment[]> {
    const pricingGroups = await db
      .select()
      .from(pricing_groups)
      .where(and(
        eq(pricing_groups.tenantId, tenantId),
        eq(pricing_groups.isActive, true)
      ))
      .orderBy(pricing_groups.sortOrder, pricing_groups.name);

    const segments: CustomerSegment[] = [];

    for (const group of pricingGroups) {
      // Get customer count for this pricing group
      const [customerCount] = await db
        .select({ count: sql<number>`count(*)` })
        .from(customers)
        .where(and(
          eq(customers.tenantId, tenantId),
          eq(customers.pricingGroupId, group.id)
        ));

      segments.push({
        id: `pricing_group_${group.id}`,
        name: `${group.name} Members`,
        description: group.description || `Customers in ${group.name} pricing group`,
        type: 'pricing_group',
        criteria: { type: 'pricing_group', pricingGroupId: group.id },
        customerCount: customerCount.count,
        tenantId,
      });
    }

    return segments;
  }

  /**
   * Get location based segments
   */
  private async getLocationSegments(tenantId: number): Promise<CustomerSegment[]> {
    const tenantLocations = await db
      .select()
      .from(locations)
      .where(eq(locations.tenantId, tenantId))
      .orderBy(locations.name);

    const segments: CustomerSegment[] = [];

    for (const location of tenantLocations) {
      // Get customer count for this location
      const [customerCount] = await db
        .select({ count: sql<number>`count(*)` })
        .from(customers)
        .where(and(
          eq(customers.tenantId, tenantId),
          eq(customers.locationId, location.id)
        ));

      segments.push({
        id: `location_${location.id}`,
        name: `${location.name} Customers`,
        description: `Customers associated with ${location.name}`,
        type: 'location',
        criteria: { type: 'location', locationId: location.id },
        customerCount: customerCount.count,
        tenantId,
      });
    }

    return segments;
  }

  /**
   * Get customers in a specific segment
   */
  async getCustomersInSegment(segmentId: string, tenantId: number): Promise<Customer[]> {
    console.log(`👥 [CustomerSegmentsService] Getting customers in segment ${segmentId} for tenant ${tenantId}`);

    // Parse segment ID to determine type and criteria
    if (segmentId === 'all_customers') {
      return await db
        .select()
        .from(customers)
        .where(eq(customers.tenantId, tenantId))
        .orderBy(customers.firstName, customers.lastName);
    }

    if (segmentId === 'members_only') {
      return await db
        .select()
        .from(customers)
        .where(and(
          eq(customers.tenantId, tenantId),
          eq(customers.isActive, true)
        ))
        .orderBy(customers.firstName, customers.lastName);
    }

    if (segmentId === 'new_customers') {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      return await db
        .select()
        .from(customers)
        .where(and(
          eq(customers.tenantId, tenantId),
          sql`${customers.createdAt} >= ${thirtyDaysAgo.toISOString()}`
        ))
        .orderBy(customers.firstName, customers.lastName);
    }

    if (segmentId === 'existing_customers') {
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      return await db
        .select()
        .from(customers)
        .where(and(
          eq(customers.tenantId, tenantId),
          sql`${customers.createdAt} < ${thirtyDaysAgo.toISOString()}`
        ))
        .orderBy(customers.firstName, customers.lastName);
    }

    if (segmentId.startsWith('pricing_group_')) {
      const pricingGroupId = segmentId.replace('pricing_group_', '');
      return await db
        .select()
        .from(customers)
        .where(and(
          eq(customers.tenantId, tenantId),
          eq(customers.pricingGroupId, pricingGroupId)
        ))
        .orderBy(customers.firstName, customers.lastName);
    }

    if (segmentId.startsWith('location_')) {
      const locationId = segmentId.replace('location_', '');
      return await db
        .select()
        .from(customers)
        .where(and(
          eq(customers.tenantId, tenantId),
          eq(customers.locationId, locationId)
        ))
        .orderBy(customers.firstName, customers.lastName);
    }

    console.log(`⚠️ [CustomerSegmentsService] Unknown segment ID: ${segmentId}`);
    return [];
  }

  /**
   * Get segment statistics
   */
  async getStats(tenantId?: number): Promise<CustomerSegmentStats> {
    const segments = tenantId 
      ? await this.getByTenant(tenantId)
      : [];

    const byType = segments.reduce((acc, segment) => {
      acc[segment.type] = (acc[segment.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const totalCustomers = segments.reduce((sum, segment) => sum + segment.customerCount, 0);

    return {
      total: segments.length,
      byType,
      byTenant: tenantId ? { [tenantId]: segments.length } : {},
      totalCustomers,
    };
  }
}

// Export singleton instance
export const customerSegmentsService = new CustomerSegmentsService();
