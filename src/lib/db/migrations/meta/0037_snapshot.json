{"id": "9773640c-27fc-4168-8889-1d144ddfddcb", "prevId": "c5141e8c-5b5d-47af-96f9-bd1eff90463f", "version": "7", "dialect": "postgresql", "tables": {"public.accounts": {"name": "accounts", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "provider_account_id": {"name": "provider_account_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": false}, "token_type": {"name": "token_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false}, "session_state": {"name": "session_state", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"accounts_user_id_users_id_fk": {"name": "accounts_user_id_users_id_fk", "tableFrom": "accounts", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"accounts_provider_provider_account_id_pk": {"name": "accounts_provider_provider_account_id_pk", "columns": ["provider", "provider_account_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.addresses": {"name": "addresses", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": false}, "address_line_1": {"name": "address_line_1", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "address_line_2": {"name": "address_line_2", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "city": {"name": "city", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "state": {"name": "state", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "zip": {"name": "zip", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "country": {"name": "country", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "postal_code": {"name": "postal_code", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "accepted_at": {"name": "accepted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"addresses_tenant_id_tenants_id_fk": {"name": "addresses_tenant_id_tenants_id_fk", "tableFrom": "addresses", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.api_key_usage": {"name": "api_key_usage", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "api_key_id": {"name": "api_key_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "endpoint": {"name": "endpoint", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "method": {"name": "method", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "status_code": {"name": "status_code", "type": "integer", "primaryKey": false, "notNull": false}, "response_time": {"name": "response_time", "type": "integer", "primaryKey": false, "notNull": false}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "success": {"name": "success", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "error_message": {"name": "error_message", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"api_key_usage_api_key_id_api_keys_id_fk": {"name": "api_key_usage_api_key_id_api_keys_id_fk", "tableFrom": "api_key_usage", "tableTo": "api_keys", "columnsFrom": ["api_key_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.api_keys": {"name": "api_keys", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "key_hash": {"name": "key_hash", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": true}, "permissions": {"name": "permissions", "type": "text", "primaryKey": false, "notNull": true}, "rate_limit": {"name": "rate_limit", "type": "text", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "last_used_at": {"name": "last_used_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"api_keys_tenant_id_tenants_id_fk": {"name": "api_keys_tenant_id_tenants_id_fk", "tableFrom": "api_keys", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"api_keys_key_hash_unique": {"name": "api_keys_key_hash_unique", "nullsNotDistinct": false, "columns": ["key_hash"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.appointment_categories": {"name": "appointment_categories", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"appointment_categories_tenant_id_tenants_id_fk": {"name": "appointment_categories_tenant_id_tenants_id_fk", "tableFrom": "appointment_categories", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.appointment_subcategories": {"name": "appointment_subcategories", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": true}, "category_id": {"name": "category_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"appointment_subcategories_tenant_id_tenants_id_fk": {"name": "appointment_subcategories_tenant_id_tenants_id_fk", "tableFrom": "appointment_subcategories", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "appointment_subcategories_category_id_appointment_categories_id_fk": {"name": "appointment_subcategories_category_id_appointment_categories_id_fk", "tableFrom": "appointment_subcategories", "tableTo": "appointment_categories", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.audit_logs": {"name": "audit_logs", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "api_key_id": {"name": "api_key_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": true}, "action": {"name": "action", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "resource": {"name": "resource", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "resource_id": {"name": "resource_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "changes": {"name": "changes", "type": "text", "primaryKey": false, "notNull": false}, "ip": {"name": "ip", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": true}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": true}, "timestamp": {"name": "timestamp", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "success": {"name": "success", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "error": {"name": "error", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"audit_logs_api_key_id_api_keys_id_fk": {"name": "audit_logs_api_key_id_api_keys_id_fk", "tableFrom": "audit_logs", "tableTo": "api_keys", "columnsFrom": ["api_key_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "audit_logs_tenant_id_tenants_id_fk": {"name": "audit_logs_tenant_id_tenants_id_fk", "tableFrom": "audit_logs", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.blog_categories": {"name": "blog_categories", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "color": {"name": "color", "type": "<PERSON><PERSON><PERSON>(7)", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"blog_categories_tenant_slug_idx": {"name": "blog_categories_tenant_slug_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "blog_categories_tenant_active_idx": {"name": "blog_categories_tenant_active_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"blog_categories_tenant_id_tenants_id_fk": {"name": "blog_categories_tenant_id_tenants_id_fk", "tableFrom": "blog_categories", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.blog_posts": {"name": "blog_posts", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "excerpt": {"name": "excerpt", "type": "text", "primaryKey": false, "notNull": false}, "featured_image": {"name": "featured_image", "type": "<PERSON><PERSON><PERSON>(1000)", "primaryKey": false, "notNull": false}, "category_id": {"name": "category_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "tags": {"name": "tags", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'draft'"}, "is_featured": {"name": "is_featured", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "published_at": {"name": "published_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "author_id": {"name": "author_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "seo_title": {"name": "seo_title", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "seo_description": {"name": "seo_description", "type": "text", "primaryKey": false, "notNull": false}, "view_count": {"name": "view_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"blog_posts_tenant_slug_idx": {"name": "blog_posts_tenant_slug_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "slug", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "blog_posts_tenant_status_idx": {"name": "blog_posts_tenant_status_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "blog_posts_tenant_featured_idx": {"name": "blog_posts_tenant_featured_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "is_featured", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "blog_posts_tenant_category_idx": {"name": "blog_posts_tenant_category_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "category_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "blog_posts_published_at_idx": {"name": "blog_posts_published_at_idx", "columns": [{"expression": "published_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "blog_posts_author_idx": {"name": "blog_posts_author_idx", "columns": [{"expression": "author_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"blog_posts_tenant_id_tenants_id_fk": {"name": "blog_posts_tenant_id_tenants_id_fk", "tableFrom": "blog_posts", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "blog_posts_category_id_blog_categories_id_fk": {"name": "blog_posts_category_id_blog_categories_id_fk", "tableFrom": "blog_posts", "tableTo": "blog_categories", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "blog_posts_author_id_users_id_fk": {"name": "blog_posts_author_id_users_id_fk", "tableFrom": "blog_posts", "tableTo": "users", "columnsFrom": ["author_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.business_profiles": {"name": "business_profiles", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": false}, "business_name": {"name": "business_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "business_logo": {"name": "business_logo", "type": "text", "primaryKey": false, "notNull": false}, "company_registered_name": {"name": "company_registered_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "company_registered_no": {"name": "company_registered_no", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "business_industry": {"name": "business_industry", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "contact_email": {"name": "contact_email", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": false}, "show_whatsapp_floating": {"name": "show_whatsapp_floating", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"business_profiles_tenant_id_tenants_id_fk": {"name": "business_profiles_tenant_id_tenants_id_fk", "tableFrom": "business_profiles", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.class_bookings": {"name": "class_bookings", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": true}, "schedule_id": {"name": "schedule_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "class_id": {"name": "class_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "customer_id": {"name": "customer_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "booked_by_user_id": {"name": "booked_by_user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "booked_by_customer_id": {"name": "booked_by_customer_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'booked'"}, "is_waitlist": {"name": "is_waitlist", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "waitlist_position": {"name": "waitlist_position", "type": "integer", "primaryKey": false, "notNull": false}, "payment_status": {"name": "payment_status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "payment_method": {"name": "payment_method", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "credits_used": {"name": "credits_used", "type": "integer", "primaryKey": false, "notNull": false}, "booking_time": {"name": "booking_time", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "check_in_time": {"name": "check_in_time", "type": "timestamp", "primaryKey": false, "notNull": false}, "cancel_time": {"name": "cancel_time", "type": "timestamp", "primaryKey": false, "notNull": false}, "cancellation_reason": {"name": "cancellation_reason", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"class_bookings_tenant_id_tenants_id_fk": {"name": "class_bookings_tenant_id_tenants_id_fk", "tableFrom": "class_bookings", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "class_bookings_schedule_id_class_schedules_id_fk": {"name": "class_bookings_schedule_id_class_schedules_id_fk", "tableFrom": "class_bookings", "tableTo": "class_schedules", "columnsFrom": ["schedule_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "class_bookings_class_id_classes_id_fk": {"name": "class_bookings_class_id_classes_id_fk", "tableFrom": "class_bookings", "tableTo": "classes", "columnsFrom": ["class_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "class_bookings_customer_id_customers_id_fk": {"name": "class_bookings_customer_id_customers_id_fk", "tableFrom": "class_bookings", "tableTo": "customers", "columnsFrom": ["customer_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "class_bookings_booked_by_user_id_users_id_fk": {"name": "class_bookings_booked_by_user_id_users_id_fk", "tableFrom": "class_bookings", "tableTo": "users", "columnsFrom": ["booked_by_user_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "class_bookings_booked_by_customer_id_customers_id_fk": {"name": "class_bookings_booked_by_customer_id_customers_id_fk", "tableFrom": "class_bookings", "tableTo": "customers", "columnsFrom": ["booked_by_customer_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.class_categories": {"name": "class_categories", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"class_categories_tenant_id_tenants_id_fk": {"name": "class_categories_tenant_id_tenants_id_fk", "tableFrom": "class_categories", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.class_images": {"name": "class_images", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "class_id": {"name": "class_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "image_url": {"name": "image_url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "upload_at": {"name": "upload_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"class_images_class_id_classes_id_fk": {"name": "class_images_class_id_classes_id_fk", "tableFrom": "class_images", "tableTo": "classes", "columnsFrom": ["class_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.class_levels": {"name": "class_levels", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "sort_order": {"name": "sort_order", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"class_levels_tenant_id_tenants_id_fk": {"name": "class_levels_tenant_id_tenants_id_fk", "tableFrom": "class_levels", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.class_membership_plans ": {"name": "class_membership_plans ", "schema": "", "columns": {"class_id": {"name": "class_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "membership_plan_id": {"name": "membership_plan_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"class_membership_plans _class_id_classes_id_fk": {"name": "class_membership_plans _class_id_classes_id_fk", "tableFrom": "class_membership_plans ", "tableTo": "classes", "columnsFrom": ["class_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "class_membership_plans _membership_plan_id_membership_plans_id_fk": {"name": "class_membership_plans _membership_plan_id_membership_plans_id_fk", "tableFrom": "class_membership_plans ", "tableTo": "membership_plans", "columnsFrom": ["membership_plan_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"class_membership_plans _class_id_membership_plan_id_pk": {"name": "class_membership_plans _class_id_membership_plan_id_pk", "columns": ["class_id", "membership_plan_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.class_package_pricing": {"name": "class_package_pricing", "schema": "", "columns": {"class_id": {"name": "class_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "package_pricing_id": {"name": "package_pricing_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"class_package_pricing_class_id_classes_id_fk": {"name": "class_package_pricing_class_id_classes_id_fk", "tableFrom": "class_package_pricing", "tableTo": "classes", "columnsFrom": ["class_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "class_package_pricing_package_pricing_id_package_pricing_id_fk": {"name": "class_package_pricing_package_pricing_id_package_pricing_id_fk", "tableFrom": "class_package_pricing", "tableTo": "package_pricing", "columnsFrom": ["package_pricing_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"class_package_pricing_class_id_package_pricing_id_pk": {"name": "class_package_pricing_class_id_package_pricing_id_pk", "columns": ["class_id", "package_pricing_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.class_payment_options": {"name": "class_payment_options", "schema": "", "columns": {"class_id": {"name": "class_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "payment_type": {"name": "payment_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "drop_in_enable": {"name": "drop_in_enable", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "drop_in_price": {"name": "drop_in_price", "type": "integer", "primaryKey": false, "notNull": false}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"class_payment_options_class_id_classes_id_fk": {"name": "class_payment_options_class_id_classes_id_fk", "tableFrom": "class_payment_options", "tableTo": "classes", "columnsFrom": ["class_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.class_schedules": {"name": "class_schedules", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "class_id": {"name": "class_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": false}, "location_id": {"name": "location_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "facility_id": {"name": "facility_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "staff_id": {"name": "staff_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "start_date": {"name": "start_date", "type": "date", "primaryKey": false, "notNull": false}, "end_date": {"name": "end_date", "type": "date", "primaryKey": false, "notNull": false}, "start_time": {"name": "start_time", "type": "timestamp", "primaryKey": false, "notNull": false}, "end_time": {"name": "end_time", "type": "timestamp", "primaryKey": false, "notNull": false}, "duration": {"name": "duration", "type": "integer", "primaryKey": false, "notNull": true}, "calender_color": {"name": "calender_color", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "repeat_rule": {"name": "repeat_rule", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'none'"}, "pax": {"name": "pax", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "waitlist": {"name": "waitlist", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "allow_classpass": {"name": "allow_classpass", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "is_private": {"name": "is_private", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "publish_now": {"name": "publish_now", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "publish_at": {"name": "publish_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "auto_cancel_if_minimum_not_met": {"name": "auto_cancel_if_minimum_not_met", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "booking_window_start": {"name": "booking_window_start", "type": "timestamp", "primaryKey": false, "notNull": false}, "booking_window_end": {"name": "booking_window_end", "type": "timestamp", "primaryKey": false, "notNull": false}, "check_in_window_start": {"name": "check_in_window_start", "type": "timestamp", "primaryKey": false, "notNull": false}, "check_in_window_end": {"name": "check_in_window_end", "type": "timestamp", "primaryKey": false, "notNull": false}, "late_cancellation_rule": {"name": "late_cancellation_rule", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"idx_class_schedules_time_overlap": {"name": "idx_class_schedules_time_overlap", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "start_time", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "end_time", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_class_schedules_location_time": {"name": "idx_class_schedules_location_time", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "location_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "start_time", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "end_time", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_class_schedules_facility_time": {"name": "idx_class_schedules_facility_time", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "facility_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "start_time", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "end_time", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_class_schedules_staff_time": {"name": "idx_class_schedules_staff_time", "columns": [{"expression": "staff_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "start_time", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "end_time", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_class_schedules_recurring": {"name": "idx_class_schedules_recurring", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "repeat_rule", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "start_date", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "end_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"class_schedules_class_id_classes_id_fk": {"name": "class_schedules_class_id_classes_id_fk", "tableFrom": "class_schedules", "tableTo": "classes", "columnsFrom": ["class_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "class_schedules_tenant_id_tenants_id_fk": {"name": "class_schedules_tenant_id_tenants_id_fk", "tableFrom": "class_schedules", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "class_schedules_location_id_locations_id_fk": {"name": "class_schedules_location_id_locations_id_fk", "tableFrom": "class_schedules", "tableTo": "locations", "columnsFrom": ["location_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "class_schedules_facility_id_facilities_id_fk": {"name": "class_schedules_facility_id_facilities_id_fk", "tableFrom": "class_schedules", "tableTo": "facilities", "columnsFrom": ["facility_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "class_schedules_staff_id_users_id_fk": {"name": "class_schedules_staff_id_users_id_fk", "tableFrom": "class_schedules", "tableTo": "users", "columnsFrom": ["staff_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"unique_schedule_time_location": {"name": "unique_schedule_time_location", "nullsNotDistinct": false, "columns": ["tenant_id", "start_time", "end_time", "location_id"]}, "unique_schedule_time_facility": {"name": "unique_schedule_time_facility", "nullsNotDistinct": false, "columns": ["tenant_id", "start_time", "end_time", "facility_id"]}, "unique_staff_time_slot": {"name": "unique_staff_time_slot", "nullsNotDistinct": false, "columns": ["staff_id", "start_time", "end_time"]}}, "policies": {}, "checkConstraints": {"check_start_before_end": {"name": "check_start_before_end", "value": "\"class_schedules\".\"start_time\" < \"class_schedules\".\"end_time\""}, "check_positive_duration": {"name": "check_positive_duration", "value": "\"class_schedules\".\"duration\" > 0"}, "check_positive_capacity": {"name": "check_positive_capacity", "value": "\"class_schedules\".\"pax\" > 0 AND \"class_schedules\".\"waitlist\" >= 0"}, "check_valid_repeat_rule": {"name": "check_valid_repeat_rule", "value": "\"class_schedules\".\"repeat_rule\" IN ('none', 'daily', 'weekly', 'monthly')"}, "check_booking_window_order": {"name": "check_booking_window_order", "value": "\"class_schedules\".\"booking_window_start\" IS NULL OR \"class_schedules\".\"booking_window_end\" IS NULL OR \"class_schedules\".\"booking_window_start\" <= \"class_schedules\".\"booking_window_end\""}, "check_checkin_window_order": {"name": "check_checkin_window_order", "value": "\"class_schedules\".\"check_in_window_start\" IS NULL OR \"class_schedules\".\"check_in_window_end\" IS NULL OR \"class_schedules\".\"check_in_window_start\" <= \"class_schedules\".\"check_in_window_end\""}}, "isRLSEnabled": false}, "public.class_subcategories": {"name": "class_subcategories", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": true}, "category_id": {"name": "category_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"class_subcategories_tenant_id_tenants_id_fk": {"name": "class_subcategories_tenant_id_tenants_id_fk", "tableFrom": "class_subcategories", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "class_subcategories_category_id_class_categories_id_fk": {"name": "class_subcategories_category_id_class_categories_id_fk", "tableFrom": "class_subcategories", "tableTo": "class_categories", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.classes": {"name": "classes", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "category_id": {"name": "category_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "subcategory_id": {"name": "subcategory_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "duration": {"name": "duration", "type": "integer", "primaryKey": false, "notNull": false}, "duration_unit": {"name": "duration_unit", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "level_id": {"name": "level_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "delivery_mode": {"name": "delivery_mode", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "is_private": {"name": "is_private", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "custom_cancellation_policy": {"name": "custom_cancellation_policy", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "cancellation_policy_description": {"name": "cancellation_policy_description", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "location_id": {"name": "location_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "images": {"name": "images", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "items_to_bring": {"name": "items_to_bring", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "youtube_links": {"name": "youtube_links", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "membership_plan_ids": {"name": "membership_plan_ids", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"classes_tenant_id_tenants_id_fk": {"name": "classes_tenant_id_tenants_id_fk", "tableFrom": "classes", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "classes_category_id_class_categories_id_fk": {"name": "classes_category_id_class_categories_id_fk", "tableFrom": "classes", "tableTo": "class_categories", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "classes_subcategory_id_class_subcategories_id_fk": {"name": "classes_subcategory_id_class_subcategories_id_fk", "tableFrom": "classes", "tableTo": "class_subcategories", "columnsFrom": ["subcategory_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "classes_level_id_class_levels_id_fk": {"name": "classes_level_id_class_levels_id_fk", "tableFrom": "classes", "tableTo": "class_levels", "columnsFrom": ["level_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "classes_location_id_locations_id_fk": {"name": "classes_location_id_locations_id_fk", "tableFrom": "classes", "tableTo": "locations", "columnsFrom": ["location_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.credit_transactions": {"name": "credit_transactions", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"credit_transactions_user_id_users_id_fk": {"name": "credit_transactions_user_id_users_id_fk", "tableFrom": "credit_transactions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "credit_transactions_organization_id_organizations_id_fk": {"name": "credit_transactions_organization_id_organizations_id_fk", "tableFrom": "credit_transactions", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.customer_auth_logs": {"name": "customer_auth_logs", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "customer_id": {"name": "customer_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": true}, "event": {"name": "event", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "method": {"name": "method", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": true}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "device_type": {"name": "device_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "device_id": {"name": "device_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "oauth_provider": {"name": "oauth_provider", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "oauth_account_id": {"name": "oauth_account_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "error_code": {"name": "error_code", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "error_message": {"name": "error_message", "type": "text", "primaryKey": false, "notNull": false}, "risk_score": {"name": "risk_score", "type": "integer", "primaryKey": false, "notNull": false}, "is_blocked": {"name": "is_blocked", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "block_reason": {"name": "block_reason", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "session_id": {"name": "session_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "location": {"name": "location", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "timezone": {"name": "timezone", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"customer_auth_logs_customer_id_idx": {"name": "customer_auth_logs_customer_id_idx", "columns": [{"expression": "customer_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "customer_auth_logs_tenant_id_idx": {"name": "customer_auth_logs_tenant_id_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "customer_auth_logs_event_idx": {"name": "customer_auth_logs_event_idx", "columns": [{"expression": "event", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "customer_auth_logs_status_idx": {"name": "customer_auth_logs_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "customer_auth_logs_ip_address_idx": {"name": "customer_auth_logs_ip_address_idx", "columns": [{"expression": "ip_address", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "customer_auth_logs_created_at_idx": {"name": "customer_auth_logs_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "customer_auth_logs_email_idx": {"name": "customer_auth_logs_email_idx", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"customer_auth_logs_customer_id_customers_id_fk": {"name": "customer_auth_logs_customer_id_customers_id_fk", "tableFrom": "customer_auth_logs", "tableTo": "customers", "columnsFrom": ["customer_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "customer_auth_logs_tenant_id_tenants_id_fk": {"name": "customer_auth_logs_tenant_id_tenants_id_fk", "tableFrom": "customer_auth_logs", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "customer_auth_logs_session_id_customer_sessions_id_fk": {"name": "customer_auth_logs_session_id_customer_sessions_id_fk", "tableFrom": "customer_auth_logs", "tableTo": "customer_sessions", "columnsFrom": ["session_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.customer_oauth_accounts": {"name": "customer_oauth_accounts", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "customer_id": {"name": "customer_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "provider_account_id": {"name": "provider_account_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": false}, "token_type": {"name": "token_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "session_state": {"name": "session_state", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "provider_profile": {"name": "provider_profile", "type": "jsonb", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "last_used": {"name": "last_used", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"customer_oauth_accounts_customer_id_customers_id_fk": {"name": "customer_oauth_accounts_customer_id_customers_id_fk", "tableFrom": "customer_oauth_accounts", "tableTo": "customers", "columnsFrom": ["customer_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "customer_oauth_accounts_tenant_id_tenants_id_fk": {"name": "customer_oauth_accounts_tenant_id_tenants_id_fk", "tableFrom": "customer_oauth_accounts", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.customer_oauth_challenges": {"name": "customer_oauth_challenges", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": true}, "code_challenge": {"name": "code_challenge", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "code_challenge_method": {"name": "code_challenge_method", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "state": {"name": "state", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "redirect_uri": {"name": "redirect_uri", "type": "text", "primaryKey": false, "notNull": true}, "scope": {"name": "scope", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "default": "'openid email profile'"}, "client_type": {"name": "client_type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": true}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "device_id": {"name": "device_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "is_used": {"name": "is_used", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "used_at": {"name": "used_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"customer_oauth_challenges_state_idx": {"name": "customer_oauth_challenges_state_idx", "columns": [{"expression": "state", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "customer_oauth_challenges_tenant_id_idx": {"name": "customer_oauth_challenges_tenant_id_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "customer_oauth_challenges_expires_at_idx": {"name": "customer_oauth_challenges_expires_at_idx", "columns": [{"expression": "expires_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"customer_oauth_challenges_tenant_id_tenants_id_fk": {"name": "customer_oauth_challenges_tenant_id_tenants_id_fk", "tableFrom": "customer_oauth_challenges", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"customer_oauth_challenges_state_unique": {"name": "customer_oauth_challenges_state_unique", "nullsNotDistinct": false, "columns": ["state"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.customer_sessions": {"name": "customer_sessions", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "customer_id": {"name": "customer_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": true}, "jti": {"name": "jti", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "token_hash": {"name": "token_hash", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "refresh_token_hash": {"name": "refresh_token_hash", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "device_type": {"name": "device_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "device_id": {"name": "device_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}, "location": {"name": "location", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "oauth_provider": {"name": "oauth_provider", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "oauth_account_id": {"name": "oauth_account_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "issued_at": {"name": "issued_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "refresh_expires_at": {"name": "refresh_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "last_used_at": {"name": "last_used_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "revoked_at": {"name": "revoked_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "revoked_by": {"name": "revoked_by", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "revoked_reason": {"name": "revoked_reason", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "is_suspicious": {"name": "is_suspicious", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"customer_sessions_customer_id_idx": {"name": "customer_sessions_customer_id_idx", "columns": [{"expression": "customer_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "customer_sessions_tenant_id_idx": {"name": "customer_sessions_tenant_id_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "customer_sessions_jti_idx": {"name": "customer_sessions_jti_idx", "columns": [{"expression": "jti", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "customer_sessions_expires_at_idx": {"name": "customer_sessions_expires_at_idx", "columns": [{"expression": "expires_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "customer_sessions_device_id_idx": {"name": "customer_sessions_device_id_idx", "columns": [{"expression": "device_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"customer_sessions_customer_id_customers_id_fk": {"name": "customer_sessions_customer_id_customers_id_fk", "tableFrom": "customer_sessions", "tableTo": "customers", "columnsFrom": ["customer_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "customer_sessions_tenant_id_tenants_id_fk": {"name": "customer_sessions_tenant_id_tenants_id_fk", "tableFrom": "customer_sessions", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"customer_sessions_jti_unique": {"name": "customer_sessions_jti_unique", "nullsNotDistinct": false, "columns": ["jti"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.customer_addresses": {"name": "customer_addresses", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "customer_id": {"name": "customer_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "address_line_1": {"name": "address_line_1", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "address_line_2": {"name": "address_line_2", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "city": {"name": "city", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "state": {"name": "state", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "zip": {"name": "zip", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "country": {"name": "country", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "is_primary": {"name": "is_primary", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"customer_addresses_customer_id_customers_id_fk": {"name": "customer_addresses_customer_id_customers_id_fk", "tableFrom": "customer_addresses", "tableTo": "customers", "columnsFrom": ["customer_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.customer_waivers": {"name": "customer_waivers", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "customer_id": {"name": "customer_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "waiver_form_id": {"name": "waiver_form_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "signed_at": {"name": "signed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "acknowledged": {"name": "acknowledged", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"customer_waivers_customer_id_customers_id_fk": {"name": "customer_waivers_customer_id_customers_id_fk", "tableFrom": "customer_waivers", "tableTo": "customers", "columnsFrom": ["customer_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "customer_waivers_waiver_form_id_waiver_forms_id_fk": {"name": "customer_waivers_waiver_form_id_waiver_forms_id_fk", "tableFrom": "customer_waivers", "tableTo": "waiver_forms", "columnsFrom": ["waiver_form_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.customers": {"name": "customers", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "mobile_country_code": {"name": "mobile_country_code", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "mobile_number": {"name": "mobile_number", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": false}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "date_of_birth": {"name": "date_of_birth", "type": "date", "primaryKey": false, "notNull": false}, "gender": {"name": "gender", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "location_id": {"name": "location_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "pricing_group_id": {"name": "pricing_group_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}, "google_id": {"name": "google_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "email_verified": {"name": "email_verified", "type": "timestamp", "primaryKey": false, "notNull": false}, "preferences": {"name": "preferences", "type": "jsonb", "primaryKey": false, "notNull": false}, "membership_type": {"name": "membership_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "'basic'"}, "membership_expires_at": {"name": "membership_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "is_email_verified": {"name": "is_email_verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "last_login_at": {"name": "last_login_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "last_login_ip": {"name": "last_login_ip", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}, "failed_login_attempts": {"name": "failed_login_attempts", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "locked_at": {"name": "locked_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "locked_until": {"name": "locked_until", "type": "timestamp", "primaryKey": false, "notNull": false}, "marketing_emails": {"name": "marketing_emails", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "terms_accepted_at": {"name": "terms_accepted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "terms_version": {"name": "terms_version", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "privacy_accepted_at": {"name": "privacy_accepted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "privacy_version": {"name": "privacy_version", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "tags": {"name": "tags", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "custom_fields": {"name": "custom_fields", "type": "jsonb", "primaryKey": false, "notNull": false}, "display_name": {"name": "display_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "created_by": {"name": "created_by", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "updated_by": {"name": "updated_by", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"customers_tenant_id_tenants_id_fk": {"name": "customers_tenant_id_tenants_id_fk", "tableFrom": "customers", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "customers_location_id_locations_id_fk": {"name": "customers_location_id_locations_id_fk", "tableFrom": "customers", "tableTo": "locations", "columnsFrom": ["location_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "customers_pricing_group_id_pricing_groups_id_fk": {"name": "customers_pricing_group_id_pricing_groups_id_fk", "tableFrom": "customers", "tableTo": "pricing_groups", "columnsFrom": ["pricing_group_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"customers_google_id_unique": {"name": "customers_google_id_unique", "nullsNotDistinct": false, "columns": ["google_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.equipment": {"name": "equipment", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "default_display_name": {"name": "default_display_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"equipment_tenant_id_tenants_id_fk": {"name": "equipment_tenant_id_tenants_id_fk", "tableFrom": "equipment", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.equipment_instances": {"name": "equipment_instances", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "equipment_id": {"name": "equipment_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "location_id": {"name": "location_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "quantity": {"name": "quantity", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "display_name": {"name": "display_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"equipment_instances_equipment_id_equipment_id_fk": {"name": "equipment_instances_equipment_id_equipment_id_fk", "tableFrom": "equipment_instances", "tableTo": "equipment", "columnsFrom": ["equipment_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "equipment_instances_location_id_locations_id_fk": {"name": "equipment_instances_location_id_locations_id_fk", "tableFrom": "equipment_instances", "tableTo": "locations", "columnsFrom": ["location_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.facilities": {"name": "facilities", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "images": {"name": "images", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"facilities_tenant_id_tenants_id_fk": {"name": "facilities_tenant_id_tenants_id_fk", "tableFrom": "facilities", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.invitations": {"name": "invitations", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "organization_id": {"name": "organization_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'member'"}, "invited_by": {"name": "invited_by", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "accepted_at": {"name": "accepted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"invitations_organization_id_organizations_id_fk": {"name": "invitations_organization_id_organizations_id_fk", "tableFrom": "invitations", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "invitations_invited_by_users_id_fk": {"name": "invitations_invited_by_users_id_fk", "tableFrom": "invitations", "tableTo": "users", "columnsFrom": ["invited_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"invitations_token_unique": {"name": "invitations_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.legacy_api_keys": {"name": "legacy_api_keys", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "last_used": {"name": "last_used", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"legacy_api_keys_user_id_users_id_fk": {"name": "legacy_api_keys_user_id_users_id_fk", "tableFrom": "legacy_api_keys", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "legacy_api_keys_organization_id_organizations_id_fk": {"name": "legacy_api_keys_organization_id_organizations_id_fk", "tableFrom": "legacy_api_keys", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"legacy_api_keys_key_unique": {"name": "legacy_api_keys_key_unique", "nullsNotDistinct": false, "columns": ["key"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.locations": {"name": "locations", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": false}, "address_line_1": {"name": "address_line_1", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "address_line_2": {"name": "address_line_2", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "city": {"name": "city", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "state": {"name": "state", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "country": {"name": "country", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "postal_code": {"name": "postal_code", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "phone_number": {"name": "phone_number", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"locations_tenant_id_tenants_id_fk": {"name": "locations_tenant_id_tenants_id_fk", "tableFrom": "locations", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.membership_plan_locations": {"name": "membership_plan_locations", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "membership_plan_id": {"name": "membership_plan_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "location_id": {"name": "location_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"idx_membership_plan_locations_plan_id": {"name": "idx_membership_plan_locations_plan_id", "columns": [{"expression": "membership_plan_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_membership_plan_locations_location_id": {"name": "idx_membership_plan_locations_location_id", "columns": [{"expression": "location_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_membership_plan_locations_tenant_id": {"name": "idx_membership_plan_locations_tenant_id", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"membership_plan_locations_membership_plan_id_membership_plans_id_fk": {"name": "membership_plan_locations_membership_plan_id_membership_plans_id_fk", "tableFrom": "membership_plan_locations", "tableTo": "membership_plans", "columnsFrom": ["membership_plan_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "membership_plan_locations_location_id_locations_id_fk": {"name": "membership_plan_locations_location_id_locations_id_fk", "tableFrom": "membership_plan_locations", "tableTo": "locations", "columnsFrom": ["location_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "membership_plan_locations_tenant_id_tenants_id_fk": {"name": "membership_plan_locations_tenant_id_tenants_id_fk", "tableFrom": "membership_plan_locations", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"membership_plan_locations_membership_plan_id_location_id_unique": {"name": "membership_plan_locations_membership_plan_id_location_id_unique", "nullsNotDistinct": false, "columns": ["membership_plan_id", "location_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.membership_plans": {"name": "membership_plans", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "price": {"name": "price", "type": "integer", "primaryKey": false, "notNull": false}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "duration": {"name": "duration", "type": "integer", "primaryKey": false, "notNull": false}, "duration_unit": {"name": "duration_unit", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"membership_plans_tenant_id_tenants_id_fk": {"name": "membership_plans_tenant_id_tenants_id_fk", "tableFrom": "membership_plans", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.organization_members": {"name": "organization_members", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "organization_id": {"name": "organization_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'member'"}, "invited_by": {"name": "invited_by", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "joined_at": {"name": "joined_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"organization_members_organization_id_organizations_id_fk": {"name": "organization_members_organization_id_organizations_id_fk", "tableFrom": "organization_members", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "organization_members_user_id_users_id_fk": {"name": "organization_members_user_id_users_id_fk", "tableFrom": "organization_members", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "organization_members_invited_by_users_id_fk": {"name": "organization_members_invited_by_users_id_fk", "tableFrom": "organization_members", "tableTo": "users", "columnsFrom": ["invited_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.organizations": {"name": "organizations", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "logo": {"name": "logo", "type": "text", "primaryKey": false, "notNull": false}, "plan": {"name": "plan", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'free'"}, "credits": {"name": "credits", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "monthly_credits": {"name": "monthly_credits", "type": "integer", "primaryKey": false, "notNull": true, "default": 1000}, "last_credit_refresh": {"name": "last_credit_refresh", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"organizations_slug_unique": {"name": "organizations_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.package_categories": {"name": "package_categories", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"package_categories_tenant_id_tenants_id_fk": {"name": "package_categories_tenant_id_tenants_id_fk", "tableFrom": "package_categories", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.package_category_rel": {"name": "package_category_rel", "schema": "", "columns": {"package_id": {"name": "package_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "category_id": {"name": "category_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"package_category_rel_package_id_package_id_fk": {"name": "package_category_rel_package_id_package_id_fk", "tableFrom": "package_category_rel", "tableTo": "package", "columnsFrom": ["package_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "package_category_rel_category_id_package_categories_id_fk": {"name": "package_category_rel_category_id_package_categories_id_fk", "tableFrom": "package_category_rel", "tableTo": "package_categories", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "package_category_rel_tenant_id_tenants_id_fk": {"name": "package_category_rel_tenant_id_tenants_id_fk", "tableFrom": "package_category_rel", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"package_category_rel_package_id_category_id_pk": {"name": "package_category_rel_package_id_category_id_pk", "columns": ["package_id", "category_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.package_customer_segements": {"name": "package_customer_segements", "schema": "", "columns": {"package_id": {"name": "package_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "customer_id": {"name": "customer_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"package_customer_segements_package_id_package_id_fk": {"name": "package_customer_segements_package_id_package_id_fk", "tableFrom": "package_customer_segements", "tableTo": "package", "columnsFrom": ["package_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "package_customer_segements_customer_id_customers_id_fk": {"name": "package_customer_segements_customer_id_customers_id_fk", "tableFrom": "package_customer_segements", "tableTo": "customers", "columnsFrom": ["customer_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.package_display_options": {"name": "package_display_options", "schema": "", "columns": {"package_id": {"name": "package_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "use_custom_description": {"name": "use_custom_description", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "custom_description": {"name": "custom_description", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"package_display_options_package_id_package_id_fk": {"name": "package_display_options_package_id_package_id_fk", "tableFrom": "package_display_options", "tableTo": "package", "columnsFrom": ["package_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.package_locations": {"name": "package_locations", "schema": "", "columns": {"package_id": {"name": "package_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "location_id": {"name": "location_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"package_locations_package_id_package_id_fk": {"name": "package_locations_package_id_package_id_fk", "tableFrom": "package_locations", "tableTo": "package", "columnsFrom": ["package_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "package_locations_location_id_locations_id_fk": {"name": "package_locations_location_id_locations_id_fk", "tableFrom": "package_locations", "tableTo": "locations", "columnsFrom": ["location_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"package_locations_package_id_location_id_pk": {"name": "package_locations_package_id_location_id_pk", "columns": ["package_id", "location_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.package_pricing": {"name": "package_pricing", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "package_id": {"name": "package_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "pricing_group_id": {"name": "pricing_group_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "price": {"name": "price", "type": "integer", "primaryKey": false, "notNull": false}, "credit_amount": {"name": "credit_amount", "type": "integer", "primaryKey": false, "notNull": false}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "sort_order": {"name": "sort_order", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"package_pricing_package_id_package_id_fk": {"name": "package_pricing_package_id_package_id_fk", "tableFrom": "package_pricing", "tableTo": "package", "columnsFrom": ["package_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "package_pricing_pricing_group_id_pricing_groups_id_fk": {"name": "package_pricing_pricing_group_id_pricing_groups_id_fk", "tableFrom": "package_pricing", "tableTo": "pricing_groups", "columnsFrom": ["pricing_group_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.package_purchase_options": {"name": "package_purchase_options", "schema": "", "columns": {"package_id": {"name": "package_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "purchase_limit": {"name": "purchase_limit", "type": "integer", "primaryKey": false, "notNull": false}, "restrict_to": {"name": "restrict_to", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "transferable": {"name": "transferable", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "specify_sold_at_location": {"name": "specify_sold_at_location", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "sold_at_location_id": {"name": "sold_at_location_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "class_booking_limit": {"name": "class_booking_limit", "type": "integer", "primaryKey": false, "notNull": false}, "show_online": {"name": "show_online", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "show_onsite": {"name": "show_onsite", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "is_private": {"name": "is_private", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"package_purchase_options_package_id_package_id_fk": {"name": "package_purchase_options_package_id_package_id_fk", "tableFrom": "package_purchase_options", "tableTo": "package", "columnsFrom": ["package_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "package_purchase_options_sold_at_location_id_locations_id_fk": {"name": "package_purchase_options_sold_at_location_id_locations_id_fk", "tableFrom": "package_purchase_options", "tableTo": "locations", "columnsFrom": ["sold_at_location_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.package": {"name": "package", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "is_private": {"name": "is_private", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "validity_date": {"name": "validity_date", "type": "date", "primaryKey": false, "notNull": false}, "validity_duration": {"name": "validity_duration", "type": "integer", "primaryKey": false, "notNull": false}, "schedule_availability": {"name": "schedule_availability", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "included_classes": {"name": "included_classes", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"package_tenant_id_tenants_id_fk": {"name": "package_tenant_id_tenants_id_fk", "tableFrom": "package", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.permissions": {"name": "permissions", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "module": {"name": "module", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "action": {"name": "action", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "resource": {"name": "resource", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "display_name": {"name": "display_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_system_permission": {"name": "is_system_permission", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.pricing_groups": {"name": "pricing_groups", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "discount_percentage": {"name": "discount_percentage", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "is_default": {"name": "is_default", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "sort_order": {"name": "sort_order", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"pricing_groups_tenant_id_tenants_id_fk": {"name": "pricing_groups_tenant_id_tenants_id_fk", "tableFrom": "pricing_groups", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.rbac_activity_logs": {"name": "rbac_activity_logs", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "target_user_id": {"name": "target_user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "action": {"name": "action", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "resource_type": {"name": "resource_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "resource_id": {"name": "resource_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "old_value": {"name": "old_value", "type": "jsonb", "primaryKey": false, "notNull": false}, "new_value": {"name": "new_value", "type": "jsonb", "primaryKey": false, "notNull": false}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"rbac_activity_logs_tenant_id_tenants_id_fk": {"name": "rbac_activity_logs_tenant_id_tenants_id_fk", "tableFrom": "rbac_activity_logs", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "rbac_activity_logs_user_id_users_id_fk": {"name": "rbac_activity_logs_user_id_users_id_fk", "tableFrom": "rbac_activity_logs", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "rbac_activity_logs_target_user_id_users_id_fk": {"name": "rbac_activity_logs_target_user_id_users_id_fk", "tableFrom": "rbac_activity_logs", "tableTo": "users", "columnsFrom": ["target_user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.role_permissions": {"name": "role_permissions", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "role_id": {"name": "role_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "permission_id": {"name": "permission_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "conditions": {"name": "conditions", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"role_permissions_role_id_roles_id_fk": {"name": "role_permissions_role_id_roles_id_fk", "tableFrom": "role_permissions", "tableTo": "roles", "columnsFrom": ["role_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "role_permissions_permission_id_permissions_id_fk": {"name": "role_permissions_permission_id_permissions_id_fk", "tableFrom": "role_permissions", "tableTo": "permissions", "columnsFrom": ["permission_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"unique_role_permission": {"name": "unique_role_permission", "nullsNotDistinct": false, "columns": ["role_id", "permission_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.roles": {"name": "roles", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "display_name": {"name": "display_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_system_role": {"name": "is_system_role", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "hierarchy_level": {"name": "hierarchy_level", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "permissions": {"name": "permissions", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"roles_tenant_id_tenants_id_fk": {"name": "roles_tenant_id_tenants_id_fk", "tableFrom": "roles", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sessions": {"name": "sessions", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "session_token": {"name": "session_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "user_type": {"name": "user_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "expires": {"name": "expires", "type": "timestamp", "primaryKey": false, "notNull": true}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "last_activity": {"name": "last_activity", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "CURRENT_TIMESTAMP"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"sessions_tenant_id_tenants_id_fk": {"name": "sessions_tenant_id_tenants_id_fk", "tableFrom": "sessions", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"sessions_session_token_unique": {"name": "sessions_session_token_unique", "nullsNotDistinct": false, "columns": ["session_token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.settings": {"name": "settings", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": false}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "time_zone": {"name": "time_zone", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "accepted_at": {"name": "accepted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"settings_tenant_id_tenants_id_fk": {"name": "settings_tenant_id_tenants_id_fk", "tableFrom": "settings", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.stripe_customers": {"name": "stripe_customers", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "organization_id": {"name": "organization_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "stripe_customer_id": {"name": "stripe_customer_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"stripe_customers_user_id_users_id_fk": {"name": "stripe_customers_user_id_users_id_fk", "tableFrom": "stripe_customers", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "stripe_customers_organization_id_organizations_id_fk": {"name": "stripe_customers_organization_id_organizations_id_fk", "tableFrom": "stripe_customers", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"stripe_customers_stripe_customer_id_unique": {"name": "stripe_customers_stripe_customer_id_unique", "nullsNotDistinct": false, "columns": ["stripe_customer_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.stripe_payments": {"name": "stripe_payments", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "stripe_payment_intent_id": {"name": "stripe_payment_intent_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "stripe_customer_id": {"name": "stripe_customer_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "organization_id": {"name": "organization_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(3)", "primaryKey": false, "notNull": true, "default": "'usd'"}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "credits": {"name": "credits", "type": "integer", "primaryKey": false, "notNull": true}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"stripe_payments_user_id_users_id_fk": {"name": "stripe_payments_user_id_users_id_fk", "tableFrom": "stripe_payments", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "stripe_payments_organization_id_organizations_id_fk": {"name": "stripe_payments_organization_id_organizations_id_fk", "tableFrom": "stripe_payments", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"stripe_payments_stripe_payment_intent_id_unique": {"name": "stripe_payments_stripe_payment_intent_id_unique", "nullsNotDistinct": false, "columns": ["stripe_payment_intent_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tags": {"name": "tags", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": true}, "customer_id": {"name": "customer_id", "type": "text", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "custom_color": {"name": "custom_color", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"tags_tenant_id_tenants_id_fk": {"name": "tags_tenant_id_tenants_id_fk", "tableFrom": "tags", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "tags_customer_id_customers_id_fk": {"name": "tags_customer_id_customers_id_fk", "tableFrom": "tags", "tableTo": "customers", "columnsFrom": ["customer_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tenant_domains": {"name": "tenant_domains", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": true}, "domain": {"name": "domain", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "is_verified": {"name": "is_verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "is_primary": {"name": "is_primary", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "ssl_enabled": {"name": "ssl_enabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "verification_token": {"name": "verification_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "verified_at": {"name": "verified_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"tenant_domains_tenant_id_tenants_id_fk": {"name": "tenant_domains_tenant_id_tenants_id_fk", "tableFrom": "tenant_domains", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"tenant_domains_domain_unique": {"name": "tenant_domains_domain_unique", "nullsNotDistinct": false, "columns": ["domain"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tenants": {"name": "tenants", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "subdomain": {"name": "subdomain", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "custom_domain": {"name": "custom_domain", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "settings": {"name": "settings", "type": "jsonb", "primaryKey": false, "notNull": false}, "subscription_tier": {"name": "subscription_tier", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'basic'"}, "max_users": {"name": "max_users", "type": "integer", "primaryKey": false, "notNull": true, "default": 100}, "max_customers": {"name": "max_customers", "type": "integer", "primaryKey": false, "notNull": true, "default": 1000}, "password_policy": {"name": "password_policy", "type": "jsonb", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"tenants_organization_id_organizations_id_fk": {"name": "tenants_organization_id_organizations_id_fk", "tableFrom": "tenants", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"tenants_subdomain_unique": {"name": "tenants_subdomain_unique", "nullsNotDistinct": false, "columns": ["subdomain"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.usage_logs": {"name": "usage_logs", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "organization_id": {"name": "organization_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "api_key_id": {"name": "api_key_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "endpoint": {"name": "endpoint", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "method": {"name": "method", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "status_code": {"name": "status_code", "type": "integer", "primaryKey": false, "notNull": true}, "credits_used": {"name": "credits_used", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "response_time": {"name": "response_time", "type": "integer", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"usage_logs_user_id_users_id_fk": {"name": "usage_logs_user_id_users_id_fk", "tableFrom": "usage_logs", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "usage_logs_organization_id_organizations_id_fk": {"name": "usage_logs_organization_id_organizations_id_fk", "tableFrom": "usage_logs", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "usage_logs_api_key_id_legacy_api_keys_id_fk": {"name": "usage_logs_api_key_id_legacy_api_keys_id_fk", "tableFrom": "usage_logs", "tableTo": "legacy_api_keys", "columnsFrom": ["api_key_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_location_access": {"name": "user_location_access", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "location_id": {"name": "location_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": true}, "access_level": {"name": "access_level", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'full'"}, "assigned_by": {"name": "assigned_by", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "assigned_at": {"name": "assigned_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"user_location_access_user_id_users_id_fk": {"name": "user_location_access_user_id_users_id_fk", "tableFrom": "user_location_access", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_location_access_location_id_locations_id_fk": {"name": "user_location_access_location_id_locations_id_fk", "tableFrom": "user_location_access", "tableTo": "locations", "columnsFrom": ["location_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_location_access_tenant_id_tenants_id_fk": {"name": "user_location_access_tenant_id_tenants_id_fk", "tableFrom": "user_location_access", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_location_access_assigned_by_users_id_fk": {"name": "user_location_access_assigned_by_users_id_fk", "tableFrom": "user_location_access", "tableTo": "users", "columnsFrom": ["assigned_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"unique_user_location": {"name": "unique_user_location", "columns": ["user_id", "location_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_roles": {"name": "user_roles", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "role_id": {"name": "role_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": false}, "assigned_by": {"name": "assigned_by", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "assigned_at": {"name": "assigned_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"user_roles_user_id_users_id_fk": {"name": "user_roles_user_id_users_id_fk", "tableFrom": "user_roles", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_roles_role_id_roles_id_fk": {"name": "user_roles_role_id_roles_id_fk", "tableFrom": "user_roles", "tableTo": "roles", "columnsFrom": ["role_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_roles_tenant_id_tenants_id_fk": {"name": "user_roles_tenant_id_tenants_id_fk", "tableFrom": "user_roles", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_roles_assigned_by_users_id_fk": {"name": "user_roles_assigned_by_users_id_fk", "tableFrom": "user_roles", "tableTo": "users", "columnsFrom": ["assigned_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {"unique_user_role_tenant": {"name": "unique_user_role_tenant", "columns": ["user_id", "role_id", "tenant_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email_verified": {"name": "email_verified", "type": "timestamp", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'user'"}, "organization_id": {"name": "organization_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": false}, "location_id": {"name": "location_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "credits": {"name": "credits", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "monthly_credits": {"name": "monthly_credits", "type": "integer", "primaryKey": false, "notNull": true, "default": 100}, "last_credit_refresh": {"name": "last_credit_refresh", "type": "timestamp", "primaryKey": false, "notNull": false}, "employee_id": {"name": "employee_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "departement": {"name": "departement", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "position": {"name": "position", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "hire_date": {"name": "hire_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "emergency_contact": {"name": "emergency_contact", "type": "jsonb", "primaryKey": false, "notNull": false}, "certifications": {"name": "certifications", "type": "jsonb", "primaryKey": false, "notNull": false}, "specializations": {"name": "specializations", "type": "jsonb", "primaryKey": false, "notNull": false}, "bio": {"name": "bio", "type": "text", "primaryKey": false, "notNull": false}, "permissions": {"name": "permissions", "type": "jsonb", "primaryKey": false, "notNull": false}, "accessible_locations": {"name": "accessible_locations", "type": "jsonb", "primaryKey": false, "notNull": false}, "last_login_at": {"name": "last_login_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "last_login_ip": {"name": "last_login_ip", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}, "failed_login_attempts": {"name": "failed_login_attempts", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "locked_at": {"name": "locked_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "locked_until": {"name": "locked_until", "type": "timestamp", "primaryKey": false, "notNull": false}, "mfa_enabled": {"name": "mfa_enabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "mfa_secret": {"name": "mfa_secret", "type": "text", "primaryKey": false, "notNull": false}, "backup_codes": {"name": "backup_codes", "type": "jsonb", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "created_by": {"name": "created_by", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "updated_by": {"name": "updated_by", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"users_organization_id_organizations_id_fk": {"name": "users_organization_id_organizations_id_fk", "tableFrom": "users", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "users_tenant_id_tenants_id_fk": {"name": "users_tenant_id_tenants_id_fk", "tableFrom": "users", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "users_location_id_locations_id_fk": {"name": "users_location_id_locations_id_fk", "tableFrom": "users", "tableTo": "locations", "columnsFrom": ["location_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.verification_tokens": {"name": "verification_tokens", "schema": "", "columns": {"identifier": {"name": "identifier", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "expires": {"name": "expires", "type": "timestamp", "primaryKey": false, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "user_type": {"name": "user_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "used_at": {"name": "used_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"verification_tokens_tenant_id_tenants_id_fk": {"name": "verification_tokens_tenant_id_tenants_id_fk", "tableFrom": "verification_tokens", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"verification_tokens_token_unique": {"name": "verification_tokens_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.voucher_customer_assignments": {"name": "voucher_customer_assignments", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "voucher_id": {"name": "voucher_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "customer_id": {"name": "customer_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "assigned_by": {"name": "assigned_by", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "assigned_at": {"name": "assigned_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"idx_voucher_assignments_voucher": {"name": "idx_voucher_assignments_voucher", "columns": [{"expression": "voucher_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_voucher_assignments_customer": {"name": "idx_voucher_assignments_customer", "columns": [{"expression": "customer_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_voucher_assignments_active": {"name": "idx_voucher_assignments_active", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "assigned_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"voucher_customer_assignments_voucher_id_vouchers_id_fk": {"name": "voucher_customer_assignments_voucher_id_vouchers_id_fk", "tableFrom": "voucher_customer_assignments", "tableTo": "vouchers", "columnsFrom": ["voucher_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "voucher_customer_assignments_customer_id_customers_id_fk": {"name": "voucher_customer_assignments_customer_id_customers_id_fk", "tableFrom": "voucher_customer_assignments", "tableTo": "customers", "columnsFrom": ["customer_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "voucher_customer_assignments_assigned_by_users_id_fk": {"name": "voucher_customer_assignments_assigned_by_users_id_fk", "tableFrom": "voucher_customer_assignments", "tableTo": "users", "columnsFrom": ["assigned_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"unique_voucher_customer": {"name": "unique_voucher_customer", "nullsNotDistinct": false, "columns": ["voucher_id", "customer_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.voucher_usage": {"name": "voucher_usage", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "voucher_id": {"name": "voucher_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "customer_id": {"name": "customer_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "order_id": {"name": "order_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "package_id": {"name": "package_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "class_id": {"name": "class_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "location_id": {"name": "location_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "original_amount": {"name": "original_amount", "type": "integer", "primaryKey": false, "notNull": true}, "discount_amount": {"name": "discount_amount", "type": "integer", "primaryKey": false, "notNull": true}, "final_amount": {"name": "final_amount", "type": "integer", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(3)", "primaryKey": false, "notNull": true, "default": "'USD'"}, "used_at": {"name": "used_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"idx_voucher_usage_voucher": {"name": "idx_voucher_usage_voucher", "columns": [{"expression": "voucher_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_voucher_usage_customer": {"name": "idx_voucher_usage_customer", "columns": [{"expression": "customer_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_voucher_usage_date": {"name": "idx_voucher_usage_date", "columns": [{"expression": "used_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_voucher_usage_order": {"name": "idx_voucher_usage_order", "columns": [{"expression": "order_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"voucher_usage_voucher_id_vouchers_id_fk": {"name": "voucher_usage_voucher_id_vouchers_id_fk", "tableFrom": "voucher_usage", "tableTo": "vouchers", "columnsFrom": ["voucher_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "voucher_usage_customer_id_customers_id_fk": {"name": "voucher_usage_customer_id_customers_id_fk", "tableFrom": "voucher_usage", "tableTo": "customers", "columnsFrom": ["customer_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "voucher_usage_package_id_package_id_fk": {"name": "voucher_usage_package_id_package_id_fk", "tableFrom": "voucher_usage", "tableTo": "package", "columnsFrom": ["package_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "voucher_usage_class_id_classes_id_fk": {"name": "voucher_usage_class_id_classes_id_fk", "tableFrom": "voucher_usage", "tableTo": "classes", "columnsFrom": ["class_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "voucher_usage_location_id_locations_id_fk": {"name": "voucher_usage_location_id_locations_id_fk", "tableFrom": "voucher_usage", "tableTo": "locations", "columnsFrom": ["location_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {"check_positive_amounts": {"name": "check_positive_amounts", "value": "\"voucher_usage\".\"original_amount\" > 0 AND \"voucher_usage\".\"discount_amount\" >= 0 AND \"voucher_usage\".\"final_amount\" >= 0"}, "check_valid_discount": {"name": "check_valid_discount", "value": "\"voucher_usage\".\"discount_amount\" <= \"voucher_usage\".\"original_amount\""}, "check_final_amount": {"name": "check_final_amount", "value": "\"voucher_usage\".\"final_amount\" = \"voucher_usage\".\"original_amount\" - \"voucher_usage\".\"discount_amount\""}}, "isRLSEnabled": false}, "public.vouchers": {"name": "vouchers", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "integer", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(3)", "primaryKey": false, "notNull": true, "default": "'USD'"}, "usage_limit": {"name": "usage_limit", "type": "integer", "primaryKey": false, "notNull": false}, "usage_limit_per_customer": {"name": "usage_limit_per_customer", "type": "integer", "primaryKey": false, "notNull": false, "default": 1}, "current_usage_count": {"name": "current_usage_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "valid_from": {"name": "valid_from", "type": "timestamp", "primaryKey": false, "notNull": true}, "valid_until": {"name": "valid_until", "type": "timestamp", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "is_public": {"name": "is_public", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "auto_apply": {"name": "auto_apply", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "restrictions": {"name": "restrictions", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'::jsonb"}, "created_by": {"name": "created_by", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"idx_vouchers_tenant_active": {"name": "idx_vouchers_tenant_active", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_vouchers_code_lookup": {"name": "idx_vouchers_code_lookup", "columns": [{"expression": "code", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_vouchers_validity": {"name": "idx_vouchers_validity", "columns": [{"expression": "valid_from", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "valid_until", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_vouchers_usage": {"name": "idx_vouchers_usage", "columns": [{"expression": "usage_limit", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "current_usage_count", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"vouchers_tenant_id_tenants_id_fk": {"name": "vouchers_tenant_id_tenants_id_fk", "tableFrom": "vouchers", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "vouchers_created_by_users_id_fk": {"name": "vouchers_created_by_users_id_fk", "tableFrom": "vouchers", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"unique_tenant_voucher_code": {"name": "unique_tenant_voucher_code", "nullsNotDistinct": false, "columns": ["tenant_id", "code"]}}, "policies": {}, "checkConstraints": {"check_valid_voucher_type": {"name": "check_valid_voucher_type", "value": "\"vouchers\".\"type\" IN ('percentage', 'fixed_amount', 'free_shipping', 'buy_x_get_y')"}, "check_valid_percentage": {"name": "check_valid_percentage", "value": "\"vouchers\".\"type\" != 'percentage' OR (\"vouchers\".\"value\" >= 1 AND \"vouchers\".\"value\" <= 100)"}, "check_positive_value": {"name": "check_positive_value", "value": "\"vouchers\".\"value\" > 0"}, "check_valid_period": {"name": "check_valid_period", "value": "\"vouchers\".\"valid_from\" < \"vouchers\".\"valid_until\""}, "check_usage_limits": {"name": "check_usage_limits", "value": "\"vouchers\".\"usage_limit\" IS NULL OR \"vouchers\".\"usage_limit\" > 0"}, "check_usage_limit_per_customer": {"name": "check_usage_limit_per_customer", "value": "\"vouchers\".\"usage_limit_per_customer\" > 0"}, "check_current_usage": {"name": "check_current_usage", "value": "\"vouchers\".\"current_usage_count\" >= 0"}, "check_usage_not_exceeded": {"name": "check_usage_not_exceeded", "value": "\"vouchers\".\"usage_limit\" IS NULL OR \"vouchers\".\"current_usage_count\" <= \"vouchers\".\"usage_limit\""}}, "isRLSEnabled": false}, "public.waiver_forms": {"name": "waiver_forms", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "version": {"name": "version", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'1.0'"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "is_required": {"name": "is_required", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "expiry_days": {"name": "expiry_days", "type": "integer", "primaryKey": false, "notNull": false, "default": 365}, "sort_order": {"name": "sort_order", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"waiver_forms_tenant_id_tenants_id_fk": {"name": "waiver_forms_tenant_id_tenants_id_fk", "tableFrom": "waiver_forms", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.webhook_deliveries": {"name": "webhook_deliveries", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "webhook_endpoint_id": {"name": "webhook_endpoint_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "event_id": {"name": "event_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "event_type": {"name": "event_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "payload": {"name": "payload", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "attempts": {"name": "attempts", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "last_attempt_at": {"name": "last_attempt_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "next_attempt_at": {"name": "next_attempt_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "response_status": {"name": "response_status", "type": "integer", "primaryKey": false, "notNull": false}, "response_body": {"name": "response_body", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"webhook_deliveries_webhook_endpoint_id_webhook_endpoints_id_fk": {"name": "webhook_deliveries_webhook_endpoint_id_webhook_endpoints_id_fk", "tableFrom": "webhook_deliveries", "tableTo": "webhook_endpoints", "columnsFrom": ["webhook_endpoint_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.webhook_endpoints": {"name": "webhook_endpoints", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": true}, "url": {"name": "url", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": true}, "events": {"name": "events", "type": "text", "primaryKey": false, "notNull": true}, "secret": {"name": "secret", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "retry_policy": {"name": "retry_policy", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"webhook_endpoints_tenant_id_tenants_id_fk": {"name": "webhook_endpoints_tenant_id_tenants_id_fk", "tableFrom": "webhook_endpoints", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.webhooks": {"name": "webhooks", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "provider": {"name": "provider", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "event_type": {"name": "event_type", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "event_id": {"name": "event_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "data": {"name": "data", "type": "jsonb", "primaryKey": false, "notNull": true}, "processed": {"name": "processed", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "processed_at": {"name": "processed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "error": {"name": "error", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}