import { useQuery } from '@tanstack/react-query';
import { 
  CustomerSegment, 
  CustomerSegmentFilters, 
  CustomerSegmentStats 
} from '@/lib/services/customer-segments.service';
import { Customer } from '@/lib/db/schema';

// Query keys for customer segments
const customerSegmentKeys = {
  all: ['customer-segments'] as const,
  lists: () => [...customerSegmentKeys.all, 'list'] as const,
  list: (filters: CustomerSegmentFilters) => [...customerSegmentKeys.lists(), { filters }] as const,
  byTenant: (tenantId: number) => [...customerSegmentKeys.all, 'by-tenant', tenantId] as const,
  detail: (id: string) => [...customerSegmentKeys.all, 'detail', id] as const,
  customers: (segmentId: string, tenantId: number) => [...customerSegmentKeys.all, 'customers', segmentId, tenantId] as const,
  stats: (tenantId?: number) => [...customerSegmentKeys.all, 'stats', tenantId] as const,
};

// API functions for customer segments
const customerSegmentApi = {
  async getByTenant(tenantId: number, filters?: CustomerSegmentFilters): Promise<CustomerSegment[]> {
    const params = new URLSearchParams();
    params.append('tenantId', String(tenantId));
    
    if (filters?.search) params.append('search', filters.search);
    if (filters?.type) params.append('type', filters.type);
    if (filters?.isActive !== undefined) params.append('isActive', String(filters.isActive));

    const response = await fetch(`/api/customer-segments?${params}`);
    if (!response.ok) {
      throw new Error('Failed to fetch customer segments');
    }

    const data = await response.json();
    return data.data || [];
  },

  async getCustomersInSegment(segmentId: string, tenantId: number): Promise<Customer[]> {
    const response = await fetch(`/api/customer-segments/${segmentId}/customers?tenantId=${tenantId}`);
    if (!response.ok) {
      throw new Error('Failed to fetch customers in segment');
    }

    const data = await response.json();
    return data.data || [];
  },

  async getStats(tenantId?: number): Promise<CustomerSegmentStats> {
    const params = new URLSearchParams();
    if (tenantId) params.append('tenantId', String(tenantId));

    const response = await fetch(`/api/customer-segments/stats?${params}`);
    if (!response.ok) {
      throw new Error('Failed to fetch customer segment statistics');
    }

    const data = await response.json();
    return data.data;
  },
};

/**
 * Get customer segments by tenant
 */
export function useCustomerSegments(tenantId: number, filters?: CustomerSegmentFilters) {
  return useQuery({
    queryKey: customerSegmentKeys.byTenant(tenantId),
    queryFn: () => customerSegmentApi.getByTenant(tenantId, filters),
    enabled: !!tenantId && tenantId > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Get customer segments with filters
 */
export function useCustomerSegmentsFiltered(tenantId: number, filters: CustomerSegmentFilters) {
  return useQuery({
    queryKey: customerSegmentKeys.list(filters),
    queryFn: () => customerSegmentApi.getByTenant(tenantId, filters),
    enabled: !!tenantId && tenantId > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Get customers in a specific segment
 */
export function useCustomersInSegment(segmentId: string, tenantId: number, options?: { enabled?: boolean }) {
  return useQuery({
    queryKey: customerSegmentKeys.customers(segmentId, tenantId),
    queryFn: () => customerSegmentApi.getCustomersInSegment(segmentId, tenantId),
    enabled: options?.enabled !== false && !!segmentId && !!tenantId && tenantId > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Get customer segment statistics
 */
export function useCustomerSegmentStats(tenantId?: number) {
  return useQuery({
    queryKey: customerSegmentKeys.stats(tenantId),
    queryFn: () => customerSegmentApi.getStats(tenantId),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

/**
 * Get customer segments for dropdown/select usage
 */
export function useCustomerSegmentsForSelect(tenantId: number) {
  const query = useQuery({
    queryKey: [...customerSegmentKeys.byTenant(tenantId), 'for-select'],
    queryFn: () => customerSegmentApi.getByTenant(tenantId),
    enabled: !!tenantId && tenantId > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
    select: (data) => {
      // Transform data for select component usage
      return data.map(segment => ({
        value: segment.id,
        label: segment.name,
        description: segment.description,
        customerCount: segment.customerCount,
        type: segment.type,
      }));
    },
  });

  return {
    ...query,
    // Backward compatibility
    segments: query.data || [],
    loading: query.isLoading,
    error: query.error?.message || null,
  };
}

/**
 * Get built-in customer segments (all, members, new, existing)
 */
export function useBuiltInCustomerSegments(tenantId: number) {
  return useQuery({
    queryKey: [...customerSegmentKeys.byTenant(tenantId), 'built-in'],
    queryFn: () => customerSegmentApi.getByTenant(tenantId, { type: 'all' }),
    enabled: !!tenantId && tenantId > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
    select: (data) => {
      // Filter to only built-in segments
      return data.filter(segment => 
        ['all', 'members', 'new_customers', 'existing_customers'].includes(segment.type)
      );
    },
  });
}

/**
 * Get pricing group based segments
 */
export function usePricingGroupSegments(tenantId: number) {
  return useQuery({
    queryKey: [...customerSegmentKeys.byTenant(tenantId), 'pricing-groups'],
    queryFn: () => customerSegmentApi.getByTenant(tenantId, { type: 'pricing_group' }),
    enabled: !!tenantId && tenantId > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Get location based segments
 */
export function useLocationSegments(tenantId: number) {
  return useQuery({
    queryKey: [...customerSegmentKeys.byTenant(tenantId), 'locations'],
    queryFn: () => customerSegmentApi.getByTenant(tenantId, { type: 'location' }),
    enabled: !!tenantId && tenantId > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Export types for use in components
export type {
  CustomerSegment,
  CustomerSegmentFilters,
  CustomerSegmentStats,
};

// Export query keys for advanced usage
export { customerSegmentKeys };
