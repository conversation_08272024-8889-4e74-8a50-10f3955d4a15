import { DefaultSession } from "next-auth";
import { JWT } from "next-auth/jwt";

// Extend the built-in session types
declare module "next-auth" {
  interface Session {
    user: {
      id: string;
      email: string;
      name?: string | null;
      image?: string | null;
      tenantId?: number | null;
      roles?: string[];
      permissions?: string[];
      accessibleLocations?: string[];
    } & DefaultSession["user"];
  }

  interface User {
    id: string;
    email: string;
    name?: string | null;
    image?: string | null;
    tenantId?: number | null;
    roles?: string[];
    permissions?: string[];
    accessibleLocations?: string[];
  }
}

// Extend the built-in JWT types
declare module "next-auth/jwt" {
  interface JWT {
    id: string;
    email: string;
    name?: string | null;
    tenantId?: number | null;
    roles?: string[];
    permissions?: string[];
    accessibleLocations?: string[];
    rbacLastUpdated?: number; // timestamp untuk cache invalidation
  }
}

// User roles
export type UserRole = "user" | "admin";

// Organization roles
export type OrganizationRole = "owner" | "admin" | "member";

// Auth-related types
export interface AuthUser {
  id: string;
  email: string;
  name?: string;
  image?: string;
  role: UserRole;
  organizationId?: string;
  tenantId?: number;
  emailVerified?: Date;
}

export interface SignUpData {
  email: string;
  password: string;
  name: string;
  organizationName?: string;
}

export interface SignInData {
  email: string;
  password: string;
}

export interface ResetPasswordData {
  email: string;
}

export interface ChangePasswordData {
  currentPassword: string;
  newPassword: string;
}

export interface VerifyEmailData {
  token: string;
}

// WebAuthn types
export interface WebAuthnCredential {
  id: string;
  rawId: ArrayBuffer;
  response: AuthenticatorAttestationResponse | AuthenticatorAssertionResponse;
  type: "public-key";
}

export interface WebAuthnRegistrationOptions {
  challenge: string;
  rp: {
    name: string;
    id: string;
  };
  user: {
    id: string;
    name: string;
    displayName: string;
  };
  pubKeyCredParams: Array<{
    type: "public-key";
    alg: number;
  }>;
  authenticatorSelection?: {
    authenticatorAttachment?: "platform" | "cross-platform";
    userVerification?: "required" | "preferred" | "discouraged";
    residentKey?: "required" | "preferred" | "discouraged";
  };
  timeout?: number;
  attestation?: "none" | "indirect" | "direct" | "enterprise";
}

export interface WebAuthnAuthenticationOptions {
  challenge: string;
  timeout?: number;
  rpId?: string;
  allowCredentials?: Array<{
    type: "public-key";
    id: string;
    transports?: Array<"usb" | "nfc" | "ble" | "internal">;
  }>;
  userVerification?: "required" | "preferred" | "discouraged";
}

// Rate limiting types
export interface RateLimitConfig {
  windowMs: number;
  maxAttempts: number;
  blockDurationMs?: number;
}

export interface RateLimitResult {
  success: boolean;
  remaining: number;
  resetTime: Date;
  blocked?: boolean;
}

// Session management types
export interface SessionInfo {
  id: string;
  userId: string;
  sessionToken: string;
  expires: Date;
  createdAt: Date;
  userAgent?: string;
  ipAddress?: string;
  isCurrent: boolean;
}

// Email verification types
export interface EmailVerificationToken {
  email: string;
  token: string;
  expires: Date;
}

// Password reset types
export interface PasswordResetToken {
  email: string;
  token: string;
  expires: Date;
}
