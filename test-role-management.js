// Test script to verify role management functionality
const { RoleService } = require('./src/lib/services/role.service.ts');

async function testRoleManagement() {
  console.log('🧪 Testing Role Management System...\n');

  try {
    // Test 1: Create a test role
    console.log('1️⃣ Creating test role...');
    const testRole = await RoleService.create({
      name: 'test_manager',
      display_name: 'Test Manager',
      description: 'A test role for verification',
      is_system_role: false,
      hierarchy_level: 50,
      tenantId: 1,
      permissions: []
    });
    console.log('✅ Test role created:', testRole.id);

    // Test 2: Search for roles
    console.log('\n2️⃣ Searching for roles...');
    const searchResult = await RoleService.searchRoles(null, undefined, undefined, 10, 0);
    console.log('✅ Found roles:', searchResult.roles.length);
    console.log('📋 Roles:', searchResult.roles.map(r => `${r.name} (${r.display_name})`));

    // Test 3: Get role by ID
    console.log('\n3️⃣ Getting role by ID...');
    const retrievedRole = await RoleService.getById(testRole.id);
    console.log('✅ Retrieved role:', retrievedRole?.name);

    // Test 4: Update role
    console.log('\n4️⃣ Updating role...');
    const updatedRole = await RoleService.update(testRole.id, {
      description: 'Updated test role description'
    });
    console.log('✅ Role updated:', updatedRole.description);

    // Test 5: Delete role (soft delete)
    console.log('\n5️⃣ Deleting role...');
    await RoleService.delete(testRole.id);
    console.log('✅ Role deleted (soft delete)');

    console.log('\n🎉 All tests passed! Role Management System is working correctly.');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Run the test
testRoleManagement();
