import { toast } from "sonner";

/**
 * RBAC Error Handler
 * 
 * Utility ini handle RBAC-related errors dengan user-friendly messages.
 * Bisa show toast notifications atau return error messages.
 */

export enum RBACErrorType {
  INSUFFICIENT_PERMISSIONS = "insufficient_permissions",
  AUTHENTICATION_REQUIRED = "authentication_required",
  LOCATION_ACCESS_DENIED = "location_access_denied",
  TENANT_ACCESS_DENIED = "tenant_access_denied",
  ROLE_REQUIRED = "role_required",
  PERMISSION_REQUIRED = "permission_required",
  SESSION_EXPIRED = "session_expired",
  UNKNOWN_ERROR = "unknown_error",
}

interface RBACError {
  type: RBACErrorType;
  message: string;
  details?: any;
  module?: string;
  action?: string;
  role?: string;
  locationId?: string;
  tenantId?: number;
}

class RBACErrorHandler {
  /**
   * Get user-friendly error message
   */
  getErrorMessage(error: RBACError): string {
    switch (error.type) {
      case RBACErrorType.INSUFFICIENT_PERMISSIONS:
        if (error.module && error.action) {
          return `Anda tidak memiliki permission untuk ${error.action} ${error.module}`;
        }
        return "Anda tidak memiliki permission yang diperlukan";

      case RBACErrorType.AUTHENTICATION_REQUIRED:
        return "Silakan login terlebih dahulu untuk mengakses fitur ini";

      case RBACErrorType.LOCATION_ACCESS_DENIED:
        return "Anda tidak memiliki akses ke location ini";

      case RBACErrorType.TENANT_ACCESS_DENIED:
        return "Anda tidak memiliki akses ke tenant ini";

      case RBACErrorType.ROLE_REQUIRED:
        if (error.role) {
          return `Anda memerlukan role ${error.role} untuk mengakses fitur ini`;
        }
        return "Anda tidak memiliki role yang diperlukan";

      case RBACErrorType.PERMISSION_REQUIRED:
        if (error.module && error.action) {
          return `Permission ${error.action} pada ${error.module} diperlukan`;
        }
        return "Permission khusus diperlukan untuk fitur ini";

      case RBACErrorType.SESSION_EXPIRED:
        return "Session Anda telah berakhir. Silakan login kembali";

      case RBACErrorType.UNKNOWN_ERROR:
      default:
        return error.message || "Terjadi kesalahan yang tidak diketahui";
    }
  }

  /**
   * Show error toast notification
   */
  showErrorToast(error: RBACError): void {
    const message = this.getErrorMessage(error);
    
    toast.error("Akses Ditolak", {
      description: message,
      duration: 5000,
    });
  }

  /**
   * Show warning toast untuk permission issues
   */
  showWarningToast(error: RBACError): void {
    const message = this.getErrorMessage(error);
    
    toast.warning("Peringatan", {
      description: message,
      duration: 4000,
    });
  }

  /**
   * Handle API response errors
   */
  handleAPIError(response: Response, context?: Partial<RBACError>): RBACError {
    let errorType: RBACErrorType;
    let message: string;

    switch (response.status) {
      case 401:
        errorType = RBACErrorType.AUTHENTICATION_REQUIRED;
        message = "Authentication required";
        break;
      
      case 403:
        errorType = RBACErrorType.INSUFFICIENT_PERMISSIONS;
        message = "Insufficient permissions";
        break;
      
      case 404:
        errorType = RBACErrorType.UNKNOWN_ERROR;
        message = "Resource not found";
        break;
      
      default:
        errorType = RBACErrorType.UNKNOWN_ERROR;
        message = `Request failed with status ${response.status}`;
    }

    return {
      type: errorType,
      message,
      ...context,
    };
  }

  /**
   * Handle JavaScript errors
   */
  handleJSError(error: any, context?: Partial<RBACError>): RBACError {
    let errorType: RBACErrorType = RBACErrorType.UNKNOWN_ERROR;
    let message: string = error.message || "Unknown error occurred";

    // Check for specific error patterns
    if (message.includes("permission")) {
      errorType = RBACErrorType.INSUFFICIENT_PERMISSIONS;
    } else if (message.includes("authentication") || message.includes("unauthorized")) {
      errorType = RBACErrorType.AUTHENTICATION_REQUIRED;
    } else if (message.includes("location")) {
      errorType = RBACErrorType.LOCATION_ACCESS_DENIED;
    } else if (message.includes("tenant")) {
      errorType = RBACErrorType.TENANT_ACCESS_DENIED;
    } else if (message.includes("role")) {
      errorType = RBACErrorType.ROLE_REQUIRED;
    } else if (message.includes("session")) {
      errorType = RBACErrorType.SESSION_EXPIRED;
    }

    return {
      type: errorType,
      message,
      details: error,
      ...context,
    };
  }

  /**
   * Create specific error types
   */
  createPermissionError(module: string, action: string): RBACError {
    return {
      type: RBACErrorType.PERMISSION_REQUIRED,
      message: `Permission required: ${action} on ${module}`,
      module,
      action,
    };
  }

  createRoleError(role: string): RBACError {
    return {
      type: RBACErrorType.ROLE_REQUIRED,
      message: `Role required: ${role}`,
      role,
    };
  }

  createLocationError(locationId: string): RBACError {
    return {
      type: RBACErrorType.LOCATION_ACCESS_DENIED,
      message: `Location access denied: ${locationId}`,
      locationId,
    };
  }

  createTenantError(tenantId: number): RBACError {
    return {
      type: RBACErrorType.TENANT_ACCESS_DENIED,
      message: `Tenant access denied: ${tenantId}`,
      tenantId,
    };
  }
}

// Export singleton instance
export const rbacErrorHandler = new RBACErrorHandler();

/**
 * Hook untuk error handling dalam React components
 */
export function useRBACErrorHandler() {
  const handleError = (error: any, context?: Partial<RBACError>, showToast: boolean = true) => {
    let rbacError: RBACError;

    if (error instanceof Response) {
      rbacError = rbacErrorHandler.handleAPIError(error, context);
    } else {
      rbacError = rbacErrorHandler.handleJSError(error, context);
    }

    if (showToast) {
      rbacErrorHandler.showErrorToast(rbacError);
    }

    return rbacError;
  };

  const handlePermissionError = (module: string, action: string, showToast: boolean = true) => {
    const error = rbacErrorHandler.createPermissionError(module, action);
    
    if (showToast) {
      rbacErrorHandler.showErrorToast(error);
    }
    
    return error;
  };

  const handleRoleError = (role: string, showToast: boolean = true) => {
    const error = rbacErrorHandler.createRoleError(role);
    
    if (showToast) {
      rbacErrorHandler.showErrorToast(error);
    }
    
    return error;
  };

  const handleLocationError = (locationId: string, showToast: boolean = true) => {
    const error = rbacErrorHandler.createLocationError(locationId);
    
    if (showToast) {
      rbacErrorHandler.showErrorToast(error);
    }
    
    return error;
  };

  return {
    handleError,
    handlePermissionError,
    handleRoleError,
    handleLocationError,
    showErrorToast: rbacErrorHandler.showErrorToast.bind(rbacErrorHandler),
    showWarningToast: rbacErrorHandler.showWarningToast.bind(rbacErrorHandler),
    getErrorMessage: rbacErrorHandler.getErrorMessage.bind(rbacErrorHandler),
  };
}
