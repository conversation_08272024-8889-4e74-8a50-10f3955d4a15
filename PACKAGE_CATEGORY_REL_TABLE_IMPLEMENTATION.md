# 🗄️ Package Category Relationship Table - Implementasi

## 📋 **Overview**

Halo teman-teman! Dokumentasi ini menjelaskan implementasi table `package_category_rel` yang digunakan untuk membuat **many-to-many relationship** antara packages dan package categories.

## 🎯 **Tujuan Table**

Table ini memungkinkan:
- **Satu package bisa punya banyak categories** (misal: Package "Gym Premium" bisa masuk kategori "Fitness" dan "Premium")
- **Satu category bisa dipake banyak packages** (misal: Kategori "Fitness" bisa dipake sama "Gym Basic", "Gym Premium", dll)

## 🏗️ **Struktur Table**

### **Schema Definition**

```typescript
export const package_category_rel = pgTable("package_category_rel", {
  package_id: varchar("package_id", { length: 255 })
    .references(() => packages.id, { onDelete: "cascade" })
    .notNull(),
  category_id: varchar("category_id", { length: 255 })
    .references(() => package_categories.id, { onDelete: "cascade" })
    .notNull(),
  tenantId: integer("tenant_id")
    .references(() => tenants.id, { onDelete: "cascade" })
    .notNull(),
  createdAt: timestamp("created_at")
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
  updatedAt: timestamp("updated_at")
    .default(sql`CURRENT_TIMESTAMP`)
    .notNull(),
}, (table) => ({
  // Compound primary key untuk mencegah duplicate relationships
  compoundKey: primaryKey({
    columns: [table.package_id, table.category_id],
  }),
}));
```

### **Field Explanation**

| Field | Type | Description |
|-------|------|-------------|
| `package_id` | varchar(255) | ID package yang mau dikaitkan |
| `category_id` | varchar(255) | ID category yang mau dikaitkan |
| `tenantId` | integer | ID tenant untuk multi-tenant isolation |
| `createdAt` | timestamp | Kapan relationship dibuat |
| `updatedAt` | timestamp | Kapan terakhir diupdate |

### **Constraints & Keys**

1. **Compound Primary Key**: `(package_id, category_id)`
   - Mencegah duplicate relationship antara package dan category yang sama
   - Satu package ga bisa dikaitkan ke category yang sama lebih dari sekali

2. **Foreign Key Constraints**:
   - `package_id` → `packages.id` (cascade delete)
   - `category_id` → `package_categories.id` (cascade delete)
   - `tenantId` → `tenants.id` (cascade delete)

3. **Cascade Delete Behavior**:
   - Kalau package dihapus → semua relationship-nya ikut kehapus
   - Kalau category dihapus → semua relationship-nya ikut kehapus
   - Kalau tenant dihapus → semua data tenant ikut kehapus

## 🔧 **Type Definitions**

```typescript
// Auto-generated types dari Drizzle
export type PackageCategoryRel = typeof package_category_rel.$inferSelect;
export type NewPackageCategoryRel = typeof package_category_rel.$inferInsert;
```

**Contoh Usage**:
```typescript
// Tipe untuk data yang udah ada di database
const existingRel: PackageCategoryRel = {
  package_id: "pkg_123",
  category_id: "cat_456",
  tenantId: 1,
  createdAt: new Date(),
  updatedAt: new Date(),
};

// Tipe untuk insert data baru
const newRel: NewPackageCategoryRel = {
  package_id: "pkg_789",
  category_id: "cat_101",
  tenantId: 1,
  // createdAt dan updatedAt otomatis di-set
};
```

## 🚀 **Cara Pakai**

### **1. Insert Relationship Baru**

```typescript
import { db } from '@/lib/db';
import { package_category_rel } from '@/lib/db/schema';

// Kaitkan package dengan category
await db.insert(package_category_rel).values({
  package_id: "pkg_gym_premium",
  category_id: "cat_fitness",
  tenantId: 1,
});
```

### **2. Query Package dengan Categories**

```typescript
import { eq } from 'drizzle-orm';

// Ambil semua categories untuk satu package
const packageCategories = await db
  .select({
    categoryId: package_category_rel.category_id,
    categoryName: package_categories.name,
  })
  .from(package_category_rel)
  .innerJoin(
    package_categories,
    eq(package_category_rel.category_id, package_categories.id)
  )
  .where(eq(package_category_rel.package_id, "pkg_gym_premium"));
```

### **3. Query Categories dengan Packages**

```typescript
// Ambil semua packages dalam satu category
const categoryPackages = await db
  .select({
    packageId: package_category_rel.package_id,
    packageName: packages.name,
  })
  .from(package_category_rel)
  .innerJoin(
    packages,
    eq(package_category_rel.package_id, packages.id)
  )
  .where(eq(package_category_rel.category_id, "cat_fitness"));
```

### **4. Delete Relationship**

```typescript
import { and } from 'drizzle-orm';

// Hapus relationship tertentu
await db
  .delete(package_category_rel)
  .where(
    and(
      eq(package_category_rel.package_id, "pkg_gym_premium"),
      eq(package_category_rel.category_id, "cat_fitness")
    )
  );
```

## 🔒 **Multi-Tenant Security**

### **Tenant Isolation**

Table ini include `tenantId` field untuk memastikan:
- **Data isolation**: Tenant A ga bisa liat/akses data tenant B
- **Security**: Semua query harus include tenant filter

### **Best Practice Query**

```typescript
// ✅ Selalu include tenantId dalam query
const tenantPackageCategories = await db
  .select()
  .from(package_category_rel)
  .where(
    and(
      eq(package_category_rel.tenantId, currentTenantId),
      eq(package_category_rel.package_id, packageId)
    )
  );

// ❌ Jangan query tanpa tenant filter
const unsafeQuery = await db
  .select()
  .from(package_category_rel)
  .where(eq(package_category_rel.package_id, packageId)); // Bahaya!
```

## 🧪 **Testing Examples**

### **Test Data Setup**

```typescript
// Setup test data
const testPackage = await db.insert(packages).values({
  id: "test_package",
  tenantId: 1,
  name: "Test Package",
}).returning();

const testCategory = await db.insert(package_categories).values({
  id: "test_category",
  tenantId: 1,
  name: "Test Category",
}).returning();

// Create relationship
await db.insert(package_category_rel).values({
  package_id: testPackage[0].id,
  category_id: testCategory[0].id,
  tenantId: 1,
});
```

### **Test Scenarios**

```typescript
// Test 1: Prevent duplicate relationships
try {
  await db.insert(package_category_rel).values({
    package_id: "test_package",
    category_id: "test_category", // Same combination
    tenantId: 1,
  });
  // Should throw error karena compound primary key
} catch (error) {
  console.log("✅ Duplicate prevention works!");
}

// Test 2: Cascade delete
await db.delete(packages).where(eq(packages.id, "test_package"));
const orphanedRels = await db
  .select()
  .from(package_category_rel)
  .where(eq(package_category_rel.package_id, "test_package"));
// Should be empty karena cascade delete
```

## ✅ **Status Implementation**

**Package Category Relationship Table**: ✅ **FULLY IMPLEMENTED**

### **Yang Udah Beres:**
- ✅ Table structure dengan proper fields
- ✅ Foreign key constraints ke packages dan categories
- ✅ Compound primary key untuk prevent duplicates
- ✅ Tenant isolation dengan tenantId field
- ✅ Audit fields (createdAt, updatedAt)
- ✅ Type definitions untuk TypeScript
- ✅ Cascade delete behavior
- ✅ Consistent dengan pattern table lain

### **Bonus Improvements:**
- ✅ Juga diperbaiki `package_locations` table
- ✅ Juga diperbaiki `class_membership_plans` table  
- ✅ Juga diperbaiki `class_package_plans` table
- ✅ Semua relationship tables sekarang punya compound primary key

## 🎯 **Next Steps**

Table `package_category_rel` udah **100% siap pakai** untuk:
1. **Package Management**: Assign categories ke packages
2. **Category Filtering**: Filter packages berdasarkan category
3. **Reporting**: Analytics berdasarkan package categories
4. **UI Components**: Dropdown category selection di package forms

**Happy coding teman-teman!** 🚀
