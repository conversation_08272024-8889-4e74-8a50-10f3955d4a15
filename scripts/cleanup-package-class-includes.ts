// Script untuk menghapus tabel package_class_includes yang tidak terpakai
// Run dengan: npx tsx scripts/cleanup-package-class-includes.ts

import { db } from "../src/lib/db";
import { sql } from "drizzle-orm";

async function cleanupPackageClassIncludes() {
  try {
    console.log("🧹 Memulai pembersihan tabel package_class_includes...");

    // Step 1: Cek apakah tabel ada dan berisi data
    console.log("📊 Mengecek isi tabel package_class_includes...");
    
    const tableExists = await db.execute(sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'package_class_includes'
      );
    `);

    if (!tableExists.rows[0]?.exists) {
      console.log("✅ Tabel package_class_includes sudah tidak ada");
      return;
    }

    // Cek jumlah data
    const countResult = await db.execute(sql`
      SELECT COUNT(*) as count FROM package_class_includes;
    `);
    
    const recordCount = countResult.rows[0]?.count || 0;
    console.log(`📋 Ditemukan ${recordCount} record di tabel package_class_includes`);

    if (recordCount > 0) {
      console.log("⚠️  PERINGATAN: Tabel berisi data!");
      console.log("   Pastikan data ini memang tidak diperlukan sebelum menghapus.");
      console.log("   Jika ragu, backup dulu dengan:");
      console.log("   pg_dump -t package_class_includes your_database > backup_package_class_includes.sql");
      
      // Tampilkan sample data
      const sampleData = await db.execute(sql`
        SELECT * FROM package_class_includes LIMIT 5;
      `);
      
      console.log("\n📋 Sample data:");
      console.table(sampleData.rows);
      
      console.log("\n❓ Apakah Anda yakin ingin menghapus tabel ini?");
      console.log("   Uncomment baris DROP TABLE di bawah jika yakin.");
      console.log("   Atau jalankan manual: DROP TABLE package_class_includes;");
      
      // Uncomment baris ini jika yakin ingin hapus
      // await db.execute(sql`DROP TABLE package_class_includes;`);
      // console.log("✅ Tabel package_class_includes berhasil dihapus");
      
    } else {
      console.log("📋 Tabel kosong, aman untuk dihapus");
      
      // Hapus tabel kosong
      await db.execute(sql`DROP TABLE package_class_includes;`);
      console.log("✅ Tabel package_class_includes berhasil dihapus");
    }

    // Step 2: Verifikasi tabel sudah terhapus
    const verifyResult = await db.execute(sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'package_class_includes'
      );
    `);

    if (!verifyResult.rows[0]?.exists) {
      console.log("✅ Verifikasi: Tabel package_class_includes sudah tidak ada");
    } else {
      console.log("❌ Verifikasi gagal: Tabel masih ada");
    }

    console.log("\n🎉 Pembersihan selesai!");
    console.log("\n📝 Yang sudah dibersihkan:");
    console.log("   ✅ Tabel package_class_includes dihapus dari database");
    console.log("   ✅ Schema TypeScript sudah diupdate");
    console.log("   ✅ Type definitions sudah dihapus");

    console.log("\n💡 Catatan:");
    console.log("   - Relationship package-class tetap bisa dihandle via class_package_pricing");
    console.log("   - Jika butuh package-class relationship di masa depan, bisa buat ulang");
    console.log("   - Database sekarang lebih bersih dan tidak ada dead code");

  } catch (error) {
    console.error("💥 Error saat cleanup:", error);
    throw error;
  }
}

// Jalankan cleanup
cleanupPackageClassIncludes()
  .then(() => {
    console.log("✅ Script cleanup berhasil dijalankan");
    process.exit(0);
  })
  .catch((error) => {
    console.error("💥 Script cleanup gagal:", error);
    process.exit(1);
  });
