import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { z } from "zod";
import { packageCategoryService } from "@/lib/services/package-category.service";

// Validation schema for updates
const updatePackageCategorySchema = z.object({
  name: z.string().min(1, "Name is required").max(255, "Name must be less than 255 characters").optional(),
  description: z.string().max(255, "Description must be less than 255 characters").optional(),
});

/**
 * GET /api/package-categories/[id]
 * Get a specific package category by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    console.log(`📦 [GET /api/package-categories/${id}] Fetching package category`);

    const packageCategory = await packageCategoryService.getById(id);

    if (!packageCategory) {
      return NextResponse.json({ error: "Package category not found" }, { status: 404 });
    }

    // For now, skip tenant verification since we don't have tenantId in session
    // TODO: Add proper tenant verification when session includes tenantId

    return NextResponse.json({
      success: true,
      data: packageCategory
    });
  } catch (error) {
    console.error(`📦 [GET /api/package-categories/${params}] Error:`, error);
    return NextResponse.json(
      { error: "Failed to fetch package category" },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/package-categories/[id]
 * Update a specific package category
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    const body = await request.json();
    const validatedData = updatePackageCategorySchema.parse(body);

    console.log(`📦 [PUT /api/package-categories/${id}] Updating package category:`, validatedData);

    // Check if the package category exists
    const existingCategory = await packageCategoryService.getById(id);
    if (!existingCategory) {
      return NextResponse.json({ error: "Package category not found" }, { status: 404 });
    }

    // Check if name already exists for this tenant (excluding current category)
    if (validatedData.name) {
      const nameExists = await packageCategoryService.nameExists(
        validatedData.name,
        existingCategory.tenantId,
        id
      );

      if (nameExists) {
        return NextResponse.json(
          { error: "Package category name already exists for this tenant" },
          { status: 409 }
        );
      }
    }

    const updatedCategory = await packageCategoryService.update(id, validatedData);

    return NextResponse.json({
      success: true,
      data: updatedCategory,
      message: "Package category updated successfully"
    });
  } catch (error) {
    console.error(`📦 [PUT /api/package-categories/${params}] Error:`, error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid package category data", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to update package category" },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/package-categories/[id]
 * Delete a specific package category
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    console.log(`📦 [DELETE /api/package-categories/${id}] Deleting package category`);

    // Check if the package category exists
    const existingCategory = await packageCategoryService.getById(id);
    if (!existingCategory) {
      return NextResponse.json({ error: "Package category not found" }, { status: 404 });
    }

    // TODO: Check if category is being used by any packages before deletion
    // This would require checking the package_category_rel table

    const deletedCategory = await packageCategoryService.delete(id);

    return NextResponse.json({
      success: true,
      data: deletedCategory,
      message: "Package category deleted successfully"
    });
  } catch (error) {
    console.error(`📦 [DELETE /api/package-categories/${params}] Error:`, error);
    return NextResponse.json(
      { error: "Failed to delete package category" },
      { status: 500 }
    );
  }
}
