import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';
import { PackagePurchaseOptions } from '@/lib/db/schema';
import { createEntityHooks, BaseApiInterface } from '@/lib/core/base-query-hooks';
import { QueryOptions } from '@/lib/core/base-service';
import { 
  CreatePackagePurchaseOptionsData, 
  UpdatePackagePurchaseOptionsData, 
  PackagePurchaseOptionsFilters,
  PackagePurchaseOptionsStats,
  PackagePurchaseOptionsWithDetails
} from '@/lib/services/package-purchase-options.service';

// API interface for package purchase options
const packagePurchaseOptionsApi: BaseApiInterface<PackagePurchaseOptions, CreatePackagePurchaseOptionsData, UpdatePackagePurchaseOptionsData> = {
  async getAll(options?: QueryOptions): Promise<PackagePurchaseOptions[]> {
    const params = new URLSearchParams();
    
    if (options?.filters) {
      Object.entries(options.filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          params.append(key, String(value));
        }
      });
    }

    if (options?.limit) params.append('limit', String(options.limit));
    if (options?.offset) params.append('offset', String(options.offset));
    if (options?.sortBy) params.append('sortBy', options.sortBy);
    if (options?.sortOrder) params.append('sortOrder', options.sortOrder);

    const response = await fetch(`/api/package-purchase-options?${params}`);
    if (!response.ok) {
      throw new Error('Failed to fetch package purchase options');
    }

    const data = await response.json();
    return data.data || [];
  },

  async getById(id: string): Promise<PackagePurchaseOptions | null> {
    const response = await fetch(`/api/package-purchase-options/${id}`);
    if (!response.ok) {
      if (response.status === 404) return null;
      throw new Error('Failed to fetch package purchase options');
    }

    const data = await response.json();
    return data.data;
  },

  async create(data: CreatePackagePurchaseOptionsData): Promise<PackagePurchaseOptions> {
    const response = await fetch('/api/package-purchase-options', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to create package purchase options');
    }

    const result = await response.json();
    return result.data;
  },

  async update(id: string, data: UpdatePackagePurchaseOptionsData): Promise<PackagePurchaseOptions> {
    const response = await fetch(`/api/package-purchase-options/${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to update package purchase options');
    }

    const result = await response.json();
    return result.data;
  },

  async delete(id: string): Promise<void> {
    const response = await fetch(`/api/package-purchase-options/${id}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to delete package purchase options');
    }
  },
};

// Create entity hooks using the factory
const {
  useEntities: usePackagePurchaseOptionsList,
  useEntity: usePackagePurchaseOptions,
  useCreateEntity: useCreatePackagePurchaseOptionsBase,
  useUpdateEntity: useUpdatePackagePurchaseOptionsBase,
  useDeleteEntity: useDeletePackagePurchaseOptionsBase,
} = createEntityHooks('package-purchase-options', packagePurchaseOptionsApi);

// Custom hooks with specific functionality

/**
 * Get package purchase options by package ID
 */
export function usePackagePurchaseOptionsByPackage(packageId: string, options?: { enabled?: boolean }) {
  return useQuery({
    queryKey: ['package-purchase-options', 'by-package', packageId],
    queryFn: async (): Promise<PackagePurchaseOptions | null> => {
      const response = await fetch(`/api/package-purchase-options/by-package/${packageId}`);
      if (!response.ok) {
        if (response.status === 404) return null;
        throw new Error('Failed to fetch package purchase options');
      }
      const data = await response.json();
      return data.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    enabled: options?.enabled !== false && !!packageId,
  });
}

/**
 * Get package purchase options with details (package and location info)
 */
export function usePackagePurchaseOptionsWithDetails(packageId: string) {
  return useQuery({
    queryKey: ['package-purchase-options', 'with-details', packageId],
    queryFn: async (): Promise<PackagePurchaseOptionsWithDetails | null> => {
      const response = await fetch(`/api/package-purchase-options/with-details/${packageId}`);
      if (!response.ok) {
        if (response.status === 404) return null;
        throw new Error('Failed to fetch package purchase options with details');
      }
      const data = await response.json();
      return data.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Get package purchase options statistics
 */
export function usePackagePurchaseOptionsStats() {
  return useQuery({
    queryKey: ['package-purchase-options', 'stats'],
    queryFn: async (): Promise<PackagePurchaseOptionsStats> => {
      const response = await fetch('/api/package-purchase-options/stats');
      if (!response.ok) {
        throw new Error('Failed to fetch package purchase options statistics');
      }
      const data = await response.json();
      return data.data;
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Custom mutation hooks with proper cache invalidation

/**
 * Create package purchase options
 */
export function useCreatePackagePurchaseOptions() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: packagePurchaseOptionsApi.create,
    onSuccess: (data) => {
      // Invalidate all package purchase options related queries
      queryClient.invalidateQueries({ queryKey: ['package-purchase-options'] });
      
      // Specifically invalidate the package-specific queries
      queryClient.invalidateQueries({ 
        queryKey: ['package-purchase-options', 'by-package', data.package_id] 
      });
      queryClient.invalidateQueries({ 
        queryKey: ['package-purchase-options', 'with-details', data.package_id] 
      });
      
      // Invalidate stats
      queryClient.invalidateQueries({ queryKey: ['package-purchase-options', 'stats'] });
    },
  });
}

/**
 * Update package purchase options
 */
export function useUpdatePackagePurchaseOptions() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ packageId, data }: { packageId: string; data: UpdatePackagePurchaseOptionsData }) =>
      packagePurchaseOptionsApi.update(packageId, data),
    onSuccess: (data) => {
      // Invalidate all package purchase options related queries
      queryClient.invalidateQueries({ queryKey: ['package-purchase-options'] });
      
      // Specifically invalidate the package-specific queries
      queryClient.invalidateQueries({ 
        queryKey: ['package-purchase-options', 'by-package', data.package_id] 
      });
      queryClient.invalidateQueries({ 
        queryKey: ['package-purchase-options', 'with-details', data.package_id] 
      });
      
      // Invalidate stats
      queryClient.invalidateQueries({ queryKey: ['package-purchase-options', 'stats'] });
    },
  });
}

/**
 * Delete package purchase options
 */
export function useDeletePackagePurchaseOptions() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (packageId: string) => packagePurchaseOptionsApi.delete(packageId),
    onSuccess: (_, packageId) => {
      // Invalidate all package purchase options related queries
      queryClient.invalidateQueries({ queryKey: ['package-purchase-options'] });
      
      // Specifically invalidate the package-specific queries
      queryClient.invalidateQueries({ 
        queryKey: ['package-purchase-options', 'by-package', packageId] 
      });
      queryClient.invalidateQueries({ 
        queryKey: ['package-purchase-options', 'with-details', packageId] 
      });
      
      // Invalidate stats
      queryClient.invalidateQueries({ queryKey: ['package-purchase-options', 'stats'] });
    },
  });
}

// Export filtered queries for specific use cases

/**
 * Get package purchase options filtered by criteria
 */
export function usePackagePurchaseOptionsFiltered(filters: PackagePurchaseOptionsFilters) {
  return useQuery({
    queryKey: ['package-purchase-options', 'filtered', filters],
    queryFn: () => packagePurchaseOptionsApi.getAll({ filters }),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Get transferable package purchase options
 */
export function useTransferablePackagePurchaseOptions() {
  return useQuery({
    queryKey: ['package-purchase-options', 'transferable'],
    queryFn: () => packagePurchaseOptionsApi.getAll({ 
      filters: { transferable: true } 
    }),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Get online visible package purchase options
 */
export function useOnlineVisiblePackagePurchaseOptions() {
  return useQuery({
    queryKey: ['package-purchase-options', 'online-visible'],
    queryFn: () => packagePurchaseOptionsApi.getAll({ 
      filters: { showOnline: true } 
    }),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Get package purchase options by location
 */
export function usePackagePurchaseOptionsByLocation(locationId: string) {
  return useQuery({
    queryKey: ['package-purchase-options', 'by-location', locationId],
    queryFn: () => packagePurchaseOptionsApi.getAll({ 
      filters: { locationId } 
    }),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Export types for use in components
export type {
  CreatePackagePurchaseOptionsData,
  UpdatePackagePurchaseOptionsData,
  PackagePurchaseOptionsFilters,
  PackagePurchaseOptionsStats,
  PackagePurchaseOptionsWithDetails,
};

// Export the base hooks for advanced usage
export {
  usePackagePurchaseOptionsList,
  usePackagePurchaseOptions,
};
