import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { packagePurchaseOptionsService } from "@/lib/services/package-purchase-options.service";

/**
 * GET /api/package-purchase-options/by-package/[packageId]
 * Get package purchase options by package ID (alternative endpoint)
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ packageId: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { packageId } = await params;
    console.log(`📦 [GET /api/package-purchase-options/by-package/${packageId}] Fetching package purchase options`);

    const packagePurchaseOptions = await packagePurchaseOptionsService.getByPackageId(packageId);

    return NextResponse.json({
      success: true,
      data: packagePurchaseOptions
    });
  } catch (error) {
    console.error(`📦 [GET /api/package-purchase-options/by-package/${params}] Error:`, error);
    return NextResponse.json(
      { error: "Failed to fetch package purchase options" },
      { status: 500 }
    );
  }
}
