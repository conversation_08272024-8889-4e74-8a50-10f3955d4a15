/**
 * Metrics Collection Service
 * 
 * FAANG-level metrics collection with:
 * - Request/response metrics
 * - Performance monitoring
 * - Error tracking
 * - Business metrics
 * - Real-time dashboards support
 */

import { APIMetrics } from '../types';

interface MetricEvent {
  type: 'request' | 'error' | 'performance' | 'business';
  name: string;
  value: number;
  tags?: Record<string, string>;
  timestamp?: Date;
}

interface RequestMetric {
  endpoint: string;
  method: string;
  statusCode: number;
  responseTime: number;
  apiKeyId?: string;
  tenantId?: number;
  userAgent?: string;
  ip?: string;
  timestamp: Date;
}

interface ErrorMetric {
  endpoint: string;
  method: string;
  errorCode: string;
  errorMessage: string;
  apiKeyId?: string;
  tenantId?: number;
  timestamp: Date;
}

export class MetricsService {
  private metrics: Map<string, number> = new Map();
  private requestMetrics: RequestMetric[] = [];
  private errorMetrics: ErrorMetric[] = [];
  private readonly maxStoredMetrics = 10000;

  /**
   * Record a request metric
   */
  recordRequest(metric: RequestMetric): void {
    // Store request metric
    this.requestMetrics.push(metric);
    
    // Trim old metrics
    if (this.requestMetrics.length > this.maxStoredMetrics) {
      this.requestMetrics = this.requestMetrics.slice(-this.maxStoredMetrics);
    }

    // Update counters
    this.increment('requests_total', {
      endpoint: metric.endpoint,
      method: metric.method,
      status_code: metric.statusCode.toString(),
    });

    // Record response time
    this.recordValue('response_time_ms', metric.responseTime, {
      endpoint: metric.endpoint,
      method: metric.method,
    });

    // Record by API key
    if (metric.apiKeyId) {
      this.increment('requests_by_api_key', {
        api_key_id: metric.apiKeyId,
      });
    }

    // Record by tenant
    if (metric.tenantId) {
      this.increment('requests_by_tenant', {
        tenant_id: metric.tenantId.toString(),
      });
    }

    // Log to structured logger for external monitoring
    this.logMetric({
      type: 'request',
      name: 'api_request',
      value: 1,
      tags: {
        endpoint: metric.endpoint,
        method: metric.method,
        status_code: metric.statusCode.toString(),
        api_key_id: metric.apiKeyId || 'unknown',
        tenant_id: metric.tenantId?.toString() || 'unknown',
      },
      timestamp: metric.timestamp,
    });
  }

  /**
   * Record an error metric
   */
  recordError(metric: ErrorMetric): void {
    // Store error metric
    this.errorMetrics.push(metric);
    
    // Trim old metrics
    if (this.errorMetrics.length > this.maxStoredMetrics) {
      this.errorMetrics = this.errorMetrics.slice(-this.maxStoredMetrics);
    }

    // Update error counters
    this.increment('errors_total', {
      endpoint: metric.endpoint,
      method: metric.method,
      error_code: metric.errorCode,
    });

    // Record by API key
    if (metric.apiKeyId) {
      this.increment('errors_by_api_key', {
        api_key_id: metric.apiKeyId,
      });
    }

    // Log to structured logger
    this.logMetric({
      type: 'error',
      name: 'api_error',
      value: 1,
      tags: {
        endpoint: metric.endpoint,
        method: metric.method,
        error_code: metric.errorCode,
        api_key_id: metric.apiKeyId || 'unknown',
        tenant_id: metric.tenantId?.toString() || 'unknown',
      },
      timestamp: metric.timestamp,
    });
  }

  /**
   * Record a performance metric
   */
  recordPerformance(name: string, value: number, tags?: Record<string, string>): void {
    this.recordValue(name, value, tags);
    
    this.logMetric({
      type: 'performance',
      name,
      value,
      tags,
    });
  }

  /**
   * Record a business metric
   */
  recordBusiness(name: string, value: number, tags?: Record<string, string>): void {
    this.recordValue(name, value, tags);
    
    this.logMetric({
      type: 'business',
      name,
      value,
      tags,
    });
  }

  /**
   * Get API metrics summary
   */
  getAPIMetrics(timeWindow: number = 3600000): APIMetrics { // Default 1 hour
    const now = Date.now();
    const windowStart = now - timeWindow;

    // Filter metrics within time window
    const recentRequests = this.requestMetrics.filter(
      m => m.timestamp.getTime() >= windowStart
    );

    const recentErrors = this.errorMetrics.filter(
      m => m.timestamp.getTime() >= windowStart
    );

    // Calculate metrics
    const requestCount = recentRequests.length;
    const errorCount = recentErrors.length;
    
    const responseTimes = recentRequests.map(r => r.responseTime);
    const averageResponseTime = responseTimes.length > 0 
      ? responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length 
      : 0;

    const sortedResponseTimes = responseTimes.sort((a, b) => a - b);
    const p95ResponseTime = sortedResponseTimes.length > 0
      ? sortedResponseTimes[Math.floor(sortedResponseTimes.length * 0.95)]
      : 0;
    const p99ResponseTime = sortedResponseTimes.length > 0
      ? sortedResponseTimes[Math.floor(sortedResponseTimes.length * 0.99)]
      : 0;

    // Count rate limit hits
    const rateLimitHits = this.getMetricValue('rate_limit_hits_total') || 0;

    // Count unique clients
    const uniqueClients = new Set(recentRequests.map(r => r.apiKeyId).filter(Boolean)).size;

    // Top endpoints
    const endpointCounts = recentRequests.reduce((acc, r) => {
      const key = `${r.method} ${r.endpoint}`;
      acc[key] = (acc[key] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const endpointResponseTimes = recentRequests.reduce((acc, r) => {
      const key = `${r.method} ${r.endpoint}`;
      if (!acc[key]) acc[key] = [];
      acc[key].push(r.responseTime);
      return acc;
    }, {} as Record<string, number[]>);

    const topEndpoints = Object.entries(endpointCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([endpoint, count]) => ({
        endpoint,
        count,
        averageResponseTime: endpointResponseTimes[endpoint]
          ? endpointResponseTimes[endpoint].reduce((a, b) => a + b, 0) / endpointResponseTimes[endpoint].length
          : 0,
      }));

    return {
      requestCount,
      errorCount,
      averageResponseTime,
      p95ResponseTime,
      p99ResponseTime,
      rateLimitHits,
      uniqueClients,
      topEndpoints,
    };
  }

  /**
   * Get metric value by name and tags
   */
  getMetricValue(name: string, tags?: Record<string, string>): number {
    const key = this.getMetricKey(name, tags);
    return this.metrics.get(key) || 0;
  }

  /**
   * Reset all metrics (for testing)
   */
  reset(): void {
    this.metrics.clear();
    this.requestMetrics.length = 0;
    this.errorMetrics.length = 0;
  }

  /**
   * Export metrics in Prometheus format
   */
  exportPrometheus(): string {
    const lines: string[] = [];
    
    // Group metrics by name
    const metricGroups = new Map<string, Array<{ key: string; value: number; tags: Record<string, string> }>>();
    
    this.metrics.forEach((value, key) => {
      const { name, tags } = this.parseMetricKey(key);
      if (!metricGroups.has(name)) {
        metricGroups.set(name, []);
      }
      metricGroups.get(name)!.push({ key, value, tags });
    });

    // Format as Prometheus metrics
    metricGroups.forEach((metrics, name) => {
      lines.push(`# TYPE ${name} counter`);
      metrics.forEach(({ value, tags }) => {
        const tagString = Object.entries(tags)
          .map(([k, v]) => `${k}="${v}"`)
          .join(',');
        lines.push(`${name}{${tagString}} ${value}`);
      });
      lines.push('');
    });

    return lines.join('\n');
  }

  /**
   * Increment a counter metric
   */
  private increment(name: string, tags?: Record<string, string>): void {
    const key = this.getMetricKey(name, tags);
    this.metrics.set(key, (this.metrics.get(key) || 0) + 1);
  }

  /**
   * Record a value metric
   */
  private recordValue(name: string, value: number, tags?: Record<string, string>): void {
    const key = this.getMetricKey(name, tags);
    this.metrics.set(key, value);
  }

  /**
   * Generate metric key from name and tags
   */
  private getMetricKey(name: string, tags?: Record<string, string>): string {
    if (!tags || Object.keys(tags).length === 0) {
      return name;
    }
    
    const tagString = Object.entries(tags)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([k, v]) => `${k}:${v}`)
      .join(',');
    
    return `${name}{${tagString}}`;
  }

  /**
   * Parse metric key back to name and tags
   */
  private parseMetricKey(key: string): { name: string; tags: Record<string, string> } {
    const match = key.match(/^([^{]+)(?:\{([^}]+)\})?$/);
    if (!match) {
      return { name: key, tags: {} };
    }

    const name = match[1];
    const tagString = match[2];
    
    if (!tagString) {
      return { name, tags: {} };
    }

    const tags = tagString.split(',').reduce((acc, pair) => {
      const [k, v] = pair.split(':');
      acc[k] = v;
      return acc;
    }, {} as Record<string, string>);

    return { name, tags };
  }

  /**
   * Log metric to structured logger
   */
  private logMetric(event: MetricEvent): void {
    const logEntry = {
      level: 'info',
      timestamp: (event.timestamp || new Date()).toISOString(),
      service: 'public-api',
      component: 'metrics',
      event: 'metric_recorded',
      metric_type: event.type,
      metric_name: event.name,
      metric_value: event.value,
      tags: event.tags,
    };

    // In production, send to your metrics service (DataDog, Prometheus, etc.)
    console.log(JSON.stringify(logEntry));
  }
}

// Singleton instance
export const metricsService = new MetricsService();
