import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { z } from "zod";
import { individualCustomerSelectionService } from "@/lib/services/individual-customer-selection.service";

// Validation schema for saving individual customer targeting
const saveIndividualTargetingSchema = z.object({
  packageId: z.string().min(1, "Package ID is required"),
  selectedCustomerIds: z.array(z.string()),
  tenantId: z.number().int().positive("Tenant ID must be positive"),
});

/**
 * POST /api/packages/individual-customer-targeting
 * Save individual customer targeting for a package
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = saveIndividualTargetingSchema.parse(body);

    console.log(`👤 [POST /api/packages/individual-customer-targeting] Saving individual customer targeting:`, validatedData);

    await individualCustomerSelectionService.saveIndividualCustomerTargeting(validatedData);

    return NextResponse.json({
      success: true,
      message: `Individual customer targeting saved for ${validatedData.selectedCustomerIds.length} customers`
    }, { status: 200 });
  } catch (error) {
    console.error("👤 [POST /api/packages/individual-customer-targeting] Error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid individual customer targeting data", details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      if (error.message.includes("do not belong to the current tenant")) {
        return NextResponse.json(
          { error: error.message },
          { status: 403 }
        );
      }

      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to save individual customer targeting" },
      { status: 500 }
    );
  }
}
