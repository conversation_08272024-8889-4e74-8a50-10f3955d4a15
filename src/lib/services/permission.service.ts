import { db } from "@/lib/db";
import { permissions, role_permissions, user_roles, roles, type Permission, type NewPermission, type RolePermission, type NewRolePermission } from "@/lib/db/schema";
import { eq, and, ilike, desc, sql, inArray } from "drizzle-orm";
import { createId } from "@paralleldrive/cuid2";

/**
 * Permission Service
 * 
 * Service untuk manage permissions dalam RBAC system.
 * Mengikuti pola yang sama dengan RoleService - simple dan proven.
 */

// Interface untuk search results
export interface PermissionSearchResult {
  permissions: Permission[];
  total: number;
  hasMore: boolean;
}

/**
 * PermissionService - Service untuk manage permissions
 */
export class PermissionService {
  /**
   * Search permissions dengan filtering dan pagination
   */
  static async searchPermissions(
    module?: string,
    action?: string,
    search?: string,
    limit = 20,
    offset = 0
  ): Promise<PermissionSearchResult> {
    try {
      // Build where conditions
      let whereConditions: any = sql`1=1`;

      // Filter by module if provided
      if (module) {
        whereConditions = and(whereConditions, eq(permissions.module, module));
      }

      // Filter by action if provided
      if (action) {
        whereConditions = and(whereConditions, eq(permissions.action, action));
      }

      // Kalau ada search term, tambahin filter nama
      if (search && search.trim()) {
        whereConditions = and(
          whereConditions,
          ilike(permissions.display_name, `%${search.trim()}%`)
        ) as any;
      }

      // Query dengan pagination
      const permissionResults = await db
        .select()
        .from(permissions)
        .where(whereConditions)
        .orderBy(permissions.module, permissions.action)
        .limit(limit + 1)
        .offset(offset);

      // Cek apakah ada data lebih
      const hasMore = permissionResults.length > limit;
      if (hasMore) {
        permissionResults.pop();
      }

      // Count total
      const totalResult = await db
        .select({ count: sql<number>`count(*)` })
        .from(permissions)
        .where(whereConditions);

      const total = Number(totalResult[0]?.count || 0);

      return {
        permissions: permissionResults,
        total,
        hasMore,
      };
    } catch (error) {
      console.error("Error searching permissions:", error);
      throw error;
    }
  }

  /**
   * Get permission by ID
   */
  static async getById(id: string): Promise<Permission | null> {
    try {
      const result = await db
        .select()
        .from(permissions)
        .where(eq(permissions.id, id))
        .limit(1);

      return result[0] || null;
    } catch (error) {
      console.error("Error getting permission by ID:", error);
      throw error;
    }
  }

  /**
   * Get permission by module and action
   */
  static async getByModuleAndAction(module: string, action: string): Promise<Permission | null> {
    try {
      const result = await db
        .select()
        .from(permissions)
        .where(and(eq(permissions.module, module), eq(permissions.action, action)))
        .limit(1);

      return result[0] || null;
    } catch (error) {
      console.error("Error getting permission by module and action:", error);
      throw error;
    }
  }

  /**
   * Create new permission
   */
  static async create(data: {
    module: string;
    action: string;
    resource?: string;
    display_name: string;
    description?: string;
    is_system_permission?: boolean;
  }): Promise<Permission> {
    try {
      const permissionData: NewPermission = {
        id: createId(),
        module: data.module,
        action: data.action,
        resource: data.resource || null,
        display_name: data.display_name,
        description: data.description || null,
        is_system_permission: data.is_system_permission || true,
      };

      const result = await db.insert(permissions).values(permissionData).returning();
      return result[0];
    } catch (error) {
      console.error("Error creating permission:", error);
      throw error;
    }
  }

  /**
   * Update permission
   */
  static async update(
    id: string,
    data: {
      module?: string;
      action?: string;
      resource?: string;
      display_name?: string;
      description?: string;
    }
  ): Promise<Permission> {
    try {
      const result = await db
        .update(permissions)
        .set({
          ...data,
          updatedAt: new Date(),
        })
        .where(eq(permissions.id, id))
        .returning();

      if (result.length === 0) {
        throw new Error("Permission not found");
      }

      return result[0];
    } catch (error) {
      console.error("Error updating permission:", error);
      throw error;
    }
  }

  /**
   * Delete permission
   */
  static async delete(id: string): Promise<void> {
    try {
      await db.delete(permissions).where(eq(permissions.id, id));
    } catch (error) {
      console.error("Error deleting permission:", error);
      throw error;
    }
  }

  /**
   * Get all permissions grouped by module
   */
  static async getAllGroupedByModule(): Promise<Record<string, Permission[]>> {
    try {
      const allPermissions = await db
        .select()
        .from(permissions)
        .orderBy(permissions.module, permissions.action);

      const grouped: Record<string, Permission[]> = {};
      
      for (const permission of allPermissions) {
        if (!grouped[permission.module]) {
          grouped[permission.module] = [];
        }
        grouped[permission.module].push(permission);
      }

      return grouped;
    } catch (error) {
      console.error("Error getting permissions grouped by module:", error);
      throw error;
    }
  }

  /**
   * Assign permission to role
   */
  static async assignPermissionToRole(
    roleId: string,
    permissionId: string,
    conditions?: any
  ): Promise<RolePermission> {
    try {
      // Check if assignment already exists
      const existing = await db
        .select()
        .from(role_permissions)
        .where(
          and(
            eq(role_permissions.roleId, roleId),
            eq(role_permissions.permissionId, permissionId)
          )
        )
        .limit(1);

      if (existing.length > 0) {
        // Update existing assignment
        const result = await db
          .update(role_permissions)
          .set({
            conditions: conditions || null,
            updatedAt: new Date(),
          })
          .where(eq(role_permissions.id, existing[0].id))
          .returning();

        return result[0];
      }

      // Create new assignment
      const assignmentData: NewRolePermission = {
        id: createId(),
        roleId,
        permissionId,
        conditions: conditions || null,
      };

      const result = await db.insert(role_permissions).values(assignmentData).returning();
      return result[0];
    } catch (error) {
      console.error("Error assigning permission to role:", error);
      throw error;
    }
  }

  /**
   * Revoke permission from role
   */
  static async revokePermissionFromRole(roleId: string, permissionId: string): Promise<void> {
    try {
      await db
        .delete(role_permissions)
        .where(
          and(
            eq(role_permissions.roleId, roleId),
            eq(role_permissions.permissionId, permissionId)
          )
        );
    } catch (error) {
      console.error("Error revoking permission from role:", error);
      throw error;
    }
  }

  /**
   * Get permissions for a role
   */
  static async getPermissionsForRole(roleId: string): Promise<Permission[]> {
    try {
      const result = await db
        .select({
          id: permissions.id,
          module: permissions.module,
          action: permissions.action,
          resource: permissions.resource,
          display_name: permissions.display_name,
          description: permissions.description,
          is_system_permission: permissions.is_system_permission,
          createdAt: permissions.createdAt,
          updatedAt: permissions.updatedAt,
        })
        .from(role_permissions)
        .innerJoin(permissions, eq(role_permissions.permissionId, permissions.id))
        .where(eq(role_permissions.roleId, roleId))
        .orderBy(permissions.module, permissions.action);

      return result;
    } catch (error) {
      console.error("Error getting permissions for role:", error);
      throw error;
    }
  }

  /**
   * Get user permissions (through roles)
   */
  static async getUserPermissions(userId: string, tenantId?: number | null): Promise<Permission[]> {
    try {
      // Build where conditions for user roles
      let whereConditions: any = and(
        eq(user_roles.userId, userId),
        eq(user_roles.is_active, true)
      );

      if (tenantId !== undefined) {
        if (tenantId === null) {
          whereConditions = and(whereConditions, sql`${user_roles.tenantId} IS NULL`);
        } else {
          whereConditions = and(whereConditions, eq(user_roles.tenantId, tenantId));
        }
      }

      // Get user's roles first
      const userRoleResults = await db
        .select({ roleId: user_roles.roleId })
        .from(user_roles)
        .where(whereConditions);

      if (userRoleResults.length === 0) {
        return [];
      }

      const roleIds = userRoleResults.map(ur => ur.roleId);

      // Get permissions for those roles
      const result = await db
        .select({
          id: permissions.id,
          module: permissions.module,
          action: permissions.action,
          resource: permissions.resource,
          display_name: permissions.display_name,
          description: permissions.description,
          is_system_permission: permissions.is_system_permission,
          createdAt: permissions.createdAt,
          updatedAt: permissions.updatedAt,
        })
        .from(role_permissions)
        .innerJoin(permissions, eq(role_permissions.permissionId, permissions.id))
        .where(inArray(role_permissions.roleId, roleIds))
        .orderBy(permissions.module, permissions.action);

      // Remove duplicates
      const uniquePermissions = result.filter((permission, index, self) =>
        index === self.findIndex(p => p.id === permission.id)
      );

      return uniquePermissions;
    } catch (error) {
      console.error("Error getting user permissions:", error);
      throw error;
    }
  }

  /**
   * Check if user has specific permission
   */
  static async userHasPermission(
    userId: string,
    module: string,
    action: string,
    tenantId?: number | null
  ): Promise<boolean> {
    try {
      const userPermissions = await this.getUserPermissions(userId, tenantId);
      
      return userPermissions.some(
        permission => permission.module === module && permission.action === action
      );
    } catch (error) {
      console.error("Error checking user permission:", error);
      return false;
    }
  }
}
