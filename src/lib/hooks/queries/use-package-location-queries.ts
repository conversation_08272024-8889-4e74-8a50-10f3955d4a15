import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { PackageLocationWithDetails, PackageLocationFilters } from "@/lib/services/package-location.service";
import { PackageLocation } from "@/lib/db/schema";

// Query Keys
export const packageLocationKeys = {
  all: ['package-locations'] as const,
  lists: () => [...packageLocationKeys.all, 'list'] as const,
  list: (filters: PackageLocationFilters) => [...packageLocationKeys.lists(), filters] as const,
  byPackage: (packageId: string) => [...packageLocationKeys.all, 'by-package', packageId] as const,
  byLocation: (locationId: string) => [...packageLocationKeys.all, 'by-location', locationId] as const,
};

// Fetch package locations with details
export function usePackageLocations(filters: PackageLocationFilters = {}) {
  return useQuery({
    queryKey: packageLocationKeys.list(filters),
    queryFn: async (): Promise<PackageLocationWithDetails[]> => {
      const params = new URLSearchParams();
      
      if (filters.packageId) params.append('packageId', filters.packageId);
      if (filters.locationId) params.append('locationId', filters.locationId);
      if (filters.search) params.append('search', filters.search);

      const response = await fetch(`/api/package-locations?${params.toString()}`);
      if (!response.ok) {
        throw new Error('Failed to fetch package locations');
      }

      const result = await response.json();
      return result.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Fetch locations for a specific package
export function usePackageLocations_ByPackage(packageId: string) {
  return useQuery({
    queryKey: packageLocationKeys.byPackage(packageId),
    queryFn: async (): Promise<PackageLocationWithDetails[]> => {
      const response = await fetch(`/api/package-locations?packageId=${packageId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch package locations');
      }

      const result = await response.json();
      return result.data;
    },
    enabled: !!packageId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Fetch packages for a specific location
export function usePackageLocations_ByLocation(locationId: string) {
  return useQuery({
    queryKey: packageLocationKeys.byLocation(locationId),
    queryFn: async (): Promise<PackageLocationWithDetails[]> => {
      const response = await fetch(`/api/package-locations?locationId=${locationId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch package locations');
      }

      const result = await response.json();
      return result.data;
    },
    enabled: !!locationId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Create package location relationship
export function useCreatePackageLocation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: { packageId: string; locationId: string }): Promise<PackageLocation> => {
      const response = await fetch('/api/package-locations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error('Failed to create package location');
      }

      return response.json();
    },
    onSuccess: (_, variables) => {
      // Invalidate all related queries
      queryClient.invalidateQueries({ queryKey: packageLocationKeys.all });
      queryClient.invalidateQueries({ queryKey: packageLocationKeys.byPackage(variables.packageId) });
      queryClient.invalidateQueries({ queryKey: packageLocationKeys.byLocation(variables.locationId) });
    },
  });
}

// Bulk update locations for a package
export function useBulkUpdatePackageLocations() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: { 
      packageId?: string; 
      locationId?: string; 
      locationIds?: string[]; 
      packageIds?: string[] 
    }): Promise<PackageLocation[]> => {
      const response = await fetch('/api/package-locations', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error('Failed to bulk update package locations');
      }

      const result = await response.json();
      return result.data;
    },
    onSuccess: (_, variables) => {
      // Invalidate all related queries
      queryClient.invalidateQueries({ queryKey: packageLocationKeys.all });
      
      if (variables.packageId) {
        queryClient.invalidateQueries({ queryKey: packageLocationKeys.byPackage(variables.packageId) });
      }
      
      if (variables.locationId) {
        queryClient.invalidateQueries({ queryKey: packageLocationKeys.byLocation(variables.locationId) });
      }

      // Invalidate specific location queries if locationIds provided
      if (variables.locationIds) {
        variables.locationIds.forEach(locationId => {
          queryClient.invalidateQueries({ queryKey: packageLocationKeys.byLocation(locationId) });
        });
      }

      // Invalidate specific package queries if packageIds provided
      if (variables.packageIds) {
        variables.packageIds.forEach(packageId => {
          queryClient.invalidateQueries({ queryKey: packageLocationKeys.byPackage(packageId) });
        });
      }
    },
  });
}

// Delete package location relationship
export function useDeletePackageLocation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: { packageId: string; locationId: string }): Promise<void> => {
      const response = await fetch(
        `/api/package-locations?packageId=${data.packageId}&locationId=${data.locationId}`,
        {
          method: 'DELETE',
        }
      );

      if (!response.ok) {
        throw new Error('Failed to delete package location');
      }
    },
    onSuccess: (_, variables) => {
      // Invalidate all related queries
      queryClient.invalidateQueries({ queryKey: packageLocationKeys.all });
      queryClient.invalidateQueries({ queryKey: packageLocationKeys.byPackage(variables.packageId) });
      queryClient.invalidateQueries({ queryKey: packageLocationKeys.byLocation(variables.locationId) });
    },
  });
}

// Convenience hooks for specific use cases
export function usePackageLocationsByPackage(packageId: string) {
  return usePackageLocations({ packageId });
}

export function usePackageLocationsByLocation(locationId: string) {
  return usePackageLocations({ locationId });
}

// Hook to get available locations for a package (locations not yet assigned)
export function useAvailableLocationsForPackage(packageId: string) {
  const { data: allLocations = [] } = useQuery({
    queryKey: ['locations', 'all'],
    queryFn: async () => {
      const response = await fetch('/api/locations');
      if (!response.ok) throw new Error('Failed to fetch locations');
      const result = await response.json();
      return result.data;
    },
  });

  const { data: assignedLocations = [] } = usePackageLocationsByPackage(packageId);

  const assignedLocationIds = assignedLocations.map(pl => pl.location_id);
  const availableLocations = allLocations.filter(location => 
    !assignedLocationIds.includes(location.id)
  );

  return {
    data: availableLocations,
    isLoading: false, // Derived from other queries
  };
}

// Hook to get available packages for a location (packages not yet assigned)
export function useAvailablePackagesForLocation(locationId: string) {
  const { data: allPackages = [] } = useQuery({
    queryKey: ['packages', 'all'],
    queryFn: async () => {
      const response = await fetch('/api/packages');
      if (!response.ok) throw new Error('Failed to fetch packages');
      const result = await response.json();
      return result.data;
    },
  });

  const { data: assignedPackages = [] } = usePackageLocationsByLocation(locationId);

  const assignedPackageIds = assignedPackages.map(pl => pl.package_id);
  const availablePackages = allPackages.filter(pkg => 
    !assignedPackageIds.includes(pkg.id)
  );

  return {
    data: availablePackages,
    isLoading: false, // Derived from other queries
  };
}
