import { Equipment, NewEquipment, equipment } from './../../db/schema';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { equipmentKeys, locationKeys } from './query-keys';
const equipmentApi = {
    getByTenant: async (tenantId: number): Promise<Equipment[]> => {
      const response = await fetch(`/api/equipments?tenantId=${tenantId}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch data: ${response.statusText}`);
      }
      const data = await response.json();
      return data.equip || [];
    },

    getAll: async (): Promise<Equipment[]> => {
      const response = await fetch('/api/equipments');
      if (!response.ok) {
        throw new Error(`Failed to fetch data: ${response.statusText}`);
      }
      const data = await response.json();
      return data.equip || [];
    },

    create: async (data: Omit<NewEquipment, "id" | "createdAt">): Promise<Equipment> => {
      const response = await fetch('/api/equipments', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create equipment');
      }
      const result = await response.json();
      return result.data;
    },

    update: async ({ equipmentId, data}: {
        equipmentId: string;
        data: Partial<NewEquipment>
    }): Promise<Equipment> => {
      const response = await fetch(`/api/equipments/${equipmentId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update equipment');
      }
      const result = await response.json();
      return result.data;
    },

    delete: async (equipmentId: string): Promise<Equipment> => {
      const response = await fetch(`/api/equipments/${equipmentId}`, {
        method: 'DELETE',
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete equipment');
      }
      const result = await response.json();
      return result.data;
    },

    bulkCreate: async (eqs: Omit<NewEquipment, "id" | "createdAt">[]): Promise<Equipment[]> => {
    const response = await fetch('/api/equipments/bulk', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ eqs }),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to bulk create equipments');
    }
    const result = await response.json();
    return result.equipment;
  },

    getStats: async (): Promise<{
      totalEquipments: number;
      activeEquipments: number;
      inactiveEquipments: number;
    }> => {
      const response = await fetch('/api/equipments/stats');
      if (!response.ok) {
        throw new Error('Failed to fetch data stats');
      }
      return response.json();
    },
  };


  // Query Hooks with backward compatibility
export function useEquipment(tenantIdOrOptions: number | null | { tenantId?: number }) {
  const tenantId = typeof tenantIdOrOptions === 'number'
    ? tenantIdOrOptions
    : (tenantIdOrOptions?.tenantId || null);

  const query = useQuery({
    queryKey: equipmentKeys.detail(tenantId || 0),
    queryFn: () => equipmentApi.getByTenant(tenantId!),
    enabled: !!tenantId && tenantId > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Return format compatible with old hook
  return {
    profile: query.data || null,
    loading: query.isLoading,
    error: query.error?.message || null,
    isLoading: query.isLoading,
    isError: query.isError,
    data: query.data,
    refetch: query.refetch,
  };
}

export function useEquipments() {
  const query = useQuery({
    queryKey: equipmentKeys.lists(),
    queryFn: async () => {
      const response = await fetch('/api/equipments');
      if (!response.ok) {
        throw new Error(`Failed to fetch equipments: ${response.statusText}`);
      }
      const data = await response.json();

      // Handle different possible response structures
      if (data.equip && Array.isArray(data.equip)) {
        return data.equip;
      } else if (data.equipment && Array.isArray(data.equipment)) {
        return data.equipment;
      } else if (Array.isArray(data)) {
        return data;
      } else {
        return [];
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: false, // Disable retry to prevent infinite loops
  });



  // Always return an array, never undefined
  const equipments = Array.isArray(query.data) ? query.data : [];

  return {
    profiles: equipments,
    loading: query.isLoading,
    error: query.error?.message || null,
    isLoading: query.isLoading,
    isError: query.isError,
    data: equipments,
    refetch: query.refetch,
  };
}


export function useCreateEquipment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: equipmentApi.create,
    onSuccess: (newEquipment) => {
      // Update cache with new equipment
      queryClient.setQueryData(equipmentKeys.detail(newEquipment.id), newEquipment);

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: equipmentKeys.lists() });
      if (newEquipment.tenantId) {
        queryClient.invalidateQueries({
          queryKey: equipmentKeys.byTenant(newEquipment.tenantId)
        });
        queryClient.invalidateQueries({
          queryKey: equipmentKeys.stats(newEquipment.tenantId)
        });
      }
    },
  });
}

export function useUpdateEquipment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: equipmentApi.update,
    onSuccess: (updateData) => {
      // Update cache with updated location
      queryClient.setQueryData(equipmentKeys.detail(updateData.id), updateData);

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: equipmentKeys.lists() });
      if (updateData.tenantId) {
        queryClient.invalidateQueries({
          queryKey: equipmentKeys.byTenant(updateData.tenantId)
        });
        queryClient.invalidateQueries({
          queryKey: equipmentKeys.stats(updateData.tenantId)
        });
      }
    },
  });
}

export function useDeleteEquipment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: equipmentApi.delete,
    onMutate: async (dataId: string) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: equipmentKeys.all });

      // Get the location data before deletion for rollback
      const previousData = queryClient.getQueryData(equipmentKeys.detail(dataId)) as Equipment;

      // Get all locations to update the list optimistically
      const previousDatas = queryClient.getQueryData(equipmentKeys.lists()) as Equipment[] | undefined;

      // Optimistically remove from all equipment list
      if (previousDatas) {
        const updateDatas = previousDatas.filter(loc => loc.id !== dataId);
        queryClient.setQueryData(equipmentKeys.lists(), updateDatas);
      }

      // Remove from detail cache
      queryClient.removeQueries({ queryKey: equipmentKeys.detail(dataId) });

      return { previousData, previousDatas };
    },
    onError: (error, dataId, context) => {
      // Restore the previous state if deletion failed
      if (context?.previousDatas) {
        queryClient.setQueryData(equipmentKeys.lists(), context.previousDatas);
      }
      if (context?.previousData) {
        queryClient.setQueryData(equipmentKeys.detail(dataId), context.previousData);
      }
    },
    onSuccess: (deleteData) => {
      // Invalidate and refetch related queries to ensure consistency
      queryClient.invalidateQueries({ queryKey: equipmentKeys.lists() });

      if (deleteData.tenantId) {
        queryClient.invalidateQueries({
          queryKey: equipmentKeys.byTenant(deleteData.tenantId)
        });
        queryClient.invalidateQueries({
          queryKey: equipmentKeys.stats(deleteData.tenantId)
        });
      }
    },
  });
}

export function useBulkCreateEquipment() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: equipmentApi.bulkCreate,
    onSuccess: (newDatas) => {
      // Invalidate all address-related queries
      queryClient.invalidateQueries({ queryKey: locationKeys.all });
      
      // If all addresses belong to the same tenant, invalidate tenant-specific queries
      const tenantIds = [...new Set(newDatas.map(addr => addr.tenantId).filter(Boolean))];
      tenantIds.forEach(tenantId => {
        queryClient.invalidateQueries({ queryKey: locationKeys.byTenant(tenantId!) });
        queryClient.invalidateQueries({ queryKey: locationKeys.stats(tenantId!) });
      });
    },
  });
}