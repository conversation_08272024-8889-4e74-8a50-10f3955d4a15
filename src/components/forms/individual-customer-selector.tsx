"use client";

import React, { useState, useCallback, useMemo } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { 
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { 
  Search, 
  X, 
  Users, 
  UserPlus, 
  Loader2, 
  CheckCircle2,
  AlertCircle,
  MapPin,
  Crown,
  Mail,
  Phone
} from "lucide-react";
import { 
  useCustomerSearch, 
  useCustomersByIds,
  type CustomerSearchResult 
} from "@/lib/hooks/queries/use-individual-customer-selection-queries";

interface IndividualCustomerSelectorProps {
  tenantId: number;
  selectedCustomerIds: string[];
  onSelectionChange: (customerIds: string[]) => void;
  maxSelections?: number;
  className?: string;
  placeholder?: string;
  disabled?: boolean;
}

export function IndividualCustomerSelector({
  tenantId,
  selectedCustomerIds,
  onSelectionChange,
  maxSelections = 50,
  className = "",
  placeholder = "Search and select customers...",
  disabled = false
}: IndividualCustomerSelectorProps) {


  const [searchTerm, setSearchTerm] = useState("");
  const [isOpen, setIsOpen] = useState(false);

  // Search customers with direct fetch for testing
  const [searchResults, setSearchResults] = useState<any>(null);
  const [isSearching, setIsSearching] = useState(false);
  const [searchError, setSearchError] = useState<any>(null);

  // Direct fetch for testing
  React.useEffect(() => {
    if (searchTerm && searchTerm.length >= 2) {
      setIsSearching(true);
      setSearchError(null);

      const params = new URLSearchParams();
      params.append('tenantId', String(tenantId));
      params.append('search', searchTerm);
      params.append('limit', '20');

      fetch(`/api/customers/search?${params}`)
        .then(response => response.json())
        .then(data => {
          console.log(`🔍 [IndividualCustomerSelector] Direct fetch result:`, data);
          setSearchResults(data.data);
          setIsSearching(false);
        })
        .catch(error => {
          console.error(`🔍 [IndividualCustomerSelector] Direct fetch error:`, error);
          setSearchError(error);
          setIsSearching(false);
        });
    } else {
      setSearchResults(null);
    }
  }, [searchTerm, tenantId]);



  // Get selected customers details
  const { 
    data: selectedCustomers = [], 
    isLoading: isLoadingSelected 
  } = useCustomersByIds(selectedCustomerIds, tenantId, {
    enabled: selectedCustomerIds.length > 0
  });

  // Handle customer selection
  const handleCustomerSelect = useCallback((customer: CustomerSearchResult) => {
    if (selectedCustomerIds.includes(customer.id)) {
      // Remove if already selected
      onSelectionChange(selectedCustomerIds.filter(id => id !== customer.id));
    } else {
      // Add if not selected and under limit
      if (selectedCustomerIds.length < maxSelections) {
        onSelectionChange([...selectedCustomerIds, customer.id]);
      }
    }
    setSearchTerm("");
    setIsOpen(false);
  }, [selectedCustomerIds, onSelectionChange, maxSelections]);

  // Handle customer removal
  const handleCustomerRemove = useCallback((customerId: string) => {
    onSelectionChange(selectedCustomerIds.filter(id => id !== customerId));
  }, [selectedCustomerIds, onSelectionChange]);

  // Get customer initials for avatar
  const getCustomerInitials = (customer: CustomerSearchResult) => {
    return `${customer.firstName.charAt(0)}${customer.lastName.charAt(0)}`.toUpperCase();
  };

  // Get customer display name
  const getCustomerDisplayName = (customer: CustomerSearchResult) => {
    return `${customer.firstName} ${customer.lastName}`;
  };

  // Filter search results to exclude already selected customers
  const filteredSearchResults = useMemo(() => {
    console.log(`🔍 [IndividualCustomerSelector] ===== FILTERING SEARCH RESULTS =====`);
    console.log(`🔍 [IndividualCustomerSelector] Processing search results:`, searchResults);
    console.log(`🔍 [IndividualCustomerSelector] Search results type:`, typeof searchResults);
    console.log(`🔍 [IndividualCustomerSelector] Search results customers:`, searchResults?.customers);
    console.log(`🔍 [IndividualCustomerSelector] Selected customer IDs:`, selectedCustomerIds);



    if (!searchResults?.customers) {
      console.log(`🔍 [IndividualCustomerSelector] No customers in search results - returning empty array`);
      return [];
    }

    console.log(`🔍 [IndividualCustomerSelector] About to filter ${searchResults.customers.length} customers`);

    const filtered = searchResults.customers.filter(customer => {
      const isSelected = selectedCustomerIds.includes(customer.id);
      console.log(`🔍 [IndividualCustomerSelector] Customer ${customer.firstName} ${customer.lastName} (${customer.id}) - isSelected: ${isSelected}`);
      return !isSelected;
    });

    console.log(`🔍 [IndividualCustomerSelector] Filtered results count:`, filtered.length);
    console.log(`🔍 [IndividualCustomerSelector] Filtered results:`, filtered);
    console.log(`🔍 [IndividualCustomerSelector] ===== END FILTERING =====`);
    return filtered;
  }, [searchResults?.customers, selectedCustomerIds, searchTerm]);

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Label and Info */}
      <div className="flex items-center justify-between">
        <Label className="flex items-center gap-2">
          <UserPlus className="h-4 w-4" />
          Individual Customer Targeting
        </Label>
        <div className="text-xs text-muted-foreground">
          {selectedCustomerIds.length} / {maxSelections} selected
        </div>
      </div>

      {/* Customer Search */}
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={isOpen}
            className="w-full justify-between"
            disabled={disabled || selectedCustomerIds.length >= maxSelections}
          >
            <div className="flex items-center gap-2">
              <Search className="h-4 w-4" />
              <span className="text-muted-foreground">
                {selectedCustomerIds.length >= maxSelections 
                  ? "Maximum customers selected" 
                  : placeholder
                }
              </span>
            </div>
            <Users className="h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          <Command>
            <CommandInput
              placeholder="Search customers by name or email..."
              value={searchTerm}
              onValueChange={setSearchTerm}
            />
            <CommandList>
              {isSearching && (
                <div className="flex items-center justify-center p-4">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span className="ml-2 text-sm">Searching customers...</span>
                </div>
              )}

              {searchError && (
                <div className="flex items-center justify-center p-4 text-destructive">
                  <AlertCircle className="h-4 w-4" />
                  <span className="ml-2 text-sm">Failed to search customers</span>
                </div>
              )}



              {!isSearching && !searchError && searchTerm.length >= 2 && (
                <>
                  {filteredSearchResults.length === 0 ? (
                    <CommandEmpty>No customers found.</CommandEmpty>
                  ) : (
                    <CommandGroup>
                      {filteredSearchResults.map((customer) => (
                        <CommandItem
                          key={customer.id}
                          value={`${customer.firstName} ${customer.lastName} ${customer.email}`}
                          onSelect={() => handleCustomerSelect(customer)}
                          className="flex items-center gap-3 p-3"
                        >
                          <Avatar className="h-8 w-8">
                            <AvatarFallback className="text-xs">
                              {getCustomerInitials(customer)}
                            </AvatarFallback>
                          </Avatar>
                          
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2">
                              <span className="font-medium truncate">
                                {getCustomerDisplayName(customer)}
                              </span>
                              {customer.pricingGroupName && (
                                <Badge variant="secondary" className="text-xs">
                                  <Crown className="h-3 w-3 mr-1" />
                                  {customer.pricingGroupName}
                                </Badge>
                              )}
                              <Badge 
                                variant={customer.isActive ? "default" : "destructive"}
                                className="text-xs"
                              >
                                {customer.isActive ? "Active" : "Inactive"}
                              </Badge>
                            </div>
                            
                            <div className="flex items-center gap-4 mt-1 text-xs text-muted-foreground">
                              <div className="flex items-center gap-1">
                                <Mail className="h-3 w-3" />
                                <span className="truncate">{customer.email}</span>
                              </div>
                              {customer.locationName && (
                                <div className="flex items-center gap-1">
                                  <MapPin className="h-3 w-3" />
                                  <span className="truncate">{customer.locationName}</span>
                                </div>
                              )}
                            </div>
                          </div>
                          
                          <CheckCircle2 className="h-4 w-4 text-primary" />
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  )}
                </>
              )}

              {searchTerm.length > 0 && searchTerm.length < 2 && (
                <div className="flex items-center justify-center p-4 text-muted-foreground">
                  <span className="text-sm">Type at least 2 characters to search</span>
                </div>
              )}
            </CommandList>
          </Command>
        </PopoverContent>
      </Popover>

      {/* Selected Customers */}
      {selectedCustomerIds.length > 0 && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: "auto" }}
          exit={{ opacity: 0, height: 0 }}
          className="space-y-3"
        >
          <div className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            <span className="text-sm font-medium">Selected Customers</span>
            <Badge variant="outline">{selectedCustomerIds.length}</Badge>
          </div>

          {isLoadingSelected ? (
            <div className="flex items-center gap-2 p-3 border rounded-lg">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span className="text-sm">Loading selected customers...</span>
            </div>
          ) : (
            <div className="grid gap-2">
              <AnimatePresence>
                {selectedCustomers.map((customer) => (
                  <motion.div
                    key={customer.id}
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.95 }}
                    className="flex items-center gap-3 p-3 border rounded-lg bg-muted/50"
                  >
                    <Avatar className="h-8 w-8">
                      <AvatarFallback className="text-xs">
                        {getCustomerInitials(customer)}
                      </AvatarFallback>
                    </Avatar>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <span className="font-medium truncate">
                          {getCustomerDisplayName(customer)}
                        </span>
                        {customer.pricingGroupName && (
                          <Badge variant="secondary" className="text-xs">
                            <Crown className="h-3 w-3 mr-1" />
                            {customer.pricingGroupName}
                          </Badge>
                        )}
                      </div>
                      
                      <div className="flex items-center gap-4 mt-1 text-xs text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <Mail className="h-3 w-3" />
                          <span className="truncate">{customer.email}</span>
                        </div>
                        {customer.locationName && (
                          <div className="flex items-center gap-1">
                            <MapPin className="h-3 w-3" />
                            <span className="truncate">{customer.locationName}</span>
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleCustomerRemove(customer.id)}
                      className="h-8 w-8 p-0"
                      disabled={disabled}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </motion.div>
                ))}
              </AnimatePresence>
            </div>
          )}
        </motion.div>
      )}

      {/* Helper Text */}
      <p className="text-xs text-muted-foreground">
        Search and select specific customers to target this package individually. 
        This works alongside segment-based targeting.
      </p>
    </div>
  );
}
