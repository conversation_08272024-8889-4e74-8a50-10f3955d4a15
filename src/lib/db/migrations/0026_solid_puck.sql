CREATE TABLE "class_schedules" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"class_id" varchar(255) NOT NULL,
	"tenant_id" integer,
	"location_id" varchar(255),
	"facility_id" varchar(255),
	"staff_id" varchar(255),
	"start_date" date,
	"end_date" date,
	"start_time" timestamp,
	"end_time" timestamp,
	"duration" integer NOT NULL,
	"calender_color" varchar(50),
	"repeat_rule" varchar(50) DEFAULT 'none' NOT NULL,
	"pax" integer DEFAULT 1 NOT NULL,
	"waitlist" integer DEFAULT 1 NOT NULL,
	"allow_classpass" boolean DEFAULT false NOT NULL,
	"is_private" boolean DEFAULT false NOT NULL,
	"publish_now" boolean DEFAULT false NOT NULL,
	"publish_at" timestamp,
	"auto_cancel_if_minimum_not_met" boolean DEFAULT false NOT NULL,
	"booking_window_start" timestamp,
	"booking_window_end" timestamp,
	"check_in_window_start" timestamp,
	"check_in_window_end" timestamp,
	"late_cancellation_rule" varchar(255),
	"created_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updated_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL
);
--> statement-breakpoint
CREATE TABLE "facilities" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"tenant_id" integer NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" varchar(500),
	"is_active" boolean DEFAULT true NOT NULL,
	"images" jsonb DEFAULT '[]'::jsonb,
	"created_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updated_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL
);
--> statement-breakpoint
ALTER TABLE "class_schedules" ADD CONSTRAINT "class_schedules_class_id_classes_id_fk" FOREIGN KEY ("class_id") REFERENCES "public"."classes"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "class_schedules" ADD CONSTRAINT "class_schedules_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "class_schedules" ADD CONSTRAINT "class_schedules_location_id_locations_id_fk" FOREIGN KEY ("location_id") REFERENCES "public"."locations"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "class_schedules" ADD CONSTRAINT "class_schedules_facility_id_facilities_id_fk" FOREIGN KEY ("facility_id") REFERENCES "public"."facilities"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "class_schedules" ADD CONSTRAINT "class_schedules_staff_id_users_id_fk" FOREIGN KEY ("staff_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "facilities" ADD CONSTRAINT "facilities_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;