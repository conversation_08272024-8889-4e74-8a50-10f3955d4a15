# 📦 Workflow Package Purchase Options - Panduan Lengkap

## 🎯 Apa itu Package Purchase Options?

Halo teman-teman! Jadi gini, **Package Purchase Options** itu adalah fitur yang memungkinkan kita mengatur bagaimana customer bisa beli dan pakai package di aplikasi kita. Misalnya:

- Berapa banyak orang yang boleh beli package ini?
- Siapa aja yang boleh beli? (semua orang, member only, dll)
- Bisa dipindahtangankan ke orang lain atau nggak?
- Harus dijual di lokasi tertentu atau nggak?
- Kalau ini package kelas, berapa kali bisa booking?
- Ditampilkan di website/app atau nggak?

## 🏗️ Gimana Cara Kerjanya?

### 1. **Arsitektur yang Kita Pakai**

Kita pakai pola yang sama seperti fitur-fitur lain di aplikasi ini:

```
📁 Service Layer (package-purchase-options.service.ts)
   ↓ Mengurus database operations (CRUD)
   
📁 Hooks Layer (use-package-purchase-options-queries.ts)  
   ↓ Mengurus data fetching dengan TanStack Query
   
📁 API Layer (api/package-purchase-options/)
   ↓ Mengurus HTTP requests dan responses
   
📁 UI Layer (package-purchase-options-section.tsx)
   ↓ Mengurus tampilan form dan user interaction
```

### 2. **Database Structure**

```sql
package_purchase_options:
├── package_id (ID package yang mau dikasih opsi)
├── purchase_limit (maksimal berapa orang bisa beli)
├── restrict_to (siapa yang boleh beli)
├── transferable (bisa dipindahtangankan atau nggak)
├── specify_sold_at_location (harus dijual di lokasi tertentu)
├── sold_at_location_id (ID lokasi kalau specify_sold_at_location = true)
├── class_booking_limit (maksimal berapa kelas bisa dibooking)
└── show_online (ditampilkan online atau nggak)
```

## 🚀 Step-by-Step Workflow

### **Step 1: User Buka Form Create Package**

1. User klik tombol "Create Package"
2. Dialog form terbuka dengan beberapa section:
   - **Basic Information** (nama, deskripsi, dll)
   - **Package Settings** (active/inactive, public/private)
   - **Purchase Options** ← Ini yang baru kita tambahkan!

### **Step 2: User Isi Purchase Options**

Di section Purchase Options, user bisa atur:

#### **Purchase Limit**
- Input number (0 - 1,000,000)
- Kosong = unlimited
- Contoh: "100" artinya maksimal 100 orang bisa beli

#### **Restrict To**
- Dropdown dengan pilihan:
  - All Customers (semua customer)
  - Members Only (member aja)
  - New Customers Only (customer baru aja)
  - Existing Customers Only (customer lama aja)
  - VIP Members Only (VIP member aja)

#### **Class Booking Limit**
- Input number (0 - 10,000)
- Kosong = unlimited
- Contoh: "20" artinya bisa booking maksimal 20 kelas

#### **Sold At Location**
- Dropdown lokasi (data dari locations table)
- Kosong = semua lokasi
- Contoh: "Jakarta Pusat" artinya cuma bisa dijual di Jakarta Pusat

#### **Boolean Options (Switch)**
- **Transferable**: Bisa dipindahtangankan atau nggak
- **Location Specific**: Harus dijual di lokasi tertentu
- **Show Online**: Ditampilkan di website/app

### **Step 3: Form Validation**

Sistem akan validasi input secara real-time:

```typescript
// Contoh validasi
if (purchaseLimit > 1000000) {
  error = "Purchase limit tidak boleh lebih dari 1,000,000"
}

if (specifySoldAtLocation && !soldAtLocationId) {
  error = "Lokasi harus dipilih kalau Location Specific diaktifkan"
}
```

### **Step 4: Submit Form**

Ketika user klik "Create Package":

1. **Frontend** kirim data ke API
2. **API** validasi data dengan Zod schema
3. **Service** buat package dulu di database
4. **Service** buat purchase options dengan package_id yang baru dibuat
5. **Success!** Tampilkan animasi success + confetti 🎉

### **Step 5: Success Animation**

```typescript
// Yang terjadi setelah berhasil create:
setSuccessMessage("Package created successfully!");
setShowSuccessModal(true);
setShowConfetti(true);

// Confetti hilang setelah 3 detik
setTimeout(() => setShowConfetti(false), 3000);
```

## 🔄 Edit Package Workflow

### **Step 1: User Klik Edit Package**

1. User klik tombol edit di package list
2. System fetch existing purchase options untuk package tersebut
3. Form terbuka dengan data yang sudah ada

### **Step 2: Form Pre-filled**

```typescript
// Data yang sudah ada akan di-load ke form
defaultValues: {
  purchaseOptions: {
    purchaseLimit: existingPurchaseOptions?.purchase_limit,
    restrictTo: existingPurchaseOptions?.restrict_to || "all",
    transferable: existingPurchaseOptions?.transferable ?? false,
    // ... dst
  }
}
```

### **Step 3: User Edit Data**

User bisa ubah data apa aja yang mau diubah

### **Step 4: Submit Update**

1. **Frontend** kirim data update ke API
2. **API** update package data
3. **API** update purchase options data
4. **Success!** Tampilkan animasi success

## 🎨 UI/UX Features

### **1. Loading States**

```typescript
// Saat loading locations
{locationsLoading && <Loader2 className="h-3 w-3 animate-spin" />}

// Placeholder yang berubah
placeholder={
  locationsLoading 
    ? "Loading locations..." 
    : locationsError 
    ? "Error loading locations" 
    : "Select location (optional)"
}
```

### **2. Error Handling**

```typescript
// Real-time validation
{field.state.meta.errors.length > 0 && (
  <div className="flex items-center gap-2 text-sm text-destructive">
    <AlertCircle className="h-4 w-4" />
    {field.state.meta.errors[0]}
  </div>
)}
```

### **3. Responsive Layout**

```css
/* Two-column di desktop, single column di mobile */
.grid.grid-cols-1.md:grid-cols-2.gap-4
```

## 🔧 Technical Implementation

### **1. Service Layer**

```typescript
class PackagePurchaseOptionsService extends BaseService {
  async create(data) {
    // Validasi data
    this.validateCreateData(data);
    
    // Cek package exists
    const packageExists = await checkPackage(data.packageId);
    
    // Cek purchase options belum ada
    const existing = await this.getByPackageId(data.packageId);
    if (existing) throw new Error("Already exists");
    
    // Create purchase options
    return await db.insert(package_purchase_options).values(data);
  }
}
```

### **2. Hooks Layer**

```typescript
export function useCreatePackagePurchaseOptions() {
  return useMutation({
    mutationFn: packagePurchaseOptionsApi.create,
    onSuccess: (data) => {
      // Invalidate cache
      queryClient.invalidateQueries(['package-purchase-options']);
      queryClient.invalidateQueries(['package-purchase-options', 'by-package', data.package_id]);
    },
  });
}
```

### **3. API Layer**

```typescript
export async function POST(request: NextRequest) {
  // Auth check
  const session = await auth();
  if (!session?.user) return unauthorized();
  
  // Validate input
  const validatedData = createSchema.parse(await request.json());
  
  // Create purchase options
  const result = await service.create(validatedData);
  
  return NextResponse.json({ success: true, data: result });
}
```

## 🧪 Testing Scenarios

### **1. Happy Path Testing**

```typescript
// Test create package dengan purchase options
test("Create package with purchase options", async () => {
  // 1. Fill package basic info
  // 2. Fill purchase options
  // 3. Submit form
  // 4. Verify success animation
  // 5. Verify data in database
});
```

### **2. Error Handling Testing**

```typescript
// Test validation errors
test("Show error when purchase limit exceeds maximum", async () => {
  // 1. Input purchase limit > 1,000,000
  // 2. Verify error message appears
  // 3. Verify form cannot be submitted
});
```

### **3. Integration Testing**

```typescript
// Test dengan locations dropdown
test("Load locations for dropdown", async () => {
  // 1. Open form
  // 2. Verify locations loading
  // 3. Verify locations populated
  // 4. Test location selection
});
```

## 🚨 Common Issues & Solutions

### **Issue 1: Locations tidak load**

**Penyebab**: API error atau network issue
**Solusi**: 
```typescript
// Error handling di component
{locationsError && (
  <div className="text-sm text-destructive">
    Failed to load locations. Please refresh.
  </div>
)}
```

### **Issue 2: Form validation tidak jalan**

**Penyebab**: Schema validation error
**Solusi**: 
```typescript
// Debug validation
console.log("Validation errors:", field.state.meta.errors);
```

### **Issue 3: Success animation tidak muncul**

**Penyebab**: State management issue
**Solusi**:
```typescript
// Pastikan state di-set dengan benar
setShowSuccessModal(true);
setShowConfetti(true);
```

## 🎯 Best Practices

### **1. Form Design**
- Gunakan two-column layout untuk efisiensi space
- Berikan helper text yang jelas
- Gunakan appropriate input types (number, select, switch)

### **2. Validation**
- Real-time validation untuk immediate feedback
- Server-side validation untuk security
- Clear error messages yang actionable

### **3. Performance**
- Cache locations data
- Debounce input validation
- Lazy load components yang tidak immediately visible

### **4. User Experience**
- Loading states untuk semua async operations
- Success animations untuk positive feedback
- Error handling yang graceful

---

## 📝 Kesimpulan

Implementasi Package Purchase Options ini mengikuti best practices dan pola yang sudah established di codebase. Dengan workflow yang jelas dan dokumentasi yang lengkap, fitur ini mudah untuk di-maintain dan di-extend di masa depan.

**Key Points:**
- ✅ Modular dan reusable architecture
- ✅ Type-safe dengan TypeScript
- ✅ Comprehensive validation
- ✅ Great user experience dengan animations
- ✅ Performance optimized
- ✅ Well documented

Sekarang teman-teman bisa dengan mudah memahami dan menggunakan fitur Package Purchase Options ini! 🚀
