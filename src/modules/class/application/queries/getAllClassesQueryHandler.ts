import { PublicClassDTO } from '../dto/ClassDTO';
import { db } from '@/lib/db';
import { classes, class_categories } from '@/lib/db/schema';
import { eq, and, desc, sql } from 'drizzle-orm';

interface GetAllClassesQuery {
  tenantId?: string;
  limit: number;
  page: number;
}

export async function getAllClassesQueryHandler(query: GetAllClassesQuery): Promise<{ classes: PublicClassDTO[]; meta: any }> {
  try {
    // Build where conditions
    let whereConditions = eq(classes.is_active, true);

    if (query.tenantId) {
      whereConditions = and(
        whereConditions,
        eq(classes.tenantId, parseInt(query.tenantId, 10))
      ) as any;
    }

    // Calculate offset
    const offset = (query.page - 1) * query.limit;

    // Query with JOIN to get category name
    const classResults = await db
      .select({
        id: classes.id,
        name: classes.name,
        description: classes.description,
        categoryId: classes.categoryId,
        categoryName: class_categories.name,
        is_active: classes.is_active,
        createdAt: classes.createdAt,
        updatedAt: classes.updatedAt,
      })
      .from(classes)
      .leftJoin(class_categories, eq(classes.categoryId, class_categories.id))
      .where(whereConditions)
      .orderBy(desc(classes.createdAt))
      .limit(query.limit)
      .offset(offset);

    // Count total for metadata
    const totalResult = await db
      .select({ count: sql<number>`count(*)` })
      .from(classes)
      .leftJoin(class_categories, eq(classes.categoryId, class_categories.id))
      .where(whereConditions);

    const total = Number(totalResult[0]?.count || 0);

    // Transform to PublicClassDTO format
    const transformedClasses: PublicClassDTO[] = classResults.map(cls => ({
      id: cls.id,
      name: cls.name,
      description: cls.description || '',
      category: cls.categoryName || 'Uncategorized', // Use category name for display
      categoryId: cls.categoryId, // Keep categoryId for reference
      startDate: new Date().toISOString(), // TODO: Use actual dates from schema
      endDate: new Date().toISOString(), // TODO: Use actual dates from schema
      isActive: cls.is_active,
      createdAt: cls.createdAt.toISOString(),
      updatedAt: cls.updatedAt.toISOString(),
    }));

    return {
      classes: transformedClasses,
      meta: {
        total,
        page: query.page,
        limit: query.limit,
        pageCount: Math.ceil(total / query.limit)
      }
    };
  } catch (error) {
    console.error('Error in getAllClassesQueryHandler:', error);
    throw error;
  }
}
