/**
 * Rate Limiting Configuration for Customer Authentication
 * 
 * FAANG-level rate limiting with different tiers for different endpoints
 */

export const rateLimitConfig = {
  // Authentication endpoints - stricter limits
  auth: {
    login: {
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 5, // 5 attempts per 15 minutes
    },
    oauth: {
      windowMs: 5 * 60 * 1000, // 5 minutes
      max: 10, // 10 OAuth attempts per 5 minutes
    },
    refresh: {
      windowMs: 60 * 1000, // 1 minute
      max: 10, // 10 refresh attempts per minute
    },
    logout: {
      windowMs: 60 * 1000, // 1 minute
      max: 20, // 20 logout attempts per minute
    },
  },

  // API endpoints - moderate limits
  api: {
    general: {
      windowMs: 60 * 1000, // 1 minute
      max: 100, // 100 requests per minute
    },
    profile: {
      windowMs: 60 * 1000, // 1 minute
      max: 30, // 30 profile requests per minute
    },
    upload: {
      windowMs: 60 * 1000, // 1 minute
      max: 10, // 10 uploads per minute
    },
  },

  // Public endpoints - lenient limits
  public: {
    general: {
      windowMs: 60 * 1000, // 1 minute
      max: 200, // 200 requests per minute
    },
  },

  // Global limits per IP
  global: {
    windowMs: 60 * 1000, // 1 minute
    max: 500, // 500 requests per minute per IP
  },
} as const;

/**
 * Security headers configuration
 */
export const securityHeaders = {
  // CORS configuration for customer API
  cors: {
    origin: process.env.FRONTEND_URL || "*",
    methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowedHeaders: [
      "Content-Type",
      "Authorization",
      "X-Tenant-ID",
      "X-Device-ID",
      "X-Client-Version",
    ],
    credentials: true,
    maxAge: 86400, // 24 hours
  },

  // Content Security Policy
  csp: {
    directives: {
      "default-src": ["'none'"],
      "frame-ancestors": ["'none'"],
      "base-uri": ["'self'"],
      "form-action": ["'self'"],
    },
  },

  // Security headers
  security: {
    "X-Content-Type-Options": "nosniff",
    "X-Frame-Options": "DENY",
    "X-XSS-Protection": "1; mode=block",
    "Referrer-Policy": "strict-origin-when-cross-origin",
    "Permissions-Policy": "camera=(), microphone=(), geolocation=()",
    "Strict-Transport-Security": "max-age=31536000; includeSubDomains",
  },
} as const;

/**
 * JWT token configuration
 */
export const jwtConfig = {
  // Token expiry times
  expiry: {
    accessToken: "1h", // 1 hour
    refreshToken: "30d", // 30 days
    emailVerification: "24h", // 24 hours
    passwordReset: "1h", // 1 hour
  },

  // Token algorithms
  algorithm: "HS256" as const,

  // Token issuer and audience
  issuer: process.env.JWT_ISSUER || "saas-app",
  audience: process.env.JWT_AUDIENCE || "customer",

  // Token validation options
  validation: {
    clockTolerance: 30, // 30 seconds clock skew tolerance
    maxAge: "1h", // Maximum token age
  },
} as const;

/**
 * OAuth configuration
 */
export const oauthConfig = {
  google: {
    scopes: ["openid", "email", "profile"],
    prompt: "consent",
    accessType: "offline",
    
    // PKCE configuration for mobile
    pkce: {
      codeChallengeMethod: "S256" as const,
      codeVerifierLength: 128,
      challengeExpiry: 10 * 60 * 1000, // 10 minutes
    },
  },

  // OAuth flow timeouts
  timeouts: {
    authorizationCode: 10 * 60 * 1000, // 10 minutes
    tokenExchange: 30 * 1000, // 30 seconds
    userProfile: 30 * 1000, // 30 seconds
  },
} as const;

/**
 * Session management configuration
 */
export const sessionConfig = {
  // Session cleanup intervals
  cleanup: {
    expired: 60 * 60 * 1000, // Clean expired sessions every hour
    inactive: 24 * 60 * 60 * 1000, // Clean inactive sessions every 24 hours
  },

  // Session limits per customer
  limits: {
    maxActiveSessions: 10, // Maximum 10 active sessions per customer
    maxDevicesPerCustomer: 5, // Maximum 5 devices per customer
  },

  // Session security
  security: {
    rotateOnLogin: true, // Rotate session on login
    invalidateOnPasswordChange: true, // Invalidate all sessions on password change
    trackSuspiciousActivity: true, // Track suspicious login patterns
  },
} as const;

/**
 * Audit logging configuration
 */
export const auditConfig = {
  // Events to log
  events: {
    authentication: [
      "login_attempt",
      "login_success",
      "login_failed",
      "logout",
      "token_refresh",
      "token_revoked",
    ],
    oauth: [
      "oauth_start",
      "oauth_callback",
      "oauth_success",
      "oauth_failed",
      "oauth_link",
      "oauth_unlink",
    ],
    security: [
      "suspicious_activity",
      "rate_limit_exceeded",
      "invalid_token",
      "session_hijack_attempt",
    ],
    account: [
      "account_created",
      "account_updated",
      "account_locked",
      "account_unlocked",
      "email_verified",
      "password_changed",
    ],
  },

  // Retention periods
  retention: {
    authLogs: 90, // 90 days
    securityLogs: 365, // 1 year
    auditLogs: 2555, // 7 years (compliance)
  },

  // Log levels
  levels: {
    info: ["login_success", "logout", "account_created"],
    warning: ["login_failed", "rate_limit_exceeded"],
    error: ["oauth_failed", "suspicious_activity"],
    critical: ["session_hijack_attempt", "account_locked"],
  },
} as const;

/**
 * Error codes for customer authentication
 */
export const authErrorCodes = {
  // Authentication errors
  INVALID_CREDENTIALS: "INVALID_CREDENTIALS",
  ACCOUNT_LOCKED: "ACCOUNT_LOCKED",
  ACCOUNT_DISABLED: "ACCOUNT_DISABLED",
  EMAIL_NOT_VERIFIED: "EMAIL_NOT_VERIFIED",
  
  // Token errors
  TOKEN_INVALID: "TOKEN_INVALID",
  TOKEN_EXPIRED: "TOKEN_EXPIRED",
  TOKEN_REVOKED: "TOKEN_REVOKED",
  REFRESH_TOKEN_INVALID: "REFRESH_TOKEN_INVALID",
  
  // OAuth errors
  OAUTH_ERROR: "OAUTH_ERROR",
  OAUTH_STATE_MISMATCH: "OAUTH_STATE_MISMATCH",
  OAUTH_CODE_INVALID: "OAUTH_CODE_INVALID",
  OAUTH_PROVIDER_ERROR: "OAUTH_PROVIDER_ERROR",
  
  // Session errors
  SESSION_INVALID: "SESSION_INVALID",
  SESSION_EXPIRED: "SESSION_EXPIRED",
  SESSION_LIMIT_EXCEEDED: "SESSION_LIMIT_EXCEEDED",
  
  // Rate limiting
  RATE_LIMIT_EXCEEDED: "RATE_LIMIT_EXCEEDED",
  
  // Validation errors
  INVALID_INPUT: "INVALID_INPUT",
  MISSING_REQUIRED_FIELD: "MISSING_REQUIRED_FIELD",
  TENANT_MISMATCH: "TENANT_MISMATCH",
  
  // Server errors
  INTERNAL_ERROR: "INTERNAL_ERROR",
  SERVICE_UNAVAILABLE: "SERVICE_UNAVAILABLE",
} as const;

/**
 * HTTP status codes for authentication responses
 */
export const authStatusCodes = {
  // Success
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  
  // Client errors
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  TOO_MANY_REQUESTS: 429,
  
  // Server errors
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503,
} as const;

/**
 * Validation schemas for common auth inputs
 */
export const validationSchemas = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  password: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
  tenantId: /^\d+$/,
  deviceId: /^[a-zA-Z0-9-_]{1,255}$/,
  ipAddress: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$|^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/,
} as const;
