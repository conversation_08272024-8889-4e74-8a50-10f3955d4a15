# 🧪 CORS Test Script

## Test dari Browser Console (localhost:3002)

Buka browser di `http://localhost:3002` dan jalankan script ini di console:

```javascript
// Test 1: OPTIONS Preflight Request
console.log('🧪 Testing CORS Preflight...');
fetch('http://localhost:3000/api/public/v1/class-schedules', {
  method: 'OPTIONS',
  headers: {
    'Origin': 'http://localhost:3002',
    'Access-Control-Request-Method': 'GET',
    'Access-Control-Request-Headers': 'X-API-Key, Content-Type'
  }
})
.then(response => {
  console.log('✅ Preflight Status:', response.status);
  console.log('✅ CORS Headers:', {
    'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
    'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
    'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers'),
    'Access-Control-Allow-Credentials': response.headers.get('Access-Control-Allow-Credentials')
  });
})
.catch(error => console.error('❌ Preflight Error:', error));

// Test 2: Actual GET Request
console.log('🧪 Testing Actual Request...');
fetch('http://localhost:3000/api/public/v1/class-schedules?tenantId=1&limit=10', {
  method: 'GET',
  headers: {
    'X-API-Key': 'pk_9b4d8a5f3e9ec298be21ac0589d0346d0289f717d9ca3841093498ef4530c64b',
    'Content-Type': 'application/json'
  }
})
.then(response => {
  console.log('✅ Request Status:', response.status);
  console.log('✅ Response Headers:', {
    'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
    'Content-Type': response.headers.get('Content-Type')
  });
  return response.json();
})
.then(data => {
  console.log('✅ Response Data:', data);
  if (data.success) {
    console.log('🎉 CORS Fix berhasil! Data received:', data.data.schedules.length, 'schedules');
  }
})
.catch(error => console.error('❌ Request Error:', error));
```

## Expected Results

### ✅ Success Response:
```
✅ Preflight Status: 200
✅ CORS Headers: {
  Access-Control-Allow-Origin: "http://localhost:3002",
  Access-Control-Allow-Methods: "GET, POST, PUT, DELETE, OPTIONS",
  Access-Control-Allow-Headers: "Content-Type, Authorization, X-API-Key, X-Tenant-ID",
  Access-Control-Allow-Credentials: "true"
}

✅ Request Status: 200
✅ Response Data: { success: true, data: { schedules: [...], pagination: {...} } }
🎉 CORS Fix berhasil! Data received: X schedules
```

### ❌ If Still Error:
```
❌ Preflight Error: CORS error
❌ Request Error: CORS policy blocked
```

## Manual Test dengan curl

```bash
# Test OPTIONS request
curl -X OPTIONS \
  -H "Origin: http://localhost:3002" \
  -H "Access-Control-Request-Method: GET" \
  -H "Access-Control-Request-Headers: X-API-Key, Content-Type" \
  -v \
  http://localhost:3000/api/public/v1/class-schedules

# Test GET request
curl -X GET \
  -H "X-API-Key: pk_9b4d8a5f3e9ec298be21ac0589d0346d0289f717d9ca3841093498ef4530c64b" \
  -H "Origin: http://localhost:3002" \
  -v \
  "http://localhost:3000/api/public/v1/class-schedules?tenantId=1&limit=10"
```

## Troubleshooting

### Jika masih ada CORS error:

1. **Check server restart** - Pastikan Next.js dev server sudah restart
2. **Clear browser cache** - Hard refresh (Ctrl+Shift+R)
3. **Check console errors** - Lihat detail error di Network tab
4. **Verify headers** - Pastikan semua response headers ada

### Debug Headers:
```javascript
// Check response headers
fetch('http://localhost:3000/api/public/v1/class-schedules?tenantId=1&limit=1', {
  method: 'GET',
  headers: { 'X-API-Key': 'pk_9b4d8a5f3e9ec298be21ac0589d0346d0289f717d9ca3841093498ef4530c64b' }
})
.then(response => {
  console.log('All Headers:');
  for (let [key, value] of response.headers.entries()) {
    console.log(`${key}: ${value}`);
  }
});