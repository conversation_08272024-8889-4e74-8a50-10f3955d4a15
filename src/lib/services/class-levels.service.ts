import { db } from "@/lib/db";
import { class_levels } from "@/lib/db/schema";
import { eq, desc, asc, sql } from "drizzle-orm";
import { createId } from "@paralleldrive/cuid2";

export interface CreateClassLevelData {
  tenantId: number;
  name: string;
  description?: string;
  sortOrder?: number;
  isActive?: boolean;
}

export interface UpdateClassLevelData {
  tenantId?: number;
  name?: string;
  description?: string;
  sortOrder?: number;
  isActive?: boolean;
}

export interface ReorderClassLevelData {
  id: string;
  sortOrder: number;
}

export class ClassLevelService {
  // Get all class levels
  static async getAll() {
    return await db
      .select()
      .from(class_levels)
      .orderBy(asc(class_levels.sortOrder), asc(class_levels.name));
  }

  // Get class levels by tenant ID
  static async getByTenantId(tenantId: number) {
    return await db
      .select()
      .from(class_levels)
      .where(eq(class_levels.tenantId, tenantId))
      .orderBy(asc(class_levels.sortOrder), asc(class_levels.name));
  }

  // Get active class levels by tenant ID
  static async getActiveByTenantId(tenantId: number) {
    return await db
      .select()
      .from(class_levels)
      .where(
        sql`${class_levels.tenantId} = ${tenantId} AND ${class_levels.isActive} = true`
      )
      .orderBy(asc(class_levels.sortOrder), asc(class_levels.name));
  }

  // Get single class level by ID
  static async getById(id: string) {
    const [result] = await db
      .select()
      .from(class_levels)
      .where(eq(class_levels.id, id));
    
    return result || null;
  }

  // Create new class level
  static async create(data: CreateClassLevelData) {
    const id = createId();
    
    // Get the next sort order if not provided
    let sortOrder = data.sortOrder;
    if (sortOrder === undefined) {
      const [maxOrder] = await db
        .select({ max: sql<number>`max(${class_levels.sortOrder})` })
        .from(class_levels)
        .where(eq(class_levels.tenantId, data.tenantId));
      
      sortOrder = (maxOrder.max || 0) + 1;
    }
    
    const [newClassLevel] = await db
      .insert(class_levels)
      .values({
        id,
        tenantId: data.tenantId,
        name: data.name,
        description: data.description,
        sortOrder,
        isActive: data.isActive ?? true,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning();

    return newClassLevel;
  }

  // Update class level
  static async update(id: string, data: UpdateClassLevelData) {
    const [updatedClassLevel] = await db
      .update(class_levels)
      .set({
        ...data,
        updatedAt: new Date(),
      })
      .where(eq(class_levels.id, id))
      .returning();

    if (!updatedClassLevel) {
      throw new Error("Class level not found");
    }

    return updatedClassLevel;
  }

  // Delete class level
  static async delete(id: string) {
    const [deletedClassLevel] = await db
      .delete(class_levels)
      .where(eq(class_levels.id, id))
      .returning();

    if (!deletedClassLevel) {
      throw new Error("Class level not found");
    }

    return deletedClassLevel;
  }

  // Reorder class levels
  static async reorder(tenantId: number, reorderData: ReorderClassLevelData[]) {
    // Use transaction to ensure all updates succeed or fail together
    return await db.transaction(async (tx) => {
      const updatedClassLevels = [];
      
      for (const item of reorderData) {
        const [updated] = await tx
          .update(class_levels)
          .set({
            sortOrder: item.sortOrder,
            updatedAt: new Date(),
          })
          .where(
            sql`${class_levels.id} = ${item.id} AND ${class_levels.tenantId} = ${tenantId}`
          )
          .returning();
        
        if (updated) {
          updatedClassLevels.push(updated);
        }
      }
      
      return updatedClassLevels;
    });
  }

  // Toggle active status
  static async toggleActive(id: string) {
    // First get current status
    const current = await this.getById(id);
    if (!current) {
      throw new Error("Class level not found");
    }

    // Toggle the status
    const [updated] = await db
      .update(class_levels)
      .set({
        isActive: !current.isActive,
        updatedAt: new Date(),
      })
      .where(eq(class_levels.id, id))
      .returning();

    return updated;
  }

  // Get statistics
  static async getStats(tenantId?: number) {
    let whereClause = sql`1=1`;
    if (tenantId) {
      whereClause = sql`${class_levels.tenantId} = ${tenantId}`;
    }

    const [totalCount] = await db
      .select({ count: sql<number>`count(*)` })
      .from(class_levels)
      .where(whereClause);

    const [activeCount] = await db
      .select({ count: sql<number>`count(*)` })
      .from(class_levels)
      .where(sql`${whereClause} AND ${class_levels.isActive} = true`);

    const [inactiveCount] = await db
      .select({ count: sql<number>`count(*)` })
      .from(class_levels)
      .where(sql`${whereClause} AND ${class_levels.isActive} = false`);

    return {
      total: totalCount.count,
      active: activeCount.count,
      inactive: inactiveCount.count,
    };
  }

  // Bulk update active status
  static async bulkUpdateActive(ids: string[], isActive: boolean) {
    return await db.transaction(async (tx) => {
      const updatedClassLevels = [];
      
      for (const id of ids) {
        const [updated] = await tx
          .update(class_levels)
          .set({
            isActive,
            updatedAt: new Date(),
          })
          .where(eq(class_levels.id, id))
          .returning();
        
        if (updated) {
          updatedClassLevels.push(updated);
        }
      }
      
      return updatedClassLevels;
    });
  }

  // Bulk delete
  static async bulkDelete(ids: string[]) {
    return await db.transaction(async (tx) => {
      const deletedClassLevels = [];
      
      for (const id of ids) {
        const [deleted] = await tx
          .delete(class_levels)
          .where(eq(class_levels.id, id))
          .returning();
        
        if (deleted) {
          deletedClassLevels.push(deleted);
        }
      }
      
      return deletedClassLevels;
    });
  }
}
