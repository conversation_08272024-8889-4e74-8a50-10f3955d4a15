name: Test

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    name: Test on Node.js ${{ matrix.node-version }}
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [18, 20]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run linting
        run: npm run lint

      - name: Run type checking
        run: npx tsc --noEmit

      - name: Setup test database
        run: |
          npm run db:generate
          npm run db:migrate
        env:
          DATABASE_URL: "./test.db"

      - name: Run tests
        run: echo "Tests will be implemented later"
        env:
          CI: true
          DATABASE_URL: "./test.db"
          NEXTAUTH_SECRET: "test-secret"
          NEXTAUTH_URL: "http://localhost:3000"
          AUTH_SECRET: "test-secret"

      - name: Run build test
        run: npm run build
        env:
          NODE_ENV: production
          DATABASE_URL: "./test.db"
          NEXTAUTH_SECRET: "test-secret"
          NEXTAUTH_URL: "http://localhost:3000"
          AUTH_SECRET: "test-secret"
          STRIPE_SECRET_KEY: "sk_test_fake"
          STRIPE_PUBLISHABLE_KEY: "pk_test_fake"
          STRIPE_WEBHOOK_SECRET: "whsec_fake"

      - name: Upload coverage reports
        if: matrix.node-version == 18
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella
