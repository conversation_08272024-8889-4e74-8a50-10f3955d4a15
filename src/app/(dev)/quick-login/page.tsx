"use client";

import { useState } from "react";
import { signIn, getSession } from "next-auth/react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useRouter } from "next/navigation";

export default function QuickLoginPage() {
  const [email, setEmail] = useState("<EMAIL>");
  const [password, setPassword] = useState("password");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const router = useRouter();

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");

    try {
      console.log("🔐 Attempting login with:", { email, password });
      
      const result = await signIn("credentials", {
        email,
        password,
        redirect: false,
      });

      console.log("🔐 Login result:", result);

      if (result?.error) {
        setError("Invalid credentials");
      } else if (result?.ok) {
        console.log("✅ Login successful, redirecting...");
        
        // Wait a bit for session to be established
        setTimeout(async () => {
          const session = await getSession();
          console.log("📋 Session after login:", session);
          router.push("/role-management");
        }, 1000);
      }
    } catch (error) {
      console.error("❌ Login error:", error);
      setError("Login failed");
    } finally {
      setIsLoading(false);
    }
  };

  const testRoleAPI = async () => {
    try {
      console.log("🧪 Testing role API...");
      const response = await fetch("/api/test-roles");
      const data = await response.json();
      console.log("📊 Role API response:", data);
      alert(`Found ${data.data?.totalRoles || 0} roles in database`);
    } catch (error) {
      console.error("❌ Role API test failed:", error);
      alert("Role API test failed");
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-center">Quick Login for Testing</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <form onSubmit={handleLogin} className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Email</label>
                <Input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="<EMAIL>"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Password</label>
                <Input
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  placeholder="password"
                />
              </div>

              {error && (
                <div className="text-red-500 text-sm">{error}</div>
              )}

              <Button 
                type="submit" 
                className="w-full" 
                disabled={isLoading}
              >
                {isLoading ? "Logging in..." : "Login as Admin"}
              </Button>
            </form>

            <div className="border-t pt-4">
              <Button 
                variant="outline" 
                className="w-full" 
                onClick={testRoleAPI}
              >
                Test Role API (No Auth)
              </Button>
            </div>

            <div className="text-xs text-gray-500 space-y-1">
              <p><strong>Default Credentials:</strong></p>
              <p>Email: <EMAIL></p>
              <p>Password: password</p>
              <p>This will give you superadmin access to role management.</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
