version: '3.8'

services:
  app:
    build:
      context: .
      target: development  # Target specific stage
      cache_from:
        - node:18-alpine
        - ${COMPOSE_PROJECT_NAME:-nextjs-app}:deps-dev
    environment:
      - NODE_ENV=development
      - NEXT_TELEMETRY_DISABLED=1
      # Development-specific env vars
      - WATCHPACK_POLLING=true
      - CHOKIDAR_USEPOLLING=true
    ports:
      - "3000:3000"
      - "9229:9229"  # Debugging port
    volumes:
      # Hot reload volumes
      - .:/app
      - /app/node_modules
      - /app/.next
    # Docker Compose Watch for hot reload
    develop:
      watch:
        - action: sync
          path: ./src
          target: /app/src
        - action: sync
          path: ./public
          target: /app/public
        - action: rebuild
          path: package.json
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 512M

  # Development database tools
  db-admin:
    image: dpage/pgadmin4:latest
    environment:
      - PGADMIN_DEFAULT_EMAIL=${PGADMIN_EMAIL:-admin@localhost}
      - PGADMIN_DEFAULT_PASSWORD=${PGADMIN_PASSWORD:-admin}
      - PGADMIN_CONFIG_SERVER_MODE=False
    ports:
      - "5050:80"
    networks:
      - postgres-network
    volumes:
      - pgadmin-data:/var/lib/pgadmin
    restart: unless-stopped

  # Redis for development caching and sessions
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    networks:
      - app-network
    volumes:
      - redis-data:/data
    restart: unless-stopped
    command: redis-server --appendonly yes

  # Development nginx with simpler config
  nginx:
    volumes:
      - ./nginx/dev.conf:/etc/nginx/nginx.conf:ro
    ports:
      - "80:80"

volumes:
  pgadmin-data:
    driver: local
  redis-data:
    driver: local
