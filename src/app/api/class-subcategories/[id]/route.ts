import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { ClassSubcategoryService } from "@/lib/services/class-subcategory.service";

// Validation schemas
const paramsSchema = z.object({
  id: z.string().min(1, "ID is required"),
});

const querySchema = z.object({
  tenantId: z.string().transform((val) => parseInt(val, 10)),
});

const updateClassSubcategorySchema = z.object({
  name: z.string().min(1, "Name is required").max(255, "Name too long"),
});

/**
 * GET /api/class-subcategories/[id]
 * Get a single class subcategory by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Validate params
    const validatedParams = paramsSchema.parse(params);

    // Get subcategory by ID
    const subcategory = await ClassSubcategoryService.getById(validatedParams.id);

    if (!subcategory) {
      return NextResponse.json(
        { success: false, error: "Class subcategory not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: subcategory,
    });
  } catch (error) {
    console.error("GET /api/class-subcategories/[id] error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: "Invalid parameters", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/class-subcategories/[id]
 * Update a class subcategory
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Validate params
    const validatedParams = paramsSchema.parse(params);
    
    // Validate query parameters
    const { searchParams } = new URL(request.url);
    const validatedQuery = querySchema.parse({
      tenantId: searchParams.get("tenantId"),
    });

    // Validate request body
    const body = await request.json();
    const validatedData = updateClassSubcategorySchema.parse(body);

    // Update subcategory
    const updatedSubcategory = await ClassSubcategoryService.update(
      validatedParams.id,
      validatedData
    );

    if (!updatedSubcategory) {
      return NextResponse.json(
        { success: false, error: "Class subcategory not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: updatedSubcategory,
      message: "Class subcategory updated successfully",
    });
  } catch (error) {
    console.error("PUT /api/class-subcategories/[id] error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/class-subcategories/[id]
 * Delete a class subcategory
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Validate params
    const validatedParams = paramsSchema.parse(params);
    
    // Validate query parameters
    const { searchParams } = new URL(request.url);
    const validatedQuery = querySchema.parse({
      tenantId: searchParams.get("tenantId"),
    });

    // Delete subcategory
    await ClassSubcategoryService.delete(validatedParams.id);

    return NextResponse.json({
      success: true,
      message: "Class subcategory deleted successfully",
    });
  } catch (error) {
    console.error("DELETE /api/class-subcategories/[id] error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error && error.message.includes("not found")) {
      return NextResponse.json(
        { success: false, error: "Class subcategory not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}
