"use client";

import { useState } from "react";
import { Plus, Edit, Trash2, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Off } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/components/ui/alert-dialog";
import { toast } from "sonner";
import { CustomerAddressForm } from "./customer-address-form";
import { 
  useCustomerAddresses, 
  useDeleteCustomerAddress, 
  useSetPrimaryAddress 
} from "@/lib/hooks/queries/use-customer-address-queries";
import { CustomerAddress } from "@/lib/db/schema";

interface CustomerAddressManagerProps {
  customerId: string;
  className?: string;
}

export function CustomerAddressManager({ customerId, className }: CustomerAddressManagerProps) {
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [editingAddress, setEditingAddress] = useState<CustomerAddress | null>(null);

  // Queries and mutations
  const { data: addresses = [], isLoading } = useCustomerAddresses(customerId);
  const deleteAddressMutation = useDeleteCustomerAddress();
  const setPrimaryMutation = useSetPrimaryAddress();

  const handleAddSuccess = () => {
    setIsAddDialogOpen(false);
    toast.success("Address added successfully");
  };

  const handleEditSuccess = () => {
    setEditingAddress(null);
    toast.success("Address updated successfully");
  };

  const handleDelete = async (addressId: string) => {
    try {
      await deleteAddressMutation.mutateAsync({ customerId, addressId });
      toast.success("Address deleted successfully");
    } catch (error) {
      toast.error("Failed to delete address");
    }
  };

  const handleSetPrimary = async (addressId: string) => {
    try {
      await setPrimaryMutation.mutateAsync({ customerId, addressId });
      toast.success("Primary address updated successfully");
    } catch (error) {
      toast.error("Failed to set primary address");
    }
  };

  const formatAddress = (address: CustomerAddress) => {
    const parts = [
      address.addressLine1,
      address.addressLine2,
      address.city,
      address.state,
      address.zip,
      address.country,
    ].filter(Boolean);
    
    return parts.join(", ");
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5" />
            Addresses
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center p-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              Addresses
            </CardTitle>
            <CardDescription>
              Manage customer addresses
            </CardDescription>
          </div>
          <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Address
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>Add New Address</DialogTitle>
                <DialogDescription>
                  Add a new address for this customer
                </DialogDescription>
              </DialogHeader>
              <CustomerAddressForm
                customerId={customerId}
                onSuccess={handleAddSuccess}
                onCancel={() => setIsAddDialogOpen(false)}
              />
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        {addresses.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            <MapPin className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No addresses found</p>
            <p className="text-sm">Add an address to get started</p>
          </div>
        ) : (
          <div className="space-y-4">
            {addresses.map((address) => (
              <div
                key={address.id}
                className="flex items-start justify-between p-4 border rounded-lg"
              >
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    {address.is_primary && (
                      <Badge variant="default" className="text-xs">
                        Primary
                      </Badge>
                    )}
                  </div>
                  <p className="text-sm font-medium">
                    {formatAddress(address)}
                  </p>
                </div>
                <div className="flex items-center gap-2">
                  {!address.is_primary && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleSetPrimary(address.id)}
                      disabled={setPrimaryMutation.isPending}
                    >
                      <Star className="h-4 w-4" />
                    </Button>
                  )}
                  <Dialog 
                    open={editingAddress?.id === address.id} 
                    onOpenChange={(open) => !open && setEditingAddress(null)}
                  >
                    <DialogTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setEditingAddress(address)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="max-w-2xl">
                      <DialogHeader>
                        <DialogTitle>Edit Address</DialogTitle>
                        <DialogDescription>
                          Update the address information
                        </DialogDescription>
                      </DialogHeader>
                      <CustomerAddressForm
                        customerId={customerId}
                        address={editingAddress}
                        onSuccess={handleEditSuccess}
                        onCancel={() => setEditingAddress(null)}
                      />
                    </DialogContent>
                  </Dialog>
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Delete Address</AlertDialogTitle>
                        <AlertDialogDescription>
                          Are you sure you want to delete this address? This action cannot be undone.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={() => handleDelete(address.id)}
                          disabled={deleteAddressMutation.isPending}
                        >
                          Delete
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
