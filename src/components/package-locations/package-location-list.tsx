"use client";

import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Package, MapPin, Trash2, Plus, Search, Filter } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog";
import { PackageLocationWithDetails } from "@/lib/services/package-location.service";
import { useDeletePackageLocation } from "@/lib/hooks/queries/use-package-location-queries";
import { usePackageList } from "@/lib/hooks/queries/use-package-queries";
import { useLocations } from "@/lib/hooks/queries/use-location-queries";
import { toast } from "sonner";

interface PackageLocationListProps {
  packageLocations: PackageLocationWithDetails[];
  onAdd?: () => void;
  showActions?: boolean;
  tenantId: number;
}

export function PackageLocationList({
  packageLocations,
  onAdd,
  showActions = true,
  tenantId,
}: PackageLocationListProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [packageFilter, setPackageFilter] = useState<string>("all");
  const [locationFilter, setLocationFilter] = useState<string>("all");
  const [deleteConfirmation, setDeleteConfirmation] = useState<{
    isOpen: boolean;
    packageLocation: PackageLocationWithDetails | null;
  }>({ isOpen: false, packageLocation: null });

  // Fetch data for filters
  const { data: packages = [] } = usePackageList({}, tenantId);
  const { data: locations = [] } = useLocations({ tenantId });

  // Mutations
  const deletePackageLocationMutation = useDeletePackageLocation();

  // Filter package locations
  const filteredPackageLocations = packageLocations.filter((pl) => {
    const matchesSearch = !searchQuery || 
      pl.package?.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      pl.location?.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      pl.location?.address?.toLowerCase().includes(searchQuery.toLowerCase());

    const matchesPackage = packageFilter === "all" || pl.package_id === packageFilter;
    const matchesLocation = locationFilter === "all" || pl.location_id === locationFilter;

    return matchesSearch && matchesPackage && matchesLocation;
  });

  const handleDeleteClick = (packageLocation: PackageLocationWithDetails) => {
    setDeleteConfirmation({ isOpen: true, packageLocation });
  };

  const handleDeleteConfirm = async () => {
    if (!deleteConfirmation.packageLocation) return;

    try {
      await deletePackageLocationMutation.mutateAsync({
        packageId: deleteConfirmation.packageLocation.package_id,
        locationId: deleteConfirmation.packageLocation.location_id,
      });
      setDeleteConfirmation({ isOpen: false, packageLocation: null });
      toast.success("Package location assignment removed successfully!");
    } catch (error) {
      console.error("Failed to remove package location:", error);
      toast.error("Failed to remove package location assignment");
    }
  };

  const clearFilters = () => {
    setSearchQuery("");
    setPackageFilter("all");
    setLocationFilter("all");
  };

  const hasActiveFilters = searchQuery || packageFilter !== "all" || locationFilter !== "all";

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                Package Location Assignments
              </CardTitle>
              <CardDescription>
                Manage which packages are available at which locations
              </CardDescription>
            </div>
            {showActions && onAdd && (
              <Button onClick={onAdd}>
                <Plus className="h-4 w-4 mr-2" />
                Assign Package
              </Button>
            )}
          </div>

          {/* Filters */}
          <div className="grid gap-4 md:grid-cols-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search packages or locations..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Package Filter */}
            <Select value={packageFilter} onValueChange={setPackageFilter}>
              <SelectTrigger>
                <SelectValue placeholder="All packages" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All packages</SelectItem>
                {packages.map((pkg) => (
                  <SelectItem key={pkg.id} value={pkg.id}>
                    {pkg.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Location Filter */}
            <Select value={locationFilter} onValueChange={setLocationFilter}>
              <SelectTrigger>
                <SelectValue placeholder="All locations" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All locations</SelectItem>
                {locations.map((location) => (
                  <SelectItem key={location.id} value={location.id}>
                    {location.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Clear Filters */}
            <Button
              variant="outline"
              onClick={clearFilters}
              disabled={!hasActiveFilters}
            >
              <Filter className="h-4 w-4 mr-2" />
              Clear Filters
            </Button>
          </div>
        </CardHeader>

        <CardContent>
          {filteredPackageLocations.length === 0 ? (
            <div className="text-center py-12">
              <Package className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-semibold text-gray-900">No package locations</h3>
              <p className="mt-1 text-sm text-gray-500">
                {hasActiveFilters 
                  ? "No package locations match your current filters."
                  : "Get started by assigning packages to locations."
                }
              </p>
              {showActions && onAdd && !hasActiveFilters && (
                <div className="mt-6">
                  <Button onClick={onAdd}>
                    <Plus className="h-4 w-4 mr-2" />
                    Assign Package
                  </Button>
                </div>
              )}
            </div>
          ) : (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              <AnimatePresence>
                {filteredPackageLocations.map((packageLocation) => (
                  <motion.div
                    key={`${packageLocation.package_id}-${packageLocation.location_id}`}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Card className="hover:shadow-md transition-shadow">
                      <CardContent className="p-4">
                        <div className="space-y-3">
                          {/* Package Info */}
                          <div className="flex items-start gap-2">
                            <Package className="h-4 w-4 text-blue-500 mt-0.5" />
                            <div className="flex-1 min-w-0">
                              <div className="font-medium truncate">
                                {packageLocation.package?.name || "Unknown Package"}
                              </div>
                              {packageLocation.package?.description && (
                                <div className="text-sm text-muted-foreground truncate">
                                  {packageLocation.package.description}
                                </div>
                              )}
                            </div>
                          </div>

                          {/* Location Info */}
                          <div className="flex items-start gap-2">
                            <MapPin className="h-4 w-4 text-green-500 mt-0.5" />
                            <div className="flex-1 min-w-0">
                              <div className="font-medium truncate">
                                {packageLocation.location?.name || "Unknown Location"}
                              </div>
                              {packageLocation.location?.address && (
                                <div className="text-sm text-muted-foreground truncate">
                                  {packageLocation.location.address}
                                </div>
                              )}
                            </div>
                          </div>

                          {/* Metadata */}
                          <div className="flex items-center justify-between text-xs text-muted-foreground">
                            <span>
                              Created: {new Date(packageLocation.createdAt).toLocaleDateString()}
                            </span>
                            {showActions && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleDeleteClick(packageLocation)}
                                className="h-auto p-1 text-red-500 hover:text-red-700"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </AnimatePresence>
            </div>
          )}

          {/* Results Summary */}
          {filteredPackageLocations.length > 0 && (
            <div className="mt-4 text-sm text-muted-foreground text-center">
              Showing {filteredPackageLocations.length} of {packageLocations.length} package location assignments
            </div>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog 
        open={deleteConfirmation.isOpen} 
        onOpenChange={(open) => !open && setDeleteConfirmation({ isOpen: false, packageLocation: null })}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Remove Package Location Assignment</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to remove the assignment of "
              {deleteConfirmation.packageLocation?.package?.name}" from "
              {deleteConfirmation.packageLocation?.location?.name}"? 
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleDeleteConfirm}>
              Remove Assignment
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
