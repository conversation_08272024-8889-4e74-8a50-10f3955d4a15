import { NextRequest, NextResponse } from "next/server";
import { VoucherService } from "@/lib/services/voucher.service";
import { z } from "zod";
import { withRBAC } from "@/lib/middleware/rbac-middleware";

// Validation schema for updating vouchers
const updateVoucherSchema = z.object({
  code: z.string().min(1).max(50).optional(),
  name: z.string().min(1).max(255).optional(),
  description: z.string().max(1000).optional(),
  type: z.enum(["percentage", "fixed_amount", "free_shipping", "buy_x_get_y"]).optional(),
  value: z.number().positive().optional(),
  currency: z.string().length(3).optional(),
  usage_limit: z.number().int().positive().optional(),
  usage_limit_per_customer: z.number().int().positive().optional(),
  valid_from: z.string().transform((str) => new Date(str)).optional(),
  valid_until: z.string().transform((str) => new Date(str)).optional(),
  is_active: z.boolean().optional(),
  is_public: z.boolean().optional(),
  auto_apply: z.boolean().optional(),
  restrictions: z.object({
    min_purchase_amount: z.number().positive().optional(),
    max_discount_amount: z.number().positive().optional(),
    applicable_locations: z.array(z.string()).optional(),
    applicable_packages: z.array(z.string()).optional(),
    applicable_classes: z.array(z.string()).optional(),
    applicable_customer_groups: z.array(z.string()).optional(),
    first_time_customers_only: z.boolean().optional(),
    existing_customers_only: z.boolean().optional(),
  }).optional(),
});

interface RouteParams {
  params: {
    id: string;
  };
}

// GET /api/vouchers/[id] - Get single voucher
export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  return withRBAC(
    async () => {
      try {
        const { id } = params;

        if (!id) {
          return NextResponse.json(
            { error: "Voucher ID is required" },
            { status: 400 }
          );
        }

        const voucher = await VoucherService.getById(id);

        if (!voucher) {
          return NextResponse.json(
            { error: "Voucher not found" },
            { status: 404 }
          );
        }

        return NextResponse.json({
          success: true,
          data: voucher,
        });
      } catch (error) {
        console.error("Error fetching voucher:", error);
        return NextResponse.json(
          { 
            error: error instanceof Error ? error.message : "Failed to fetch voucher",
            success: false 
          },
          { status: 500 }
        );
      }
    },
    { module: "vouchers", action: "read" }
  )(request);
}

// PUT /api/vouchers/[id] - Update voucher
export async function PUT(
  request: NextRequest,
  { params }: RouteParams
) {
  return withRBAC(
    async () => {
      try {
        const { id } = params;
        const body = await request.json();

        if (!id) {
          return NextResponse.json(
            { error: "Voucher ID is required" },
            { status: 400 }
          );
        }

        // Validate request body
        const validatedData = updateVoucherSchema.parse(body);

        // Check if voucher exists
        const existingVoucher = await VoucherService.getById(id);
        if (!existingVoucher) {
          return NextResponse.json(
            { error: "Voucher not found" },
            { status: 404 }
          );
        }

        // Update voucher
        const updatedVoucher = await VoucherService.update(id, validatedData);

        return NextResponse.json({
          success: true,
          data: updatedVoucher,
          message: "Voucher updated successfully",
        });
      } catch (error) {
        console.error("Error updating voucher:", error);
        
        if (error instanceof z.ZodError) {
          return NextResponse.json(
            { 
              error: "Validation failed",
              details: error.errors,
              success: false 
            },
            { status: 400 }
          );
        }

        return NextResponse.json(
          { 
            error: error instanceof Error ? error.message : "Failed to update voucher",
            success: false 
          },
          { status: 500 }
        );
      }
    },
    { module: "vouchers", action: "update" }
  )(request);
}

// DELETE /api/vouchers/[id] - Delete voucher
export async function DELETE(
  request: NextRequest,
  { params }: RouteParams
) {
  return withRBAC(
    async () => {
      try {
        const { id } = params;

        if (!id) {
          return NextResponse.json(
            { error: "Voucher ID is required" },
            { status: 400 }
          );
        }

        // Check if voucher exists
        const existingVoucher = await VoucherService.getById(id);
        if (!existingVoucher) {
          return NextResponse.json(
            { error: "Voucher not found" },
            { status: 404 }
          );
        }

        // Check if voucher has been used
        const stats = await VoucherService.getVoucherStats(id);
        if (stats.total_usage > 0) {
          return NextResponse.json(
            { 
              error: "Cannot delete voucher that has been used. Consider deactivating it instead.",
              success: false 
            },
            { status: 400 }
          );
        }

        // Delete voucher
        const deletedVoucher = await VoucherService.delete(id);

        return NextResponse.json({
          success: true,
          data: deletedVoucher,
          message: "Voucher deleted successfully",
        });
      } catch (error) {
        console.error("Error deleting voucher:", error);
        return NextResponse.json(
          { 
            error: error instanceof Error ? error.message : "Failed to delete voucher",
            success: false 
          },
          { status: 500 }
        );
      }
    },
    { module: "vouchers", action: "delete" }
  )(request);
}

// PATCH /api/vouchers/[id] - Toggle voucher active status
export async function PATCH(
  request: NextRequest,
  { params }: RouteParams
) {
  return withRBAC(
    async () => {
      try {
        const { id } = params;
        const { searchParams } = new URL(request.url);
        const action = searchParams.get("action");

        if (!id) {
          return NextResponse.json(
            { error: "Voucher ID is required" },
            { status: 400 }
          );
        }

        // Check if voucher exists
        const existingVoucher = await VoucherService.getById(id);
        if (!existingVoucher) {
          return NextResponse.json(
            { error: "Voucher not found" },
            { status: 404 }
          );
        }

        let updatedVoucher;

        switch (action) {
          case "toggle-active":
            updatedVoucher = await VoucherService.update(id, {
              is_active: !existingVoucher.is_active,
            });
            break;

          default:
            return NextResponse.json(
              { error: "Invalid action" },
              { status: 400 }
            );
        }

        return NextResponse.json({
          success: true,
          data: updatedVoucher,
          message: `Voucher ${updatedVoucher.is_active ? 'activated' : 'deactivated'} successfully`,
        });
      } catch (error) {
        console.error("Error updating voucher:", error);
        return NextResponse.json(
          { 
            error: error instanceof Error ? error.message : "Failed to update voucher",
            success: false 
          },
          { status: 500 }
        );
      }
    },
    { module: "vouchers", action: "update" }
  )(request);
}
