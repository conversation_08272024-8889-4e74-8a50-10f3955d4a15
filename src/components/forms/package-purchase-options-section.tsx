"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import { z } from "zod";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { AlertCircle, ShoppingCart, Users, MapPin, BookOpen, Globe, ArrowRightLeft, Loader2, Target, UserPlus } from "lucide-react";
import { useLocations } from "@/lib/hooks/queries/use-location-queries";
import { useCustomerSegmentsForSelect } from "@/lib/hooks/queries/use-customer-segments-queries";
import { IndividualCustomerSelector } from "./individual-customer-selector";

// Package Purchase Options validation schema
export const packagePurchaseOptionsSchema = z.object({
  purchaseLimit: z.number()
    .int("Purchase limit must be a whole number")
    .min(0, "Purchase limit cannot be negative")
    .max(1000000, "Purchase limit cannot exceed 1,000,000")
    .optional(),
  restrictTo: z.string()
    .max(255, "Restrict to value cannot exceed 255 characters")
    .optional(),
  customerSegmentIds: z.array(z.string()).optional(), // Array of customer segment IDs
  individualCustomerIds: z.array(z.string()).optional(), // Array of individual customer IDs
  transferable: z.boolean().default(false),
  specifySoldAtLocation: z.boolean().default(false),
  soldAtLocationId: z.string()
    .min(1, "Location ID cannot be empty")
    .optional()
    .or(z.literal("")),
  classBookingLimit: z.number()
    .int("Class booking limit must be a whole number")
    .min(0, "Class booking limit cannot be negative")
    .max(10000, "Class booking limit cannot exceed 10,000")
    .optional(),
  showOnline: z.boolean().default(false),
}).refine((data) => {
  // If specifySoldAtLocation is true, soldAtLocationId must be provided
  if (data.specifySoldAtLocation && (!data.soldAtLocationId || data.soldAtLocationId === "")) {
    return false;
  }
  return true;
}, {
  message: "Location must be selected when 'Location Specific' is enabled",
  path: ["soldAtLocationId"],
});

export type PackagePurchaseOptionsData = z.infer<typeof packagePurchaseOptionsSchema>;

interface PackagePurchaseOptionsSectionProps {
  form: any; // TanStack form instance
  tenantId?: number;
  className?: string;
}

// Customer segments will be loaded dynamically from the API

export function PackagePurchaseOptionsSection({
  form,
  tenantId = 1,
  className = ""
}: PackagePurchaseOptionsSectionProps) {

  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  // Get locations for dropdown - tenant-aware fetching
  const {
    data: locationsData,
    isLoading: locationsLoading,
    error: locationsError
  } = useLocations({
    tenantId: tenantId // Pastikan kita fetch locations untuk tenant yang benar
  });

  // Get customer segments for dropdown - tenant-aware fetching
  const {
    segments: customerSegments,
    loading: segmentsLoading,
    error: segmentsError
  } = useCustomerSegmentsForSelect(tenantId);

  // Extract locations array dengan fallback
  const locations = locationsData || [];

  // Handle form validation errors
  const handleFieldError = (fieldName: string, error: string | null) => {
    setFormErrors(prev => {
      if (error) {
        return { ...prev, [fieldName]: error };
      } else {
        const { [fieldName]: _, ...rest } = prev;
        return rest;
      }
    });
  };

  return (
    <motion.div
      className={`space-y-4 ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3, delay: 0.1 }}
    >
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3, delay: 0.2 }}
      >
        <h3 className="text-lg font-medium">Purchase Options</h3>
        <p className="text-sm text-muted-foreground">
          Configure how customers can purchase and use this package
        </p>
      </motion.div>

      <motion.div
        className="grid grid-cols-1 md:grid-cols-2 gap-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3, delay: 0.3 }}
      >
        {/* Purchase Limit */}
        <form.Field
          name="purchaseOptions.purchaseLimit"
          validators={{
            onChange: (value: number | undefined) => {
              // Allow undefined (empty input)
              if (value === undefined || value === null) {
                return undefined;
              }

              // Check if it's a valid number
              // if (typeof value !== 'number' || isNaN(value)) {
              //   return 'Purchase limit must be a valid number';
              // }

              // Check if it's a whole number
              // if (!Number.isInteger(value)) {
              //   return 'Purchase limit must be a whole number';
              // }

              // Check range
              if (value < 0) {
                return 'Purchase limit cannot be negative';
              }
              if (value > 1000000) {
                return 'Purchase limit cannot exceed 1,000,000';
              }

              return undefined;
            },
          }}
          children={(field: any) => (
            <div className="space-y-2">
              <Label htmlFor={field.name} className="flex items-center gap-2">
                <ShoppingCart className="h-4 w-4" />
                Purchase Limit
              </Label>
              <Input
                id={field.name}
                name={field.name}
                type="number"
                min="0"
                max="1000000"
                value={field.state.value || ""}
                onBlur={field.handleBlur}
                onChange={(e) => {
                  const value = e.target.value.trim();

                  // Handle empty input
                  if (value === "") {
                    field.handleChange(undefined);
                    handleFieldError(field.name, null);
                    return;
                  }

                  // Parse and validate number
                  const numValue = parseInt(value, 10);
                  if (!isNaN(numValue)) {
                    field.handleChange(numValue);
                  }
                  handleFieldError(field.name, null);
                }}
                placeholder="Enter purchase limit (leave empty for unlimited)"
              />
              {field.state.meta.errors.length > 0 && (
                <div className="flex items-center gap-2 text-sm text-destructive">
                  <AlertCircle className="h-4 w-4" />
                  {field.state.meta.errors[0]}
                </div>
              )}
              <p className="text-xs text-muted-foreground">
                Maximum number of customers who can purchase this package (max: 1,000,000)
              </p>
            </div>
          )}
        />

        {/* Restrict To */}
        <form.Field
          name="purchaseOptions.restrictTo"
          children={(field: any) => (
            <div className="space-y-2">
              <Label htmlFor={field.name} className="flex items-center gap-2">
                <Target className="h-4 w-4" />
                Customer Targeting
                {segmentsLoading && <Loader2 className="h-3 w-3 animate-spin" />}
              </Label>
              <Select
                value={field.state.value || "all_customers"}
                onValueChange={(value) => {
                  field.handleChange(value);
                  handleFieldError(field.name, null);
                }}
                disabled={segmentsLoading}
              >
                <SelectTrigger>
                  <SelectValue
                    placeholder={
                      segmentsLoading
                        ? "Loading customer segments..."
                        : segmentsError
                        ? "Error loading segments"
                        : "Select customer targeting"
                    }
                  />
                </SelectTrigger>
                <SelectContent>
                  {/* Built-in segments */}
                  {customerSegments
                    .filter(segment => ['all', 'members', 'new_customers', 'existing_customers'].includes(segment.type))
                    .map((segment) => (
                      <SelectItem key={segment.value} value={segment.value}>
                        <div className="flex items-center justify-between w-full">
                          <div className="flex flex-col">
                            <span className="font-medium">{segment.label}</span>
                            <span className="text-xs text-muted-foreground">
                              {segment.description}
                            </span>
                          </div>
                          <span className="text-xs bg-muted px-2 py-1 rounded ml-2">
                            {segment.customerCount} customers
                          </span>
                        </div>
                      </SelectItem>
                    ))}

                  {/* Pricing group segments */}
                  {customerSegments.filter(segment => segment.type === 'pricing_group').length > 0 && (
                    <>
                      <div className="px-2 py-1 text-xs font-semibold text-muted-foreground border-t">
                        Pricing Groups
                      </div>
                      {customerSegments
                        .filter(segment => segment.type === 'pricing_group')
                        .map((segment) => (
                          <SelectItem key={segment.value} value={segment.value}>
                            <div className="flex items-center justify-between w-full">
                              <div className="flex flex-col">
                                <span className="font-medium">{segment.label}</span>
                                <span className="text-xs text-muted-foreground">
                                  {segment.description}
                                </span>
                              </div>
                              <span className="text-xs bg-muted px-2 py-1 rounded ml-2">
                                {segment.customerCount} customers
                              </span>
                            </div>
                          </SelectItem>
                        ))}
                    </>
                  )}

                  {/* Location segments */}
                  {customerSegments.filter(segment => segment.type === 'location').length > 0 && (
                    <>
                      <div className="px-2 py-1 text-xs font-semibold text-muted-foreground border-t">
                        Location-based
                      </div>
                      {customerSegments
                        .filter(segment => segment.type === 'location')
                        .map((segment) => (
                          <SelectItem key={segment.value} value={segment.value}>
                            <div className="flex items-center justify-between w-full">
                              <div className="flex flex-col">
                                <span className="font-medium">{segment.label}</span>
                                <span className="text-xs text-muted-foreground">
                                  {segment.description}
                                </span>
                              </div>
                              <span className="text-xs bg-muted px-2 py-1 rounded ml-2">
                                {segment.customerCount} customers
                              </span>
                            </div>
                          </SelectItem>
                        ))}
                    </>
                  )}
                </SelectContent>
              </Select>
              {segmentsError && (
                <div className="flex items-center gap-2 text-sm text-destructive">
                  <AlertCircle className="h-4 w-4" />
                  Failed to load customer segments
                </div>
              )}
              <p className="text-xs text-muted-foreground">
                Choose which customers can purchase this package. Segments are automatically updated based on customer data.
              </p>
            </div>
          )}
        />

        {/* Class Booking Limit */}
        <form.Field
          name="purchaseOptions.classBookingLimit"
          validators={{
            onChange: (value: number | undefined) => {
              // Allow undefined (empty input)
              if (value === undefined || value === null) {
                return undefined;
              }

              // Check if it's a valid number
              // if (typeof value !== 'number' || isNaN(value)) {
              //   return 'Class booking limit must be a valid number';
              // }

              // // Check if it's a whole number
              // if (!Number.isInteger(value)) {
              //   return 'Class booking limit must be a whole number';
              // }

              // Check range
              if (value < 0) {
                return 'Class booking limit cannot be negative';
              }
              if (value > 10000) {
                return 'Class booking limit cannot exceed 10,000';
              }

              return undefined;
            },
          }}
          children={(field: any) => (
            <div className="space-y-2">
              <Label htmlFor={field.name} className="flex items-center gap-2">
                <BookOpen className="h-4 w-4" />
                Class Booking Limit
              </Label>
              <Input
                id={field.name}
                name={field.name}
                type="number"
                min="0"
                max="10000"
                value={field.state.value || ""}
                onBlur={field.handleBlur}
                onChange={(e) => {
                  const value = e.target.value.trim();

                  // Handle empty input
                  if (value === "") {
                    field.handleChange(undefined);
                    handleFieldError(field.name, null);
                    return;
                  }

                  // Parse and validate number
                  const numValue = parseInt(value, 10);
                  if (!isNaN(numValue)) {
                    field.handleChange(numValue);
                  }
                  handleFieldError(field.name, null);
                }}
                placeholder="Enter class booking limit (leave empty for unlimited)"
              />
              {field.state.meta.errors.length > 0 && (
                <div className="flex items-center gap-2 text-sm text-destructive">
                  <AlertCircle className="h-4 w-4" />
                  {field.state.meta.errors[0]}
                </div>
              )}
              <p className="text-xs text-muted-foreground">
                Maximum number of classes that can be booked with this package (max: 10,000)
              </p>
            </div>
          )}
        />

        {/* Sold At Location */}
        <form.Field
          name="purchaseOptions.soldAtLocationId"
          children={(field: any) => (
            <div className="space-y-2">
              <Label htmlFor={field.name} className="flex items-center gap-2">
                <MapPin className="h-4 w-4" />
                Sold At Location
                {locationsLoading && <Loader2 className="h-3 w-3 animate-spin" />}
              </Label>
              <Select
                value={field.state.value || "all_locations"}
                onValueChange={(value) => {
                  field.handleChange(value);
                  handleFieldError(field.name, null);
                }}
                disabled={locationsLoading}
              >
                <SelectTrigger>
                  <SelectValue
                    placeholder={
                      locationsLoading
                        ? "Loading locations..."
                        : locationsError
                        ? "Error loading locations"
                        : "Select location (optional)"
                    }
                  />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all_locations">
                    <div className="flex items-center gap-2">
                      <Globe className="h-4 w-4" />
                      <span>All Locations</span>
                    </div>
                  </SelectItem>
                  {locations.map((location) => (
                    <SelectItem key={location.id} value={location.id}>
                      <div className="flex flex-col">
                        <span className="font-medium">{location.name}</span>
                        {location.city && (
                          <span className="text-xs text-muted-foreground">
                            {location.city}{location.state ? `, ${location.state}` : ''}
                          </span>
                        )}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {locationsError && (
                <div className="flex items-center gap-2 text-sm text-destructive">
                  <AlertCircle className="h-4 w-4" />
                  Failed to load locations
                </div>
              )}
              <p className="text-xs text-muted-foreground">
                Choose "All Locations" to sell at any location within your organization, or select a specific location to restrict sales
              </p>
            </div>
          )}
        />
      </motion.div>

      {/* Boolean Options */}
      <motion.div
        className="grid grid-cols-1 md:grid-cols-3 gap-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3, delay: 0.4 }}
      >
        {/* Transferable */}
        <form.Field
          name="purchaseOptions.transferable"
          children={(field: any) => (
            <div className="space-y-2">
              <Label className="flex items-center gap-2">
                <ArrowRightLeft className="h-4 w-4" />
                Transferable
              </Label>
              <div className="flex items-center space-x-2">
                <Switch
                  checked={field.state.value || false}
                  onCheckedChange={field.handleChange}
                />
                <span className="text-sm">
                  {field.state.value ? "Yes" : "No"}
                </span>
              </div>
              <p className="text-xs text-muted-foreground">
                Can customers transfer this package to others?
              </p>
            </div>
          )}
        />

        {/* Specify Sold At Location */}
        <form.Field
          name="purchaseOptions.specifySoldAtLocation"
          children={(field: any) => (
            <div className="space-y-2">
              <Label className="flex items-center gap-2">
                <MapPin className="h-4 w-4" />
                Location Specific
              </Label>
              <div className="flex items-center space-x-2">
                <Switch
                  checked={field.state.value || false}
                  onCheckedChange={field.handleChange}
                />
                <span className="text-sm">
                  {field.state.value ? "Yes" : "No"}
                </span>
              </div>
              <p className="text-xs text-muted-foreground">
                Must be sold at a specific location?
              </p>
            </div>
          )}
        />

        {/* Show Online */}
        <form.Field
          name="purchaseOptions.showOnline"
          children={(field: any) => (
            <div className="space-y-2">
              <Label className="flex items-center gap-2">
                <Globe className="h-4 w-4" />
                Show Online
              </Label>
              <div className="flex items-center space-x-2">
                <Switch
                  checked={field.state.value || false}
                  onCheckedChange={field.handleChange}
                />
                <span className="text-sm">
                  {field.state.value ? "Yes" : "No"}
                </span>
              </div>
              <p className="text-xs text-muted-foreground">
                Display this package on website/app?
              </p>
            </div>
          )}
        />
      </motion.div>

      {/* Individual Customer Selection */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3, delay: 0.5 }}
        className="border-t pt-6"
      >
        <form.Field
          name="purchaseOptions.individualCustomerIds"
          children={(field: any) => (
            <IndividualCustomerSelector
              tenantId={tenantId}
              selectedCustomerIds={field.state.value || []}
              onSelectionChange={(customerIds) => {
                field.handleChange(customerIds);
                handleFieldError(field.name, null);
              }}
              maxSelections={100}
              placeholder="Search and select specific customers..."
              disabled={false}
            />
          )}
        />
      </motion.div>
    </motion.div>
  );
}
