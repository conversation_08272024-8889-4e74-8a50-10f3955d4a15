import { db } from "@/lib/db";
import { eq, desc, asc, sql, SQL } from "drizzle-orm";
import { PgTable } from "drizzle-orm/pg-core";
import { createId } from "@paralleldrive/cuid2";

// Generic interfaces for all entities
export interface BaseEntity {
  id: string;
  tenantId?: number | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateEntityData {
  tenantId?: number;
  [key: string]: any;
}

export interface UpdateEntityData {
  [key: string]: any;
}

export interface QueryOptions {
  tenantId?: number;
  limit?: number;
  offset?: number;
  orderBy?: 'asc' | 'desc';
  orderField?: string;
  filters?: Record<string, any>;
}

export interface BulkOperationData {
  ids: string[];
  action: 'activate' | 'deactivate' | 'delete' | 'archive';
  data?: Record<string, any>;
}

export interface EntityStats {
  total: number;
  active?: number;
  inactive?: number;
  archived?: number;
  byTenant?: Record<number, number>;
}

// Generic base service class
export abstract class BaseService<T extends BaseEntity, C extends CreateEntityData, U extends UpdateEntityData> {
  protected table: PgTable;
  protected entityName: string;

  constructor(table: PgTable, entityName: string) {
    this.table = table;
    this.entityName = entityName;
  }

  // Generic CRUD operations
  async getAll(options: QueryOptions = {}): Promise<T[]> {
    let query = db.select().from(this.table);

    // Apply tenant filter
    if (options.tenantId) {
      query = query.where(eq(this.table.tenantId, options.tenantId));
    }

    // Apply additional filters
    if (options.filters) {
      const conditions = this.buildFilterConditions(options.filters);
      if (conditions.length > 0) {
        query = query.where(sql`${conditions.join(' AND ')}`);
      }
    }

    // Apply ordering
    const orderField = options.orderField || 'createdAt';
    const orderDirection = options.orderBy === 'asc' ? asc : desc;
    query = query.orderBy(orderDirection(this.table[orderField]));

    // Apply pagination
    if (options.limit) {
      query = query.limit(options.limit);
    }
    if (options.offset) {
      query = query.offset(options.offset);
    }

    return query as Promise<T[]>;
  }

  async getById(id: string): Promise<T | null> {
    const [result] = await db
      .select()
      .from(this.table)
      .where(eq(this.table.id, id));
    
    return (result as T) || null;
  }

  async getByTenantId(tenantId: number, options: Omit<QueryOptions, 'tenantId'> = {}): Promise<T[]> {
    return this.getAll({ ...options, tenantId });
  }

  async create(data: C): Promise<T> {
    const id = createId();
    const now = new Date();
    
    const [newEntity] = await db
      .insert(this.table)
      .values({
        id,
        ...data,
        createdAt: now,
        updatedAt: now,
      })
      .returning();

    return newEntity as T;
  }

  async update(id: string, data: U): Promise<T> {
    const [updatedEntity] = await db
      .update(this.table)
      .set({
        ...data,
        updatedAt: new Date(),
      })
      .where(eq(this.table.id, id))
      .returning();

    if (!updatedEntity) {
      throw new Error(`${this.entityName} not found`);
    }

    return updatedEntity as T;
  }

  async delete(id: string): Promise<T> {
    const [deletedEntity] = await db
      .delete(this.table)
      .where(eq(this.table.id, id))
      .returning();

    if (!deletedEntity) {
      throw new Error(`${this.entityName} not found`);
    }

    return deletedEntity as T;
  }

  // Bulk operations
  async bulkUpdate(ids: string[], data: Partial<U>): Promise<T[]> {
    return await db.transaction(async (tx) => {
      const updatedEntities = [];
      
      for (const id of ids) {
        const [updated] = await tx
          .update(this.table)
          .set({
            ...data,
            updatedAt: new Date(),
          })
          .where(eq(this.table.id, id))
          .returning();
        
        if (updated) {
          updatedEntities.push(updated as T);
        }
      }
      
      return updatedEntities;
    });
  }

  async bulkDelete(ids: string[]): Promise<T[]> {
    return await db.transaction(async (tx) => {
      const deletedEntities = [];
      
      for (const id of ids) {
        const [deleted] = await tx
          .delete(this.table)
          .where(eq(this.table.id, id))
          .returning();
        
        if (deleted) {
          deletedEntities.push(deleted as T);
        }
      }
      
      return deletedEntities;
    });
  }

  // Statistics
  async getStats(tenantId?: number): Promise<EntityStats> {
    let whereClause = sql`1=1`;
    if (tenantId) {
      whereClause = sql`${this.table.tenantId} = ${tenantId}`;
    }

    const [totalCount] = await db
      .select({ count: sql<number>`count(*)` })
      .from(this.table)
      .where(whereClause);

    return {
      total: totalCount.count,
    };
  }

  // Search functionality
  async search(query: string, fields: string[], options: QueryOptions = {}): Promise<T[]> {
    // Use getAll with search filter instead of raw SQL
    return this.getAll({
      ...options,
      filters: {
        ...options.filters,
        search: query
      }
    });
  }

  // Abstract methods to be implemented by subclasses
  protected abstract buildFilterConditions(filters: Record<string, any>): SQL[];

  // Optional: Custom validation
  protected validateCreateData(data: C): void {
    // Override in subclasses for custom validation
  }

  protected validateUpdateData(data: U): void {
    // Override in subclasses for custom validation
  }

  // Add db property for subclasses
  protected get db() {
    return db;
  }
}
