import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { z } from "zod";
import { packageService } from "@/lib/services/package.service";

// Validation schema for updates
const updatePackageSchema = z.object({
  tenantId: z.number().int().positive("Tenant ID must be positive").optional(),
  name: z.string().min(1, "Package name is required").max(255, "Package name too long").optional(),
  description: z.string().optional(),
  isActive: z.boolean().optional(),
  is_private: z.boolean().optional(),
  validity_date: z.string().optional(),
  validity_duration: z.number().int().min(0, "Validity duration cannot be negative").optional(),
  schedule_availability: z.array(z.object({
    id: z.string(),
    start_date: z.string().optional(),
    start_time: z.string().optional(),
    end_date: z.string().optional(),
    end_time: z.string().optional(),
  })).optional(),
  included_classes: z.array(z.object({
    id: z.string(),
    name: z.string(),
    category: z.string().optional(),
    included_at: z.string(),
  })).optional(),
});

/**
 * GET /api/packages/[id]
 * Get a specific package by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    console.log(`📦 [GET /api/packages/${id}] Fetching package`);

    const pkg = await packageService.getById(id);

    if (!pkg) {
      return NextResponse.json({ error: "Package not found" }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: pkg
    });
  } catch (error) {
    console.error(`📦 [GET /api/packages/${params}] Error:`, error);
    return NextResponse.json(
      { error: "Failed to fetch package" },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/packages/[id]
 * Update a specific package
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    const body = await request.json();
    const validatedData = updatePackageSchema.parse(body);

    console.log(`📦 [PUT /api/packages/${id}] Updating package:`, validatedData);

    // Check if the package exists
    const existingPackage = await packageService.getById(id);
    if (!existingPackage) {
      return NextResponse.json({ error: "Package not found" }, { status: 404 });
    }

    const updatedPackage = await packageService.update(id, validatedData);

    return NextResponse.json({
      success: true,
      data: updatedPackage,
      message: "Package updated successfully"
    });
  } catch (error) {
    console.error(`📦 [PUT /api/packages/${params}] Error:`, error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid package data", details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error && error.message.includes("already exists")) {
      return NextResponse.json(
        { error: error.message },
        { status: 409 }
      );
    }

    return NextResponse.json(
      { error: "Failed to update package" },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/packages/[id]
 * Delete a specific package
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    console.log(`📦 [DELETE /api/packages/${id}] Deleting package`);

    // Check if the package exists
    const existingPackage = await packageService.getById(id);
    if (!existingPackage) {
      return NextResponse.json({ error: "Package not found" }, { status: 404 });
    }

    const deletedPackage = await packageService.delete(id);

    return NextResponse.json({
      success: true,
      data: deletedPackage,
      message: "Package deleted successfully"
    });
  } catch (error) {
    console.error(`📦 [DELETE /api/packages/${params}] Error:`, error);
    return NextResponse.json(
      { error: "Failed to delete package" },
      { status: 500 }
    );
  }
}
