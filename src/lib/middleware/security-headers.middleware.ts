import { NextRequest, NextResponse } from "next/server";

/**
 * Enterprise-Grade Security Headers Middleware
 * 
 * Implements comprehensive security headers following OWASP recommendations
 * and FAANG-level security standards for SaaS applications.
 */

export interface SecurityHeadersConfig {
  contentSecurityPolicy?: string;
  strictTransportSecurity?: string;
  xFrameOptions?: string;
  xContentTypeOptions?: string;
  referrerPolicy?: string;
  permissionsPolicy?: string;
  crossOriginEmbedderPolicy?: string;
  crossOriginOpenerPolicy?: string;
  crossOriginResourcePolicy?: string;
}

const DEFAULT_SECURITY_CONFIG: Required<SecurityHeadersConfig> = {
  // Content Security Policy - Prevents XSS attacks
  contentSecurityPolicy: `
    default-src 'self';
    script-src 'self' 'unsafe-eval' 'unsafe-inline' https://js.stripe.com https://challenges.cloudflare.com;
    style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;
    img-src 'self' blob: data: https: *.gravatar.com *.githubusercontent.com;
    font-src 'self' https://fonts.gstatic.com;
    connect-src 'self' https://api.stripe.com https://challenges.cloudflare.com wss: ws:;
    media-src 'self' blob: data:;
    object-src 'none';
    base-uri 'self';
    form-action 'self';
    frame-ancestors 'none';
    upgrade-insecure-requests;
  `.replace(/\s+/g, ' ').trim(),

  // HSTS - Forces HTTPS connections
  strictTransportSecurity: "max-age=31536000; includeSubDomains; preload",

  // Prevents clickjacking attacks
  xFrameOptions: "DENY",

  // Prevents MIME type sniffing
  xContentTypeOptions: "nosniff",

  // Controls referrer information
  referrerPolicy: "strict-origin-when-cross-origin",

  // Controls browser features and APIs
  permissionsPolicy: `
    camera=(),
    microphone=(),
    geolocation=(),
    payment=(),
    usb=(),
    magnetometer=(),
    gyroscope=(),
    accelerometer=(),
    ambient-light-sensor=(),
    autoplay=(),
    encrypted-media=(),
    fullscreen=(self),
    picture-in-picture=()
  `.replace(/\s+/g, ' ').trim(),

  // Cross-Origin Embedder Policy
  crossOriginEmbedderPolicy: "require-corp",

  // Cross-Origin Opener Policy
  crossOriginOpenerPolicy: "same-origin",

  // Cross-Origin Resource Policy
  crossOriginResourcePolicy: "same-origin",
};

/**
 * Apply security headers to response
 */
export function applySecurityHeaders(
  response: NextResponse,
  config: Partial<SecurityHeadersConfig> = {}
): NextResponse {
  const finalConfig = { ...DEFAULT_SECURITY_CONFIG, ...config };

  // Apply all security headers
  response.headers.set("Content-Security-Policy", finalConfig.contentSecurityPolicy);
  response.headers.set("Strict-Transport-Security", finalConfig.strictTransportSecurity);
  response.headers.set("X-Frame-Options", finalConfig.xFrameOptions);
  response.headers.set("X-Content-Type-Options", finalConfig.xContentTypeOptions);
  response.headers.set("Referrer-Policy", finalConfig.referrerPolicy);
  response.headers.set("Permissions-Policy", finalConfig.permissionsPolicy);
  response.headers.set("Cross-Origin-Embedder-Policy", finalConfig.crossOriginEmbedderPolicy);
  response.headers.set("Cross-Origin-Opener-Policy", finalConfig.crossOriginOpenerPolicy);
  response.headers.set("Cross-Origin-Resource-Policy", finalConfig.crossOriginResourcePolicy);

  // Additional security headers
  response.headers.set("X-DNS-Prefetch-Control", "off");
  response.headers.set("X-Download-Options", "noopen");
  response.headers.set("X-Permitted-Cross-Domain-Policies", "none");
  response.headers.set("X-XSS-Protection", "1; mode=block");

  // Remove potentially sensitive headers
  response.headers.delete("Server");
  response.headers.delete("X-Powered-By");

  return response;
}

/**
 * Security headers middleware for API routes
 */
export function withSecurityHeaders(
  handler: (request: NextRequest, context?: any) => Promise<NextResponse>,
  config: Partial<SecurityHeadersConfig> = {}
) {
  return async (request: NextRequest, context?: any) => {
    try {
      // Execute the handler
      const response = await handler(request, context);

      // Apply security headers
      return applySecurityHeaders(response, config);
    } catch (error) {
      console.error("Security headers middleware error:", error);
      
      // Create error response with security headers
      const errorResponse = NextResponse.json(
        { 
          success: false,
          error: "Internal server error",
          code: "SECURITY_MIDDLEWARE_ERROR"
        },
        { status: 500 }
      );

      return applySecurityHeaders(errorResponse, config);
    }
  };
}

/**
 * Development-specific security headers (more permissive)
 */
export const DEV_SECURITY_CONFIG: Partial<SecurityHeadersConfig> = {
  contentSecurityPolicy: `
    default-src 'self';
    script-src 'self' 'unsafe-eval' 'unsafe-inline' https://js.stripe.com https://challenges.cloudflare.com;
    style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;
    img-src 'self' blob: data: https: *.gravatar.com *.githubusercontent.com;
    font-src 'self' https://fonts.gstatic.com;
    connect-src 'self' https://api.stripe.com https://challenges.cloudflare.com wss: ws: http://localhost:* http://127.0.0.1:*;
    media-src 'self' blob: data:;
    object-src 'none';
    base-uri 'self';
    form-action 'self';
    frame-ancestors 'none';
  `.replace(/\s+/g, ' ').trim(),
  
  // Less strict HSTS for development
  strictTransportSecurity: "max-age=0",
};

/**
 * Production-specific security headers (most restrictive)
 */
export const PROD_SECURITY_CONFIG: Partial<SecurityHeadersConfig> = {
  contentSecurityPolicy: `
    default-src 'self';
    script-src 'self' https://js.stripe.com https://challenges.cloudflare.com;
    style-src 'self' https://fonts.googleapis.com;
    img-src 'self' blob: data: https: *.gravatar.com;
    font-src 'self' https://fonts.gstatic.com;
    connect-src 'self' https://api.stripe.com https://challenges.cloudflare.com;
    media-src 'self';
    object-src 'none';
    base-uri 'self';
    form-action 'self';
    frame-ancestors 'none';
    upgrade-insecure-requests;
  `.replace(/\s+/g, ' ').trim(),
  
  strictTransportSecurity: "max-age=63072000; includeSubDomains; preload",
};

/**
 * Get environment-specific security config
 */
export function getSecurityConfig(): Partial<SecurityHeadersConfig> {
  return process.env.NODE_ENV === "production" 
    ? PROD_SECURITY_CONFIG 
    : DEV_SECURITY_CONFIG;
}

/**
 * Utility to check if request needs security headers
 */
export function shouldApplySecurityHeaders(request: NextRequest): boolean {
  const pathname = request.nextUrl.pathname;
  
  // Skip for static files and certain API routes
  const skipPatterns = [
    /\/_next\//,
    /\/favicon\.ico$/,
    /\.(png|jpg|jpeg|gif|svg|ico|webp)$/,
    /\/api\/webhooks\//,
    /\/api\/public\//
  ];

  return !skipPatterns.some(pattern => pattern.test(pathname));
}

/**
 * Export utilities
 */
export const SecurityHeadersUtils = {
  applySecurityHeaders,
  withSecurityHeaders,
  getSecurityConfig,
  shouldApplySecurityHeaders,
  DEFAULT_SECURITY_CONFIG,
  DEV_SECURITY_CONFIG,
  PROD_SECURITY_CONFIG,
};
