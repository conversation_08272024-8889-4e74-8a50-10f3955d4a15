import { sql, eq, and, ilike, inArray, SQL } from "drizzle-orm";
import { 
  customers, 
  package_customer_segements,
  pricing_groups,
  locations,
  type Customer,
  type NewPackageCustomerSegement
} from "@/lib/db/schema";
import { BaseService, CreateEntityData, UpdateEntityData, QueryOptions } from "@/lib/core/base-service";
import { db } from "@/lib/db";

// Individual Customer Selection types
export interface CustomerSearchResult {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber?: string;
  isActive: boolean;
  pricingGroupId?: string;
  pricingGroupName?: string;
  locationId?: string;
  locationName?: string;
  membershipStatus: 'active' | 'inactive' | 'suspended';
  joinDate: Date;
  tenantId: number;
}

export interface CustomerSearchFilters {
  tenantId: number;
  search?: string; // Search by name or email
  isActive?: boolean;
  pricingGroupId?: string;
  locationId?: string;
  limit?: number;
  offset?: number;
}

export interface IndividualCustomerTargeting {
  packageId: string;
  selectedCustomerIds: string[];
  tenantId: number;
}

export interface PackageCustomerTargetingStats {
  packageId: string;
  segmentTargeting?: string; // Segment ID if using segment targeting
  individualCustomerCount: number;
  totalTargetedCustomers: number;
  tenantId: number;
}

class IndividualCustomerSelectionService {
  
  /**
   * Search customers for selection with advanced filtering
   */
  async searchCustomers(filters: CustomerSearchFilters): Promise<{
    customers: CustomerSearchResult[];
    total: number;
    hasMore: boolean;
  }> {
    try {
      console.log(`👤 [IndividualCustomerSelectionService] Searching customers:`, filters);

      const { tenantId, search, isActive, pricingGroupId, locationId, limit = 20, offset = 0 } = filters;

    // Build where conditions
    const conditions: SQL[] = [eq(customers.tenantId, tenantId)];

    // Search by name or email
    if (search && search.trim()) {
      const searchTerm = `%${search.trim().toLowerCase()}%`;
      conditions.push(
        sql`(
          LOWER(${customers.firstName}) LIKE ${searchTerm} OR 
          LOWER(${customers.lastName}) LIKE ${searchTerm} OR 
          LOWER(${customers.email}) LIKE ${searchTerm}
        )`
      );
    }

    // Filter by active status
    if (isActive !== undefined) {
      conditions.push(eq(customers.isActive, isActive));
    }

    // Filter by pricing group
    if (pricingGroupId) {
      conditions.push(eq(customers.pricingGroupId, pricingGroupId));
    }

    // Filter by location
    if (locationId) {
      conditions.push(eq(customers.locationId, locationId));
    }

    // Get total count
    console.log(`👤 [IndividualCustomerSelectionService] Getting total count with conditions:`, conditions.length);
    const [totalResult] = await db
      .select({ count: sql<number>`count(*)` })
      .from(customers)
      .where(and(...conditions));

    const total = totalResult.count;
    console.log(`👤 [IndividualCustomerSelectionService] Total count:`, total);

    // Get customers with related data
    const customerResults = await db
      .select({
        // Customer fields
        id: customers.id,
        firstName: customers.firstName,
        lastName: customers.lastName,
        email: customers.email,
        mobileNumber: customers.mobileNumber, // Use correct field name
        isActive: customers.isActive,
        pricingGroupId: customers.pricingGroupId,
        locationId: customers.locationId,
        createdAt: customers.createdAt,
        tenantId: customers.tenantId,
        // Pricing group info
        pricingGroupName: pricing_groups.name,
        // Location info
        locationName: locations.name,
      })
      .from(customers)
      .leftJoin(pricing_groups, eq(customers.pricingGroupId, pricing_groups.id))
      .leftJoin(locations, eq(customers.locationId, locations.id))
      .where(and(...conditions))
      .orderBy(customers.firstName, customers.lastName)
      .limit(limit)
      .offset(offset);

    console.log(`👤 [IndividualCustomerSelectionService] Raw customer results:`, customerResults.length);
    console.log(`👤 [IndividualCustomerSelectionService] First customer sample:`, customerResults[0]);

    // Transform to CustomerSearchResult
    const transformedCustomers: CustomerSearchResult[] = customerResults.map(customer => ({
      id: customer.id,
      firstName: customer.firstName,
      lastName: customer.lastName,
      email: customer.email,
      phoneNumber: customer.mobileNumber || undefined, // Use correct field name
      isActive: customer.isActive,
      pricingGroupId: customer.pricingGroupId || undefined,
      pricingGroupName: customer.pricingGroupName || undefined,
      locationId: customer.locationId || undefined,
      locationName: customer.locationName || undefined,
      membershipStatus: customer.isActive ? 'active' : 'inactive',
      joinDate: customer.createdAt,
      tenantId: customer.tenantId,
    }));

    const hasMore = offset + limit < total;

      console.log(`✅ [IndividualCustomerSelectionService] Found ${transformedCustomers.length} customers (${total} total)`);

      return {
        customers: transformedCustomers,
        total,
        hasMore,
      };
    } catch (error) {
      console.error(`❌ [IndividualCustomerSelectionService] Error searching customers:`, error);
      console.error(`❌ [IndividualCustomerSelectionService] Filters that caused error:`, filters);
      throw error;
    }
  }

  /**
   * Get customers by IDs (for displaying selected customers)
   */
  async getCustomersByIds(customerIds: string[], tenantId: number): Promise<CustomerSearchResult[]> {
    if (customerIds.length === 0) return [];

    console.log(`👤 [IndividualCustomerSelectionService] Getting customers by IDs:`, customerIds);

    const customerResults = await db
      .select({
        // Customer fields
        id: customers.id,
        firstName: customers.firstName,
        lastName: customers.lastName,
        email: customers.email,
        mobileNumber: customers.mobileNumber, // Use correct field name
        isActive: customers.isActive,
        pricingGroupId: customers.pricingGroupId,
        locationId: customers.locationId,
        createdAt: customers.createdAt,
        tenantId: customers.tenantId,
        // Pricing group info
        pricingGroupName: pricing_groups.name,
        // Location info
        locationName: locations.name,
      })
      .from(customers)
      .leftJoin(pricing_groups, eq(customers.pricingGroupId, pricing_groups.id))
      .leftJoin(locations, eq(customers.locationId, locations.id))
      .where(and(
        inArray(customers.id, customerIds),
        eq(customers.tenantId, tenantId) // Ensure tenant isolation
      ))
      .orderBy(customers.firstName, customers.lastName);

    // Transform to CustomerSearchResult
    const transformedCustomers: CustomerSearchResult[] = customerResults.map(customer => ({
      id: customer.id,
      firstName: customer.firstName,
      lastName: customer.lastName,
      email: customer.email,
      phoneNumber: customer.mobileNumber || undefined, // Use correct field name
      isActive: customer.isActive,
      pricingGroupId: customer.pricingGroupId || undefined,
      pricingGroupName: customer.pricingGroupName || undefined,
      locationId: customer.locationId || undefined,
      locationName: customer.locationName || undefined,
      membershipStatus: customer.isActive ? 'active' : 'inactive',
      joinDate: customer.createdAt,
      tenantId: customer.tenantId,
    }));

    console.log(`✅ [IndividualCustomerSelectionService] Retrieved ${transformedCustomers.length} customers`);
    return transformedCustomers;
  }

  /**
   * Save individual customer targeting for a package
   */
  async saveIndividualCustomerTargeting(data: IndividualCustomerTargeting): Promise<void> {
    console.log(`👤 [IndividualCustomerSelectionService] Saving individual customer targeting:`, data);

    const { packageId, selectedCustomerIds, tenantId } = data;

    // Validate that all selected customers belong to the tenant
    if (selectedCustomerIds.length > 0) {
      const customerCheck = await db
        .select({ id: customers.id })
        .from(customers)
        .where(and(
          inArray(customers.id, selectedCustomerIds),
          eq(customers.tenantId, tenantId)
        ));

      if (customerCheck.length !== selectedCustomerIds.length) {
        throw new Error("Some selected customers do not belong to the current tenant");
      }
    }

    // Delete existing individual customer targeting for this package
    await db
      .delete(package_customer_segements)
      .where(eq(package_customer_segements.package_id, packageId));

    // Insert new individual customer targeting
    if (selectedCustomerIds.length > 0) {
      const insertData: NewPackageCustomerSegement[] = selectedCustomerIds.map(customerId => ({
        package_id: packageId,
        customer_id: customerId,
      }));

      await db.insert(package_customer_segements).values(insertData);
    }

    console.log(`✅ [IndividualCustomerSelectionService] Saved targeting for ${selectedCustomerIds.length} customers`);
  }

  /**
   * Get individual customer targeting for a package
   */
  async getIndividualCustomerTargeting(packageId: string, tenantId: number): Promise<CustomerSearchResult[]> {
    console.log(`👤 [IndividualCustomerSelectionService] Getting individual customer targeting for package:`, packageId);

    const targetedCustomers = await db
      .select({
        // Customer fields
        id: customers.id,
        firstName: customers.firstName,
        lastName: customers.lastName,
        email: customers.email,
        mobileNumber: customers.mobileNumber, // Use correct field name
        isActive: customers.isActive,
        pricingGroupId: customers.pricingGroupId,
        locationId: customers.locationId,
        createdAt: customers.createdAt,
        tenantId: customers.tenantId,
        // Pricing group info
        pricingGroupName: pricing_groups.name,
        // Location info
        locationName: locations.name,
      })
      .from(package_customer_segements)
      .innerJoin(customers, eq(package_customer_segements.customer_id, customers.id))
      .leftJoin(pricing_groups, eq(customers.pricingGroupId, pricing_groups.id))
      .leftJoin(locations, eq(customers.locationId, locations.id))
      .where(and(
        eq(package_customer_segements.package_id, packageId),
        eq(customers.tenantId, tenantId) // Ensure tenant isolation
      ))
      .orderBy(customers.firstName, customers.lastName);

    // Transform to CustomerSearchResult
    const transformedCustomers: CustomerSearchResult[] = targetedCustomers.map(customer => ({
      id: customer.id,
      firstName: customer.firstName,
      lastName: customer.lastName,
      email: customer.email,
      phoneNumber: customer.mobileNumber || undefined, // Use correct field name
      isActive: customer.isActive,
      pricingGroupId: customer.pricingGroupId || undefined,
      pricingGroupName: customer.pricingGroupName || undefined,
      locationId: customer.locationId || undefined,
      locationName: customer.locationName || undefined,
      membershipStatus: customer.isActive ? 'active' : 'inactive',
      joinDate: customer.createdAt,
      tenantId: customer.tenantId,
    }));

    console.log(`✅ [IndividualCustomerSelectionService] Found ${transformedCustomers.length} individually targeted customers`);
    return transformedCustomers;
  }

  /**
   * Get package customer targeting statistics
   */
  async getPackageTargetingStats(packageId: string, tenantId: number): Promise<PackageCustomerTargetingStats> {
    console.log(`👤 [IndividualCustomerSelectionService] Getting targeting stats for package:`, packageId);

    // Count individual customer targeting
    const [individualCount] = await db
      .select({ count: sql<number>`count(*)` })
      .from(package_customer_segements)
      .innerJoin(customers, eq(package_customer_segements.customer_id, customers.id))
      .where(and(
        eq(package_customer_segements.package_id, packageId),
        eq(customers.tenantId, tenantId)
      ));

    return {
      packageId,
      individualCustomerCount: individualCount.count,
      totalTargetedCustomers: individualCount.count, // Will be enhanced with segment targeting
      tenantId,
    };
  }
}

// Export singleton instance
export const individualCustomerSelectionService = new IndividualCustomerSelectionService();
