import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { z } from "zod";
import { FacilityService } from "@/lib/services/facility.service";

// Validation schema for creating facility
const createFacilitySchema = z.object({
  tenantId: z.number().int().positive("Tenant ID must be positive"),
  name: z.string().min(1, "Name is required").max(255, "Name too long"),
  description: z.string().max(500, "Description too long").optional(),
  isActive: z.boolean().optional(),
  images: z.array(z.string()).optional(), // Allow any string, not just URLs
});

const updateFacilitySchema = createFacilitySchema.partial().omit({ tenantId: true });

// GET /api/facilities - Get facilities
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get("tenantId");
    const search = searchParams.get("search");
    const activeOnly = searchParams.get("activeOnly");

    let facilities;

    // If tenantId is provided, validate and filter by tenant
    if (tenantId && tenantId !== "null" && tenantId !== "undefined") {
      const tenantIdNum = parseInt(tenantId);
      if (isNaN(tenantIdNum) || tenantIdNum <= 0) {
        return NextResponse.json(
          { error: "tenantId must be a valid positive number" },
          { status: 400 }
        );
      }

      if (search) {
        facilities = await FacilityService.searchFacilities(search, tenantIdNum);
      } else if (activeOnly === "true") {
        facilities = await FacilityService.getActiveFacilitiesByTenant(tenantIdNum);
      } else {
        facilities = await FacilityService.getByTenantId(tenantIdNum);
      }
    } else {
      // No tenantId provided, return all facilities (admin only)
      if (session.user.role !== "admin") {
        return NextResponse.json(
          { error: "Forbidden: Admin access required" },
          { status: 403 }
        );
      }
      
      if (search) {
        facilities = await FacilityService.searchFacilities(search);
      } else {
        facilities = await FacilityService.getAll();
      }
    }

    return NextResponse.json({ facilities });
  } catch (error) {
    console.error("Error fetching facilities:", error);
    return NextResponse.json(
      { error: "Failed to fetch facilities" },
      { status: 500 }
    );
  }
}

// POST /api/facilities - Create facility
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = createFacilitySchema.parse(body);

    // Check if user has permission to create facility for this tenant
    if (session.user.role !== "admin") {
      // Add tenant ownership check here if needed
      // For now, we'll allow any authenticated user to create facilities
    }

    // Validate facility data
    const validationErrors = FacilityService.validateFacilityData(validatedData);
    if (validationErrors.length > 0) {
      return NextResponse.json(
        { error: "Validation error", details: validationErrors },
        { status: 400 }
      );
    }

    const facility = await FacilityService.create(validatedData);

    return NextResponse.json({ facility }, { status: 201 });
  } catch (error) {
    console.error("Error creating facility:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to create facility" },
      { status: 500 }
    );
  }
}

// PUT /api/facilities/bulk - Bulk operations
export async function PUT(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { action, ids, data } = body;

    if (!action || !Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json(
        { error: "Action and ids array are required" },
        { status: 400 }
      );
    }

    let result;

    switch (action) {
      case "activate":
        result = await FacilityService.bulkUpdateStatus(ids, true);
        break;
      case "deactivate":
        result = await FacilityService.bulkUpdateStatus(ids, false);
        break;
      default:
        return NextResponse.json(
          { error: "Invalid action. Supported actions: activate, deactivate" },
          { status: 400 }
        );
    }

    return NextResponse.json({ 
      message: `Facilities ${action}d successfully`,
      facilities: result 
    });
  } catch (error) {
    console.error("Error in bulk operation:", error);
    return NextResponse.json(
      { error: "Failed to perform bulk operation" },
      { status: 500 }
    );
  }
}

// POST /api/facilities/bulk - Bulk create facilities
export async function POST_BULK(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { facilities: facilitiesData } = body;

    if (!Array.isArray(facilitiesData) || facilitiesData.length === 0) {
      return NextResponse.json(
        { error: "facilities array is required and cannot be empty" },
        { status: 400 }
      );
    }

    // Validate each facility
    const validatedFacilities = facilitiesData.map(facility => 
      createFacilitySchema.parse(facility)
    );

    // Check permissions for each tenant
    if (session.user.role !== "admin") {
      // Add tenant ownership checks here if needed
    }

    const facilities = await FacilityService.bulkCreate(validatedFacilities);

    return NextResponse.json({ facilities }, { status: 201 });
  } catch (error) {
    console.error("Error bulk creating facilities:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to bulk create facilities" },
      { status: 500 }
    );
  }
}
