import { db } from "@/lib/db";
import { tags, type Tag, type NewTag } from "@/lib/db/schema";
import { eq, and, desc, asc, ilike } from "drizzle-orm";
import { createId } from "@paralleldrive/cuid2";
import { BaseService } from "@/lib/core/base-service";

// Tag data transfer objects
export interface TagData {
  name?: string;
  description?: string;
  customColor?: string;
  customerId?: string;
}

export interface TagFilters {
  search?: string;
  customerId?: string;
}

/**
 * TagService - Simple tag management for customers
 *
 * Features:
 * - CRUD operations for tags
 * - Search and filtering
 * - Customer-specific tags
 */
export class TagService extends BaseService<Tag, TagData> {
  constructor() {
    super(tags, "Tag");
  }

  /**
   * Create a new tag
   */
  async create(tenantId: number, data: TagData): Promise<Tag> {
    console.log(`🏷️ [TagService] Creating tag for tenant ${tenantId}:`, data);

    const tagData: NewTag = {
      id: createId(),
      tenantId,
      customerId: data.customerId || null,
      name: data.name || null,
      description: data.description || null,
      custom_color: data.customColor || null,
    };

    const [tag] = await db.insert(tags).values(tagData).returning();
    console.log(`✅ [TagService] Tag created:`, tag);
    return tag;
  }

  /**
   * Update tag
   */
  async update(id: string, data: Partial<TagData>): Promise<Tag> {
    console.log(`🏷️ [TagService] Updating tag ${id}:`, data);

    const updateData: Partial<NewTag> = {};
    if (data.name !== undefined) updateData.name = data.name;
    if (data.description !== undefined) updateData.description = data.description;
    if (data.customColor !== undefined) updateData.custom_color = data.customColor;
    if (data.customerId !== undefined) updateData.customerId = data.customerId;

    const [updatedTag] = await db
      .update(tags)
      .set(updateData)
      .where(eq(tags.id, id))
      .returning();

    console.log(`✅ [TagService] Tag updated:`, updatedTag);
    return updatedTag;
  }

  /**
   * Get tags by tenant with filters
   */
  async getByTenant(tenantId: number, filters: TagFilters = {}): Promise<Tag[]> {
    console.log(`🏷️ [TagService] Getting tags for tenant ${tenantId}:`, filters);

    // Apply filters
    const conditions = [eq(tags.tenantId, tenantId)];

    if (filters.search) {
      conditions.push(ilike(tags.name, `%${filters.search}%`));
    }

    if (filters.customerId) {
      conditions.push(eq(tags.customerId, filters.customerId));
    }

    const result = await db
      .select()
      .from(tags)
      .where(and(...conditions))
      .orderBy(asc(tags.name));

    console.log(`✅ [TagService] Found ${result.length} tags`);
    return result;
  }

  /**
   * Get tags by customer
   */
  async getByCustomer(customerId: string): Promise<Tag[]> {
    console.log(`🏷️ [TagService] Getting tags for customer ${customerId}`);

    const result = await db
      .select()
      .from(tags)
      .where(eq(tags.customerId, customerId))
      .orderBy(asc(tags.name));

    console.log(`✅ [TagService] Found ${result.length} tags for customer`);
    return result;
  }
}

// Export singleton instance
export const tagService = new TagService();
