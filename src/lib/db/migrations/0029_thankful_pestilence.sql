CREATE INDEX "idx_class_schedules_time_overlap" ON "class_schedules" USING btree ("tenant_id","start_time","end_time");--> statement-breakpoint
CREATE INDEX "idx_class_schedules_location_time" ON "class_schedules" USING btree ("tenant_id","location_id","start_time","end_time");--> statement-breakpoint
CREATE INDEX "idx_class_schedules_facility_time" ON "class_schedules" USING btree ("tenant_id","facility_id","start_time","end_time");--> statement-breakpoint
CREATE INDEX "idx_class_schedules_staff_time" ON "class_schedules" USING btree ("staff_id","start_time","end_time");--> statement-breakpoint
CREATE INDEX "idx_class_schedules_recurring" ON "class_schedules" USING btree ("tenant_id","repeat_rule","start_date","end_date");--> statement-breakpoint
ALTER TABLE "class_schedules" ADD CONSTRAINT "unique_schedule_time_location" UNIQUE("tenant_id","start_time","end_time","location_id");--> statement-breakpoint
ALTER TABLE "class_schedules" ADD CONSTRAINT "unique_schedule_time_facility" UNIQUE("tenant_id","start_time","end_time","facility_id");--> statement-breakpoint
ALTER TABLE "class_schedules" ADD CONSTRAINT "unique_staff_time_slot" UNIQUE("staff_id","start_time","end_time");--> statement-breakpoint
ALTER TABLE "class_schedules" ADD CONSTRAINT "check_start_before_end" CHECK ("class_schedules"."start_time" < "class_schedules"."end_time");--> statement-breakpoint
ALTER TABLE "class_schedules" ADD CONSTRAINT "check_positive_duration" CHECK ("class_schedules"."duration" > 0);--> statement-breakpoint
ALTER TABLE "class_schedules" ADD CONSTRAINT "check_positive_capacity" CHECK ("class_schedules"."pax" > 0 AND "class_schedules"."waitlist" >= 0);--> statement-breakpoint
ALTER TABLE "class_schedules" ADD CONSTRAINT "check_valid_repeat_rule" CHECK ("class_schedules"."repeat_rule" IN ('none', 'daily', 'weekly', 'monthly'));--> statement-breakpoint
ALTER TABLE "class_schedules" ADD CONSTRAINT "check_booking_window_order" CHECK ("class_schedules"."booking_window_start" IS NULL OR "class_schedules"."booking_window_end" IS NULL OR "class_schedules"."booking_window_start" <= "class_schedules"."booking_window_end");--> statement-breakpoint
ALTER TABLE "class_schedules" ADD CONSTRAINT "check_checkin_window_order" CHECK ("class_schedules"."check_in_window_start" IS NULL OR "class_schedules"."check_in_window_end" IS NULL OR "class_schedules"."check_in_window_start" <= "class_schedules"."check_in_window_end");