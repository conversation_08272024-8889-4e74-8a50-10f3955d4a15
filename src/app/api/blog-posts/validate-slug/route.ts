import { NextRequest, NextResponse } from "next/server";
import { BlogService } from "@/lib/services/blog.service";
import { withRBAC } from "@/lib/middleware/rbac-middleware";

// GET /api/blog-posts/validate-slug - Check if slug is available
export async function GET(request: NextRequest) {
  return withRBAC(
    async () => {
      try {
        const { searchParams } = new URL(request.url);
        const tenantId = searchParams.get("tenantId");
        const slug = searchParams.get("slug");
        const excludeId = searchParams.get("excludeId");

        if (!tenantId || !slug) {
          return NextResponse.json(
            { error: "Tenant ID and slug are required" },
            { status: 400 }
          );
        }

        const isAvailable = await BlogService.isSlugAvailable(
          parseInt(tenantId),
          slug,
          excludeId || undefined
        );

        return NextResponse.json({
          success: true,
          available: isAvailable,
          slug,
        });
      } catch (error) {
        console.error("Error validating slug:", error);
        return NextResponse.json(
          { 
            error: error instanceof Error ? error.message : "Failed to validate slug",
            success: false 
          },
          { status: 500 }
        );
      }
    },
    { module: "blog", action: "read" }
  )(request);
}
