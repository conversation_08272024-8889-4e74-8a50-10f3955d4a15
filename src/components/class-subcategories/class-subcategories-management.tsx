"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Plus, Search, Edit, Trash2, FolderOpen } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { ClassSubcategoryForm } from "@/components/forms/class-subcategory-form";
import {
  useClassSubcategorySearch,
  useCreateClassSubcategory,
  useUpdateClassSubcategory,
  useDeleteClassSubcategory,
} from "@/lib/hooks/queries/use-class-subcategory-queries";
import { type ClassSubcategory } from "@/lib/db/schema";
import { SuccessToast } from "@/components/ui/success-toast";

/**
 * ClassSubcategoriesManagement Component
 * 
 * Component untuk manage class subcategories dengan full CRUD functionality.
 * Mengikuti pattern yang sama dengan class categories management.
 */

interface ClassSubcategoriesManagementProps {
  tenantId: number;
}

export function ClassSubcategoriesManagement({ tenantId }: ClassSubcategoriesManagementProps) {
  // State management
  const [searchTerm, setSearchTerm] = useState("");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingSubcategory, setEditingSubcategory] = useState<ClassSubcategory | null>(null);
  const [deletingSubcategory, setDeletingSubcategory] = useState<ClassSubcategory | null>(null);
  const [showSuccessToast, setShowSuccessToast] = useState(false);
  const [createdSubcategory, setCreatedSubcategory] = useState<ClassSubcategory | null>(null);

  // Queries
  const {
    data: searchResult,
    isLoading,
    error,
    refetch,
  } = useClassSubcategorySearch(tenantId, undefined, searchTerm, 50, 0);

  // Mutations
  const createMutation = useCreateClassSubcategory();
  const updateMutation = useUpdateClassSubcategory();
  const deleteMutation = useDeleteClassSubcategory();

  // Handlers
  const handleCreateSubcategory = async (data: { tenantId: number; categoryId: string; name: string }) => {
    try {
      const newSubcategory = await createMutation.mutateAsync({
        tenantId: data.tenantId,
        categoryId: data.categoryId,
        name: data.name,
      });

      setIsCreateDialogOpen(false);
      setCreatedSubcategory(newSubcategory);
      setShowSuccessToast(true);

      // Hide success toast after duration
      setTimeout(() => {
        setShowSuccessToast(false);
      }, 4000);
    } catch (error) {
      console.error("Create subcategory error:", error);
    }
  };

  const handleEditSubcategory = async (data: { tenantId: number; categoryId: string; name: string }) => {
    if (!editingSubcategory) return;

    try {
      const updatedSubcategory = await updateMutation.mutateAsync({
        id: editingSubcategory.id,
        tenantId: editingSubcategory.tenantId,
        data: {
          name: data.name,
        },
      });

      setEditingSubcategory(null);
      setCreatedSubcategory(updatedSubcategory);
      setShowSuccessToast(true);

      // Hide success toast after duration
      setTimeout(() => {
        setShowSuccessToast(false);
      }, 4000);
    } catch (error) {
      console.error("Update subcategory error:", error);
    }
  };

  const handleDeleteSubcategory = async () => {
    if (!deletingSubcategory) return;

    try {
      await deleteMutation.mutateAsync({
        id: deletingSubcategory.id,
        tenantId: deletingSubcategory.tenantId,
      });

      setDeletingSubcategory(null);
      setShowSuccessToast(true);

      // Hide success toast after duration
      setTimeout(() => {
        setShowSuccessToast(false);
      }, 4000);
    } catch (error) {
      console.error("Delete subcategory error:", error);
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading class subcategories...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-red-600 mb-4">Error loading class subcategories: {error}</p>
          <Button onClick={() => refetch()} variant="outline">
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  const subcategories = searchResult?.subcategories || [];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Class Subcategories</h1>
          <p className="text-muted-foreground">
            Manage your class subcategories to organize classes within categories
          </p>
        </div>
        
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Add Subcategory
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Create Class Subcategory</DialogTitle>
            </DialogHeader>
            <ClassSubcategoryForm
              tenantId={tenantId}
              onSubmit={handleCreateSubcategory}
              onCancel={() => setIsCreateDialogOpen(false)}
              isLoading={createMutation.isPending}
            />
          </DialogContent>
        </Dialog>
      </div>

      {/* Search */}
      <div className="relative max-w-md">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
        <Input
          placeholder="Search subcategories..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* Subcategories Grid */}
      {subcategories.length === 0 ? (
        <div className="text-center py-12">
          <FolderOpen className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium mb-2">No subcategories found</h3>
          <p className="text-muted-foreground mb-4">
            {searchTerm ? "Try adjusting your search terms" : "Get started by creating your first class subcategory"}
          </p>
          {!searchTerm && (
            <Button onClick={() => setIsCreateDialogOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Subcategory
            </Button>
          )}
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <AnimatePresence>
            {subcategories.map((subcategory) => (
            <motion.div
              key={subcategory.id}
              layout
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              transition={{ duration: 0.2 }}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Card className="hover:shadow-lg transition-all duration-200">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="text-lg font-medium mb-1">
                        {subcategory.name}
                      </CardTitle>
                      <Badge variant="secondary" className="text-xs">
                        ID: {subcategory.id.slice(-8)}
                      </Badge>
                    </div>
                    <div className="flex gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setEditingSubcategory(subcategory)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setDeletingSubcategory(subcategory)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between text-xs text-muted-foreground">
                    <span>Created {new Date(subcategory.createdAt).toLocaleDateString()}</span>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
            ))}
          </AnimatePresence>
        </div>
      )}

      {/* Edit Dialog */}
      <Dialog open={!!editingSubcategory} onOpenChange={() => setEditingSubcategory(null)}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Class Subcategory</DialogTitle>
          </DialogHeader>
          {editingSubcategory && (
            <ClassSubcategoryForm
              subcategory={editingSubcategory}
              tenantId={tenantId}
              onSubmit={handleEditSubcategory}
              onCancel={() => setEditingSubcategory(null)}
              isLoading={updateMutation.isPending}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deletingSubcategory} onOpenChange={() => setDeletingSubcategory(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Class Subcategory</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{deletingSubcategory?.name}"? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteSubcategory}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Success Animations */}
      <SuccessToast
        isVisible={showSuccessToast}
        onClose={() => setShowSuccessToast(false)}
        title="Class Subcategory Success!"
        description={createdSubcategory ?
          `Subcategory "${createdSubcategory.name}" has been processed successfully.` :
          "Your class subcategory operation completed successfully."
        }
        type="general"
        action={{
          label: "View All Subcategories",
          onClick: () => setShowSuccessToast(false)
        }}
      />
    </div>
  );
}
