import { sql, eq, and, ilike, SQL } from "drizzle-orm";
import { 
  package_purchase_options, 
  packages, 
  locations,
  type PackagePurchaseOptions, 
  type NewPackagePurchaseOptions 
} from "@/lib/db/schema";
import { BaseService, CreateEntityData, UpdateEntityData, QueryOptions } from "@/lib/core/base-service";
import { db } from "@/lib/db";

// Package Purchase Options data transfer objects
export interface CreatePackagePurchaseOptionsData extends CreateEntityData {
  packageId: string;
  purchaseLimit?: number;
  restrictTo?: string;
  customerSegmentIds?: string[]; // Array of customer segment IDs for advanced targeting
  transferable?: boolean;
  specifySoldAtLocation?: boolean;
  soldAtLocationId?: string;
  classBookingLimit?: number;
  showOnline?: boolean;
}

export interface UpdatePackagePurchaseOptionsData extends UpdateEntityData {
  packageId?: string;
  purchaseLimit?: number;
  restrictTo?: string;
  customerSegmentIds?: string[]; // Array of customer segment IDs for advanced targeting
  transferable?: boolean;
  specifySoldAtLocation?: boolean;
  soldAtLocationId?: string;
  classBookingLimit?: number;
  showOnline?: boolean;
}

export interface PackagePurchaseOptionsFilters {
  packageId?: string;
  restrictTo?: string;
  transferable?: boolean;
  showOnline?: boolean;
  locationId?: string;
}

export interface PackagePurchaseOptionsStats {
  total: number;
  byPackage: Record<string, number>;
  transferableCount: number;
  onlineVisibleCount: number;
  locationSpecificCount: number;
}

export interface PackagePurchaseOptionsWithDetails extends PackagePurchaseOptions {
  package?: {
    id: string;
    name: string;
    description?: string;
  };
  location?: {
    id: string;
    name: string;
    address?: string;
  };
}

class PackagePurchaseOptionsService extends BaseService<
  PackagePurchaseOptions,
  CreatePackagePurchaseOptionsData,
  UpdatePackagePurchaseOptionsData
> {
  constructor() {
    super(package_purchase_options, "package_purchase_options");
  }

  /**
   * Validate create data
   */
  protected validateCreateData(data: CreatePackagePurchaseOptionsData): void {
    if (!data.packageId) {
      throw new Error("Package ID is required");
    }

    if (typeof data.packageId !== 'string' || data.packageId.trim() === '') {
      throw new Error("Package ID must be a valid string");
    }

    if (data.purchaseLimit !== undefined) {
      if (typeof data.purchaseLimit !== 'number' || data.purchaseLimit < 0) {
        throw new Error("Purchase limit must be a non-negative number");
      }
      if (data.purchaseLimit > 1000000) {
        throw new Error("Purchase limit cannot exceed 1,000,000");
      }
    }

    if (data.classBookingLimit !== undefined) {
      if (typeof data.classBookingLimit !== 'number' || data.classBookingLimit < 0) {
        throw new Error("Class booking limit must be a non-negative number");
      }
      if (data.classBookingLimit > 10000) {
        throw new Error("Class booking limit cannot exceed 10,000");
      }
    }

    if (data.specifySoldAtLocation && (!data.soldAtLocationId || data.soldAtLocationId === "all_locations")) {
      throw new Error("Specific location ID is required when specify sold at location is enabled");
    }

    if (data.soldAtLocationId && typeof data.soldAtLocationId !== 'string') {
      throw new Error("Location ID must be a valid string");
    }

    if (data.restrictTo && typeof data.restrictTo !== 'string') {
      throw new Error("Restrict to value must be a valid string");
    }

    if (data.restrictTo && data.restrictTo.length > 255) {
      throw new Error("Restrict to value cannot exceed 255 characters");
    }

    // Validate boolean fields
    if (data.transferable !== undefined && typeof data.transferable !== 'boolean') {
      throw new Error("Transferable must be a boolean value");
    }

    if (data.specifySoldAtLocation !== undefined && typeof data.specifySoldAtLocation !== 'boolean') {
      throw new Error("Specify sold at location must be a boolean value");
    }

    if (data.showOnline !== undefined && typeof data.showOnline !== 'boolean') {
      throw new Error("Show online must be a boolean value");
    }
  }

  /**
   * Validate update data
   */
  protected validateUpdateData(data: UpdatePackagePurchaseOptionsData): void {
    if (data.purchaseLimit !== undefined) {
      if (typeof data.purchaseLimit !== 'number' || data.purchaseLimit < 0) {
        throw new Error("Purchase limit must be a non-negative number");
      }
      if (data.purchaseLimit > 1000000) {
        throw new Error("Purchase limit cannot exceed 1,000,000");
      }
    }

    if (data.classBookingLimit !== undefined) {
      if (typeof data.classBookingLimit !== 'number' || data.classBookingLimit < 0) {
        throw new Error("Class booking limit must be a non-negative number");
      }
      if (data.classBookingLimit > 10000) {
        throw new Error("Class booking limit cannot exceed 10,000");
      }
    }

    if (data.specifySoldAtLocation && (!data.soldAtLocationId || data.soldAtLocationId === "all_locations")) {
      throw new Error("Specific location ID is required when specify sold at location is enabled");
    }

    if (data.soldAtLocationId !== undefined && data.soldAtLocationId !== null && typeof data.soldAtLocationId !== 'string') {
      throw new Error("Location ID must be a valid string");
    }

    if (data.restrictTo !== undefined && data.restrictTo !== null) {
      if (typeof data.restrictTo !== 'string') {
        throw new Error("Restrict to value must be a valid string");
      }
      if (data.restrictTo.length > 255) {
        throw new Error("Restrict to value cannot exceed 255 characters");
      }
    }

    // Validate boolean fields
    if (data.transferable !== undefined && typeof data.transferable !== 'boolean') {
      throw new Error("Transferable must be a boolean value");
    }

    if (data.specifySoldAtLocation !== undefined && typeof data.specifySoldAtLocation !== 'boolean') {
      throw new Error("Specify sold at location must be a boolean value");
    }

    if (data.showOnline !== undefined && typeof data.showOnline !== 'boolean') {
      throw new Error("Show online must be a boolean value");
    }
  }

  /**
   * Build filters for queries
   */
  protected buildFilters(filters: PackagePurchaseOptionsFilters): SQL[] {
    const conditions: SQL[] = [];

    if (filters.packageId) {
      conditions.push(eq(package_purchase_options.package_id, filters.packageId));
    }

    if (filters.restrictTo) {
      conditions.push(eq(package_purchase_options.restrict_to, filters.restrictTo));
    }

    if (filters.transferable !== undefined) {
      conditions.push(eq(package_purchase_options.transferable, filters.transferable));
    }

    if (filters.showOnline !== undefined) {
      conditions.push(eq(package_purchase_options.show_online, filters.showOnline));
    }

    if (filters.locationId) {
      conditions.push(eq(package_purchase_options.sold_at_location_id, filters.locationId));
    }

    return conditions;
  }

  /**
   * Create package purchase options
   */
  async create(data: CreatePackagePurchaseOptionsData): Promise<PackagePurchaseOptions> {
    console.log(`📦 [PackagePurchaseOptionsService] Creating package purchase options:`, data);

    this.validateCreateData(data);

    // Check if package exists
    const packageExists = await db
      .select({ id: packages.id })
      .from(packages)
      .where(eq(packages.id, data.packageId))
      .limit(1);

    if (packageExists.length === 0) {
      throw new Error("Package not found");
    }

    // Check if package purchase options already exist for this package
    const existing = await this.getByPackageId(data.packageId);
    if (existing) {
      throw new Error("Package purchase options already exist for this package");
    }

    // Validate location if specified and ensure it belongs to the same tenant as the package
    if (data.soldAtLocationId) {
      // First get the package to know which tenant it belongs to
      const [packageInfo] = await db
        .select({ tenantId: packages.tenantId })
        .from(packages)
        .where(eq(packages.id, data.packageId))
        .limit(1);

      if (!packageInfo) {
        throw new Error("Package not found");
      }

      // Then check if location exists and belongs to the same tenant
      const [locationInfo] = await db
        .select({ id: locations.id, tenantId: locations.tenantId })
        .from(locations)
        .where(and(
          eq(locations.id, data.soldAtLocationId),
          eq(locations.tenantId, packageInfo.tenantId)
        ))
        .limit(1);

      if (!locationInfo) {
        throw new Error("Location not found or does not belong to the same tenant");
      }
    }

    const optionsData: NewPackagePurchaseOptions = {
      package_id: data.packageId,
      purchase_limit: data.purchaseLimit || null,
      restrict_to: data.restrictTo || null,
      transferable: data.transferable ?? false,
      specify_sold_at_location: data.specifySoldAtLocation ?? false,
      sold_at_location_id: data.soldAtLocationId || null,
      class_booking_limit: data.classBookingLimit || null,
      show_online: data.showOnline ?? false,
    };

    const [newOptions] = await db
      .insert(package_purchase_options)
      .values(optionsData)
      .returning();

    console.log(`📦 [PackagePurchaseOptionsService] Created package purchase options:`, newOptions);
    return newOptions;
  }

  /**
   * Update package purchase options
   */
  async update(packageId: string, data: UpdatePackagePurchaseOptionsData): Promise<PackagePurchaseOptions> {
    console.log(`📦 [PackagePurchaseOptionsService] Updating package purchase options:`, { packageId, data });

    this.validateUpdateData(data);

    // Validate location if specified and ensure it belongs to the same tenant as the package
    if (data.soldAtLocationId) {
      // First get the package to know which tenant it belongs to
      const [packageInfo] = await db
        .select({ tenantId: packages.tenantId })
        .from(packages)
        .where(eq(packages.id, packageId))
        .limit(1);

      if (!packageInfo) {
        throw new Error("Package not found");
      }

      // Then check if location exists and belongs to the same tenant
      const [locationInfo] = await db
        .select({ id: locations.id, tenantId: locations.tenantId })
        .from(locations)
        .where(and(
          eq(locations.id, data.soldAtLocationId),
          eq(locations.tenantId, packageInfo.tenantId)
        ))
        .limit(1);

      if (!locationInfo) {
        throw new Error("Location not found or does not belong to the same tenant");
      }
    }

    const updateData: Partial<NewPackagePurchaseOptions> = {};

    if (data.purchaseLimit !== undefined) updateData.purchase_limit = data.purchaseLimit;
    if (data.restrictTo !== undefined) updateData.restrict_to = data.restrictTo;
    if (data.transferable !== undefined) updateData.transferable = data.transferable;
    if (data.specifySoldAtLocation !== undefined) updateData.specify_sold_at_location = data.specifySoldAtLocation;
    if (data.soldAtLocationId !== undefined) updateData.sold_at_location_id = data.soldAtLocationId;
    if (data.classBookingLimit !== undefined) updateData.class_booking_limit = data.classBookingLimit;
    if (data.showOnline !== undefined) updateData.show_online = data.showOnline;

    const [updatedOptions] = await db
      .update(package_purchase_options)
      .set(updateData)
      .where(eq(package_purchase_options.package_id, packageId))
      .returning();

    if (!updatedOptions) {
      throw new Error("Package purchase options not found");
    }

    console.log(`📦 [PackagePurchaseOptionsService] Updated package purchase options:`, updatedOptions);
    return updatedOptions;
  }

  /**
   * Get package purchase options by package ID
   */
  async getByPackageId(packageId: string): Promise<PackagePurchaseOptions | null> {
    const [result] = await db
      .select()
      .from(package_purchase_options)
      .where(eq(package_purchase_options.package_id, packageId))
      .limit(1);

    return result || null;
  }

  /**
   * Get package purchase options with details (package and location info)
   */
  async getWithDetails(packageId: string): Promise<PackagePurchaseOptionsWithDetails | null> {
    const [result] = await db
      .select({
        // Package purchase options fields
        package_id: package_purchase_options.package_id,
        purchase_limit: package_purchase_options.purchase_limit,
        restrict_to: package_purchase_options.restrict_to,
        transferable: package_purchase_options.transferable,
        specify_sold_at_location: package_purchase_options.specify_sold_at_location,
        sold_at_location_id: package_purchase_options.sold_at_location_id,
        class_booking_limit: package_purchase_options.class_booking_limit,
        show_online: package_purchase_options.show_online,
        // Package info
        packageName: packages.name,
        packageDescription: packages.description,
        // Location info
        locationName: locations.name,
        locationAddress: locations.address,
      })
      .from(package_purchase_options)
      .leftJoin(packages, eq(package_purchase_options.package_id, packages.id))
      .leftJoin(locations, eq(package_purchase_options.sold_at_location_id, locations.id))
      .where(eq(package_purchase_options.package_id, packageId))
      .limit(1);

    if (!result) return null;

    return {
      package_id: result.package_id,
      purchase_limit: result.purchase_limit,
      restrict_to: result.restrict_to,
      transferable: result.transferable,
      specify_sold_at_location: result.specify_sold_at_location,
      sold_at_location_id: result.sold_at_location_id,
      class_booking_limit: result.class_booking_limit,
      show_online: result.show_online,
      package: result.packageName ? {
        id: result.package_id,
        name: result.packageName,
        description: result.packageDescription || undefined,
      } : undefined,
      location: result.locationName ? {
        id: result.sold_at_location_id!,
        name: result.locationName,
        address: result.locationAddress || undefined,
      } : undefined,
    };
  }

  /**
   * Delete package purchase options by package ID
   */
  async deleteByPackageId(packageId: string): Promise<boolean> {
    console.log(`📦 [PackagePurchaseOptionsService] Deleting package purchase options for package:`, packageId);

    const result = await db
      .delete(package_purchase_options)
      .where(eq(package_purchase_options.package_id, packageId));

    return result.rowCount > 0;
  }

  /**
   * Get statistics
   */
  async getStats(): Promise<PackagePurchaseOptionsStats> {
    const [totalResult] = await db
      .select({ count: sql<number>`count(*)` })
      .from(package_purchase_options);

    const [transferableResult] = await db
      .select({ count: sql<number>`count(*)` })
      .from(package_purchase_options)
      .where(eq(package_purchase_options.transferable, true));

    const [onlineVisibleResult] = await db
      .select({ count: sql<number>`count(*)` })
      .from(package_purchase_options)
      .where(eq(package_purchase_options.show_online, true));

    const [locationSpecificResult] = await db
      .select({ count: sql<number>`count(*)` })
      .from(package_purchase_options)
      .where(eq(package_purchase_options.specify_sold_at_location, true));

    const byPackageResults = await db
      .select({
        packageId: package_purchase_options.package_id,
        count: sql<number>`count(*)`,
      })
      .from(package_purchase_options)
      .groupBy(package_purchase_options.package_id);

    const byPackage = byPackageResults.reduce((acc, item) => {
      acc[item.packageId] = item.count;
      return acc;
    }, {} as Record<string, number>);

    return {
      total: totalResult.count,
      byPackage,
      transferableCount: transferableResult.count,
      onlineVisibleCount: onlineVisibleResult.count,
      locationSpecificCount: locationSpecificResult.count,
    };
  }
}

// Export singleton instance
export const packagePurchaseOptionsService = new PackagePurchaseOptionsService();
