"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  BarChart3, 
  Users, 
  Building2, 
  Globe, 
  TrendingUp, 
  Activity,
  Zap,
  Database,
  ArrowUpRight,
  Plus,
  Settings,
  Bell,
  Loader2,
  AlertCircle,
  CheckCircle,
  Clock,
  Target,
  MapPin,
  Briefcase,
  Shield,
  Eye,
  ExternalLink,
  CreditCard,
  DollarSign
} from "lucide-react";
import { 
  useUserStats,
  useUsers
} from "@/lib/hooks/queries/use-user-queries";
import { useOrganizationStats } from "@/lib/hooks/queries/use-organization-queries";
import { useTenantStats } from "@/lib/hooks/queries/use-tenant-queries";
import { useBusinessProfiles } from "@/lib/hooks/queries/use-business-profile-queries";
import { useAddresses } from "@/lib/hooks/queries/use-address-queries";
import Link from "next/link";
import { User } from "@/lib/db/schema";
import { AuthUser } from "@/lib/auth/types";

interface DashboardOverviewProps {
  user: AuthUser;
}

export function DashboardOverview({ user }: DashboardOverviewProps) {
  const [selectedTab, setSelectedTab] = useState("overview");

  // Global stats queries dengan TanStack Query
  const { data: userStats, isLoading: userStatsLoading, error: userStatsError } = useUserStats();
  const { data: orgStats, isLoading: orgStatsLoading, error: orgStatsError } = useOrganizationStats();
  const { data: tenantStats, isLoading: tenantStatsLoading, error: tenantStatsError } = useTenantStats();
  
  // Additional data queries
  const { data: usersData, isLoading: usersLoading } = useUsers({ limit: 5 }); // Recent users
  const { data: businessProfilesData, isLoading: businessProfilesLoading } = useBusinessProfiles({ limit: 5 });
  const { data: addressesData, isLoading: addressesLoading } = useAddresses({ tenantId: 1, limit: 5 });

  const isLoading = userStatsLoading || orgStatsLoading || tenantStatsLoading;
  const hasError = userStatsError || orgStatsError || tenantStatsError;

  // Loading state
  if (isLoading) {
    return (
      <div className="space-y-8">
        <div className="flex items-center justify-center py-12">
          <div className="flex items-center gap-2">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span>Loading dashboard data...</span>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (hasError) {
    return (
      <div className="space-y-8">
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="flex items-center gap-2 text-destructive">
              <AlertCircle className="h-6 w-6" />
              <span>Error loading dashboard data</span>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Welcome Section with TanStack branding */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            Welcome back, {user.name}!
            <Badge variant="outline" className="bg-primary/10">
              <Zap className="h-3 w-3 mr-1" />
              TanStack Powered
            </Badge>
          </h1>
          <p className="text-muted-foreground">
            Here's what's happening with your account today - powered by TanStack ecosystem.
          </p>
        </div>
        
        <Button asChild>
          <Link href="/admin">
            <Shield className="h-4 w-4 mr-2" />
            Admin Dashboard
          </Link>
        </Button>
      </div>

      {/* TanStack Features Overview */}
      <div className="grid gap-4 md:grid-cols-4 mb-6">
        
      </div>

      {/* Main Content Tabs */}
      <Tabs value={selectedTab} onValueChange={setSelectedTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="stats" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Statistics
          </TabsTrigger>
          <TabsTrigger value="recent" className="flex items-center gap-2">
            <Activity className="h-4 w-4" />
            Recent Activity
          </TabsTrigger>
          <TabsTrigger value="quick-actions" className="flex items-center gap-2">
            <Zap className="h-4 w-4" />
            Quick Actions
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <div className="space-y-6">
            {/* Global Stats */}
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Users</CardTitle>
                  <Users className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {userStats?.totalUsers || 0}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {userStats?.activeUsers || 0} active users
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Organizations</CardTitle>
                  <Building2 className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {orgStats?.totalOrganizations || 0}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {orgStats?.activeOrganizations || 0} active
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Tenants</CardTitle>
                  <Globe className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {tenantStats?.totalTenants || 0}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    {tenantStats?.activeTenants || 0} active
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Business Profiles</CardTitle>
                  <Briefcase className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {businessProfilesData?.businessProfiles?.length || 0}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Active profiles
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* TanStack Query Status */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Database className="h-5 w-5 text-primary" />
                  TanStack Query Status
                </CardTitle>
                <CardDescription>
                  Real-time status of global data synchronization
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-3">
                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <span className="text-sm font-medium">User Stats Query</span>
                    <Badge variant={userStatsLoading ? "secondary" : "default"}>
                      {userStatsLoading ? "Loading" : "Cached"}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <span className="text-sm font-medium">Organization Stats</span>
                    <Badge variant={orgStatsLoading ? "secondary" : "default"}>
                      {orgStatsLoading ? "Loading" : "Cached"}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between p-3 border rounded-lg">
                    <span className="text-sm font-medium">Tenant Stats</span>
                    <Badge variant={tenantStatsLoading ? "secondary" : "default"}>
                      {tenantStatsLoading ? "Loading" : "Cached"}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="stats">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>User Statistics</CardTitle>
                <CardDescription>User growth and activity metrics</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span>Total Users</span>
                    <span className="font-bold">{userStats?.totalUsers || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Active Users</span>
                    <span className="font-bold">{userStats?.activeUsers || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Admin Users</span>
                    <span className="font-bold">{userStats?.adminUsers || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Recent Signups</span>
                    <span className="font-bold">{userStats?.recentSignups || 0}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>System Statistics</CardTitle>
                <CardDescription>Overall system metrics</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span>Organizations</span>
                    <span className="font-bold">{orgStats?.totalOrganizations || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Tenants</span>
                    <span className="font-bold">{tenantStats?.totalTenants || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Business Profiles</span>
                    <span className="font-bold">{tenantStats?.totalBusinessProfiles || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Total Members</span>
                    <span className="font-bold">{orgStats?.totalMembers || 0}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="recent">
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Recent Users
                </CardTitle>
                <CardDescription>
                  Latest user registrations
                </CardDescription>
              </CardHeader>
              <CardContent>
                {usersLoading ? (
                  <div className="flex items-center justify-center py-4">
                    <Loader2 className="h-4 w-4 animate-spin" />
                  </div>
                ) : (
                  <div className="space-y-3">
                    {usersData?.users?.slice(0, 5).map((user) => (
                      <div key={user.id} className="flex items-center gap-3 p-3 border rounded-lg">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={user.image || ""} alt={user.name || ""} />
                          <AvatarFallback>
                            {user.name?.charAt(0)?.toUpperCase() || "U"}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <p className="text-sm font-medium">{user.name}</p>
                          <p className="text-xs text-muted-foreground">{user.email}</p>
                        </div>
                        <Badge variant={user.role === "admin" ? "default" : "secondary"}>
                          {user.role}
                        </Badge>
                      </div>
                    )) || (
                      <p className="text-sm text-muted-foreground">No recent users</p>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Briefcase className="h-5 w-5" />
                  Recent Business Profiles
                </CardTitle>
                <CardDescription>
                  Latest business profile updates
                </CardDescription>
              </CardHeader>
              <CardContent>
                {businessProfilesLoading ? (
                  <div className="flex items-center justify-center py-4">
                    <Loader2 className="h-4 w-4 animate-spin" />
                  </div>
                ) : (
                  <div className="space-y-3">
                    {businessProfilesData?.businessProfiles?.slice(0, 5).map((profile) => (
                      <div key={profile.id} className="flex items-center gap-3 p-3 border rounded-lg">
                        <div className="flex-1">
                          <p className="text-sm font-medium">{profile.businessName}</p>
                          <p className="text-xs text-muted-foreground">{profile.businessIndustry}</p>
                        </div>
                        <Badge variant="outline">
                          Tenant {profile.tenantId}
                        </Badge>
                      </div>
                    )) || (
                      <p className="text-sm text-muted-foreground">No recent profiles</p>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="quick-actions">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <CreditCard className="h-5 w-5" />
                  <span>Purchase Credits</span>
                </CardTitle>
                <CardDescription>
                  Buy more credits to continue using our services
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button asChild className="w-full">
                  <Link href="/billing">View Packages</Link>
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Shield className="h-5 w-5" />
                  <span>API Keys</span>
                </CardTitle>
                <CardDescription>
                  Manage your API keys and access tokens
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button asChild variant="outline" className="w-full">
                  <Link href="/api-keys">Manage Keys</Link>
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Activity className="h-5 w-5" />
                  <span>Analytics</span>
                </CardTitle>
                <CardDescription>
                  View your usage statistics and trends
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button asChild variant="outline" className="w-full">
                  <Link href="/analytics">View Analytics</Link>
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Users className="h-5 w-5" />
                  <span>User Management</span>
                </CardTitle>
                <CardDescription>
                  Manage users with TanStack Table
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button asChild variant="outline" className="w-full">
                  <Link href="/admin">
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Admin Dashboard
                  </Link>
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Briefcase className="h-5 w-5" />
                  <span>Business Management</span>
                </CardTitle>
                <CardDescription>
                  Manage business profiles with TanStack
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button asChild variant="outline" className="w-full">
                  <Link href="/business">
                    <ExternalLink className="h-4 w-4 mr-2" />
                    Business Dashboard
                  </Link>
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Zap className="h-5 w-5" />
                  <span>TanStack Demo</span>
                </CardTitle>
                <CardDescription>
                  Full TanStack ecosystem demonstration
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button asChild variant="outline" className="w-full">
                  <Link href="/business-tanstack">
                    <ExternalLink className="h-4 w-4 mr-2" />
                    TanStack Demo
                  </Link>
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
