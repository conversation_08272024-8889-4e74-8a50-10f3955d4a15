# 📅 Panduan Implementasi Class Schedules - Gaya Sandika Galih

> **Dokumentasi lengkap implementasi fitur Class Schedules menggunakan pola arsitektur modular yang sudah terbukti berhasil**  
> Di<PERSON><PERSON> dengan gaya mengajar Sandika Galih yang ramah untuk new coder dan profesional

---

## 📋 Daftar Isi

1. [🎯 Overview & Arsitektur](#-overview--arsitektur)
2. [🗄️ Database Schema & Relationships](#️-database-schema--relationships)
3. [⚙️ Service Layer Implementation](#️-service-layer-implementation)
4. [🔄 TanStack Query Integration](#-tanstack-query-integration)
5. [📝 Form Components](#-form-components)
6. [🎨 Management Interface](#-management-interface)
7. [🔒 RBAC & Security](#-rbac--security)
8. [🚀 Testing & Troubleshooting](#-testing--troubleshooting)

---

## 🎯 Overview & Arsitektur

### **Mengapa Menggunakan Pattern Ini?**

Halo teman-teman! Kali ini kita akan belajar implementasi Class Schedules yang mengikuti pola arsitektur modular yang sudah terbukti berhasil di fitur-fitur sebelumnya seperti Classes, Categories, Equipment, dan Locations.

**Kenapa kita pakai pattern yang sama?**
1. **Konsistensi** - Semua developer di tim bisa langsung paham
2. **Maintainability** - Mudah di-maintain dan di-extend
3. **Reusability** - Komponen bisa dipakai ulang
4. **Type Safety** - Full TypeScript support
5. **Performance** - Optimistic updates dan caching yang baik

### **Arsitektur Overview**

```mermaid
graph TD
    A[Page Component] --> B[Management Component]
    B --> C[Form Component]
    B --> D[TanStack Query Hooks]
    D --> E[API Routes]
    E --> F[Service Layer]
    F --> G[Database Schema]
    
    H[RBAC Guard] --> A
    I[Tenant Isolation] --> F
```

**Penjelasan Layer by Layer:**

1. **Page Component** - Entry point dengan authentication dan RBAC
2. **Management Component** - UI untuk list, create, edit, delete
3. **Form Component** - Reusable form untuk create/edit
4. **TanStack Query Hooks** - Data fetching dengan caching
5. **API Routes** - RESTful endpoints
6. **Service Layer** - Business logic dan database operations
7. **Database Schema** - Drizzle ORM schema dengan relationships

---

## 🗄️ Database Schema & Relationships

### **Schema Class Schedules**

Mari kita lihat schema yang sudah ada di `src/lib/db/schema.ts`:

```typescript
export const class_schedules = pgTable("class_schedules", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  class_id: varchar("class_id", { length: 255 }).references(() => classes.id, { onDelete: "cascade" }).notNull(),
  tenant_id: integer("tenant_id").references(() => tenants.id, { onDelete: "cascade" }),
  location_id: varchar("location_id", { length: 255 }).references(() => locations.id, { onDelete: "cascade" }),
  facility_id: varchar("facility_id", { length: 255 }).references(() => facilities.id, { onDelete: "cascade" }),
  staff_id: varchar("staff_id", { length: 255 }).references(() => users.id, { onDelete: "cascade" }),
  start_date: date("start_date"),
  end_date: date("end_date"),
  start_time: timestamp("start_time"),
  end_time: timestamp("end_time"),
  duration: integer("duration").notNull(),
  calender_color: varchar("calender_color", { length: 50 }),
  repeat_rule: varchar("repeat_rule", { length: 50 }).default("none").notNull(),
  pax: integer("pax").default(1).notNull(),
  waitlist: integer("waitlist").default(1).notNull(),
  allow_classpass: boolean("allow_classpass").default(false).notNull(),
  is_private: boolean("is_private").default(false).notNull(),
  publish_now: boolean("publish_now").default(false).notNull(),
  publish_at: timestamp("publish_at"),
  auto_cancel_if_minimum_not_met: boolean("auto_cancel_if_minimum_not_met").default(false).notNull(),
  booking_window_start: timestamp("booking_window_start"),
  booking_window_end: timestamp("booking_window_end"),
  check_in_window_start: timestamp("check_in_window_start"),
  check_in_window_end: timestamp("check_in_window_end"),
  late_cancellation_rule: varchar("late_cancellation_rule", { length: 255 }),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});
```

### **Relationships Explained**

**Kenapa ada banyak foreign keys?**

1. **class_id** → `classes.id` - Setiap schedule harus terkait dengan class tertentu
2. **tenant_id** → `tenants.id` - Untuk tenant isolation (multi-tenancy)
3. **location_id** → `locations.id` - Schedule bisa di location tertentu (optional)
4. **facility_id** → `facilities.id` - Schedule bisa di facility tertentu (optional)
5. **staff_id** → `users.id` - Instructor yang mengajar (optional)

**Mengapa ada field-field seperti booking_window dan check_in_window?**

Ini untuk business logic yang kompleks:
- **booking_window** - Kapan customer bisa booking class ini
- **check_in_window** - Kapan customer bisa check-in
- **late_cancellation_rule** - Aturan pembatalan terlambat

### **Type Definitions**

```typescript
// Type definitions untuk class schedules
export type ClassSchedule = typeof class_schedules.$inferSelect;
export type NewClassSchedule = typeof class_schedules.$inferInsert;
```

**Kenapa pakai $inferSelect dan $inferInsert?**
- **$inferSelect** - Type untuk data yang dibaca dari database
- **$inferInsert** - Type untuk data yang akan diinsert ke database
- Drizzle ORM otomatis generate types yang akurat

---

## ⚙️ Service Layer Implementation

### **Mengapa Pakai Service Layer?**

Service layer adalah "jembatan" antara API routes dan database. Ini tempat kita taruh business logic.

**Keuntungan pakai service layer:**
1. **Separation of Concerns** - API routes cuma handle HTTP, service handle business logic
2. **Reusability** - Service bisa dipanggil dari mana aja
3. **Testing** - Mudah di-unit test
4. **Validation** - Foreign key validation sebelum insert/update

### **ClassScheduleService Implementation**

Mari kita breakdown service yang sudah kita buat di `src/lib/services/class-schedule.service.ts`:

#### **1. Search Method dengan Filtering**

```typescript
static async searchSchedules(
  tenantId: number,
  classId?: string,
  locationId?: string,
  facilityId?: string,
  staffId?: string,
  startDate?: string,
  endDate?: string,
  search?: string,
  limit = 20,
  offset = 0
): Promise<ClassScheduleSearchResult>
```

**Kenapa method ini kompleks?**
- Support multiple filters sekaligus
- Pagination dengan limit/offset
- Search functionality dengan JOIN ke tables lain
- Tenant isolation yang ketat

**Bagaimana search bekerja?**
```typescript
// Search di nama class atau instructor
if (search && search.trim()) {
  const searchResults = await db
    .select({
      schedule: class_schedules,
      class_name: classes.name,
      instructor_name: users.name,
    })
    .from(class_schedules)
    .leftJoin(classes, eq(class_schedules.class_id, classes.id))
    .leftJoin(users, eq(class_schedules.staff_id, users.id))
    .where(
      and(
        whereConditions,
        sql`(${classes.name} ILIKE ${`%${search}%`} OR ${users.name} ILIKE ${`%${search}%`})`
      )
    )
}
```

**Penjelasan:**
- `leftJoin` - Ambil data dari table lain meski null
- `ILIKE` - Case-insensitive search
- `sql` template - Raw SQL untuk complex conditions

#### **2. Create Method dengan Validation**

```typescript
static async create(data: {
  tenant_id: number;
  class_id: string;
  // ... other fields
}): Promise<ClassSchedule>
```

**Kenapa ada banyak validation?**
```typescript
// Check if class exists and belongs to tenant
const [classExists] = await db
  .select({ id: classes.id })
  .from(classes)
  .where(and(
    eq(classes.id, data.class_id),
    eq(classes.tenantId, data.tenant_id)
  ))
  .limit(1);

if (!classExists) {
  throw new Error(`Class with ID ${data.class_id} not found or does not belong to tenant ${data.tenant_id}`);
}
```

**Ini penting untuk:**
1. **Data Integrity** - Pastikan foreign keys valid
2. **Security** - Prevent cross-tenant data access
3. **User Experience** - Error messages yang jelas

#### **3. Specialized Methods**

```typescript
// Get schedules by class
static async getByClassId(classId: string): Promise<ClassSchedule[]>

// Get schedules by location  
static async getByLocationId(locationId: string): Promise<ClassSchedule[]>

// Get schedules by instructor
static async getByInstructorId(staffId: string): Promise<ClassSchedule[]>

// Get schedules by date range (untuk calendar view)
static async getByDateRange(tenantId: number, startDate: string, endDate: string): Promise<ClassScheduleWithRelations[]>
```

**Kenapa perlu method-method khusus ini?**
- **Performance** - Query yang lebih spesifik lebih cepat
- **Use Cases** - Different UI components need different data
- **Caching** - TanStack Query bisa cache per use case

---

## 🔄 TanStack Query Integration

### **Mengapa TanStack Query?**

TanStack Query (dulu React Query) adalah library untuk data fetching yang sangat powerful.

**Keuntungan:**
1. **Automatic Caching** - Data di-cache otomatis
2. **Background Updates** - Refetch data di background
3. **Optimistic Updates** - UI update dulu, sync ke server belakangan
4. **Error Handling** - Built-in error states
5. **Loading States** - Built-in loading states

### **createEntityHooks Factory Pattern**

Kita pakai factory pattern untuk generate hooks yang konsisten:

```typescript
// Create base hooks using the factory
const baseHooks = createEntityHooks('class-schedules', classScheduleApi);

// Export base hooks with class schedule-specific names
export const useClassSchedules = baseHooks.useEntities;
export const useClassSchedule = baseHooks.useEntity;
export const useClassSchedulesByTenant = baseHooks.useEntitiesByTenant;
export const useCreateClassSchedule = baseHooks.useCreateEntity;
export const useUpdateClassSchedule = baseHooks.useUpdateEntity;
export const useDeleteClassSchedule = baseHooks.useDeleteEntity;
```

**Kenapa pakai factory pattern?**
1. **DRY Principle** - Don't Repeat Yourself
2. **Consistency** - Semua hooks punya behavior yang sama
3. **Maintainability** - Update factory, semua hooks ikut update
4. **Type Safety** - TypeScript types otomatis ter-generate

### **Custom Hooks untuk Use Cases Khusus**

```typescript
export function useClassSchedulesByClass(classId: string) {
  return useQuery({
    queryKey: classScheduleKeys.byClass(classId),
    queryFn: () => classScheduleApi.getByClass(classId),
    enabled: !!classId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}
```

**Penjelasan parameter:**
- **queryKey** - Unique identifier untuk cache
- **queryFn** - Function yang fetch data
- **enabled** - Conditional fetching
- **staleTime** - Berapa lama data dianggap fresh

### **Query Keys Strategy**

```typescript
export const classScheduleKeys = {
  all: ['class-schedules'] as const,
  lists: () => [...classScheduleKeys.all, 'list'] as const,
  list: (filters: Record<string, any>) => [...classScheduleKeys.lists(), { filters }] as const,
  details: () => [...classScheduleKeys.all, 'detail'] as const,
  detail: (id: string) => [...classScheduleKeys.details(), id] as const,
  byTenant: (tenantId: number) => [...classScheduleKeys.all, 'tenant', tenantId] as const,
  byClass: (classId: string) => [...classScheduleKeys.all, 'class', classId] as const,
  // ... more keys
};
```

**Kenapa query keys penting?**
1. **Cache Management** - TanStack Query pakai keys untuk organize cache
2. **Invalidation** - Bisa invalidate cache yang spesifik
3. **Hierarchical** - Keys yang hierarchical memudahkan invalidation

---

## 📝 Form Components

### **Mengapa Form Components Reusable?**

Form component yang baik harus bisa dipakai untuk create dan edit. Ini prinsip DRY (Don't Repeat Yourself).

### **ClassScheduleForm Implementation**

Mari kita breakdown form yang sudah kita buat di `src/components/forms/class-schedule-form.tsx`:

#### **1. Schema Validation dengan Zod**

```typescript
const classScheduleFormSchema = z.object({
  class_id: z.string().min(1, "Class is required"),
  location_id: z.string().optional(),
  facility_id: z.string().optional(),
  start_date: z.string().min(1, "Start date is required"),
  start_time: z.string().min(1, "Start time is required"),
  end_time: z.string().min(1, "End time is required"),
  duration: z.number().int().positive("Duration must be positive"),
  pax: z.number().int().positive("Capacity must be positive").default(1),
  // ... more fields
});
```

**Kenapa pakai Zod?**
1. **Type Safety** - Generate TypeScript types otomatis
2. **Runtime Validation** - Validate data saat runtime
3. **Error Messages** - Custom error messages yang user-friendly
4. **Integration** - Works seamlessly dengan react-hook-form

#### **2. React Hook Form Integration**

```typescript
const form = useForm<ClassScheduleFormValues>({
  resolver: zodResolver(classScheduleFormSchema),
  defaultValues: {
    class_id: initialData?.class_id || "",
    location_id: initialData?.location_id || "",
    // ... populate dari initialData untuk edit mode
  },
});
```

**Kenapa pakai react-hook-form?**
1. **Performance** - Minimal re-renders
2. **Validation** - Built-in validation support
3. **TypeScript** - Full type safety
4. **Developer Experience** - Easy to use API

#### **3. Dropdown Relationships**

```typescript
// Fetch dropdown data
const { data: classes = [], isLoading: isLoadingClasses } = useClassesByTenant(tenantId);
const { data: locations = [], isLoading: isLoadingLocations } = useLocationsByTenant(tenantId);
const { data: facilities = [], isLoading: isLoadingFacilities } = useFacilitiesByTenant(tenantId);

// Filter facilities by selected location
const filteredFacilities = selectedLocationId
  ? facilities.filter(facility => facility.locationId === selectedLocationId)
  : facilities;
```

**Kenapa ada filtering?**
- **User Experience** - Facilities cuma show yang relevant ke location
- **Data Integrity** - Prevent invalid combinations
- **Performance** - Smaller dropdown lists

#### **4. Two-Column Layout**

```typescript
<CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
  {/* Form fields */}
</CardContent>
```

**Kenapa two-column?**
1. **Space Efficiency** - Manfaatin horizontal space
2. **User Experience** - Form tidak terlalu panjang vertical
3. **Responsive** - Single column di mobile, two columns di desktop

---

## 🎨 Management Interface

### **ClassSchedulesManagement Component**

Management component adalah "command center" untuk CRUD operations.

#### **1. State Management**

```typescript
const [searchTerm, setSearchTerm] = useState("");
const [selectedClassId, setSelectedClassId] = useState<string>("all");
const [selectedLocationId, setSelectedLocationId] = useState<string>("all");
const [startDate, setStartDate] = useState<string>("");
const [endDate, setEndDate] = useState<string>("");
const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
const [editingSchedule, setEditingSchedule] = useState<ClassSchedule | null>(null);
const [deletingSchedule, setDeletingSchedule] = useState<ClassSchedule | null>(null);
```

**Kenapa banyak state?**
- **Filters** - Multiple filters untuk search
- **Dialogs** - Control modal open/close states
- **Editing** - Track item yang sedang di-edit
- **UI State** - Loading, success, error states

#### **2. Advanced Search dengan Multiple Filters**

```typescript
const {
  data: searchResult,
  isLoading,
  error,
  refetch,
} = useClassScheduleSearchAdvanced(
  tenantId,
  selectedClassId === "all" ? undefined : selectedClassId,
  selectedLocationId === "all" ? undefined : selectedLocationId,
  undefined, // facilityId
  undefined, // staffId
  startDate || undefined,
  endDate || undefined,
  searchTerm,
  50,
  0
);
```

**Kenapa advanced search?**
1. **User Experience** - Users bisa filter data dengan mudah
2. **Performance** - Server-side filtering lebih efisien
3. **Scalability** - Bisa handle large datasets

#### **3. Card-Based Layout dengan Animations**

```typescript
<motion.div
  key={schedule.id}
  initial={{ opacity: 0, y: 20 }}
  animate={{ opacity: 1, y: 0 }}
  exit={{ opacity: 0, y: -20 }}
  transition={{ duration: 0.2 }}
>
  <Card className="hover:shadow-md transition-shadow">
    {/* Schedule content */}
  </Card>
</motion.div>
```

**Kenapa pakai animations?**
1. **User Experience** - Smooth transitions feel professional
2. **Visual Feedback** - Users tahu ada perubahan
3. **Modern UI** - Sesuai dengan design trends

#### **4. Success Feedback**

```typescript
const handleCreateSchedule = async (data: ClassScheduleFormData) => {
  try {
    const newSchedule = await createMutation.mutateAsync(data);
    setCreatedSchedule(newSchedule);
    setShowSuccessToast(true);
    setIsCreateDialogOpen(false);
    refetch();
  } catch (error) {
    console.error("Error creating schedule:", error);
  }
};
```

**Kenapa perlu success feedback?**
1. **User Confidence** - Users tahu action berhasil
2. **Professional Feel** - Aplikasi terasa polished
3. **Error Prevention** - Users tidak submit ulang

---

## 🔒 RBAC & Security

### **Permission Guard Implementation**

```typescript
<PermissionGuard permission="classes.manage">
  <div className="container mx-auto py-6">
    <Suspense fallback={<ClassSchedulesLoading />}>
      <ClassSchedulesManagement tenantId={tenantId} />
    </Suspense>
  </div>
</PermissionGuard>
```

**Kenapa pakai PermissionGuard?**
1. **Security** - Prevent unauthorized access
2. **Consistency** - Same pattern across all protected pages
3. **User Experience** - Clear feedback when access denied

### **Tenant Isolation**

```typescript
// Di service layer
static async searchSchedules(tenantId: number, ...) {
  // Build where conditions - mulai dari tenant isolation
  let whereConditions = eq(class_schedules.tenant_id, tenantId);
  // ... rest of the method
}
```

**Kenapa tenant isolation penting?**
1. **Security** - Data tidak bocor antar tenant
2. **Compliance** - Meet regulatory requirements
3. **Performance** - Queries lebih cepat dengan proper filtering

### **API Route Security**

```typescript
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    // ... rest of the handler
  }
}
```

**Security layers:**
1. **Authentication** - User harus login
2. **Authorization** - User harus punya permission
3. **Tenant Isolation** - Data filtering by tenant
4. **Input Validation** - Zod schema validation

---

## 🚀 Testing & Troubleshooting

### **Common Issues & Solutions**

#### **1. "Permission Denied" Error**
```
❌ Problem: User tidak bisa akses halaman class schedules
✅ Solution: 
- Check user punya permission 'classes.manage'
- Check RBAC data loading properly
- Check admin bypass working
```

#### **2. "Class not found" Error saat Create**
```
❌ Problem: Error saat create schedule
✅ Solution:
- Check class_id valid dan belongs to tenant
- Check foreign key constraints
- Check tenant isolation working
```

#### **3. Form Validation Errors**
```
❌ Problem: Form tidak submit atau validation error
✅ Solution:
- Check Zod schema matches form fields
- Check required fields filled
- Check data types (string vs number)
```

#### **4. TanStack Query Not Updating**
```
❌ Problem: Data tidak update setelah create/edit
✅ Solution:
- Check query invalidation after mutations
- Check query keys consistent
- Check refetch() called properly
```

### **Debugging Tips**

#### **1. Enable Debug Logging**
```typescript
// Di service methods
console.log('🔍 Search params:', { tenantId, classId, search });
console.log('✅ Query result:', result);
```

#### **2. Check Network Tab**
- API calls returning correct data?
- HTTP status codes correct?
- Request/response format valid?

#### **3. Check React DevTools**
- TanStack Query DevTools untuk cache inspection
- Component state updates properly?
- Props passing correctly?

### **Performance Optimization**

#### **1. Query Optimization**
```typescript
// Use specific queries instead of generic ones
const { data } = useClassSchedulesByClass(classId); // ✅ Specific
// Instead of
const { data } = useClassSchedules(); // ❌ Too broad
```

#### **2. Proper Caching**
```typescript
// Set appropriate staleTime
staleTime: 5 * 60 * 1000, // 5 minutes for relatively static data
staleTime: 30 * 1000,     // 30 seconds for frequently changing data
```

#### **3. Conditional Fetching**
```typescript
// Only fetch when needed
enabled: !!classId && !!tenantId,
```

---

## 🎉 Kesimpulan

Selamat! Kita sudah berhasil implementasi Class Schedules dengan pola arsitektur modular yang solid. 

**Yang sudah kita pelajari:**
1. ✅ Database schema dengan proper relationships
2. ✅ Service layer dengan business logic validation
3. ✅ TanStack Query integration dengan caching
4. ✅ Reusable form components dengan validation
5. ✅ Management interface dengan advanced filtering
6. ✅ RBAC protection dan tenant isolation
7. ✅ Error handling dan troubleshooting

**Next Steps:**
1. 🧪 Write unit tests untuk service methods
2. 🎨 Add more advanced UI features (calendar view, drag & drop)
3. 📱 Make it mobile responsive
4. 🔔 Add real-time notifications
5. 📊 Add analytics dan reporting

**Remember:** Pattern ini bisa dipakai untuk implement fitur-fitur lain juga. Consistency is key! 🔑

---

**Happy Coding! 🚀**

*Dokumentasi ini dibuat dengan ❤️ menggunakan gaya mengajar Sandika Galih yang ramah untuk semua level programmer.*
