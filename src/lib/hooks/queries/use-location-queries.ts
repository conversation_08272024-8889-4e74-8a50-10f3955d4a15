import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Location, NewLocation, locations } from './../../db/schema';
import { locationKeys } from './query-keys';
const locationApi = {
    getByTenant: async (tenantId: number): Promise<Location[]> => {
      const response = await fetch(`/api/locations?tenantId=${tenantId}`);
      if (!response.ok) {
        throw new Error(`Failed to fetch locations: ${response.statusText}`);
      }
      const data = await response.json();
      return data.locations;
    },
  
    getAll: async (): Promise<Location[]> => {
      const response = await fetch('/api/locations');
      if (!response.ok) {
        throw new Error(`Failed to fetch locations: ${response.statusText}`);
      }
      const data = await response.json();
      return data.locations;
    },

    create: async (data: Omit<NewLocation, "id" | "createdAt">): Promise<Location> => {
      const response = await fetch('/api/locations', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create location');
      }
      const result = await response.json();
      return result.location;
    },

    update: async ({ locationId, data}: {
        locationId: string;
        data: Partial<NewLocation>
    }): Promise<Location> => {
      const response = await fetch(`/api/locations/${locationId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update location');
      }
      const result = await response.json();
      return result.location;
    },

    delete: async (locationId: string): Promise<Location> => {
      const response = await fetch(`/api/locations/${locationId}`, {
        method: 'DELETE',
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete location');
      }
      const result = await response.json();
      return result.location;
    },

    bulkCreate: async (locations: Omit<NewLocation, "id" | "createdAt">[]): Promise<Location[]> => {
    const response = await fetch('/api/locations/bulk', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ locations }),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to bulk create locations');
    }
    const result = await response.json();
    return result.locations;
  },

    getStats: async (): Promise<{
      totalLocations: number;
      activeLocations: number;
      inactiveLocations: number;
    }> => {
      const response = await fetch('/api/locations/stats');
      if (!response.ok) {
        throw new Error('Failed to fetch location stats');
      }
      return response.json();
    },
  };


  // Query Hooks with backward compatibility
export function useLocation(tenantIdOrOptions: number | null | { tenantId?: number }) {
  const tenantId = typeof tenantIdOrOptions === 'number'
    ? tenantIdOrOptions
    : (tenantIdOrOptions?.tenantId || null);

  const query = useQuery({
    queryKey: locationKeys.detail(tenantId || 0),
    queryFn: () => locationApi.getByTenant(tenantId!),
    enabled: !!tenantId && tenantId > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Return format compatible with old hook
  return {
    profile: query.data || null,
    loading: query.isLoading,
    error: query.error?.message || null,
    isLoading: query.isLoading,
    isError: query.isError,
    data: query.data,
    refetch: query.refetch,
  };
}


export function useLocations(options?: { tenantId?: number }) {
  const query = useQuery({
    queryKey: options?.tenantId
      ? locationKeys.byTenant(options.tenantId)
      : locationKeys.lists(),
    queryFn: options?.tenantId
      ? () => locationApi.getByTenant(options.tenantId!)
      : locationApi.getAll,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });

  // Return format compatible with old hook
  return {
    profiles: query.data || [],
    loading: query.isLoading,
    error: query.error?.message || null,
    isLoading: query.isLoading,
    isError: query.isError,
    data: query.data,
    refetch: query.refetch,
  };
}


export function useCreateLocation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: locationApi.create,
    onSuccess: (newLocation) => {
      // Update cache with new location
      queryClient.setQueryData(locationKeys.detail(newLocation.tenantId!), newLocation);

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: locationKeys.lists() });
      queryClient.invalidateQueries({
        queryKey: locationKeys.byTenant(newLocation.tenantId!)
      });
      queryClient.invalidateQueries({
        queryKey: locationKeys.stats(newLocation.tenantId!)
      });
    },
  });
}

export function useUpdateLocation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: locationApi.update,
    onSuccess: (updatedLocation) => {
      // Update cache with updated location
      queryClient.setQueryData(locationKeys.detail(updatedLocation.id), updatedLocation);

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: locationKeys.lists() });
      if (updatedLocation.tenantId) {
        queryClient.invalidateQueries({
          queryKey: locationKeys.byTenant(updatedLocation.tenantId)
        });
        queryClient.invalidateQueries({
          queryKey: locationKeys.stats(updatedLocation.tenantId)
        });
      }
    },
  });
}

export function useDeleteLocation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: locationApi.delete,
    onMutate: async (locationId: string) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: locationKeys.all });

      // Get the location data before deletion for rollback
      const previousLocation = queryClient.getQueryData(locationKeys.detail(locationId)) as Location;

      // Get all locations to update the list optimistically
      const previousLocations = queryClient.getQueryData(locationKeys.lists()) as Location[] | undefined;

      // Optimistically remove from all locations list
      if (previousLocations) {
        const updatedLocations = previousLocations.filter(loc => loc.id !== locationId);
        queryClient.setQueryData(locationKeys.lists(), updatedLocations);
      }

      // Remove from detail cache
      queryClient.removeQueries({ queryKey: locationKeys.detail(locationId) });

      return { previousLocation, previousLocations };
    },
    onError: (error, locationId, context) => {
      // Restore the previous state if deletion failed
      if (context?.previousLocations) {
        queryClient.setQueryData(locationKeys.lists(), context.previousLocations);
      }
      if (context?.previousLocation) {
        queryClient.setQueryData(locationKeys.detail(locationId), context.previousLocation);
      }
    },
    onSuccess: (deletedLocation) => {
      // Invalidate and refetch related queries to ensure consistency
      queryClient.invalidateQueries({ queryKey: locationKeys.lists() });

      if (deletedLocation.tenantId) {
        queryClient.invalidateQueries({
          queryKey: locationKeys.byTenant(deletedLocation.tenantId)
        });
        queryClient.invalidateQueries({
          queryKey: locationKeys.stats(deletedLocation.tenantId)
        });
      }
    },
  });
}

export function useBulkCreateAddresses() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: locationApi.bulkCreate,
    onSuccess: (newLocations) => {
      // Invalidate all address-related queries
      queryClient.invalidateQueries({ queryKey: locationKeys.all });
      
      // If all addresses belong to the same tenant, invalidate tenant-specific queries
      const tenantIds = [...new Set(newLocations.map(addr => addr.tenantId).filter(Boolean))];
      tenantIds.forEach(tenantId => {
        queryClient.invalidateQueries({ queryKey: locationKeys.byTenant(tenantId!) });
        queryClient.invalidateQueries({ queryKey: locationKeys.stats(tenantId!) });
      });
    },
  });
}