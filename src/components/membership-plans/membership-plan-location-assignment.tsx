"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { MapPin, Building2, Check, X, Save, Loader2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { useMembershipPlanLocations } from "@/lib/hooks/use-membership-plan-locations";
import { SuccessToast } from "@/components/ui/success-toast";

interface MembershipPlanLocationAssignmentProps {
  tenantId: number;
}

export function MembershipPlanLocationAssignment({ tenantId }: MembershipPlanLocationAssignmentProps) {
  const [assignments, setAssignments] = useState<Record<string, string[]>>({});
  const [showSuccessToast, setShowSuccessToast] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);

  const {
    membershipPlansWithLocations,
    availableLocations,
    isLoading,
    error,
    bulkUpdateAssignments,
    isBulkUpdating,
    bulkUpdateError,
  } = useMembershipPlanLocations();

  // Initialize assignments from current data
  useState(() => {
    if (membershipPlansWithLocations.length > 0) {
      const initialAssignments: Record<string, string[]> = {};
      membershipPlansWithLocations.forEach(plan => {
        initialAssignments[plan.id] = plan.assignedLocations.map(loc => loc.id);
      });
      setAssignments(initialAssignments);
    }
  });

  const handleLocationToggle = (planId: string, locationId: string, checked: boolean) => {
    setAssignments(prev => {
      const currentAssignments = prev[planId] || [];
      const newAssignments = checked
        ? [...currentAssignments, locationId]
        : currentAssignments.filter(id => id !== locationId);
      
      const updated = {
        ...prev,
        [planId]: newAssignments,
      };
      
      // Check if there are changes
      const hasChanges = membershipPlansWithLocations.some(plan => {
        const currentLocations = plan.assignedLocations.map(loc => loc.id).sort();
        const newLocations = (updated[plan.id] || []).sort();
        return JSON.stringify(currentLocations) !== JSON.stringify(newLocations);
      });
      
      setHasChanges(hasChanges);
      return updated;
    });
  };

  const handleSaveChanges = async () => {
    try {
      const assignmentData = Object.entries(assignments).map(([membershipPlanId, locationIds]) => ({
        membershipPlanId,
        locationIds,
      }));

      await bulkUpdateAssignments(assignmentData);
      
      setHasChanges(false);
      setShowSuccessToast(true);
      setTimeout(() => setShowSuccessToast(false), 4000);
    } catch (error) {
      console.error("Error saving assignments:", error);
    }
  };

  const handleResetChanges = () => {
    const initialAssignments: Record<string, string[]> = {};
    membershipPlansWithLocations.forEach(plan => {
      initialAssignments[plan.id] = plan.assignedLocations.map(loc => loc.id);
    });
    setAssignments(initialAssignments);
    setHasChanges(false);
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <p className="text-red-600">Error loading data: {error.message}</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Location Assignments</h2>
          <p className="text-muted-foreground">
            Assign membership plans to specific locations
          </p>
        </div>
        
        {hasChanges && (
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={handleResetChanges}>
              Reset
            </Button>
            <Button onClick={handleSaveChanges} disabled={isBulkUpdating}>
              {isBulkUpdating ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <Save className="h-4 w-4 mr-2" />
              )}
              Save Changes
            </Button>
          </div>
        )}
      </div>

      {/* Error Display */}
      {bulkUpdateError && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <p className="text-red-600">Error: {bulkUpdateError.message}</p>
          </CardContent>
        </Card>
      )}

      {/* Assignment Grid */}
      <div className="grid gap-6">
        {membershipPlansWithLocations.map((plan) => (
          <Card key={plan.id}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Building2 className="h-5 w-5" />
                    {plan.name}
                  </CardTitle>
                  {plan.description && (
                    <p className="text-sm text-muted-foreground mt-1">
                      {plan.description}
                    </p>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant={plan.is_active ? "default" : "secondary"}>
                    {plan.is_active ? "Active" : "Inactive"}
                  </Badge>
                  <Badge variant="outline">
                    {(assignments[plan.id] || []).length} / {availableLocations.length} locations
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <Label className="text-sm font-medium">Assigned Locations</Label>
                
                {availableLocations.length === 0 ? (
                  <p className="text-muted-foreground text-sm">No locations available</p>
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                    {availableLocations.map((location) => {
                      const isAssigned = (assignments[plan.id] || []).includes(location.id);
                      
                      return (
                        <motion.div
                          key={location.id}
                          layout
                          className={`flex items-center space-x-3 p-3 rounded-lg border transition-colors ${
                            isAssigned 
                              ? "bg-blue-50 border-blue-200" 
                              : "bg-gray-50 border-gray-200 hover:bg-gray-100"
                          }`}
                        >
                          <Checkbox
                            id={`${plan.id}-${location.id}`}
                            checked={isAssigned}
                            onCheckedChange={(checked) => 
                              handleLocationToggle(plan.id, location.id, checked as boolean)
                            }
                          />
                          <Label
                            htmlFor={`${plan.id}-${location.id}`}
                            className="flex items-center gap-2 cursor-pointer flex-1"
                          >
                            <MapPin className="h-4 w-4 text-muted-foreground" />
                            <span className="text-sm">{location.name || "Unnamed Location"}</span>
                          </Label>
                          {isAssigned && (
                            <Check className="h-4 w-4 text-blue-600" />
                          )}
                        </motion.div>
                      );
                    })}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {membershipPlansWithLocations.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <Building2 className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">No membership plans found</h3>
            <p className="text-muted-foreground">
              Create membership plans first to assign them to locations
            </p>
          </CardContent>
        </Card>
      )}

      {/* Success Toast */}
      <AnimatePresence>
        {showSuccessToast && (
          <SuccessToast
            message="Location assignments updated successfully!"
            onClose={() => setShowSuccessToast(false)}
          />
        )}
      </AnimatePresence>
    </div>
  );
}
