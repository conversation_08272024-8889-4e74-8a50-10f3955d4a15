# Next.js 15 SaaS Template

A comprehensive, production-ready SaaS template built with Next.js 15, featuring authentication, billing, multi-tenancy, and everything you need to launch your SaaS product quickly.

## 🚀 Features

### Core Features
- ⚡ **Next.js 15** with App Router and Turbopack
- 🔐 **NextAuth v5** with multiple providers (Credentials, Google, GitHub)
- 💳 **Stripe Integration** for billing and subscriptions
- 🏢 **Multi-tenancy** with organization management
- 📱 **Responsive Design** with Tailwind CSS
- 🌙 **Dark/Light Mode** with theme switching
- 🔒 **Role-based Access Control** (Admin, User, Owner)
- 📧 **Email Integration** (Resend/Brevo)
- 🛡️ **Security Features** (CSRF, Rate limiting, Input validation)

### Database & Storage
- 🗄️ **SQLite** with Drizzle ORM (easily switchable to PostgreSQL)
- ☁️ **Cloudflare Integration** (D1, KV, Workers)
- 📊 **Database Migrations** and seeding

### SEO & Performance
- 🔍 **SEO Optimized** with meta tags, OpenGraph, Twitter Cards
- 🗺️ **Sitemap** and robots.txt generation
- 📱 **PWA Support** with manifest
- 🌐 **Internationalization** (English, Indonesian)
- 📈 **Structured Data** (JSON-LD)

### Developer Experience
- 🔧 **TypeScript** for type safety
- 🎨 **Tailwind CSS** for styling
- 📦 **Component Library** with shadcn/ui
- 🧪 **Testing Setup** ready
- 📝 **ESLint & Prettier** configured
- 🔄 **Hot Reload** with Turbopack

## 🛠️ Tech Stack

- **Framework**: Next.js 15
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: shadcn/ui
- **Authentication**: NextAuth v5
- **Database**: SQLite (Drizzle ORM)
- **Payments**: Stripe
- **Email**: Resend/Brevo
- **Deployment**: Cloudflare (recommended)

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- npm/yarn/pnpm

### Installation

1. **Clone the repository**
   ```bash
   git clone <your-repo-url>
   cd starter
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Setup environment variables**
   ```bash
   cp .env.example .env.local
   ```

   Fill in your environment variables in `.env.local`:
   ```env
   # Database
   DATABASE_URL="./dev.db"

   # Authentication
   NEXTAUTH_SECRET="your-super-secret-key"
   NEXTAUTH_URL="http://localhost:3000"
   AUTH_SECRET="your-super-secret-key"

   # OAuth Providers
   GOOGLE_CLIENT_ID="your-google-client-id"
   GOOGLE_CLIENT_SECRET="your-google-client-secret"

   # Stripe
   STRIPE_SECRET_KEY="sk_test_..."
   STRIPE_PUBLISHABLE_KEY="pk_test_..."
   STRIPE_WEBHOOK_SECRET="whsec_..."

   # Email
   RESEND_API_KEY="your-resend-api-key"
   FROM_EMAIL="<EMAIL>"
   ```

4. **Setup database**
   ```bash
   npm run db:generate
   npm run db:migrate
   npm run db:seed
   ```

5. **Start development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**
   Navigate to `http://localhost:3000`

## 🔐 Authentication

The template includes a complete authentication system:

### Default Test Account
- **Email**: <EMAIL>
- **Password**: admin123
- **Role**: Admin

### Supported Providers
- Email/Password (Credentials)
- Google OAuth
- GitHub OAuth (configurable)

### Features
- User registration and login
- Password reset
- Email verification
- Role-based access control
- Session management
- Protected routes

## 💳 Billing & Subscriptions

Integrated Stripe billing system:

- Credit-based billing model
- Subscription management
- Invoice generation
- Payment method management
- Webhook handling
- Usage tracking

## 🏢 Multi-tenancy

Complete organization management:

- Create and manage organizations
- Invite team members
- Role-based permissions (Owner, Admin, User)
- Organization switching
- Isolated data per organization

## 🌐 Internationalization

Built-in i18n support:

- English (default)
- Indonesian
- Easy to add more languages
- Language switcher component
- Localized routes
- RTL support ready

## 📱 Responsive Design

- Mobile-first approach
- Tablet and desktop optimized
- Touch-friendly interfaces
- Progressive Web App (PWA) ready

## 🔒 Security

- CSRF protection
- Rate limiting
- Input validation with Zod
- SQL injection prevention
- XSS protection
- Secure headers

## 📈 SEO & Performance

- Server-side rendering (SSR)
- Static site generation (SSG)
- Optimized images
- Meta tags and OpenGraph
- Structured data (JSON-LD)
- Sitemap generation
- Core Web Vitals optimized

## 🚀 Deployment

### Recommended: Cloudflare Pages

1. Connect your GitHub repository
2. Set environment variables
3. Deploy automatically

### Alternative: Vercel

1. Connect your GitHub repository
2. Configure environment variables
3. Deploy with zero configuration

---

**Happy coding! 🚀**
