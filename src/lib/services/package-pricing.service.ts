import { sql, eq, and, ilike, SQL, inArray } from "drizzle-orm";
import { package_pricing, packages, pricing_groups, type PackagePricing, type NewPackagePricing } from "@/lib/db/schema";
import { BaseService, CreateEntityData, UpdateEntityData } from "@/lib/core/base-service";
import { createId } from "@paralleldrive/cuid2";
import { db } from "@/lib/db";

// Package Pricing data transfer objects
export interface CreatePackagePricingData extends CreateEntityData {
  packageId: string;
  pricingGroupId: string;
  price?: number;
  creditAmount?: number;
  currency?: string;
}

export interface UpdatePackagePricingData extends UpdateEntityData {
  packageId?: string;
  pricingGroupId?: string;
  price?: number;
  creditAmount?: number;
  currency?: string;
}

export interface PackagePricingFilters {
  search?: string;
  packageId?: string;
  pricingGroupId?: string;
  tenantId?: number;
  minPrice?: number;
  maxPrice?: number;
  currency?: string;
}

export interface PackagePricingStats {
  total: number;
  byPackage: Record<string, number>;
  byPricingGroup: Record<string, number>;
  averagePrice: number;
  totalRevenue: number;
  currencies: string[];
}

export interface PackagePricingWithDetails extends PackagePricing {
  package?: {
    id: string;
    name: string;
    description?: string;
    tenantId: number;
  };
  pricingGroup?: {
    id: string;
    name: string;
    description?: string;
    discountPercentage: number;
  };
}

/**
 * PackagePricingService - Package pricing management
 *
 * Features:
 * - CRUD operations for package pricing
 * - Package and pricing group relationships
 * - Price calculations and currency handling
 * - Search and filtering
 * - Statistics and analytics
 */
export class PackagePricingService extends BaseService<PackagePricing, CreatePackagePricingData, UpdatePackagePricingData> {
  constructor() {
    super(package_pricing, "PackagePricing");
  }



  /**
   * Create a new package pricing
   */
  async create(data: CreatePackagePricingData): Promise<PackagePricing> {
    console.log(`💰 [PackagePricingService] Creating package pricing:`, data);

    this.validateCreateData(data);

    // Check if pricing already exists for this package and pricing group combination
    const existing = await this.getByPackageAndPricingGroup(data.packageId, data.pricingGroupId);
    if (existing) {
      throw new Error("Pricing already exists for this package and pricing group combination");
    }

    const pricingData: NewPackagePricing = {
      id: createId(),
      package_id: data.packageId,
      pricing_group_id: data.pricingGroupId,
      price: data.price || null,
      credit_amount: data.creditAmount || null,
      currency: data.currency || "USD",
    };

    const [pricing] = await db.insert(package_pricing).values(pricingData).returning();
    console.log(`✅ [PackagePricingService] Package pricing created:`, pricing);
    return pricing;
  }

  /**
   * Update package pricing
   */
  async update(id: string, data: UpdatePackagePricingData): Promise<PackagePricing> {
    console.log(`💰 [PackagePricingService] Updating package pricing ${id}:`, data);

    this.validateUpdateData(data);

    // Get current pricing
    const current = await this.getById(id);
    if (!current) {
      throw new Error("Package pricing not found");
    }

    const updateData: Partial<NewPackagePricing> = {};
    if (data.packageId !== undefined) updateData.package_id = data.packageId;
    if (data.pricingGroupId !== undefined) updateData.pricing_group_id = data.pricingGroupId;
    if (data.price !== undefined) updateData.price = data.price;
    if (data.creditAmount !== undefined) updateData.credit_amount = data.creditAmount;
    if (data.currency !== undefined) updateData.currency = data.currency;

    // Check for duplicate if package or pricing group is being changed
    if (data.packageId || data.pricingGroupId) {
      const newPackageId = data.packageId || current.package_id;
      const newPricingGroupId = data.pricingGroupId || current.pricing_group_id;
      const existing = await this.getByPackageAndPricingGroup(newPackageId, newPricingGroupId);
      if (existing && existing.id !== id) {
        throw new Error("Pricing already exists for this package and pricing group combination");
      }
    }

    const [updatedPricing] = await db
      .update(package_pricing)
      .set({
        ...updateData,
        updatedAt: new Date(),
      })
      .where(eq(package_pricing.id, id))
      .returning();

    if (!updatedPricing) {
      throw new Error("Package pricing not found");
    }

    console.log(`✅ [PackagePricingService] Package pricing updated:`, updatedPricing);
    return updatedPricing;
  }

  /**
   * Get package pricing by package and pricing group
   */
  async getByPackageAndPricingGroup(packageId: string, pricingGroupId: string): Promise<PackagePricing | null> {
    const [result] = await db
      .select()
      .from(package_pricing)
      .where(
        and(
          eq(package_pricing.package_id, packageId),
          eq(package_pricing.pricing_group_id, pricingGroupId)
        )
      )
      .limit(1);

    return result || null;
  }

  /**
   * Get package pricing with package category and pricing group details
   */
  async getWithDetails(id: string): Promise<PackagePricingWithDetails | null> {
    const [result] = await db
      .select({
        id: package_pricing.id,
        package_id: package_pricing.package_id,
        pricing_group_id: package_pricing.pricing_group_id,
        price: package_pricing.price,
        credit_amount: package_pricing.credit_amount,
        currency: package_pricing.currency,
        createdAt: package_pricing.createdAt,
        updatedAt: package_pricing.updatedAt,
        package: {
          id: packages.id,
          name: packages.name,
          description: packages.description,
          tenantId: packages.tenantId,
        },
        pricingGroup: {
          id: pricing_groups.id,
          name: pricing_groups.name,
          description: pricing_groups.description,
          discountPercentage: pricing_groups.discountPercentage,
        },
      })
      .from(package_pricing)
      .leftJoin(packages, eq(package_pricing.package_id, packages.id))
      .leftJoin(pricing_groups, eq(package_pricing.pricing_group_id, pricing_groups.id))
      .where(eq(package_pricing.id, id))
      .limit(1);

    return result || null;
  }

  /**
   * Get package pricing by tenant with filters
   */
  async getByTenant(tenantId: number, filters: PackagePricingFilters = {}): Promise<PackagePricingWithDetails[]> {
    console.log(`💰 [PackagePricingService] Getting package pricing for tenant ${tenantId}:`, filters);

    const conditions: SQL[] = [];

    // Add tenant filter through packages table
    conditions.push(eq(packages.tenantId, tenantId));

    if (filters.pricingGroupId) {
      conditions.push(eq(package_pricing.pricing_group_id, filters.pricingGroupId));
    }

    if (filters.minPrice !== undefined) {
      conditions.push(sql`${package_pricing.price} >= ${filters.minPrice}`);
    }

    if (filters.maxPrice !== undefined) {
      conditions.push(sql`${package_pricing.price} <= ${filters.maxPrice}`);
    }

    if (filters.currency) {
      conditions.push(eq(package_pricing.currency, filters.currency));
    }

    if (filters.search) {
      conditions.push(
        sql`(
          ${ilike(packages.name, `%${filters.search}%`)} OR
          ${ilike(pricing_groups.name, `%${filters.search}%`)}
        )`
      );
    }

    const result = await db
      .select({
        id: package_pricing.id,
        package_id: package_pricing.package_id,
        pricing_group_id: package_pricing.pricing_group_id,
        price: package_pricing.price,
        credit_amount: package_pricing.credit_amount,
        currency: package_pricing.currency,
        createdAt: package_pricing.createdAt,
        updatedAt: package_pricing.updatedAt,
        package: {
          id: packages.id,
          name: packages.name,
          description: packages.description,
          tenantId: packages.tenantId,
        },
        pricingGroup: {
          id: pricing_groups.id,
          name: pricing_groups.name,
          description: pricing_groups.description,
          discountPercentage: pricing_groups.discountPercentage,
        },
      })
      .from(package_pricing)
      .leftJoin(packages, eq(package_pricing.package_id, packages.id))
      .leftJoin(pricing_groups, eq(package_pricing.pricing_group_id, pricing_groups.id))
      .where(and(...conditions))
      .orderBy(packages.name, pricing_groups.name);

    console.log(`✅ [PackagePricingService] Found ${result.length} package pricing records`);
    return result;
  }



  /**
   * Validate create data
   */
  protected validateCreateData(data: CreatePackagePricingData): void {
    if (!data.packageId?.trim()) {
      throw new Error("Package ID is required");
    }
    if (!data.pricingGroupId?.trim()) {
      throw new Error("Pricing group ID is required");
    }
    if (data.price !== undefined && data.price < 0) {
      throw new Error("Price cannot be negative");
    }
    if (data.creditAmount !== undefined && data.creditAmount < 0) {
      throw new Error("Credit amount cannot be negative");
    }
    if (data.price === undefined && data.creditAmount === undefined) {
      throw new Error("Either price or credit amount must be specified");
    }
  }

  /**
   * Validate update data
   */
  protected validateUpdateData(data: UpdatePackagePricingData): void {
    if (data.packageId !== undefined && !data.packageId?.trim()) {
      throw new Error("Package ID cannot be empty");
    }
    if (data.pricingGroupId !== undefined && !data.pricingGroupId?.trim()) {
      throw new Error("Pricing group ID cannot be empty");
    }
    if (data.price !== undefined && data.price < 0) {
      throw new Error("Price cannot be negative");
    }
    if (data.creditAmount !== undefined && data.creditAmount < 0) {
      throw new Error("Credit amount cannot be negative");
    }
  }

  /**
   * Bulk create package pricing
   */
  async bulkCreate(data: CreatePackagePricingData[]): Promise<PackagePricing[]> {
    console.log(`💰 [PackagePricingService] Bulk creating ${data.length} package pricing records`);

    // Validate all data first
    for (const item of data) {
      if (!item.packageId || !item.pricingGroupId) {
        throw new Error('Package ID and Pricing Group ID are required for all items');
      }
      if (!item.price && !item.creditAmount) {
        throw new Error('Either price or credit amount must be specified for all items');
      }
    }

    // Check for duplicates within the batch
    const combinations = new Set();
    for (const item of data) {
      const key = `${item.packageId}-${item.pricingGroupId}`;
      if (combinations.has(key)) {
        throw new Error(`Duplicate package-pricing group combination found: ${key}`);
      }
      combinations.add(key);
    }

    // Check for existing combinations in database
    const existingCombinations = await db
      .select({
        package_id: package_pricing.package_id,
        pricing_group_id: package_pricing.pricing_group_id,
      })
      .from(package_pricing);

    const existingKeys = new Set(
      existingCombinations.map(item => `${item.package_id}-${item.pricing_group_id}`)
    );

    for (const item of data) {
      const key = `${item.packageId}-${item.pricingGroupId}`;
      if (existingKeys.has(key)) {
        throw new Error(`Package pricing already exists for package ${item.packageId} and pricing group ${item.pricingGroupId}`);
      }
    }

    // Prepare insert data
    const insertData = data.map(item => ({
      id: createId(),
      package_id: item.packageId,
      pricing_group_id: item.pricingGroupId,
      price: item.price || null,
      credit_amount: item.creditAmount || null,
      currency: item.currency || 'USD',
    }));

    // Insert all records
    const results = await db
      .insert(package_pricing)
      .values(insertData)
      .returning();

    console.log(`✅ [PackagePricingService] Successfully created ${results.length} package pricing records`);
    return results;
  }

  /**
   * Bulk delete package pricing
   */
  async bulkDelete(ids: string[]): Promise<PackagePricing[]> {
    console.log(`💰 [PackagePricingService] Bulk deleting ${ids.length} package pricing records`);

    if (ids.length === 0) {
      throw new Error('No IDs provided for bulk delete');
    }

    // Get records before deletion for return
    const recordsToDelete = await db
      .select()
      .from(package_pricing)
      .where(inArray(package_pricing.id, ids));

    if (recordsToDelete.length === 0) {
      throw new Error('No package pricing records found with the provided IDs');
    }

    // Delete records
    const deletedRecords = await db
      .delete(package_pricing)
      .where(inArray(package_pricing.id, ids))
      .returning();

    console.log(`✅ [PackagePricingService] Successfully deleted ${deletedRecords.length} package pricing records`);
    return deletedRecords;
  }

  /**
   * Bulk update package pricing
   */
  async bulkUpdate(updates: { id: string; data: UpdatePackagePricingData }[]): Promise<PackagePricing[]> {
    console.log(`💰 [PackagePricingService] Bulk updating ${updates.length} package pricing records`);

    if (updates.length === 0) {
      throw new Error('No updates provided for bulk update');
    }

    const results: PackagePricing[] = [];

    // Process updates one by one (could be optimized with a single query if needed)
    for (const update of updates) {
      const result = await this.update(update.id, update.data);
      if (result) {
        results.push(result);
      }
    }

    console.log(`✅ [PackagePricingService] Successfully updated ${results.length} package pricing records`);
    return results;
  }

  /**
   * Bulk operation handler
   */
  async bulkOperation(operation: {
    action: 'create' | 'delete' | 'update';
    data?: CreatePackagePricingData[];
    ids?: string[];
    updates?: { id: string; data: UpdatePackagePricingData }[];
  }): Promise<PackagePricing[]> {
    console.log(`💰 [PackagePricingService] Performing bulk operation: ${operation.action}`);

    switch (operation.action) {
      case 'create':
        if (!operation.data || operation.data.length === 0) {
          throw new Error('Data is required for bulk create operation');
        }
        return this.bulkCreate(operation.data);

      case 'delete':
        if (!operation.ids || operation.ids.length === 0) {
          throw new Error('IDs are required for bulk delete operation');
        }
        return this.bulkDelete(operation.ids);

      case 'update':
        if (!operation.updates || operation.updates.length === 0) {
          throw new Error('Updates are required for bulk update operation');
        }
        return this.bulkUpdate(operation.updates);

      default:
        throw new Error(`Unsupported bulk operation: ${operation.action}`);
    }
  }

  /**
   * Reorder package pricing items
   */
  async reorder(tenantId: number, reorderData: { id: string; sortOrder: number }[]): Promise<PackagePricing[]> {
    console.log(`💰 [PackagePricingService] Reordering package pricing for tenant ${tenantId}:`, reorderData);

    return await this.db.transaction(async (tx) => {
      const updatedPackagePricing = [];

      for (const item of reorderData) {
        // Verify the package pricing belongs to the tenant before updating
        const existingPricing = await tx
          .select({ id: package_pricing.id })
          .from(package_pricing)
          .innerJoin(packages, eq(package_pricing.package_id, packages.id))
          .where(
            and(
              eq(package_pricing.id, item.id),
              eq(packages.tenantId, tenantId)
            )
          )
          .limit(1);

        if (existingPricing.length === 0) {
          console.warn(`💰 [PackagePricingService] Package pricing ${item.id} not found for tenant ${tenantId}`);
          continue;
        }

        const [updated] = await tx
          .update(package_pricing)
          .set({
            sortOrder: item.sortOrder,
            updatedAt: new Date(),
          })
          .where(eq(package_pricing.id, item.id))
          .returning();

        if (updated) {
          updatedPackagePricing.push(updated);
        }
      }

      console.log(`💰 [PackagePricingService] Successfully reordered ${updatedPackagePricing.length} package pricing items`);
      return updatedPackagePricing;
    });
  }
}

// Export singleton instance
export const packagePricingService = new PackagePricingService();
