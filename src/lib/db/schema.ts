// import { appointment } from './schema';
import { relations, sql } from "drizzle-orm";
import { pgTable, text, integer, real, timestamp, boolean, varchar, jsonb, primaryKey, serial, date, unique, index, check, uniqueIndex } from "drizzle-orm/pg-core";
import { createId } from "@paralleldrive/cuid2";
import { id } from "zod/v4/locales";
import { time } from "drizzle-orm/mysql-core";

// Type definitions for JSONB fields
export interface ClassItemToBring {
  id: string;
  item_name: string;
  is_required: boolean;
}

export interface ClassYoutubeLinkData {
  id: string;
  yt_url: string;
}

export interface PackageScheduleAvailabilityData {
  id: string;
  start_date?: string; // ISO date string
  start_time?: string; // ISO datetime string
  end_date?: string; // ISO date string
  end_time?: string; // ISO datetime string
}

export interface PackageIncludedClass {
  id: string; // class ID
  name: string; // class name untuk display
  category?: string; // category name untuk grouping
  included_at: string; // timestamp kapan ditambahkan
}

// RBAC Permission Data Structure
export interface PermissionData {
  module: string; // e.g., "classes", "customers", "packages"
  actions: string[]; // e.g., ["create", "read", "update", "delete"]
  conditions?: {
    own_only?: boolean; // hanya data milik sendiri
    location_restricted?: boolean; // terbatas pada location tertentu
    tenant_restricted?: boolean; // terbatas pada tenant tertentu
  };
}


//

// Location Access Data Structure
export interface LocationAccessData {
  location_id: string;
  location_name: string;
  access_level: "full" | "read_only" | "restricted"; // level akses
  granted_at: string; // timestamp kapan akses diberikan
}

// Users table
export const users = pgTable("users", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  email: varchar("email", { length: 255 }).notNull().unique(),
  emailVerified: timestamp("email_verified"),
  name: varchar("name", { length: 255 }),
  image: text("image"),
  password: text("password"), // For email/password auth
  role: varchar("role", { length: 50 }).default("user").notNull(),
  organizationId: varchar("organization_id", { length: 255 }).references(() => organizations.id),
  tenantId: integer("tenant_id").references(() => tenants.id, { onDelete: "cascade" }),
  locationId: varchar("location_id", { length: 255 }).references(() => locations.id),
  credits: integer("credits").default(0).notNull(),
  monthlyCredits: integer("monthly_credits").default(100).notNull(),
  lastCreditRefresh: timestamp("last_credit_refresh"),

  employeeId: varchar("employee_id", { length: 100 }), // ID karyawan jika ada
  departement: varchar("departement", { length: 100 }), // ID karyawan jika ada
  position: varchar("position", { length: 100 }), // ID karyawan jika ada
  hireDate: timestamp("hire_date"), // tanggal di-hire

    // Contact and emergency information
  phone: varchar("phone", { length: 50 }),
  emergencyContact: jsonb("emergency_contact").$type<{
    name?: string;
    relationship?: string;
    phone?: string;
    email?: string;
  }>(),
  
  // Professional details
  certifications: jsonb("certifications").$type<Array<{
    name: string;
    issuer: string;
    issuedDate: string;
    expiryDate?: string;
    credentialId?: string;
  }>>(),
  specializations: jsonb("specializations").$type<string[]>(),
  bio: text("bio"),


   // System access and permissions
  permissions: jsonb("permissions").$type<string[]>(),
  accessibleLocations: jsonb("accessible_locations").$type<string[]>(),
  
  // Credits and billing (if applicable)
  lastLoginAt: timestamp("last_login_at"),
  lastLoginIP: varchar("last_login_ip", { length: 45 }),
  failedLoginAttempts: integer("failed_login_attempts").default(0).notNull(),
  lockedAt: timestamp("locked_at"),
  lockedUntil: timestamp("locked_until"),
  
  // MFA settings
  // 
  mfaEnabled: boolean("mfa_enabled").default(false).notNull(),
  mfaSecret: text("mfa_secret"), // Encrypted TOTP secret
  backupCodes: jsonb("backup_codes").$type<string[]>(),
  


  isActive: boolean("is_active").default(true).notNull(),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  createdBy: varchar("created_by", { length: 255 }),
  updatedBy: varchar("updated_by", { length: 255 }),
});

// Organizations table for multi-tenancy
export const organizations = pgTable("organizations", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  name: varchar("name", { length: 255 }).notNull(),
  slug: varchar("slug", { length: 255 }).notNull().unique(),
  description: text("description"),
  logo: text("logo"),
  plan: varchar("plan", { length: 50 }).default("free").notNull(),
  credits: integer("credits").default(0).notNull(),
  monthlyCredits: integer("monthly_credits").default(1000).notNull(),
  lastCreditRefresh: timestamp("last_credit_refresh"),
  isActive: boolean("is_active").default(true).notNull(),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});

// Tenants table for multi-tenant architecture
export const tenants = pgTable("tenants", {
  id: serial("id").primaryKey(),
  name: varchar("name", { length: 255 }).notNull(),
  subdomain: varchar("subdomain", { length: 255 }).notNull().unique(),
  organizationId: varchar("organization_id", { length: 255 }).references(() => organizations.id, { onDelete: "cascade" }),
  customDomain: varchar("custom_domain", { length: 255 }),
  // settings: jsonb("settings"), // Store tenant-specific settings

  // Tenant configuration
  settings: jsonb("settings").$type<{
    allowGoogleOAuth?: boolean;
    allowedDomains?: string[];
    requireEmailVerification?: boolean;
    sessionTimeout?: number;
    mfa?: boolean;
    customBranding?: {
      logo?: string;
      primaryColor?: string;
      secondaryColor?: string;
    };
  }>(),
  subscriptionTier: varchar("subscription_tier", { length: 50 }).default("basic").notNull(),
  maxUsers: integer("max_users").default(100).notNull(),
  maxCustomers: integer("max_customers").default(1000).notNull(),

  // Security settings
  passwordPolicy: jsonb("password_policy").$type<{
    minLength?: number;
    requireUppercase?: boolean;
    requireLowercase?: boolean;
    requireNumbers?: boolean;
    requireSymbols?: boolean;
    maxAge?: number;
  }>(),

  isActive: boolean("is_active").default(true).notNull(),

  // Audit field
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});

// Organization members
export const organizationMembers = pgTable("organization_members", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  organizationId: varchar("organization_id", { length: 255 }).notNull().references(() => organizations.id, { onDelete: "cascade" }),
  userId: varchar("user_id", { length: 255 }).notNull().references(() => users.id, { onDelete: "cascade" }),
  role: varchar("role", { length: 50 }).default("member").notNull(),
  invitedBy: varchar("invited_by", { length: 255 }).references(() => users.id),
  joinedAt: timestamp("joined_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});

// Accounts table for OAuth
export const accounts = pgTable("accounts", {
  userId: varchar("user_id", { length: 255 }).notNull().references(() => users.id, { onDelete: "cascade" }),
  type: varchar("type", { length: 255 }).notNull(),
  provider: varchar("provider", { length: 255 }).notNull(),
  providerAccountId: varchar("provider_account_id", { length: 255 }).notNull(),
  refresh_token: text("refresh_token"),
  access_token: text("access_token"),
  expires_at: integer("expires_at"),
  token_type: varchar("token_type", { length: 255 }),
  scope: varchar("scope", { length: 255 }),
  id_token: text("id_token"),
  session_state: varchar("session_state", { length: 255 }),
}, (account) => ({
  compoundKey: primaryKey({
    columns: [account.provider, account.providerAccountId],
  }),
}));

// Sessions table for NextAuth
export const sessions = pgTable("sessions", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  sessionToken: varchar("session_token", { length: 255 }).notNull().unique(),
  tenantId: integer("tenant_id").references(() => tenants.id, { onDelete: "cascade" }).notNull(),
  
  // User information (polymorphic - can be customer or staff user)
  userId: varchar("user_id", { length: 255 }).notNull(),
  userType: varchar("user_type", { length: 50 }).notNull(), // "customer" or "staff"
  
  // Session metadata
  expires: timestamp("expires").notNull(),
  ipAddress: varchar("ip_address", { length: 45 }),
  userAgent: text("user_agent"),
  
  // Security tracking
  isActive: boolean("is_active").default(true).notNull(),
  lastActivity: timestamp("last_activity").default(sql`CURRENT_TIMESTAMP`),
  
  // Audit fields
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});




// Verification tokens table for NextAuth
export const verificationTokens = pgTable("verification_tokens", {
  identifier: varchar("identifier", { length: 255 }).notNull(), // email or user ID
  token: varchar("token", { length: 255 }).notNull().unique(),
  expires: timestamp("expires").notNull(),
  
  // Token context
  tenantId: integer("tenant_id").references(() => tenants.id, { onDelete: "cascade" }).notNull(),
  type: varchar("type", { length: 50 }).notNull(), // "email_verification", "password_reset", "mfa"
  userType: varchar("user_type", { length: 50 }).notNull(), // "customer" or "staff"
  
  // Token metadata
  metadata: jsonb("metadata").$type<Record<string, any>>(),
  usedAt: timestamp("used_at"),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});

// Credit transactions table
export const creditTransactions = pgTable("credit_transactions", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  userId: varchar("user_id", { length: 255 }).notNull().references(() => users.id, { onDelete: "cascade" }),
  organizationId: varchar("organization_id", { length: 255 }).references(() => organizations.id, { onDelete: "cascade" }),
  type: varchar("type", { length: 50 }).notNull(), // 'purchase', 'usage', 'refund', 'bonus'
  amount: integer("amount").notNull(), // Positive for credits added, negative for credits used
  description: text("description"),
  metadata: jsonb("metadata"), // Store additional data like Stripe payment ID, etc.
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});

// Stripe customers table
export const stripeCustomers = pgTable("stripe_customers", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  userId: varchar("user_id", { length: 255 }).references(() => users.id, { onDelete: "cascade" }),
  organizationId: varchar("organization_id", { length: 255 }).references(() => organizations.id, { onDelete: "cascade" }),
  stripeCustomerId: varchar("stripe_customer_id", { length: 255 }).notNull().unique(),
  email: varchar("email", { length: 255 }).notNull(),
  name: varchar("name", { length: 255 }),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});


// ========================================
// TENANT DOMAIN MAPPINGS TABLE
// ========================================
export const tenantDomains = pgTable("tenant_domains", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  tenantId: integer("tenant_id").references(() => tenants.id, { onDelete: "cascade" }).notNull(),
  domain: varchar("domain", { length: 255 }).notNull().unique(),
  type: varchar("type", { length: 50 }).notNull(), // "subdomain" or "custom"
  isVerified: boolean("is_verified").default(false).notNull(),
  isPrimary: boolean("is_primary").default(false).notNull(),
  
  // SSL and verification
  sslEnabled: boolean("ssl_enabled").default(false).notNull(),
  verificationToken: varchar("verification_token", { length: 255 }),
  verifiedAt: timestamp("verified_at"),
  
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});





// Stripe payments table
export const stripePayments = pgTable("stripe_payments", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  stripePaymentIntentId: varchar("stripe_payment_intent_id", { length: 255 }).notNull().unique(),
  stripeCustomerId: varchar("stripe_customer_id", { length: 255 }).notNull(),
  userId: varchar("user_id", { length: 255 }).references(() => users.id),
  organizationId: varchar("organization_id", { length: 255 }).references(() => organizations.id),
  amount: integer("amount").notNull(), // Amount in cents
  currency: varchar("currency", { length: 3 }).default("usd").notNull(),
  status: varchar("status", { length: 50 }).notNull(),
  credits: integer("credits").notNull(), // Credits purchased
  metadata: jsonb("metadata"),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});

// Legacy API keys table for internal application access
export const legacyApiKeys = pgTable("legacy_api_keys", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  userId: varchar("user_id", { length: 255 }).notNull().references(() => users.id, { onDelete: "cascade" }),
  organizationId: varchar("organization_id", { length: 255 }).references(() => organizations.id, { onDelete: "cascade" }),
  name: varchar("name", { length: 255 }).notNull(),
  key: varchar("key", { length: 255 }).notNull().unique(),
  lastUsed: timestamp("last_used"),
  isActive: boolean("is_active").default(true).notNull(),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});

// Usage logs table for tracking API usage
export const usageLogs = pgTable("usage_logs", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  userId: varchar("user_id", { length: 255 }).references(() => users.id, { onDelete: "cascade" }),
  organizationId: varchar("organization_id", { length: 255 }).references(() => organizations.id, { onDelete: "cascade" }),
  apiKeyId: varchar("api_key_id", { length: 255 }).references(() => legacyApiKeys.id, { onDelete: "cascade" }),
  endpoint: varchar("endpoint", { length: 255 }).notNull(),
  method: varchar("method", { length: 10 }).notNull(),
  statusCode: integer("status_code").notNull(),
  creditsUsed: integer("credits_used").default(0).notNull(),
  responseTime: integer("response_time"), // in milliseconds
  userAgent: text("user_agent"),
  ipAddress: varchar("ip_address", { length: 45 }),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});

// Invitations table for organization invites
export const invitations = pgTable("invitations", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  organizationId: varchar("organization_id", { length: 255 }).notNull().references(() => organizations.id, { onDelete: "cascade" }),
  email: varchar("email", { length: 255 }).notNull(),
  role: varchar("role", { length: 50 }).default("member").notNull(),
  invitedBy: varchar("invited_by", { length: 255 }).notNull().references(() => users.id),
  token: varchar("token", { length: 255 }).notNull().unique(),
  expiresAt: timestamp("expires_at").notNull(),
  acceptedAt: timestamp("accepted_at"),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});

// Webhooks table for storing webhook events
export const webhooks = pgTable("webhooks", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  provider: varchar("provider", { length: 50 }).notNull(), // 'stripe', 'github', etc.
  eventType: varchar("event_type", { length: 100 }).notNull(),
  eventId: varchar("event_id", { length: 255 }).notNull(),
  data: jsonb("data").notNull(),
  processed: boolean("processed").default(false).notNull(),
  processedAt: timestamp("processed_at"),
  error: text("error"),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});

export const business_profiles = pgTable("business_profiles", {
  id: serial("id").primaryKey(),
  tenantId: integer("tenant_id").references(() => tenants.id, { onDelete: "cascade" }),
  business_name: varchar("business_name", { length: 255 }).notNull(),
  business_logo: text("business_logo"),
  company_registered_name: varchar("company_registered_name", { length: 255 }),
  company_registered_no: varchar("company_registered_no", { length: 255 }),
  business_industry: varchar("business_industry", { length: 255 }),
  contact_email: varchar("contact_email", { length: 255 }),
  contact_number: varchar("contact_email", { length: 30 }),
  whatsapp_number: varchar("contact_email", { length: 30 }),
  show_whatsapp_floating: boolean("show_whatsapp_floating").default(false).notNull(),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});

export const addresses = pgTable("addresses", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  tenantId: integer("tenant_id").references(() => tenants.id, { onDelete: "cascade" }),
  addressLine1: varchar("address_line_1", { length: 255 }),
  addressLine2: varchar("address_line_2", { length: 255 }),
  city: varchar("city", { length: 255 }),
  state: varchar("state", { length: 255 }),
  zip: varchar("zip", { length: 255 }),
  country: varchar("country", { length: 255 }),
  postalCode: varchar("postal_code", { length: 255 }),
  acceptedAt: timestamp("accepted_at"),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});

export const settings = pgTable("settings", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  tenantId: integer("tenant_id").references(() => tenants.id, { onDelete: "cascade" }),
  currency: varchar("currency", { length: 50 }),
  timeZone: varchar("time_zone", { length: 50 }),
  acceptedAt: timestamp("accepted_at"),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});

export const locations = pgTable("locations", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  name: varchar("name", { length: 255 }),
  tenantId: integer("tenant_id").references(() => tenants.id, { onDelete: "cascade" }),
  addressLine1: varchar("address_line_1", { length: 255 }),
  addressLine2: varchar("address_line_2", { length: 255 }),
  city: varchar("city", { length: 255 }),
  state: varchar("state", { length: 255 }),
  country: varchar("country", { length: 255 }),
  postalCode: varchar("postal_code", { length: 255 }),
  phoneNumber: varchar("phone_number", { length: 30 }),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});

export const equipment = pgTable("equipment", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  tenantId: integer("tenant_id").references(() => tenants.id, { onDelete: "cascade" }),
  name: varchar("name", { length: 255 }),
  default_display_name: varchar("default_display_name", { length: 255 }),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});


export const equipment_instances = pgTable("equipment_instances", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  equipmentId: varchar("equipment_id", { length: 255 }).references(() => equipment.id, { onDelete: "cascade" }),
  locationId: varchar("location_id", { length: 255 }).references(() => locations.id, { onDelete: "cascade" }),
  quantity: integer("quantity").default(1).notNull(),
  displayName: varchar("display_name", { length: 255 }),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});

export const class_levels = pgTable("class_levels", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  tenantId: integer("tenant_id").references(() => tenants.id, { onDelete: "cascade" }),
  name: varchar("name", { length: 255 }).notNull(),
  description: text("description"),
  sortOrder: integer("sort_order").default(0).notNull(),
  isActive: boolean("is_active").default(true).notNull(),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});

export const waiver_forms = pgTable("waiver_forms", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  tenantId: integer("tenant_id").references(() => tenants.id, { onDelete: "cascade" }).notNull(),
  name: varchar("name", { length: 255 }).notNull(),
  description: text("description"),
  content: text("content").notNull(), // HTML/JSON content of the waiver form
  version: varchar("version", { length: 50 }).default("1.0").notNull(),
  isActive: boolean("is_active").default(true).notNull(),
  isRequired: boolean("is_required").default(false).notNull(),
  expiryDays: integer("expiry_days").default(365), // Days until waiver expires
  sortOrder: integer("sort_order").default(0).notNull(),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});

export const pricing_groups = pgTable("pricing_groups", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  tenantId: integer("tenant_id").references(() => tenants.id, { onDelete: "cascade" }).notNull(),
  name: varchar("name", { length: 255 }).notNull(),
  description: text("description"),
  discountPercentage: integer("discount_percentage").default(0), // Percentage discount (0-100)
  isActive: boolean("is_active").default(true).notNull(),
  isDefault: boolean("is_default").default(false).notNull(),
  sortOrder: integer("sort_order").default(0).notNull(),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});

export const customers = pgTable("customers", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  tenantId: integer("tenant_id").references(() => tenants.id, { onDelete: "cascade" }).notNull(),
  email: varchar("email", { length: 255 }).notNull(),
  mobileCountryCode: varchar("mobile_country_code", { length: 10 }),
  mobileNumber: varchar("mobile_number", { length: 30 }),
  firstName: varchar("first_name", { length: 255 }).notNull(),
  lastName: varchar("last_name", { length: 255 }),
  dateOfBirth: date("date_of_birth"),
  gender: varchar("gender", { length: 50 }),
   // Location and business context
  locationId: varchar("location_id", { length: 255 }).references(() => locations.id, { onDelete: "set null" }),
  pricingGroupId: varchar("pricing_group_id", { length: 255 }).references(() => pricing_groups.id, { onDelete: "set null" }),

// Authentication Method
  password: text('password'),
  googleId: varchar('google_id', { length: 255 }).unique(),
  emailVerified: timestamp("email_verified"),


  // customer preferences and settings
    preferences: jsonb("preferences").$type<{
    language?: string;
    timezone?: string;
    notifications?: {
      email?: boolean;
      sms?: boolean;
      push?: boolean;
    };
    privacy?: {
      shareDataWithPartners?: boolean;
      allowAnalytics?: boolean;
    };
  }>(),

   // Membership and status
  membershipType: varchar("membership_type", { length: 50 }).default("basic"),
  membershipExpiry: timestamp("membership_expires_at"),
  isActive: boolean("is_active").default(true).notNull(),
  isEmailVerified: boolean("is_email_verified").default(false).notNull(),
  
  // Security and compliance
  lastLoginAt: timestamp("last_login_at"),
  lastLoginIP: varchar("last_login_ip", { length: 45 }),
  failedLoginAttempts: integer("failed_login_attempts").default(0).notNull(),
  lockedAt: timestamp("locked_at"),
  lockedUntil: timestamp("locked_until"),

  // Marketing and consent
  marketingEmails: boolean("marketing_emails").default(false).notNull(),
  termsAcceptedAt: timestamp("terms_accepted_at"),
  termsVersion: varchar("terms_version", { length: 50 }),
  privacyAcceptedAt: timestamp("privacy_accepted_at"),
  privacyVersion: varchar("privacy_version", { length: 50 }),


  notes: text("notes"),
  tags: jsonb("tags").$type<string[]>().default([]), // Array of tag IDs
  customFields: jsonb("custom_fields").$type<Record<string, any>>(),
  displayName: varchar("display_name", { length: 255 }),
  image: text("image"),


  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  createdBy: varchar("created_by", { length: 255 }),
  updatedBy: varchar("updated_by", { length: 255 }),
});


// ========================================
// CUSTOMER OAUTH ACCOUNTS TABLE
// ========================================
export const customerOAuthAccounts = pgTable("customer_oauth_accounts", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  customerId: varchar("customer_id", { length: 255 }).notNull().references(() => customers.id, { onDelete: "cascade" }),
  tenantId: integer("tenant_id").references(() => tenants.id, { onDelete: "cascade" }).notNull(),
  
  // OAuth provider information
  provider: varchar("provider", { length: 255 }).notNull(), // "google", "facebook", etc.
  providerAccountId: varchar("provider_account_id", { length: 255 }).notNull(),
  type: varchar("type", { length: 255 }).notNull(), // "oauth", "oidc"
  
  // OAuth tokens and metadata
  accessToken: text("access_token"),
  refreshToken: text("refresh_token"),
  idToken: text("id_token"),
  expiresAt: integer("expires_at"),
  tokenType: varchar("token_type", { length: 255 }),
  scope: varchar("scope", { length: 255 }),
  sessionState: varchar("session_state", { length: 255 }),
  
  // Provider profile data (cached)
  providerProfile: jsonb("provider_profile").$type<{
    name?: string;
    email?: string;
    picture?: string;
    locale?: string;
    emailVerified?: boolean;
  }>(),
  
  // Security and audit
  isActive: boolean("is_active").default(true).notNull(),
  lastUsed: timestamp("last_used"),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});

// ========================================
// CUSTOMER JWT SESSIONS TABLE
// ========================================
export const customerSessions = pgTable("customer_sessions", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  customerId: varchar("customer_id", { length: 255 }).notNull().references(() => customers.id, { onDelete: "cascade" }),
  tenantId: integer("tenant_id").references(() => tenants.id, { onDelete: "cascade" }).notNull(),

  // JWT token information
  jti: varchar("jti", { length: 255 }).notNull().unique(), // JWT ID for token revocation
  tokenHash: varchar("token_hash", { length: 255 }).notNull(), // SHA256 hash of JWT for security
  refreshTokenHash: varchar("refresh_token_hash", { length: 255 }), // SHA256 hash of refresh token

  // Session metadata
  deviceType: varchar("device_type", { length: 50 }), // "web", "mobile", "tablet"
  deviceId: varchar("device_id", { length: 255 }), // Unique device identifier
  userAgent: text("user_agent"),
  ipAddress: varchar("ip_address", { length: 45 }),
  location: varchar("location", { length: 255 }), // Geolocation (city, country)

  // OAuth context
  oauthProvider: varchar("oauth_provider", { length: 50 }), // "google", "credentials"
  oauthAccountId: varchar("oauth_account_id", { length: 255 }), // Reference to customerOAuthAccounts.id

  // Session lifecycle
  issuedAt: timestamp("issued_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  expiresAt: timestamp("expires_at").notNull(),
  refreshExpiresAt: timestamp("refresh_expires_at"), // Refresh token expiry
  lastUsedAt: timestamp("last_used_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  revokedAt: timestamp("revoked_at"), // Manual revocation
  revokedBy: varchar("revoked_by", { length: 255 }), // Who revoked (customer_id or admin_id)
  revokedReason: varchar("revoked_reason", { length: 100 }), // "logout", "security", "admin"

  // Security flags
  isActive: boolean("is_active").default(true).notNull(),
  isSuspicious: boolean("is_suspicious").default(false).notNull(), // Flagged for security review

  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
}, (table) => ({
  // Indexes for performance
  customerIdIdx: index("customer_sessions_customer_id_idx").on(table.customerId),
  tenantIdIdx: index("customer_sessions_tenant_id_idx").on(table.tenantId),
  jtiIdx: index("customer_sessions_jti_idx").on(table.jti),
  expiresAtIdx: index("customer_sessions_expires_at_idx").on(table.expiresAt),
  deviceIdIdx: index("customer_sessions_device_id_idx").on(table.deviceId),
}));

// ========================================
// CUSTOMER AUTHENTICATION AUDIT LOGS
// ========================================
export const customerAuthLogs = pgTable("customer_auth_logs", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  customerId: varchar("customer_id", { length: 255 }).references(() => customers.id, { onDelete: "set null" }), // Nullable for failed attempts
  tenantId: integer("tenant_id").references(() => tenants.id, { onDelete: "cascade" }).notNull(),

  // Authentication event details
  event: varchar("event", { length: 50 }).notNull(), // "login_attempt", "login_success", "login_failed", "logout", "token_refresh", "password_reset", "oauth_link", "oauth_unlink"
  method: varchar("method", { length: 50 }).notNull(), // "google_oauth", "credentials", "refresh_token"
  status: varchar("status", { length: 20 }).notNull(), // "success", "failed", "blocked"

  // Request context
  email: varchar("email", { length: 255 }), // Email used in attempt (even if customer not found)
  ipAddress: varchar("ip_address", { length: 45 }).notNull(),
  userAgent: text("user_agent"),
  deviceType: varchar("device_type", { length: 50 }),
  deviceId: varchar("device_id", { length: 255 }),

  // OAuth specific data
  oauthProvider: varchar("oauth_provider", { length: 50 }),
  oauthAccountId: varchar("oauth_account_id", { length: 255 }),

  // Security and error details
  errorCode: varchar("error_code", { length: 50 }), // "invalid_credentials", "account_locked", "oauth_error", etc.
  errorMessage: text("error_message"),
  riskScore: integer("risk_score"), // 0-100 risk assessment
  isBlocked: boolean("is_blocked").default(false).notNull(),
  blockReason: varchar("block_reason", { length: 100 }),

  // Session reference
  sessionId: varchar("session_id", { length: 255 }).references(() => customerSessions.id, { onDelete: "set null" }),

  // Geolocation and metadata
  location: varchar("location", { length: 255 }), // City, Country
  timezone: varchar("timezone", { length: 50 }),
  metadata: jsonb("metadata").$type<{
    pkceChallenge?: string; // For mobile OAuth
    redirectUri?: string;
    scope?: string;
    clientType?: "web" | "mobile";
    appVersion?: string;
    osVersion?: string;
  }>(),

  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
}, (table) => ({
  // Indexes for security monitoring and analytics
  customerIdIdx: index("customer_auth_logs_customer_id_idx").on(table.customerId),
  tenantIdIdx: index("customer_auth_logs_tenant_id_idx").on(table.tenantId),
  eventIdx: index("customer_auth_logs_event_idx").on(table.event),
  statusIdx: index("customer_auth_logs_status_idx").on(table.status),
  ipAddressIdx: index("customer_auth_logs_ip_address_idx").on(table.ipAddress),
  createdAtIdx: index("customer_auth_logs_created_at_idx").on(table.createdAt),
  emailIdx: index("customer_auth_logs_email_idx").on(table.email),
}));

// ========================================
// CUSTOMER OAUTH PKCE CHALLENGES (for mobile security)
// ========================================
export const customerOAuthChallenges = pgTable("customer_oauth_challenges", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  tenantId: integer("tenant_id").references(() => tenants.id, { onDelete: "cascade" }).notNull(),

  // PKCE parameters
  codeChallenge: varchar("code_challenge", { length: 255 }).notNull(),
  codeChallengeMethod: varchar("code_challenge_method", { length: 10 }).notNull(), // "S256"
  state: varchar("state", { length: 255 }).notNull().unique(), // OAuth state parameter

  // OAuth flow context
  redirectUri: text("redirect_uri").notNull(),
  scope: varchar("scope", { length: 255 }).default("openid email profile").notNull(),
  clientType: varchar("client_type", { length: 20 }).notNull(), // "web", "mobile"

  // Request metadata
  ipAddress: varchar("ip_address", { length: 45 }).notNull(),
  userAgent: text("user_agent"),
  deviceId: varchar("device_id", { length: 255 }),

  // Lifecycle
  isUsed: boolean("is_used").default(false).notNull(),
  usedAt: timestamp("used_at"),
  expiresAt: timestamp("expires_at").notNull(), // 10 minutes from creation

  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
}, (table) => ({
  // Indexes for OAuth flow performance
  stateIdx: index("customer_oauth_challenges_state_idx").on(table.state),
  tenantIdIdx: index("customer_oauth_challenges_tenant_id_idx").on(table.tenantId),
  expiresAtIdx: index("customer_oauth_challenges_expires_at_idx").on(table.expiresAt),
}));


export const customer_addresses = pgTable("customer_addresses", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  customerId: varchar("customer_id", { length: 255 }).references(() => customers.id, { onDelete: "cascade" }),
  addressLine1: varchar("address_line_1", { length: 255 }),
  addressLine2: varchar("address_line_2", { length: 255 }),
  city: varchar("city", { length: 255 }),
  state: varchar("state", { length: 255 }),
  zip: varchar("zip", { length: 255 }),
  country: varchar("country", { length: 255 }),
  is_primary: boolean("is_primary").default(false),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});


export const customer_waivers = pgTable("customer_waivers", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  customerId: varchar("customer_id", { length: 255 }).references(() => customers.id, { onDelete: "cascade" }),
  waiverFormId: varchar("waiver_form_id", { length: 255 }).references(() => waiver_forms.id, { onDelete: "cascade" }),
  signed_at: timestamp("signed_at"),
  acknowledged: boolean("acknowledged").default(false),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});


export const tags = pgTable("tags", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  tenantId: integer("tenant_id").references(() => tenants.id, { onDelete: "cascade" }).notNull(),
  customerId: text("customer_id").references(() => customers.id, { onDelete: "cascade" }),
  name: varchar("name", { length: 255 }),
  description: varchar("description", { length: 255 }),
  custom_color: varchar("custom_color", { length: 255 }),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});

export const packages = pgTable("package", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  tenantId: integer("tenant_id").references(() => tenants.id, { onDelete: "cascade" }).notNull(),
  name: varchar("name", { length: 255 }).notNull(),
  description: varchar("description", { length: 255 }),
  isActive: boolean("is_active").default(true).notNull(),
  is_private: boolean("is_private").default(false).notNull(),
  validity_date: date("validity_date"),
  validity_duration: integer("validity_duration"),
  schedule_availability: jsonb("schedule_availability").$type<PackageScheduleAvailabilityData[]>().default([]),
  included_classes: jsonb("included_classes").$type<PackageIncludedClass[]>().default([]),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});

export const package_categories = pgTable("package_categories", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  tenantId: integer("tenant_id").references(() => tenants.id, { onDelete: "cascade" }).notNull(),
  name: varchar("name", { length: 255 }).notNull(),
  description: varchar("description", { length: 255 }),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});

export const package_category_rel = pgTable("package_category_rel", {
  package_id: varchar("package_id", { length: 255 }).references(() => packages.id, { onDelete: "cascade" }).notNull(),
  category_id: varchar("category_id", { length: 255 }).references(() => package_categories.id, { onDelete: "cascade" }).notNull(),
  tenantId: integer("tenant_id").references(() => tenants.id, { onDelete: "cascade" }).notNull(),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
}, (table) => ({
  // Compound primary key untuk mencegah duplicate package-category relationships
  compoundKey: primaryKey({
    columns: [table.package_id, table.category_id],
  }),
}));


export const package_locations = pgTable("package_locations", {
  package_id: varchar("package_id", { length: 255 }).references(() => packages.id, { onDelete: "cascade" }).notNull(),
  location_id: varchar("location_id", { length: 255 }).references(() => locations.id, { onDelete: "cascade" }).notNull(),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
}, (table) => ({
  // Compound primary key untuk mencegah duplicate package-location relationships
  compoundKey: primaryKey({
    columns: [table.package_id, table.location_id],
  }),
}));

export const package_pricing = pgTable("package_pricing", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  package_id: varchar("package_id", { length: 255 }).references(() => packages.id, { onDelete: "cascade" }).notNull(),
  pricing_group_id : varchar("pricing_group_id", { length: 255 }).references(() => pricing_groups.id, { onDelete: "cascade" }).notNull(),
  price: integer("price"),
  credit_amount: integer("credit_amount"),
  currency: varchar("currency", { length: 255 }),
  sortOrder: integer("sort_order").default(0).notNull(),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});

export const package_purchase_options = pgTable("package_purchase_options", {
  package_id: varchar("package_id", { length: 255 }).references(() => packages.id, { onDelete: "cascade" }).notNull(),
  purchase_limit: integer("purchase_limit"), // null jika unlimited
  restrict_to: varchar("restrict_to", { length: 255 }), // "all", "member_only", "etc"
  transferable: boolean("transferable").default(false).notNull(),
  specify_sold_at_location: boolean("specify_sold_at_location").default(false).notNull(),
  sold_at_location_id: varchar("sold_at_location_id", { length: 255 }).references(() => locations.id, { onDelete: "cascade" }),
  class_booking_limit: integer("class_booking_limit"), // null jika unlimited
  show_online: boolean("show_online").default(false).notNull(),
  show_onsite: boolean("show_onsite").default(false).notNull(),
  isPrivate: boolean("is_private").default(false).notNull(),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});

export const package_customer_segements = pgTable("package_customer_segements", {
  package_id: varchar("package_id", { length: 255 }).references(() => packages.id, { onDelete: "cascade" }).notNull(),
  customer_id: varchar("customer_id", { length: 255 }).references(() => customers.id, { onDelete: "cascade" }).notNull(),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});

export const class_categories = pgTable("class_categories", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  tenantId: integer("tenant_id").references(() => tenants.id, { onDelete: "cascade" }).notNull(),
  name: varchar("name", { length: 255 }).notNull(),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});

export const class_subcategories = pgTable("class_subcategories", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  tenantId: integer("tenant_id").references(() => tenants.id, { onDelete: "cascade" }).notNull(),
  categoryId: varchar("category_id", { length: 255 }).references(() => class_categories.id, { onDelete: "cascade" }).notNull(),
  name: varchar("name", { length: 255 }).notNull(),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});

//bagian classes
export const classes = pgTable("classes", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  tenantId: integer("tenant_id").references(() => tenants.id, { onDelete: "cascade" }).notNull(),
  name: varchar("name", { length: 255 }).notNull(),
  description: varchar("description", { length: 255 }),
  categoryId: varchar("category_id", { length: 255 }).references(() => class_categories.id, { onDelete: "cascade" }).notNull(),
  subcategoryId: varchar("subcategory_id", { length: 255 }).references(() => class_subcategories.id, { onDelete: "cascade" }),
  duration_value: integer("duration"), // in minutes
  duration_unit: varchar("duration_unit", { length: 50 }), // "minutes", "hours", "days", "weeks", "months", "years"
  level_id: varchar("level_id", { length: 255 }).references(() => class_levels.id, { onDelete: "cascade" }),
  delivery_mode: varchar("delivery_mode", { length: 50 }), // "onsite", "online", "hybrid"
  is_private: boolean("is_private").default(false).notNull(),
  custom_cancellation_policy: boolean("custom_cancellation_policy").default(false).notNull(),
  cancellation_policy_description: varchar("cancellation_policy_description", { length: 255 }),
  is_active: boolean("is_active").default(true).notNull(),
  location_id: varchar("location_id", { length: 255 }).references(() => locations.id, { onDelete: "cascade" }),
  images: jsonb("images").$type<string[]>().default([]), // Array of image URLs
  items_to_bring: jsonb("items_to_bring").$type<ClassItemToBring[]>().default([]), // Array of items to bring
  youtube_links: jsonb("youtube_links").$type<ClassYoutubeLinkData[]>().default([]), // Array of YouTube links
  membership_plan_ids: jsonb("membership_plan_ids").$type<string[]>().default([]), // Array of membership plan IDs
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});

export const class_images = pgTable("class_images", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  class_id: varchar("class_id", { length: 255 }).references(() => classes.id, { onDelete: "cascade" }).notNull(),
  image_url: varchar("image_url", { length: 255 }).notNull(),
  upload_at: timestamp("upload_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});

export const class_payment_options = pgTable("class_payment_options", {
  class_id: varchar("class_id", { length: 255 }).references(() => classes.id, { onDelete: "cascade" }).notNull(),
  payment_type: varchar("payment_type", { length: 255 }).notNull(), // "free", "paid", "subscription", "donation"
  drop_in_enable: boolean("drop_in_enable").default(false).notNull(),
  drop_in_price: integer("drop_in_price"),
  currency: varchar("currency", { length: 255 }),
  name: varchar("name", { length: 255 }),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});

export const class_membership_plans = pgTable("class_membership_plans ", {
  class_id: varchar("class_id", { length: 255 }).references(() => classes.id, { onDelete: "cascade" }).notNull(),
  membership_plan_id: varchar("membership_plan_id", { length: 255 }).references(() => membership_plans.id, { onDelete: "cascade" }).notNull(),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
}, (table) => ({
  // Compound primary key untuk mencegah duplicate class-membership relationships
  compoundKey: primaryKey({
    columns: [table.class_id, table.membership_plan_id],
  }),
}));


export const class_package_pricing = pgTable("class_package_pricing", {
  class_id: varchar("class_id", { length: 255 }).references(() => classes.id, { onDelete: "cascade" }).notNull(),
  package_pricing_id: varchar("package_pricing_id", { length: 255 }).references(() => package_pricing.id, { onDelete: "cascade" }).notNull(),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
}, (table) => ({
  compoundKey: primaryKey({
    columns: [table.class_id, table.package_pricing_id],
  }),
}));

export const membership_plans = pgTable("membership_plans", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  tenantId: integer("tenant_id").references(() => tenants.id, { onDelete: "cascade" }).notNull(),
  name: varchar("name", { length: 255 }).notNull(),
  description: varchar("description", { length: 255 }),
  price: integer("price"),
  currency: varchar("currency", { length: 255 }),
  duration_value: integer("duration"),
  duration_unit: varchar("duration_unit", { length: 50 }),
  is_active: boolean("is_active").default(true).notNull(),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});

// Many-to-many relationship between membership plans and locations
export const membership_plan_locations = pgTable("membership_plan_locations", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  membership_plan_id: varchar("membership_plan_id", { length: 255 }).references(() => membership_plans.id, { onDelete: "cascade" }).notNull(),
  location_id: varchar("location_id", { length: 255 }).references(() => locations.id, { onDelete: "cascade" }).notNull(),
  tenantId: integer("tenant_id").references(() => tenants.id, { onDelete: "cascade" }).notNull(),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
}, (table) => ({
  // Compound unique constraint to prevent duplicate assignments
  compoundKey: unique().on(table.membership_plan_id, table.location_id),
  // Indexes for performance
  membershipPlanIdx: index("idx_membership_plan_locations_plan_id").on(table.membership_plan_id),
  locationIdx: index("idx_membership_plan_locations_location_id").on(table.location_id),
  tenantIdx: index("idx_membership_plan_locations_tenant_id").on(table.tenantId),
}));

export const package_display_options = pgTable("package_display_options", {
  package_id: varchar("package_id", { length: 255 }).references(() => packages.id, { onDelete: "cascade" }).notNull(),
  use_custom_descrition: boolean("use_custom_description").default(false).notNull(),
  custom_description: varchar("custom_description", { length: 255 }),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});

export const appointment_categories = pgTable("appointment_categories", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  tenantId: integer("tenant_id").references(() => tenants.id, { onDelete: "cascade" }).notNull(),
  name: varchar("name", { length: 255 }).notNull(),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});

export const appointment_subcategories = pgTable("appointment_subcategories", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  tenantId: integer("tenant_id").references(() => tenants.id, { onDelete: "cascade" }).notNull(),
  categoryId: varchar("category_id", { length: 255 }).references(() => appointment_categories.id, { onDelete: "cascade" }).notNull(),
  name: varchar("name", { length: 255 }).notNull(),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});



// export const appointments  = pgTable("appointment", {
//   id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
//   tenantId: integer("tenant_id").references(() => tenants.id, { onDelete: "cascade" }).notNull(),
//   name: varchar("name", { length: 255 }).notNull(),
//   description: varchar("description", { length: 255 }),
//   categoryId: varchar("category_id", { length: 255 }).references(() => appointment_categories.id, { onDelete: "cascade" }).notNull(),
//   subcategoryId: varchar("subcategory_id", { length: 255 }).references(() => appointment_subcategories.id, { onDelete: "cascade" }),
//   duration_value: integer("duration"), // in minutes
//   duration_unit: varchar("duration_unit", { length: 50 }), // "minutes", "hours", "days", "weeks", "months", "years"
//   level_id: varchar("level_id", { length: 255 }).references(() => class_levels.id, { onDelete: "cascade" }),
//   delivery_mode: varchar("delivery_mode", { length: 50 }), // "onsite", "online", "hybrid"
//   is_private: boolean("is_private").default(false).notNull(),
//   custom_cancellation_policy: boolean("custom_cancellation_policy").default(false).notNull(),
//   cancellation_policy_description: varchar("cancellation_policy_description", { length: 255 }),
//   is_active: boolean("is_active").default(true).notNull(),
//   location_id: varchar("location_id", { length: 255 }).references(() => locations.id, { onDelete: "cascade" }),
//   images: jsonb("images").$type<string[]>().default([]), // Array of image URLs
//   items_to_bring: jsonb("items_to_bring").$type<ClassItemToBring[]>().default([]), // Array of items to bring
//   youtube_links: jsonb("youtube_links").$type<ClassYoutubeLinkData[]>().default([]), // Array of YouTube links
//   membership_plan_ids: jsonb("membership_plan_ids").$type<string[]>().default([]), // Array of membership plan IDs
//   createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
//   updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
// });

export const class_bookings = pgTable("class_bookings", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  tenant_id: integer("tenant_id").references(() => tenants.id, { onDelete: "cascade" }).notNull(),
  schedule_id: varchar("schedule_id", { length: 255 }).references(() => class_schedules.id, { onDelete: "cascade" }).notNull(),
  class_id: varchar("class_id", { length: 255 }).references(() => classes.id, { onDelete: "cascade" }).notNull(),
  customer_id: varchar("customer_id", { length: 255 }).references(() => customers.id, { onDelete: "cascade" }).notNull(),
  booked_by_user_id: varchar("booked_by_user_id", { length: 255 }).references(() => users.id, { onDelete: "set null" }), // NULL jika self-booking
  booked_by_customer_id: varchar("booked_by_customer_id", { length: 255 }).references(() => customers.id, { onDelete: "set null" }), // NULL jika dibooking oleh staff/admin
  status: varchar("status", { length: 50 }).default("booked").notNull(), // booked, checked_in, cancelled, waitlisted, no_show, completed
  is_waitlist: boolean("is_waitlist").default(false).notNull(),
  waitlist_position: integer("waitlist_position"), // null jika bukan waitlist
  payment_status: varchar("payment_status", { length: 50 }), // paid, unpaid, refunded
  payment_method: varchar("payment_method", { length: 50 }), // credit, cash, classpass, etc
  credits_used: integer("credits_used"), // jika menggunakan credit
  booking_time: timestamp("booking_time").default(sql`CURRENT_TIMESTAMP`).notNull(),
  check_in_time: timestamp("check_in_time"),
  cancel_time: timestamp("cancel_time"),
  cancellation_reason: varchar("cancellation_reason", { length: 255 }),
  notes: varchar("notes", { length: 255 }),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});



// Export types
export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;
export type Organization = typeof organizations.$inferSelect;
export type NewOrganization = typeof organizations.$inferInsert;
export type Tenant = typeof tenants.$inferSelect;
export type NewTenant = typeof tenants.$inferInsert;
export type OrganizationMember = typeof organizationMembers.$inferSelect;
export type NewOrganizationMember = typeof organizationMembers.$inferInsert;
export type Account = typeof accounts.$inferSelect;
export type NewAccount = typeof accounts.$inferInsert;
export type Session = typeof sessions.$inferSelect;
export type NewSession = typeof sessions.$inferInsert;
export type VerificationToken = typeof verificationTokens.$inferSelect;
export type NewVerificationToken = typeof verificationTokens.$inferInsert;
export type CreditTransaction = typeof creditTransactions.$inferSelect;
export type NewCreditTransaction = typeof creditTransactions.$inferInsert;
export type StripeCustomer = typeof stripeCustomers.$inferSelect;
export type NewStripeCustomer = typeof stripeCustomers.$inferInsert;
export type StripePayment = typeof stripePayments.$inferSelect;
export type NewStripePayment = typeof stripePayments.$inferInsert;
export type LegacyApiKey = typeof legacyApiKeys.$inferSelect;
export type NewLegacyApiKey = typeof legacyApiKeys.$inferInsert;
export type UsageLog = typeof usageLogs.$inferSelect;
export type NewUsageLog = typeof usageLogs.$inferInsert;
export type Invitation = typeof invitations.$inferSelect;
export type NewInvitation = typeof invitations.$inferInsert;
export type Webhook = typeof webhooks.$inferSelect;
export type NewWebhook = typeof webhooks.$inferInsert;
export type BusinessProfile = typeof business_profiles.$inferSelect;
export type NewBusinessProfile = typeof business_profiles.$inferInsert;
export type Address = typeof addresses.$inferSelect;
export type NewAddress = typeof addresses.$inferInsert;
export type Setting = typeof settings.$inferSelect;
export type NewSetting = typeof settings.$inferInsert;
export type Location = typeof locations.$inferSelect;
export type NewLocation = typeof locations.$inferInsert;
export type Equipment = typeof equipment.$inferSelect;
export type NewEquipment = typeof equipment.$inferInsert;
export type EquipmentInstance = typeof equipment_instances.$inferSelect;
export type NewEquipmentInstance = typeof equipment_instances.$inferInsert;
export type ClassLevel = typeof class_levels.$inferSelect;
export type NewClassLevel = typeof class_levels.$inferInsert;
export type WaiverForm = typeof waiver_forms.$inferSelect;
export type NewWaiverForm = typeof waiver_forms.$inferInsert;
export type PricingGroup = typeof pricing_groups.$inferSelect;
export type NewPricingGroup = typeof pricing_groups.$inferInsert;
export type Customer = typeof customers.$inferSelect;
export type NewCustomer = typeof customers.$inferInsert;
export type CustomerAddress = typeof customer_addresses.$inferSelect;
export type NewCustomerAddress = typeof customer_addresses.$inferInsert;
export type CustomerWaiver = typeof customer_waivers.$inferSelect;
export type NewCustomerWaiver = typeof customer_waivers.$inferInsert;
export type Tag = typeof tags.$inferSelect;
export type NewTag = typeof tags.$inferInsert;
export type Package = typeof packages.$inferSelect;
export type NewPackage = typeof packages.$inferInsert;
export type PackageCategory = typeof package_categories.$inferSelect;
export type NewPackageCategory = typeof package_categories.$inferInsert;
export type PackageCategoryRel = typeof package_category_rel.$inferSelect;
export type NewPackageCategoryRel = typeof package_category_rel.$inferInsert;
export type PackageLocation = typeof package_locations.$inferSelect;
export type NewPackageLocation = typeof package_locations.$inferInsert;
export type PackagePricing = typeof package_pricing.$inferSelect;
export type NewPackagePricing = typeof package_pricing.$inferInsert;
export type PackagePurchaseOptions = typeof package_purchase_options.$inferSelect;
export type NewPackagePurchaseOptions = typeof package_purchase_options.$inferInsert;
export type PackageCustomerSegements = typeof package_customer_segements.$inferSelect;
export type NewPackageCustomerSegements = typeof package_customer_segements.$inferInsert;
export type ClassCategory = typeof class_categories.$inferSelect;
export type NewClassCategory = typeof class_categories.$inferInsert;
export type Class = typeof classes.$inferSelect;
export type NewClass = typeof classes.$inferInsert;

// Extended Class type with package pricing relationships for edit mode
export type ClassWithRelations = Class & {
  package_pricing_ids?: string[];
};

export type PackageDisplayOptions = typeof package_display_options.$inferSelect;
export type NewPackageDisplayOptions = typeof package_display_options.$inferInsert;

export type ClassImage = typeof class_images.$inferSelect;
export type NewClassImage = typeof class_images.$inferInsert;


export type ClassPaymentOptions = typeof class_payment_options.$inferSelect;
export type NewClassPaymentOptions = typeof class_payment_options.$inferInsert;
export type ClassMembershipPlans = typeof class_membership_plans.$inferSelect;
export type NewClassMembershipPlans = typeof class_membership_plans.$inferInsert;
export type ClassPackagePricing = typeof class_package_pricing.$inferSelect;
export type NewClassPackagePricing = typeof class_package_pricing.$inferInsert;
export type MembershipPlan = typeof membership_plans.$inferSelect;
export type NewMembershipPlan = typeof membership_plans.$inferInsert;
export type MembershipPlanLocation = typeof membership_plan_locations.$inferSelect;
export type NewMembershipPlanLocation = typeof membership_plan_locations.$inferInsert;

// ===== RBAC SYSTEM TABLES =====
// Mengikuti pola yang sama dengan tabel lain: CUID2 IDs, tenant isolation, audit fields




// Roles table - Definisi role dalam sistem
export const roles = pgTable("roles", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  tenantId: integer("tenant_id").references(() => tenants.id, { onDelete: "cascade" }),
  name: varchar("name", { length: 100 }).notNull(), // "super_admin", "tenant_admin", "instructor", "customer"
  display_name: varchar("display_name", { length: 255 }).notNull(), // "Super Administrator", "Tenant Admin", etc.
  description: text("description"),
  is_system_role: boolean("is_system_role").default(false).notNull(), // true untuk built-in roles
  is_active: boolean("is_active").default(true).notNull(),
  hierarchy_level: integer("hierarchy_level").default(0).notNull(), // 0=highest, 100=lowest
  permissions: jsonb("permissions").$type<PermissionData[]>().default([]), // JSONB untuk flexibility
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});

// Permissions/Modules table - Definisi permission granular
export const permissions = pgTable("permissions", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  module: varchar("module", { length: 100 }).notNull(), // "classes", "customers", "packages", etc.
  action: varchar("action", { length: 50 }).notNull(), // "create", "read", "update", "delete", "manage"
  resource: varchar("resource", { length: 100 }), // specific resource jika perlu
  display_name: varchar("display_name", { length: 255 }).notNull(),
  description: text("description"),
  is_system_permission: boolean("is_system_permission").default(true).notNull(),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});

// User Role Assignments - Many-to-many dengan tenant context
export const user_roles = pgTable("user_roles", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  userId: varchar("user_id", { length: 255 }).references(() => users.id, { onDelete: "cascade" }).notNull(),
  roleId: varchar("role_id", { length: 255 }).references(() => roles.id, { onDelete: "cascade" }).notNull(),
  tenantId: integer("tenant_id").references(() => tenants.id, { onDelete: "cascade" }), // null untuk super admin
  assignedBy: varchar("assigned_by", { length: 255 }).references(() => users.id),
  assignedAt: timestamp("assigned_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  expiresAt: timestamp("expires_at"), // optional expiration
  is_active: boolean("is_active").default(true).notNull(),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
}, (table) => ({
  // Unique constraint: satu user hanya bisa punya satu role per tenant
  uniqueUserRoleTenant: primaryKey({
    name: "unique_user_role_tenant",
    columns: [table.userId, table.roleId, table.tenantId],
  }),
}));

// Role Permission Assignments - Many-to-many antara roles dan permissions
export const role_permissions = pgTable("role_permissions", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  roleId: varchar("role_id", { length: 255 }).references(() => roles.id, { onDelete: "cascade" }).notNull(),
  permissionId: varchar("permission_id", { length: 255 }).references(() => permissions.id, { onDelete: "cascade" }).notNull(),
  conditions: jsonb("conditions"), // additional conditions untuk permission
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
}, (table) => ({
  // Unique constraint: satu role tidak bisa punya permission yang sama 2x
  uniqueRolePermission: unique("unique_role_permission").on(table.roleId, table.permissionId),
}));

// Location Access Control - Untuk multi-location tenants
export const user_location_access = pgTable("user_location_access", {
  userId: varchar("user_id", { length: 255 }).references(() => users.id, { onDelete: "cascade" }).notNull(),
  locationId: varchar("location_id", { length: 255 }).references(() => locations.id, { onDelete: "cascade" }).notNull(),
  tenantId: integer("tenant_id").references(() => tenants.id, { onDelete: "cascade" }).notNull(),
  access_level: varchar("access_level", { length: 50 }).default("full").notNull(), // "full", "read_only", "restricted"
  assignedBy: varchar("assigned_by", { length: 255 }).references(() => users.id),
  assignedAt: timestamp("assigned_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  is_active: boolean("is_active").default(true).notNull(),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
}, (table) => ({
  // Unique constraint: satu user hanya bisa punya satu access level per location
  uniqueUserLocation: primaryKey({
    name: "unique_user_location",
    columns: [table.userId, table.locationId],
  }),
}));

// Activity Log untuk audit trail RBAC changes
export const rbac_activity_logs = pgTable("rbac_activity_logs", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  tenantId: integer("tenant_id").references(() => tenants.id, { onDelete: "cascade" }),
  userId: varchar("user_id", { length: 255 }).references(() => users.id, { onDelete: "cascade" }),
  targetUserId: varchar("target_user_id", { length: 255 }).references(() => users.id, { onDelete: "cascade" }), // user yang di-assign role
  action: varchar("action", { length: 100 }).notNull(), // "role_assigned", "role_revoked", "permission_granted", etc.
  resource_type: varchar("resource_type", { length: 50 }).notNull(), // "role", "permission", "location_access"
  resource_id: varchar("resource_id", { length: 255 }), // ID dari resource yang diubah
  old_value: jsonb("old_value"), // nilai sebelum perubahan
  new_value: jsonb("new_value"), // nilai setelah perubahan
  ip_address: varchar("ip_address", { length: 45 }), // IPv4/IPv6
  user_agent: text("user_agent"),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});


// Type exports untuk RBAC
export type Role = typeof roles.$inferSelect;
export type NewRole = typeof roles.$inferInsert;
export type Permission = typeof permissions.$inferSelect;
export type NewPermission = typeof permissions.$inferInsert;
export type UserRole = typeof user_roles.$inferSelect;
export type NewUserRole = typeof user_roles.$inferInsert;
export type RolePermission = typeof role_permissions.$inferSelect;
export type NewRolePermission = typeof role_permissions.$inferInsert;
export type UserLocationAccess = typeof user_location_access.$inferSelect;
export type NewUserLocationAccess = typeof user_location_access.$inferInsert;
export type RbacActivityLog = typeof rbac_activity_logs.$inferSelect;
export type NewRbacActivityLog = typeof rbac_activity_logs.$inferInsert;


export const facilities = pgTable("facilities", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  tenantId: integer("tenant_id").references(() => tenants.id, { onDelete: "cascade" }).notNull(),
  name: varchar("name", { length: 255 }).notNull(),
  description: varchar("description", { length: 500 }),
  isActive: boolean("is_active").default(true).notNull(),
  images: jsonb("images").$type<string[]>().default([]), // Array of image URLs like classes
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});

// Type definitions for facilities
export type Facility = typeof facilities.$inferSelect;
export type NewFacility = typeof facilities.$inferInsert;


export const class_schedules = pgTable("class_schedules", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  class_id: varchar("class_id", { length: 255 }).references(() => classes.id, { onDelete: "cascade" }).notNull(),
  tenant_id: integer("tenant_id").references(() => tenants.id, { onDelete: "cascade" }),
  location_id: varchar("location_id", { length: 255 }).references(() => locations.id, { onDelete: "cascade" }),
  facility_id: varchar("facility_id", { length: 255 }).references(() => facilities.id, { onDelete: "cascade" }),
  staff_id: varchar("staff_id", { length: 255 }).references(() => users.id, { onDelete: "cascade" }),
  name: varchar("name", { length: 255 }), // Nama/judul schedule
  description: text("description"), // Deskripsi schedule
  start_date: date("start_date"),
  end_date: date("end_date"),
  start_time: timestamp("start_time"),
  end_time: timestamp("end_time"),
  duration: integer("duration").notNull(),
  calender_color: varchar("calender_color", { length: 50 }),
  repeat_rule: varchar("repeat_rule", { length: 50 }).default("none").notNull(), //none, daily, weekly, monthly
  pax: integer("pax").default(1).notNull(),
  waitlist: integer("waitlist").default(1).notNull(), //kapasitas waitlist
  allow_classpass: boolean("allow_classpass").default(false).notNull(), //boleh bayar pakai classpass
  is_private: boolean("is_private").default(false).notNull(),
  publish_now: boolean("publish_now").default(false).notNull(), //langsung publish ke website
  publish_at: timestamp("publish_at"), //publish di tanggal tertentu
  auto_cancel_if_minimum_not_met: boolean("auto_cancel_if_minimum_not_met").default(false).notNull(), //jika pax tidak mencapai minimum, maka otomatis dibatalkan
  booking_window_start: timestamp("booking_window_start"),
  booking_window_end: timestamp("booking_window_end"),
  check_in_window_start: timestamp("check_in_window_start"),
  check_in_window_end: timestamp("check_in_window_end"),
  late_cancellation_rule: varchar("late_cancellation_rule", { length: 255 }),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
}, (table) => ({
  // FAANG-Level Database Constraints untuk Schedule Conflict Prevention

  // Unique constraints untuk prevent exact duplicate schedules
  uniqueScheduleTimeLocation: unique("unique_schedule_time_location").on(
    table.tenant_id,
    table.start_time,
    table.end_time,
    table.location_id
  ),

  uniqueScheduleTimeFacility: unique("unique_schedule_time_facility").on(
    table.tenant_id,
    table.start_time,
    table.end_time,
    table.facility_id
  ),

  // Prevent staff double booking
  uniqueStaffTimeSlot: unique("unique_staff_time_slot").on(
    table.staff_id,
    table.start_time,
    table.end_time
  ),

  // Performance optimization indexes
  idxTimeOverlap: index("idx_class_schedules_time_overlap").on(
    table.tenant_id,
    table.start_time,
    table.end_time
  ),

  idxLocationTime: index("idx_class_schedules_location_time").on(
    table.tenant_id,
    table.location_id,
    table.start_time,
    table.end_time
  ),

  idxFacilityTime: index("idx_class_schedules_facility_time").on(
    table.tenant_id,
    table.facility_id,
    table.start_time,
    table.end_time
  ),

  idxStaffTime: index("idx_class_schedules_staff_time").on(
    table.staff_id,
    table.start_time,
    table.end_time
  ),

  idxRecurring: index("idx_class_schedules_recurring").on(
    table.tenant_id,
    table.repeat_rule,
    table.start_date,
    table.end_date
  ),

  // Check constraints untuk data validation
  checkStartBeforeEnd: check("check_start_before_end", sql`${table.start_time} < ${table.end_time}`),
  checkPositiveDuration: check("check_positive_duration", sql`${table.duration} > 0`),
  checkPositiveCapacity: check("check_positive_capacity", sql`${table.pax} > 0 AND ${table.waitlist} >= 0`),
  checkValidRepeatRule: check("check_valid_repeat_rule", sql`${table.repeat_rule} IN ('none', 'daily', 'weekly', 'monthly')`),
  checkBookingWindowOrder: check("check_booking_window_order", sql`${table.booking_window_start} IS NULL OR ${table.booking_window_end} IS NULL OR ${table.booking_window_start} <= ${table.booking_window_end}`),
  checkCheckinWindowOrder: check("check_checkin_window_order", sql`${table.check_in_window_start} IS NULL OR ${table.check_in_window_end} IS NULL OR ${table.check_in_window_start} <= ${table.check_in_window_end}`),
}));

// ===== VOUCHER SYSTEM TABLES =====
// Comprehensive voucher system with multi-tenant isolation and FAANG-level constraints

// Voucher Usage Data Structure
export interface VoucherUsageData {
  id: string;
  customer_id: string;
  customer_name: string;
  order_id?: string;
  package_id?: string;
  class_id?: string;
  amount_discounted: number;
  used_at: string; // ISO timestamp
}

// Voucher Restrictions Data Structure
export interface VoucherRestrictionsData {
  min_purchase_amount?: number; // Minimum purchase amount to use voucher
  max_discount_amount?: number; // Maximum discount amount (for percentage vouchers)
  applicable_packages?: string[]; // Package IDs that can use this voucher
  applicable_classes?: string[]; // Class IDs that can use this voucher
  applicable_locations?: string[]; // Location IDs where voucher can be used
  applicable_customer_groups?: string[]; // Pricing group IDs that can use this voucher
  exclude_packages?: string[]; // Package IDs that cannot use this voucher
  exclude_classes?: string[]; // Class IDs that cannot use this voucher
  first_time_customers_only?: boolean; // Only for new customers
  existing_customers_only?: boolean; // Only for existing customers
}

// Main vouchers table
export const vouchers = pgTable("vouchers", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  tenantId: integer("tenant_id").references(() => tenants.id, { onDelete: "cascade" }).notNull(),

  // Basic voucher information
  code: varchar("code", { length: 50 }).notNull(), // Voucher code (e.g., "SAVE20", "WELCOME10")
  name: varchar("name", { length: 255 }).notNull(), // Display name for admin
  description: text("description"), // Description for customers

  // Voucher type and value
  type: varchar("type", { length: 50 }).notNull(), // "percentage", "fixed_amount", "free_shipping", "buy_x_get_y"
  value: integer("value").notNull(), // Percentage (1-100) or fixed amount in cents
  currency: varchar("currency", { length: 3 }).default("USD").notNull(),

  // Usage limits
  usage_limit: integer("usage_limit"), // null = unlimited
  usage_limit_per_customer: integer("usage_limit_per_customer").default(1), // How many times one customer can use
  current_usage_count: integer("current_usage_count").default(0).notNull(),

  // Validity period
  valid_from: timestamp("valid_from").notNull(),
  valid_until: timestamp("valid_until").notNull(),

  // Status and visibility
  is_active: boolean("is_active").default(true).notNull(),
  is_public: boolean("is_public").default(true).notNull(), // false = private/targeted voucher
  auto_apply: boolean("auto_apply").default(false).notNull(), // Automatically apply if conditions met

  // Restrictions and conditions (JSONB for flexibility)
  restrictions: jsonb("restrictions").$type<VoucherRestrictionsData>().default({}),

  // Audit fields
  created_by: varchar("created_by", { length: 255 }).references(() => users.id, { onDelete: "set null" }),
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
}, (table) => ({
  // Unique constraints
  uniqueTenantCode: unique("unique_tenant_voucher_code").on(table.tenantId, table.code),

  // Performance indexes
  idxTenantActive: index("idx_vouchers_tenant_active").on(table.tenantId, table.is_active),
  idxCodeLookup: index("idx_vouchers_code_lookup").on(table.code, table.is_active),
  idxValidityPeriod: index("idx_vouchers_validity").on(table.valid_from, table.valid_until),
  idxUsageTracking: index("idx_vouchers_usage").on(table.usage_limit, table.current_usage_count),

  // Check constraints for data validation
  checkValidType: check("check_valid_voucher_type", sql`${table.type} IN ('percentage', 'fixed_amount', 'free_shipping', 'buy_x_get_y')`),
  checkValidPercentage: check("check_valid_percentage", sql`${table.type} != 'percentage' OR (${table.value} >= 1 AND ${table.value} <= 100)`),
  checkPositiveValue: check("check_positive_value", sql`${table.value} > 0`),
  checkValidPeriod: check("check_valid_period", sql`${table.valid_from} < ${table.valid_until}`),
  checkUsageLimits: check("check_usage_limits", sql`${table.usage_limit} IS NULL OR ${table.usage_limit} > 0`),
  checkUsageLimitPerCustomer: check("check_usage_limit_per_customer", sql`${table.usage_limit_per_customer} > 0`),
  checkCurrentUsage: check("check_current_usage", sql`${table.current_usage_count} >= 0`),
  checkUsageNotExceeded: check("check_usage_not_exceeded", sql`${table.usage_limit} IS NULL OR ${table.current_usage_count} <= ${table.usage_limit}`),
}));

// Voucher usage tracking table
export const voucher_usage = pgTable("voucher_usage", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  voucher_id: varchar("voucher_id", { length: 255 }).references(() => vouchers.id, { onDelete: "cascade" }).notNull(),
  customer_id: varchar("customer_id", { length: 255 }).references(() => customers.id, { onDelete: "cascade" }).notNull(),

  // Usage context
  order_id: varchar("order_id", { length: 255 }), // Reference to order/booking
  package_id: varchar("package_id", { length: 255 }).references(() => packages.id, { onDelete: "set null" }),
  class_id: varchar("class_id", { length: 255 }).references(() => classes.id, { onDelete: "set null" }),
  location_id: varchar("location_id", { length: 255 }).references(() => locations.id, { onDelete: "set null" }),

  // Financial details
  original_amount: integer("original_amount").notNull(), // Original amount in cents
  discount_amount: integer("discount_amount").notNull(), // Discount applied in cents
  final_amount: integer("final_amount").notNull(), // Final amount after discount in cents
  currency: varchar("currency", { length: 3 }).default("USD").notNull(),

  // Usage metadata
  used_at: timestamp("used_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  ip_address: varchar("ip_address", { length: 45 }), // IPv4/IPv6
  user_agent: text("user_agent"),


  // Audit fields
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
}, (table) => ({
  // Indexes for performance
  idxVoucherUsage: index("idx_voucher_usage_voucher").on(table.voucher_id),
  idxCustomerUsage: index("idx_voucher_usage_customer").on(table.customer_id),
  idxUsageDate: index("idx_voucher_usage_date").on(table.used_at),
  idxOrderTracking: index("idx_voucher_usage_order").on(table.order_id),

  // Check constraints
  checkPositiveAmounts: check("check_positive_amounts", sql`${table.original_amount} > 0 AND ${table.discount_amount} >= 0 AND ${table.final_amount} >= 0`),
  checkValidDiscount: check("check_valid_discount", sql`${table.discount_amount} <= ${table.original_amount}`),
  checkFinalAmount: check("check_final_amount", sql`${table.final_amount} = ${table.original_amount} - ${table.discount_amount}`),
}));

// Voucher customer assignments (for targeted/private vouchers)
export const voucher_customer_assignments = pgTable("voucher_customer_assignments", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  voucher_id: varchar("voucher_id", { length: 255 }).references(() => vouchers.id, { onDelete: "cascade" }).notNull(),
  customer_id: varchar("customer_id", { length: 255 }).references(() => customers.id, { onDelete: "cascade" }).notNull(),

  // Assignment metadata
  assigned_by: varchar("assigned_by", { length: 255 }).references(() => users.id, { onDelete: "set null" }),
  assigned_at: timestamp("assigned_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  expires_at: timestamp("expires_at"), // Optional expiration override

  // Status
  is_active: boolean("is_active").default(true).notNull(),

  // Audit fields
  createdAt: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updatedAt: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
}, (table) => ({
  // Unique constraint: one assignment per voucher-customer pair
  uniqueVoucherCustomer: unique("unique_voucher_customer").on(table.voucher_id, table.customer_id),

  // Indexes
  idxVoucherAssignments: index("idx_voucher_assignments_voucher").on(table.voucher_id),
  idxCustomerAssignments: index("idx_voucher_assignments_customer").on(table.customer_id),
  idxActiveAssignments: index("idx_voucher_assignments_active").on(table.is_active, table.assigned_at),
}));

// Type definitions for vouchers
export type Voucher = typeof vouchers.$inferSelect;
export type NewVoucher = typeof vouchers.$inferInsert;
export type VoucherUsage = typeof voucher_usage.$inferSelect;
export type NewVoucherUsage = typeof voucher_usage.$inferInsert;
export type VoucherCustomerAssignment = typeof voucher_customer_assignments.$inferSelect;
export type NewVoucherCustomerAssignment = typeof voucher_customer_assignments.$inferInsert;

// Type definitions for class schedules
export type ClassSchedule = typeof class_schedules.$inferSelect;
export type NewClassSchedule = typeof class_schedules.$inferInsert;

// Type definitions for class bookings
export type ClassBooking = typeof class_bookings.$inferSelect;
export type NewClassBooking = typeof class_bookings.$inferInsert;

// Blog Categories Table
export const blogCategories = pgTable("blog_categories", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  tenantId: integer("tenant_id").notNull().references(() => tenants.id, { onDelete: "cascade" }),
  name: varchar("name", { length: 255 }).notNull(),
  slug: varchar("slug", { length: 255 }).notNull(),
  description: text("description"),
  color: varchar("color", { length: 7 }), // Hex color code
  is_active: boolean("is_active").default(true).notNull(),
  created_at: timestamp("created_at").defaultNow().notNull(),
  updated_at: timestamp("updated_at").defaultNow().notNull(),
}, (table) => ({
  tenantSlugIdx: uniqueIndex("blog_categories_tenant_slug_idx").on(table.tenantId, table.slug),
  tenantActiveIdx: index("blog_categories_tenant_active_idx").on(table.tenantId, table.is_active),
}));

// Blog Posts Table
export const blogPosts = pgTable("blog_posts", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  tenantId: integer("tenant_id").notNull().references(() => tenants.id, { onDelete: "cascade" }),
  title: varchar("title", { length: 500 }).notNull(),
  slug: varchar("slug", { length: 500 }).notNull(),
  content: text("content").notNull(),
  excerpt: text("excerpt"),
  featured_image: varchar("featured_image", { length: 1000 }),
  category_id: varchar("category_id", { length: 255 }).references(() => blogCategories.id, { onDelete: "set null" }),
  tags: jsonb("tags").$type<string[]>().default([]),
  status: varchar("status", { length: 20 }).notNull().default("draft"), // draft, published
  is_featured: boolean("is_featured").default(false).notNull(),
  published_at: timestamp("published_at"),
  author_id: varchar("author_id", { length: 255 }).notNull().references(() => users.id, { onDelete: "cascade" }),
  seo_title: varchar("seo_title", { length: 255 }),
  seo_description: text("seo_description"),
  view_count: integer("view_count").default(0).notNull(),
  created_at: timestamp("created_at").defaultNow().notNull(),
  updated_at: timestamp("updated_at").defaultNow().notNull(),
}, (table) => ({
  tenantSlugIdx: uniqueIndex("blog_posts_tenant_slug_idx").on(table.tenantId, table.slug),
  tenantStatusIdx: index("blog_posts_tenant_status_idx").on(table.tenantId, table.status),
  tenantFeaturedIdx: index("blog_posts_tenant_featured_idx").on(table.tenantId, table.is_featured),
  tenantCategoryIdx: index("blog_posts_tenant_category_idx").on(table.tenantId, table.category_id),
  publishedAtIdx: index("blog_posts_published_at_idx").on(table.published_at),
  authorIdx: index("blog_posts_author_idx").on(table.author_id),
}));

// Types
export type BlogCategory = typeof blogCategories.$inferSelect;
export type NewBlogCategory = typeof blogCategories.$inferInsert;
export type BlogPost = typeof blogPosts.$inferSelect;
export type NewBlogPost = typeof blogPosts.$inferInsert;

// ============================================================================
// Public API Tables
// ============================================================================

export const api_keys = pgTable("api_keys", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  name: varchar("name", { length: 255 }).notNull(),
  key_hash: varchar("key_hash", { length: 255 }).notNull().unique(),
  tenant_id: integer("tenant_id").references(() => tenants.id, { onDelete: "cascade" }).notNull(),
  permissions: text("permissions").notNull(), // JSON string of permissions
  rate_limit: text("rate_limit").notNull(), // JSON string of rate limit config
  is_active: boolean("is_active").default(true).notNull(),
  expires_at: timestamp("expires_at"),
  last_used_at: timestamp("last_used_at"),
  created_at: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updated_at: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});

export const api_key_usage = pgTable("api_key_usage", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  api_key_id: varchar("api_key_id", { length: 255 }).references(() => api_keys.id, { onDelete: "cascade" }).notNull(),
  endpoint: varchar("endpoint", { length: 255 }).notNull(),
  method: varchar("method", { length: 10 }).notNull(),
  status_code: integer("status_code"),
  response_time: integer("response_time"), // milliseconds
  ip_address: varchar("ip_address", { length: 45 }),
  user_agent: text("user_agent"),
  success: boolean("success").default(true).notNull(),
  error_message: text("error_message"),
  created_at: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});

export const audit_logs = pgTable("audit_logs", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  api_key_id: varchar("api_key_id", { length: 255 }).references(() => api_keys.id, { onDelete: "cascade" }),
  tenant_id: integer("tenant_id").references(() => tenants.id, { onDelete: "cascade" }).notNull(),
  action: varchar("action", { length: 255 }).notNull(),
  resource: varchar("resource", { length: 255 }).notNull(),
  resource_id: varchar("resource_id", { length: 255 }),
  changes: text("changes"), // JSON string of changes
  ip: varchar("ip", { length: 45 }).notNull(),
  user_agent: text("user_agent").notNull(),
  timestamp: timestamp("timestamp").default(sql`CURRENT_TIMESTAMP`).notNull(),
  success: boolean("success").default(true).notNull(),
  error: text("error"),
});

export const webhook_endpoints = pgTable("webhook_endpoints", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  tenant_id: integer("tenant_id").references(() => tenants.id, { onDelete: "cascade" }).notNull(),
  url: varchar("url", { length: 500 }).notNull(),
  events: text("events").notNull(), // JSON array of event types
  secret: varchar("secret", { length: 255 }),
  is_active: boolean("is_active").default(true).notNull(),
  retry_policy: text("retry_policy"), // JSON string of retry configuration
  created_at: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updated_at: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});

export const webhook_deliveries = pgTable("webhook_deliveries", {
  id: varchar("id", { length: 255 }).primaryKey().$defaultFn(() => createId()),
  webhook_endpoint_id: varchar("webhook_endpoint_id", { length: 255 }).references(() => webhook_endpoints.id, { onDelete: "cascade" }).notNull(),
  event_id: varchar("event_id", { length: 255 }).notNull(),
  event_type: varchar("event_type", { length: 255 }).notNull(),
  payload: text("payload").notNull(), // JSON string
  status: varchar("status", { length: 50 }).notNull(), // 'pending', 'delivered', 'failed'
  attempts: integer("attempts").default(0).notNull(),
  last_attempt_at: timestamp("last_attempt_at"),
  next_attempt_at: timestamp("next_attempt_at"),
  response_status: integer("response_status"),
  response_body: text("response_body"),
  created_at: timestamp("created_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
  updated_at: timestamp("updated_at").default(sql`CURRENT_TIMESTAMP`).notNull(),
});

// Public API Types
export type APIKey = typeof api_keys.$inferSelect;
export type NewAPIKey = typeof api_keys.$inferInsert;

export type APIKeyUsage = typeof api_key_usage.$inferSelect;
export type NewAPIKeyUsage = typeof api_key_usage.$inferInsert;

export type AuditLog = typeof audit_logs.$inferSelect;
export type NewAuditLog = typeof audit_logs.$inferInsert;

export type WebhookEndpoint = typeof webhook_endpoints.$inferSelect;
export type NewWebhookEndpoint = typeof webhook_endpoints.$inferInsert;

export type WebhookDelivery = typeof webhook_deliveries.$inferSelect;
export type NewWebhookDelivery = typeof webhook_deliveries.$inferInsert;

// ========================================
// RELATIONS
// ========================================
export const tenantsRelations = relations(tenants, ({ many }) => ({
  customers: many(customers),
  users: many(users),
  sessions: many(sessions),
  domains: many(tenantDomains),
}));

export const customersRelations = relations(customers, ({ one, many }) => ({
  tenant: one(tenants, {
    fields: [customers.tenantId],
    references: [tenants.id],
  }),
  oauthAccounts: many(customerOAuthAccounts),
}));

export const usersRelations = relations(users, ({ one }) => ({
  tenant: one(tenants, {
    fields: [users.tenantId],
    references: [tenants.id],
  }),
}));

export const customerOAuthAccountsRelations = relations(customerOAuthAccounts, ({ one }) => ({
  customer: one(customers, {
    fields: [customerOAuthAccounts.customerId],
    references: [customers.id],
  }),
  tenant: one(tenants, {
    fields: [customerOAuthAccounts.tenantId],
    references: [tenants.id],
  }),
}));

export const sessionsRelations = relations(sessions, ({ one }) => ({
  tenant: one(tenants, {
    fields: [sessions.tenantId],
    references: [tenants.id],
  }),
}));

export const tenantDomainsRelations = relations(tenantDomains, ({ one }) => ({
  tenant: one(tenants, {
    fields: [tenantDomains.tenantId],
    references: [tenants.id],
  }),
}));


// CREATE INDEX CONCURRENTLY idx_customers_tenant_email ON customers(tenant_id, email);
// CREATE INDEX CONCURRENTLY idx_customers_tenant_active ON customers(tenant_id, is_active);
// CREATE INDEX CONCURRENTLY idx_customers_google_id ON customers(google_id) WHERE google_id IS NOT NULL;
// CREATE INDEX CONCURRENTLY idx_users_tenant_role ON users(tenant_id, role);
// CREATE INDEX CONCURRENTLY idx_sessions_tenant_user ON sessions(tenant_id, user_id, user_type);
// CREATE INDEX CONCURRENTLY idx_sessions_token ON sessions(session_token);
// CREATE INDEX CONCURRENTLY idx_oauth_accounts_tenant_provider ON customer_oauth_accounts(tenant_id, provider, provider_account_id);
// CREATE INDEX CONCURRENTLY idx_tenant_domains_domain ON tenant_domains(domain);