// Script to check packages in database
import { db } from "../src/lib/db";
import { packages } from "../src/lib/db/schema";

async function checkPackages() {
  console.log('📦 Checking packages in database...');
  
  try {
    const result = await db.select().from(packages);
    console.log(`Found ${result.length} packages in database:`);
    
    result.forEach(pkg => {
      console.log(`- ${pkg.name} (ID: ${pkg.id}, Tenant: ${pkg.tenantId})`);
    });
    
  } catch (error) {
    console.error('❌ Error checking packages:', error);
  }
}

checkPackages().catch(console.error);
