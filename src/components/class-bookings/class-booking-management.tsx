"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DeleteConfirmationDialog } from "@/components/ui/delete-confirmation-dialog";
import { SuccessToast } from "@/components/ui/success-toast";
import {
  Calendar,
  User,
  Clock,
  CreditCard,
  Edit,
  Trash2,
  Plus,
  Search,
  Filter,
  CheckCircle,
  XCircle,
  AlertCircle,
  MapPin,
} from "lucide-react";
import { type ClassBookingWithRelations, type CreateClassBookingData } from "@/lib/hooks/queries/use-class-booking-queries";
import { ClassBookingForm } from "@/components/forms/class-booking-form";
import {
  useClassBookingSearchAdvanced,
  useCreateClassBooking,
  useUpdateClassBooking,
  useDeleteClassBooking,
  useCheckInBooking,
  useCancelBooking,
} from "@/lib/hooks/queries/use-class-booking-queries";

/**
 * ClassBookingManagement Component
 * 
 * Component untuk manage class bookings dengan full CRUD functionality.
 * Mengikuti pattern yang sama dengan LocationManagement dan EquipmentManagement.
 * 
 * Features:
 * - Display bookings dalam list layout dengan informasi lengkap
 * - Create, edit, delete bookings
 * - Check-in dan cancel booking functionality
 * - Search dan filtering berdasarkan status, customer, class
 * - Success feedback dengan SuccessToast
 * - Professional animations dan transitions
 */

interface ClassBookingManagementProps {
  tenantId: number;
  className?: string;
}

export function ClassBookingManagement({ tenantId, className = "" }: ClassBookingManagementProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingBooking, setEditingBooking] = useState<ClassBookingWithRelations | null>(null);
  const [deleteConfirmation, setDeleteConfirmation] = useState<{
    isOpen: boolean;
    booking: ClassBookingWithRelations | null;
  }>({ isOpen: false, booking: null });
  const [showSuccessToast, setShowSuccessToast] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [deleteError, setDeleteError] = useState<string | null>(null);

  // Build filters for search
  const filters = {
    tenantId,
    search: searchQuery || undefined,
    status: statusFilter !== "all" ? statusFilter : undefined,
  };

  // Queries
  const { data: searchResult, isLoading, error, refetch } = useClassBookingSearchAdvanced(filters, {
    limit: 50,
    offset: 0,
  });

  // Mutations
  const createBookingMutation = useCreateClassBooking();
  const updateBookingMutation = useUpdateClassBooking();
  const deleteBookingMutation = useDeleteClassBooking();
  const checkInMutation = useCheckInBooking();
  const cancelMutation = useCancelBooking();

  const bookings = searchResult?.bookings || [];

  const handleCreateBooking = async (data: CreateClassBookingData) => {
    try {
      await createBookingMutation.mutateAsync(data);
      setShowCreateForm(false);
      setSuccessMessage("Class booking created successfully!");
      setShowSuccessToast(true);
      refetch();
    } catch (error) {
      console.error("Failed to create booking:", error);
      throw error;
    }
  };

  const handleUpdateBooking = async (data: CreateClassBookingData) => {
    if (!editingBooking) return;

    try {
      await updateBookingMutation.mutateAsync({
        id: editingBooking.id,
        data: data as any,
      });
      setEditingBooking(null);
      setSuccessMessage("Class booking updated successfully!");
      setShowSuccessToast(true);
      refetch();
    } catch (error) {
      console.error("Failed to update booking:", error);
      throw error;
    }
  };

  const handleDeleteClick = (booking: ClassBookingWithRelations) => {
    setDeleteConfirmation({ isOpen: true, booking });
    setDeleteError(null);
  };

  const handleDeleteConfirm = async () => {
    if (!deleteConfirmation.booking) return;

    try {
      await deleteBookingMutation.mutateAsync(deleteConfirmation.booking.id);
      setDeleteConfirmation({ isOpen: false, booking: null });
      setSuccessMessage("Class booking deleted successfully!");
      setShowSuccessToast(true);
      refetch();
    } catch (error) {
      console.error("Failed to delete booking:", error);
      setDeleteError(error instanceof Error ? error.message : "Failed to delete booking");
    }
  };

  const handleCheckIn = async (booking: ClassBookingWithRelations) => {
    try {
      await checkInMutation.mutateAsync({ id: booking.id, tenantId });
      setSuccessMessage(`${booking.customerName} checked in successfully!`);
      setShowSuccessToast(true);
      refetch();
    } catch (error) {
      console.error("Failed to check in:", error);
    }
  };

  const handleCancel = async (booking: ClassBookingWithRelations) => {
    try {
      await cancelMutation.mutateAsync({ 
        id: booking.id, 
        tenantId,
        reason: "Cancelled by admin"
      });
      setSuccessMessage("Booking cancelled successfully!");
      setShowSuccessToast(true);
      refetch();
    } catch (error) {
      console.error("Failed to cancel booking:", error);
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      booked: { variant: "default" as const, icon: Calendar, color: "text-blue-600" },
      checked_in: { variant: "default" as const, icon: CheckCircle, color: "text-green-600" },
      cancelled: { variant: "destructive" as const, icon: XCircle, color: "text-red-600" },
      waitlisted: { variant: "secondary" as const, icon: Clock, color: "text-yellow-600" },
      no_show: { variant: "destructive" as const, icon: AlertCircle, color: "text-red-600" },
      completed: { variant: "default" as const, icon: CheckCircle, color: "text-green-600" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.booked;
    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {status.replace('_', ' ').toUpperCase()}
      </Badge>
    );
  };

  const formatDateTime = (date: Date | string | null) => {
    if (!date) return "Not set";
    const d = new Date(date);
    return d.toLocaleString();
  };

  if (showCreateForm) {
    return (
      <div className={className}>
        <ClassBookingForm
          tenantId={tenantId}
          onSubmit={handleCreateBooking}
          onCancel={() => setShowCreateForm(false)}
          isLoading={createBookingMutation.isPending}
        />
      </div>
    );
  }

  if (editingBooking) {
    return (
      <div className={className}>
        <ClassBookingForm
          booking={editingBooking}
          tenantId={tenantId}
          onSubmit={handleUpdateBooking}
          onCancel={() => setEditingBooking(null)}
          isLoading={updateBookingMutation.isPending}
        />
      </div>
    );
  }

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Class Bookings
              </CardTitle>
              <CardDescription>
                Manage class bookings, check-ins, and customer attendance
              </CardDescription>
            </div>
            <Button onClick={() => setShowCreateForm(true)} size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Add Booking
            </Button>
          </div>

          {/* Filters */}
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search */}
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search by customer name, class, or notes..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Status Filter */}
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-gray-500" />
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[150px]">
                  <SelectValue placeholder="All Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="booked">Booked</SelectItem>
                  <SelectItem value="checked_in">Checked In</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                  <SelectItem value="waitlisted">Waitlisted</SelectItem>
                  <SelectItem value="no_show">No Show</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {deleteError && (
            <Alert variant="destructive" className="mb-4">
              <AlertDescription>{deleteError}</AlertDescription>
            </Alert>
          )}

          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertDescription>
                Failed to load bookings: {error instanceof Error ? error.message : "Unknown error"}
              </AlertDescription>
            </Alert>
          )}

          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-2"></div>
                <p className="text-sm text-gray-600">Loading bookings...</p>
              </div>
            </div>
          ) : bookings.length === 0 ? (
            <div className="text-center py-8">
              <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No bookings found</h3>
              <p className="text-gray-600 mb-4">
                {searchQuery || statusFilter !== "all" 
                  ? "No bookings match your current filters." 
                  : "Get started by creating your first class booking."}
              </p>
              <Button onClick={() => setShowCreateForm(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Create First Booking
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {bookings.map((booking) => (
                <div
                  key={booking.id}
                  className="border rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <User className="h-4 w-4 text-gray-500" />
                        <h4 className="font-medium text-gray-900 dark:text-gray-100">
                          {booking.customerName}
                        </h4>
                        {getStatusBadge(booking.status || "booked")}
                        {booking.is_waitlist && (
                          <Badge variant="outline" className="text-xs">
                            Waitlist #{booking.waitlist_position}
                          </Badge>
                        )}
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-600 dark:text-gray-400">
                        <div className="flex items-center gap-2">
                          <Calendar className="h-3 w-3" />
                          <span>{booking.className}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Clock className="h-3 w-3" />
                          <span>{formatDateTime(booking.scheduleStartTime || null)}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <MapPin className="h-3 w-3" />
                          <span>{booking.locationName || "No location"}</span>
                        </div>
                        {booking.payment_status && (
                          <div className="flex items-center gap-2">
                            <CreditCard className="h-3 w-3" />
                            <span>{booking.payment_status} {booking.payment_method && `(${booking.payment_method})`}</span>
                          </div>
                        )}
                      </div>

                      {booking.notes && (
                        <p className="text-sm text-gray-600 dark:text-gray-400 mt-2 italic">
                          "{booking.notes}"
                        </p>
                      )}

                      <div className="flex items-center gap-2 mt-2">
                        <Badge variant="secondary" className="text-xs">
                          Booked: {formatDateTime(booking.booking_time)}
                        </Badge>
                        {booking.check_in_time && (
                          <Badge variant="secondary" className="text-xs">
                            Checked in: {formatDateTime(booking.check_in_time)}
                          </Badge>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center gap-2 ml-4">
                      {/* Quick Actions */}
                      {booking.status === "booked" && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleCheckIn(booking)}
                          disabled={checkInMutation.isPending}
                          className="text-green-600 hover:text-green-700 hover:bg-green-50"
                        >
                          <CheckCircle className="h-4 w-4" />
                        </Button>
                      )}
                      
                      {(booking.status === "booked" || booking.status === "waitlisted") && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleCancel(booking)}
                          disabled={cancelMutation.isPending}
                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          <XCircle className="h-4 w-4" />
                        </Button>
                      )}

                      {/* Edit */}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setEditingBooking(booking)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>

                      {/* Delete */}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteClick(booking)}
                        disabled={deleteBookingMutation.isPending}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationDialog
        isOpen={deleteConfirmation.isOpen}
        onClose={() => setDeleteConfirmation({ isOpen: false, booking: null })}
        onConfirm={handleDeleteConfirm}
        title="Delete Class Booking"
        description={`Are you sure you want to delete the booking for ${deleteConfirmation.booking?.customerName}? This action cannot be undone.`}
        isLoading={deleteBookingMutation.isPending}
      />

      {/* Success Toast */}
      <SuccessToast
        isVisible={showSuccessToast}
        title={successMessage}
        onClose={() => setShowSuccessToast(false)}
        type="general"
      />
    </div>
  );
}
