import { customers, db, tenantDomains, tenants, users } from "@/lib/db";
import { eq, and, or } from "drizzle-orm";
import { cache } from "react";
import { headers } from "next/headers";

/**
 * Enterprise Tenant Context Service
 * 
 * Handles multi-tenant context detection, validation, and management.
 * Implements caching and performance optimizations for FAANG-scale traffic.
 */

export interface TenantContext {
  id: number;
  name: string;
  subdomain: string;
  customDomain?: string | null;
  settings: TenantSettings;
  isActive: boolean;
  subscriptionTier: string;
  domain: string; // The domain used to access this tenant
  domainType: 'subdomain' | 'custom';
}

export interface TenantSettings {
  allowGoogleOAuth?: boolean;
  allowedDomains?: string[];
  requireEmailVerification?: boolean;
  sessionTimeout?: number;
  mfa?: boolean;
  customBranding?: {
    logo?: string;
    primaryColor?: string;
    secondaryColor?: string;
  };
}

export interface UserTenantScope {
  tenantId: number;
  userId: string;
  userType: 'customer' | 'staff';
  permissions?: string[];
  accessibleLocations?: string[];
}

class TenantContextService {
  private tenantCache = new Map<string, TenantContext>();
  private cacheExpiry = new Map<string, number>();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  /**
   * Get tenant context from current request
   * Uses Next.js headers to detect domain/subdomain
   */
  async getCurrentTenantContext(): Promise<TenantContext | null> {
    const headersList = headers();
    const host = headersList.get('host');
    const origin = headersList.get('origin');
    
    if (!host) {
      console.warn('TenantContext: No host header found');
      return null;
    }

    return this.getTenantByDomain(host);
  }

  /**
   * Get tenant context by domain with caching
   */
  async getTenantByDomain(domain: string): Promise<TenantContext | null> {
    // Check cache first
    const cached = this.getCachedTenant(domain);
    if (cached) {
      return cached;
    }

    try {
      // Extract subdomain and custom domain possibilities
      const { subdomain, customDomain } = this.parseDomain(domain);
      
      console.log(`🏢 TenantContext: Resolving domain "${domain}" -> subdomain: "${subdomain}", custom: "${customDomain}"`);

      // Query database for tenant
      let tenant;
      
      if (customDomain) {
        // First try custom domain
        const [customDomainRecord] = await db
          .select({
            tenant: tenants,
            domain: tenantDomains,
          })
          .from(tenantDomains)
          .innerJoin(tenants, eq(tenantDomains.tenantId, tenants.id))
          .where(
            and(
              eq(tenantDomains.domain, customDomain),
              eq(tenantDomains.isVerified, true),
              eq(tenants.isActive, true)
            )
          )
          .limit(1);

        if (customDomainRecord) {
          tenant = {
            ...customDomainRecord.tenant,
            domain: customDomain,
            domainType: 'custom' as const,
          };
        }
      }

      if (!tenant && subdomain) {
        // Try subdomain
        const [subdomainRecord] = await db
          .select()
          .from(tenants)
          .where(
            and(
              eq(tenants.subdomain, subdomain),
              eq(tenants.isActive, true)
            )
          )
          .limit(1);

        if (subdomainRecord) {
          tenant = {
            ...subdomainRecord,
            domain: domain,
            domainType: 'subdomain' as const,
          };
        }
      }

      if (!tenant) {
        console.warn(`🏢 TenantContext: No tenant found for domain "${domain}"`);
        return null;
      }

      const tenantContext: TenantContext = {
        id: tenant.id,
        name: tenant.name,
        subdomain: tenant.subdomain,
        customDomain: tenant.customDomain,
        settings: tenant.settings || {},
        isActive: tenant.isActive,
        subscriptionTier: tenant.subscriptionTier || 'basic',
        domain: tenant.domain,
        domainType: tenant.domainType,
      };

      // Cache the result
      this.cacheTenant(domain, tenantContext);

      console.log(`✅ TenantContext: Resolved tenant "${tenantContext.name}" (ID: ${tenantContext.id})`);
      return tenantContext;

    } catch (error) {
      console.error('🚨 TenantContext: Error resolving tenant:', error);
      return null;
    }
  }

  /**
   * Get user's tenant scope and permissions
   */
  async getUserTenantScope(userId: string, userType: 'customer' | 'staff'): Promise<UserTenantScope | null> {
    try {
      if (userType === 'customer') {
        const [customer] = await db
          .select({
            tenantId: customers.tenantId,
            userId: customers.id,
          })
          .from(customers)
          .where(eq(customers.id, userId))
          .limit(1);

        if (!customer) return null;

        return {
          tenantId: customer.tenantId,
          userId: customer.userId,
          userType: 'customer',
        };
      } else {
        const [user] = await db
          .select({
            tenantId: users.tenantId,
            userId: users.id,
            permissions: users.permissions,
            accessibleLocations: users.accessibleLocations,
          })
          .from(users)
          .where(eq(users.id, userId))
          .limit(1);

        if (!user) return null;

        return {
          tenantId: user.tenantId || 0, // Default tenant for super admins
          userId: user.userId,
          userType: 'staff',
          permissions: user.permissions || [],
          accessibleLocations: user.accessibleLocations || [],
        };
      }
    } catch (error) {
      console.error('🚨 TenantContext: Error getting user tenant scope:', error);
      return null;
    }
  }

  /**
   * Validate if user has access to tenant
   */
  async validateUserTenantAccess(userId: string, userType: 'customer' | 'staff', tenantId: number): Promise<boolean> {
    try {
      const userScope = await this.getUserTenantScope(userId, userType);
      
      if (!userScope) return false;
      
      // Super admins (staff with no tenantId) can access any tenant
      if (userType === 'staff' && userScope.tenantId === 0) {
        return true;
      }
      
      return userScope.tenantId === tenantId;
    } catch (error) {
      console.error('🚨 TenantContext: Error validating user tenant access:', error);
      return false;
    }
  }

  /**
   * Get tenant configuration for OAuth
   */
  async getTenantOAuthConfig(tenantId: number): Promise<{
    allowGoogleOAuth: boolean;
    allowedDomains: string[];
    requireEmailVerification: boolean;
  }> {
    try {
      const [tenant] = await db
        .select({ settings: tenants.settings })
        .from(tenants)
        .where(eq(tenants.id, tenantId))
        .limit(1);

      const settings = tenant?.settings || {};
      
      return {
        allowGoogleOAuth: settings.allowGoogleOAuth ?? true,
        allowedDomains: settings.allowedDomains || [],
        requireEmailVerification: settings.requireEmailVerification ?? true,
      };
    } catch (error) {
      console.error('🚨 TenantContext: Error getting OAuth config:', error);
      return {
        allowGoogleOAuth: false,
        allowedDomains: [],
        requireEmailVerification: true,
      };
    }
  }

  /**
   * Parse domain to extract subdomain and custom domain possibilities
   */
  private parseDomain(domain: string): { subdomain?: string; customDomain?: string } {
    // Remove port if present
    const cleanDomain = domain.split(':')[0];
    
    // Check if it's a custom domain (not a subdomain of your main domain)
    const mainDomains = [
      process.env.NEXT_PUBLIC_MAIN_DOMAIN || 'localhost',
      'vercel.app',
      'netlify.app',
    ];
    
    const isSubdomain = mainDomains.some(mainDomain => 
      cleanDomain.endsWith(`.${mainDomain}`)
    );
    
    if (isSubdomain) {
      // Extract subdomain
      const parts = cleanDomain.split('.');
      if (parts.length >= 3) {
        return { subdomain: parts[0] };
      }
    } else {
      // Treat as custom domain
      return { customDomain: cleanDomain };
    }
    
    return {};
  }

  /**
   * Cache management
   */
  private getCachedTenant(domain: string): TenantContext | null {
    const cached = this.tenantCache.get(domain);
    const expiry = this.cacheExpiry.get(domain);
    
    if (cached && expiry && Date.now() < expiry) {
      return cached;
    }
    
    // Clean up expired cache
    if (cached) {
      this.tenantCache.delete(domain);
      this.cacheExpiry.delete(domain);
    }
    
    return null;
  }

  private cacheTenant(domain: string, tenant: TenantContext): void {
    this.tenantCache.set(domain, tenant);
    this.cacheExpiry.set(domain, Date.now() + this.CACHE_TTL);
  }

  /**
   * Clear cache (useful for testing or when tenant settings change)
   */
  clearCache(domain?: string): void {
    if (domain) {
      this.tenantCache.delete(domain);
      this.cacheExpiry.delete(domain);
    } else {
      this.tenantCache.clear();
      this.cacheExpiry.clear();
    }
  }
}

// Export singleton instance
export const tenantContextService = new TenantContextService();

// React cache wrapper for SSR
export const getCurrentTenantContext = cache(async (): Promise<TenantContext | null> => {
  return tenantContextService.getCurrentTenantContext();
});