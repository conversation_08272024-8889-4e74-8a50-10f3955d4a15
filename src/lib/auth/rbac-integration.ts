import { NextAuthConfig } from "next-auth";
import { JWT } from "next-auth/jwt";
import { Session } from "next-auth";
import { RoleService } from "@/lib/services/role.service";
import { PermissionService } from "@/lib/services/permission.service";
import { LocationAccessService } from "@/lib/services/location-access.service";

/**
 * RBAC Integration dengan NextAuth v5
 * 
 * File ini handle integrasi antara RBAC system dengan NextAuth.
 * <PERSON><PERSON><PERSON><PERSON> pattern yang sama dengan auth config yang sudah ada.
 * 
 * Ini kayak "bridge" antara authentication dan authorization.
 */

// Extended types untuk include RBAC data
declare module "next-auth" {
  interface Session {
    user: {
      id: string;
      email: string;
      name?: string | null;
      image?: string | null;
      tenantId?: number | null;
      roles?: string[];
      permissions?: string[];
      accessibleLocations?: string[];
    };
  }

  interface User {
    id: string;
    email: string;
    name?: string | null;
    image?: string | null;
    tenantId?: number | null;
    roles?: string[];
    permissions?: string[];
    accessibleLocations?: string[];
  }
}

declare module "next-auth/jwt" {
  interface JWT {
    id: string;
    email: string;
    name?: string | null;
    image?: string | null;
    tenantId?: number | null;
    roles?: string[];
    permissions?: string[];
    accessibleLocations?: string[];
    rbacLastUpdated?: number; // timestamp untuk cache invalidation
  }
}

/**
 * RBAC callbacks untuk NextAuth
 * 
 * Ini functions yang dipanggil NextAuth untuk enrich session dengan RBAC data.
 */
export const rbacCallbacks = {
  /**
   * JWT callback - dipanggil setiap kali JWT token dibuat/diupdate
   */
  async jwt({ token, user, trigger }: { token: JWT; user?: any; trigger?: string }): Promise<JWT> {
    try {
      console.log("JWT callback called with:", { userId: token.id, trigger, hasUser: !!user });

      // Jika ada user baru (login), set basic info
      if (user) {
        token.id = user.id;
        token.email = user.email;
        token.name = user.name;
        token.image = user.image;
        token.tenantId = user.tenantId;
        console.log("User data set in token:", { id: token.id, email: token.email });
      }

      // Check if we need to refresh RBAC data
      const shouldRefreshRBAC =
        !token.rbacLastUpdated || // belum pernah load
        (Date.now() - token.rbacLastUpdated) > (5 * 60 * 1000) || // lebih dari 5 menit
        trigger === "update"; // manual update

      console.log("Should refresh RBAC:", shouldRefreshRBAC, {
        hasLastUpdated: !!token.rbacLastUpdated,
        timeSinceUpdate: token.rbacLastUpdated ? Date.now() - token.rbacLastUpdated : 0,
        trigger
      });

      if (shouldRefreshRBAC && token.id) {
        console.log("Loading RBAC data for user:", token.id);
        // Load RBAC data
        const rbacData = await loadUserRBACData(token.id, token.tenantId);

        console.log("RBAC data loaded:", rbacData);

        token.roles = rbacData.roles;
        token.permissions = rbacData.permissions;
        token.accessibleLocations = rbacData.accessibleLocations;
        token.rbacLastUpdated = Date.now();
      }

      return token;
    } catch (error) {
      console.error("JWT callback error:", error);
      // Return token tanpa RBAC data jika error
      return token;
    }
  },

  /**
   * Session callback - dipanggil setiap kali session diakses
   */
  async session({ session, token }: { session: Session; token: JWT }): Promise<Session> {
    try {
      console.log("Session callback called with token:", {
        id: token.id,
        email: token.email,
        roles: token.roles,
        permissions: token.permissions
      });

      if (token && session.user) {
        session.user.id = token.id;
        session.user.email = token.email;
        session.user.name = token.name;
        session.user.image = token.image;
        session.user.tenantId = token.tenantId;
        session.user.roles = token.roles || [];
        session.user.permissions = token.permissions || [];
        session.user.accessibleLocations = token.accessibleLocations || [];

        console.log("Session user data set:", {
          id: session.user.id,
          email: session.user.email,
          roles: session.user.roles,
          permissions: session.user.permissions
        });
      }

      return session;
    } catch (error) {
      console.error("Session callback error:", error);
      return session;
    }
  },
};

/**
 * Load RBAC data untuk user
 * 
 * Function ini compile semua RBAC data untuk user dari database.
 */
async function loadUserRBACData(
  userId: string, 
  tenantId?: number | null
): Promise<{
  roles: string[];
  permissions: string[];
  accessibleLocations: string[];
}> {
  try {
    // Get user roles dengan role names
    const userRolesResult = await RoleService.getUserRoles(tenantId, userId);

    // Get role names dari roleIds
    const roleNames: string[] = [];
    for (const userRole of userRolesResult.userRoles) {
      try {
        const role = await RoleService.getById(userRole.roleId);
        if (role) {
          roleNames.push(role.name);
        }
      } catch (error) {
        console.error(`Error getting role ${userRole.roleId}:`, error);
      }
    }

    // Get user permissions
    const userPermissions = await PermissionService.getUserPermissions(userId, tenantId);
    const permissions = userPermissions.map(p => `${p.module}.${p.action}`);

    // Get accessible locations (jika ada tenantId)
    let accessibleLocations: string[] = [];
    if (tenantId) {
      try {
        const locationAccess = await LocationAccessService.getUserAccessibleLocations(userId, tenantId);
        accessibleLocations = locationAccess.map(la => la.locationId);
      } catch (error) {
        console.error("Error getting accessible locations:", error);
        // Ignore error, just use empty array
      }
    }

    return {
      roles: roleNames,
      permissions,
      accessibleLocations,
    };
  } catch (error) {
    console.error("Error loading RBAC data:", error);
    return {
      roles: [],
      permissions: [],
      accessibleLocations: [],
    };
  }
}

/**
 * Helper functions untuk check permissions di client/server components
 */
export const RBACHelpers = {
  /**
   * Check if user has specific permission
   */
  hasPermission: (session: Session | null, module: string, action: string): boolean => {
    if (!session?.user?.permissions) return false;
    return session.user.permissions.includes(`${module}.${action}`);
  },

  /**
   * Check if user has specific role
   */
  hasRole: (session: Session | null, roleName: string): boolean => {
    if (!session?.user?.roles) return false;
    return session.user.roles.some(role => role.includes(roleName));
  },

  /**
   * Check if user is super admin
   */
  isSuperAdmin: (session: Session | null): boolean => {
    return RBACHelpers.hasPermission(session, "system", "manage") ||
           RBACHelpers.hasRole(session, "super_admin");
  },

  /**
   * Check if user is tenant admin
   */
  isTenantAdmin: (session: Session | null): boolean => {
    return RBACHelpers.hasPermission(session, "tenant", "manage") ||
           RBACHelpers.hasRole(session, "tenant_admin");
  },

  /**
   * Check if user can access specific tenant
   */
  canAccessTenant: (session: Session | null, tenantId: number): boolean => {
    if (RBACHelpers.isSuperAdmin(session)) return true;
    return session?.user?.tenantId === tenantId;
  },

  /**
   * Check if user can access specific location
   */
  canAccessLocation: (session: Session | null, locationId: string): boolean => {
    if (RBACHelpers.isSuperAdmin(session)) return true;
    return session?.user?.accessibleLocations?.includes(locationId) || false;
  },

  /**
   * Get user's accessible locations
   */
  getAccessibleLocations: (session: Session | null): string[] => {
    return session?.user?.accessibleLocations || [];
  },

  /**
   * Get user's permissions
   */
  getPermissions: (session: Session | null): string[] => {
    return session?.user?.permissions || [];
  },

  /**
   * Get user's roles
   */
  getRoles: (session: Session | null): string[] => {
    return session?.user?.roles || [];
  },
};

/**
 * Server-side permission check
 * 
 * Function ini untuk check permissions di server components atau API routes.
 */
export async function checkServerPermission(
  userId: string,
  module: string,
  action: string,
  tenantId?: number | null
): Promise<boolean> {
  try {
    return await PermissionService.userHasPermission(userId, module, action, tenantId);
  } catch (error) {
    console.error("Server permission check error:", error);
    return false;
  }
}

/**
 * Refresh RBAC data untuk user
 * 
 * Function ini untuk force refresh RBAC data, misalnya setelah role assignment.
 */
export async function refreshUserRBAC(userId: string): Promise<void> {
  try {
    // Ini akan trigger JWT callback dengan trigger: "update"
    // Implementation tergantung pada NextAuth setup
    console.log(`Refreshing RBAC data for user: ${userId}`);
    // TODO: Implement actual refresh mechanism
  } catch (error) {
    console.error("Error refreshing user RBAC:", error);
  }
}
