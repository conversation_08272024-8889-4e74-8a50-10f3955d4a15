-- ALTE<PERSON> TABLE "class_subcategories " RENAME TO "class_subcategories";--> statement-breakpoint
-- ALTER TABLE "class_subcategories" DROP CONSTRAINT "class_subcategories _category_id_class_categories_id_fk";
-- --> statement-breakpoint
-- ALTER TABLE "classes" DROP CONSTRAINT "classes_subcategory_id_class_subcategories _id_fk";
-- --> statement-breakpoint
-- ALTER TABLE "class_membership_plans " ADD CONSTRAINT "class_membership_plans _class_id_membership_plan_id_pk" PRIMARY KEY("class_id","membership_plan_id");--> statement-breakpoint
-- ALTER TABLE "class_package_plans" ADD CONSTRAINT "class_package_plans_class_id_package_id_pk" PRIMARY KEY("class_id","package_id");--> statement-breakpoint
-- ALTER TABLE "package_category_rel" ADD CONSTRAINT "package_category_rel_package_id_category_id_pk" PRIMARY KEY("package_id","category_id");--> statement-breakpoint
-- ALTER TABLE "package_locations" ADD CONSTRAINT "package_locations_package_id_location_id_pk" PRIMARY KEY("package_id","location_id");--> statement-breakpoint
-- ALTER TABLE "class_subcategories" ADD COLUMN "tenant_id" integer NOT NULL;--> statement-breakpoint
ALTER TABLE "classes" ADD COLUMN "images" jsonb DEFAULT '[]'::jsonb;--> statement-breakpoint
-- ALTER TABLE "class_subcategories" ADD CONSTRAINT "class_subcategories_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
-- ALTER TABLE "class_subcategories" ADD CONSTRAINT "class_subcategories_category_id_class_categories_id_fk" FOREIGN KEY ("category_id") REFERENCES "public"."class_categories"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
-- ALTER TABLE "classes" ADD CONSTRAINT "classes_subcategory_id_class_subcategories_id_fk" FOREIGN KEY ("subcategory_id") REFERENCES "public"."class_subcategories"("id") ON DELETE cascade ON UPDATE no action;