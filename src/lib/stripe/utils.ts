import { stripe } from "./config";
import { db } from "../db";
import { stripeCustomers, creditTransactions, users, organizations } from "../db/schema";
import { eq, and, desc, sql } from "drizzle-orm";
import { createId } from "@paralleldrive/cuid2";

// Create or get Stripe customer
export async function createOrGetStripeCustomer(
  userId: string,
  organizationId?: string
): Promise<string> {
  try {
    // Check if customer already exists
    const [existingCustomer] = await db
      .select()
      .from(stripeCustomers)
      .where(
        organizationId
          ? eq(stripeCustomers.organizationId, organizationId)
          : eq(stripeCustomers.userId, userId)
      )
      .limit(1);

    if (existingCustomer) {
      return existingCustomer.stripeCustomerId;
    }

    // Get user details
    const [user] = await db
      .select()
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);

    if (!user) {
      throw new Error("User not found");
    }

    // Create Stripe customer
    const customer = await stripe.customers.create({
      email: user.email,
      name: user.name || undefined,
      metadata: {
        userId,
        organizationId: organizationId || "",
      },
    });

    // Save customer to database
    await db
      .insert(stripeCustomers)
      .values({
        id: createId(),
        userId: organizationId ? null : userId,
        organizationId: organizationId || null,
        stripeCustomerId: customer.id,
      });

    return customer.id;
  } catch (error) {
    console.error("Error creating Stripe customer:", error);
    throw new Error("Failed to create Stripe customer");
  }
}

// Create checkout session for credit purchase
export async function createCheckoutSession({
  userId,
  organizationId,
  priceId,
  credits,
  successUrl,
  cancelUrl,
}: {
  userId: string;
  organizationId?: string;
  priceId: string;
  credits: number;
  successUrl: string;
  cancelUrl: string;
}) {
  try {
    const customerId = await createOrGetStripeCustomer(userId, organizationId);

    const session = await stripe.checkout.sessions.create({
      customer: customerId,
      payment_method_types: ["card"],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      mode: "payment",
      success_url: successUrl,
      cancel_url: cancelUrl,
      metadata: {
        userId,
        organizationId: organizationId || "",
        credits: credits.toString(),
        type: "credit_purchase",
      },
    });

    return session;
  } catch (error) {
    console.error("Error creating checkout session:", error);
    throw new Error("Failed to create checkout session");
  }
}

// Add credits to user or organization
export async function addCredits({
  userId,
  organizationId,
  credits,
  description,
  stripePaymentIntentId,
  type = "purchase",
}: {
  userId: string;
  organizationId?: string;
  credits: number;
  description: string;
  stripePaymentIntentId?: string;
  type?: "purchase" | "bonus" | "monthly_refresh";
}) {
  try {
    await db.transaction(async (tx) => {
      // Add credits to user or organization
      if (organizationId) {
        await tx
          .update(organizations)
          .set({
            credits: sql`credits + ${credits}`,
            updatedAt: new Date(),
          })
          .where(eq(organizations.id, organizationId));
      } else {
        await tx
          .update(users)
          .set({
            credits: sql`credits + ${credits}`,
            updatedAt: new Date(),
          })
          .where(eq(users.id, userId));
      }

      // Record transaction
      await tx
        .insert(creditTransactions)
        .values({
          id: createId(),
          userId: organizationId ? null : userId,
          organizationId: organizationId || null,
          type,
          amount: credits,
          description,
          stripePaymentIntentId,
        });
    });

    return true;
  } catch (error) {
    console.error("Error adding credits:", error);
    throw new Error("Failed to add credits");
  }
}

// Deduct credits from user or organization
export async function deductCredits({
  userId,
  organizationId,
  credits,
  description,
}: {
  userId: string;
  organizationId?: string;
  credits: number;
  description: string;
}) {
  try {
    const result = await db.transaction(async (tx) => {
      // Check current credits
      let currentCredits = 0;
      
      if (organizationId) {
        const [org] = await tx
          .select({ credits: organizations.credits })
          .from(organizations)
          .where(eq(organizations.id, organizationId))
          .limit(1);
        
        if (!org) {
          throw new Error("Organization not found");
        }
        
        currentCredits = org.credits;
      } else {
        const [user] = await tx
          .select({ credits: users.credits })
          .from(users)
          .where(eq(users.id, userId))
          .limit(1);
        
        if (!user) {
          throw new Error("User not found");
        }
        
        currentCredits = user.credits;
      }

      // Check if sufficient credits
      if (currentCredits < credits) {
        throw new Error("Insufficient credits");
      }

      // Deduct credits
      if (organizationId) {
        await tx
          .update(organizations)
          .set({
            credits: sql`credits - ${credits}`,
            updatedAt: new Date(),
          })
          .where(eq(organizations.id, organizationId));
      } else {
        await tx
          .update(users)
          .set({
            credits: sql`credits - ${credits}`,
            updatedAt: new Date(),
          })
          .where(eq(users.id, userId));
      }

      // Record transaction
      await tx
        .insert(creditTransactions)
        .values({
          id: createId(),
          userId: organizationId ? null : userId,
          organizationId: organizationId || null,
          type: "usage",
          amount: -credits,
          description,
        });

      return { success: true, remainingCredits: currentCredits - credits };
    });

    return result;
  } catch (error) {
    console.error("Error deducting credits:", error);
    throw error;
  }
}

// Get credit balance
export async function getCreditBalance(userId: string, organizationId?: string): Promise<number> {
  try {
    if (organizationId) {
      const [org] = await db
        .select({ credits: organizations.credits })
        .from(organizations)
        .where(eq(organizations.id, organizationId))
        .limit(1);
      
      return org?.credits || 0;
    } else {
      const [user] = await db
        .select({ credits: users.credits })
        .from(users)
        .where(eq(users.id, userId))
        .limit(1);
      
      return user?.credits || 0;
    }
  } catch (error) {
    console.error("Error getting credit balance:", error);
    return 0;
  }
}

// Get credit transactions history
export async function getCreditTransactions(
  userId: string,
  organizationId?: string,
  limit = 50
) {
  try {
    const transactions = await db
      .select()
      .from(creditTransactions)
      .where(
        organizationId
          ? eq(creditTransactions.organizationId, organizationId)
          : eq(creditTransactions.userId, userId)
      )
      .orderBy(desc(creditTransactions.createdAt))
      .limit(limit);

    return transactions;
  } catch (error) {
    console.error("Error getting credit transactions:", error);
    return [];
  }
}
