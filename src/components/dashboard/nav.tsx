"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { useState } from "react";
import { cn } from "@/lib/utils";
import { useRBAC } from "@/lib/hooks/use-rbac";
import {
  LayoutDashboard,
  CreditCard,
  Settings,
  Users,
  BarChart3,
  FileText,
  Shield,
  Zap,
  MapPin,
  Wrench,
  Layers,
  GraduationCap,
  DollarSign,
  User,
  Tag,
  Package,
  BookOpen,
  FolderOpen,
  Grid3X3,
  Building,
  MapPinCheck,
  Calendar,
  ChevronDown,
  ChevronRight,
  TicketPercent,
} from "lucide-react";
import type { AuthUser } from "@/types/auth";

interface DashboardNavProps {
  user: AuthUser;
}

interface NavItem {
  title: string;
  href: string;
  icon: any;
  requiredPermission?: {
    module: string;
    action: string;
  };
  children?: NavItem[];
}

const navItems: NavItem[] = [
  {
    title: "Dashboard",
    href: "/dashboard",
    icon: LayoutDashboard,
    // Dashboard accessible to all authenticated users
  },

  {
    title: "Class Management",
    href: "/classes",
    icon: BookOpen,
    requiredPermission: { module: "classes", action: "manage" },
    children: [
      {
        title: "Class Schedules",
        href: "/class-schedules",
        icon: MapPinCheck,
        requiredPermission: { module: "class-schedules", action: "manage" },
      },
      {
        title: "Class Bookings",
        href: "/class-bookings",
        icon: Calendar,
        requiredPermission: { module: "bookings", action: "manage" },
      },
      {
        title: "Class Level",
        href: "/class-levels",
        icon: BookOpen,
        requiredPermission: { module: "class-level", action: "manage" },
      },
      {
        title: "Classes",
        href: "/classes",
        icon: BookOpen,
        requiredPermission: { module: "classes", action: "manage" },
      },
      {
        title: "Class Categories",
        href: "/class-categories",
        icon: FolderOpen,
        requiredPermission: { module: "classes", action: "manage" },
      },
      {
        title: "Class Subcategories",
        href: "/class-subcategories",
        icon: Grid3X3,
        requiredPermission: { module: "classes", action: "manage" },
      },
    ],
  },
  {
    title: "Master Management",
    href: "/locations",
    icon: BookOpen,
    requiredPermission: { module: "locations", action: "manage" },
    children: [
      
      {
        title: "Admin",
        href: "/admin",
        icon: Shield,
        requiredPermission: { module: "admin", action: "access" },
      },
      {
        title: "Business",
        href: "/business",
        icon: Zap,
      },
      {
        title: "Equipment",
        href: "/equipment",
        icon: MapPinCheck,
        requiredPermission: { module: "equipment", action: "manage" },
      },
      {
        title: "Equipment Instances",
        href: "/equipment-instances",
        icon: MapPinCheck,
        requiredPermission: { module: "equipment-instances", action: "manage" },
      },
      {
        title: "Facilities",
        href: "/facilities",
        icon: Calendar,
        requiredPermission: { module: "facilities", action: "manage" },
      },
      {
        title: "Locations",
        href: "/locations",
        icon: Calendar,
        requiredPermission: { module: "locations", action: "manage" },
      },
      {
        title: "Role Management",
        href: "/role-management",
        icon: Calendar,
        requiredPermission: { module: "role-management", action: "manage" },
      },
      {
        title: "Tags",
        href: "/tags",
        icon: BookOpen,
        requiredPermission: { module: "tags", action: "manage" },
      },
      {
        title: "TanStack Demo",
        href: "/business-tanstack",
        icon: Zap,
      },
    ],
  },
  {
    title: "Membership Plans",
    href: "/membership-plans",
    icon: CreditCard,
    requiredPermission: { module: "packages", action: "manage" },
  },
  
  {
    title: "Customers",
    href: "/customers",
    icon: User,
    requiredPermission: { module: "customers", action: "manage" },
  },
  {
    title: "Package Management",
    href: "/packages",
    icon: Package,
    requiredPermission: { module: "packages", action: "manage" },
    children: [
      {
        title: "Packages",
        href: "/packages",
        icon: Package,
        requiredPermission: { module: "packages", action: "manage" },
      },
      {
        title: "Package Locations",
        href: "/package-locations",
        icon: MapPin,
        requiredPermission: { module: "packages", action: "manage" },
      },
      {
        title: "Package Categories",
        href: "/package-categories",
        icon: FolderOpen,
        requiredPermission: { module: "packages", action: "manage" },
      },
      {
        title: "Pricing Groups",
        href: "/pricing-groups",
        icon: DollarSign,
        requiredPermission: { module: "packages", action: "manage" },
      },
      {
        title: "Package Pricing",
        href: "/package-pricing",
        icon: DollarSign,
        requiredPermission: { module: "packages", action: "manage" },
      },
      {
        title: "Voucher",
        href: "/vouchers",
        icon: TicketPercent,
        requiredPermission: { module: "vouchers", action: "manage" },
      },
      {
        title: "Blog Management",
        href: "/blog-management",
        icon: FileText,
        requiredPermission: { module: "blog", action: "manage" },
      },
    ],
  },
  {
    title: "Settings",
    href: "/packages",
    icon: Package,
    requiredPermission: { module: "packages", action: "manage" },
    children: [
      {
        title: "Analytics",
        href: "/analytics",
        icon: Package,
        requiredPermission: { module: "reports", action: "read" },
      },
      {
        title: "Animation Demo",
        href: "/animation-demo",
        icon: MapPin,
        // requiredPermission: { module: "animation-demo", action: "manage" },
      },
      {
        title: "API KEYS",
        href: "/api-keys",
        icon: MapPin,
        requiredPermission: { module: "animation-demo", action: "manage" },
      },
      {
        title: "Billing",
        href: "/billing",
        icon: FolderOpen,
        requiredPermission: { module: "admin", action: "manage" },
      },
      {
        title: "Documentations",
        href: "/docs",
        icon: DollarSign,
      },
      {
        title: "RBAC Test",
        href: "/rbac-test",
        icon: DollarSign,
      },
      {
        title: "Waiver Forms",
        href: "/waiver-forms",
        icon: FileText,
        requiredPermission: { module: "customers", action: "manage" },
      },
    ],
  },
];

const adminNavItems: NavItem[] = [
  // {
  //   title: "Admin Panel",
  //   href: "/admin",
  //   icon: Users,
  //   requiredPermission: { module: "admin", action: "access" },
  // },
];

export function DashboardNav({ user }: DashboardNavProps) {
  const pathname = usePathname();
  const { hasPermission, isLoading, permissions, roles } = useRBAC();
  const [expandedGroups, setExpandedGroups] = useState<Set<string>>(
    new Set(["Class Management", "Package Management"])
  );

  const toggleGroup = (groupTitle: string) => {
    setExpandedGroups((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(groupTitle)) {
        newSet.delete(groupTitle);
      } else {
        newSet.add(groupTitle);
      }
      return newSet;
    });
  };

  const isGroupExpanded = (groupTitle: string) =>
    expandedGroups.has(groupTitle);

  const isGroupActive = (item: NavItem) => {
    if (item.children) {
      return item.children.some(
        (child) =>
          pathname === child.href ||
          (child.href !== "/dashboard" && pathname.startsWith(child.href))
      );
    }
    return (
      pathname === item.href ||
      (item.href !== "/dashboard" && pathname.startsWith(item.href))
    );
  };

  // Debug logging
  console.log("DashboardNav Debug:", {
    isLoading,
    permissions,
    roles,
    userEmail: user?.email,
  });

  // Combine all nav items
  const allNavItems = [...navItems, ...adminNavItems];

  // Filter nav items based on RBAC permissions
  const filteredNavItems = allNavItems.filter((item) => {
    // Handle grouped items
    if (item.children) {
      // Filter children first
      const filteredChildren = item.children.filter((child) => {
        if (!child.requiredPermission) {
          return true;
        }

        if (user?.email === "<EMAIL>") {
          return true;
        }

        const permissionString = `${child.requiredPermission.module}.${child.requiredPermission.action}`;
        return hasPermission(permissionString);
      });

      // Show group if it has any accessible children
      if (filteredChildren.length > 0) {
        item.children = filteredChildren;
        return true;
      }
      return false;
    }

    // Handle regular items
    if (!item.requiredPermission) {
      return true;
    }

    if (user?.email === "<EMAIL>") {
      return true;
    }

    const permissionString = `${item.requiredPermission.module}.${item.requiredPermission.action}`;
    const hasAccess = hasPermission(permissionString);

    console.log(`Permission check for ${item.title}:`, {
      required: permissionString,
      hasAccess,
      allPermissions: permissions,
    });

    return hasAccess;
  });

  // Show loading state while RBAC data is loading
  if (isLoading || permissions.length === 0) {
    return (
      <nav className="w-64 border-r bg-background p-6">
        <div className="space-y-2">
          <div className="flex items-center space-x-3 rounded-lg px-3 py-2">
            <div className="h-4 w-4 bg-gray-200 rounded animate-pulse" />
            <div className="h-4 w-20 bg-gray-200 rounded animate-pulse" />
          </div>
          {Array.from({ length: 8 }).map((_, i) => (
            <div
              key={i}
              className="flex items-center space-x-3 rounded-lg px-3 py-2"
            >
              <div className="h-4 w-4 bg-gray-200 rounded animate-pulse" />
              <div className="h-4 w-24 bg-gray-200 rounded animate-pulse" />
            </div>
          ))}
        </div>
      </nav>
    );
  }

  return (
    <nav className="w-64 border-r bg-background p-6">
      <div className="space-y-2">
        {filteredNavItems.map((item) => {
          // Handle grouped navigation items
          if (item.children) {
            const isExpanded = isGroupExpanded(item.title);
            const isActive = isGroupActive(item);

            return (
              <div key={item.title}>
                {/* Group Header */}
                <button
                  onClick={() => toggleGroup(item.title)}
                  className={cn(
                    "flex items-center justify-between w-full rounded-lg px-3 py-2 text-sm font-medium transition-colors",
                    isActive
                      ? "bg-primary/10 text-primary"
                      : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
                  )}
                >
                  <div className="flex items-start space-x-3">
                    <item.icon className="h-4 w-4" />
                    <span>{item.title}</span>
                  </div>
                  {isExpanded ? (
                    <ChevronDown className="h-4 w-4" />
                  ) : (
                    <ChevronRight className="h-4 w-4" />
                  )}
                </button>

                {/* Group Children */}
                {isExpanded && (
                  <div className="ml-6 mt-1 space-y-1">
                    {item.children.map((child) => {
                      const isChildActive =
                        pathname === child.href ||
                        (child.href !== "/dashboard" &&
                          pathname.startsWith(child.href));

                      return (
                        <Link
                          key={child.href}
                          href={child.href}
                          className={cn(
                            "flex items-center space-x-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors",
                            isChildActive
                              ? "bg-primary text-primary-foreground"
                              : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
                          )}
                        >
                          <child.icon className="h-4 w-4" />
                          <span>{child.title}</span>
                        </Link>
                      );
                    })}
                  </div>
                )}
              </div>
            );
          }

          // Handle regular navigation items
          const isActive =
            pathname === item.href ||
            (item.href !== "/dashboard" && pathname.startsWith(item.href));

          return (
            <Link
              key={item.href}
              href={item.href}
              className={cn(
                "flex items-center space-x-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors",
                isActive
                  ? "bg-primary text-primary-foreground"
                  : "text-muted-foreground hover:bg-accent hover:text-accent-foreground"
              )}
            >
              <item.icon className="h-4 w-4" />
              <span>{item.title}</span>
            </Link>
          );
        })}
      </div>
    </nav>
  );
}
