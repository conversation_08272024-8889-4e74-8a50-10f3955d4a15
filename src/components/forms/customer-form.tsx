"use client";

import { z } from "zod";
import { User, Building, Mail, Phone, Calendar, MapPin, DollarSign, ToggleLeft, FileText, Shield } from "lucide-react";
import { createFormComponent } from "@/lib/core/base-form";
import { Customer } from "@/lib/db/schema";
import { useTenants } from "@/lib/hooks/queries/use-tenant-queries";
import { useLocations } from "@/lib/hooks/queries/use-location-queries";
import { usePricingGroups } from "@/lib/hooks/queries/use-pricing-group-queries";
import { useWaiverFormsByTenant } from "@/lib/hooks/queries/use-waiver-form-queries";
import { DynamicWaiverForms } from "./dynamic-waiver-forms";
import React from "react";

// Form validation schema
const customerSchema = z.object({
  tenantId: z.number().int().positive("Tenant is required"),
  locationId: z.string().optional(),
  email: z.string().email("Invalid email format"),
  mobileNumber: z.string().optional(),
  mobileCountryCode: z.string().max(10, "Country code must be less than 10 characters").optional(),
  firstName: z.string().min(1, "First name is required").max(255, "First name must be less than 255 characters"),
  lastName: z.string().max(255, "Last name must be less than 255 characters").optional(),
  dateOfBirth: z.string().optional(),
  gender: z.string().max(50, "Gender must be less than 50 characters").optional(),
  pricingGroupId: z.string().optional(),
  isActive: z.boolean(),
  notes: z.string().max(1000, "Notes must be less than 1000 characters").optional(),
  // Address fields
  addressLine1: z.string().max(255, "Address line 1 must be less than 255 characters").optional(),
  addressLine2: z.string().max(255, "Address line 2 must be less than 255 characters").optional(),
  city: z.string().max(255, "City must be less than 255 characters").optional(),
  state: z.string().max(255, "State must be less than 255 characters").optional(),
  zip: z.string().max(255, "ZIP code must be less than 255 characters").optional(),
  country: z.string().max(255, "Country must be less than 255 characters").optional(),
  // Waiver fields
  selectedWaiverForms: z.array(z.string()).optional(), // Selected waiver form IDs
  waiverResponses: z.record(z.any()).optional(), // Dynamic waiver form responses
});

type CustomerData = z.infer<typeof customerSchema>;

// Get options for dropdowns
function useFormOptions(tenantId?: number, entity?: Customer) {
  console.log('🔍 [CustomerForm] useFormOptions called with:', { tenantId, entityTenantId: entity?.tenantId });

  const { data: tenantsData, isLoading: tenantsLoading } = useTenants();

  // Use entity's tenantId if available, otherwise use passed tenantId
  const effectiveTenantId = entity?.tenantId || tenantId;

  const { data: locationsData, isLoading: locationsLoading } = useLocations({
    tenantId: effectiveTenantId
  });
  const { data: pricingGroupsData, isLoading: pricingGroupsLoading } = usePricingGroups({
    tenantId: effectiveTenantId
  });
  const { data: waiverFormsData, isLoading: waiverFormsLoading } = useWaiverFormsByTenant(effectiveTenantId || 0);

  console.log('🔍 [CustomerForm] Waiver forms data:', {
    effectiveTenantId,
    waiverFormsData,
    waiverFormsLoading,
    waiverFormsCount: waiverFormsData?.length || 0
  });

  const tenants = tenantsData?.tenants || [];
  const locations = locationsData || [];
  const pricingGroups = pricingGroupsData || [];
  const waiverForms = waiverFormsData || [];

  return {
    tenantOptions: tenants.map(tenant => ({
      value: tenant.id,
      label: tenant.name,
    })),
    locationOptions: locations.map(location => ({
      value: location.id,
      label: location.name || 'Unnamed Location',
    })),
    pricingGroupOptions: pricingGroups.map(group => ({
      value: group.id,
      label: group.name,
    })),
    waiverFormOptions: (() => {
      const activeWaiverForms = waiverForms.filter(form => form.isActive);
      const options = activeWaiverForms.map(form => ({
        value: form.id,
        label: form.name,
        description: form.description,
        isRequired: form.isRequired,
      }));

      console.log('🔍 [CustomerForm] Waiver form options:', {
        totalWaiverForms: waiverForms.length,
        activeWaiverForms: activeWaiverForms.length,
        options: options
      });

      return options;
    })(),
    isLoading: tenantsLoading || locationsLoading || pricingGroupsLoading || waiverFormsLoading,
  };
}

// Form configuration
const formConfig = {
  schema: customerSchema,
  defaultValues: {
    tenantId: 1,
    locationId: "",
    email: "",
    mobileNumber: "",
    mobileCountryCode: "",
    firstName: "",
    lastName: "",
    dateOfBirth: "",
    gender: "",
    pricingGroupId: "",
    isActive: true,
    notes: "",
    // Address defaults
    addressLine1: "",
    addressLine2: "",
    city: "",
    state: "",
    zip: "",
    country: "",
    // Waiver defaults
    selectedWaiverForms: [],
    waiverResponses: {},
  },
  fields: [
    {
      name: "tenantId" as const,
      label: "Tenant",
      type: "select" as const,
      placeholder: "Select tenant",
      required: true,
      icon: Building,
      options: [], // Will be populated dynamically
    },
    {
      name: "firstName" as const,
      label: "First Name",
      type: "text" as const,
      placeholder: "Enter customer's first name",
      required: true,
      icon: User,
    },
    {
      name: "lastName" as const,
      label: "Last Name",
      type: "text" as const,
      placeholder: "Enter customer's last name",
      icon: User,
    },
    {
      name: "email" as const,
      label: "Email Address",
      type: "email" as const,
      placeholder: "Enter customer's email address",
      required: true,
      icon: Mail,
    },
    {
      name: "mobileCountryCode" as const,
      label: "Country Code",
      type: "select" as const,
      placeholder: "Select country code",
      icon: Phone,
      description: "Country code for mobile number",
      options: [
        { value: "+1", label: "+1 (US/Canada)" },
        { value: "+44", label: "+44 (UK)" },
        { value: "+62", label: "+62 (Indonesia)" },
        { value: "+65", label: "+65 (Singapore)" },
        { value: "+60", label: "+60 (Malaysia)" },
        { value: "+66", label: "+66 (Thailand)" },
        { value: "+84", label: "+84 (Vietnam)" },
        { value: "+63", label: "+63 (Philippines)" },
        { value: "+91", label: "+91 (India)" },
        { value: "+86", label: "+86 (China)" },
        { value: "+81", label: "+81 (Japan)" },
        { value: "+82", label: "+82 (South Korea)" },
        { value: "+61", label: "+61 (Australia)" },
        { value: "+49", label: "+49 (Germany)" },
        { value: "+33", label: "+33 (France)" },
        { value: "+39", label: "+39 (Italy)" },
        { value: "+34", label: "+34 (Spain)" },
        { value: "+31", label: "+31 (Netherlands)" },
        { value: "+41", label: "+41 (Switzerland)" },
        { value: "+46", label: "+46 (Sweden)" },
        { value: "+47", label: "+47 (Norway)" },
        { value: "+45", label: "+45 (Denmark)" },
        { value: "+358", label: "+358 (Finland)" },
      ],
    },
    {
      name: "mobileNumber" as const,
      label: "Mobile Number",
      type: "text" as const,
      placeholder: "Enter mobile number",
      icon: Phone,
    },
    {
      name: "dateOfBirth" as const,
      label: "Date of Birth",
      type: "date" as const,
      placeholder: "Select date of birth",
      icon: Calendar,
    },
    {
      name: "gender" as const,
      label: "Gender",
      type: "select" as const,
      placeholder: "Select gender",
      icon: User,
      options: [
        { value: "male", label: "Male" },
        { value: "female", label: "Female" },
        { value: "other", label: "Other" },
        { value: "prefer_not_to_say", label: "Prefer not to say" },
      ],
    },
    {
      name: "locationId" as const,
      label: "Location",
      type: "select" as const,
      placeholder: "Select location",
      icon: MapPin,
      options: [], // Will be populated dynamically
      description: "Primary location for this customer",
    },
    {
      name: "pricingGroupId" as const,
      label: "Pricing Group",
      type: "select" as const,
      placeholder: "Select pricing group",
      icon: DollarSign,
      options: [], // Will be populated dynamically
      description: "Pricing group that applies to this customer",
    },
    {
      name: "notes" as const,
      label: "Notes",
      type: "textarea" as const,
      placeholder: "Enter any additional notes about the customer",
      icon: FileText,
      description: "Internal notes about the customer",
    },
    {
      name: "isActive" as const,
      label: "Active Status",
      type: "switch" as const,
      icon: ToggleLeft,
      description: "Whether this customer is active and can make bookings",
    },
    // Address fields
    {
      name: "addressLine1" as const,
      label: "Address Line 1",
      type: "text" as const,
      placeholder: "Enter street address",
      icon: MapPin,
    },
    {
      name: "addressLine2" as const,
      label: "Address Line 2",
      type: "text" as const,
      placeholder: "Apartment, suite, etc. (optional)",
      icon: MapPin,
    },
    {
      name: "city" as const,
      label: "City",
      type: "text" as const,
      placeholder: "Enter city",
      icon: MapPin,
    },
    {
      name: "state" as const,
      label: "State/Province",
      type: "text" as const,
      placeholder: "Enter state or province",
      icon: MapPin,
    },
    {
      name: "zip" as const,
      label: "ZIP/Postal Code",
      type: "text" as const,
      placeholder: "Enter ZIP or postal code",
      icon: MapPin,
    },
    {
      name: "country" as const,
      label: "Country",
      type: "select" as const,
      placeholder: "Select country",
      icon: MapPin,
      options: [
        { value: "US", label: "United States" },
        { value: "CA", label: "Canada" },
        { value: "GB", label: "United Kingdom" },
        { value: "AU", label: "Australia" },
        { value: "DE", label: "Germany" },
        { value: "FR", label: "France" },
        { value: "IT", label: "Italy" },
        { value: "ES", label: "Spain" },
        { value: "NL", label: "Netherlands" },
        { value: "BE", label: "Belgium" },
        { value: "CH", label: "Switzerland" },
        { value: "AT", label: "Austria" },
        { value: "SE", label: "Sweden" },
        { value: "NO", label: "Norway" },
        { value: "DK", label: "Denmark" },
        { value: "FI", label: "Finland" },
        { value: "JP", label: "Japan" },
        { value: "KR", label: "South Korea" },
        { value: "CN", label: "China" },
        { value: "IN", label: "India" },
        { value: "SG", label: "Singapore" },
        { value: "MY", label: "Malaysia" },
        { value: "TH", label: "Thailand" },
        { value: "ID", label: "Indonesia" },
        { value: "PH", label: "Philippines" },
        { value: "VN", label: "Vietnam" },
        { value: "BR", label: "Brazil" },
        { value: "MX", label: "Mexico" },
        { value: "AR", label: "Argentina" },
        { value: "CL", label: "Chile" },
      ],
    },
    {
      name: "selectedWaiverForms" as const,
      label: "Select Waiver Forms",
      type: "select" as const,
      placeholder: "Select waiver forms",
      icon: Shield,
      description: "Select waiver forms that this customer needs to sign",
      options: [], // Will be populated dynamically
    },
  ],
  sections: [
    {
      title: "Basic Information",
      description: "Customer's personal details",
      fields: ["tenantId", "firstName", "lastName", "email"] as (keyof CustomerData)[],
    },
    {
      title: "Contact Information",
      description: "Phone and additional contact details",
      fields: ["mobileCountryCode", "mobileNumber", "dateOfBirth", "gender"] as (keyof CustomerData)[],
    },
    {
      title: "Business Settings",
      description: "Location, pricing, and business-related settings",
      fields: ["locationId", "pricingGroupId", "notes"] as (keyof CustomerData)[],
    },
    {
      title: "Address Information",
      description: "Customer's primary address",
      fields: ["addressLine1", "addressLine2", "city", "state", "zip", "country"] as (keyof CustomerData)[],
    },
    {
      title: "Waiver Forms",
      description: "Required waiver forms for this customer",
      fields: ["selectedWaiverForms"] as (keyof CustomerData)[],
    },
    {
      title: "Status",
      description: "Customer status and availability",
      fields: ["isActive"] as (keyof CustomerData)[],
    },
  ],
};



// Enhanced component with dynamic options
interface CustomerFormProps {
  entity?: Customer;
  tenantId?: number;
  onSubmit: (data: CustomerData) => Promise<void>;
  onCancel: () => void;
  className?: string;
  title?: string;
  description?: string;
}

export function CustomerForm({ tenantId, entity, ...props }: CustomerFormProps) {
  console.log('🔍 CustomerForm received entity:', entity);
  console.log('🔍 CustomerForm received tenantId:', tenantId);

  const [waiverResponses, setWaiverResponses] = React.useState<Record<string, any>>({});

  const {
    tenantOptions,
    locationOptions,
    pricingGroupOptions,
    waiverFormOptions,
    isLoading
  } = useFormOptions(tenantId, entity);

  // Update form config with dynamic options and default tenantId
  const enhancedConfig = {
    ...formConfig,
    defaultValues: {
      ...formConfig.defaultValues,
      tenantId: tenantId || formConfig.defaultValues.tenantId,
    },
    fields: formConfig.fields.map(field => {
      switch (field.name) {
        case "tenantId":
          return { ...field, options: tenantOptions };
        case "locationId":
          return { ...field, options: locationOptions };
        case "pricingGroupId":
          return { ...field, options: pricingGroupOptions };
        case "selectedWaiverForms":
          return { ...field, options: waiverFormOptions };
        default:
          return field;
      }
    }),
  };

  // Create enhanced form component
  const EnhancedForm = createFormComponent<Customer, CustomerData>(
    "Customer",
    enhancedConfig
  );

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  // Debug entity data
  console.log('🔍 [CustomerForm] Entity received:', entity);
  if (entity) {
    console.log('🏠 [CustomerForm] Entity address fields:', {
      addressLine1: (entity as any).addressLine1,
      addressLine2: (entity as any).addressLine2,
      city: (entity as any).city,
      state: (entity as any).state,
      zip: (entity as any).zip,
      country: (entity as any).country,
    });
  }

  // Handle waiver response change
  const handleWaiverResponseChange = (waiverFormId: string, fieldId: string, value: any) => {
    setWaiverResponses(prev => ({
      ...prev,
      [waiverFormId]: {
        ...prev[waiverFormId],
        [fieldId]: value
      }
    }));
  };

  // Handle waiver completion
  const handleWaiverComplete = (waiverFormId: string, isComplete: boolean) => {
    console.log(`🔍 Waiver ${waiverFormId} completion status:`, isComplete);
  };

  // For demo purposes, use the first 2 waiver forms from the available options
  // Later we can integrate with form state management
  const demoSelectedWaiverForms = waiverFormOptions.slice(0, 2).map(option => option.value);

  console.log('🔍 [CustomerForm] waiverFormOptions:', waiverFormOptions);
  console.log('🔍 [CustomerForm] demoSelectedWaiverForms:', demoSelectedWaiverForms);

  return (
    <div className="space-y-6">
      <EnhancedForm
        {...props}
        entity={entity}
        icon={User}
      />

      {demoSelectedWaiverForms.length > 0 && (
        <div className="border-t pt-6">
          <h3 className="text-lg font-semibold mb-4">Complete Waiver Forms</h3>
          <DynamicWaiverForms
            selectedWaiverFormIds={demoSelectedWaiverForms}
            waiverResponses={waiverResponses}
            onWaiverResponseChange={handleWaiverResponseChange}
            onWaiverComplete={handleWaiverComplete}
          />
        </div>
      )}
    </div>
  );
}
