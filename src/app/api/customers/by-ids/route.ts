import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { z } from "zod";
import { individualCustomerSelectionService } from "@/lib/services/individual-customer-selection.service";

// Validation schema for getting customers by IDs
const customersByIdsSchema = z.object({
  tenantId: z.string().transform((val) => parseInt(val)),
  customerIds: z.array(z.string()).or(z.string().transform(val => [val])),
});

/**
 * GET /api/customers/by-ids
 * Get customers by their IDs with tenant isolation
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    
    // Handle multiple customerIds parameters
    const customerIds = searchParams.getAll('customerIds');
    const tenantId = searchParams.get('tenantId');
    
    const queryParams = {
      tenantId: tenantId || '',
      customerIds: customerIds,
    };
    
    // Validate query parameters
    const validatedParams = customersByIdsSchema.parse(queryParams);

    if (!validatedParams.tenantId || validatedParams.tenantId <= 0) {
      return NextResponse.json(
        { error: "Valid tenant ID is required" },
        { status: 400 }
      );
    }

    if (!validatedParams.customerIds || validatedParams.customerIds.length === 0) {
      return NextResponse.json({
        success: true,
        data: [],
        meta: {
          tenantId: validatedParams.tenantId,
          customerIds: [],
          total: 0,
        }
      });
    }

    console.log(`👤 [GET /api/customers/by-ids] Getting customers by IDs for tenant ${validatedParams.tenantId}:`, validatedParams.customerIds);

    const customers = await individualCustomerSelectionService.getCustomersByIds(
      validatedParams.customerIds, 
      validatedParams.tenantId
    );

    return NextResponse.json({
      success: true,
      data: customers,
      meta: {
        tenantId: validatedParams.tenantId,
        customerIds: validatedParams.customerIds,
        total: customers.length,
      }
    });
  } catch (error) {
    console.error("👤 [GET /api/customers/by-ids] Error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid query parameters", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to get customers by IDs" },
      { status: 500 }
    );
  }
}
