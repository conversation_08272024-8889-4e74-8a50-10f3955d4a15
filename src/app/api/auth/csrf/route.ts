import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { generateCSRFToken } from "@/lib/middleware/admin-security.middleware";

/**
 * CSRF Token Generation Endpoint
 * 
 * GET /api/auth/csrf
 * 
 * Generates and returns a CSRF token for authenticated users.
 * This token must be included in all non-GET requests to admin endpoints.
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    
    if (!session?.user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    // Generate CSRF token
    const csrfToken = generateCSRFToken();
    
    // In a real implementation, you would store this token in the session
    // For now, we'll return it directly
    
    console.log(`🔒 CSRF token generated for user ${session.user.email}`);
    
    return NextResponse.json({
      success: true,
      csrfToken,
      expiresIn: 3600, // 1 hour
      meta: {
        userId: session.user.id,
        timestamp: Date.now()
      }
    });
    
  } catch (error) {
    console.error("Error generating CSRF token:", error);
    
    return NextResponse.json(
      { 
        success: false,
        error: "Failed to generate CSRF token" 
      },
      { status: 500 }
    );
  }
}
