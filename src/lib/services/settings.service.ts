import { db } from "../db";
import { settings, tenants, type Setting, type NewSetting } from "../db/schema";
import { eq, and, desc } from "drizzle-orm";
import { createId } from "@paralleldrive/cuid2";

export class SettingsService {
  /**
   * Get settings by tenant ID
   */
  static async getByTenantId(tenantId: number): Promise<Setting | null> {
    const [setting] = await db
      .select()
      .from(settings)
      .where(eq(settings.tenantId, tenantId))
      .limit(1);

    return setting || null;
  }

  /**
   * Get settings by ID
   */
  static async getById(id: string): Promise<Setting | null> {
    const [setting] = await db
      .select()
      .from(settings)
      .where(eq(settings.id, id))
      .limit(1);

    return setting || null;
  }

  /**
   * Create settings
   */
  static async create(data: Omit<NewSetting, "id" | "createdAt">): Promise<Setting> {
    // Check if settings already exist for this tenant
    const existingSettings = await this.getByTenantId(data.tenantId!);
    if (existingSettings) {
      throw new Error("Settings already exist for this tenant");
    }

    const [setting] = await db
      .insert(settings)
      .values({
        ...data,
        id: createId(),
      })
      .returning();

    return setting;
  }

  /**
   * Update settings
   */
  static async update(
    tenantId: number,
    data: Partial<Omit<NewSetting, "id" | "tenantId" | "createdAt">>
  ): Promise<Setting | null> {
    const [setting] = await db
      .update(settings)
      .set(data)
      .where(eq(settings.tenantId, tenantId))
      .returning();

    return setting || null;
  }

  /**
   * Upsert settings (create or update)
   */
  static async upsert(data: Omit<NewSetting, "id" | "createdAt">): Promise<Setting> {
    const existingSettings = await this.getByTenantId(data.tenantId!);
    
    if (existingSettings) {
      const updated = await this.update(data.tenantId!, data);
      return updated!;
    } else {
      return await this.create(data);
    }
  }

  /**
   * Delete settings
   */
  static async delete(tenantId: number): Promise<boolean> {
    const result = await db
      .delete(settings)
      .where(eq(settings.tenantId, tenantId));

    return result.rowCount > 0;
  }

  /**
   * Get settings with tenant info
   */
  static async getWithTenant(tenantId: number) {
    const result = await db
      .select({
        settings: settings,
        tenant: tenants,
      })
      .from(settings)
      .leftJoin(tenants, eq(settings.tenantId, tenants.id))
      .where(eq(settings.tenantId, tenantId))
      .limit(1);

    return result[0] || null;
  }

  /**
   * Update currency
   */
  static async updateCurrency(tenantId: number, currency: string): Promise<Setting | null> {
    return await this.update(tenantId, { currency });
  }

  /**
   * Update timezone
   */
  static async updateTimeZone(tenantId: number, timeZone: string): Promise<Setting | null> {
    return await this.update(tenantId, { timeZone });
  }

  /**
   * Mark settings as accepted
   */
  static async markAsAccepted(tenantId: number): Promise<Setting | null> {
    return await this.update(tenantId, { acceptedAt: new Date() });
  }

  /**
   * Get all settings (admin only)
   */
  static async getAll(): Promise<Setting[]> {
    return await db
      .select()
      .from(settings)
      .orderBy(desc(settings.createdAt));
  }

  /**
   * Get settings by currency
   */
  static async getByCurrency(currency: string): Promise<Setting[]> {
    return await db
      .select()
      .from(settings)
      .where(eq(settings.currency, currency))
      .orderBy(desc(settings.createdAt));
  }

  /**
   * Get settings by timezone
   */
  static async getByTimeZone(timeZone: string): Promise<Setting[]> {
    return await db
      .select()
      .from(settings)
      .where(eq(settings.timeZone, timeZone))
      .orderBy(desc(settings.createdAt));
  }

  /**
   * Get default settings for new tenant
   */
  static getDefaultSettings(tenantId: number): Omit<NewSetting, "id" | "createdAt"> {
    return {
      tenantId,
      currency: "USD",
      timeZone: "UTC",
      acceptedAt: null,
    };
  }

  /**
   * Validate currency code
   */
  static validateCurrency(currency: string): boolean {
    const validCurrencies = [
      "USD", "EUR", "GBP", "JPY", "AUD", "CAD", "CHF", "CNY", "SEK", "NZD",
      "MXN", "SGD", "HKD", "NOK", "TRY", "RUB", "INR", "BRL", "ZAR", "KRW",
      "IDR", "MYR", "THB", "PHP", "VND", "EGP", "AED", "SAR", "QAR", "KWD"
    ];
    
    return validCurrencies.includes(currency.toUpperCase());
  }

  /**
   * Validate timezone
   */
  static validateTimeZone(timeZone: string): boolean {
    try {
      // Use Intl.DateTimeFormat to validate timezone
      Intl.DateTimeFormat(undefined, { timeZone });
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Get supported currencies
   */
  static getSupportedCurrencies() {
    return [
      { code: "USD", name: "US Dollar", symbol: "$" },
      { code: "EUR", name: "Euro", symbol: "€" },
      { code: "GBP", name: "British Pound", symbol: "£" },
      { code: "JPY", name: "Japanese Yen", symbol: "¥" },
      { code: "AUD", name: "Australian Dollar", symbol: "A$" },
      { code: "CAD", name: "Canadian Dollar", symbol: "C$" },
      { code: "CHF", name: "Swiss Franc", symbol: "CHF" },
      { code: "CNY", name: "Chinese Yuan", symbol: "¥" },
      { code: "INR", name: "Indian Rupee", symbol: "₹" },
      { code: "BRL", name: "Brazilian Real", symbol: "R$" },
      // Add more as needed
    ];
  }

  /**
   * Get common timezones
   */
  static getCommonTimeZones() {
    return [
      { value: "UTC", label: "UTC (Coordinated Universal Time)" },
      { value: "America/New_York", label: "Eastern Time (US & Canada)" },
      { value: "America/Chicago", label: "Central Time (US & Canada)" },
      { value: "America/Denver", label: "Mountain Time (US & Canada)" },
      { value: "America/Los_Angeles", label: "Pacific Time (US & Canada)" },
      { value: "Europe/London", label: "London" },
      { value: "Europe/Paris", label: "Paris" },
      { value: "Europe/Berlin", label: "Berlin" },
      { value: "Asia/Tokyo", label: "Tokyo" },
      { value: "Asia/Shanghai", label: "Shanghai" },
      { value: "Asia/Kolkata", label: "Mumbai, Kolkata" },
      { value: "Australia/Sydney", label: "Sydney" },
      // Add more as needed
    ];
  }

  /**
   * Get settings statistics
   */
  static async getStats() {
    const [totalSettings] = await db
      .select({ count: settings.id })
      .from(settings);

    const [acceptedSettings] = await db
      .select({ count: settings.id })
      .from(settings)
      .where(eq(settings.acceptedAt, null)); // This should be NOT NULL

    return {
      totalSettings: totalSettings?.count || 0,
      acceptedSettings: acceptedSettings?.count || 0,
      pendingSettings: (totalSettings?.count || 0) - (acceptedSettings?.count || 0),
    };
  }
}
