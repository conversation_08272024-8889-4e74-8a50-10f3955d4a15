#!/usr/bin/env tsx

/**
 * Script untuk membuat API key untuk testing public API
 * Usage: npx tsx scripts/create-api-key.ts
 */

import { db } from '../src/lib/db/db';
import { apiKeys } from '../src/lib/db/schema';
import { APIKeyService } from '../src/lib/public-api/auth/api-key-service';

async function createTestAPIKey() {
  try {
    console.log('🔑 Creating test API key...');
    
    // Buat API key untuk tenant ID 1 (default tenant)
    const apiKeyData = {
      name: 'Test API Key',
      tenantId: 1,
      permissions: [
        {
          resource: 'package-pricing',
          actions: ['read', 'write']
        }
      ],
      rateLimit: {
        requests: 100,
        window: 3600 // 1 hour
      },
      expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000) // 1 year
    };

    const apiKeyService = new APIKeyService();
    const result = await apiKeyService.generateAPIKey(apiKeyData);
    
    console.log('✅ API Key created successfully!');
    console.log('📋 Details:');
    console.log(`   ID: ${result.apiKey.id}`);
    console.log(`   Name: ${result.apiKey.name}`);
    console.log(`   Key: ${result.plainKey}`);
    console.log(`   Tenant ID: ${result.apiKey.tenantId}`);
    console.log(`   Permissions: ${result.apiKey.permissions.map(p => `${p.resource}:${p.actions.join(',')}`).join(', ')}`);
    console.log(`   Rate Limit: ${result.apiKey.rateLimit.requests} requests per ${result.apiKey.rateLimit.window} seconds`);
    console.log(`   Expires: ${result.apiKey.expiresAt?.toISOString() || 'Never'}`);
    console.log('');
    console.log('🧪 Test the API with:');
    console.log(`curl -X GET http://localhost:3000/api/public/v1/package-pricing \\`);
    console.log(`  -H "X-API-Key: ${result.plainKey}" \\`);
    console.log(`  -H "Content-Type: application/json"`);
    
  } catch (error) {
    console.error('❌ Error creating API key:', error);
    process.exit(1);
  }
}

// Run the script
createTestAPIKey().then(() => {
  console.log('🎉 Script completed!');
  process.exit(0);
});
