CREATE TABLE "legacy_api_keys" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"user_id" varchar(255) NOT NULL,
	"organization_id" varchar(255),
	"name" varchar(255) NOT NULL,
	"key" varchar(255) NOT NULL,
	"last_used" timestamp,
	"is_active" boolean DEFAULT true NOT NULL,
	"created_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updated_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT "legacy_api_keys_key_unique" UNIQUE("key")
);
--> statement-breakpoint
ALTER TABLE "usage_logs" DROP CONSTRAINT "usage_logs_api_key_id_api_keys_id_fk";
--> statement-breakpoint
ALTER TABLE "legacy_api_keys" ADD CONSTRAINT "legacy_api_keys_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "legacy_api_keys" ADD CONSTRAINT "legacy_api_keys_organization_id_organizations_id_fk" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "usage_logs" ADD CONSTRAINT "usage_logs_api_key_id_legacy_api_keys_id_fk" FOREIGN KEY ("api_key_id") REFERENCES "public"."legacy_api_keys"("id") ON DELETE cascade ON UPDATE no action;