import { useMutation, useQueryClient } from '@tanstack/react-query';
import { WaiverForm, NewWaiverForm } from '@/lib/db/schema';
import { createEntityHooks, BaseApiInterface } from '@/lib/core/base-query-hooks';
import { QueryOptions } from '@/lib/core/base-service';

// API interface implementation
const waiverFormApi: BaseApiInterface<WaiverForm, Omit<NewWaiverForm, "id" | "createdAt" | "updatedAt">, Partial<NewWaiverForm>> = {
  getAll: async (options?: QueryOptions): Promise<WaiverForm[]> => {
    const params = new URLSearchParams();
    if (options?.tenantId) params.append('tenantId', options.tenantId.toString());
    if (options?.limit) params.append('limit', options.limit.toString());
    if (options?.offset) params.append('offset', options.offset.toString());
    if (options?.filters?.search) params.append('search', options.filters.search);
    if (options?.filters?.isActive !== undefined) params.append('activeOnly', options.filters.isActive.toString());
    if (options?.filters?.isRequired !== undefined) params.append('requiredOnly', options.filters.isRequired.toString());

    const response = await fetch(`/api/waiver-forms?${params}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch waiver forms: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data || [];
  },

  getById: async (id: string): Promise<WaiverForm> => {
    const response = await fetch(`/api/waiver-forms/${id}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch waiver form: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data;
  },

  getByTenant: async (tenantId: number, options?: QueryOptions): Promise<WaiverForm[]> => {
    return waiverFormApi.getAll({ ...options, tenantId });
  },

  create: async (data: Omit<NewWaiverForm, "id" | "createdAt" | "updatedAt">): Promise<WaiverForm> => {
    const response = await fetch('/api/waiver-forms', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to create waiver form');
    }
    const result = await response.json();
    return result.data;
  },

  update: async ({ id, data }: { id: string; data: Partial<NewWaiverForm> }): Promise<WaiverForm> => {
    const response = await fetch(`/api/waiver-forms/${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to update waiver form');
    }
    const result = await response.json();
    return result.data;
  },

  delete: async (id: string): Promise<WaiverForm> => {
    const response = await fetch(`/api/waiver-forms/${id}`, {
      method: 'DELETE',
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to delete waiver form');
    }
    const result = await response.json();
    return result.data;
  },

  bulkOperation: async (data: { ids: string[]; action: string }): Promise<WaiverForm[]> => {
    const response = await fetch('/api/waiver-forms', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to perform bulk operation');
    }
    const result = await response.json();
    return result.data;
  },

  search: async (query: string, options?: QueryOptions): Promise<WaiverForm[]> => {
    return waiverFormApi.getAll({ ...options, filters: { search: query } });
  },

  getStats: async (tenantId?: number) => {
    const params = tenantId ? `?tenantId=${tenantId}` : '';
    const response = await fetch(`/api/waiver-forms/stats${params}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch waiver form stats: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data;
  },
};

// Create base hooks using the factory
const baseHooks = createEntityHooks('waiver-forms', waiverFormApi);

// Export base hooks with waiver-specific names
export const useWaiverForms = baseHooks.useEntities;
export const useWaiverForm = baseHooks.useEntity;
export const useWaiverFormsByTenant = baseHooks.useEntitiesByTenant;
export const useWaiverFormStats = baseHooks.useEntityStats;
export const useWaiverFormSearch = baseHooks.useEntitySearch;
export const useCreateWaiverForm = baseHooks.useCreateEntity;
export const useUpdateWaiverForm = baseHooks.useUpdateEntity;
export const useDeleteWaiverForm = baseHooks.useDeleteEntity;
export const useBulkWaiverFormOperation = baseHooks.useBulkOperation;

// Additional waiver-specific hooks
export function useToggleWaiverFormActive() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string): Promise<WaiverForm> => {
      const response = await fetch(`/api/waiver-forms/${id}?action=toggle-active`, {
        method: 'PATCH',
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to toggle waiver form status');
      }
      const result = await response.json();
      return result.data;
    },
    onSuccess: (updatedWaiverForm) => {
      // Update cache with updated waiver form
      queryClient.setQueryData(baseHooks.queryKeys.detail(updatedWaiverForm.id), updatedWaiverForm);

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: baseHooks.queryKeys.lists() });
      if (updatedWaiverForm.tenantId) {
        queryClient.invalidateQueries({
          queryKey: baseHooks.queryKeys.byTenant(updatedWaiverForm.tenantId)
        });
        queryClient.invalidateQueries({
          queryKey: baseHooks.queryKeys.stats(updatedWaiverForm.tenantId)
        });
      }
      queryClient.invalidateQueries({ queryKey: baseHooks.queryKeys.stats() });
    },
  });
}

export function useToggleWaiverFormRequired() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string): Promise<WaiverForm> => {
      const response = await fetch(`/api/waiver-forms/${id}?action=toggle-required`, {
        method: 'PATCH',
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to toggle waiver form required status');
      }
      const result = await response.json();
      return result.data;
    },
    onSuccess: (updatedWaiverForm) => {
      // Update cache with updated waiver form
      queryClient.setQueryData(baseHooks.queryKeys.detail(updatedWaiverForm.id), updatedWaiverForm);

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: baseHooks.queryKeys.lists() });
      if (updatedWaiverForm.tenantId) {
        queryClient.invalidateQueries({
          queryKey: baseHooks.queryKeys.byTenant(updatedWaiverForm.tenantId)
        });
        queryClient.invalidateQueries({
          queryKey: baseHooks.queryKeys.stats(updatedWaiverForm.tenantId)
        });
      }
      queryClient.invalidateQueries({ queryKey: baseHooks.queryKeys.stats() });
    },
  });
}

export function useReorderWaiverForms() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ tenantId, items }: { tenantId: number; items: { id: string; sortOrder: number }[] }): Promise<WaiverForm[]> => {
      const response = await fetch('/api/waiver-forms/reorder', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ tenantId, items }),
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to reorder waiver forms');
      }
      const result = await response.json();
      return result.data;
    },
    onSuccess: (updatedWaiverForms, { tenantId }) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: baseHooks.queryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: baseHooks.queryKeys.byTenant(tenantId) });
    },
  });
}

export function useDuplicateWaiverForm() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, name }: { id: string; name?: string }): Promise<WaiverForm> => {
      const response = await fetch(`/api/waiver-forms/${id}/duplicate`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name }),
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to duplicate waiver form');
      }
      const result = await response.json();
      return result.data;
    },
    onSuccess: (newWaiverForm) => {
      // Update cache with new waiver form
      queryClient.setQueryData(baseHooks.queryKeys.detail(newWaiverForm.id), newWaiverForm);

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: baseHooks.queryKeys.lists() });
      if (newWaiverForm.tenantId) {
        queryClient.invalidateQueries({
          queryKey: baseHooks.queryKeys.byTenant(newWaiverForm.tenantId)
        });
        queryClient.invalidateQueries({
          queryKey: baseHooks.queryKeys.stats(newWaiverForm.tenantId)
        });
      }
      queryClient.invalidateQueries({ queryKey: baseHooks.queryKeys.stats() });
    },
  });
}

// Export query keys for external use
export const waiverFormQueryKeys = baseHooks.queryKeys;
