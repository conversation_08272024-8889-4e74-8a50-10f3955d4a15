import { NextRequest, NextResponse } from "next/server";
import { RoleService } from "@/lib/services/role.service";
import { db } from "@/lib/db";
import { roles } from "@/lib/db/schema";
import { eq } from "drizzle-orm";

// Test endpoint to directly query database and check role creation
export async function POST(request: NextRequest) {
  try {
    console.log("🔍 Testing direct database query for role creation...");
    
    // Step 1: Create a role
    console.log("\n1️⃣ Creating role...");
    const newRoleData = {
      name: `direct_test_${Date.now()}`,
      display_name: "Direct Test Role",
      description: "Role created for direct database test",
      is_system_role: false,
      hierarchy_level: 70,
      tenantId: 1,
      permissions: []
    };

    const createdRole = await RoleService.create(newRoleData);
    console.log("✅ Role created:", {
      id: createdRole.id,
      name: createdRole.name,
      display_name: createdRole.display_name,
      is_active: createdRole.is_active
    });

    // Step 2: Direct database query to check if role exists
    console.log("\n2️⃣ Direct database query...");
    const directQuery = await db
      .select()
      .from(roles)
      .where(eq(roles.id, createdRole.id))
      .limit(1);
    
    console.log("Direct query result:", directQuery);

    // Step 3: Query all roles without filters
    console.log("\n3️⃣ Query all roles (no filters)...");
    const allRoles = await db
      .select()
      .from(roles)
      .orderBy(roles.createdAt);
    
    console.log(`Total roles in database: ${allRoles.length}`);
    
    // Check if our created role is in the list
    const foundInAll = allRoles.find(r => r.id === createdRole.id);
    console.log("Created role found in all roles:", !!foundInAll);

    // Step 4: Query with is_active = true filter
    console.log("\n4️⃣ Query with is_active = true filter...");
    const activeRoles = await db
      .select()
      .from(roles)
      .where(eq(roles.is_active, true))
      .orderBy(roles.createdAt);
    
    console.log(`Active roles in database: ${activeRoles.length}`);
    
    // Check if our created role is in the active list
    const foundInActive = activeRoles.find(r => r.id === createdRole.id);
    console.log("Created role found in active roles:", !!foundInActive);

    // Step 5: Use RoleService.searchRoles
    console.log("\n5️⃣ Using RoleService.searchRoles...");
    const searchResult = await RoleService.searchRoles(undefined, undefined, undefined, 100, 0);
    console.log(`SearchRoles returned: ${searchResult.roles.length} roles`);
    
    const foundInSearch = searchResult.roles.find(r => r.id === createdRole.id);
    console.log("Created role found in search results:", !!foundInSearch);

    return NextResponse.json({
      success: true,
      message: "Direct database query test completed",
      data: {
        createdRole: {
          id: createdRole.id,
          name: createdRole.name,
          display_name: createdRole.display_name,
          is_active: createdRole.is_active
        },
        directQueryFound: directQuery.length > 0,
        foundInAllRoles: !!foundInAll,
        foundInActiveRoles: !!foundInActive,
        foundInSearchResults: !!foundInSearch,
        counts: {
          totalRoles: allRoles.length,
          activeRoles: activeRoles.length,
          searchResults: searchResult.roles.length
        },
        analysis: {
          databaseInsertWorking: directQuery.length > 0,
          activeFilterWorking: !!foundInActive,
          searchMethodWorking: !!foundInSearch,
          possibleIssue: directQuery.length > 0 && !foundInSearch ? "SearchRoles method has issue" : "Unknown"
        }
      }
    });
  } catch (error) {
    console.error("❌ Direct database query test failed:", error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}
