/**
 * Health Check Endpoint for Public API
 * 
 * FAANG-level health monitoring with:
 * - Database connectivity checks
 * - Redis connectivity checks
 * - External service checks
 * - Performance metrics
 * - Detailed error reporting
 */

import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { RateLimiterService } from '@/lib/public-api/auth/rate-limiter-service';
import { HealthCheck, HealthCheckResult } from '@/lib/public-api/types';

const rateLimiter = new RateLimiterService();

/**
 * GET /api/public/v1/health
 * Health check endpoint - no authentication required
 */
export async function GET(request: NextRequest) {
  const startTime = Date.now();
  
  try {
    // Perform health checks in parallel
    const [databaseCheck, redisCheck, externalCheck] = await Promise.allSettled([
      checkDatabase(),
      checkRedis(),
      checkExternalServices(),
    ]);

    // Determine overall status
    const checks = {
      database: getCheckResult(databaseCheck),
      redis: getCheckResult(redisCheck),
      external: getCheckResult(externalCheck),
    };

    const overallStatus = determineOverallStatus(checks);
    const processingTime = Date.now() - startTime;

    const healthCheck: HealthCheck = {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      version: process.env.API_VERSION || '1.0.0',
      uptime: process.uptime(),
      checks,
    };

    const statusCode = overallStatus === 'healthy' ? 200 : 503;

    return NextResponse.json(healthCheck, { 
      status: statusCode,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'X-Response-Time': processingTime.toString(),
      },
    });

  } catch (error) {
    console.error('Health check error:', error);
    
    const healthCheck: HealthCheck = {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      version: process.env.API_VERSION || '1.0.0',
      uptime: process.uptime(),
      checks: {
        database: { status: 'fail', error: 'Health check failed' },
        redis: { status: 'fail', error: 'Health check failed' },
        external: { status: 'fail', error: 'Health check failed' },
      },
    };

    return NextResponse.json(healthCheck, { 
      status: 503,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate',
      },
    });
  }
}

/**
 * Check database connectivity and performance
 */
async function checkDatabase(): Promise<HealthCheckResult> {
  const startTime = Date.now();
  
  try {
    // Simple query to test database connectivity
    await db.execute('SELECT 1 as health_check');
    
    const responseTime = Date.now() - startTime;
    
    // Consider slow if > 1000ms
    const status = responseTime > 1000 ? 'warn' : 'pass';
    
    return {
      status,
      responseTime,
    };
  } catch (error) {
    return {
      status: 'fail',
      responseTime: Date.now() - startTime,
      error: error instanceof Error ? error.message : 'Database connection failed',
    };
  }
}

/**
 * Check Redis connectivity and performance
 */
async function checkRedis(): Promise<HealthCheckResult> {
  try {
    const result = await rateLimiter.healthCheck();
    return result;
  } catch (error) {
    return {
      status: 'fail',
      error: error instanceof Error ? error.message : 'Redis connection failed',
    };
  }
}

/**
 * Check external services (placeholder for actual external dependencies)
 */
async function checkExternalServices(): Promise<HealthCheckResult> {
  const startTime = Date.now();
  
  try {
    // In a real implementation, you would check external services like:
    // - Payment processors (Stripe, PayPal)
    // - Email services (SendGrid, AWS SES)
    // - File storage (AWS S3, Cloudflare R2)
    // - Third-party APIs
    
    // For now, simulate a quick check
    await new Promise(resolve => setTimeout(resolve, 10));
    
    const responseTime = Date.now() - startTime;
    
    return {
      status: 'pass',
      responseTime,
    };
  } catch (error) {
    return {
      status: 'fail',
      responseTime: Date.now() - startTime,
      error: error instanceof Error ? error.message : 'External service check failed',
    };
  }
}

/**
 * Extract health check result from Promise.allSettled result
 */
function getCheckResult(settledResult: PromiseSettledResult<HealthCheckResult>): HealthCheckResult {
  if (settledResult.status === 'fulfilled') {
    return settledResult.value;
  } else {
    return {
      status: 'fail',
      error: settledResult.reason?.message || 'Check failed',
    };
  }
}

/**
 * Determine overall system health status
 */
function determineOverallStatus(checks: {
  database: HealthCheckResult;
  redis: HealthCheckResult;
  external: HealthCheckResult;
}): 'healthy' | 'degraded' | 'unhealthy' {
  const statuses = Object.values(checks).map(check => check.status);
  
  // If any critical service fails, system is unhealthy
  if (checks.database.status === 'fail') {
    return 'unhealthy';
  }
  
  // If any service fails, system is unhealthy
  if (statuses.includes('fail')) {
    return 'unhealthy';
  }
  
  // If any service has warnings, system is degraded
  if (statuses.includes('warn')) {
    return 'degraded';
  }
  
  // All services are healthy
  return 'healthy';
}

/**
 * OPTIONS /api/public/v1/health
 * Handle CORS preflight requests
 */
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  });
}
