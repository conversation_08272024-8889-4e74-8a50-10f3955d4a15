import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Class } from '@/lib/db/schema';

/**
 * Class Queries Hooks
 * 
 * Hooks untuk manage state dan data fetching classes menggunakan TanStack Query.
 * Pattern ini mengikuti use-class-category-queries.ts yang sudah established dan proven.
 * 
 * Ini kayak "jembatan" antara UI components dan service layer.
 * Semua caching, error handling, dan optimistic updates dihandle di sini.
 */

// Query Keys - ini penting untuk caching strategy
export const classKeys = {
  all: ['classes'] as const,
  lists: () => [...classKeys.all, 'list'] as const,
  list: (filters: Record<string, any>) => [...classKeys.lists(), { filters }] as const,
  details: () => [...classKeys.all, 'detail'] as const,
  detail: (id: string) => [...classKeys.details(), id] as const,
  byTenant: (tenantId: number) => [...classKeys.all, 'tenant', tenantId] as const,
  byCategory: (categoryId: string) => [...classKeys.all, 'category', categoryId] as const,
  bySubcategory: (subcategoryId: string) => [...classKeys.all, 'subcategory', subcategoryId] as const,
  search: (params: any) => [...classKeys.all, 'search', params] as const,
};

// API Functions - ini yang handle komunikasi dengan backend
const classApi = {
  getAll: async (): Promise<Class[]> => {
    const response = await fetch('/api/classes');
    if (!response.ok) {
      throw new Error(`Failed to fetch classes: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data?.classes || [];
  },

  getByTenant: async (tenantId: number): Promise<Class[]> => {
    const params = new URLSearchParams({
      tenantId: tenantId.toString(),
    });
    const response = await fetch(`/api/classes?${params}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch classes: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data?.classes || [];
  },

  getByCategory: async (categoryId: string): Promise<Class[]> => {
    const params = new URLSearchParams({
      categoryId: categoryId,
    });
    const response = await fetch(`/api/classes?${params}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch classes: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data?.classes || [];
  },

  getBySubcategory: async (subcategoryId: string): Promise<Class[]> => {
    const params = new URLSearchParams({
      subcategoryId: subcategoryId,
    });
    const response = await fetch(`/api/classes?${params}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch classes: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data?.classes || [];
  },

  search: async (
    tenantId: number, 
    categoryId?: string, 
    subcategoryId?: string, 
    searchTerm?: string, 
    limit = 20, 
    offset = 0
  ) => {
    const params = new URLSearchParams({
      tenantId: tenantId.toString(),
      limit: limit.toString(),
      offset: offset.toString(),
    });
    if (categoryId) {
      params.append('categoryId', categoryId);
    }
    if (subcategoryId) {
      params.append('subcategoryId', subcategoryId);
    }
    if (searchTerm) {
      params.append('search', searchTerm);
    }
    const response = await fetch(`/api/classes?${params}`);
    if (!response.ok) {
      throw new Error(`Failed to search classes: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data;
  },

  getById: async (id: string): Promise<Class> => {
    const response = await fetch(`/api/classes/${id}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch class: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data;
  },

  create: async (data: {
    tenantId: number;
    name: string;
    description?: string;
    categoryId: string;
    subcategoryId?: string;
    duration_value?: number;
    duration_unit?: string;
    level_id?: string;
    delivery_mode?: string;
    is_private?: boolean;
    custom_cancellation_policy?: boolean;
    cancellation_policy_description?: string;
    is_active?: boolean;
    location_id?: string;
    images?: string[];
    package_pricing_ids?: string[];
  }): Promise<Class> => {
    const response = await fetch('/api/classes', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      throw new Error(`Failed to create class: ${response.statusText}`);
    }
    const result = await response.json();
    return result.data;
  },

  update: async (id: string, tenantId: number, data: {
    name?: string;
    description?: string;
    categoryId?: string;
    subcategoryId?: string;
    duration_value?: number;
    duration_unit?: string;
    level_id?: string;
    delivery_mode?: string;
    is_private?: boolean;
    custom_cancellation_policy?: boolean;
    cancellation_policy_description?: string;
    is_active?: boolean;
    location_id?: string;
    images?: string[];
    package_pricing_ids?: string[];
  }): Promise<Class> => {
    const response = await fetch(`/api/classes/${id}?tenantId=${tenantId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      throw new Error(`Failed to update class: ${response.statusText}`);
    }
    const result = await response.json();
    return result.data;
  },

  delete: async (id: string, tenantId: number): Promise<void> => {
    const response = await fetch(`/api/classes/${id}?tenantId=${tenantId}`, {
      method: 'DELETE',
    });
    if (!response.ok) {
      throw new Error(`Failed to delete class: ${response.statusText}`);
    }
  },
};

// Query Hooks - ini yang dipake di components
export function useClasses() {
  const query = useQuery({
    queryKey: classKeys.lists(),
    queryFn: classApi.getAll,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: false,
  });

  const classes = Array.isArray(query.data) ? query.data : [];

  return {
    data: classes,
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error?.message || null,
    refetch: query.refetch,
  };
}

export function useClassesByTenant(tenantId: number) {
  return useQuery({
    queryKey: classKeys.byTenant(tenantId),
    queryFn: () => classApi.getByTenant(tenantId),
    enabled: !!tenantId,
    staleTime: 5 * 60 * 1000,
  });
}

export function useClassesByCategory(categoryId: string) {
  return useQuery({
    queryKey: classKeys.byCategory(categoryId),
    queryFn: () => classApi.getByCategory(categoryId),
    enabled: !!categoryId,
    staleTime: 5 * 60 * 1000,
  });
}

export function useClassesBySubcategory(subcategoryId: string) {
  return useQuery({
    queryKey: classKeys.bySubcategory(subcategoryId),
    queryFn: () => classApi.getBySubcategory(subcategoryId),
    enabled: !!subcategoryId,
    staleTime: 5 * 60 * 1000,
  });
}

export function useClass(id: string) {
  return useQuery({
    queryKey: classKeys.detail(id),
    queryFn: () => classApi.getById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
}

export function useClassSearch(
  tenantId: number, 
  categoryId?: string, 
  subcategoryId?: string, 
  searchTerm?: string, 
  limit = 20, 
  offset = 0
) {
  return useQuery({
    queryKey: classKeys.search({ tenantId, categoryId, subcategoryId, searchTerm, limit, offset }),
    queryFn: () => classApi.search(tenantId, categoryId, subcategoryId, searchTerm, limit, offset),
    enabled: !!tenantId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

// Mutation Hooks - ini untuk create, update, delete
export function useCreateClass() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: classApi.create,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: classKeys.all });
    },
  });
}

export function useUpdateClass() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, tenantId, data }: {
      id: string;
      tenantId: number;
      data: {
        name?: string;
        description?: string;
        categoryId?: string;
        subcategoryId?: string;
        duration_value?: number;
        duration_unit?: string;
        level_id?: string;
        delivery_mode?: string;
        is_private?: boolean;
        custom_cancellation_policy?: boolean;
        cancellation_policy_description?: string;
        is_active?: boolean;
        location_id?: string;
      }
    }) => {
      return classApi.update(id, tenantId, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: classKeys.all });
    },
  });
}

export function useDeleteClass() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, tenantId }: { id: string; tenantId: number }) => {
      return classApi.delete(id, tenantId);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: classKeys.all });
    },
  });
}

// Export types
export interface ClassSearchParams {
  tenantId: number;
  categoryId?: string;
  subcategoryId?: string;
  search?: string;
  limit?: number;
  offset?: number;
}

export interface ClassFormData {
  tenantId: number;
  name: string;
  description?: string;
  categoryId: string;
  subcategoryId?: string;
  duration_value?: number;
  duration_unit?: string;
  level_id?: string;
  delivery_mode?: string;
  is_private?: boolean;
  custom_cancellation_policy?: boolean;
  cancellation_policy_description?: string;
  is_active?: boolean;
  location_id?: string;
}
