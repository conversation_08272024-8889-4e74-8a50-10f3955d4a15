import { ClassRepository } from '../../domain/ClassRepository';
import { Class } from '../../domain/Class';
import { db } from '@/lib/db'; // adjust import to your db client

export class DbClassRepository implements ClassRepository {
  async findById(id: string): Promise<Class | null> {
    const row = await db.class_schedules.findUnique({ where: { id } });
    if (!row) return null;
    return this.mapRowToClass(row);
  }

  async findAll(): Promise<Class[]> {
    const rows = await db.class_schedules.findMany();
    return rows.map(this.mapRowToClass);
  }

  async create(cls: Class): Promise<Class> {
    const row = await db.class_schedules.create({
      data: {
        id: cls.id,
        name: cls.name,
        description: cls.description,
        category: cls.category,
        startDate: cls.startDate,
        endDate: cls.endDate,
        isActive: cls.isActive,
        createdAt: cls.createdAt,
        updatedAt: cls.updatedAt,
      },
    });
    return this.mapRowToClass(row);
  }

  async update(cls: Class): Promise<Class> {
    const row = await db.class_schedules.update({
      where: { id: cls.id },
      data: {
        name: cls.name,
        description: cls.description,
        category: cls.category,
        startDate: cls.startDate,
        endDate: cls.endDate,
        isActive: cls.isActive,
        updatedAt: cls.updatedAt,
      },
    });
    return this.mapRowToClass(row);
  }

  async delete(id: string): Promise<void> {
    await db.class_schedules.delete({ where: { id } });
  }

  private mapRowToClass(row: any): Class {
    return new Class(
      row.id,
      row.name,
      row.description,
      row.category,
      new Date(row.startDate),
      new Date(row.endDate),
      row.isActive,
      new Date(row.createdAt),
      new Date(row.updatedAt)
    );
  }
}
