import { NextRequest, NextResponse } from "next/server";

/**
 * Test endpoint untuk verify public class schedules API
 * 
 * Test endpoint ini akan:
 * 1. Test public class schedules list endpoint
 * 2. Test public class schedule individual endpoint
 * 3. Verify filtering logic untuk private schedules
 * 4. Test error handling dan validation
 */

export async function GET(request: NextRequest) {
  try {
    console.log("🧪 Testing Public Class Schedules API...");
    
    const testResults = {
      listEndpoint: { success: false, error: null, data: null },
      individualEndpoint: { success: false, error: null, data: null },
      privateFiltering: { success: false, error: null, data: null },
      errorHandling: { success: false, error: null, data: null }
    };

    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
    const testTenantId = 1;

    // Test 1: List endpoint
    console.log("\n1️⃣ Testing public class schedules list endpoint...");
    try {
      const listResponse = await fetch(`${baseUrl}/api/public/class-schedules?tenantId=${testTenantId}&limit=5`);
      const listData = await listResponse.json();
      
      if (listResponse.ok && listData.success) {
        testResults.listEndpoint.success = true;
        testResults.listEndpoint.data = {
          status: listResponse.status,
          scheduleCount: listData.data?.schedules?.length || 0,
          hasMore: listData.data?.hasMore,
          total: listData.data?.total
        };
        console.log(`✅ List endpoint working - Found ${listData.data?.schedules?.length || 0} schedules`);
      } else {
        testResults.listEndpoint.error = `HTTP ${listResponse.status}: ${listData.error || 'Unknown error'}`;
        console.log(`❌ List endpoint failed: ${testResults.listEndpoint.error}`);
      }
    } catch (error) {
      testResults.listEndpoint.error = error instanceof Error ? error.message : String(error);
      console.log(`❌ List endpoint error: ${testResults.listEndpoint.error}`);
    }

    // Test 2: Individual endpoint (if we have schedules)
    console.log("\n2️⃣ Testing public class schedule individual endpoint...");
    try {
      // First get a schedule ID from the list
      const listResponse = await fetch(`${baseUrl}/api/public/class-schedules?tenantId=${testTenantId}&limit=1`);
      const listData = await listResponse.json();
      
      if (listData.success && listData.data?.schedules?.length > 0) {
        const scheduleId = listData.data.schedules[0].id;
        
        const individualResponse = await fetch(`${baseUrl}/api/public/class-schedules/${scheduleId}?tenantId=${testTenantId}`);
        const individualData = await individualResponse.json();
        
        if (individualResponse.ok && individualData.success) {
          testResults.individualEndpoint.success = true;
          testResults.individualEndpoint.data = {
            status: individualResponse.status,
            scheduleId: individualData.data?.id,
            isPrivate: individualData.data?.is_private
          };
          console.log(`✅ Individual endpoint working - Schedule ID: ${scheduleId}`);
        } else {
          testResults.individualEndpoint.error = `HTTP ${individualResponse.status}: ${individualData.error || 'Unknown error'}`;
          console.log(`❌ Individual endpoint failed: ${testResults.individualEndpoint.error}`);
        }
      } else {
        testResults.individualEndpoint.error = "No schedules available to test individual endpoint";
        console.log(`⚠️ Individual endpoint skipped: ${testResults.individualEndpoint.error}`);
      }
    } catch (error) {
      testResults.individualEndpoint.error = error instanceof Error ? error.message : String(error);
      console.log(`❌ Individual endpoint error: ${testResults.individualEndpoint.error}`);
    }

    // Test 3: Private filtering verification
    console.log("\n3️⃣ Testing private schedule filtering...");
    try {
      const listResponse = await fetch(`${baseUrl}/api/public/class-schedules?tenantId=${testTenantId}`);
      const listData = await listResponse.json();
      
      if (listData.success) {
        const schedules = listData.data?.schedules || [];
        const hasPrivateSchedules = schedules.some((schedule: any) => schedule.is_private === true);
        
        if (!hasPrivateSchedules) {
          testResults.privateFiltering.success = true;
          testResults.privateFiltering.data = {
            totalSchedules: schedules.length,
            privateSchedulesFound: 0,
            message: "No private schedules returned - filtering working correctly"
          };
          console.log(`✅ Private filtering working - No private schedules in public API`);
        } else {
          testResults.privateFiltering.error = "Private schedules found in public API response";
          console.log(`❌ Private filtering failed: ${testResults.privateFiltering.error}`);
        }
      } else {
        testResults.privateFiltering.error = "Could not fetch schedules to test filtering";
        console.log(`❌ Private filtering test failed: ${testResults.privateFiltering.error}`);
      }
    } catch (error) {
      testResults.privateFiltering.error = error instanceof Error ? error.message : String(error);
      console.log(`❌ Private filtering error: ${testResults.privateFiltering.error}`);
    }

    // Test 4: Error handling
    console.log("\n4️⃣ Testing error handling...");
    try {
      // Test missing tenantId
      const errorResponse = await fetch(`${baseUrl}/api/public/class-schedules`);
      const errorData = await errorResponse.json();
      
      if (errorResponse.status === 400 && !errorData.success) {
        testResults.errorHandling.success = true;
        testResults.errorHandling.data = {
          status: errorResponse.status,
          errorMessage: errorData.error,
          message: "Error handling working correctly"
        };
        console.log(`✅ Error handling working - Proper 400 response for missing tenantId`);
      } else {
        testResults.errorHandling.error = "Expected 400 error for missing tenantId";
        console.log(`❌ Error handling failed: ${testResults.errorHandling.error}`);
      }
    } catch (error) {
      testResults.errorHandling.error = error instanceof Error ? error.message : String(error);
      console.log(`❌ Error handling test error: ${testResults.errorHandling.error}`);
    }

    // Calculate success rate
    const successCount = Object.values(testResults).filter(result => result.success).length;
    const totalTests = Object.keys(testResults).length;
    const allSuccess = successCount === totalTests;

    console.log(`\n📊 Test Results: ${successCount}/${totalTests} tests passed`);

    return NextResponse.json({
      success: allSuccess,
      message: allSuccess ? "All public class schedules API tests passed!" : `${successCount}/${totalTests} tests passed`,
      data: {
        summary: {
          allTestsPassed: allSuccess,
          successfulTests: successCount,
          totalTests: totalTests,
          endpoints: {
            listEndpoint: testResults.listEndpoint.success ? "✅ WORKING" : "❌ FAILED",
            individualEndpoint: testResults.individualEndpoint.success ? "✅ WORKING" : "❌ FAILED",
            privateFiltering: testResults.privateFiltering.success ? "✅ WORKING" : "❌ FAILED",
            errorHandling: testResults.errorHandling.success ? "✅ WORKING" : "❌ FAILED"
          }
        },
        results: testResults,
        testInfo: {
          baseUrl,
          testTenantId,
          timestamp: new Date().toISOString()
        }
      }
    });

  } catch (error) {
    console.error("❌ Test execution failed:", error);
    return NextResponse.json(
      { 
        success: false,
        error: error instanceof Error ? error.message : "Test execution failed",
        message: "Failed to execute public class schedules API tests"
      },
      { status: 500 }
    );
  }
}
