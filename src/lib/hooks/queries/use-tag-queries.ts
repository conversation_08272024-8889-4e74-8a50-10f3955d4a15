import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { type Tag } from "@/lib/db/schema";

// Types for API requests/responses
export interface TagFilters {
  search?: string;
  customerId?: string;
}

export interface CreateTagData {
  tenantId: number;
  name?: string;
  description?: string;
  customColor?: string;
  customerId?: string;
}

export interface UpdateTagData extends Partial<CreateTagData> {}

// Query keys factory
export const tagKeys = {
  all: ["tags"] as const,
  lists: () => [...tagKeys.all, "list"] as const,
  list: (filters: TagFilters) => [...tagKeys.lists(), filters] as const,
  details: () => [...tagKeys.all, "detail"] as const,
  detail: (id: string) => [...tagKeys.details(), id] as const,
  customer: (customerId: string) => [...tagKeys.all, "customer", customerId] as const,
};

/**
 * Hook to fetch tags with optional filtering
 */
export function useTags(filters: TagFilters = {}, tenantId: number = 1) {
  return useQuery({
    queryKey: tagKeys.list({ ...filters, tenantId }),
    queryFn: async (): Promise<Tag[]> => {
      const params = new URLSearchParams();

      params.append("tenantId", tenantId.toString());
      if (filters.search) params.append("search", filters.search);
      if (filters.customerId) params.append("customerId", filters.customerId);

      const response = await fetch(`/api/tags?${params.toString()}`);
      if (!response.ok) {
        throw new Error("Failed to fetch tags");
      }

      const result = await response.json();
      return result.data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to fetch a single tag by ID
 */
export function useTag(id: string) {
  return useQuery({
    queryKey: tagKeys.detail(id),
    queryFn: async (): Promise<Tag> => {
      const response = await fetch(`/api/tags/${id}`);
      if (!response.ok) {
        throw new Error("Failed to fetch tag");
      }
      
      const result = await response.json();
      return result.data;
    },
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to fetch tags for a specific customer
 */
export function useCustomerTags(customerId: string) {
  return useQuery({
    queryKey: tagKeys.customer(customerId),
    queryFn: async (): Promise<Tag[]> => {
      const response = await fetch(`/api/tags?customerId=${customerId}`);
      if (!response.ok) {
        throw new Error("Failed to fetch customer tags");
      }

      const result = await response.json();
      return result.data;
    },
    enabled: !!customerId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Hook to create a new tag
 */
export function useCreateTag() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateTagData): Promise<Tag> => {
      const response = await fetch("/api/tags", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to create tag");
      }

      const result = await response.json();
      return result.data;
    },
    onSuccess: () => {
      // Invalidate and refetch tags lists
      queryClient.invalidateQueries({ queryKey: tagKeys.lists() });
    },
  });
}

/**
 * Hook to update a tag
 */
export function useUpdateTag() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: UpdateTagData }): Promise<Tag> => {
      const response = await fetch(`/api/tags/${id}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to update tag");
      }

      const result = await response.json();
      return result.data;
    },
    onSuccess: (updatedTag) => {
      // Update the specific tag in cache
      queryClient.setQueryData(tagKeys.detail(updatedTag.id), updatedTag);
      // Invalidate lists to reflect changes
      queryClient.invalidateQueries({ queryKey: tagKeys.lists() });
    },
  });
}

/**
 * Hook to delete a tag
 */
export function useDeleteTag() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string): Promise<void> => {
      const response = await fetch(`/api/tags/${id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || "Failed to delete tag");
      }
    },
    onSuccess: (_, deletedId) => {
      // Remove the tag from cache
      queryClient.removeQueries({ queryKey: tagKeys.detail(deletedId) });
      // Invalidate lists to reflect changes
      queryClient.invalidateQueries({ queryKey: tagKeys.lists() });
    },
  });
}


