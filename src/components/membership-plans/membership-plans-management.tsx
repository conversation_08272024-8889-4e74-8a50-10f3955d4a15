"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Plus, Search, Edit, Trash2, CreditCard, Filter, ToggleLeft, ToggleRight } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { MembershipPlanForm } from "@/components/forms/membership-plan-form";
import {
  useMembershipPlanSearch,
  useCreateMembershipPlan,
  useUpdateMembershipPlan,
  useDeleteMembershipPlan,
  type MembershipPlanFormData,
} from "@/lib/hooks/queries/use-membership-plan-queries";
import { type MembershipPlan } from "@/lib/db/schema";
import { SuccessToast } from "@/components/ui/success-toast";

/**
 * MembershipPlansManagement Component
 * 
 * Component untuk manage membership plans dengan full CRUD functionality.
 * Mengikuti pattern yang sama dengan classes management dan components lainnya.
 * 
 * Features:
 * - Search plans by name
 * - Filter by active status
 * - Create, edit, delete plans
 * - Toggle active status
 * - Responsive grid layout
 * - Success feedback dengan SuccessToast
 */

interface MembershipPlansManagementProps {
  tenantId: number;
}

export function MembershipPlansManagement({ tenantId }: MembershipPlansManagementProps) {
  // State management
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingPlan, setEditingPlan] = useState<MembershipPlan | null>(null);
  const [deletingPlan, setDeletingPlan] = useState<MembershipPlan | null>(null);
  const [showSuccessToast, setShowSuccessToast] = useState(false);
  const [createdPlan, setCreatedPlan] = useState<MembershipPlan | null>(null);

  // Queries
  const {
    data: searchResult,
    isLoading,
    error,
    refetch,
  } = useMembershipPlanSearch(
    tenantId, 
    searchTerm, 
    statusFilter === "all" ? undefined : statusFilter === "active", 
    50, 
    0
  );

  // Mutations
  const createMutation = useCreateMembershipPlan();
  const updateMutation = useUpdateMembershipPlan();
  const deleteMutation = useDeleteMembershipPlan();

  // Handlers
  const handleCreatePlan = async (data: MembershipPlanFormData) => {
    try {
      const newPlan = await createMutation.mutateAsync(data);

      setIsCreateDialogOpen(false);
      setCreatedPlan(newPlan);
      setShowSuccessToast(true);

      // Hide success toast after duration
      setTimeout(() => {
        setShowSuccessToast(false);
      }, 4000);
    } catch (error) {
      console.error("Create plan error:", error);
    }
  };

  const handleEditPlan = async (data: MembershipPlanFormData) => {
    if (!editingPlan) return;

    try {
      const updatedPlan = await updateMutation.mutateAsync({
        id: editingPlan.id,
        tenantId: editingPlan.tenantId,
        data: {
          name: data.name,
          description: data.description,
          price: data.price,
          currency: data.currency,
          duration_value: data.duration_value,
          duration_unit: data.duration_unit,
          is_active: data.is_active,
        },
      });

      setEditingPlan(null);
      setCreatedPlan(updatedPlan);
      setShowSuccessToast(true);

      // Hide success toast after duration
      setTimeout(() => {
        setShowSuccessToast(false);
      }, 4000);
    } catch (error) {
      console.error("Update plan error:", error);
    }
  };

  const handleDeletePlan = async () => {
    if (!deletingPlan) return;

    try {
      await deleteMutation.mutateAsync({
        id: deletingPlan.id,
        tenantId: deletingPlan.tenantId,
      });

      setDeletingPlan(null);
      setShowSuccessToast(true);

      // Hide success toast after duration
      setTimeout(() => {
        setShowSuccessToast(false);
      }, 4000);
    } catch (error) {
      console.error("Delete plan error:", error);
    }
  };

  const handleToggleStatus = async (plan: MembershipPlan) => {
    try {
      await updateMutation.mutateAsync({
        id: plan.id,
        tenantId: plan.tenantId,
        data: {
          is_active: !plan.is_active,
        },
      });

      setShowSuccessToast(true);
      setTimeout(() => {
        setShowSuccessToast(false);
      }, 4000);
    } catch (error) {
      console.error("Toggle status error:", error);
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading membership plans...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <p className="text-red-600 mb-4">Error loading plans: {String(error)}</p>
          <Button onClick={() => refetch()} variant="outline">
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  const plans = searchResult?.plans || [];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Membership Plans</h1>
          <p className="text-muted-foreground">
            Manage your membership plans and pricing
          </p>
        </div>
        
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Add Plan
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Create Membership Plan</DialogTitle>
            </DialogHeader>
            <MembershipPlanForm
              tenantId={tenantId}
              onSubmit={handleCreatePlan}
              onCancel={() => setIsCreateDialogOpen(false)}
              isLoading={createMutation.isPending}
            />
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        {/* Search */}
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search plans..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Status Filter */}
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-full sm:w-[200px]">
            <Filter className="h-4 w-4 mr-2" />
            <SelectValue placeholder="All Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="active">Active Only</SelectItem>
            <SelectItem value="inactive">Inactive Only</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Plans Grid */}
      {plans.length === 0 ? (
        <div className="text-center py-12">
          <CreditCard className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium mb-2">No plans found</h3>
          <p className="text-muted-foreground mb-4">
            {searchTerm || statusFilter !== "all" ? "Try adjusting your search or filters" : "Get started by creating your first membership plan"}
          </p>
          {!searchTerm && statusFilter === "all" && (
            <Button onClick={() => setIsCreateDialogOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Add Plan
            </Button>
          )}
        </div>
      ) : (
        <motion.div 
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          <AnimatePresence>
            {plans.map((plan: MembershipPlan) => (
              <motion.div
                key={plan.id}
                layout
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                transition={{ duration: 0.2 }}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Card className="hover:shadow-lg transition-all duration-200">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-lg font-medium mb-2">
                          {plan.name}
                        </CardTitle>
                        <div className="flex flex-wrap gap-1 mb-2">
                          {plan.price && (
                            <Badge variant="secondary" className="text-xs">
                              {plan.currency || 'USD'} {plan.price}
                            </Badge>
                          )}
                          {plan.duration_value && plan.duration_unit && (
                            <Badge variant="outline" className="text-xs">
                              {plan.duration_value} {plan.duration_unit}
                            </Badge>
                          )}
                          <Badge 
                            variant={plan.is_active ? "default" : "secondary"} 
                            className="text-xs"
                          >
                            {plan.is_active ? "Active" : "Inactive"}
                          </Badge>
                        </div>
                      </div>
                      <div className="flex gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleToggleStatus(plan)}
                          title={plan.is_active ? "Deactivate" : "Activate"}
                        >
                          {plan.is_active ? (
                            <ToggleRight className="h-4 w-4 text-green-600" />
                          ) : (
                            <ToggleLeft className="h-4 w-4 text-gray-400" />
                          )}
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setEditingPlan(plan)}
                          title="Edit Plan"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setDeletingPlan(plan)}
                          title="Delete Plan"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    {plan.description && (
                      <p className="text-sm text-muted-foreground mb-3 line-clamp-2">
                        {plan.description}
                      </p>
                    )}
                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <span>Created {new Date(plan.createdAt).toLocaleDateString()}</span>
                      <Badge variant="secondary" className="text-xs">
                        ID: {plan.id.slice(-8)}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </AnimatePresence>
        </motion.div>
      )}

      {/* Edit Dialog */}
      <Dialog open={!!editingPlan} onOpenChange={() => setEditingPlan(null)}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Membership Plan</DialogTitle>
          </DialogHeader>
          {editingPlan && (
            <MembershipPlanForm
              membershipPlan={editingPlan}
              tenantId={tenantId}
              onSubmit={handleEditPlan}
              onCancel={() => setEditingPlan(null)}
              isLoading={updateMutation.isPending}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deletingPlan} onOpenChange={() => setDeletingPlan(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Membership Plan</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{deletingPlan?.name}"? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeletePlan}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Success Animations */}
      <SuccessToast
        isVisible={showSuccessToast}
        onClose={() => setShowSuccessToast(false)}
        title="Membership Plan Success!"
        description={createdPlan ? 
          `Plan "${createdPlan.name}" has been processed successfully.` : 
          "Your membership plan operation completed successfully."
        }
        type="general"
        action={{
          label: "View All Plans",
          onClick: () => setShowSuccessToast(false)
        }}
      />
    </div>
  );
}
