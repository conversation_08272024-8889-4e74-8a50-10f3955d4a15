import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { z } from "zod";
import { customerSegmentsService } from "@/lib/services/customer-segments.service";

// Validation schema for query parameters
const querySchema = z.object({
  tenantId: z.string().transform((val) => parseInt(val)).optional(),
});

/**
 * GET /api/customer-segments/stats
 * Get customer segment statistics
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    // Validate query parameters
    const { tenantId } = querySchema.parse(queryParams);

    console.log(`👥 [GET /api/customer-segments/stats] Fetching customer segment statistics for tenant ${tenantId || 'all'}`);

    const stats = await customerSegmentsService.getStats(tenantId);

    return NextResponse.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error("👥 [GET /api/customer-segments/stats] Error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid query parameters", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to fetch customer segment statistics" },
      { status: 500 }
    );
  }
}
