import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { ClassImage } from '@/lib/db/schema';

/**
 * Class Image Queries Hooks
 * 
 * Hooks untuk manage state dan data fetching class images menggunakan TanStack Query.
 * Pattern ini mengikuti use-class-queries.ts yang sudah established dan proven.
 * 
 * Ini kayak "jembatan" antara UI components dan service layer.
 * Semua caching, error handling, dan optimistic updates dihandle di sini.
 */

// Query Keys - ini penting untuk caching strategy
export const classImageKeys = {
  all: ['class-images'] as const,
  lists: () => [...classImageKeys.all, 'list'] as const,
  list: (filters: Record<string, any>) => [...classImageKeys.lists(), { filters }] as const,
  details: () => [...classImageKeys.all, 'detail'] as const,
  detail: (id: string) => [...classImageKeys.details(), id] as const,
  byClass: (classId: string) => [...classImageKeys.all, 'class', classId] as const,
  stats: (classId: string) => [...classImageKeys.all, 'stats', classId] as const,
};

// API Functions - ini yang handle komunikasi dengan backend
const classImageApi = {
  getByClass: async (classId: string): Promise<ClassImage[]> => {
    const response = await fetch(`/api/class-images?classId=${classId}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch class images: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data?.images || [];
  },

  getById: async (id: string): Promise<ClassImage> => {
    const response = await fetch(`/api/class-images/${id}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch class image: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data;
  },

  create: async (data: {
    class_id: string;
    image_url: string;
  }): Promise<ClassImage> => {
    const response = await fetch('/api/class-images', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      throw new Error(`Failed to create class image: ${response.statusText}`);
    }
    const result = await response.json();
    return result.data;
  },

  update: async (id: string, data: {
    image_url?: string;
  }): Promise<ClassImage> => {
    const response = await fetch(`/api/class-images/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      throw new Error(`Failed to update class image: ${response.statusText}`);
    }
    const result = await response.json();
    return result.data;
  },

  delete: async (id: string): Promise<void> => {
    const response = await fetch(`/api/class-images/${id}`, {
      method: 'DELETE',
    });
    if (!response.ok) {
      throw new Error(`Failed to delete class image: ${response.statusText}`);
    }
  },

  getStats: async (classId: string) => {
    const response = await fetch(`/api/class-images/stats?classId=${classId}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch image stats: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data;
  },
};

// Query Hooks - ini yang dipake di components
export function useClassImages(classId: string) {
  return useQuery({
    queryKey: classImageKeys.byClass(classId),
    queryFn: () => classImageApi.getByClass(classId),
    enabled: !!classId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useClassImage(id: string) {
  return useQuery({
    queryKey: classImageKeys.detail(id),
    queryFn: () => classImageApi.getById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
}

export function useClassImageStats(classId: string) {
  return useQuery({
    queryKey: classImageKeys.stats(classId),
    queryFn: () => classImageApi.getStats(classId),
    enabled: !!classId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

// Mutation Hooks - ini untuk create, update, delete
export function useCreateClassImage() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: classImageApi.create,
    onSuccess: (newImage) => {
      // Invalidate and refetch class images
      queryClient.invalidateQueries({ 
        queryKey: classImageKeys.byClass(newImage.class_id) 
      });
      queryClient.invalidateQueries({ 
        queryKey: classImageKeys.stats(newImage.class_id) 
      });
    },
  });
}

export function useUpdateClassImage() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, data }: { 
      id: string; 
      data: { image_url?: string; }
    }) => {
      return classImageApi.update(id, data);
    },
    onSuccess: (updatedImage) => {
      // Update cache with updated image
      queryClient.setQueryData(
        classImageKeys.detail(updatedImage.id), 
        updatedImage
      );
      
      // Invalidate related queries
      queryClient.invalidateQueries({ 
        queryKey: classImageKeys.byClass(updatedImage.class_id) 
      });
    },
  });
}

export function useDeleteClassImage() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, classId }: { id: string; classId: string }) => {
      return classImageApi.delete(id);
    },
    onMutate: async ({ id, classId }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ 
        queryKey: classImageKeys.byClass(classId) 
      });

      // Snapshot the previous value
      const previousImages = queryClient.getQueryData(
        classImageKeys.byClass(classId)
      ) as ClassImage[] | undefined;

      // Optimistically update to the new value
      if (previousImages) {
        const updatedImages = previousImages.filter(img => img.id !== id);
        queryClient.setQueryData(
          classImageKeys.byClass(classId), 
          updatedImages
        );
      }

      // Return a context object with the snapshotted value
      return { previousImages };
    },
    onError: (_, { classId }, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousImages) {
        queryClient.setQueryData(
          classImageKeys.byClass(classId), 
          context.previousImages
        );
      }
    },
    onSuccess: (_, { classId }) => {
      // Invalidate and refetch
      queryClient.invalidateQueries({ 
        queryKey: classImageKeys.byClass(classId) 
      });
      queryClient.invalidateQueries({ 
        queryKey: classImageKeys.stats(classId) 
      });
    },
  });
}

// Export types
export interface ClassImageFormData {
  class_id: string;
  image_url: string;
}

export interface ClassImageUploadData {
  class_id: string;
  file: File;
}

// Upload function for direct file upload
export const uploadClassImage = async (classId: string, file: File): Promise<ClassImage> => {
  // First upload the file
  const formData = new FormData();
  formData.append('file', file);
  formData.append('folder', 'class-images');

  const uploadResponse = await fetch('/api/upload', {
    method: 'POST',
    body: formData,
  });

  if (!uploadResponse.ok) {
    const errorData = await uploadResponse.json();
    throw new Error(errorData.error || 'Upload failed');
  }

  const uploadData = await uploadResponse.json();
  const imageUrl = uploadData.file.url;

  // Then create the class image record
  return classImageApi.create({
    class_id: classId,
    image_url: imageUrl,
  });
};
