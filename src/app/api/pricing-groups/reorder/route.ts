import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { z } from "zod";
import { PricingGroupService } from "@/lib/services/pricing-groups.service";

// Validation schema for reordering
const reorderSchema = z.object({
  tenantId: z.number().int().positive("Tenant ID is required"),
  items: z.array(z.object({
    id: z.string().min(1, "ID is required"),
    sortOrder: z.number().int().min(0, "Sort order must be non-negative"),
  })).min(1, "At least one item is required"),
});

const pricingGroupService = new PricingGroupService();

// PUT /api/pricing-groups/reorder - Reorder pricing groups
export async function PUT(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = reorderSchema.parse(body);

    const updatedPricingGroups = await pricingGroupService.reorder(
      validatedData.tenantId,
      validatedData.items
    );

    return NextResponse.json(
      {
        message: "Pricing groups reordered successfully",
        data: updatedPricingGroups
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error reordering pricing groups:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to reorder pricing groups" },
      { status: 500 }
    );
  }
}
