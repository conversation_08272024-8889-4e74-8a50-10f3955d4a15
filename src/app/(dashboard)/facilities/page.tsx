"use client";

import { useState } from "react";
import { useSession } from "next-auth/react";
import { redirect } from "next/navigation";
import { FacilitiesManagement } from "@/components/facilities/facilities-management";
import { PermissionGuard } from "@/components/rbac/permission-guard";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Building, Shield, Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useRBAC } from "@/lib/hooks/use-rbac";
import { useTenants } from "@/lib/hooks/queries/use-tenant-queries";

/**
 * Facilities Page
 *
 * Halaman untuk manage facilities dengan RBAC protection.
 * Mengikuti pola yang sama dengan locations dan equipment pages.
 *
 * Features:
 * - RBAC protection dengan PermissionGuard
 * - Multi-tenant support untuk super admin
 * - Tenant isolation untuk regular users
 * - Professional UI layout dengan tabs
 * - Error handling untuk unauthorized access
 */

export default function FacilitiesPage() {
  const { data: session, status } = useSession();
  const { isSuperAdmin } = useRBAC();
  const [selectedTenantId, setSelectedTenantId] = useState<number | null>(null);

  // Always call hooks at the top level
  const { data: tenantsData, isLoading: tenantsLoading } = useTenants();
  const tenants = tenantsData?.tenants || [];

  // Redirect if not authenticated
  if (status === "loading") {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!session?.user) {
    redirect("/auth/signin");
  }

  // Get tenant ID from session or allow super admin to manage all
  const userTenantId = session.user.tenantId;
  const isUserSuperAdmin = isSuperAdmin();

  // Debug: Log session data for troubleshooting
  console.log("DEBUG - Facilities Page Session data:", {
    userId: session.user.id,
    email: session.user.email,
    tenantId: userTenantId,
    roles: session.user.roles,
    permissions: session.user.permissions,
    isUserSuperAdmin,
    sessionStatus: status,
    hasPermission: session.user.permissions?.includes("facilities.manage"),
  });

  // For regular users, require tenantId
  if (!isUserSuperAdmin && !userTenantId) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-600">
              <Shield className="h-5 w-5" />
              Access Denied
            </CardTitle>
            <CardDescription>
              You need to be associated with a tenant to access facilities.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              Please contact your administrator to get proper access.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (tenantsLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8">
      {/* Custom permission check that handles super admin properly */}
      {(session.user.roles?.includes("super_admin") || session.user.permissions?.includes("facilities.manage")) ? (
        <div className="space-y-6">
          {/* Page Header */}
          <div className="flex items-center gap-3">
            <div className="p-2 bg-primary/10 rounded-lg">
              <Building className="h-6 w-6 text-primary" />
            </div>
            <div>
              <h1 className="text-3xl font-bold tracking-tight">Facilities Management</h1>
              <p className="text-muted-foreground">
                Manage your facility spaces, amenities, and resources
              </p>
            </div>
          </div>

          {/* Content based on user type */}
          {isUserSuperAdmin ? (
            // Super Admin: Show tabs for all tenants
            <Tabs defaultValue="all" className="space-y-4">
              <TabsList>
                <TabsTrigger value="all">All Facilities</TabsTrigger>
                {tenants.map((tenant) => (
                  <TabsTrigger key={tenant.id} value={`tenant-${tenant.id}`}>
                    {tenant.name}
                    <Badge variant="secondary" className="ml-2">
                      {/* TODO: Add facility count per tenant */}
                      0
                    </Badge>
                  </TabsTrigger>
                ))}
              </TabsList>

              <TabsContent value="all">
                <div className="text-center py-12">
                  <Building className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">Select a tenant to manage facilities</h3>
                  <p className="text-muted-foreground mb-4">
                    Choose a tenant tab above to view and manage their facilities
                  </p>
                </div>
              </TabsContent>

              {tenants.map((tenant) => (
                <TabsContent key={tenant.id} value={`tenant-${tenant.id}`}>
                  <FacilitiesManagement tenantId={tenant.id} />
                </TabsContent>
              ))}
            </Tabs>
          ) : (
            // Regular User: Show only their tenant's facilities
            <FacilitiesManagement tenantId={userTenantId!} />
          )}
        </div>
      ) : (
        // No permission fallback
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-600">
              <Shield className="h-5 w-5" />
              Access Denied
            </CardTitle>
            <CardDescription>
              You don't have permission to manage facilities.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              Contact your administrator if you need access to this feature.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
