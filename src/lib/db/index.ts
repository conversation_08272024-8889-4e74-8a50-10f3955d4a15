import { drizzle } from "drizzle-orm/node-postgres";
import { Pool } from "pg";
import * as schema from "./schema";

// PostgreSQL database configuration
const dbConfig = {
  host: process.env.DB_HOST || "127.0.0.1",
  port: parseInt(process.env.DB_PORT || "5433"),
  user: process.env.DB_USER || "citizix_user",
  password: process.env.DB_PASSWORD || "S3cret",
  database: process.env.DB_NAME || "saas_app",
  ssl: process.env.NODE_ENV === "production" ? { rejectUnauthorized: false } : false,
  max: 20, // Maximum number of clients in the pool
  idleTimeoutMillis: 30000, // Close idle clients after 30 seconds
  connectionTimeoutMillis: 2000, // Return an error after 2 seconds if connection could not be established
};

// Create PostgreSQL connection pool
const pool = new Pool(dbConfig);

// Type untuk database instance
export type DatabaseInstance = ReturnType<typeof drizzle<typeof schema>>;

// Create Drizzle instance
const db = drizzle(pool, { schema });

export { db, pool };

// Helper function untuk mendapatkan database instance
export function getDatabase(): DatabaseInstance {
  return db;
}

// Helper function untuk close database connection
export async function closeDatabaseConnection() {
  try {
    await pool.end();
  } catch (error) {
    console.error("Error closing database connection:", error);
  }
}

// Export schema untuk digunakan di tempat lain
export * from "./schema";

// Database utilities
export async function healthCheck(): Promise<boolean> {
  try {
    await db.select().from(schema.users).limit(1);
    return true;
  } catch (error) {
    console.error("Database health check failed:", error);
    return false;
  }
}

// Transaction helper
export async function withTransaction<T>(
  callback: (tx: any) => Promise<T>
): Promise<T> {
  return await db.transaction(callback);
}

// Test database connection
export async function testConnection(): Promise<boolean> {
  try {
    const client = await pool.connect();
    await client.query('SELECT NOW()');
    client.release();
    return true;
  } catch (error) {
    console.error("Database connection test failed:", error);
    return false;
  }
}
