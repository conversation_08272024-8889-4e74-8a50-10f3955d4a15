"use client";

import { z } from "zod";
import { FileText, Building, Hash, Clock, Shield, ToggleLeft } from "lucide-react";
import { createFormComponent } from "@/lib/core/base-form";
import { WaiverForm } from "@/lib/db/schema";
import { useTenants } from "@/lib/hooks/queries/use-tenant-queries";

// Form validation schema
const waiverFormSchema = z.object({
  tenantId: z.number().int().positive("Tenant is required"),
  name: z.string().min(1, "Name is required").max(255, "Name must be less than 255 characters"),
  description: z.string().max(1000, "Description must be less than 1000 characters").optional(),
  content: z.string().min(1, "Content is required"),
  version: z.string().max(50, "Version must be less than 50 characters").optional(),
  isActive: z.boolean(),
  isRequired: z.boolean(),
  expiryDays: z.number().int().min(1, "Expiry days must be at least 1").optional(),
  sortOrder: z.number().int().min(0, "Sort order must be non-negative").optional(),
});

type WaiverFormData = z.infer<typeof waiverFormSchema>;

// Get tenants for dropdown
function useTenantOptions() {
  const { data: tenantsData, isLoading } = useTenants();
  const tenants = tenantsData?.tenants || [];
  
  return {
    options: tenants.map(tenant => ({
      value: tenant.id,
      label: tenant.name,
    })),
    isLoading,
  };
}

// Form configuration
const formConfig = {
  schema: waiverFormSchema,
  defaultValues: {
    tenantId: 1,
    name: "",
    description: "",
    content: "",
    version: "1.0",
    isActive: true,
    isRequired: false,
    expiryDays: 365,
    sortOrder: 0,
  },
  fields: [
    {
      name: "tenantId" as const,
      label: "Tenant",
      type: "select" as const,
      placeholder: "Select tenant",
      required: true,
      icon: Building,
      options: [], // Will be populated dynamically
    },
    {
      name: "name" as const,
      label: "Waiver Form Name",
      type: "text" as const,
      placeholder: "Enter waiver form name (e.g., General Liability Waiver)",
      required: true,
      icon: FileText,
    },
    {
      name: "description" as const,
      label: "Description",
      type: "textarea" as const,
      placeholder: "Enter description for this waiver form",
      icon: FileText,
      description: "Describe what this waiver form covers or its purpose",
    },
    {
      name: "content" as const,
      label: "Waiver Content",
      type: "textarea" as const,
      placeholder: "Enter the waiver form content (HTML or plain text)",
      required: true,
      icon: FileText,
      description: "The actual content of the waiver form that users will see and sign",
    },
    {
      name: "version" as const,
      label: "Version",
      type: "text" as const,
      placeholder: "Enter version (e.g., 1.0, 2.1)",
      icon: Hash,
      description: "Version number for tracking changes to the waiver form",
    },
    {
      name: "expiryDays" as const,
      label: "Expiry Days",
      type: "number" as const,
      placeholder: "Enter number of days until waiver expires",
      icon: Clock,
      description: "Number of days after signing before the waiver expires (default: 365)",
    },
    {
      name: "sortOrder" as const,
      label: "Sort Order",
      type: "number" as const,
      placeholder: "Enter sort order (0 = first)",
      icon: Hash,
      description: "Lower numbers appear first in the list. Leave as 0 to add at the end.",
    },
    {
      name: "isActive" as const,
      label: "Active Status",
      type: "switch" as const,
      icon: ToggleLeft,
      description: "Whether this waiver form is active and available for use",
    },
    {
      name: "isRequired" as const,
      label: "Required Status",
      type: "switch" as const,
      icon: Shield,
      description: "Whether this waiver form is required for all customers",
    },
  ],
  sections: [
    {
      title: "Basic Information",
      description: "General details about the waiver form",
      fields: ["tenantId", "name", "description"] as const,
    },
    {
      title: "Content & Version",
      description: "Waiver form content and versioning",
      fields: ["content", "version"] as const,
    },
    {
      title: "Settings",
      description: "Configuration and behavior settings",
      fields: ["expiryDays", "sortOrder", "isActive", "isRequired"] as const,
    },
  ],
};

// Create the form component using the factory
const BaseWaiverFormForm = createFormComponent<WaiverForm, WaiverFormData>(
  "Waiver Form",
  formConfig
);

// Enhanced component with tenant options
interface WaiverFormFormProps {
  entity?: WaiverForm;
  tenantId?: number;
  onSubmit: (data: WaiverFormData) => Promise<void>;
  onCancel: () => void;
  className?: string;
  title?: string;
  description?: string;
}

export function WaiverFormForm({ tenantId, ...props }: WaiverFormFormProps) {
  const { options: tenantOptions, isLoading: tenantsLoading } = useTenantOptions();

  // Update form config with tenant options and default tenantId
  const enhancedConfig = {
    ...formConfig,
    defaultValues: {
      ...formConfig.defaultValues,
      tenantId: tenantId || formConfig.defaultValues.tenantId,
    },
    fields: formConfig.fields.map(field =>
      field.name === "tenantId"
        ? { ...field, options: tenantOptions }
        : field
    ),
  };

  // Create enhanced form component
  const EnhancedForm = createFormComponent<WaiverForm, WaiverFormData>(
    "Waiver Form",
    enhancedConfig
  );

  if (tenantsLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return <EnhancedForm {...props} icon={FileText} />;
}
