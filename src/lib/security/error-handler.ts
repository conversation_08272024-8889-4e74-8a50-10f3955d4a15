import { NextResponse } from "next/server";
import { z } from "zod";
import { customerAuthLogs, db } from "@/lib/db";
import { createId } from "@paralleldrive/cuid2";
import { authErrorCodes, authStatusCodes } from "./rate-limit.config";

/**
 * FAANG-Level Error Handling System for Customer Authentication
 * 
 * Provides comprehensive error handling with specific error codes,
 * audit logging, and security monitoring
 */

export interface AuthError {
  code: string;
  message: string;
  statusCode: number;
  details?: Record<string, any>;
  timestamp: string;
  requestId: string;
  severity: "low" | "medium" | "high" | "critical";
}

export interface SecurityEvent {
  customerId?: string;
  tenantId?: number;
  event: string;
  severity: "info" | "warning" | "error" | "critical";
  ipAddress: string;
  userAgent?: string;
  details: Record<string, any>;
  riskScore?: number;
}

/**
 * Error classification and mapping
 */
const errorClassification = {
  // Authentication errors
  [authErrorCodes.INVALID_CREDENTIALS]: {
    statusCode: authStatusCodes.UNAUTHORIZED,
    severity: "medium" as const,
    message: "Invalid email or password",
    logEvent: "login_failed",
  },
  [authErrorCodes.ACCOUNT_LOCKED]: {
    statusCode: authStatusCodes.FORBIDDEN,
    severity: "high" as const,
    message: "Account is temporarily locked due to multiple failed login attempts",
    logEvent: "account_locked_access_attempt",
  },
  [authErrorCodes.ACCOUNT_DISABLED]: {
    statusCode: authStatusCodes.FORBIDDEN,
    severity: "high" as const,
    message: "Account has been disabled",
    logEvent: "disabled_account_access_attempt",
  },
  [authErrorCodes.EMAIL_NOT_VERIFIED]: {
    statusCode: authStatusCodes.FORBIDDEN,
    severity: "low" as const,
    message: "Email address must be verified before login",
    logEvent: "unverified_email_login_attempt",
  },

  // Token errors
  [authErrorCodes.TOKEN_INVALID]: {
    statusCode: authStatusCodes.UNAUTHORIZED,
    severity: "medium" as const,
    message: "Invalid authentication token",
    logEvent: "invalid_token_used",
  },
  [authErrorCodes.TOKEN_EXPIRED]: {
    statusCode: authStatusCodes.UNAUTHORIZED,
    severity: "low" as const,
    message: "Authentication token has expired",
    logEvent: "expired_token_used",
  },
  [authErrorCodes.TOKEN_REVOKED]: {
    statusCode: authStatusCodes.UNAUTHORIZED,
    severity: "medium" as const,
    message: "Authentication token has been revoked",
    logEvent: "revoked_token_used",
  },
  [authErrorCodes.REFRESH_TOKEN_INVALID]: {
    statusCode: authStatusCodes.UNAUTHORIZED,
    severity: "medium" as const,
    message: "Invalid refresh token",
    logEvent: "invalid_refresh_token_used",
  },

  // OAuth errors
  [authErrorCodes.OAUTH_ERROR]: {
    statusCode: authStatusCodes.BAD_REQUEST,
    severity: "medium" as const,
    message: "OAuth authentication failed",
    logEvent: "oauth_failed",
  },
  [authErrorCodes.OAUTH_STATE_MISMATCH]: {
    statusCode: authStatusCodes.BAD_REQUEST,
    severity: "high" as const,
    message: "OAuth state parameter mismatch - possible CSRF attack",
    logEvent: "oauth_csrf_attempt",
  },
  [authErrorCodes.OAUTH_CODE_INVALID]: {
    statusCode: authStatusCodes.BAD_REQUEST,
    severity: "medium" as const,
    message: "Invalid OAuth authorization code",
    logEvent: "oauth_invalid_code",
  },

  // Session errors
  [authErrorCodes.SESSION_INVALID]: {
    statusCode: authStatusCodes.UNAUTHORIZED,
    severity: "medium" as const,
    message: "Invalid session",
    logEvent: "invalid_session_used",
  },
  [authErrorCodes.SESSION_EXPIRED]: {
    statusCode: authStatusCodes.UNAUTHORIZED,
    severity: "low" as const,
    message: "Session has expired",
    logEvent: "expired_session_used",
  },
  [authErrorCodes.SESSION_LIMIT_EXCEEDED]: {
    statusCode: authStatusCodes.FORBIDDEN,
    severity: "medium" as const,
    message: "Maximum number of active sessions exceeded",
    logEvent: "session_limit_exceeded",
  },

  // Rate limiting
  [authErrorCodes.RATE_LIMIT_EXCEEDED]: {
    statusCode: authStatusCodes.TOO_MANY_REQUESTS,
    severity: "high" as const,
    message: "Too many requests - please try again later",
    logEvent: "rate_limit_exceeded",
  },

  // Validation errors
  [authErrorCodes.INVALID_INPUT]: {
    statusCode: authStatusCodes.BAD_REQUEST,
    severity: "low" as const,
    message: "Invalid input data",
    logEvent: "invalid_input_provided",
  },
  [authErrorCodes.MISSING_REQUIRED_FIELD]: {
    statusCode: authStatusCodes.BAD_REQUEST,
    severity: "low" as const,
    message: "Required field is missing",
    logEvent: "missing_required_field",
  },
  [authErrorCodes.TENANT_MISMATCH]: {
    statusCode: authStatusCodes.FORBIDDEN,
    severity: "high" as const,
    message: "Tenant context mismatch",
    logEvent: "tenant_mismatch_attempt",
  },

  // Server errors
  [authErrorCodes.INTERNAL_ERROR]: {
    statusCode: authStatusCodes.INTERNAL_SERVER_ERROR,
    severity: "critical" as const,
    message: "Internal server error",
    logEvent: "internal_error",
  },
  [authErrorCodes.SERVICE_UNAVAILABLE]: {
    statusCode: authStatusCodes.SERVICE_UNAVAILABLE,
    severity: "critical" as const,
    message: "Service temporarily unavailable",
    logEvent: "service_unavailable",
  },
} as const;

/**
 * Generate unique request ID for tracking
 */
function generateRequestId(): string {
  return createId();
}

/**
 * Calculate risk score based on error type and context
 */
function calculateRiskScore(
  errorCode: string,
  context: {
    ipAddress: string;
    userAgent?: string;
    customerId?: string;
    tenantId?: number;
  }
): number {
  let riskScore = 0;

  // Base risk by error type
  const baseRisk = {
    [authErrorCodes.OAUTH_STATE_MISMATCH]: 90,
    [authErrorCodes.RATE_LIMIT_EXCEEDED]: 70,
    [authErrorCodes.ACCOUNT_LOCKED]: 60,
    [authErrorCodes.TENANT_MISMATCH]: 80,
    [authErrorCodes.TOKEN_INVALID]: 50,
    [authErrorCodes.INVALID_CREDENTIALS]: 30,
  };

  riskScore = baseRisk[errorCode as keyof typeof baseRisk] || 20;

  // Increase risk for suspicious patterns
  if (context.ipAddress === "unknown") riskScore += 20;
  if (!context.userAgent) riskScore += 15;

  return Math.min(riskScore, 100);
}

/**
 * Log security event to audit log
 */
async function logSecurityEvent(event: SecurityEvent): Promise<void> {
  try {
    await db.insert(customerAuthLogs).values({
      id: createId(),
      customerId: event.customerId || null,
      tenantId: event.tenantId || null,
      event: event.event,
      method: "api",
      status: event.severity === "info" ? "success" : "failed",
      ipAddress: event.ipAddress,
      userAgent: event.userAgent || null,
      deviceType: null,
      deviceId: null,
      sessionId: null,
      errorCode: event.details.errorCode || null,
      errorMessage: event.details.message || null,
      riskScore: event.riskScore || 0,
      metadata: event.details,
      createdAt: new Date(),
    });
  } catch (error) {
    console.error("Failed to log security event:", error);
  }
}

/**
 * Create standardized auth error
 */
export function createAuthError(
  errorCode: string,
  context: {
    customerId?: string;
    tenantId?: number;
    ipAddress: string;
    userAgent?: string;
    details?: Record<string, any>;
  }
): AuthError {
  const classification = errorClassification[errorCode as keyof typeof errorClassification];
  
  if (!classification) {
    console.error(`Unknown error code: ${errorCode}`);
    return createAuthError(authErrorCodes.INTERNAL_ERROR, context);
  }

  const requestId = generateRequestId();
  const riskScore = calculateRiskScore(errorCode, context);

  const authError: AuthError = {
    code: errorCode,
    message: classification.message,
    statusCode: classification.statusCode,
    details: context.details,
    timestamp: new Date().toISOString(),
    requestId,
    severity: classification.severity,
  };

  // Log security event asynchronously
  logSecurityEvent({
    customerId: context.customerId,
    tenantId: context.tenantId,
    event: classification.logEvent,
    severity: classification.severity === "critical" ? "critical" : 
             classification.severity === "high" ? "error" :
             classification.severity === "medium" ? "warning" : "info",
    ipAddress: context.ipAddress,
    userAgent: context.userAgent,
    details: {
      errorCode,
      message: classification.message,
      requestId,
      ...context.details,
    },
    riskScore,
  }).catch(console.error);

  return authError;
}

/**
 * Create error response with security headers
 */
export function createSecureErrorResponse(
  authError: AuthError,
  additionalHeaders: Record<string, string> = {}
): NextResponse {
  const response = NextResponse.json(
    {
      success: false,
      error: {
        code: authError.code,
        message: authError.message,
        timestamp: authError.timestamp,
        requestId: authError.requestId,
        // Don't expose sensitive details in production
        ...(process.env.NODE_ENV === "development" && {
          details: authError.details,
        }),
      },
    },
    { 
      status: authError.statusCode,
      headers: additionalHeaders,
    }
  );

  // Add security headers
  response.headers.set("X-Content-Type-Options", "nosniff");
  response.headers.set("X-Frame-Options", "DENY");
  response.headers.set("X-XSS-Protection", "1; mode=block");
  response.headers.set("Referrer-Policy", "strict-origin-when-cross-origin");
  response.headers.set("X-Request-ID", authError.requestId);

  return response;
}

/**
 * Handle Zod validation errors
 */
export function handleValidationError(
  error: z.ZodError,
  context: {
    customerId?: string;
    tenantId?: number;
    ipAddress: string;
    userAgent?: string;
  }
): AuthError {
  const details = {
    validationErrors: error.errors.map(err => ({
      field: err.path.join("."),
      message: err.message,
      code: err.code,
    })),
  };

  return createAuthError(authErrorCodes.INVALID_INPUT, {
    ...context,
    details,
  });
}

/**
 * Handle unexpected errors
 */
export function handleUnexpectedError(
  error: unknown,
  context: {
    customerId?: string;
    tenantId?: number;
    ipAddress: string;
    userAgent?: string;
    operation?: string;
  }
): AuthError {
  console.error("Unexpected error:", error);

  const details = {
    operation: context.operation,
    errorType: error instanceof Error ? error.constructor.name : typeof error,
    // Don't expose error details in production
    ...(process.env.NODE_ENV === "development" && {
      errorMessage: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
    }),
  };

  return createAuthError(authErrorCodes.INTERNAL_ERROR, {
    ...context,
    details,
  });
}
