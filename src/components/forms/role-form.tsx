"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useSession } from "next-auth/react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useGroupedPermissions } from "@/lib/hooks/queries/use-permission-queries";
import { useCreateRole, useUpdateRole } from "@/lib/hooks/queries/use-role-queries";
import { Role, Permission } from "@/lib/db/schema";
import { 
  Shield, 
  Save, 
  X, 
  ChevronDown, 
  ChevronRight,
  Check,
  AlertCircle
} from "lucide-react";

// Form schema
const roleFormSchema = z.object({
  name: z.string().min(1, "Role name is required").max(100, "Role name too long"),
  display_name: z.string().min(1, "Display name is required").max(255, "Display name too long"),
  description: z.string().optional(),
  hierarchy_level: z.number().min(0).max(100),
  is_system_role: z.boolean().default(false),
  permissions: z.array(z.object({
    id: z.string(),
    module: z.string(),
    action: z.string(),
    display_name: z.string(),
  })).default([]),
});

type RoleFormData = z.infer<typeof roleFormSchema>;

interface RoleFormProps {
  role?: Role;
  onSuccess?: (role: Role) => void;
  onCancel?: () => void;
}

export function RoleForm({ role, onSuccess, onCancel }: RoleFormProps) {
  const { data: session } = useSession();
  const [expandedModules, setExpandedModules] = useState<Set<string>>(new Set());
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [isSuccess, setIsSuccess] = useState(false);

  // Queries and mutations
  const { data: groupedPermissions = {}, isLoading: permissionsLoading } = useGroupedPermissions();
  const createRoleMutation = useCreateRole();
  const updateRoleMutation = useUpdateRole();

  // Form setup
  const form = useForm<RoleFormData>({
    resolver: zodResolver(roleFormSchema),
    defaultValues: {
      name: role?.name || "",
      display_name: role?.display_name || "",
      description: role?.description || "",
      hierarchy_level: role?.hierarchy_level || 50,
      is_system_role: role?.is_system_role || false,
      permissions: role?.permissions ? 
        (Array.isArray(role.permissions) ? role.permissions.map((p: any) => ({
          id: p.id || p.permissionId || "",
          module: p.module || "",
          action: p.action || "",
          display_name: p.display_name || `${p.module}.${p.action}`,
        })) : []) : [],
    },
  });

  const { register, handleSubmit, watch, setValue, formState: { errors, isSubmitting } } = form;
  const selectedPermissions = watch("permissions");

  // Helper functions
  const hasPermission = (permission: string): boolean => {
    return session?.user?.permissions?.includes(permission) || false;
  };

  const canManageRoles = hasPermission("roles.manage") || session?.user?.roles?.includes("super_admin");

  const toggleModule = (module: string) => {
    const newExpanded = new Set(expandedModules);
    if (newExpanded.has(module)) {
      newExpanded.delete(module);
    } else {
      newExpanded.add(module);
    }
    setExpandedModules(newExpanded);
  };

  const isPermissionSelected = (permission: Permission): boolean => {
    return selectedPermissions.some(p => p.id === permission.id);
  };

  const togglePermission = (permission: Permission) => {
    const current = selectedPermissions;
    const isSelected = isPermissionSelected(permission);

    if (isSelected) {
      // Remove permission
      const updated = current.filter(p => p.id !== permission.id);
      setValue("permissions", updated);
    } else {
      // Add permission
      const newPermission = {
        id: permission.id,
        module: permission.module,
        action: permission.action,
        display_name: permission.display_name,
      };
      setValue("permissions", [...current, newPermission]);
    }
  };

  const selectAllInModule = (modulePermissions: Permission[]) => {
    const current = selectedPermissions;
    const modulePermissionIds = modulePermissions.map(p => p.id);
    
    // Remove existing permissions from this module
    const withoutModule = current.filter(p => !modulePermissionIds.includes(p.id));
    
    // Add all permissions from this module
    const newPermissions = modulePermissions.map(p => ({
      id: p.id,
      module: p.module,
      action: p.action,
      display_name: p.display_name,
    }));
    
    setValue("permissions", [...withoutModule, ...newPermissions]);
  };

  const deselectAllInModule = (modulePermissions: Permission[]) => {
    const current = selectedPermissions;
    const modulePermissionIds = modulePermissions.map(p => p.id);
    const updated = current.filter(p => !modulePermissionIds.includes(p.id));
    setValue("permissions", updated);
  };

  // Form submission
  const onSubmit = async (data: RoleFormData) => {
    try {
      setSubmitError(null);

      const roleData = {
        tenantId: session?.user?.tenantId || null,
        name: data.name,
        display_name: data.display_name,
        description: data.description,
        hierarchy_level: data.hierarchy_level,
        is_system_role: data.is_system_role,
        permissions: data.permissions,
      };

      let savedRole: Role;
      if (role) {
        // Update existing role
        savedRole = await updateRoleMutation.mutateAsync({ id: role.id, data: roleData });
      } else {
        // Create new role
        savedRole = await createRoleMutation.mutateAsync(roleData);
      }

      setIsSuccess(true);
      setTimeout(() => setIsSuccess(false), 2000);
      onSuccess?.(savedRole);
    } catch (error) {
      console.error("Error saving role:", error);

      // Handle specific validation errors
      let errorMessage = "Failed to save role. Please try again.";
      if (error instanceof Error) {
        if (error.message.includes("Tenant") && error.message.includes("not found")) {
          errorMessage = "Invalid tenant. Please contact support.";
        } else if (error.message.includes("duplicate") || error.message.includes("already exists")) {
          errorMessage = "A role with this name already exists. Please choose a different name.";
        } else if (error.message.includes("permission")) {
          errorMessage = "Invalid permissions selected. Please review your selections.";
        } else {
          errorMessage = error.message;
        }
      }

      setSubmitError(errorMessage);
    }
  };

  if (!canManageRoles) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">
            <AlertCircle className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Access Denied</h3>
            <p className="text-gray-600">You don't have permission to manage roles.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {/* Error Alert */}
      {submitError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{submitError}</AlertDescription>
        </Alert>
      )}

      {/* Success Alert */}
      {isSuccess && (
        <Alert className="border-green-200 bg-green-50 text-green-800">
          <Check className="h-4 w-4" />
          <AlertDescription>
            {role ? "Role updated successfully!" : "Role created successfully!"}
          </AlertDescription>
        </Alert>
      )}

      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            {role ? "Edit Role" : "Create New Role"}
          </CardTitle>
          <CardDescription>
            {role ? "Update role information and permissions" : "Create a new role with specific permissions"}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Role Name *</Label>
              <Input
                id="name"
                {...register("name")}
                placeholder="e.g., custom_admin"
                className={errors.name ? "border-red-500" : ""}
              />
              {errors.name && (
                <p className="text-sm text-red-600">{errors.name.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="display_name">Display Name *</Label>
              <Input
                id="display_name"
                {...register("display_name")}
                placeholder="e.g., Custom Administrator"
                className={errors.display_name ? "border-red-500" : ""}
              />
              {errors.display_name && (
                <p className="text-sm text-red-600">{errors.display_name.message}</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              {...register("description")}
              placeholder="Describe the role's purpose and responsibilities..."
              rows={3}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="hierarchy_level">Hierarchy Level</Label>
              <Select
                value={watch("hierarchy_level")?.toString()}
                onValueChange={(value) => setValue("hierarchy_level", parseInt(value))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select hierarchy level" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="0">0 - Highest (Super Admin)</SelectItem>
                  <SelectItem value="10">10 - Very High</SelectItem>
                  <SelectItem value="20">20 - High</SelectItem>
                  <SelectItem value="30">30 - Medium-High</SelectItem>
                  <SelectItem value="40">40 - Medium</SelectItem>
                  <SelectItem value="50">50 - Medium-Low</SelectItem>
                  <SelectItem value="60">60 - Low</SelectItem>
                  <SelectItem value="70">70 - Very Low</SelectItem>
                  <SelectItem value="80">80 - Minimal</SelectItem>
                  <SelectItem value="90">90 - Basic</SelectItem>
                  <SelectItem value="100">100 - Lowest (Customer)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Role Type</Label>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="is_system_role"
                  checked={watch("is_system_role")}
                  onCheckedChange={(checked) => setValue("is_system_role", !!checked)}
                />
                <Label htmlFor="is_system_role" className="text-sm">
                  System Role (built-in, cannot be deleted)
                </Label>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Permissions Selection */}
      <Card>
        <CardHeader>
          <CardTitle>Permissions</CardTitle>
          <CardDescription>
            Select the permissions this role should have. Selected: {selectedPermissions.length}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {permissionsLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="mt-2 text-gray-600">Loading permissions...</p>
            </div>
          ) : (
            <div className="space-y-4 max-h-96 overflow-y-auto">
              {Object.entries(groupedPermissions).map(([module, permissions]) => {
                const isExpanded = expandedModules.has(module);
                const selectedInModule = permissions.filter(p => isPermissionSelected(p)).length;
                const totalInModule = permissions.length;

                return (
                  <div key={module} className="border rounded-lg">
                    <div className="flex items-center justify-between p-3 bg-gray-50 rounded-t-lg">
                      <button
                        type="button"
                        onClick={() => toggleModule(module)}
                        className="flex items-center gap-2 text-sm font-medium hover:text-blue-600"
                      >
                        {isExpanded ? (
                          <ChevronDown className="h-4 w-4" />
                        ) : (
                          <ChevronRight className="h-4 w-4" />
                        )}
                        {module.charAt(0).toUpperCase() + module.slice(1)}
                        <Badge variant="outline" className="text-xs">
                          {selectedInModule}/{totalInModule}
                        </Badge>
                      </button>
                      
                      <div className="flex gap-2">
                        {selectedInModule < totalInModule && (
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => selectAllInModule(permissions)}
                            className="text-xs h-6"
                          >
                            Select All
                          </Button>
                        )}
                        {selectedInModule > 0 && (
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => deselectAllInModule(permissions)}
                            className="text-xs h-6"
                          >
                            Deselect All
                          </Button>
                        )}
                      </div>
                    </div>
                    
                    {isExpanded && (
                      <div className="p-3 space-y-2">
                        {permissions.map((permission) => (
                          <div
                            key={permission.id}
                            className="flex items-center justify-between p-2 rounded hover:bg-gray-50"
                          >
                            <div className="flex items-center space-x-3">
                              <Checkbox
                                id={permission.id}
                                checked={isPermissionSelected(permission)}
                                onCheckedChange={() => togglePermission(permission)}
                              />
                              <div>
                                <Label htmlFor={permission.id} className="text-sm font-medium cursor-pointer">
                                  {permission.display_name}
                                </Label>
                                <p className="text-xs text-gray-500">
                                  {permission.action} • {permission.module}
                                </p>
                                {permission.description && (
                                  <p className="text-xs text-gray-400 mt-1">{permission.description}</p>
                                )}
                              </div>
                            </div>
                            {isPermissionSelected(permission) && (
                              <Check className="h-4 w-4 text-green-600" />
                            )}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Form Actions */}
      <div className="flex items-center justify-end gap-4">
        {onCancel && (
          <Button type="button" variant="outline" onClick={onCancel}>
            <X className="h-4 w-4 mr-2" />
            Cancel
          </Button>
        )}
        <Button type="submit" disabled={isSubmitting}>
          <Save className="h-4 w-4 mr-2" />
          {isSubmitting ? "Saving..." : role ? "Update Role" : "Create Role"}
        </Button>
      </div>
    </form>
  );
}
