import NextAuth from "next-auth";
import { authConfig } from "./auth.config";

/**
 * NextAuth Instance dengan RBAC Configuration
 * 
 * File ini export NextAuth instance yang sudah dikonfigurasi dengan RBAC.
 * Digunakan untuk authentication dan authorization di seluruh aplikasi.
 */

const nextAuth = NextAuth(authConfig);

export const { handlers, auth, signIn, signOut } = nextAuth;
export const { GET, POST } = handlers;

// Export default untuk compatibility
export default NextAuth(authConfig);
