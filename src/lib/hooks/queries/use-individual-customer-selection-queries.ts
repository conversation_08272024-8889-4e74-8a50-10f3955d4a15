import React from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  CustomerSearchResult, 
  CustomerSearchFilters, 
  IndividualCustomerTargeting,
  PackageCustomerTargetingStats
} from '@/lib/services/individual-customer-selection.service';

// Query keys for individual customer selection
const individualCustomerKeys = {
  all: ['individual-customer-selection'] as const,
  search: (filters: CustomerSearchFilters) => [...individualCustomerKeys.all, 'search', filters] as const,
  byIds: (customerIds: string[], tenantId: number) => [...individualCustomerKeys.all, 'by-ids', customerIds, tenantId] as const,
  packageTargeting: (packageId: string, tenantId: number) => [...individualCustomerKeys.all, 'package-targeting', packageId, tenantId] as const,
  targetingStats: (packageId: string, tenantId: number) => [...individualCustomerKeys.all, 'targeting-stats', packageId, tenantId] as const,
};

// API functions for individual customer selection
const individualCustomerApi = {
  async searchCustomers(filters: CustomerSearchFilters): Promise<{
    customers: CustomerSearchResult[];
    total: number;
    hasMore: boolean;
  }> {
    const params = new URLSearchParams();
    params.append('tenantId', String(filters.tenantId));
    
    if (filters.search) params.append('search', filters.search);
    if (filters.isActive !== undefined) params.append('isActive', String(filters.isActive));
    if (filters.pricingGroupId) params.append('pricingGroupId', filters.pricingGroupId);
    if (filters.locationId) params.append('locationId', filters.locationId);
    if (filters.limit) params.append('limit', String(filters.limit));
    if (filters.offset) params.append('offset', String(filters.offset));

    const response = await fetch(`/api/customers/search?${params}`);
    if (!response.ok) {
      throw new Error('Failed to search customers');
    }

    const data = await response.json();
    return data.data;
  },

  async getCustomersByIds(customerIds: string[], tenantId: number): Promise<CustomerSearchResult[]> {
    if (customerIds.length === 0) return [];

    const params = new URLSearchParams();
    params.append('tenantId', String(tenantId));
    customerIds.forEach(id => params.append('customerIds', id));

    const response = await fetch(`/api/customers/by-ids?${params}`);
    if (!response.ok) {
      throw new Error('Failed to get customers by IDs');
    }

    const data = await response.json();
    return data.data || [];
  },

  async saveIndividualCustomerTargeting(data: IndividualCustomerTargeting): Promise<void> {
    const response = await fetch('/api/packages/individual-customer-targeting', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to save individual customer targeting');
    }
  },

  async getIndividualCustomerTargeting(packageId: string, tenantId: number): Promise<CustomerSearchResult[]> {
    const response = await fetch(`/api/packages/${packageId}/individual-customer-targeting?tenantId=${tenantId}`);
    if (!response.ok) {
      throw new Error('Failed to get individual customer targeting');
    }

    const data = await response.json();
    return data.data || [];
  },

  async getPackageTargetingStats(packageId: string, tenantId: number): Promise<PackageCustomerTargetingStats> {
    const response = await fetch(`/api/packages/${packageId}/targeting-stats?tenantId=${tenantId}`);
    if (!response.ok) {
      throw new Error('Failed to get package targeting statistics');
    }

    const data = await response.json();
    return data.data;
  },
};

/**
 * Search customers with filters and pagination
 */
export function useCustomerSearch(filters: CustomerSearchFilters, options?: { enabled?: boolean }) {
  return useQuery({
    queryKey: individualCustomerKeys.search(filters),
    queryFn: () => individualCustomerApi.searchCustomers(filters),
    enabled: options?.enabled !== false && !!filters.tenantId && filters.tenantId > 0,
    staleTime: 2 * 60 * 1000, // 2 minutes (shorter for search results)
    keepPreviousData: true, // Keep previous results while loading new ones
  });
}

/**
 * Get customers by IDs (for displaying selected customers)
 */
export function useCustomersByIds(customerIds: string[], tenantId: number, options?: { enabled?: boolean }) {
  return useQuery({
    queryKey: individualCustomerKeys.byIds(customerIds, tenantId),
    queryFn: () => individualCustomerApi.getCustomersByIds(customerIds, tenantId),
    enabled: options?.enabled !== false && !!tenantId && tenantId > 0 && customerIds.length > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Get individual customer targeting for a package
 */
export function useIndividualCustomerTargeting(packageId: string, tenantId: number, options?: { enabled?: boolean }) {
  return useQuery({
    queryKey: individualCustomerKeys.packageTargeting(packageId, tenantId),
    queryFn: () => individualCustomerApi.getIndividualCustomerTargeting(packageId, tenantId),
    enabled: options?.enabled !== false && !!packageId && !!tenantId && tenantId > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Get package targeting statistics
 */
export function usePackageTargetingStats(packageId: string, tenantId: number, options?: { enabled?: boolean }) {
  return useQuery({
    queryKey: individualCustomerKeys.targetingStats(packageId, tenantId),
    queryFn: () => individualCustomerApi.getPackageTargetingStats(packageId, tenantId),
    enabled: options?.enabled !== false && !!packageId && !!tenantId && tenantId > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

/**
 * Save individual customer targeting mutation
 */
export function useSaveIndividualCustomerTargeting() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: individualCustomerApi.saveIndividualCustomerTargeting,
    onSuccess: (_, variables) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ 
        queryKey: individualCustomerKeys.packageTargeting(variables.packageId, variables.tenantId) 
      });
      queryClient.invalidateQueries({ 
        queryKey: individualCustomerKeys.targetingStats(variables.packageId, variables.tenantId) 
      });
    },
  });
}

/**
 * Hook for customer search with debouncing
 */
export function useCustomerSearchWithDebounce(
  baseFilters: Omit<CustomerSearchFilters, 'search'>, 
  searchTerm: string,
  debounceMs: number = 300
) {
  const [debouncedSearchTerm, setDebouncedSearchTerm] = React.useState(searchTerm);

  // Debounce search term
  React.useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, debounceMs);

    return () => clearTimeout(timer);
  }, [searchTerm, debounceMs]);

  const filters: CustomerSearchFilters = {
    ...baseFilters,
    search: debouncedSearchTerm,
  };

  return useCustomerSearch(filters, {
    enabled: !!baseFilters.tenantId && baseFilters.tenantId > 0,
  });
}

/**
 * Hook for infinite customer search (pagination)
 */
export function useInfiniteCustomerSearch(baseFilters: CustomerSearchFilters) {
  const [allCustomers, setAllCustomers] = React.useState<CustomerSearchResult[]>([]);
  const [hasMore, setHasMore] = React.useState(true);
  const [isLoading, setIsLoading] = React.useState(false);

  const loadMore = React.useCallback(async () => {
    if (isLoading || !hasMore) return;

    setIsLoading(true);
    try {
      const filters: CustomerSearchFilters = {
        ...baseFilters,
        offset: allCustomers.length,
        limit: 20,
      };

      const result = await individualCustomerApi.searchCustomers(filters);
      
      setAllCustomers(prev => [...prev, ...result.customers]);
      setHasMore(result.hasMore);
    } catch (error) {
      console.error('Failed to load more customers:', error);
    } finally {
      setIsLoading(false);
    }
  }, [baseFilters, allCustomers.length, hasMore, isLoading]);

  const reset = React.useCallback(() => {
    setAllCustomers([]);
    setHasMore(true);
  }, []);

  // Load initial data
  React.useEffect(() => {
    reset();
    loadMore();
  }, [baseFilters.tenantId, baseFilters.search, baseFilters.isActive, baseFilters.pricingGroupId, baseFilters.locationId]);

  return {
    customers: allCustomers,
    hasMore,
    isLoading,
    loadMore,
    reset,
  };
}

// Export types for use in components
export type {
  CustomerSearchResult,
  CustomerSearchFilters,
  IndividualCustomerTargeting,
  PackageCustomerTargetingStats,
};

// Export query keys for advanced usage
export { individualCustomerKeys };
