"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { motion } from "framer-motion";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, AlertCircle, CheckCircle, Package, MapPin, Hash, Tag } from "lucide-react";
import { EquipmentInstanceWithRelations } from "@/lib/services/equipment-instances.service";
import { useEquipments } from "@/lib/hooks/queries/use-equipment-queries";
import { useLocations } from "@/lib/hooks/queries/use-location-queries";

// Form validation schema
const equipmentInstanceSchema = z.object({
  equipmentId: z.string().min(1, "Equipment is required"),
  locationId: z.string().min(1, "Location is required"),
  quantity: z.number().int().min(1, "Quantity must be at least 1"),
  displayName: z.string().max(255, "Display name must be less than 255 characters").optional(),
});

type EquipmentInstanceFormData = z.infer<typeof equipmentInstanceSchema>;

interface EquipmentInstanceFormProps {
  instance?: EquipmentInstanceWithRelations;
  onSubmit: (data: EquipmentInstanceFormData) => Promise<void>;
  onCancel: () => void;
  className?: string;
}

export function EquipmentInstanceForm({
  instance,
  onSubmit,
  onCancel,
  className = "",
}: EquipmentInstanceFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [showSuccess, setShowSuccess] = useState(false);

  const { data: equipments = [], isLoading: equipmentsLoading } = useEquipments();
  const { data: locations = [], isLoading: locationsLoading } = useLocations();

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isValid },
  } = useForm<EquipmentInstanceFormData>({
    resolver: zodResolver(equipmentInstanceSchema),
    defaultValues: {
      equipmentId: instance?.equipmentId || "",
      locationId: instance?.locationId || "",
      quantity: instance?.quantity || 1,
      displayName: instance?.displayName || "",
    },
    mode: "onChange",
  });

  const watchedEquipmentId = watch("equipmentId");
  const watchedLocationId = watch("locationId");

  const handleFormSubmit = async (data: EquipmentInstanceFormData) => {
    try {
      setIsSubmitting(true);
      setSubmitError(null);
      
      await onSubmit(data);
      
      setShowSuccess(true);
      setTimeout(() => setShowSuccess(false), 2000);
    } catch (error) {
      setSubmitError(error instanceof Error ? error.message : "An error occurred");
    } finally {
      setIsSubmitting(false);
    }
  };

  const selectedEquipment = equipments.find(eq => eq.id === watchedEquipmentId);
  const selectedLocation = locations.find(loc => loc.id === watchedLocationId);

  return (
    <Card className={`w-full max-w-2xl mx-auto ${className}`}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Package className="h-5 w-5" />
          {instance ? "Edit Equipment Instance" : "Add Equipment Instance"}
        </CardTitle>
        <CardDescription>
          {instance 
            ? "Update the equipment instance details below."
            : "Create a new equipment instance by assigning equipment to a location."
          }
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {submitError && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{submitError}</AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          {/* Equipment Selection */}
          <div className="space-y-2">
            <Label htmlFor="equipmentId" className="flex items-center gap-2">
              <Package className="h-4 w-4" />
              Equipment *
            </Label>
            <Select
              value={watchedEquipmentId}
              onValueChange={(value) => setValue("equipmentId", value, { shouldValidate: true })}
              disabled={equipmentsLoading}
            >
              <SelectTrigger>
                <SelectValue placeholder={equipmentsLoading ? "Loading equipment..." : "Select equipment"} />
              </SelectTrigger>
              <SelectContent>
                {equipments.map((equipment) => (
                  <SelectItem key={equipment.id} value={equipment.id}>
                    <div className="flex items-center gap-2">
                      <Package className="h-4 w-4" />
                      <span>{equipment.name}</span>
                      {equipment.default_display_name && (
                        <span className="text-sm text-gray-500">
                          ({equipment.default_display_name})
                        </span>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.equipmentId && (
              <p className="text-sm text-red-600">{errors.equipmentId.message}</p>
            )}
            {selectedEquipment && (
              <div className="text-sm text-gray-600 bg-gray-50 p-2 rounded">
                Selected: {selectedEquipment.name}
                {selectedEquipment.default_display_name && (
                  <span className="ml-1">({selectedEquipment.default_display_name})</span>
                )}
              </div>
            )}
          </div>

          {/* Location Selection */}
          <div className="space-y-2">
            <Label htmlFor="locationId" className="flex items-center gap-2">
              <MapPin className="h-4 w-4" />
              Location *
            </Label>
            <Select
              value={watchedLocationId}
              onValueChange={(value) => setValue("locationId", value, { shouldValidate: true })}
              disabled={locationsLoading}
            >
              <SelectTrigger>
                <SelectValue placeholder={locationsLoading ? "Loading locations..." : "Select location"} />
              </SelectTrigger>
              <SelectContent>
                {locations.map((location) => (
                  <SelectItem key={location.id} value={location.id}>
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4" />
                      <span>{location.addressLine1}</span>
                      {location.city && (
                        <span className="text-sm text-gray-500">
                          - {location.city}
                        </span>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.locationId && (
              <p className="text-sm text-red-600">{errors.locationId.message}</p>
            )}
            {selectedLocation && (
              <div className="text-sm text-gray-600 bg-gray-50 p-2 rounded">
                Selected: {selectedLocation.addressLine1}
                {selectedLocation.city && `, ${selectedLocation.city}`}
                {selectedLocation.state && `, ${selectedLocation.state}`}
              </div>
            )}
          </div>

          {/* Quantity */}
          <div className="space-y-2">
            <Label htmlFor="quantity" className="flex items-center gap-2">
              <Hash className="h-4 w-4" />
              Quantity *
            </Label>
            <Input
              id="quantity"
              type="number"
              min="1"
              {...register("quantity", { valueAsNumber: true })}
              placeholder="Enter quantity"
              className="w-full"
            />
            {errors.quantity && (
              <p className="text-sm text-red-600">{errors.quantity.message}</p>
            )}
          </div>

          {/* Display Name */}
          <div className="space-y-2">
            <Label htmlFor="displayName" className="flex items-center gap-2">
              <Tag className="h-4 w-4" />
              Display Name (Optional)
            </Label>
            <Input
              id="displayName"
              {...register("displayName")}
              placeholder="Custom display name for this instance"
              className="w-full"
            />
            {errors.displayName && (
              <p className="text-sm text-red-600">{errors.displayName.message}</p>
            )}
            <p className="text-sm text-gray-500">
              Leave empty to use the equipment's default display name
            </p>
          </div>

          {/* Form Actions */}
          <div className="flex items-center gap-3 pt-6 border-t">
            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="flex-1"
            >
              <Button
                type="submit"
                disabled={!isValid || isSubmitting || equipmentsLoading || locationsLoading}
                className="w-full"
              >
                <motion.div
                  initial={false}
                  animate={{
                    scale: showSuccess ? [1, 1.1, 1] : 1,
                    backgroundColor: showSuccess ? "#10b981" : undefined,
                  }}
                  transition={{ duration: 0.3 }}
                  className="flex items-center gap-2"
                >
                  {isSubmitting ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : showSuccess ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    <Package className="h-4 w-4" />
                  )}
                  {isSubmitting
                    ? "Creating..."
                    : showSuccess
                    ? "Success!"
                    : instance
                    ? "Update Instance"
                    : "Create Instance"
                  }
                </motion.div>
              </Button>
            </motion.div>

            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isSubmitting}
              className="px-6"
            >
              Cancel
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
