import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { PricingGroupService } from "@/lib/services/pricing-groups.service";

const pricingGroupService = new PricingGroupService();

// GET /api/pricing-groups/stats - Get pricing group statistics
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const tenantIdParam = searchParams.get("tenantId");

    let tenantId: number | undefined;
    if (tenantIdParam) {
      const tenantIdNum = parseInt(tenantIdParam);
      if (isNaN(tenantIdNum)) {
        return NextResponse.json(
          { error: "Invalid tenant ID" },
          { status: 400 }
        );
      }
      tenantId = tenantIdNum;
    }

    const stats = await pricingGroupService.getStats(tenantId);

    return NextResponse.json(
      { data: stats },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error fetching pricing group stats:", error);
    return NextResponse.json(
      { error: "Failed to fetch pricing group stats" },
      { status: 500 }
    );
  }
}
