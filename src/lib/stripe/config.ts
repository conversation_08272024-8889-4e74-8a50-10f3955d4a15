import Stripe from "stripe";

// Server-side Stripe instance
let stripeInstance: Stripe | null = null;

export function getStripe(): Stripe {
  if (!stripeInstance) {
    if (!process.env.STRIPE_SECRET_KEY) {
      throw new Error("STRIPE_SECRET_KEY is not defined");
    }

    stripeInstance = new Stripe(process.env.STRIPE_SECRET_KEY, {
      apiVersion: "2025-05-28.basil",
      typescript: true,
    });
  }

  return stripeInstance;
}

// For backward compatibility (only use on server-side)
export const stripe = typeof window === 'undefined' ? getStripe() : null as any;

// Stripe configuration
export const stripeConfig = {
  publishableKey: process.env.STRIPE_PUBLISHABLE_KEY!,
  webhookSecret: process.env.STRIPE_WEBHOOK_SECRET!,
  currency: "usd",
  
  // Credit packages configuration
  creditPackages: [
    {
      id: "starter",
      name: "Starter Pack",
      description: "Perfect for getting started",
      credits: 100,
      price: 999, // $9.99 in cents
      stripePriceId: process.env.STRIPE_PRICE_STARTER || "price_starter",
      popular: false,
    },
    {
      id: "pro",
      name: "Pro Pack",
      description: "For power users",
      credits: 500,
      price: 3999, // $39.99 in cents
      stripePriceId: process.env.STRIPE_PRICE_PRO || "price_pro",
      popular: true,
    },
    {
      id: "enterprise",
      name: "Enterprise Pack",
      description: "For large teams",
      credits: 2000,
      price: 12999, // $129.99 in cents
      stripePriceId: process.env.STRIPE_PRICE_ENTERPRISE || "price_enterprise",
      popular: false,
    },
    {
      id: "mega",
      name: "Mega Pack",
      description: "Maximum value",
      credits: 5000,
      price: 24999, // $249.99 in cents
      stripePriceId: process.env.STRIPE_PRICE_MEGA || "price_mega",
      popular: false,
    },
  ],
};

// Helper function to format price
export function formatPrice(priceInCents: number): string {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: stripeConfig.currency,
  }).format(priceInCents / 100);
}

// Helper function to format credits
export function formatCredits(credits: number): string {
  return new Intl.NumberFormat("en-US").format(credits);
}

// Calculate price per credit
export function calculatePricePerCredit(priceInCents: number, credits: number): string {
  const pricePerCredit = priceInCents / credits / 100;
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: stripeConfig.currency,
    minimumFractionDigits: 3,
    maximumFractionDigits: 3,
  }).format(pricePerCredit);
}

// Get credit package by ID
export function getCreditPackage(id: string) {
  return stripeConfig.creditPackages.find(pkg => pkg.id === id);
}

// Get credit package by Stripe price ID
export function getCreditPackageByPriceId(priceId: string) {
  return stripeConfig.creditPackages.find(pkg => pkg.stripePriceId === priceId);
}
