import { db } from "@/lib/db";
import { user_location_access, users, locations, type UserLocationAccess, type NewUserLocationAccess } from "@/lib/db/schema";
import { eq, and, desc, sql } from "drizzle-orm";
import { createId } from "@paralleldrive/cuid2";

/**
 * Location Access Service
 * 
 * Service untuk manage location-based access control dalam RBAC system.
 * Mengikuti pola yang sama dengan RoleService dan PermissionService.
 */

// Interface untuk search results
export interface LocationAccessSearchResult {
  locationAccess: (UserLocationAccess & { 
    user: { name: string | null; email: string };
    location: { name: string };
  })[];
  total: number;
  hasMore: boolean;
}

/**
 * LocationAccessService - Service untuk manage location access
 */
export class LocationAccessService {
  /**
   * Search location access dengan filtering dan pagination
   */
  static async searchLocationAccess(
    tenantId: number,
    userId?: string,
    locationId?: string,
    limit = 20,
    offset = 0
  ): Promise<LocationAccessSearchResult> {
    try {
      // Build where conditions
      let whereConditions: any = and(
        eq(user_location_access.tenantId, tenantId),
        eq(user_location_access.is_active, true)
      );

      if (userId) {
        whereConditions = and(whereConditions, eq(user_location_access.userId, userId));
      }

      if (locationId) {
        whereConditions = and(whereConditions, eq(user_location_access.locationId, locationId));
      }

      // Query dengan join ke users dan locations table
      const accessResults = await db
        .select({
          id: user_location_access.id,
          userId: user_location_access.userId,
          locationId: user_location_access.locationId,
          tenantId: user_location_access.tenantId,
          access_level: user_location_access.access_level,
          assignedBy: user_location_access.assignedBy,
          assignedAt: user_location_access.assignedAt,
          is_active: user_location_access.is_active,
          createdAt: user_location_access.createdAt,
          updatedAt: user_location_access.updatedAt,
          user: {
            name: users.name,
            email: users.email,
          },
          location: {
            name: locations.name,
          },
        })
        .from(user_location_access)
        .innerJoin(users, eq(user_location_access.userId, users.id))
        .innerJoin(locations, eq(user_location_access.locationId, locations.id))
        .where(whereConditions)
        .orderBy(desc(user_location_access.assignedAt))
        .limit(limit + 1)
        .offset(offset);

      // Cek apakah ada data lebih
      const hasMore = accessResults.length > limit;
      if (hasMore) {
        accessResults.pop();
      }

      // Count total
      const totalResult = await db
        .select({ count: sql<number>`count(*)` })
        .from(user_location_access)
        .innerJoin(users, eq(user_location_access.userId, users.id))
        .innerJoin(locations, eq(user_location_access.locationId, locations.id))
        .where(whereConditions);

      const total = Number(totalResult[0]?.count || 0);

      return {
        locationAccess: accessResults,
        total,
        hasMore,
      };
    } catch (error) {
      console.error("Error searching location access:", error);
      throw error;
    }
  }

  /**
   * Grant location access to user
   */
  static async grantAccess(data: {
    userId: string;
    locationId: string;
    tenantId: number;
    access_level: "full" | "read_only" | "restricted";
    assignedBy?: string;
  }): Promise<UserLocationAccess> {
    try {
      // Check if access already exists
      const existing = await db
        .select()
        .from(user_location_access)
        .where(
          and(
            eq(user_location_access.userId, data.userId),
            eq(user_location_access.locationId, data.locationId)
          )
        )
        .limit(1);

      if (existing.length > 0) {
        // Update existing access
        const result = await db
          .update(user_location_access)
          .set({
            access_level: data.access_level,
            is_active: true,
            assignedBy: data.assignedBy || null,
            assignedAt: new Date(),
            updatedAt: new Date(),
          })
          .where(eq(user_location_access.id, existing[0].id))
          .returning();

        return result[0];
      }

      // Create new access
      const accessData: NewUserLocationAccess = {
        id: createId(),
        userId: data.userId,
        locationId: data.locationId,
        tenantId: data.tenantId,
        access_level: data.access_level,
        assignedBy: data.assignedBy || null,
        is_active: true,
      };

      const result = await db.insert(user_location_access).values(accessData).returning();
      return result[0];
    } catch (error) {
      console.error("Error granting location access:", error);
      throw error;
    }
  }

  /**
   * Revoke location access from user
   */
  static async revokeAccess(userId: string, locationId: string): Promise<void> {
    try {
      await db
        .update(user_location_access)
        .set({
          is_active: false,
          updatedAt: new Date(),
        })
        .where(
          and(
            eq(user_location_access.userId, userId),
            eq(user_location_access.locationId, locationId)
          )
        );
    } catch (error) {
      console.error("Error revoking location access:", error);
      throw error;
    }
  }

  /**
   * Get user's location access
   */
  static async getUserLocationAccess(userId: string, tenantId: number): Promise<UserLocationAccess[]> {
    try {
      return await db
        .select()
        .from(user_location_access)
        .where(
          and(
            eq(user_location_access.userId, userId),
            eq(user_location_access.tenantId, tenantId),
            eq(user_location_access.is_active, true)
          )
        )
        .orderBy(user_location_access.assignedAt);
    } catch (error) {
      console.error("Error getting user location access:", error);
      throw error;
    }
  }

  /**
   * Check if user has access to specific location
   */
  static async userHasLocationAccess(
    userId: string,
    locationId: string,
    requiredLevel: "full" | "read_only" | "restricted" = "read_only"
  ): Promise<boolean> {
    try {
      const access = await db
        .select()
        .from(user_location_access)
        .where(
          and(
            eq(user_location_access.userId, userId),
            eq(user_location_access.locationId, locationId),
            eq(user_location_access.is_active, true)
          )
        )
        .limit(1);

      if (access.length === 0) {
        return false;
      }

      const userAccess = access[0];
      
      // Check access level hierarchy: full > read_only > restricted
      const accessLevels = {
        "restricted": 1,
        "read_only": 2,
        "full": 3,
      };

      return accessLevels[userAccess.access_level] >= accessLevels[requiredLevel];
    } catch (error) {
      console.error("Error checking user location access:", error);
      return false;
    }
  }

  /**
   * Get locations accessible by user
   */
  static async getUserAccessibleLocations(userId: string, tenantId: number): Promise<{
    locationId: string;
    locationName: string;
    access_level: string;
  }[]> {
    try {
      const result = await db
        .select({
          locationId: user_location_access.locationId,
          locationName: locations.name,
          access_level: user_location_access.access_level,
        })
        .from(user_location_access)
        .innerJoin(locations, eq(user_location_access.locationId, locations.id))
        .where(
          and(
            eq(user_location_access.userId, userId),
            eq(user_location_access.tenantId, tenantId),
            eq(user_location_access.is_active, true)
          )
        )
        .orderBy(locations.name);

      return result;
    } catch (error) {
      console.error("Error getting user accessible locations:", error);
      throw error;
    }
  }

  /**
   * Bulk grant access to multiple locations
   */
  static async bulkGrantAccess(data: {
    userId: string;
    locationIds: string[];
    tenantId: number;
    access_level: "full" | "read_only" | "restricted";
    assignedBy?: string;
  }): Promise<UserLocationAccess[]> {
    try {
      const results: UserLocationAccess[] = [];

      for (const locationId of data.locationIds) {
        const access = await this.grantAccess({
          userId: data.userId,
          locationId,
          tenantId: data.tenantId,
          access_level: data.access_level,
          assignedBy: data.assignedBy,
        });
        results.push(access);
      }

      return results;
    } catch (error) {
      console.error("Error bulk granting location access:", error);
      throw error;
    }
  }

  /**
   * Bulk revoke access from multiple locations
   */
  static async bulkRevokeAccess(userId: string, locationIds: string[]): Promise<void> {
    try {
      await db
        .update(user_location_access)
        .set({
          is_active: false,
          updatedAt: new Date(),
        })
        .where(
          and(
            eq(user_location_access.userId, userId),
            sql`${user_location_access.locationId} = ANY(${locationIds})`
          )
        );
    } catch (error) {
      console.error("Error bulk revoking location access:", error);
      throw error;
    }
  }
}
