#!/bin/bash

# FAANG-level deployment script
set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
ENVIRONMENT=${1:-production}
COMPOSE_PROJECT_NAME="nextjs-saas"

# Functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if Docker is running
    if ! docker info >/dev/null 2>&1; then
        log_error "Docker is not running"
        exit 1
    fi
    
    # Check if docker-compose is available
    if ! command -v docker-compose >/dev/null 2>&1; then
        log_error "docker-compose is not installed"
        exit 1
    fi
    
    # Check if environment file exists
    if [[ "$ENVIRONMENT" == "production" && ! -f ".env.production" ]]; then
        log_error ".env.production file not found"
        exit 1
    elif [[ "$ENVIRONMENT" == "development" && ! -f ".env.local" ]]; then
        log_warn ".env.local file not found, using defaults"
    fi
}

deploy_development() {
    log_info "Deploying development environment..."
    
    # Create external network if it doesn't exist
    docker network create postgres-network 2>/dev/null || true
    
    # Start development stack
    docker-compose \
        -f docker-compose.yml \
        -f docker-compose.dev.yml \
        --env-file .env.local \
        -p "${COMPOSE_PROJECT_NAME}-dev" \
        up --build -d
    
    log_info "Development environment deployed successfully!"
    log_info "Application: http://localhost:3000"
    log_info "PgAdmin: http://localhost:5050"
    log_info "Redis: localhost:6379"
}

deploy_production() {
    log_info "Deploying production environment..."
    
    # Create external network if it doesn't exist
    docker network create postgres-network 2>/dev/null || true
    
    # Build and deploy production stack
    docker-compose \
        -f docker-compose.yml \
        -f docker-compose.prod.yml \
        --env-file .env.production \
        -p "${COMPOSE_PROJECT_NAME}-prod" \
        up --build -d
    
    # Wait for services to be healthy
    log_info "Waiting for services to be healthy..."
    sleep 30
    
    # Check health
    if docker-compose \
        -f docker-compose.yml \
        -f docker-compose.prod.yml \
        -p "${COMPOSE_PROJECT_NAME}-prod" \
        ps | grep -q "unhealthy"; then
        log_error "Some services are unhealthy"
        exit 1
    fi
    
    log_info "Production environment deployed successfully!"
    log_info "Application: http://localhost"
    log_info "Monitoring: http://localhost:9090"
}

cleanup() {
    log_info "Cleaning up old containers and images..."
    docker system prune -f
    docker image prune -f
}

# Main execution
main() {
    log_info "Starting deployment for environment: $ENVIRONMENT"
    
    check_prerequisites
    
    case "$ENVIRONMENT" in
        "development"|"dev")
            deploy_development
            ;;
        "production"|"prod")
            deploy_production
            ;;
        *)
            log_error "Invalid environment: $ENVIRONMENT"
            log_info "Usage: $0 [development|production]"
            exit 1
            ;;
    esac
    
    cleanup
    log_info "Deployment completed successfully!"
}

# Run main function
main "$@"