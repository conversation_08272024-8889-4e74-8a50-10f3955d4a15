-- ALTER TABLE "users" DROP CONSTRAINT "users_location_id_tenants_id_fk";
-- --> statement-breakpoint
-- ALTER TABLE "equipment_instances" ALTER COLUMN "equipment_id" SET DATA TYPE varchar(255);--> statement-breakpoint
-- ALTER TABLE "equipment_instances" ALTER COLUMN "location_id" SET DATA TYPE varchar(255);--> statement-breakpoint
-- ALTER TABLE "users" ALTER COLUMN "location_id" SET DATA TYPE varchar(255);--> statement-breakpoint
-- ALTER TABLE "users" ADD CONSTRAINT "users_location_id_locations_id_fk" FOREIGN KEY ("location_id") REFERENCES "public"."locations"("id") ON DELETE no action ON UPDATE no action;