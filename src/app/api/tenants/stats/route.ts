import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { db } from "@/lib/db";
import { tenants, users, business_profiles } from "@/lib/db/schema";
import { eq, count, isNotNull } from "drizzle-orm";

// GET /api/tenants/stats - Get tenant statistics
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Only admins can view tenant stats
    if (session.user.role !== "admin") {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Get total tenants
    const [totalTenantsResult] = await db
      .select({ count: count() })
      .from(tenants);

    // Get active tenants (assuming all are active for now)
    const [activeTenantsResult] = await db
      .select({ count: count() })
      .from(tenants);

    // Get inactive tenants (0 for now)
    const inactiveTenants = 0;

    // Get total users across all tenants
    const [totalUsersResult] = await db
      .select({ count: count() })
      .from(users)
      .where(isNotNull(users.tenantId));

    // Get total business profiles
    const [totalBusinessProfilesResult] = await db
      .select({ count: count() })
      .from(business_profiles);

    const stats = {
      totalTenants: totalTenantsResult.count,
      activeTenants: activeTenantsResult.count,
      inactiveTenants,
      totalUsers: totalUsersResult.count,
      totalBusinessProfiles: totalBusinessProfilesResult.count,
    };

    return NextResponse.json(stats);
  } catch (error) {
    console.error("Error fetching tenant stats:", error);
    return NextResponse.json(
      { error: "Failed to fetch tenant stats" },
      { status: 500 }
    );
  }
}
