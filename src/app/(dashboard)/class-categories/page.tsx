import { Metadata } from "next";
import { ClassCategoriesManagement } from "@/components/class-categories/class-categories-management";

/**
 * Class Categories Page
 * 
 * Halaman utama untuk manage class categories. Ini adalah entry point
 * yang user akses dari navigation menu.
 */

export const metadata: Metadata = {
  title: "Class Categories | Dashboard",
  description: "Manage class categories to organize your classes effectively",
};

/**
 * Class Categories Page Component
 * 
 * Page ini render ClassCategoriesManagement component dengan
 * proper layout dan metadata.
 */
export default function ClassCategoriesPage() {
  // TODO: Get tenantId from session/context
  // Untuk sekarang hardcode ke 1, nanti bisa diambil dari session
  const tenantId = 1;

  return (
    <div className="container mx-auto py-6">
      <ClassCategoriesManagement tenantId={tenantId} />
    </div>
  );
}
