// Migration script to add items_to_bring JSONB field to classes table
// and migrate existing class_items_to_bring data
// Run with: npx tsx scripts/migrate-class-items-to-bring.ts

import { db } from "../src/lib/db";
import { classes, class_items_to_bring } from "../src/lib/db/schema";
import { eq } from "drizzle-orm";
import { sql } from "drizzle-orm";

async function migrateClassItemsToBring() {
  try {
    console.log("🚀 Starting migration: Add items_to_bring JSONB field to classes table");

    // Step 1: Add the new JSONB column to classes table
    console.log("📝 Adding items_to_bring column to classes table...");
    
    await db.execute(sql`
      ALTER TABLE classes 
      ADD COLUMN IF NOT EXISTS items_to_bring JSONB DEFAULT '[]'::jsonb
    `);

    console.log("✅ Column added successfully");

    // Step 2: Migrate existing data from class_items_to_bring to classes.items_to_bring
    console.log("📦 Migrating existing class_items_to_bring data...");

    // Get all existing class items to bring grouped by class_id
    const existingItems = await db
      .select()
      .from(class_items_to_bring);

    console.log(`📋 Found ${existingItems.length} existing items to migrate`);

    // Group items by class_id
    const itemsByClass = new Map<string, any[]>();
    
    for (const item of existingItems) {
      if (!itemsByClass.has(item.class_id)) {
        itemsByClass.set(item.class_id, []);
      }
      
      itemsByClass.get(item.class_id)!.push({
        id: item.id,
        item_name: item.item_name,
        is_required: item.is_required
      });
    }

    console.log(`🔄 Updating ${itemsByClass.size} classes with their items...`);

    // Update each class with its items
    let updatedCount = 0;
    for (const [classId, items] of itemsByClass) {
      try {
        await db
          .update(classes)
          .set({ 
            items_to_bring: items,
            updatedAt: new Date()
          })
          .where(eq(classes.id, classId));
        
        updatedCount++;
        console.log(`✅ Updated class ${classId} with ${items.length} items`);
      } catch (error) {
        console.error(`❌ Failed to update class ${classId}:`, error);
      }
    }

    console.log(`🎉 Migration completed! Updated ${updatedCount} classes`);

    // Step 3: Verify migration
    console.log("🔍 Verifying migration...");
    
    const classesWithItems = await db
      .select({
        id: classes.id,
        name: classes.name,
        items_to_bring: classes.items_to_bring
      })
      .from(classes)
      .where(sql`jsonb_array_length(items_to_bring) > 0`);

    console.log(`✅ Verification: ${classesWithItems.length} classes have items_to_bring data`);

    // Show sample data
    if (classesWithItems.length > 0) {
      console.log("\n📋 Sample migrated data:");
      classesWithItems.slice(0, 3).forEach((cls, index) => {
        console.log(`${index + 1}. Class: ${cls.name}`);
        console.log(`   Items: ${JSON.stringify(cls.items_to_bring, null, 2)}`);
      });
    }

    console.log("\n⚠️  IMPORTANT: After verifying the migration is successful,");
    console.log("   you can optionally drop the old class_items_to_bring table:");
    console.log("   DROP TABLE class_items_to_bring;");
    console.log("\n   But keep it for now as backup until you're 100% sure migration worked correctly.");

  } catch (error) {
    console.error("💥 Migration failed:", error);
    throw error;
  }
}

// Run the migration
migrateClassItemsToBring()
  .then(() => {
    console.log("✅ Migration script completed successfully");
    process.exit(0);
  })
  .catch((error) => {
    console.error("💥 Migration script failed:", error);
    process.exit(1);
  });
