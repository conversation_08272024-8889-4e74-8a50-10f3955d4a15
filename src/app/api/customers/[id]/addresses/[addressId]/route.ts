import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { CustomerAddressService } from "@/lib/services/customer-address.service";
import { z } from "zod";

// Validation schema for updating customer address
const updateCustomerAddressSchema = z.object({
  addressLine1: z.string().max(255).optional(),
  addressLine2: z.string().max(255).optional(),
  city: z.string().max(255).optional(),
  state: z.string().max(255).optional(),
  zip: z.string().max(255).optional(),
  country: z.string().max(255).optional(),
  is_primary: z.boolean().optional(),
});

// GET /api/customers/[id]/addresses/[addressId] - Get single customer address
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; addressId: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { addressId } = await params;
    if (!addressId) {
      return NextResponse.json(
        { error: "Address ID is required" },
        { status: 400 }
      );
    }

    const address = await CustomerAddressService.getById(addressId);

    if (!address) {
      return NextResponse.json(
        { error: "Customer address not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({ data: address });
  } catch (error) {
    console.error("Error fetching customer address:", error);
    return NextResponse.json(
      { error: "Failed to fetch customer address" },
      { status: 500 }
    );
  }
}

// PUT /api/customers/[id]/addresses/[addressId] - Update customer address
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; addressId: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { addressId } = await params;
    if (!addressId) {
      return NextResponse.json(
        { error: "Address ID is required" },
        { status: 400 }
      );
    }

    const body = await request.json();
    const validatedData = updateCustomerAddressSchema.parse(body);

    const updatedAddress = await CustomerAddressService.update(addressId, validatedData);

    if (!updatedAddress) {
      return NextResponse.json(
        { error: "Customer address not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { 
        message: "Customer address updated successfully", 
        data: updatedAddress 
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error updating customer address:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to update customer address" },
      { status: 500 }
    );
  }
}

// DELETE /api/customers/[id]/addresses/[addressId] - Delete customer address
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; addressId: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { addressId } = await params;
    if (!addressId) {
      return NextResponse.json(
        { error: "Address ID is required" },
        { status: 400 }
      );
    }

    const deleted = await CustomerAddressService.delete(addressId);

    if (!deleted) {
      return NextResponse.json(
        { error: "Customer address not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { message: "Customer address deleted successfully" },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error deleting customer address:", error);
    return NextResponse.json(
      { error: "Failed to delete customer address" },
      { status: 500 }
    );
  }
}

// PATCH /api/customers/[id]/addresses/[addressId] - Set as primary or other actions
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string; addressId: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { addressId } = await params;
    if (!addressId) {
      return NextResponse.json(
        { error: "Address ID is required" },
        { status: 400 }
      );
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get("action");

    let updatedAddress;
    let message;

    switch (action) {
      case "set-primary":
        updatedAddress = await CustomerAddressService.setPrimary(addressId);
        message = "Address set as primary successfully";
        break;
      default:
        return NextResponse.json(
          { error: "Invalid action. Use 'set-primary'" },
          { status: 400 }
        );
    }

    if (!updatedAddress) {
      return NextResponse.json(
        { error: "Customer address not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(
      {
        message,
        data: updatedAddress
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error updating customer address:", error);
    return NextResponse.json(
      { error: "Failed to update customer address" },
      { status: 500 }
    );
  }
}
