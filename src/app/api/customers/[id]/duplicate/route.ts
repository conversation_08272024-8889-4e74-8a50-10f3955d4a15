import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { z } from "zod";
import { CustomerService } from "@/lib/services/customer.service";

const duplicateSchema = z.object({
  email: z.string().email().optional(),
});

const customerService = new CustomerService();

// POST /api/customers/[id]/duplicate - Duplicate customer
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id: customerId } = await params;
    if (!customerId) {
      return NextResponse.json(
        { error: "Customer ID is required" },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { email } = duplicateSchema.parse(body);

    const duplicatedCustomer = await customerService.duplicate(customerId, email);

    return NextResponse.json(
      {
        message: "Customer duplicated successfully",
        data: duplicatedCustomer
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error duplicating customer:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid data", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to duplicate customer" },
      { status: 500 }
    );
  }
}
