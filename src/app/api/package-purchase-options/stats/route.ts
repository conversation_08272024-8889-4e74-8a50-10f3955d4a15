import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { packagePurchaseOptionsService } from "@/lib/services/package-purchase-options.service";

/**
 * GET /api/package-purchase-options/stats
 * Get package purchase options statistics
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    console.log(`📦 [GET /api/package-purchase-options/stats] Fetching package purchase options statistics`);

    const stats = await packagePurchaseOptionsService.getStats();

    return NextResponse.json({
      success: true,
      data: stats
    });
  } catch (error) {
    console.error("📦 [GET /api/package-purchase-options/stats] Error:", error);
    return NextResponse.json(
      { error: "Failed to fetch package purchase options statistics" },
      { status: 500 }
    );
  }
}
