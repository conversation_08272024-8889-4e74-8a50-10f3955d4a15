"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  useCreatePermission,
  useUpdatePermission,
  type PermissionFormData,
} from "@/lib/hooks/queries/use-permission-queries";
import { type Permission } from "@/lib/db/schema";

// Form validation schema
const permissionFormSchema = z.object({
  module: z.string().min(1, "Module is required"),
  action: z.string().min(1, "Action is required"),
  display_name: z.string().min(1, "Display name is required"),
  description: z.string().optional(),
  is_system_permission: z.boolean().default(false),
});

type PermissionFormValues = z.infer<typeof permissionFormSchema>;

interface PermissionFormProps {
  permission?: Permission;
  onSubmit: () => void;
  onCancel: () => void;
}

// Common modules and actions
const commonModules = [
  "system", "tenant", "users", "roles", "classes", "class-schedules",
  "customers", "packages", "locations", "equipment", "facilities", 
  "bookings", "reports", "admin"
];

const commonActions = [
  "create", "read", "update", "delete", "manage", "assign", "revoke", "access", "export"
];

export function PermissionForm({ permission, onSubmit, onCancel }: PermissionFormProps) {
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [customModule, setCustomModule] = useState(false);
  const [customAction, setCustomAction] = useState(false);

  // Mutations
  const createMutation = useCreatePermission();
  const updateMutation = useUpdatePermission();

  // Form setup
  const form = useForm<PermissionFormValues>({
    resolver: zodResolver(permissionFormSchema),
    defaultValues: {
      module: permission?.module || "",
      action: permission?.action || "",
      display_name: permission?.display_name || "",
      description: permission?.description || "",
      is_system_permission: permission?.is_system_permission || false,
    },
  });

  const { register, handleSubmit, formState: { errors }, watch, setValue } = form;
  const watchModule = watch("module");
  const watchAction = watch("action");

  // Check if we should show custom inputs
  const shouldShowCustomModule = customModule || !commonModules.includes(watchModule);
  const shouldShowCustomAction = customAction || !commonActions.includes(watchAction);

  const handleFormSubmit = async (data: PermissionFormValues) => {
    try {
      setSubmitError(null);

      if (permission) {
        // Update existing permission
        await updateMutation.mutateAsync({
          id: permission.id,
          data,
        });
      } else {
        // Create new permission
        await createMutation.mutateAsync(data);
      }

      onSubmit();
    } catch (error: any) {
      console.error("Error submitting permission:", error);
      setSubmitError(error.message || "An error occurred while saving the permission");
    }
  };

  const isLoading = createMutation.isPending || updateMutation.isPending;

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
      {/* Error Alert */}
      {submitError && (
        <Alert variant="destructive">
          <AlertDescription>{submitError}</AlertDescription>
        </Alert>
      )}

      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle>Permission Details</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Module */}
          <div className="space-y-2">
            <Label htmlFor="module">Module *</Label>
            {shouldShowCustomModule ? (
              <div className="flex gap-2">
                <Input
                  id="module"
                  placeholder="Enter module name"
                  {...register("module")}
                  className={errors.module ? "border-red-500" : ""}
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setCustomModule(false);
                    setValue("module", "");
                  }}
                >
                  Select
                </Button>
              </div>
            ) : (
              <div className="flex gap-2">
                <Select
                  value={watchModule}
                  onValueChange={(value) => setValue("module", value)}
                >
                  <SelectTrigger className={errors.module ? "border-red-500" : ""}>
                    <SelectValue placeholder="Select module" />
                  </SelectTrigger>
                  <SelectContent>
                    {commonModules.map((module) => (
                      <SelectItem key={module} value={module}>
                        {module.charAt(0).toUpperCase() + module.slice(1)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setCustomModule(true);
                    setValue("module", "");
                  }}
                >
                  Custom
                </Button>
              </div>
            )}
            {errors.module && (
              <p className="text-sm text-red-500">{errors.module.message}</p>
            )}
          </div>

          {/* Action */}
          <div className="space-y-2">
            <Label htmlFor="action">Action *</Label>
            {shouldShowCustomAction ? (
              <div className="flex gap-2">
                <Input
                  id="action"
                  placeholder="Enter action name"
                  {...register("action")}
                  className={errors.action ? "border-red-500" : ""}
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setCustomAction(false);
                    setValue("action", "");
                  }}
                >
                  Select
                </Button>
              </div>
            ) : (
              <div className="flex gap-2">
                <Select
                  value={watchAction}
                  onValueChange={(value) => setValue("action", value)}
                >
                  <SelectTrigger className={errors.action ? "border-red-500" : ""}>
                    <SelectValue placeholder="Select action" />
                  </SelectTrigger>
                  <SelectContent>
                    {commonActions.map((action) => (
                      <SelectItem key={action} value={action}>
                        {action.charAt(0).toUpperCase() + action.slice(1)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setCustomAction(true);
                    setValue("action", "");
                  }}
                >
                  Custom
                </Button>
              </div>
            )}
            {errors.action && (
              <p className="text-sm text-red-500">{errors.action.message}</p>
            )}
          </div>

          {/* Display Name */}
          <div className="space-y-2">
            <Label htmlFor="display_name">Display Name *</Label>
            <Input
              id="display_name"
              placeholder="e.g., Manage Classes"
              {...register("display_name")}
              className={errors.display_name ? "border-red-500" : ""}
            />
            {errors.display_name && (
              <p className="text-sm text-red-500">{errors.display_name.message}</p>
            )}
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              placeholder="Describe what this permission allows..."
              {...register("description")}
              rows={3}
            />
          </div>

          {/* System Permission */}
          <div className="flex items-center space-x-2">
            <Switch
              id="is_system_permission"
              checked={watch("is_system_permission")}
              onCheckedChange={(checked) => setValue("is_system_permission", checked)}
            />
            <Label htmlFor="is_system_permission">System Permission</Label>
          </div>
          <p className="text-sm text-muted-foreground">
            System permissions cannot be deleted and are managed by the system.
          </p>
        </CardContent>
      </Card>

      {/* Preview */}
      {watchModule && watchAction && (
        <Card>
          <CardHeader>
            <CardTitle>Permission Preview</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">Permission Key:</span>
                <code className="bg-muted px-2 py-1 rounded text-sm">
                  {watchModule}.{watchAction}
                </code>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium">Display Name:</span>
                <span className="text-sm">{watch("display_name") || "Not set"}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Form Actions */}
      <div className="flex justify-end gap-3">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? "Saving..." : permission ? "Update Permission" : "Create Permission"}
        </Button>
      </div>
    </form>
  );
}
