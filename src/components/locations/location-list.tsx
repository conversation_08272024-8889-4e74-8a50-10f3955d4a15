"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { DeleteConfirmationDialog } from "@/components/ui/delete-confirmation-dialog";
import { DeleteSuccessToast } from "@/components/ui/delete-success-toast";
import {
  MapPin,
  Phone,
  Edit,
  Trash2,
  Plus,
  Search,
  Building,
  AlertCircle
} from "lucide-react";
import { Location } from "@/lib/db/schema";
import { useLocations, useDeleteLocation } from "@/lib/hooks/queries/use-location-queries";

interface LocationListProps {
  tenantId?: number;
  onEdit?: (location: Location) => void;
  onAdd?: () => void;
  showActions?: boolean;
  className?: string;
}

export function LocationList({
  tenantId,
  onEdit,
  onAdd,
  showActions = true,
  className = "",
}: LocationListProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [deleteError, setDeleteError] = useState<string | null>(null);
  const [deleteConfirmation, setDeleteConfirmation] = useState<{
    isOpen: boolean;
    location: Location | null;
  }>({ isOpen: false, location: null });
  const [showDeleteSuccess, setShowDeleteSuccess] = useState(false);
  const [deletedLocationName, setDeletedLocationName] = useState("");

  const { data: locations = [], isLoading, error } = useLocations();
  const deleteLocationMutation = useDeleteLocation();

  // Filter locations by tenant and search query
  const filteredLocations = locations.filter((location) => {
    const matchesTenant = !tenantId || location.tenantId === tenantId;
    const matchesSearch = !searchQuery || 
      location.addressLine1?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      location.city?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      location.state?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      location.country?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      location.postalCode?.toLowerCase().includes(searchQuery.toLowerCase());
    
    return matchesTenant && matchesSearch;
  });

  const handleDeleteClick = (location: Location) => {
    setDeleteConfirmation({ isOpen: true, location });
  };

  const handleDeleteConfirm = async () => {
    if (!deleteConfirmation.location) return;

    const locationName = deleteConfirmation.location.addressLine1 || "Unknown location";

    try {
      setDeleteError(null);
      await deleteLocationMutation.mutateAsync(deleteConfirmation.location.id);

      // Close confirmation dialog
      setDeleteConfirmation({ isOpen: false, location: null });

      // Show success toast
      setDeletedLocationName(locationName);
      setShowDeleteSuccess(true);

    } catch (error) {
      setDeleteError(error instanceof Error ? error.message : "Failed to delete location");
    }
  };

  const handleDeleteCancel = () => {
    setDeleteConfirmation({ isOpen: false, location: null });
  };

  const formatAddress = (location: Location) => {
    const parts = [
      location.addressLine1,
      location.addressLine2,
      location.city,
      location.state,
      location.postalCode,
      location.country
    ].filter(Boolean);
    
    return parts.join(", ");
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Failed to load locations: {error}
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              Locations
            </CardTitle>
            <CardDescription>
              Manage location addresses and contact information
            </CardDescription>
          </div>
          {showActions && onAdd && (
            <Button onClick={onAdd} size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Add Location
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {deleteError && (
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{deleteError}</AlertDescription>
          </Alert>
        )}

        {/* Search */}
        <div className="relative mb-4">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search locations..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Locations List */}
        {filteredLocations.length === 0 ? (
          <div className="text-center py-8">
            <MapPin className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              {searchQuery ? "No locations found" : "No locations yet"}
            </h3>
            <p className="text-gray-500 dark:text-gray-400 mb-4">
              {searchQuery 
                ? "Try adjusting your search terms."
                : "Get started by adding your first location."
              }
            </p>
            {showActions && onAdd && !searchQuery && (
              <Button onClick={onAdd}>
                <Plus className="h-4 w-4 mr-2" />
                Add Location
              </Button>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            {filteredLocations.map((location) => (
              <div
                key={location.id}
                className="border rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <Building className="h-4 w-4 text-gray-500" />
                      <h4 className="font-medium text-gray-900 dark:text-gray-100">
                        {location.addressLine1}
                      </h4>
                    </div>
                    
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                      {formatAddress(location)}
                    </p>

                    {location.phoneNumber && (
                      <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                        <Phone className="h-3 w-3" />
                        {location.phoneNumber}
                      </div>
                    )}

                    <div className="flex items-center gap-2 mt-2">
                      <Badge variant="secondary" className="text-xs">
                        Tenant ID: {location.tenantId}
                      </Badge>
                    </div>
                  </div>

                  {showActions && (
                    <div className="flex items-center gap-2 ml-4">
                      {onEdit && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onEdit(location)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                      )}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteClick(location)}
                        disabled={deleteLocationMutation.isPending}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Summary */}
        {filteredLocations.length > 0 && (
          <div className="mt-4 pt-4 border-t text-sm text-gray-500 dark:text-gray-400">
            Showing {filteredLocations.length} of {locations.length} locations
          </div>
        )}
      </CardContent>

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationDialog
        isOpen={deleteConfirmation.isOpen}
        onClose={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        title="Delete Location"
        description="Are you sure you want to delete this location? This action cannot be undone."
        itemName={deleteConfirmation.location?.addressLine1 || ""}
        isLoading={deleteLocationMutation.isPending}
      />

      {/* Delete Success Toast */}
      <DeleteSuccessToast
        isVisible={showDeleteSuccess}
        onClose={() => setShowDeleteSuccess(false)}
        itemName={deletedLocationName}
      />
    </Card>
  );
}
