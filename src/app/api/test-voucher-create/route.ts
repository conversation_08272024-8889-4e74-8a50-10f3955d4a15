import { NextRequest, NextResponse } from "next/server";
import { VoucherService } from "@/lib/services/voucher.service";

// Test endpoint to verify voucher CREATE operation and data flow
export async function POST(request: NextRequest) {
  try {
    console.log("🧪 Testing voucher CREATE operation...");
    
    // Step 1: Get vouchers before creation
    console.log("\n1️⃣ Getting vouchers before creation...");
    const beforeVouchers = await VoucherService.getByTenantWithFilters(1, {});
    console.log(`Found ${beforeVouchers.length} vouchers before creation`);

    // Step 2: Create a new voucher
    console.log("\n2️⃣ Creating new voucher...");
    const voucherData = {
      tenantId: 1,
      code: `TEST_${Date.now()}`,
      name: "Test Voucher CREATE",
      description: "Voucher created for testing CREATE operation",
      type: "percentage" as const,
      value: 15,
      currency: "USD",
      usage_limit: 100,
      usage_limit_per_customer: 1,
      valid_from: new Date(),
      valid_until: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      is_active: true,
      is_public: true,
      auto_apply: false,
      restrictions: {
        min_purchase_amount: 50,
        max_discount_amount: 100,
        first_time_customers_only: false,
        existing_customers_only: false,
      }
    };

    const createdVoucher = await VoucherService.create(voucherData);
    console.log("✅ Voucher created successfully:", {
      id: createdVoucher.id,
      code: createdVoucher.code,
      name: createdVoucher.name,
      tenantId: createdVoucher.tenantId
    });

    // Step 3: Get vouchers after creation
    console.log("\n3️⃣ Getting vouchers after creation...");
    const afterVouchers = await VoucherService.getByTenantWithFilters(1, {});
    console.log(`Found ${afterVouchers.length} vouchers after creation`);

    // Step 4: Verify the created voucher exists in the list
    const foundVoucher = afterVouchers.find(v => v.id === createdVoucher.id);
    console.log("Created voucher found in list:", !!foundVoucher);

    // Step 5: Test API endpoint response structure
    console.log("\n4️⃣ Testing API endpoint response structure...");
    const apiResponse = await fetch(`http://localhost:3001/api/vouchers?tenantId=1&limit=100`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    });

    if (apiResponse.ok) {
      const apiData = await apiResponse.json();
      console.log("API Response structure:", {
        success: apiData.success,
        hasData: !!apiData.data,
        dataType: Array.isArray(apiData.data) ? 'array' : typeof apiData.data,
        dataLength: Array.isArray(apiData.data) ? apiData.data.length : 'N/A',
        count: apiData.count
      });

      // Check if created voucher is in API response
      if (Array.isArray(apiData.data)) {
        const foundInApi = apiData.data.find((v: any) => v.id === createdVoucher.id);
        console.log("Created voucher found in API response:", !!foundInApi);
      }
    } else {
      console.log("❌ API request failed:", apiResponse.status, apiResponse.statusText);
    }

    return NextResponse.json({
      success: true,
      message: "Voucher CREATE test completed",
      data: {
        beforeCount: beforeVouchers.length,
        afterCount: afterVouchers.length,
        countDifference: afterVouchers.length - beforeVouchers.length,
        createdVoucher: {
          id: createdVoucher.id,
          code: createdVoucher.code,
          name: createdVoucher.name,
          tenantId: createdVoucher.tenantId
        },
        foundInServiceList: !!foundVoucher,
        tests: {
          serviceCreate: true,
          serviceList: afterVouchers.length > beforeVouchers.length,
          voucherInList: !!foundVoucher,
          apiEndpointWorking: apiResponse.ok
        }
      }
    });
  } catch (error) {
    console.error("❌ Voucher CREATE test failed:", error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}
