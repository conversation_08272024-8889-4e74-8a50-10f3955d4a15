import NextAuth from "next-auth";
import { Driz<PERSON>Adapter } from "@auth/drizzle-adapter";
import CredentialsProvider from "next-auth/providers/credentials";
import GoogleProvider from "next-auth/providers/google";
import { db } from "../db";
import { users, accounts, sessions, verificationTokens } from "../db/schema";
import { eq } from "drizzle-orm";
import { z } from "zod";
import { RoleService } from "../services/role.service";
import { PermissionService } from "../services/permission.service";
import { LocationAccessService } from "../services/location-access.service";

// Schema untuk validasi login
const loginSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(6, "Password must be at least 6 characters"),
});

const signupSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(8, "Password must be at least 8 characters")
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/, "Password must contain uppercase, lowercase, and number"),
  name: z.string().min(2, "Name must be at least 2 characters"),
  termsAccepted: z.boolean().refine(val => val === true, "You must accept the terms and conditions"),
});

const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

// Rate limiting function
function checkRateLimit(identifier: string, maxAttempts = 5, windowMs = 15 * 60 * 1000): boolean {
  const now = Date.now();
  const record = rateLimitStore.get(identifier);

  if (!record || now > record.resetTime) {
    rateLimitStore.set(identifier, { count: 1, resetTime: now + windowMs });
    return true;
  }

  if (record.count >= maxAttempts) {
    return false;
  }

  record.count++;
  return true;
}



export const authConfig = {
  secret: process.env.NEXTAUTH_SECRET || process.env.AUTH_SECRET,
  adapter: DrizzleAdapter(db, {
    usersTable: users,
    accountsTable: accounts,
    sessionsTable: sessions,
    verificationTokensTable: verificationTokens,
  }),
  providers: [
    // Email/Password Provider
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        try {
          console.log("🔐 Authorize attempt:", { email: credentials?.email, hasPassword: !!credentials?.password });

          // Validasi input
          const { email, password } = loginSchema.parse(credentials);
          console.log("✅ Input validation passed");

          // Cari user berdasarkan email
          const [user] = await db
            .select()
            .from(users)
            .where(eq(users.email, email))
            .limit(1);

          console.log("👤 User lookup result:", {
            found: !!user,
            hasPassword: !!user?.password,
            userId: user?.id,
            userEmail: user?.email
          });

          if (!user || !user.password) {
            console.log("❌ User not found or no password");
            return null;
          }

          // Verifikasi password - import bcryptjs secara dinamis untuk Edge Runtime
          const { compare } = await import("bcryptjs");
          const isPasswordValid = await compare(password, user.password);

          console.log("🔑 Password validation:", { isValid: isPasswordValid });

          if (!isPasswordValid) {
            console.log("❌ Password invalid");
            return null;
          }

          console.log("✅ Authentication successful");

          // Return user object (tanpa password)
          return {
            id: user.id,
            email: user.email,
            name: user.name,
            image: user.image,
            role: user.role,
            organizationId: user.organizationId,
          };
        } catch (error) {
          console.error("❌ Auth error:", error);
          return null;
        }
      },
    }),

    // Google OAuth Provider
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      allowDangerousEmailAccountLinking: true,
    }),
  ],
  
  session: {
    strategy: "jwt" as const,
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },

  trustHost: true,

  callbacks: {
    async jwt({ token, user, account, trigger }) {
      try {
        // Saat login pertama kali
        if (user) {
          token.role = user.role;
          token.organizationId = user.organizationId;
          token.tenantId = user.tenantId;
        }

        // ✅ Always fetch user data from database if not available in token
        console.log("🔍 JWT callback - checking token:", { hasRole: !!token.role, hasEmail: !!token.email, email: token.email, role: token.role });
        if (!token.role && token.email) {
          try {
            console.log("🔍 Fetching user role from database for:", token.email);
            const [existingUser] = await db
              .select()
              .from(users)
              .where(eq(users.email, token.email))
              .limit(1);

            if (existingUser) {
              console.log("✅ Found user in database:", { role: existingUser.role, id: existingUser.id });
              token.role = existingUser.role;
              token.organizationId = existingUser.organizationId;
              token.tenantId = existingUser.tenantId;
              token.sub = existingUser.id; // Set user ID
            } else {
              console.log("❌ User not found in database for email:", token.email);
            }
          } catch (error) {
            console.error("Error fetching user in JWT callback:", error);
          }
        }

        // Untuk OAuth providers, update user info
        if (account?.provider === "google" && user) {
          try {
            const [existingUser] = await db
              .select()
              .from(users)
              .where(eq(users.email, user.email!))
              .limit(1);

            if (existingUser) {
              token.role = existingUser.role;
              token.organizationId = existingUser.organizationId;
              token.tenantId = existingUser.tenantId;
            }
          } catch (error) {
            console.error("Error fetching user in JWT callback:", error);
          }
        }

        // Load RBAC data jika belum ada atau perlu refresh
        const shouldRefreshRBAC =
          !token.rbacLastUpdated ||
          (Date.now() - token.rbacLastUpdated) > (5 * 60 * 1000) || // 5 minutes
          trigger === "update";

        if (shouldRefreshRBAC && token.sub) {
          console.log("Loading RBAC data for user:", token.sub);

          // Get user roles
          const userRolesResult = await RoleService.getUserRoles(token.tenantId as number, token.sub);

          // Get role names
          const roleNames: string[] = [];
          for (const userRole of userRolesResult.userRoles) {
            try {
              const role = await RoleService.getById(userRole.roleId);
              if (role) {
                roleNames.push(role.name);
              }
            } catch (error) {
              console.error(`Error getting role ${userRole.roleId}:`, error);
            }
          }

          // Get user permissions
          const userPermissions = await PermissionService.getUserPermissions(token.sub, token.tenantId as number);
          const permissions = userPermissions.map(p => `${p.module}.${p.action}`);

          // Get accessible locations
          let accessibleLocations: string[] = [];
          if (token.tenantId) {
            try {
              const locationAccess = await LocationAccessService.getUserAccessibleLocations(token.sub, token.tenantId as number);
              accessibleLocations = locationAccess.map(la => la.locationId);
            } catch (error) {
              console.error("Error getting accessible locations:", error);
            }
          }

          token.roles = roleNames;
          token.permissions = permissions;
          token.accessibleLocations = accessibleLocations;
          token.rbacLastUpdated = Date.now();

          console.log("RBAC data loaded:", { roles: roleNames, permissions: permissions.length });
        }

        return token;
      } catch (error) {
        console.error("JWT callback error:", error);
        return token;
      }
    },

    async session({ session, token }) {
      console.log("🔍 Session callback - token received:", JSON.stringify(token, null, 2));
      if (token) {
        session.user.id = token.sub!;
        session.user.role = token.role as string;
        session.user.organizationId = token.organizationId as string;
        session.user.tenantId = token.tenantId as number;
        session.user.roles = token.roles as string[] || [];
        session.user.permissions = token.permissions as string[] || [];
        session.user.accessibleLocations = token.accessibleLocations as string[] || [];
      } else {
        // ✅ Fallback: Jika token tidak ada, coba ambil dari database
        console.log("⚠️ Session callback - no token, trying to fetch from database...");
        if (session?.user?.email) {
          try {
            const user = await db.query.users.findFirst({
              where: eq(users.email, session.user.email),
              with: {
                organizationMembers: {
                  with: {
                    organization: true
                  }
                }
              }
            });

            if (user) {
              session.user.id = user.id;
              session.user.role = user.role || "user";
              session.user.organizationId = user.organizationMembers?.[0]?.organization?.id;
              // Set default values untuk RBAC
              session.user.roles = [];
              session.user.permissions = [];
              session.user.accessibleLocations = [];
              console.log("✅ Session callback - fallback successful:", session.user.role);
            }
          } catch (error) {
            console.error("❌ Session callback - fallback failed:", error);
          }
        }
      }
      console.log("🔍 Session callback - final session:", JSON.stringify(session, null, 2));
      return session;
    },

    async signIn({ user, account, profile }) {
      // Untuk OAuth providers, pastikan user memiliki email verified
      if (account?.provider === "google") {
        if (!profile?.email_verified) {
          return false;
        }

        // Update atau create user dengan email verified
        try {
          const [existingUser] = await db
            .select()
            .from(users)
            .where(eq(users.email, user.email!))
            .limit(1);

          if (existingUser && !existingUser.emailVerified) {
            await db
              .update(users)
              .set({ emailVerified: new Date() })
              .where(eq(users.id, existingUser.id));
          }
        } catch (error) {
          console.error("Error updating user email verification:", error);
        }
      }

      return true;
    },

    async redirect({ url, baseUrl }) {
      // Allows relative callback URLs
      if (url.startsWith("/")) return `${baseUrl}${url}`;

      // Allows callback URLs on the same origin
      if (new URL(url).origin === baseUrl) return url;

      // Default redirect to dashboard
      return `${baseUrl}/dashboard`;
    },
  },

  pages: {
    signIn: "/auth/signin",
    signUp: "/auth/signup",
    error: "/auth/error",
    verifyRequest: "/auth/verify-request",
  },

  events: {
    async signIn({ user, account, isNewUser }) {
      console.log(`User ${user.email} signed in with ${account?.provider}`);
      
      // Log sign in event
      // Bisa ditambahkan logging ke database atau analytics
    },
    
    async signOut({ session }) {
      console.log(`User ${session?.user?.email} signed out`);
    },
  },

  debug: process.env.NODE_ENV === "development",

  logger: {
    error(code, metadata) {
      console.error("NextAuth Error:", code, metadata);
    },
    warn(code) {
      console.warn("NextAuth Warning:", code);
    },
    debug(code, metadata) {
      if (process.env.NODE_ENV === "development") {
        console.log("NextAuth Debug:", code, metadata);
      }
    },
  },
};

export const { handlers, auth, signIn, signOut } = NextAuth(authConfig);
