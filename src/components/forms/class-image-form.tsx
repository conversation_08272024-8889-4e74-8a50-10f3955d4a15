"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { motion } from "framer-motion";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, ImageIcon, CheckCircle } from "lucide-react";
import { ImageUpload } from "@/components/ui/image-upload";
import { ClassImage } from "@/lib/db/schema";

/**
 * Validation schema untuk class image form
 * 
 * Ini define rules untuk validasi form:
 * - class_id: wajib, ID class yang valid
 * - image_url: wajib, URL image yang valid
 */
const classImageSchema = z.object({
  class_id: z.string().min(1, "Class ID is required"),
  image_url: z.string().url("Invalid image URL").min(1, "Image URL is required"),
});

type ClassImageFormData = z.infer<typeof classImageSchema>;

interface ClassImageFormProps {
  classImage?: ClassImage;
  classId: string;
  onSubmit: (data: ClassImageFormData) => Promise<void>;
  onCancel?: () => void;
  isLoading?: boolean;
  className?: string;
}

/**
 * ClassImageForm Component
 * 
 * Form untuk create/edit class image dengan:
 * - Image upload dengan drag & drop
 * - URL input alternative
 * - Image preview
 * - Validation yang comprehensive
 * 
 * Pattern ini sama dengan ClassForm dan form components lainnya.
 */
export function ClassImageForm({ 
  classImage, 
  classId, 
  onSubmit, 
  onCancel, 
  isLoading = false,
  className = "" 
}: ClassImageFormProps) {
  const [isSuccess, setIsSuccess] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  const form = useForm<ClassImageFormData>({
    resolver: zodResolver(classImageSchema),
    defaultValues: {
      class_id: classId,
      image_url: classImage?.image_url || "",
    },
  });

  const handleImageUpload = (url: string) => {
    form.setValue("image_url", url);
    setSubmitError(null);
  };

  const handleUrlChange = (url: string) => {
    form.setValue("image_url", url);
    setSubmitError(null);
  };

  const handleSubmit = async (data: ClassImageFormData) => {
    try {
      setSubmitError(null);
      
      // Additional validation
      if (!data.image_url.trim()) {
        setSubmitError("Please provide an image URL or upload a file");
        return;
      }

      await onSubmit(data);
      setIsSuccess(true);
      setTimeout(() => setIsSuccess(false), 2000);
    } catch (error) {
      console.error("Form submission error:", error);
      
      // Handle specific validation errors
      let errorMessage = "An error occurred";
      if (error instanceof Error) {
        if (error.message.includes("Class with ID") && error.message.includes("not found")) {
          errorMessage = "The selected class is invalid or no longer exists.";
        } else if (error.message.includes("Invalid image URL")) {
          errorMessage = "The provided image URL is invalid. Please check the URL and try again.";
        } else {
          errorMessage = error.message;
        }
      }
      
      setSubmitError(errorMessage);
    }
  };

  const isFormLoading = isLoading || form.formState.isSubmitting;

  return (
    <Card className={`w-full max-w-2xl mx-auto ${className}`}>
      <CardHeader className="text-center">
        <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
          <ImageIcon className="h-6 w-6 text-blue-600" />
        </div>
        <CardTitle className="text-xl font-semibold">
          {classImage ? "Edit Class Image" : "Add Class Image"}
        </CardTitle>
        <CardDescription>
          {classImage ? "Update the class image" : "Add a new image to the class"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          {/* Success Alert */}
          {isSuccess && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="mb-4"
            >
              <Alert className="border-green-200 bg-green-50">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <AlertDescription className="text-green-800">
                  Class image {classImage ? "updated" : "added"} successfully!
                </AlertDescription>
              </Alert>
            </motion.div>
          )}

          {/* Error Alert */}
          {submitError && (
            <Alert variant="destructive" className="mb-4">
              <AlertDescription>{submitError}</AlertDescription>
            </Alert>
          )}

          {/* Image Upload Section */}
          <div className="space-y-4">
            <Label>Class Image</Label>
            <ImageUpload
              onImageUpload={handleImageUpload}
              onUrlChange={handleUrlChange}
              currentImageUrl={classImage?.image_url}
              disabled={isFormLoading}
              placeholder="Upload a class image or enter image URL"
              autoUpload={true}
            />
            
            {/* Hidden input for form validation */}
            <input
              type="hidden"
              {...form.register("image_url")}
            />
            
            {form.formState.errors.image_url && (
              <p className="text-sm text-red-500">
                {form.formState.errors.image_url.message}
              </p>
            )}
          </div>

          {/* Image Info */}
          {form.watch("image_url") && (
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              className="p-4 bg-muted rounded-lg"
            >
              <div className="space-y-2">
                <Label className="text-sm font-medium">Image URL:</Label>
                <p className="text-sm text-muted-foreground break-all">
                  {form.watch("image_url")}
                </p>
              </div>
            </motion.div>
          )}

          {/* Form Actions */}
          <div className="flex gap-3 pt-4">
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isFormLoading}
                className="flex-1"
              >
                Cancel
              </Button>
            )}
            <Button
              type="submit"
              disabled={isFormLoading || !form.watch("image_url")}
              className="flex-1"
            >
              {isFormLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {classImage ? "Update" : "Add"} Image
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
