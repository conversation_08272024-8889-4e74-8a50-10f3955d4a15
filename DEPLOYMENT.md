# Deployment Guide

This guide covers different deployment options for your Next.js 15 SaaS template.

## 🚀 Quick Deploy Options

### 1. Cloudflare Pages (Recommended)

Cloudflare Pages offers excellent performance with global CDN and edge computing.

#### Prerequisites
- Cloudflare account
- GitHub repository

#### Steps
1. **Connect Repository**
   - Go to Cloudflare Dashboard > Pages
   - Click "Create a project"
   - Connect your GitHub repository

2. **Configure Build Settings**
   ```
   Build command: npm run build
   Build output directory: .next
   Root directory: /
   ```

3. **Environment Variables**
   Set these in Cloudflare Pages settings:
   ```
   NODE_ENV=production
   DATABASE_URL=your_d1_database_url
   NEXTAUTH_SECRET=your_secret_key
   NEXTAUTH_URL=https://your-domain.pages.dev
   AUTH_SECRET=your_secret_key
   GOOGLE_CLIENT_ID=your_google_client_id
   GOOGLE_CLIENT_SECRET=your_google_client_secret
   STRIPE_SECRET_KEY=your_stripe_secret_key
   STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
   NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=your_stripe_publishable_key
   STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret
   RESEND_API_KEY=your_resend_api_key
   FROM_EMAIL=<EMAIL>
   APP_URL=https://your-domain.pages.dev
   CLOUDFLARE_ACCOUNT_ID=your_account_id
   CLOUDFLARE_API_TOKEN=your_api_token
   CLOUDFLARE_KV_NAMESPACE_ID=your_kv_namespace_id
   CLOUDFLARE_D1_DATABASE_ID=your_d1_database_id
   ```

4. **Deploy**
   - Push to your main branch
   - Cloudflare will automatically build and deploy

### 2. Vercel

Vercel provides seamless Next.js deployment with zero configuration.

#### Steps
1. **Connect Repository**
   - Go to Vercel Dashboard
   - Click "New Project"
   - Import your GitHub repository

2. **Environment Variables**
   Add the same environment variables as listed above in Vercel's project settings.

3. **Deploy**
   - Vercel will automatically deploy on every push to main branch

### 3. Docker Deployment

For self-hosted or cloud container deployments.

#### Build Docker Image
```bash
# Build the image
docker build -t your-saas-app .

# Run the container
docker run -p 3000:3000 \
  -e DATABASE_URL="./prod.db" \
  -e NEXTAUTH_SECRET="your_secret" \
  -e NEXTAUTH_URL="https://yourdomain.com" \
  your-saas-app
```

#### Using Docker Compose
```bash
# Copy environment variables
cp .env.example .env.production

# Edit .env.production with your production values
nano .env.production

# Start the application
docker-compose up -d
```

### 4. Traditional VPS/Server

For deployment on Ubuntu/Debian servers.

#### Prerequisites
- Node.js 18+
- PM2 (process manager)
- Nginx (reverse proxy)

#### Steps
1. **Setup Server**
   ```bash
   # Update system
   sudo apt update && sudo apt upgrade -y
   
   # Install Node.js
   curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
   sudo apt-get install -y nodejs
   
   # Install PM2
   sudo npm install -g pm2
   
   # Install Nginx
   sudo apt install nginx -y
   ```

2. **Deploy Application**
   ```bash
   # Clone repository
   git clone https://github.com/yourusername/your-saas-app.git
   cd your-saas-app
   
   # Install dependencies
   npm ci
   
   # Setup environment
   cp .env.example .env.production
   nano .env.production
   
   # Build application
   npm run build
   
   # Start with PM2
   pm2 start ecosystem.config.js
   pm2 save
   pm2 startup
   ```

3. **Configure Nginx**
   ```nginx
   server {
       listen 80;
       server_name yourdomain.com;
       
       location / {
           proxy_pass http://localhost:3000;
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection 'upgrade';
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
           proxy_cache_bypass $http_upgrade;
       }
   }
   ```

## 🗄️ Database Setup

### SQLite (Development/Small Scale)
- Default configuration works out of the box
- Database file stored locally

### PostgreSQL (Production Recommended)
1. **Update DATABASE_URL**
   ```
   DATABASE_URL="postgresql://username:password@host:port/database"
   ```

2. **Run Migrations**
   ```bash
   npm run db:migrate
   npm run db:seed
   ```

### Cloudflare D1 (Serverless)
1. **Create D1 Database**
   ```bash
   npx wrangler d1 create your-saas-db
   ```

2. **Update Environment Variables**
   ```
   CLOUDFLARE_D1_DATABASE_ID=your_database_id
   DATABASE_URL=your_d1_connection_string
   ```

## 📧 Email Configuration

### Resend (Recommended)
```
RESEND_API_KEY=re_your_api_key
FROM_EMAIL=<EMAIL>
```

### Brevo (Alternative)
```
BREVO_API_KEY=your_brevo_api_key
FROM_EMAIL=<EMAIL>
```

## 💳 Stripe Configuration

1. **Get API Keys**
   - Go to Stripe Dashboard > Developers > API keys
   - Copy Publishable key and Secret key

2. **Setup Webhooks**
   - Go to Stripe Dashboard > Developers > Webhooks
   - Add endpoint: `https://yourdomain.com/api/webhooks/stripe`
   - Select events: `payment_intent.succeeded`, `customer.created`

3. **Environment Variables**
   ```
   STRIPE_SECRET_KEY=sk_live_your_secret_key
   STRIPE_PUBLISHABLE_KEY=pk_live_your_publishable_key
   NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_live_your_publishable_key
   STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
   ```

## 🔐 Security Checklist

- [ ] Use strong, unique secrets for all environment variables
- [ ] Enable HTTPS/SSL certificates
- [ ] Configure proper CORS settings
- [ ] Set up rate limiting
- [ ] Enable security headers
- [ ] Regular security updates
- [ ] Database backups
- [ ] Monitor application logs

## 📊 Monitoring

### Health Checks
- Endpoint: `/api/health`
- Returns application status and database connectivity

### Logging
- Application logs via console
- Error tracking with Sentry (optional)
- Performance monitoring

## 🔄 CI/CD Pipeline

The included GitHub Actions workflow automatically:
- Runs tests on pull requests
- Deploys to staging on `develop` branch
- Deploys to production on `main` branch

### Required GitHub Secrets
```
CLOUDFLARE_API_TOKEN
CLOUDFLARE_ACCOUNT_ID
DATABASE_URL
NEXTAUTH_SECRET
NEXTAUTH_URL
AUTH_SECRET
STRIPE_SECRET_KEY
STRIPE_PUBLISHABLE_KEY
STRIPE_WEBHOOK_SECRET
RESEND_API_KEY
FROM_EMAIL
APP_URL
```

## 🚨 Troubleshooting

### Common Issues

1. **Build Failures**
   - Check environment variables
   - Verify Node.js version (18+)
   - Clear `.next` cache

2. **Database Connection**
   - Verify DATABASE_URL format
   - Check network connectivity
   - Run migrations

3. **Authentication Issues**
   - Verify NEXTAUTH_SECRET is set
   - Check OAuth provider configuration
   - Ensure NEXTAUTH_URL matches deployment URL

4. **Stripe Integration**
   - Verify API keys are correct
   - Check webhook endpoint configuration
   - Test in Stripe's test mode first

### Getting Help

1. Check application logs
2. Review environment variables
3. Test locally first
4. Check GitHub Issues
5. Contact support

---

**Happy Deploying! 🚀**
