import { Metadata } from "next";
import { getCurrentUser } from "@/lib/auth/utils";
import { redirect } from "next/navigation";
import { CreditBalance } from "@/components/billing/credit-balance";
import { CreditPackages } from "@/components/billing/credit-packages";
import { TransactionHistory } from "@/components/billing/transaction-history";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

export const metadata: Metadata = {
  title: "Billing - Your SaaS App",
  description: "Manage your credits and billing",
};

export default async function BillingPage() {
  const user = await getCurrentUser();
  
  if (!user) {
    redirect("/auth/signin");
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold">Billing & Credits</h1>
        <p className="text-muted-foreground">
          Manage your credits, view usage, and purchase additional credits
        </p>
      </div>

      <div className="space-y-8">
        {/* Credit Balance Overview */}
        <CreditBalance 
          userId={user.id} 
          organizationId={user.organizationId} 
        />

        <Tabs defaultValue="purchase" className="space-y-6">
          <TabsList>
            <TabsTrigger value="purchase">Purchase Credits</TabsTrigger>
            <TabsTrigger value="history">Transaction History</TabsTrigger>
          </TabsList>

          <TabsContent value="purchase" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Credit Packages</CardTitle>
                <CardDescription>
                  Choose a credit package that fits your needs. Credits never expire and can be used for all API calls.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <CreditPackages organizationId={user.organizationId} />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="history" className="space-y-6">
            <TransactionHistory 
              userId={user.id} 
              organizationId={user.organizationId} 
            />
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
