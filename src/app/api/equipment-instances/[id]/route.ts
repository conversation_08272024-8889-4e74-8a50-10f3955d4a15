import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { z } from "zod";
import { EquipmentInstanceService } from "@/lib/services/equipment-instances.service";

// Validation schema for updating equipment instance
const updateEquipmentInstanceSchema = z.object({
  equipmentId: z.string().min(1).optional(),
  locationId: z.string().min(1).optional(),
  quantity: z.number().int().min(1).optional(),
  displayName: z.string().max(255).optional(),
});

// PUT /api/equipment-instances/[id] - Update equipment instance
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id: instanceId } = await params;
    if (!instanceId) {
      return NextResponse.json(
        { error: "Equipment instance ID is required" },
        { status: 400 }
      );
    }

    const body = await request.json();
    const validatedData = updateEquipmentInstanceSchema.parse(body);

    const updatedInstance = await EquipmentInstanceService.update(instanceId, validatedData);

    return NextResponse.json(
      { 
        message: "Equipment instance updated successfully", 
        data: updatedInstance 
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error updating equipment instance:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid data", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to update equipment instance" },
      { status: 500 }
    );
  }
}

// DELETE /api/equipment-instances/[id] - Delete equipment instance
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id: instanceId } = await params;
    if (!instanceId) {
      return NextResponse.json(
        { error: "Equipment instance ID is required" },
        { status: 400 }
      );
    }

    const deletedInstance = await EquipmentInstanceService.delete(instanceId);

    return NextResponse.json(
      {
        message: "Equipment instance deleted successfully",
        data: deletedInstance
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error deleting equipment instance:", error);
    return NextResponse.json(
      { error: "Failed to delete equipment instance" },
      { status: 500 }
    );
  }
}

// GET /api/equipment-instances/[id] - Get single equipment instance
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id: instanceId } = await params;
    if (!instanceId) {
      return NextResponse.json(
        { error: "Equipment instance ID is required" },
        { status: 400 }
      );
    }

    const instance = await EquipmentInstanceService.getById(instanceId);

    if (!instance) {
      return NextResponse.json(
        { error: "Equipment instance not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { data: instance },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error fetching equipment instance:", error);
    return NextResponse.json(
      { error: "Failed to fetch equipment instance" },
      { status: 500 }
    );
  }
}
