"use client";

import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { CheckCircle, Trash2, X, Undo2 } from "lucide-react";
import { Button } from "@/components/ui/button";

interface DeleteSuccessToastProps {
  isVisible: boolean;
  onClose: () => void;
  itemName: string;
  onUndo?: () => void;
  autoClose?: boolean;
  duration?: number;
}

export function DeleteSuccessToast({
  isVisible,
  onClose,
  itemName,
  onUndo,
  autoClose = true,
  duration = 5000
}: DeleteSuccessToastProps) {
  const [progress, setProgress] = useState(100);

  useEffect(() => {
    if (!isVisible || !autoClose) return;

    const interval = setInterval(() => {
      setProgress((prev) => {
        if (prev <= 0) {
          onClose();
          return 0;
        }
        return prev - (100 / (duration / 100));
      });
    }, 100);

    return () => clearInterval(interval);
  }, [isVisible, autoClose, duration, onClose]);

  useEffect(() => {
    if (isVisible) {
      setProgress(100);
    }
  }, [isVisible]);

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: -100, scale: 0.9 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          exit={{ opacity: 0, y: -100, scale: 0.9 }}
          transition={{
            type: "spring",
            stiffness: 300,
            damping: 30,
            duration: 0.4
          }}
          className="fixed top-4 right-4 z-50 w-96 max-w-sm"
        >
          <div className="relative bg-white dark:bg-gray-800 rounded-lg shadow-2xl border border-red-200 dark:border-red-800 overflow-hidden">
            {/* Success glow effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-red-50 to-orange-50 dark:from-red-900/20 dark:to-orange-900/20" />
            
            {/* Progress bar */}
            {autoClose && (
              <motion.div
                className="absolute top-0 left-0 h-1 bg-gradient-to-r from-red-500 to-orange-500"
                initial={{ width: "100%" }}
                animate={{ width: `${progress}%` }}
                transition={{ duration: 0.1, ease: "linear" }}
              />
            )}

            <div className="relative p-4">
              <div className="flex items-start gap-3">
                {/* Animated icon */}
                <motion.div
                  initial={{ scale: 0, rotate: -180 }}
                  animate={{ scale: 1, rotate: 0 }}
                  transition={{
                    type: "spring",
                    stiffness: 400,
                    damping: 20,
                    delay: 0.2
                  }}
                  className="flex-shrink-0 relative"
                >
                  <div className="w-8 h-8 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center">
                    <Trash2 className="h-4 w-4 text-red-600 dark:text-red-400" />
                  </div>
                  
                  {/* Success checkmark overlay */}
                  <motion.div
                    initial={{ opacity: 0, scale: 0 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{
                      delay: 0.5,
                      type: "spring",
                      stiffness: 400,
                      damping: 20
                    }}
                    className="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center"
                  >
                    <CheckCircle className="h-3 w-3 text-white" />
                  </motion.div>
                </motion.div>

                <div className="flex-1 min-w-0">
                  <motion.h4
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.1 }}
                    className="text-sm font-semibold text-gray-900 dark:text-gray-100"
                  >
                    Location Deleted
                  </motion.h4>
                  
                  <motion.p
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.2 }}
                    className="text-sm text-gray-600 dark:text-gray-400 mt-1"
                  >
                    <span className="font-medium">{itemName}</span> has been permanently deleted.
                  </motion.p>

                  {onUndo && (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: 0.3 }}
                      className="mt-3"
                    >
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={onUndo}
                        className="text-blue-700 border-blue-300 hover:bg-blue-50 dark:text-blue-400 dark:border-blue-600 dark:hover:bg-blue-900/20"
                      >
                        <Undo2 className="h-3 w-3 mr-1" />
                        Undo
                      </Button>
                    </motion.div>
                  )}
                </div>

                {/* Close button */}
                <motion.div
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.4 }}
                >
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onClose}
                    className="h-6 w-6 p-0 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300"
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </motion.div>
              </div>
            </div>

            {/* Floating particles effect */}
            <div className="absolute inset-0 pointer-events-none overflow-hidden">
              {[...Array(4)].map((_, i) => (
                <motion.div
                  key={i}
                  initial={{ 
                    opacity: 0, 
                    y: 20, 
                    x: Math.random() * 100 - 50,
                    scale: 0 
                  }}
                  animate={{ 
                    opacity: [0, 1, 0], 
                    y: -30, 
                    scale: [0, 1, 0] 
                  }}
                  transition={{
                    duration: 2,
                    delay: 0.5 + i * 0.1,
                    ease: "easeOut"
                  }}
                  className="absolute bottom-4 left-1/2 w-1 h-1 bg-red-400 rounded-full"
                  style={{
                    left: `${20 + i * 20}%`
                  }}
                />
              ))}
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
