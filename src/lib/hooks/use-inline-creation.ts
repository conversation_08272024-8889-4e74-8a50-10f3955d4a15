import { useCallback } from 'react';
import { useInlineCreationStore, EntityType, InlineCreationConfig } from '@/lib/stores/inline-creation-store';

// Import form components (only the ones we're sure exist)
import { ClassLevelForm } from '@/components/forms/class-level-form';
import { ClassCategoryForm } from '@/components/forms/class-category-form';
import { ClassSubcategoryForm } from '@/components/forms/class-subcategory-form';
import { LocationForm } from '@/components/forms/location-form';
import { FacilityForm } from '@/components/forms/facility-form';
import { ClassForm } from '@/components/forms/class-form';

// Hooks are now handled in InlineCreationModal component

/**
 * Configuration mapping for different entity types
 */
const ENTITY_CONFIGS: Record<EntityType, {
  formComponent: React.ComponentType<any>;
  title: string;
}> = {
  'class': {
    formComponent: ClassForm,
    title: 'Add New Class',
  },
  'class-level': {
    formComponent: ClassLevelForm,
    title: 'Add New Class Level',
  },
  'class-category': {
    formComponent: ClassCategoryForm,
    title: 'Add New Class Category',
  },
  'class-subcategory': {
    formComponent: ClassSubcategoryForm,
    title: 'Add New Class Subcategory',
  },
  'location': {
    formComponent: LocationForm,
    title: 'Add New Location',
  },
  // TODO: Add other entity types when their forms and hooks are ready
  'pricing-group': {
    formComponent: ClassLevelForm, // Temporary placeholder
    title: 'Add New Pricing Group',
  },
  'equipment': {
    formComponent: ClassLevelForm, // Temporary placeholder
    title: 'Add New Equipment',
  },
  'facility': {
    formComponent: FacilityForm,
    title: 'Add New Facility',
  },
  'membership-plan': {
    formComponent: ClassLevelForm, // Temporary placeholder
    title: 'Add New Membership Plan',
  },
  'waiver-form': {
    formComponent: ClassLevelForm, // Temporary placeholder
    title: 'Add New Waiver Form',
  },
};

/**
 * Hook for managing inline creation modals
 * 
 * This hook provides a simplified interface for opening inline creation modals
 * with automatic configuration based on entity type.
 * 
 * @param tenantId - The tenant ID for the current context
 * @returns Object with methods to open different entity creation modals
 */
export function useInlineCreation(tenantId: number = 1) {
  const { openModal } = useInlineCreationStore();

  /**
   * Open an inline creation modal for a specific entity type
   * 
   * @param entityType - The type of entity to create
   * @param options - Additional configuration options
   */
  const openInlineCreation = useCallback((
    entityType: EntityType,
    options: {
      onSuccess?: (newEntity: any) => void;
      refetchHook?: () => void;
      additionalProps?: Record<string, any>;
    } = {}
  ) => {
    console.log('useInlineCreation: openInlineCreation called with:', entityType, options);

    const config = ENTITY_CONFIGS[entityType];

    if (!config) {
      console.error(`No configuration found for entity type: ${entityType}`);
      return;
    }

    console.log('useInlineCreation: Found config for entity type:', entityType, config);

    const modalConfig: InlineCreationConfig = {
      entityType,
      title: config.title,
      formComponent: config.formComponent,
      tenantId,
      ...options,
    };

    console.log('useInlineCreation: Opening modal with config:', modalConfig);
    openModal(modalConfig);
  }, [openModal, tenantId]);

  // Convenience methods for specific entity types (only working ones)

  const openClassCreation = useCallback((options?: {
    onSuccess?: (newEntity: any) => void;
    refetchHook?: () => void;
  }) => {
    openInlineCreation('class', options);
  }, [openInlineCreation]);

  const openClassLevelCreation = useCallback((options?: {
    onSuccess?: (newEntity: any) => void;
    refetchHook?: () => void;
  }) => {
    openInlineCreation('class-level', options);
  }, [openInlineCreation]);

  const openClassCategoryCreation = useCallback((options?: {
    onSuccess?: (newEntity: any) => void;
    refetchHook?: () => void;
  }) => {
    openInlineCreation('class-category', options);
  }, [openInlineCreation]);

  const openClassSubcategoryCreation = useCallback((options?: {
    onSuccess?: (newEntity: any) => void;
    refetchHook?: () => void;
  }) => {
    openInlineCreation('class-subcategory', options);
  }, [openInlineCreation]);

  const openLocationCreation = useCallback((options?: {
    onSuccess?: (newEntity: any) => void;
    refetchHook?: () => void;
  }) => {
    openInlineCreation('location', options);
  }, [openInlineCreation]);

  return {
    // Generic method
    openInlineCreation,
    openClassCreation,
    openClassLevelCreation,
    openClassCategoryCreation,
    openClassSubcategoryCreation,
    openLocationCreation,
  };
}
