import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { db } from "@/lib/db";
import { users, organizations, organizationMembers } from "@/lib/db/schema";
import { eq, desc } from "drizzle-orm";
import { z } from "zod";
import { createId } from "@paralleldrive/cuid2";
import { RBACHelpers } from "@/lib/auth/rbac-integration";
import { withAdminSecurity, AdminSecurityUtils } from "@/lib/middleware/admin-security.middleware";
import { hash } from "bcryptjs";

// Enhanced input validation schema
const getUsersQuerySchema = z.object({
  page: z.string().optional().transform(val => val ? parseInt(val) : 1),
  limit: z.string().optional().transform(val => val ? Math.min(parseInt(val), 100) : 20),
  search: z.string().optional(),
  role: z.string().optional(),
  isActive: z.string().optional().transform(val => val === 'true' ? true : val === 'false' ? false : undefined),
  tenantId: z.string().optional().transform(val => val ? parseInt(val) : undefined),
});

// ✅ GET /api/admin/users - Get all users (admin only) with enterprise security
const getUsers = withAdminSecurity(
  async (request: NextRequest, context: any) => {
    const startTime = Date.now();

    try {
      const { session, ip, userAgent, isSuperAdmin, isTenantAdmin } = context;

      // Parse and validate query parameters
      const url = new URL(request.url);
      const queryParams = Object.fromEntries(url.searchParams.entries());
      const validatedQuery = getUsersQuerySchema.parse(queryParams);

      console.log(`🔒 SECURE: User ${session.user.email} accessing users list`, {
        ip,
        userAgent: userAgent.substring(0, 100),
        query: validatedQuery
      });

      // Apply tenant isolation for tenant admins
      let tenantFilter = validatedQuery.tenantId;
      if (!isSuperAdmin && isTenantAdmin) {
        tenantFilter = session.user.tenantId;
        console.log(`🔒 TENANT ISOLATION: Restricting to tenant ${tenantFilter}`);
      }

      // Build secure query with pagination
      const offset = (validatedQuery.page - 1) * validatedQuery.limit;

      let query = db
        .select({
          id: users.id,
          email: users.email,
          name: users.name,
          image: users.image,
          role: users.role,
          credits: users.credits,
          isActive: users.isActive,
          emailVerified: users.emailVerified,
          createdAt: users.createdAt,
          organizationId: users.organizationId,
          organizationName: organizations.name,
          tenantId: users.tenantId,
        })
        .from(users)
        .leftJoin(organizations, eq(users.organizationId, organizations.id))
        .orderBy(desc(users.createdAt))
        .limit(validatedQuery.limit)
        .offset(offset);

      // Apply filters securely
      if (tenantFilter) {
        query = query.where(eq(users.tenantId, tenantFilter));
      }

      const allUsers = await query;

      // Sanitize sensitive data from response
      const sanitizedUsers = allUsers.map(user => ({
        ...user,
        // Remove sensitive fields for non-super admins
        ...(isSuperAdmin ? {} : {
          // Tenant admins can't see certain sensitive data
        })
      }));

      const processingTime = Date.now() - startTime;

      // Security audit log
      AdminSecurityUtils.logSecurityEvent(
        session.user.id,
        'users_list_accessed',
        ip,
        userAgent,
        {
          resultCount: sanitizedUsers.length,
          processingTime,
          filters: validatedQuery
        }
      );

      console.log(`✅ SECURE: Successfully fetched ${sanitizedUsers.length} users in ${processingTime}ms`);

      return NextResponse.json({
        success: true,
        data: sanitizedUsers,
        pagination: {
          page: validatedQuery.page,
          limit: validatedQuery.limit,
          total: sanitizedUsers.length
        },
        meta: {
          processingTime,
          securityLevel: 'enterprise'
        }
      });

    } catch (error) {
      console.error("🚨 SECURITY ERROR in users list:", error);

      // Security audit log for errors
      AdminSecurityUtils.logSecurityEvent(
        context.session?.user?.id || 'unknown',
        'users_list_error',
        context.ip,
        context.userAgent,
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          processingTime: Date.now() - startTime
        }
      );

      return NextResponse.json(
        {
          success: false,
          error: "Failed to fetch users",
          code: "USERS_FETCH_ERROR"
        },
        { status: 500 }
      );
    }
  },
  {
    requiredPermissions: ['users.read'],
    sensitiveOperation: false
  }
);

export { getUsers as GET };

// Enhanced schema for create user validation with security constraints
const createUserSchema = z.object({
  email: z.string()
    .email("Invalid email format")
    .max(255, "Email too long")
    .refine(email => !email.includes('<') && !email.includes('>'), "Invalid email format"),
  name: z.string()
    .min(1, "Name is required")
    .max(100, "Name too long")
    .refine(name => !/[<>\"'&]/.test(name), "Name contains invalid characters"),
  role: z.string()
    .min(1, "Role is required")
    .max(50, "Role name too long")
    .refine(role => /^[a-zA-Z0-9_-]+$/.test(role), "Invalid role format"),
  image: z.string()
    .url("Invalid image URL")
    .max(500, "Image URL too long")
    .optional(),
  organizationId: z.string()
    .max(50, "Organization ID too long")
    .optional(),
  tenantId: z.number()
    .int("Tenant ID must be integer")
    .positive("Tenant ID must be positive")
    .optional(),
  password: z.string()
    .min(8, "Password must be at least 8 characters")
    .max(128, "Password too long")
    .refine(pwd => /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(pwd),
      "Password must contain uppercase, lowercase, and number")
    .optional(),
});

// ✅ POST /api/admin/users - Create new user with enterprise security
const createUser = withAdminSecurity(
  async (request: NextRequest, context: any) => {
    const startTime = Date.now();

    try {
      const { session, ip, userAgent, isSuperAdmin, isTenantAdmin } = context;

      console.log(`🔒 SECURE: User ${session.user.email} creating new user`, {
        ip,
        userAgent: userAgent.substring(0, 100)
      });

      // Parse and validate request body
      const body = await request.json();
      const validatedData = createUserSchema.parse(body);

      // Additional security validations
      if (!isSuperAdmin && validatedData.role === 'super_admin') {
        AdminSecurityUtils.logSecurityEvent(
          session.user.id,
          'unauthorized_superadmin_creation_attempt',
          ip,
          userAgent,
          { attemptedRole: validatedData.role }
        );

        return NextResponse.json(
          {
            success: false,
            error: "Cannot create super admin users",
            code: "INSUFFICIENT_PRIVILEGES"
          },
          { status: 403 }
        );
      }

      // Tenant isolation for tenant admins
      if (!isSuperAdmin && isTenantAdmin) {
        validatedData.tenantId = session.user.tenantId;
        console.log(`🔒 TENANT ISOLATION: Setting tenant to ${validatedData.tenantId}`);
      }

      // Check if email already exists (with tenant isolation)
      let emailCheckQuery = db
        .select({ id: users.id, email: users.email, tenantId: users.tenantId })
        .from(users)
        .where(eq(users.email, validatedData.email))
        .limit(1);

      const existingUsers = await emailCheckQuery;

      if (existingUsers.length > 0) {
        const existingUser = existingUsers[0];

        // For tenant admins, check if user exists in their tenant
        if (!isSuperAdmin && existingUser.tenantId === validatedData.tenantId) {
          AdminSecurityUtils.logSecurityEvent(
            session.user.id,
            'duplicate_email_attempt',
            ip,
            userAgent,
            { email: validatedData.email, tenantId: validatedData.tenantId }
          );

          return NextResponse.json(
            {
              success: false,
              error: "User with this email already exists in your organization",
              code: "EMAIL_EXISTS"
            },
            { status: 409 }
          );
        }

        // For super admins, check global uniqueness
        if (isSuperAdmin) {
          return NextResponse.json(
            {
              success: false,
              error: "User with this email already exists",
              code: "EMAIL_EXISTS_GLOBAL"
            },
            { status: 409 }
          );
        }
      }

      // Hash password if provided
      let hashedPassword = null;
      if (validatedData.password) {
        hashedPassword = await hash(validatedData.password, 12);
      }

      // Create new user with security audit
      const newUserId = createId();
      const newUser = {
        id: newUserId,
        email: validatedData.email,
        name: validatedData.name,
        role: validatedData.role,
        image: validatedData.image || null,
        organizationId: validatedData.organizationId || null,
        tenantId: validatedData.tenantId || null,
        password: hashedPassword,
        credits: 0,
        isActive: true,
        emailVerified: null,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Execute database transaction with audit logging
      const [createdUser] = await db
        .insert(users)
        .values(newUser)
        .returning();

      const processingTime = Date.now() - startTime;

      // Security audit log for successful user creation
      AdminSecurityUtils.logSecurityEvent(
        session.user.id,
        'user_created',
        ip,
        userAgent,
        {
          createdUserId: createdUser.id,
          createdUserEmail: createdUser.email,
          createdUserRole: createdUser.role,
          tenantId: createdUser.tenantId,
          processingTime
        }
      );

      // Sanitize response (remove sensitive data)
      const sanitizedUser = {
        id: createdUser.id,
        email: createdUser.email,
        name: createdUser.name,
        role: createdUser.role,
        image: createdUser.image,
        organizationId: createdUser.organizationId,
        tenantId: createdUser.tenantId,
        credits: createdUser.credits,
        isActive: createdUser.isActive,
        emailVerified: createdUser.emailVerified,
        createdAt: createdUser.createdAt,
        // Exclude password hash and other sensitive fields
      };

      console.log(`✅ SECURE: User created successfully in ${processingTime}ms`);

      return NextResponse.json({
        success: true,
        data: sanitizedUser,
        meta: {
          processingTime,
          securityLevel: 'enterprise'
        }
      }, { status: 201 });

    } catch (error) {
      const processingTime = Date.now() - startTime;

      console.error("🚨 SECURITY ERROR in user creation:", error);

      // Security audit log for errors
      AdminSecurityUtils.logSecurityEvent(
        context.session?.user?.id || 'unknown',
        'user_creation_error',
        context.ip,
        context.userAgent,
        {
          error: error instanceof Error ? error.message : 'Unknown error',
          processingTime,
          requestData: validatedData ? { email: validatedData.email, role: validatedData.role } : 'invalid'
        }
      );

      if (error instanceof z.ZodError) {
        return NextResponse.json(
          {
            success: false,
            error: "Validation failed",
            details: error.errors.map(err => ({
              field: err.path.join('.'),
              message: err.message
            })),
            code: "VALIDATION_ERROR"
          },
          { status: 400 }
        );
      }

      return NextResponse.json(
        {
          success: false,
          error: "Failed to create user",
          code: "USER_CREATION_ERROR"
        },
        { status: 500 }
      );
    }
  },
  {
    requiredPermissions: ['users.create'],
    sensitiveOperation: true
  }
);

export { createUser as POST };
