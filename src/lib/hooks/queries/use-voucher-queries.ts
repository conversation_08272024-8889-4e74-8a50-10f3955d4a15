import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { createE<PERSON>tyH<PERSON>s, BaseApiInterface } from "@/lib/core/base-query-hooks";
import { QueryOptions } from "@/lib/core/base-service";
import { 
  Voucher, 
  NewVoucher, 
  VoucherUsage,
  type VoucherRestrictionsData 
} from "@/lib/db/schema";
import { 
  CreateVoucherData, 
  UpdateVoucherData, 
  VoucherFilters,
  VoucherUsageData,
  VoucherValidationResult
} from "@/lib/services/voucher.service";

// API interface implementation for vouchers
const voucherApi: BaseApiInterface<Voucher, CreateVoucherData, UpdateVoucherData> = {
  getAll: async (options?: QueryOptions): Promise<Voucher[]> => {
    const params = new URLSearchParams();
    if (options?.tenantId) params.append('tenantId', options.tenantId.toString());
    if (options?.limit) params.append('limit', options.limit.toString());
    if (options?.offset) params.append('offset', options.offset.toString());
    if (options?.filters?.search) params.append('search', options.filters.search);
    if (options?.filters?.type) params.append('type', options.filters.type);
    if (options?.filters?.is_active !== undefined) params.append('isActive', options.filters.is_active.toString());
    if (options?.filters?.is_public !== undefined) params.append('isPublic', options.filters.is_public.toString());
    if (options?.filters?.valid_only) params.append('validOnly', 'true');

    const response = await fetch(`/api/vouchers?${params}`);
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to fetch vouchers');
    }
    const result = await response.json();
    return result.data;
  },

  getById: async (id: string): Promise<Voucher> => {
    const response = await fetch(`/api/vouchers/${id}`);
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to fetch voucher');
    }
    const result = await response.json();
    return result.data;
  },

  getByTenant: async (tenantId: number, options?: QueryOptions): Promise<Voucher[]> => {
    return voucherApi.getAll({ ...options, tenantId });
  },

  create: async (data: CreateVoucherData): Promise<Voucher> => {
    const response = await fetch('/api/vouchers', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to create voucher');
    }
    const result = await response.json();
    return result.data;
  },

  update: async ({ id, data }: { id: string; data: UpdateVoucherData }): Promise<Voucher> => {
    const response = await fetch(`/api/vouchers/${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to update voucher');
    }
    const result = await response.json();
    return result.data;
  },

  delete: async (id: string): Promise<Voucher> => {
    const response = await fetch(`/api/vouchers/${id}`, {
      method: 'DELETE',
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to delete voucher');
    }
    const result = await response.json();
    return result.data;
  },

  bulkOperation: async (data: any): Promise<Voucher[]> => {
    const response = await fetch('/api/vouchers/bulk', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to perform bulk operation');
    }
    const result = await response.json();
    return result.data;
  },

  search: async (query: string, options?: QueryOptions): Promise<Voucher[]> => {
    return voucherApi.getAll({ ...options, filters: { search: query } });
  },

  getStats: async (tenantId?: number): Promise<any> => {
    const params = new URLSearchParams();
    if (tenantId) params.append('tenantId', tenantId.toString());
    
    const response = await fetch(`/api/vouchers/stats?${params}`);
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to fetch voucher stats');
    }
    const result = await response.json();
    return result.data;
  },
};

// Additional voucher-specific API functions
const voucherSpecificApi = {
  validateVoucher: async (
    code: string, 
    customerId: string, 
    tenantId: number, 
    orderAmount: number,
    context: { package_id?: string; class_id?: string; location_id?: string } = {}
  ): Promise<VoucherValidationResult> => {
    const response = await fetch('/api/vouchers/validate', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ code, customerId, tenantId, orderAmount, context }),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to validate voucher');
    }
    const result = await response.json();
    return result.data;
  },

  recordUsage: async (data: VoucherUsageData): Promise<VoucherUsage> => {
    const response = await fetch('/api/vouchers/usage', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to record voucher usage');
    }
    const result = await response.json();
    return result.data;
  },

  getAvailableForCustomer: async (customerId: string, tenantId: number): Promise<Voucher[]> => {
    const params = new URLSearchParams();
    params.append('customerId', customerId);
    params.append('tenantId', tenantId.toString());
    
    const response = await fetch(`/api/vouchers/available?${params}`);
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to fetch available vouchers');
    }
    const result = await response.json();
    return result.data;
  },

  assignToCustomer: async (
    voucherId: string, 
    customerId: string, 
    assignedBy: string,
    expiresAt?: Date
  ): Promise<any> => {
    const response = await fetch(`/api/vouchers/${voucherId}/assign`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ customerId, assignedBy, expiresAt }),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to assign voucher');
    }
    const result = await response.json();
    return result.data;
  },
  

  getVoucherStats: async (voucherId: string): Promise<any> => {
    const response = await fetch(`/api/vouchers/${voucherId}/stats`);
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to fetch voucher statistics');
    }
    const result = await response.json();
    return result.data;
  },

  findByCode: async (code: string, tenantId: number): Promise<Voucher | null> => {
    const params = new URLSearchParams();
    params.append('code', code);
    params.append('tenantId', tenantId.toString());
    
    const response = await fetch(`/api/vouchers/find-by-code?${params}`);
    if (!response.ok) {
      if (response.status === 404) return null;
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to find voucher');
    }
    const result = await response.json();
    return result.data;
  },
};

// Create base hooks using the factory
const baseHooks = createEntityHooks('vouchers', voucherApi);

// Export base hooks with voucher-specific names
export const useVouchers = baseHooks.useEntities;
export const useVoucher = baseHooks.useEntity;
export const useVouchersByTenant = baseHooks.useEntitiesByTenant;
export const useVoucherStats = baseHooks.useEntityStats;
export const useVoucherSearch = baseHooks.useEntitySearch;
export const useCreateVoucher = baseHooks.useCreateEntity;
export const useUpdateVoucher = baseHooks.useUpdateEntity;
export const useDeleteVoucher = baseHooks.useDeleteEntity;
export const useBulkVoucherOperation = baseHooks.useBulkOperation;

// Voucher-specific hooks
export function useValidateVoucher() {
  return useMutation({
    mutationFn: voucherSpecificApi.validateVoucher,
    onError: (error) => {
      console.error('Voucher validation error:', error);
    },
  });
}

export function useRecordVoucherUsage() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: voucherSpecificApi.recordUsage,
    onSuccess: (data) => {
      // Invalidate voucher queries to refresh usage counts
      queryClient.invalidateQueries({ queryKey: ['vouchers'] });
      queryClient.invalidateQueries({ queryKey: ['voucher', data.voucher_id] });
    },
    onError: (error) => {
      console.error('Record voucher usage error:', error);
    },
  });
}

export function useAvailableVouchersForCustomer(customerId: string, tenantId: number) {
  return useQuery({
    queryKey: ['vouchers', 'available', customerId, tenantId],
    queryFn: () => voucherSpecificApi.getAvailableForCustomer(customerId, tenantId),
    enabled: !!customerId && !!tenantId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useAssignVoucherToCustomer() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: voucherSpecificApi.assignToCustomer,
    onSuccess: (data, variables) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['vouchers'] });
      queryClient.invalidateQueries({ queryKey: ['vouchers', 'available', variables.customerId] });
    },
    onError: (error) => {
      console.error('Assign voucher error:', error);
    },
  });
}

export function useVoucherStatistics(voucherId: string) {
  return useQuery({
    queryKey: ['voucher', voucherId, 'stats'],
    queryFn: () => voucherSpecificApi.getVoucherStats(voucherId),
    enabled: !!voucherId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

export function useFindVoucherByCode(code: string, tenantId: number) {
  return useQuery({
    queryKey: ['voucher', 'find-by-code', code, tenantId],
    queryFn: () => voucherSpecificApi.findByCode(code, tenantId),
    enabled: !!code && !!tenantId,
    staleTime: 1 * 60 * 1000, // 1 minute
  });
}

export function useToggleVoucherActive() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string): Promise<Voucher> => {
      const response = await fetch(`/api/vouchers/${id}?action=toggle-active`, {
        method: 'PATCH',
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to toggle voucher status');
      }
      const result = await response.json();
      return result.data;
    },
    onSuccess: (data) => {
      // Update the voucher in cache
      queryClient.setQueryData(['voucher', data.id], data);
      // Invalidate voucher lists
      queryClient.invalidateQueries({ queryKey: ['vouchers'] });
    },
    onError: (error) => {
      console.error('Toggle voucher active error:', error);
    },
  });
}

// Export query keys for external use
export const voucherQueryKeys = baseHooks.queryKeys;
