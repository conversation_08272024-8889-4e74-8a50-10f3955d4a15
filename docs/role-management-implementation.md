# 🎯 Comprehensive Role Management Interface + Calendar Integration Fix - Dokumentasi Lengkap

> **"Membangun sistem RBAC yang powerful tapi tetap user-friendly itu seperti membuat remote TV yang canggih tapi mudah digunakan oleh semua orang di rumah!"** 📺

## 📋 Table of Contents

1. [Overview](#-overview)
2. [Fitur-fitur Utama](#-fitur-fitur-utama)
3. [Arsitektur & Pola Design](#-arsitektur--pola-design)
4. [Calendar Integration Fix](#-calendar-integration-fix)
5. [Step-by-Step Implementation Guide](#-step-by-step-implementation-guide)
6. [Troubleshooting](#-troubleshooting)
7. [Code Examples](#-code-examples)
8. [Best Practices](#-best-practices)
9. [Testing Guide](#-testing-guide)

---

## 🌟 Overview

Halo teman-teman developer! 👋 

Kita baru saja berhasil mengimplementasikan **Comprehensive Role Management Interface** yang memungkinkan Super Admin dan Tenant Admin untuk mengelola roles dan permissions secara penuh melalui UI yang user-friendly. 

### Apa yang Telah Kita Capai? 🎉

- ✅ **UI-based Role Management** - Tidak perlu lagi edit database manual!
- ✅ **Dynamic Permission Creation** - Buat permissions baru on-the-fly
- ✅ **Professional Forms** - Menggunakan shadcn/ui components yang cantik
- ✅ **Real-time Validation** - Error handling yang comprehensive
- ✅ **RBAC Integration** - Terintegrasi penuh dengan sistem permission yang ada
- ✅ **Tenant Isolation** - Setiap tenant punya data terpisah dan aman

### Mengapa Ini Penting? 🤔

Bayangkan kalau setiap kali mau tambah user baru atau ubah permission, kita harus buka database dan edit manual. Ribet banget kan? Dengan interface ini, admin bisa manage semua hal terkait roles dan permissions dengan mudah, seperti mengatur channel TV dengan remote - tinggal klik-klik aja! 📱

---

## 🚀 Fitur-fitur Utama

### 1. **Role Management Interface** 👥
- **Create/Edit/Delete Roles** - Kelola roles dengan form yang intuitif
- **Role Hierarchy** - Support untuk system roles dan tenant-specific roles
- **Permission Assignment** - Assign/revoke permissions via checkboxes
- **Bulk Operations** - Manage multiple permissions sekaligus

### 2. **Permission Management Interface** 🔐
- **Dynamic Permission Creation** - Buat permissions baru dengan module.action pattern
- **Permission Grouping** - Permissions dikelompokkan berdasarkan module
- **Validation System** - Prevent duplicate permissions
- **System Permission Protection** - Tidak bisa hapus permissions penting

### 3. **User Experience Features** ✨
- **Professional UI** - Menggunakan shadcn/ui components
- **Real-time Feedback** - Toast notifications untuk setiap action
- **Search & Filter** - Cari roles/permissions dengan mudah
- **Responsive Design** - Works di mobile dan desktop
- **Loading States** - User tahu kapan system sedang processing

### 4. **Security & Validation** 🛡️
- **RBAC Protection** - Setiap action dicek permission-nya
- **Tenant Isolation** - Data antar tenant tidak bisa tercampur
- **Input Validation** - Comprehensive validation di frontend dan backend
- **Error Boundaries** - Graceful error handling

---

## 📅 Calendar Integration Fix

### Masalah yang Ditemukan 🐛

Sebelumnya, ada bug kritis pada Class Schedule Form terkait integrasi dengan calendar picker:

**Problem Description:**
1. User berada dalam mode calendar view untuk Class Schedules
2. User mengklik salah satu tanggal di calendar component
3. Form "Create Class Schedule" muncul/terbuka
4. ❌ **Field "Start Date" di form tersebut TIDAK ter-populate** dengan tanggal yang dipilih dari calendar
5. Field "Start Date" tetap kosong/empty, padahal seharusnya otomatis terisi

### Root Cause Analysis 🔍

Mari kita analisis mengapa ini terjadi:

```typescript
// ❌ MASALAH: Form tidak menerima selectedDate sebagai prop
<ClassScheduleForm
  tenantId={tenantId}
  onSubmit={handleCreateSchedule}
  onCancel={() => setIsCreateDialogOpen(false)}
  isLoading={createMutation.isPending}
  // Missing: selectedDate dan selectedTime props!
/>
```

**Mengapa ini terjadi?** 🤔

Bayangkan calendar seperti remote TV dan form seperti TV-nya. Ketika kita pencet tombol channel di remote (klik tanggal di calendar), remote sudah "tahu" channel mana yang dipilih, tapi TV-nya (form) tidak "dengar" sinyal dari remote karena tidak ada koneksi yang proper!

### Solusi yang Diimplementasikan ✅

#### 1. **Enhanced Form Props Interface**
```typescript
interface ClassScheduleFormProps {
  tenantId: number;
  initialData?: ClassSchedule;
  selectedDate?: Date; // 🆕 Tanggal yang dipilih dari calendar
  selectedTime?: string; // 🆕 Waktu yang dipilih dari calendar (format HH:mm)
  onSubmit: (data: ClassScheduleFormData) => Promise<void>;
  onCancel: () => void;
  isLoading?: boolean;
}
```

#### 2. **Smart Default Value Helpers**
```typescript
// Helper function untuk format tanggal dari calendar selection
const getDefaultStartDate = () => {
  if (initialData?.start_date) {
    return initialData.start_date; // Edit mode: gunakan data existing
  }
  if (selectedDate) {
    return selectedDate.toISOString().split('T')[0]; // Create mode: gunakan tanggal dari calendar
  }
  return ""; // Fallback: empty
};

// Helper function untuk format start time dari calendar selection
const getDefaultStartTime = () => {
  if (initialData?.start_time) {
    return new Date(initialData.start_time).toISOString().slice(0, 16);
  }
  if (selectedDate && selectedTime) {
    const dateStr = selectedDate.toISOString().split('T')[0];
    return `${dateStr}T${selectedTime}`; // Combine date + time
  }
  return "";
};
```

#### 3. **Updated Calendar Component Integration**
```typescript
// ✅ FIXED: Calendar sekarang mem-pass selectedDate dan selectedTime
<ClassScheduleForm
  tenantId={tenantId}
  selectedDate={createDate || undefined} // Pass tanggal yang dipilih
  selectedTime={createTime || undefined} // Pass waktu yang dipilih
  onSubmit={handleCreateSchedule}
  onCancel={() => {
    setIsCreateDialogOpen(false);
    setCreateDate(null);
    setCreateTime(null);
  }}
  isLoading={createMutation.isPending}
/>
```

### Workflow Setelah Fix 🎯

```
1. User klik tanggal di calendar (misal: 15 Januari 2025, 10:00 AM)
   ↓
2. Calendar component set state:
   - createDate = new Date('2025-01-15')
   - createTime = '10:00'
   ↓
3. Dialog form terbuka dengan props:
   - selectedDate = Date object untuk 15 Jan 2025
   - selectedTime = '10:00'
   ↓
4. Form helper functions bekerja:
   - getDefaultStartDate() → '2025-01-15'
   - getDefaultStartTime() → '2025-01-15T10:00'
   ↓
5. ✅ Form fields ter-populate otomatis!
   - Start Date field = '2025-01-15'
   - Start Time field = '2025-01-15T10:00'
```

### Benefits dari Fix Ini 🚀

1. **Better UX** - User tidak perlu input tanggal manual lagi
2. **Reduced Errors** - Tidak ada typo atau salah tanggal
3. **Faster Workflow** - Click-to-create yang seamless
4. **Consistent Behavior** - Sama seperti Google Calendar atau Outlook

---

## 🏗️ Arsitektur & Pola Design

### Modular Architecture Pattern 📦

Kita menggunakan pola arsitektur modular yang sudah terbukti di fitur-fitur lain. Ini seperti sistem LEGO - setiap piece punya fungsi spesifik tapi bisa dikombinasikan dengan mudah!

```
📁 Role Management Architecture
├── 🎨 UI Components (Reusable)
│   ├── EnhancedRoleManagement (Main Interface)
│   ├── PermissionManagement (Permission CRUD)
│   ├── EnhancedRoleForm (Role Form with Permission Assignment)
│   └── PermissionForm (Dynamic Permission Creation)
│
├── 🔧 Services (Business Logic)
│   ├── RoleService (Role CRUD Operations)
│   └── PermissionService (Permission CRUD Operations)
│
├── 🌐 API Routes (Backend Endpoints)
│   ├── /api/roles (Role Management)
│   ├── /api/permissions (Permission Management)
│   └── /api/permissions/grouped (Grouped Permissions)
│
├── 🎣 Query Hooks (State Management)
│   ├── useRoles, useCreateRole, useUpdateRole
│   └── usePermissions, useCreatePermission, useUpdatePermission
│
└── 🛡️ RBAC Middleware (Security Layer)
    └── withRBAC (Permission Checking)
```

### Mengapa Pola Ini Bagus? 💡

1. **Reusability** - Components bisa dipakai ulang di tempat lain
2. **Maintainability** - Mudah di-maintain karena setiap bagian punya tanggung jawab jelas
3. **Scalability** - Gampang ditambah fitur baru tanpa rusak yang lama
4. **Testability** - Setiap layer bisa di-test secara terpisah

---

## 📝 Step-by-Step Implementation Guide

### Phase 1: Setup Foundation 🏗️

#### 1.1 Database Schema Enhancement
```sql
-- Pastikan tabel permissions dan roles sudah ada
-- Tambahkan permissions untuk module "permissions"
INSERT INTO permissions (module, action, display_name, description) VALUES
('permissions', 'create', 'Create Permissions', 'Create new permissions'),
('permissions', 'read', 'View Permissions', 'View permission information'),
('permissions', 'update', 'Update Permissions', 'Update permission information'),
('permissions', 'delete', 'Delete Permissions', 'Delete permissions'),
('permissions', 'manage', 'Manage Permissions', 'Full permission management');
```

#### 1.2 RBAC Seeder Update
```typescript
// Update src/lib/db/seed-rbac.ts
const rolePermissionMappings = {
  super_admin: [
    "system.manage",
    "permissions.create", "permissions.read", 
    "permissions.update", "permissions.delete", "permissions.manage",
    // ... other permissions
  ],
  // ... other roles
};
```

### Phase 2: Backend Services 🔧

#### 2.1 Enhanced Permission Service
```typescript
// src/lib/services/permission.service.ts
export class PermissionService {
  // Method untuk check existing permission
  static async getByModuleAndAction(module: string, action: string) {
    // Implementation untuk prevent duplicate permissions
  }
  
  // CRUD methods dengan proper validation
  static async create(data: PermissionFormData) {
    // Validation + creation logic
  }
}
```

#### 2.2 API Routes Implementation
```typescript
// src/app/api/permissions/route.ts
export const POST = withRBAC(
  async (request: NextRequest) => {
    // Validation, duplicate check, creation
  },
  { module: "permissions", action: "create" }
);
```

### Phase 3: Frontend Components 🎨

#### 3.1 Main Management Interface
```typescript
// src/components/role-management/enhanced-role-management.tsx
export function EnhancedRoleManagement({ tenantId }: Props) {
  // Tab-based interface untuk roles dan permissions
  // Integration dengan TanStack Query untuk data fetching
}
```

#### 3.2 Form Components
```typescript
// Reusable form components dengan validation
// Integration dengan react-hook-form dan zod
```

### Phase 4: State Management 🎣

#### 4.1 TanStack Query Hooks
```typescript
// src/lib/hooks/queries/use-permission-queries.ts
export function useCreatePermission() {
  return useMutation({
    mutationFn: async (data: PermissionFormData) => {
      // API call dengan error handling
    },
    onSuccess: () => {
      // Invalidate related queries
    },
  });
}
```

---

## 🚨 Troubleshooting

### Issue #1: Calendar Date Not Populating Form Fields ❌

**Problem:** User mengklik tanggal di calendar, tapi form "Start Date" tetap kosong.

**Root Cause:** Form component tidak menerima `selectedDate` dan `selectedTime` sebagai props dari calendar component.

**Solution:** ✅
```typescript
// ❌ Before: Missing props
<ClassScheduleForm
  tenantId={tenantId}
  onSubmit={handleCreateSchedule}
  // Missing selectedDate and selectedTime!
/>

// ✅ After: Complete props
<ClassScheduleForm
  tenantId={tenantId}
  selectedDate={createDate || undefined}
  selectedTime={createTime || undefined}
  onSubmit={handleCreateSchedule}
/>
```

**Debug Steps:** 🔧
1. Check apakah calendar component menyimpan `createDate` dan `createTime` dengan benar
2. Verify props passing dari calendar ke form
3. Test helper functions `getDefaultStartDate()` dan `getDefaultStartTime()`
4. Ensure form `defaultValues` menggunakan helper functions

### Issue #2: "roles.filter is not a function" ❌

**Problem:** Error terjadi karena `useRoles()` hook mengembalikan object dengan struktur `{ roles: [], total: number }` tapi component expect array langsung.

**Solution:** ✅
```typescript
// ❌ Wrong way
const { data: roles = [] } = useRoles();

// ✅ Correct way  
const { data: rolesData } = useRoles();
const roles = rolesData?.roles || [];
```

**Mengapa ini terjadi?** 
Service method `searchRoles` mengembalikan object dengan metadata (total, hasMore), bukan array langsung. Ini pattern yang bagus untuk pagination, tapi perlu di-handle dengan benar di frontend.

### Issue #2: "Permission Denied" saat Create Permission ❌

**Problem:** User dengan role super_admin tidak bisa create permission karena missing `permissions.create` permission.

**Root Cause Analysis:** 🔍
1. RBAC seeder menggunakan `onConflictDoNothing()` 
2. Ketika permissions sudah exist, seeder tidak insert ulang
3. Tapi `permissionIds` mapping tetap dibuat dengan ID baru
4. Role-permission mapping menggunakan ID yang salah

**Solution:** ✅
```typescript
// Fix di seeder: gunakan existing permission ID
const existingPermission = await db
  .select()
  .from(permissions)
  .where(and(
    eq(permissions.module, permData.module),
    eq(permissions.action, permData.action)
  ))
  .limit(1);

let permissionId: string;
if (existingPermission.length > 0) {
  permissionId = existingPermission[0].id; // Use existing ID
} else {
  permissionId = createId(); // Create new ID
  // Insert new permission
}
```

**Prevention Tips:** 🛡️
- Selalu test RBAC seeder di clean database
- Verify permission mappings setelah seeding
- Use debug scripts untuk check user permissions

### Issue #3: Form Time Field Not Combining Date and Time Properly ❌

**Problem:** Ketika user pilih tanggal dan waktu dari calendar, time field tidak ter-format dengan benar.

**Root Cause:** Format datetime-local input membutuhkan format `YYYY-MM-DDTHH:mm`, tapi kita hanya pass time saja.

**Solution:** ✅
```typescript
const getDefaultStartTime = () => {
  if (initialData?.start_time) {
    return new Date(initialData.start_time).toISOString().slice(0, 16);
  }
  if (selectedDate && selectedTime) {
    const dateStr = selectedDate.toISOString().split('T')[0];
    return `${dateStr}T${selectedTime}`; // Combine date + time properly
  }
  return "";
};
```

**Prevention Tips:** 🛡️
- Selalu test dengan berbagai kombinasi tanggal dan waktu
- Verify format datetime-local input requirements
- Use browser dev tools untuk check actual input values

### Issue #4: Session Cache Not Refreshing ❌

**Problem:** Setelah update permissions di database, user session masih show permissions lama.

**Solution:** ✅
- Logout dan login ulang untuk refresh session
- Atau implement session refresh mechanism
- Clear browser cache jika perlu

---

## 💻 Code Examples

### Example 1: Calendar Date Selection Integration

```typescript
// Calendar component - handling date click
const handleDateClick = useCallback((date: Date, time?: string) => {
  setCreateDate(date); // Store selected date
  setCreateTime(time || null); // Store selected time
  setIsCreateDialogOpen(true); // Open form dialog
  onEventCreate?.(date, time); // Optional callback
}, [onEventCreate]);

// Form component - using selected date/time
const getDefaultStartDate = () => {
  if (initialData?.start_date) {
    return initialData.start_date; // Edit mode
  }
  if (selectedDate) {
    return selectedDate.toISOString().split('T')[0]; // Create mode from calendar
  }
  return ""; // Empty fallback
};

// Form setup with smart defaults
const form = useForm<ClassScheduleFormValues>({
  resolver: zodResolver(classScheduleFormSchema),
  defaultValues: {
    start_date: getDefaultStartDate(), // Auto-populate from calendar
    start_time: getDefaultStartTime(), // Auto-populate from calendar
    // ... other fields
  },
});
```

### Example 2: Complete Calendar-to-Form Integration

```typescript
// Parent component (Calendar Management)
export function ClassScheduleCalendar({ tenantId }: Props) {
  const [createDate, setCreateDate] = useState<Date | null>(null);
  const [createTime, setCreateTime] = useState<string | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);

  // Handle calendar date/time selection
  const handleDateClick = useCallback((date: Date, time?: string) => {
    console.log("📅 Date clicked:", date, "⏰ Time:", time);
    setCreateDate(date);
    setCreateTime(time || null);
    setIsCreateDialogOpen(true);
  }, []);

  return (
    <>
      {/* Calendar Views */}
      <MonthView onDateClick={handleDateClick} />

      {/* Create Dialog with Date Pre-population */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              Create New Class Schedule
              {createDate && (
                <span className="text-sm font-normal text-muted-foreground ml-2">
                  for {createDate.toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </span>
              )}
            </DialogTitle>
          </DialogHeader>

          {/* Form with pre-populated date/time */}
          <ClassScheduleForm
            tenantId={tenantId}
            selectedDate={createDate || undefined} // 🎯 Key fix!
            selectedTime={createTime || undefined} // 🎯 Key fix!
            onSubmit={handleCreateSchedule}
            onCancel={() => {
              setIsCreateDialogOpen(false);
              setCreateDate(null); // Reset state
              setCreateTime(null); // Reset state
            }}
          />
        </DialogContent>
      </Dialog>
    </>
  );
}
```


```

```

### Example 3: Creating New Permission

```typescript
// Frontend usage
const createPermission = useCreatePermission();

const handleSubmit = async (data: PermissionFormData) => {
  try {
    await createPermission.mutateAsync({
      module: "bookings",
      action: "cancel",
      display_name: "Cancel Bookings",
      description: "Allow canceling customer bookings"
    });

    toast.success("Permission created successfully! 🎉");
  } catch (error) {
    toast.error("Failed to create permission 😞");
  }
};
```

### Example 4: Assigning Permissions to Role

```typescript
// Role form dengan permission assignment
const selectedPermissions = ["bookings.create", "bookings.read", "bookings.cancel"];

const handleRoleSubmit = async (roleData) => {
  // Create role first
  const newRole = await createRole.mutateAsync(roleData);
  
  // Then assign permissions
  for (const permissionKey of selectedPermissions) {
    await assignPermissionToRole.mutateAsync({
      roleId: newRole.id,
      permissionId: getPermissionId(permissionKey)
    });
  }
};
```

### Example 5: Permission Checking in Components

```typescript
// Conditional rendering berdasarkan permission
const { hasPermission } = useRBAC();

return (
  <div>
    {hasPermission("permissions.create") && (
      <Button onClick={openCreatePermissionModal}>
        Create New Permission
      </Button>
    )}
    
    {hasPermission("roles.delete") && (
      <Button variant="destructive" onClick={deleteRole}>
        Delete Role
      </Button>
    )}
  </div>
);
```

---

## 🎯 Best Practices

### 1. **Calendar-Form Integration Pattern** 📅
```typescript
// ✅ Good: Always pass selected date/time to form
<ClassScheduleForm
  selectedDate={calendarSelectedDate}
  selectedTime={calendarSelectedTime}
  // ... other props
/>

// ✅ Good: Use helper functions for default values
const getDefaultStartDate = () => {
  if (initialData?.start_date) return initialData.start_date; // Edit mode
  if (selectedDate) return selectedDate.toISOString().split('T')[0]; // Create mode
  return ""; // Fallback
};

// ❌ Bad: Hard-coded default values
const form = useForm({
  defaultValues: {
    start_date: "", // Always empty, ignores calendar selection
    start_time: "", // Always empty, ignores calendar selection
  }
});
```

### 2. **Permission Naming Convention** 📝
```typescript
// ✅ Good: module.action format
"bookings.create"
"users.read" 
"reports.export"

// ❌ Bad: inconsistent naming
"create_booking"
"readUser"
"EXPORT_REPORTS"
```

### 3. **Error Handling Pattern** 🛡️
```typescript
// ✅ Comprehensive error handling
try {
  const result = await apiCall();
  toast.success("Operation successful! 🎉");
  return result;
} catch (error: any) {
  console.error("Operation failed:", error);
  
  if (error.name === "ZodError") {
    toast.error("Invalid input data 📝");
  } else if (error.status === 403) {
    toast.error("Permission denied 🚫");
  } else {
    toast.error("Something went wrong 😞");
  }
  
  throw error;
}
```

### 4. **Component Composition** 🧩
```typescript
// ✅ Reusable components dengan clear props
interface RoleFormProps {
  role?: Role;
  onSubmit: (data: RoleFormData) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

// ✅ Single responsibility principle
const RoleForm = ({ role, onSubmit, onCancel, isLoading }: RoleFormProps) => {
  // Form logic only, no API calls
};

const RoleManagement = () => {
  // API calls dan state management
  const createRole = useCreateRole();
  
  return (
    <RoleForm 
      onSubmit={createRole.mutateAsync}
      isLoading={createRole.isLoading}
    />
  );
};
```

### 5. **Database Transaction Safety** 💾
```typescript
// ✅ Use transactions untuk operations yang complex
const createRoleWithPermissions = async (roleData, permissionIds) => {
  return await db.transaction(async (tx) => {
    // Create role
    const role = await tx.insert(roles).values(roleData);
    
    // Assign permissions
    for (const permissionId of permissionIds) {
      await tx.insert(role_permissions).values({
        roleId: role.id,
        permissionId
      });
    }
    
    return role;
  });
};
```

---

## 🧪 Testing Guide

### 1. **Manual Testing Checklist** ✅

#### Calendar Integration Testing:
- [ ] Klik tanggal di calendar view (month/week/day)
- [ ] Verify form dialog terbuka dengan tanggal yang benar
- [ ] Check field "Start Date" ter-populate otomatis
- [ ] Test dengan berbagai tanggal (hari ini, besok, minggu depan)
- [ ] Test time selection dari calendar (jika ada)
- [ ] Verify field "Start Time" ter-populate dengan benar
- [ ] Test edge cases (tanggal di masa lalu, weekend, holidays)
- [ ] Ensure form reset ketika dialog di-cancel

#### Role Management:
- [ ] Create new role dengan nama unik
- [ ] Edit existing role
- [ ] Delete role (pastikan tidak ada user yang assigned)
- [ ] Assign permissions ke role
- [ ] Revoke permissions dari role
- [ ] Test role hierarchy (system vs tenant roles)

#### Permission Management:
- [ ] Create new permission dengan module.action format
- [ ] Edit permission details
- [ ] Delete custom permission (bukan system permission)
- [ ] Test duplicate prevention
- [ ] Test permission grouping by module

#### Security Testing:
- [ ] Test dengan user yang tidak punya permission
- [ ] Test tenant isolation (tenant A tidak bisa lihat data tenant B)
- [ ] Test session refresh setelah permission changes
- [ ] Test RBAC middleware protection

### 2. **Automated Testing Examples** 🤖

```typescript
// Unit test untuk calendar-form integration
describe("ClassScheduleForm Calendar Integration", () => {
  it("should populate start date from selectedDate prop", () => {
    const selectedDate = new Date('2025-01-15');

    render(
      <ClassScheduleForm
        tenantId={1}
        selectedDate={selectedDate}
        onSubmit={jest.fn()}
        onCancel={jest.fn()}
      />
    );

    const startDateInput = screen.getByLabelText(/start date/i);
    expect(startDateInput.value).toBe('2025-01-15');
  });

  it("should populate start time from selectedDate and selectedTime", () => {
    const selectedDate = new Date('2025-01-15');
    const selectedTime = '10:30';

    render(
      <ClassScheduleForm
        tenantId={1}
        selectedDate={selectedDate}
        selectedTime={selectedTime}
        onSubmit={jest.fn()}
        onCancel={jest.fn()}
      />
    );

    const startTimeInput = screen.getByLabelText(/start time/i);
    expect(startTimeInput.value).toBe('2025-01-15T10:30');
  });

  it("should prioritize initialData over selectedDate in edit mode", () => {
    const selectedDate = new Date('2025-01-15');
    const initialData = { start_date: '2025-01-20' };

    render(
      <ClassScheduleForm
        tenantId={1}
        selectedDate={selectedDate}
        initialData={initialData}
        onSubmit={jest.fn()}
        onCancel={jest.fn()}
      />
    );

    const startDateInput = screen.getByLabelText(/start date/i);
    expect(startDateInput.value).toBe('2025-01-20'); // Should use initialData
  });
});

// Unit test untuk permission service
describe("PermissionService", () => {
  it("should prevent duplicate permissions", async () => {
    // Create permission
    await PermissionService.create({
      module: "test",
      action: "create",
      display_name: "Test Create"
    });
    
    // Try to create duplicate
    await expect(
      PermissionService.create({
        module: "test", 
        action: "create",
        display_name: "Test Create Duplicate"
      })
    ).rejects.toThrow("Permission test.create already exists");
  });
});

// Integration test untuk API
describe("POST /api/permissions", () => {
  it("should create permission with valid data", async () => {
    const response = await request(app)
      .post("/api/permissions")
      .set("Authorization", `Bearer ${superAdminToken}`)
      .send({
        module: "integration-test",
        action: "create",
        display_name: "Integration Test Permission"
      });
      
    expect(response.status).toBe(201);
    expect(response.body.success).toBe(true);
  });
});
```

### 3. **Performance Testing** ⚡

```typescript
// Test dengan large dataset
const testLargePermissionList = async () => {
  // Create 1000 permissions
  const permissions = Array.from({ length: 1000 }, (_, i) => ({
    module: `module-${i}`,
    action: "read",
    display_name: `Permission ${i}`
  }));
  
  const startTime = Date.now();
  
  // Test grouped permissions API
  const response = await fetch("/api/permissions/grouped");
  const data = await response.json();
  
  const endTime = Date.now();
  
  console.log(`Grouped permissions loaded in ${endTime - startTime}ms`);
  expect(endTime - startTime).toBeLessThan(1000); // Should load in < 1 second
};
```

---

## 🎉 Penutup

Selamat! 🎊 Kita telah berhasil mengimplementasikan sistem Role Management yang comprehensive dan production-ready, plus memperbaiki bug kritis pada Calendar Integration!

### Key Takeaways: 📚

1. **Modular Architecture** memudahkan maintenance dan scaling
2. **Proper Error Handling** membuat user experience lebih baik
3. **RBAC Integration** memastikan security yang ketat
4. **Calendar Integration Fix** meningkatkan UX secara signifikan
5. **Smart Default Values** mengurangi manual input dan errors
6. **Comprehensive Testing** mencegah bugs di production

### Calendar Integration Lessons Learned: 🎓

- **Props Passing is Critical** - Selalu pastikan data flow dari parent ke child component
- **Helper Functions** membuat code lebih maintainable dan testable
- **User Experience Matters** - Small details seperti auto-populate fields sangat berpengaruh
- **Debug Systematically** - Trace data flow step by step untuk find root cause

### Next Steps: 🚀

- Implement audit logging untuk track permission changes
- Add bulk operations untuk manage multiple roles sekaligus
- Create role templates untuk common use cases
- Implement role inheritance untuk complex hierarchies
- **Extend calendar integration** ke fitur lain yang membutuhkan date selection
- **Add time zone support** untuk multi-region applications

**Remember:** "Code yang baik bukan hanya yang berfungsi, tapi yang mudah dipahami dan di-maintain oleh developer lain! Dan jangan lupa - user experience details seperti auto-populate fields bisa membuat perbedaan besar!" 💪

---

*Happy coding, dan semoga dokumentasi ini membantu! 🚀*

**Author:** Tim Development
**Last Updated:** 2025-07-12
**Version:** 2.0.0 (Added Calendar Integration Fix)
