import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/lib/auth/utils";
import { db } from "@/lib/db";
import { creditTransactions, users, organizations } from "@/lib/db/schema";
import { eq, and, gte, sum } from "drizzle-orm";

export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const organizationId = searchParams.get("organizationId");

    // Get current balance
    let balance = 0;
    if (organizationId) {
      const [org] = await db
        .select({ credits: organizations.credits })
        .from(organizations)
        .where(eq(organizations.id, organizationId))
        .limit(1);
      balance = org?.credits || 0;
    } else {
      const [userRecord] = await db
        .select({ credits: users.credits })
        .from(users)
        .where(eq(users.id, user.id))
        .limit(1);
      balance = userRecord?.credits || 0;
    }

    // Get monthly usage (current month)
    const startOfMonth = new Date();
    startOfMonth.setDate(1);
    startOfMonth.setHours(0, 0, 0, 0);

    const monthlyUsageResult = await db
      .select({
        total: sum(creditTransactions.amount),
      })
      .from(creditTransactions)
      .where(
        and(
          organizationId
            ? eq(creditTransactions.organizationId, organizationId)
            : eq(creditTransactions.userId, user.id),
          eq(creditTransactions.type, "usage"),
          gte(creditTransactions.createdAt, startOfMonth)
        )
      );

    const monthlyUsage = Math.abs(Number(monthlyUsageResult[0]?.total || 0));

    // Get total purchased
    const totalPurchasedResult = await db
      .select({
        total: sum(creditTransactions.amount),
      })
      .from(creditTransactions)
      .where(
        and(
          organizationId
            ? eq(creditTransactions.organizationId, organizationId)
            : eq(creditTransactions.userId, user.id),
          eq(creditTransactions.type, "purchase")
        )
      );

    const totalPurchased = Number(totalPurchasedResult[0]?.total || 0);

    // Get total used
    const totalUsedResult = await db
      .select({
        total: sum(creditTransactions.amount),
      })
      .from(creditTransactions)
      .where(
        and(
          organizationId
            ? eq(creditTransactions.organizationId, organizationId)
            : eq(creditTransactions.userId, user.id),
          eq(creditTransactions.type, "usage")
        )
      );

    const totalUsed = Math.abs(Number(totalUsedResult[0]?.total || 0));

    return NextResponse.json({
      balance,
      monthlyUsage,
      totalPurchased,
      totalUsed,
    });
  } catch (error) {
    console.error("Error fetching credit stats:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
