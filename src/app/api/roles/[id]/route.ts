import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { RoleService } from "@/lib/services/role.service";
import { withRBAC, withRBACParams } from "@/lib/middleware/rbac-middleware";

/**
 * API Routes untuk individual Role operations
 */

const updateRoleSchema = z.object({
  name: z.string().min(1, "Name is required").max(100, "Name too long").optional(),
  display_name: z.string().min(1, "Display name is required").max(255, "Display name too long").optional(),
  description: z.string().max(255, "Description too long").optional(),
  hierarchy_level: z.number().int().min(0).max(100).optional(),
  permissions: z.array(z.object({
    module: z.string(),
    actions: z.array(z.string()),
    conditions: z.object({
      own_only: z.boolean().optional(),
      location_restricted: z.boolean().optional(),
      tenant_restricted: z.boolean().optional(),
    }).optional(),
  })).optional(),
  is_active: z.boolean().optional(),
});

/**
 * GET /api/roles/[id]
 * Get role by ID
 */
export const GET = withRBAC(
  async (request: NextRequest, context, { params }: { params: { id: string } }) => {
    try {
      const role = await RoleService.getById(params.id);

      if (!role) {
        return NextResponse.json(
          { success: false, error: "Role not found" },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        data: role,
      });
    } catch (error) {
      console.error("GET /api/roles/[id] error:", error);
      return NextResponse.json(
        { success: false, error: "Internal server error" },
        { status: 500 }
      );
    }
  },
  { module: "roles", action: "read" }
);

/**
 * PUT /api/roles/[id]
 * Update role
 */
export const PUT = withRBACParams(
  async (request: NextRequest, context, { params }: { params: { id: string } }) => {
    try {
      const body = await request.json();
      
      // Validate request body
      const validatedData = updateRoleSchema.parse(body);

      // Call service untuk update
      const roleResult = await RoleService.update(params.id, validatedData);

      return NextResponse.json({
        success: true,
        data: roleResult,
        message: "Role updated successfully",
      });
    } catch (error) {
      console.error("PUT /api/roles/[id] error:", error);
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { success: false, error: "Validation failed", details: error.errors },
          { status: 400 }
        );
      }

      if (error instanceof Error && error.message === "Role not found") {
        return NextResponse.json(
          { success: false, error: "Role not found" },
          { status: 404 }
        );
      }

      return NextResponse.json(
        { success: false, error: "Internal server error" },
        { status: 500 }
      );
    }
  },
  { module: "roles", action: "update" }
);

/**
 * DELETE /api/roles/[id]
 * Delete role (soft delete)
 */
export const DELETE = withRBACParams(
  async (request: NextRequest, context, { params }: { params: { id: string } }) => {
    try {
      await RoleService.delete(params.id);

      return NextResponse.json({
        success: true,
        message: "Role deleted successfully",
      });
    } catch (error) {
      console.error("DELETE /api/roles/[id] error:", error);
      return NextResponse.json(
        { success: false, error: "Internal server error" },
        { status: 500 }
      );
    }
  },
  { module: "roles", action: "delete" }
);
