import { defineConfig } from "drizzle-kit";

// PostgreSQL database configuration
const dbConfig = {
  host: process.env.DB_HOST || "127.0.0.1",
  port: parseInt(process.env.DB_PORT || "5433"),
  user: process.env.DB_USER || "citizix_user",
  password: process.env.DB_PASSWORD || "S3cret",
  database: process.env.DB_NAME || "saas_app",
  ssl: process.env.NODE_ENV === "production" ? { rejectUnauthorized: false } : false,
};

export default defineConfig({
  schema: "./src/lib/db/schema.ts",
  out: "./src/lib/db/migrations",
  dialect: "postgresql",
  dbCredentials: dbConfig,
  verbose: true,
  strict: true,
});
