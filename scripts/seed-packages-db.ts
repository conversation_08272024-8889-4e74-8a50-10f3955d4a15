// Script to seed sample packages directly to database
// Run with: npx tsx scripts/seed-packages-db.ts

import { db } from "../src/lib/db";
import { packages } from "../src/lib/db/schema";
import { createId } from "@paralleldrive/cuid2";

const samplePackages = [
  {
    id: createId(),
    tenantId: 1,
    name: "Basic Fitness Package",
    description: "Basic fitness package with gym access",
    isActive: true,
    is_private: false,
    validity_date: null,
    validity_duration: 30
  },
  {
    id: createId(),
    tenantId: 1,
    name: "Premium Fitness Package", 
    description: "Premium package with all amenities",
    isActive: true,
    is_private: false,
    validity_date: null,
    validity_duration: 90
  },
  {
    id: createId(),
    tenantId: 1,
    name: "Personal Training Package",
    description: "One-on-one personal training sessions",
    isActive: true,
    is_private: false,
    validity_date: null,
    validity_duration: 60
  },
  {
    id: createId(),
    tenantId: 2,
    name: "Yoga Classes Package",
    description: "Unlimited yoga classes for a month",
    isActive: true,
    is_private: false,
    validity_date: null,
    validity_duration: 30
  }
];

async function seedPackages() {
  console.log('🌱 Seeding sample packages to database...');
  
  try {
    // Check if packages already exist
    const existingPackages = await db.select().from(packages);
    if (existingPackages.length > 0) {
      console.log(`📦 Found ${existingPackages.length} existing packages. Skipping seed.`);
      return;
    }
    
    // Insert sample packages
    const insertedPackages = await db.insert(packages).values(samplePackages).returning();
    
    console.log(`✅ Successfully created ${insertedPackages.length} packages:`);
    insertedPackages.forEach(pkg => {
      console.log(`   - ${pkg.name} (ID: ${pkg.id})`);
    });
    
  } catch (error) {
    console.error('❌ Error seeding packages:', error);
  }
  
  console.log('🌱 Package seeding completed!');
}

// Run if called directly
if (require.main === module) {
  seedPackages().catch(console.error);
}

export { seedPackages, samplePackages };
