import { NextRequest, NextResponse } from "next/server";
import { BlogService } from "@/lib/services/blog.service";

// GET /api/public/blog-categories - Public endpoint for active blog categories
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    const tenantId = searchParams.get("tenantId");
    const search = searchParams.get("search");

    if (!tenantId) {
      return NextResponse.json(
        { error: "Tenant ID is required" },
        { status: 400 }
      );
    }

    const filters = {
      ...(search && { search }),
      is_active: true, // Only return active categories for public API
    };

    const categories = await BlogService.getCategoriesByTenant(
      parseInt(tenantId),
      filters
    );

    return NextResponse.json({
      success: true,
      data: categories,
      count: categories.length,
    });
  } catch (error) {
    console.error("Error fetching public blog categories:", error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : "Failed to fetch blog categories",
        success: false 
      },
      { status: 500 }
    );
  }
}
