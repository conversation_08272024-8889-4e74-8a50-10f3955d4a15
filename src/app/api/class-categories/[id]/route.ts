import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { z } from "zod";
import { ClassCategoryService } from "@/lib/services/class-category.service";

/**
 * Class Category Individual API Routes
 * 
 * API endpoints untuk operasi individual class category (get, update, delete by ID).
 * Ini handle operasi untuk satu class category spesifik.
 */

// Validation schemas
const updateClassCategorySchema = z.object({
  name: z.string().min(1, "Name is required").max(255, "Name too long"),
});

const paramsSchema = z.object({
  id: z.string().min(1, "ID is required"),
});

const querySchema = z.object({
  tenantId: z.string().transform((val) => parseInt(val, 10)),
});

/**
 * GET /api/class-categories/[id]
 * 
 * Get single class category by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Validate params
    const validatedParams = paramsSchema.parse(params);
    
    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const queryParams = {
      tenantId: searchParams.get("tenantId"),
    };
    
    const validatedQuery = querySchema.parse(queryParams);

    // Get category by ID
    const category = await ClassCategoryService.getById(validatedParams.id);

    if (!category) {
      return NextResponse.json(
        { success: false, error: "Class category not found" },
        { status: 404 }
      );
    }

    // Check tenant access
    if (category.tenantId !== validatedQuery.tenantId) {
      return NextResponse.json(
        { success: false, error: "Access denied" },
        { status: 403 }
      );
    }

    return NextResponse.json({
      success: true,
      data: category,
    });

  } catch (error) {
    console.error("GET /api/class-categories/[id] error:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: "Invalid parameters",
          details: error.errors,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: "Internal server error",
      },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/class-categories/[id]
 * 
 * Update class category by ID
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Validate params
    const validatedParams = paramsSchema.parse(params);

    // Parse request body
    const body = await request.json();
    const validatedData = updateClassCategorySchema.parse(body);

    // Parse query parameters untuk tenantId
    const { searchParams } = new URL(request.url);
    const queryParams = {
      tenantId: searchParams.get("tenantId"),
    };
    const validatedQuery = querySchema.parse(queryParams);

    // Update category
    const updatedCategory = await ClassCategoryService.update(
      validatedParams.id,
      validatedData
    );

    return NextResponse.json({
      success: true,
      data: updatedCategory,
      message: "Class category updated successfully",
    });

  } catch (error) {
    console.error("PUT /api/class-categories/[id] error:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: "Invalid data",
          details: error.errors,
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      // Handle specific business logic errors
      if (error.message.includes("not found") || error.message.includes("access denied")) {
        return NextResponse.json(
          {
            success: false,
            error: error.message,
          },
          { status: 404 }
        );
      }

      if (error.message.includes("already exists")) {
        return NextResponse.json(
          {
            success: false,
            error: error.message,
          },
          { status: 409 }
        );
      }

      return NextResponse.json(
        {
          success: false,
          error: error.message,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: "Internal server error",
      },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/class-categories/[id]
 * 
 * Delete class category by ID
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Validate params
    const validatedParams = paramsSchema.parse(params);

    // Parse query parameters untuk tenantId
    const { searchParams } = new URL(request.url);
    const queryParams = {
      tenantId: searchParams.get("tenantId"),
    };
    const validatedQuery = querySchema.parse(queryParams);

    // Delete category
    await ClassCategoryService.delete(validatedParams.id);

    return NextResponse.json({
      success: true,
      message: "Class category deleted successfully",
    });

  } catch (error) {
    console.error("DELETE /api/class-categories/[id] error:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: "Invalid parameters",
          details: error.errors,
        },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      if (error.message.includes("not found") || error.message.includes("access denied")) {
        return NextResponse.json(
          {
            success: false,
            error: error.message,
          },
          { status: 404 }
        );
      }

      // Handle foreign key constraint errors (kalau ada classes yang masih pakai category ini)
      if (error.message.includes("foreign key") || error.message.includes("constraint")) {
        return NextResponse.json(
          {
            success: false,
            error: "Cannot delete category that is still being used by classes",
          },
          { status: 409 }
        );
      }

      return NextResponse.json(
        {
          success: false,
          error: error.message,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: "Internal server error",
      },
      { status: 500 }
    );
  }
}
