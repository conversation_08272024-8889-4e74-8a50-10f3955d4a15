"use client";

import { useForm } from "@tanstack/react-form";
import { zodValidator } from "@tanstack/zod-form-adapter";
import { z } from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Upload, Building2, Globe, MessageCircle, Loader2, AlertCircle } from "lucide-react";
import { BusinessProfile } from "@/lib/db/schema";
import { useCreateBusinessProfile, useUpdateBusinessProfile } from "@/lib/hooks/queries/use-business-profile-queries";
import { ValidationService } from "@/lib/services/validation.service";
import { useState } from "react";

const businessProfileSchema = z.object({
  business_name: z.string()
    .min(1, "Business name is required")
    .max(255, "Business name too long")
    .refine((name) => ValidationService.validateBusinessName(name).valid, {
      message: "Invalid business name format",
    }),
  business_logo: z.string().url().optional().or(z.literal("")),
  company_registered_name: z.string().max(255).optional(),
  business_description: z.string().optional(),
  business_website: z.string()
    .optional()
    .refine((url) => !url || ValidationService.validateWebsiteUrl(url).valid, {
      message: "Invalid website URL",
    }),
  whatsapp_number: z.string()
    .optional()
    .refine((phone) => !phone || ValidationService.validatePhoneNumber(phone).valid, {
      message: "Invalid WhatsApp number format",
    }),
  show_whatsapp_floating: z.boolean().default(false),
});

type BusinessProfileFormData = z.infer<typeof businessProfileSchema>;

interface BusinessProfileTanStackFormProps {
  tenantId: number;
  profile?: BusinessProfile | null;
  onSuccess?: (profile: BusinessProfile) => void;
}

export function BusinessProfileTanStackForm({ 
  tenantId, 
  profile, 
  onSuccess 
}: BusinessProfileTanStackFormProps) {
  const [isUploading, setIsUploading] = useState(false);
  
  const createMutation = useCreateBusinessProfile();
  const updateMutation = useUpdateBusinessProfile();

  const form = useForm({
    defaultValues: {
      business_name: profile?.business_name || "",
      business_logo: profile?.business_logo || "",
      company_registered_name: profile?.company_registered_name || "",
      business_description: profile?.business_description || "",
      business_website: profile?.business_website || "",
      whatsapp_number: profile?.whatsapp_number || "",
      show_whatsapp_floating: profile?.show_whatsapp_floating || false,
    } as BusinessProfileFormData,
    onSubmit: async ({ value }) => {
      try {
        let result;
        if (profile) {
          result = await updateMutation.mutateAsync({ tenantId, data: value });
        } else {
          result = await createMutation.mutateAsync({ ...value, tenantId });
        }
        onSuccess?.(result);
      } catch (error) {
        console.error("Form submission error:", error);
      }
    },
    validatorAdapter: zodValidator,
  });

  const handleLogoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsUploading(true);
    try {
      const formData = new FormData();
      formData.append("file", file);
      formData.append("folder", "logos");
      formData.append("tenantId", tenantId.toString());

      const response = await fetch("/api/upload", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Upload failed");
      }

      const result = await response.json();
      form.setFieldValue("business_logo", result.file.url);
    } catch (error) {
      console.error("Error uploading logo:", error);
    } finally {
      setIsUploading(false);
    }
  };

  const isSubmitting = createMutation.isPending || updateMutation.isPending;

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Building2 className="h-5 w-5" />
          {profile ? "Edit Business Profile" : "Create Business Profile"}
        </CardTitle>
        <CardDescription>
          Set up your business information and branding with advanced validation
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            form.handleSubmit();
          }}
          className="space-y-6"
        >
          {/* Logo Upload */}
          <div className="flex items-center gap-4">
            <Avatar className="h-20 w-20">
              <AvatarImage src={form.getFieldValue("business_logo")} alt="Business Logo" />
              <AvatarFallback>
                <Building2 className="h-8 w-8" />
              </AvatarFallback>
            </Avatar>
            <div>
              <label htmlFor="logo-upload" className="cursor-pointer">
                <Button type="button" variant="outline" disabled={isUploading}>
                  {isUploading ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Upload className="h-4 w-4 mr-2" />
                  )}
                  Upload Logo
                </Button>
              </label>
              <input
                id="logo-upload"
                type="file"
                accept="image/*"
                className="hidden"
                onChange={handleLogoUpload}
              />
              <p className="text-sm text-muted-foreground mt-1">
                PNG, JPG up to 2MB
              </p>
            </div>
          </div>

          {/* Business Name */}
          <form.Field
            name="business_name"
            validators={{
              onChange: businessProfileSchema.shape.business_name,
            }}
            children={(field) => (
              <div className="space-y-2">
                <Label htmlFor={field.name}>Business Name *</Label>
                <Input
                  id={field.name}
                  name={field.name}
                  value={field.state.value}
                  onBlur={field.handleBlur}
                  onChange={(e) => field.handleChange(e.target.value)}
                  placeholder="Acme Corporation"
                />
                {field.state.meta.errors.length > 0 && (
                  <div className="flex items-center gap-2 text-sm text-destructive">
                    <AlertCircle className="h-4 w-4" />
                    {field.state.meta.errors[0]}
                  </div>
                )}
              </div>
            )}
          />

          {/* Registered Name */}
          <form.Field
            name="company_registered_name"
            validators={{
              onChange: businessProfileSchema.shape.company_registered_name,
            }}
            children={(field) => (
              <div className="space-y-2">
                <Label htmlFor={field.name}>Registered Company Name</Label>
                <Input
                  id={field.name}
                  name={field.name}
                  value={field.state.value}
                  onBlur={field.handleBlur}
                  onChange={(e) => field.handleChange(e.target.value)}
                  placeholder="Acme Corporation Ltd."
                />
                <p className="text-sm text-muted-foreground">
                  Official registered name of your company
                </p>
                {field.state.meta.errors.length > 0 && (
                  <div className="flex items-center gap-2 text-sm text-destructive">
                    <AlertCircle className="h-4 w-4" />
                    {field.state.meta.errors[0]}
                  </div>
                )}
              </div>
            )}
          />

          {/* Description */}
          <form.Field
            name="business_description"
            children={(field) => (
              <div className="space-y-2">
                <Label htmlFor={field.name}>Business Description</Label>
                <Textarea
                  id={field.name}
                  name={field.name}
                  value={field.state.value}
                  onBlur={field.handleBlur}
                  onChange={(e) => field.handleChange(e.target.value)}
                  placeholder="Describe your business..."
                  className="min-h-[100px]"
                />
              </div>
            )}
          />

          {/* Website */}
          <form.Field
            name="business_website"
            validators={{
              onChangeAsyncDebounceMs: 500,
              onChangeAsync: async ({ value }) => {
                if (!value) return;
                const validation = ValidationService.validateWebsiteUrl(value);
                if (!validation.valid) {
                  return validation.error;
                }
                return undefined;
              },
            }}
            children={(field) => (
              <div className="space-y-2">
                <Label htmlFor={field.name} className="flex items-center gap-2">
                  <Globe className="h-4 w-4" />
                  Website
                </Label>
                <Input
                  id={field.name}
                  name={field.name}
                  value={field.state.value}
                  onBlur={field.handleBlur}
                  onChange={(e) => field.handleChange(e.target.value)}
                  placeholder="https://example.com"
                />
                {field.state.meta.isValidating && (
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    Validating URL...
                  </div>
                )}
                {field.state.meta.errors.length > 0 && (
                  <div className="flex items-center gap-2 text-sm text-destructive">
                    <AlertCircle className="h-4 w-4" />
                    {field.state.meta.errors[0]}
                  </div>
                )}
              </div>
            )}
          />

          {/* WhatsApp Settings */}
          <div className="space-y-4 p-4 border rounded-lg">
            <h3 className="font-medium flex items-center gap-2">
              <MessageCircle className="h-4 w-4" />
              WhatsApp Integration
            </h3>
            
            <form.Field
              name="whatsapp_number"
              validators={{
                onChangeAsyncDebounceMs: 500,
                onChangeAsync: async ({ value }) => {
                  if (!value) return;
                  const validation = ValidationService.validatePhoneNumber(value);
                  if (!validation.valid) {
                    return validation.error;
                  }
                  return undefined;
                },
              }}
              children={(field) => (
                <div className="space-y-2">
                  <Label htmlFor={field.name}>WhatsApp Number</Label>
                  <Input
                    id={field.name}
                    name={field.name}
                    value={field.state.value}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(e.target.value)}
                    placeholder="+1234567890"
                  />
                  <p className="text-sm text-muted-foreground">
                    Include country code (e.g., +1234567890)
                  </p>
                  {field.state.meta.isValidating && (
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Loader2 className="h-4 w-4 animate-spin" />
                      Validating phone number...
                    </div>
                  )}
                  {field.state.meta.errors.length > 0 && (
                    <div className="flex items-center gap-2 text-sm text-destructive">
                      <AlertCircle className="h-4 w-4" />
                      {field.state.meta.errors[0]}
                    </div>
                  )}
                </div>
              )}
            />

            <form.Field
              name="show_whatsapp_floating"
              children={(field) => (
                <div className="flex flex-row items-center justify-between rounded-lg border p-3">
                  <div className="space-y-0.5">
                    <Label htmlFor={field.name}>Show Floating WhatsApp Button</Label>
                    <p className="text-sm text-muted-foreground">
                      Display a floating WhatsApp button on your website
                    </p>
                  </div>
                  <Switch
                    id={field.name}
                    checked={field.state.value}
                    onCheckedChange={field.handleChange}
                  />
                </div>
              )}
            />
          </div>

          {/* Submit Button */}
          <form.Subscribe
            selector={(state) => [state.canSubmit, state.isSubmitting]}
            children={([canSubmit, isSubmitting]) => (
              <Button 
                type="submit" 
                className="w-full" 
                disabled={!canSubmit || isSubmitting}
              >
                {isSubmitting ? (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                ) : null}
                {profile ? "Update Profile" : "Create Profile"}
              </Button>
            )}
          />

          {/* Form State Debug (Development only) */}
          {process.env.NODE_ENV === 'development' && (
            <form.Subscribe
              selector={(state) => [state.errors, state.isValid]}
              children={([errors, isValid]) => (
                <div className="mt-4 p-4 bg-muted rounded-lg">
                  <p className="text-sm font-medium">Form State (Dev)</p>
                  <p className="text-sm">Valid: {isValid ? "✅" : "❌"}</p>
                  {errors.length > 0 && (
                    <div className="text-sm text-destructive">
                      Errors: {errors.join(", ")}
                    </div>
                  )}
                </div>
              )}
            />
          )}
        </form>
      </CardContent>
    </Card>
  );
}
