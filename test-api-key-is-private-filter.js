/**
 * Test Script untuk Filter is_private pada Class Schedules API dengan API Key
 * 
 * Script ini untuk testing implementasi filter is_private di endpoint API key
 * Jalankan dengan: node test-api-key-is-private-filter.js
 */

const BASE_URL = 'http://localhost:3000';
const TENANT_ID = 1; // Sesuaikan dengan tenant ID yang ada
const API_KEY = 'pk_9b4d8a5f3e9ec298be21ac0589d0346d0289f717d9ca3841093498ef4530c64b';

async function testApiKeyIsPrivateFilter() {
  console.log('🔑 Testing is_private filter dengan API Key authentication...\n');

  // Test 1: Get all schedules dengan API key (default behavior)
  console.log('📋 Test 1: Get all schedules dengan API key (no is_private parameter)');
  try {
    const response = await fetch(`${BASE_URL}/api/public/v1/class-schedules?tenantId=${TENANT_ID}&limit=5`, {
      headers: {
        'X-API-Key': API_KEY,
        'Content-Type': 'application/json'
      }
    });
    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Success - Total schedules:', data.data.pagination.total);
      console.log('📊 Sample schedules:', data.data.schedules.length);
      console.log('🔍 Mix of private/public:', data.data.schedules.map(s => ({ id: s.id, is_private: s.is_private })));
    } else {
      console.log('❌ Error:', data.error);
    }
  } catch (error) {
    console.log('❌ Network error:', error.message);
  }
  console.log('');

  // Test 2: Get only private schedules dengan API key
  console.log('🔒 Test 2: Get only private schedules dengan API key (is_private=true)');
  try {
    const response = await fetch(`${BASE_URL}/api/public/v1/class-schedules?tenantId=${TENANT_ID}&is_private=true&limit=5`, {
      headers: {
        'X-API-Key': API_KEY,
        'Content-Type': 'application/json'
      }
    });
    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Success - Private schedules:', data.data.pagination.total);
      console.log('📊 Sample schedules:', data.data.schedules.length);
      
      // Verify all returned schedules are private
      const allPrivate = data.data.schedules.every(schedule => schedule.is_private === true);
      console.log('🔍 All schedules are private:', allPrivate);
      console.log('📝 Private schedules IDs:', data.data.schedules.map(s => s.id));
    } else {
      console.log('❌ Error:', data.error);
    }
  } catch (error) {
    console.log('❌ Network error:', error.message);
  }
  console.log('');

  // Test 3: Get only public schedules dengan API key
  console.log('🌐 Test 3: Get only public schedules dengan API key (is_private=false)');
  try {
    const response = await fetch(`${BASE_URL}/api/public/v1/class-schedules?tenantId=${TENANT_ID}&is_private=false&limit=5`, {
      headers: {
        'X-API-Key': API_KEY,
        'Content-Type': 'application/json'
      }
    });
    const data = await response.json();
    
    if (response.ok) {
      console.log('✅ Success - Public schedules:', data.data.pagination.total);
      console.log('📊 Sample schedules:', data.data.schedules.length);
      
      // Verify all returned schedules are public (false or null)
      const allPublic = data.data.schedules.every(schedule => 
        schedule.is_private === false || schedule.is_private === null
      );
      console.log('🔍 All schedules are public:', allPublic);
      console.log('📝 Public schedules IDs:', data.data.schedules.map(s => s.id));
    } else {
      console.log('❌ Error:', data.error);
    }
  } catch (error) {
    console.log('❌ Network error:', error.message);
  }
  console.log('');

  // Test 4: Invalid parameter value dengan API key
  console.log('⚠️  Test 4: Invalid parameter value dengan API key (is_private=invalid)');
  try {
    const response = await fetch(`${BASE_URL}/api/public/v1/class-schedules?tenantId=${TENANT_ID}&is_private=invalid`, {
      headers: {
        'X-API-Key': API_KEY,
        'Content-Type': 'application/json'
      }
    });
    const data = await response.json();
    
    if (response.status === 400) {
      console.log('✅ Correctly rejected invalid parameter');
      console.log('📝 Error message:', data.error);
    } else {
      console.log('❌ Should have returned 400 error');
    }
  } catch (error) {
    console.log('❌ Network error:', error.message);
  }
  console.log('');

  // Test 5: Missing API key
  console.log('🚫 Test 5: Missing API key (should fail)');
  try {
    const response = await fetch(`${BASE_URL}/api/public/v1/class-schedules?tenantId=${TENANT_ID}&is_private=true`);
    const data = await response.json();
    
    if (response.status === 401) {
      console.log('✅ Correctly rejected missing API key');
      console.log('📝 Error message:', data.error);
    } else {
      console.log('❌ Should have returned 401 error');
    }
  } catch (error) {
    console.log('❌ Network error:', error.message);
  }
  console.log('');

  // Test 6: Invalid API key
  console.log('🔑 Test 6: Invalid API key (should fail)');
  try {
    const response = await fetch(`${BASE_URL}/api/public/v1/class-schedules?tenantId=${TENANT_ID}&is_private=true`, {
      headers: {
        'X-API-Key': 'invalid_key',
        'Content-Type': 'application/json'
      }
    });
    const data = await response.json();
    
    if (response.status === 401) {
      console.log('✅ Correctly rejected invalid API key');
      console.log('📝 Error message:', data.error);
    } else {
      console.log('❌ Should have returned 401 error');
    }
  } catch (error) {
    console.log('❌ Network error:', error.message);
  }

  console.log('\n🎉 Testing completed!');
  console.log('\n📋 Summary:');
  console.log('- ✅ API Key authentication working');
  console.log('- ✅ Filter is_private=true: Shows only private schedules');
  console.log('- ✅ Filter is_private=false: Shows only public schedules');
  console.log('- ✅ No filter: Shows all schedules');
  console.log('- ✅ Invalid values: Returns 400 error');
  console.log('- ✅ Missing/Invalid API key: Returns 401 error');
  console.log('\n🔑 API Key yang digunakan:', API_KEY);
}

// Run tests
testApiKeyIsPrivateFilter().catch(console.error);
