"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import { Package, MapPin, Plus, BarChart3, Link as LinkIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { PackageLocationForm } from "@/components/forms/package-location-form";
import { PackageLocationList } from "@/components/package-locations/package-location-list";
import { usePackageLocations, useBulkUpdatePackageLocations } from "@/lib/hooks/queries/use-package-location-queries";
import { usePackageList } from "@/lib/hooks/queries/use-package-queries";
import { useLocations } from "@/lib/hooks/queries/use-location-queries";
import { useTenants } from "@/lib/hooks/queries/use-tenant-queries";
import { toast } from "sonner";

export default function PackageLocationsPage() {
  const [selectedTenantId, setSelectedTenantId] = useState<number | null>(null);
  const [showAssignForm, setShowAssignForm] = useState(false);

  // Fetch data
  const { data: tenants = [], isLoading: tenantsLoading } = useTenants();
  const safeTenants = Array.isArray(tenants) ? tenants : [];
  
  const currentTenantId = selectedTenantId || (safeTenants[0]?.id ?? 1);
  
  const { data: packageLocations = [], isLoading: packageLocationsLoading } = usePackageLocations({});
  const { data: packages = [] } = usePackageList({}, currentTenantId);
  const { data: locations = [] } = useLocations({ tenantId: currentTenantId });

  // Mutations
  const bulkUpdateMutation = useBulkUpdatePackageLocations();

  // Calculate stats
  const stats = {
    totalAssignments: packageLocations.length,
    uniquePackages: new Set(packageLocations.map(pl => pl.package_id)).size,
    uniqueLocations: new Set(packageLocations.map(pl => pl.location_id)).size,
    packagesWithLocations: packages.filter(pkg => 
      packageLocations.some(pl => pl.package_id === pkg.id)
    ).length,
    locationsWithPackages: locations.filter(location => 
      packageLocations.some(pl => pl.location_id === location.id)
    ).length,
    unassignedPackages: packages.length - new Set(packageLocations.map(pl => pl.package_id)).size,
    unassignedLocations: locations.length - new Set(packageLocations.map(pl => pl.location_id)).size,
  };

  // Stats by tenant
  const statsByTenant = safeTenants.reduce((acc, tenant) => {
    const tenantPackageLocations = packageLocations.filter(pl => {
      const pkg = packages.find(p => p.id === pl.package_id);
      return pkg?.tenantId === tenant.id;
    });
    
    acc[tenant.id] = {
      total: tenantPackageLocations.length,
      packages: new Set(tenantPackageLocations.map(pl => pl.package_id)).size,
      locations: new Set(tenantPackageLocations.map(pl => pl.location_id)).size,
    };
    return acc;
  }, {} as Record<number, { total: number; packages: number; locations: number }>);

  const handleAssignPackage = async (data: { packageId: string; locationIds: string[] }) => {
    try {
      await bulkUpdateMutation.mutateAsync({
        packageId: data.packageId,
        locationIds: data.locationIds,
      });
      setShowAssignForm(false);
      toast.success("Package assigned to locations successfully!");
    } catch (error) {
      console.error("Failed to assign package to locations:", error);
      toast.error("Failed to assign package to locations");
    }
  };

  // Show loading if tenants are still loading
  if (tenantsLoading) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Package Locations</h1>
          <p className="text-muted-foreground">
            Manage which packages are available at which locations
          </p>
        </div>
        <Button onClick={() => setShowAssignForm(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Assign Package
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Assignments</CardTitle>
            <LinkIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalAssignments}</div>
            <p className="text-xs text-muted-foreground">
              Package-location relationships
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Packages with Locations</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.packagesWithLocations}</div>
            <p className="text-xs text-muted-foreground">
              {stats.unassignedPackages} packages unassigned
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Locations with Packages</CardTitle>
            <MapPin className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.locationsWithPackages}</div>
            <p className="text-xs text-muted-foreground">
              {stats.unassignedLocations} locations unassigned
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Coverage</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {packages.length > 0 ? Math.round((stats.packagesWithLocations / packages.length) * 100) : 0}%
            </div>
            <p className="text-xs text-muted-foreground">
              Packages with locations
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="all" className="space-y-4">
        <TabsList>
          <TabsTrigger value="all">All Assignments</TabsTrigger>
          {safeTenants.map((tenant) => {
            const tenantStats = statsByTenant[tenant.id];
            return (
              <TabsTrigger key={tenant.id} value={`tenant-${tenant.id}`}>
                {tenant.name}
                <Badge variant="secondary" className="ml-2">
                  {tenantStats?.total || 0}
                </Badge>
              </TabsTrigger>
            );
          })}
        </TabsList>

        <TabsContent value="all">
          <PackageLocationList
            packageLocations={packageLocations}
            onAdd={() => setShowAssignForm(true)}
            showActions={true}
            tenantId={currentTenantId}
          />
        </TabsContent>

        {safeTenants.map((tenant) => (
          <TabsContent key={tenant.id} value={`tenant-${tenant.id}`}>
            <PackageLocationList
              packageLocations={packageLocations.filter(pl => {
                const pkg = packages.find(p => p.id === pl.package_id);
                return pkg?.tenantId === tenant.id;
              })}
              onAdd={() => {
                setSelectedTenantId(tenant.id);
                setShowAssignForm(true);
              }}
              showActions={true}
              tenantId={tenant.id}
            />
          </TabsContent>
        ))}
      </Tabs>

      {/* Assign Package Dialog */}
      <Dialog open={showAssignForm} onOpenChange={setShowAssignForm}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Assign Package to Locations</DialogTitle>
          </DialogHeader>
          <PackageLocationForm
            onSubmit={handleAssignPackage}
            onCancel={() => setShowAssignForm(false)}
            tenantId={currentTenantId}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}
