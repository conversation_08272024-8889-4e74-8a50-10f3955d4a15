/**
 * Public API Types and Interfaces
 * 
 * Comprehensive type definitions for the public package pricing API
 * Following FAANG-level engineering standards
 */

// ============================================================================
// Authentication Types
// ============================================================================

export interface APIKey {
  id: string;
  key: string;
  name: string;
  tenantId: number;
  permissions: APIPermission[];
  rateLimit: RateLimit;
  isActive: boolean;
  expiresAt?: Date;
  lastUsedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface APIPermission {
  resource: string; // e.g., 'package-pricing'
  actions: string[]; // e.g., ['read', 'write', 'delete']
  conditions?: Record<string, any>; // e.g., { tenantId: 123 }
}

export interface RateLimit {
  requests: number; // Max requests per window
  window: number; // Time window in seconds
}

// ============================================================================
// API Request/Response Types
// ============================================================================

export interface APIRequest<T = any> {
  data?: T;
  metadata?: {
    requestId: string;
    timestamp: string;
    version: string;
    clientInfo?: {
      userAgent?: string;
      ip?: string;
      country?: string;
    };
  };
}

export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: APIError;
  metadata: {
    requestId: string;
    timestamp: string;
    version: string;
    processingTime: number;
    rateLimit?: {
      remaining: number;
      resetTime: string;
      limit: number;
    };
  };
  pagination?: PaginationMeta;
}

export interface APIError {
  code: string;
  message: string;
  details?: Record<string, any>;
  stack?: string; // Only in development
}

export interface PaginationMeta {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// ============================================================================
// Package Pricing API Types
// ============================================================================

export interface PublicPackagePricing {
  id: string;
  packageId: string;
  packageName: string;
  packageDescription?: string;
  pricingGroupId: string;
  pricingGroupName: string;
  price?: number;
  creditAmount?: number;
  currency: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface PackagePricingFilters {
  packageId?: string;
  pricingGroupId?: string;
  minPrice?: number;
  maxPrice?: number;
  currency?: string;
  isActive?: boolean;
  search?: string;
}

export interface PackagePricingCreateRequest {
  packageId: string;
  pricingGroupId: string;
  price?: number;
  creditAmount?: number;
  currency?: string;
}

export interface PackagePricingUpdateRequest {
  price?: number;
  creditAmount?: number;
  currency?: string;
}

export interface PackagePricingBulkCreateRequest {
  items: PackagePricingCreateRequest[];
}

export interface PackagePricingStats {
  total: number;
  byPackage: Record<string, number>;
  byPricingGroup: Record<string, number>;
  averagePrice: number;
  totalRevenue: number;
  currencies: string[];
}

// ============================================================================
// Webhook Types
// ============================================================================

export interface WebhookEvent {
  id: string;
  type: string; // e.g., 'package-pricing.created'
  data: any;
  timestamp: string;
  tenantId: number;
}

export interface WebhookEndpoint {
  id: string;
  url: string;
  events: string[];
  secret: string;
  isActive: boolean;
  retryPolicy: {
    maxRetries: number;
    backoffMultiplier: number;
    maxBackoffSeconds: number;
  };
}

// ============================================================================
// Monitoring Types
// ============================================================================

export interface APIMetrics {
  requestCount: number;
  errorCount: number;
  averageResponseTime: number;
  p95ResponseTime: number;
  p99ResponseTime: number;
  rateLimitHits: number;
  uniqueClients: number;
  topEndpoints: Array<{
    endpoint: string;
    count: number;
    averageResponseTime: number;
  }>;
}

export interface HealthCheck {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  version: string;
  uptime: number;
  checks: {
    database: HealthCheckResult;
    redis: HealthCheckResult;
    external: HealthCheckResult;
  };
}

export interface HealthCheckResult {
  status: 'pass' | 'fail' | 'warn';
  responseTime?: number;
  error?: string;
}

// ============================================================================
// SDK Types
// ============================================================================

export interface SDKConfig {
  apiKey: string;
  baseUrl?: string;
  timeout?: number;
  retryConfig?: {
    maxRetries: number;
    backoffMultiplier: number;
    maxBackoffSeconds: number;
  };
  rateLimitConfig?: {
    enableBackoff: boolean;
    maxBackoffSeconds: number;
  };
}

export interface SDKResponse<T> extends APIResponse<T> {
  raw: Response;
}

// ============================================================================
// Circuit Breaker Types
// ============================================================================

export interface CircuitBreakerConfig {
  failureThreshold: number;
  recoveryTimeout: number;
  monitoringPeriod: number;
  expectedErrors: string[];
}

export interface CircuitBreakerState {
  state: 'closed' | 'open' | 'half-open';
  failureCount: number;
  lastFailureTime?: number;
  nextAttemptTime?: number;
}

// ============================================================================
// Cache Types
// ============================================================================

export interface CacheConfig {
  ttl: number; // Time to live in seconds
  maxSize?: number;
  strategy: 'lru' | 'lfu' | 'fifo';
}

export interface CacheEntry<T> {
  key: string;
  value: T;
  expiresAt: number;
  createdAt: number;
  accessCount: number;
  lastAccessedAt: number;
}

// ============================================================================
// Audit Types
// ============================================================================

export interface AuditLog {
  id: string;
  apiKeyId: string;
  tenantId: number;
  action: string;
  resource: string;
  resourceId?: string;
  changes?: Record<string, any>;
  ip: string;
  userAgent: string;
  timestamp: string;
  success: boolean;
  error?: string;
}

// ============================================================================
// Export all types
// ============================================================================

export * from './auth-types';
export * from './validation-types';
