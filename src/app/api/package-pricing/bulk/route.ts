import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { z } from "zod";
import { packagePricingService } from "@/lib/services/package-pricing.service";

// Validation schemas
const createPackagePricingSchema = z.object({
  packageId: z.string().min(1, "Package ID is required"),
  pricingGroupId: z.string().min(1, "Pricing group ID is required"),
  price: z.number().min(0, "Price cannot be negative").optional(),
  creditAmount: z.number().int().min(0, "Credit amount cannot be negative").optional(),
  currency: z.string().min(1, "Currency is required").max(10, "Currency code too long").optional(),
}).refine(
  (data) => data.price !== undefined || data.creditAmount !== undefined,
  {
    message: "Either price or credit amount must be specified",
    path: ["price"],
  }
);

const bulkOperationSchema = z.object({
  action: z.enum(["create", "delete", "update"]),
  data: z.array(createPackagePricingSchema).optional(),
  ids: z.array(z.string().min(1)).optional(),
  updates: z.array(z.object({
    id: z.string().min(1),
    data: z.object({
      packageId: z.string().optional(),
      pricingGroupId: z.string().optional(),
      price: z.number().min(0).optional(),
      creditAmount: z.number().int().min(0).optional(),
      currency: z.string().max(10).optional(),
    })
  })).optional(),
}).refine((data) => {
  if (data.action === "create" && (!data.data || data.data.length === 0)) {
    return false;
  }
  if (data.action === "delete" && (!data.ids || data.ids.length === 0)) {
    return false;
  }
  if (data.action === "update" && (!data.updates || data.updates.length === 0)) {
    return false;
  }
  return true;
}, {
  message: "Invalid bulk operation data for the specified action",
});

/**
 * POST /api/package-pricing/bulk
 * Bulk operations for package pricing (create, update, delete)
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = bulkOperationSchema.parse(body);

    console.log(`💰 [POST /api/package-pricing/bulk] Bulk operation:`, validatedData.action);

    let results;
    let message;

    switch (validatedData.action) {
      case "create":
        results = await packagePricingService.bulkCreate(validatedData.data!);
        message = `Successfully created ${results.length} package pricing records`;
        break;

      case "delete":
        results = await packagePricingService.bulkDelete(validatedData.ids!);
        message = `Successfully deleted ${results.length} package pricing records`;
        break;

      case "update":
        results = await packagePricingService.bulkUpdate(validatedData.updates!);
        message = `Successfully updated ${results.length} package pricing records`;
        break;

      default:
        return NextResponse.json(
          { error: "Invalid bulk operation action" },
          { status: 400 }
        );
    }

    return NextResponse.json({
      success: true,
      data: results,
      message,
      meta: {
        action: validatedData.action,
        count: results.length
      }
    });

  } catch (error) {
    console.error("💰 [POST /api/package-pricing/bulk] Error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid bulk operation data", details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      // Handle specific business logic errors
      if (error.message.includes("already exists") || 
          error.message.includes("Duplicate") ||
          error.message.includes("not found")) {
        return NextResponse.json(
          { error: error.message },
          { status: 409 }
        );
      }
    }

    return NextResponse.json(
      { error: "Failed to perform bulk operation" },
      { status: 500 }
    );
  }
}

/**
 * GET /api/package-pricing/bulk
 * Get bulk operation status or templates
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get("action");

    if (action === "template") {
      // Return templates for bulk operations
      return NextResponse.json({
        success: true,
        data: {
          create: {
            description: "Bulk create package pricing",
            example: {
              action: "create",
              data: [
                {
                  packageId: "package_id_1",
                  pricingGroupId: "pricing_group_id_1",
                  price: 100000,
                  creditAmount: 10,
                  currency: "IDR"
                }
              ]
            }
          },
          delete: {
            description: "Bulk delete package pricing",
            example: {
              action: "delete",
              ids: ["pricing_id_1", "pricing_id_2"]
            }
          },
          update: {
            description: "Bulk update package pricing",
            example: {
              action: "update",
              updates: [
                {
                  id: "pricing_id_1",
                  data: {
                    price: 150000,
                    creditAmount: 15
                  }
                }
              ]
            }
          }
        }
      });
    }

    return NextResponse.json({
      success: true,
      message: "Package pricing bulk operations endpoint",
      availableActions: ["create", "delete", "update"],
      usage: "POST with action and corresponding data"
    });

  } catch (error) {
    console.error("💰 [GET /api/package-pricing/bulk] Error:", error);
    return NextResponse.json(
      { error: "Failed to get bulk operation info" },
      { status: 500 }
    );
  }
}
