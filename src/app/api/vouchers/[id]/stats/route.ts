import { NextRequest, NextResponse } from "next/server";
import { VoucherService } from "@/lib/services/voucher.service";
import { withRBAC } from "@/lib/middleware/rbac-middleware";

interface RouteParams {
  params: {
    id: string;
  };
}

// GET /api/vouchers/[id]/stats - Get voucher statistics
export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  return withRBAC(
    async () => {
      try {
        const { id } = params;

        if (!id) {
          return NextResponse.json(
            { error: "Voucher ID is required" },
            { status: 400 }
          );
        }

        // Check if voucher exists
        const voucher = await VoucherService.getById(id);
        if (!voucher) {
          return NextResponse.json(
            { error: "Voucher not found" },
            { status: 404 }
          );
        }

        // Get voucher statistics
        const stats = await VoucherService.getVoucherStats(id);

        return NextResponse.json({
          success: true,
          data: {
            ...stats,
            voucher: voucher,
          },
        });
      } catch (error) {
        console.error("Error fetching voucher statistics:", error);
        return NextResponse.json(
          { 
            error: error instanceof Error ? error.message : "Failed to fetch voucher statistics",
            success: false 
          },
          { status: 500 }
        );
      }
    },
    { module: "vouchers", action: "read" }
  )(request);
}
