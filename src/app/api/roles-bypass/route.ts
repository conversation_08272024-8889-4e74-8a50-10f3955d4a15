import { NextRequest, NextResponse } from "next/server";
import { RoleService } from "@/lib/services/role.service";
import { z } from "zod";

// Temporary bypass endpoint for role management (testing only)
// This bypasses authentication for role operations

// Validation schemas
const createRoleSchema = z.object({
  tenantId: z.number().optional().nullable(),
  name: z.string().min(1),
  display_name: z.string().min(1),
  description: z.string().optional(),
  is_system_role: z.boolean().optional(),
  hierarchy_level: z.number().optional(),
  permissions: z.array(z.string()).optional(),
});

const updateRoleSchema = z.object({
  name: z.string().optional(),
  display_name: z.string().optional(),
  description: z.string().optional(),
  hierarchy_level: z.number().optional(),
  permissions: z.array(z.string()).optional(),
  is_active: z.boolean().optional(),
});

// GET - Search roles
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get("limit") || "20");
    const offset = parseInt(searchParams.get("offset") || "0");
    const search = searchParams.get("search") || undefined;
    const isSystemRole = searchParams.get("isSystemRole") === "true" ? true : 
                        searchParams.get("isSystemRole") === "false" ? false : undefined;
    const tenantId = searchParams.get("tenantId") ? 
                    (searchParams.get("tenantId") === "null" ? null : parseInt(searchParams.get("tenantId")!)) : 
                    undefined;

    console.log("🔓 Roles Bypass - GET:", { limit, offset, search, isSystemRole, tenantId });

    const result = await RoleService.searchRoles(tenantId, search, isSystemRole, limit, offset);

    return NextResponse.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error("❌ GET roles bypass error:", error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : "Failed to fetch roles"
    }, { status: 500 });
  }
}

// POST - Create role
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log("🔓 Roles Bypass - POST:", body);
    
    const validatedData = createRoleSchema.parse(body);
    const role = await RoleService.create(validatedData);

    return NextResponse.json({
      success: true,
      data: role,
      message: "Role created successfully",
    }, { status: 201 });
  } catch (error) {
    console.error("❌ POST roles bypass error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json({
        success: false,
        error: "Validation failed",
        details: error.errors
      }, { status: 400 });
    }

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : "Failed to create role"
    }, { status: 500 });
  }
}
