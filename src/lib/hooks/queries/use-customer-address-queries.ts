import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { CustomerAddress, NewCustomerAddress } from "@/lib/db/schema";

// Types for API operations
type CreateCustomerAddressData = Omit<NewCustomerAddress, "id" | "customerId" | "createdAt" | "updatedAt">;
type UpdateCustomerAddressData = Partial<CreateCustomerAddressData>;

// API functions
async function fetchCustomerAddresses(customerId: string): Promise<CustomerAddress[]> {
  const response = await fetch(`/api/customers/${customerId}/addresses`);
  if (!response.ok) {
    throw new Error("Failed to fetch customer addresses");
  }
  const data = await response.json();
  return data.addresses;
}

async function fetchCustomerAddress(customerId: string, addressId: string): Promise<CustomerAddress> {
  const response = await fetch(`/api/customers/${customerId}/addresses/${addressId}`);
  if (!response.ok) {
    throw new Error("Failed to fetch customer address");
  }
  const data = await response.json();
  return data.data;
}

async function createCustomerAddress(customerId: string, addressData: CreateCustomerAddressData): Promise<CustomerAddress> {
  const response = await fetch(`/api/customers/${customerId}/addresses`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(addressData),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || "Failed to create customer address");
  }

  const data = await response.json();
  return data.data;
}

async function updateCustomerAddress(
  customerId: string,
  addressId: string,
  addressData: UpdateCustomerAddressData
): Promise<CustomerAddress> {
  const response = await fetch(`/api/customers/${customerId}/addresses/${addressId}`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(addressData),
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || "Failed to update customer address");
  }

  const data = await response.json();
  return data.data;
}

async function deleteCustomerAddress(customerId: string, addressId: string): Promise<void> {
  const response = await fetch(`/api/customers/${customerId}/addresses/${addressId}`, {
    method: "DELETE",
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || "Failed to delete customer address");
  }
}

async function setPrimaryAddress(customerId: string, addressId: string): Promise<CustomerAddress> {
  const response = await fetch(`/api/customers/${customerId}/addresses/${addressId}?action=set-primary`, {
    method: "PATCH",
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.error || "Failed to set primary address");
  }

  const data = await response.json();
  return data.data;
}

// Query hooks
export function useCustomerAddresses(customerId: string) {
  return useQuery({
    queryKey: ["customer-addresses", customerId],
    queryFn: () => fetchCustomerAddresses(customerId),
    enabled: !!customerId,
  });
}

export function useCustomerAddress(customerId: string, addressId: string) {
  return useQuery({
    queryKey: ["customer-address", customerId, addressId],
    queryFn: () => fetchCustomerAddress(customerId, addressId),
    enabled: !!customerId && !!addressId,
  });
}

// Mutation hooks
export function useCreateCustomerAddress() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ customerId, addressData }: { customerId: string; addressData: CreateCustomerAddressData }) =>
      createCustomerAddress(customerId, addressData),
    onSuccess: (_, { customerId }) => {
      queryClient.invalidateQueries({ queryKey: ["customer-addresses", customerId] });
      queryClient.invalidateQueries({ queryKey: ["customers"] });
    },
  });
}

export function useUpdateCustomerAddress() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ 
      customerId, 
      addressId, 
      addressData 
    }: { 
      customerId: string; 
      addressId: string; 
      addressData: UpdateCustomerAddressData 
    }) =>
      updateCustomerAddress(customerId, addressId, addressData),
    onSuccess: (_, { customerId, addressId }) => {
      queryClient.invalidateQueries({ queryKey: ["customer-addresses", customerId] });
      queryClient.invalidateQueries({ queryKey: ["customer-address", customerId, addressId] });
      queryClient.invalidateQueries({ queryKey: ["customers"] });
    },
  });
}

export function useDeleteCustomerAddress() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ customerId, addressId }: { customerId: string; addressId: string }) =>
      deleteCustomerAddress(customerId, addressId),
    onSuccess: (_, { customerId }) => {
      queryClient.invalidateQueries({ queryKey: ["customer-addresses", customerId] });
      queryClient.invalidateQueries({ queryKey: ["customers"] });
    },
  });
}

export function useSetPrimaryAddress() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ customerId, addressId }: { customerId: string; addressId: string }) =>
      setPrimaryAddress(customerId, addressId),
    onSuccess: (_, { customerId }) => {
      queryClient.invalidateQueries({ queryKey: ["customer-addresses", customerId] });
      queryClient.invalidateQueries({ queryKey: ["customers"] });
    },
  });
}
