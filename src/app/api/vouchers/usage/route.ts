import { NextRequest, NextResponse } from "next/server";
import { VoucherService } from "@/lib/services/voucher.service";
import { z } from "zod";
import { withRBAC } from "@/lib/middleware/rbac-middleware";

// Validation schema for voucher usage recording
const recordUsageSchema = z.object({
  voucher_id: z.string().min(1, "Voucher ID is required"),
  customer_id: z.string().min(1, "Customer ID is required"),
  order_id: z.string().optional(),
  package_id: z.string().optional(),
  class_id: z.string().optional(),
  location_id: z.string().optional(),
  original_amount: z.number().int().positive("Original amount must be positive"),
  discount_amount: z.number().int().min(0, "Discount amount cannot be negative"),
  final_amount: z.number().int().min(0, "Final amount cannot be negative"),
  currency: z.string().length(3).default("USD"),
  ip_address: z.string().optional(),
  user_agent: z.string().optional(),
});

// POST /api/vouchers/usage - Record voucher usage
export async function POST(request: NextRequest) {
  return withRBAC(
    async () => {
      try {
        const body = await request.json();
        
        // Validate request body
        const validatedData = recordUsageSchema.parse(body);

        // Additional validation
        if (validatedData.discount_amount > validatedData.original_amount) {
          return NextResponse.json(
            { 
              error: "Discount amount cannot be greater than original amount",
              success: false 
            },
            { status: 400 }
          );
        }

        if (validatedData.final_amount !== validatedData.original_amount - validatedData.discount_amount) {
          return NextResponse.json(
            { 
              error: "Final amount must equal original amount minus discount amount",
              success: false 
            },
            { status: 400 }
          );
        }

        // Check if voucher exists
        const voucher = await VoucherService.getById(validatedData.voucher_id);
        if (!voucher) {
          return NextResponse.json(
            { error: "Voucher not found" },
            { status: 404 }
          );
        }

        // Check if voucher is still active and valid
        if (!voucher.is_active) {
          return NextResponse.json(
            { 
              error: "Voucher is not active",
              success: false 
            },
            { status: 400 }
          );
        }

        const now = new Date();
        if (now < new Date(voucher.valid_from) || now > new Date(voucher.valid_until)) {
          return NextResponse.json(
            { 
              error: "Voucher is not valid at this time",
              success: false 
            },
            { status: 400 }
          );
        }

        // Check usage limits
        if (voucher.usage_limit && voucher.current_usage_count >= voucher.usage_limit) {
          return NextResponse.json(
            { 
              error: "Voucher usage limit exceeded",
              success: false 
            },
            { status: 400 }
          );
        }

        // Check per-customer usage limit
        const customerUsageCount = await VoucherService.getCustomerUsageCount(
          validatedData.voucher_id,
          validatedData.customer_id
        );
        if (voucher.usage_limit_per_customer && customerUsageCount >= voucher.usage_limit_per_customer) {
          return NextResponse.json(
            { 
              error: "Customer has reached the usage limit for this voucher",
              success: false 
            },
            { status: 400 }
          );
        }

        // Get client IP and user agent from headers
        const clientIP = request.headers.get("x-forwarded-for") || 
                        request.headers.get("x-real-ip") || 
                        "unknown";
        const userAgent = request.headers.get("user-agent") || "unknown";

        // Record voucher usage
        const usageRecord = await VoucherService.recordVoucherUsage({
          ...validatedData,
          ip_address: validatedData.ip_address || clientIP,
          user_agent: validatedData.user_agent || userAgent,
        });

        return NextResponse.json({
          success: true,
          data: usageRecord,
          message: "Voucher usage recorded successfully",
        }, { status: 201 });
      } catch (error) {
        console.error("Error recording voucher usage:", error);
        
        if (error instanceof z.ZodError) {
          return NextResponse.json(
            { 
              error: "Validation failed",
              details: error.errors,
              success: false 
            },
            { status: 400 }
          );
        }

        return NextResponse.json(
          { 
            error: error instanceof Error ? error.message : "Failed to record voucher usage",
            success: false 
          },
          { status: 500 }
        );
      }
    },
    { module: "vouchers", action: "use" }
  )(request);
}
