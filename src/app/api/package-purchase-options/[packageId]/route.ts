import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { z } from "zod";
import { packagePurchaseOptionsService } from "@/lib/services/package-purchase-options.service";

// Validation schema for updates
const updatePackagePurchaseOptionsSchema = z.object({
  purchaseLimit: z.number().int().min(0, "Purchase limit cannot be negative").optional(),
  restrictTo: z.string().max(255, "Restrict to value too long").optional(),
  transferable: z.boolean().optional(),
  specifySoldAtLocation: z.boolean().optional(),
  soldAtLocationId: z.string().optional(),
  classBookingLimit: z.number().int().min(0, "Class booking limit cannot be negative").optional(),
  showOnline: z.boolean().optional(),
});

/**
 * GET /api/package-purchase-options/[packageId]
 * Get package purchase options by package ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ packageId: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { packageId } = await params;
    console.log(`📦 [GET /api/package-purchase-options/${packageId}] Fetching package purchase options`);

    const packagePurchaseOptions = await packagePurchaseOptionsService.getByPackageId(packageId);

    if (!packagePurchaseOptions) {
      return NextResponse.json({ error: "Package purchase options not found" }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: packagePurchaseOptions
    });
  } catch (error) {
    console.error(`📦 [GET /api/package-purchase-options/${params}] Error:`, error);
    return NextResponse.json(
      { error: "Failed to fetch package purchase options" },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/package-purchase-options/[packageId]
 * Update package purchase options by package ID
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ packageId: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { packageId } = await params;
    const body = await request.json();
    const validatedData = updatePackagePurchaseOptionsSchema.parse(body);

    console.log(`📦 [PUT /api/package-purchase-options/${packageId}] Updating package purchase options:`, validatedData);

    const updatedPackagePurchaseOptions = await packagePurchaseOptionsService.update(packageId, validatedData);

    return NextResponse.json({
      success: true,
      data: updatedPackagePurchaseOptions,
      message: "Package purchase options updated successfully"
    });
  } catch (error) {
    console.error(`📦 [PUT /api/package-purchase-options/${params}] Error:`, error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid package purchase options data", details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      if (error.message.includes("not found")) {
        return NextResponse.json(
          { error: error.message },
          { status: 404 }
        );
      }

      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to update package purchase options" },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/package-purchase-options/[packageId]
 * Delete package purchase options by package ID
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ packageId: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { packageId } = await params;
    console.log(`📦 [DELETE /api/package-purchase-options/${packageId}] Deleting package purchase options`);

    const deleted = await packagePurchaseOptionsService.deleteByPackageId(packageId);

    if (!deleted) {
      return NextResponse.json({ error: "Package purchase options not found" }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      message: "Package purchase options deleted successfully"
    });
  } catch (error) {
    console.error(`📦 [DELETE /api/package-purchase-options/${params}] Error:`, error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to delete package purchase options" },
      { status: 500 }
    );
  }
}
