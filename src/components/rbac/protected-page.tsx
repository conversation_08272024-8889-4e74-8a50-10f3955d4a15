"use client";

import { ReactNode, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useRBAC } from "@/lib/hooks/use-rbac";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { ShieldX, ArrowLeft } from "lucide-react";

/**
 * Protected Page Component
 * 
 * Component ini wrap entire page untuk protection.
 * Bisa check permissions, roles, atau location access.
 */

interface ProtectedPageProps {
  /** Children yang akan dirender kalau punya akses */
  children: ReactNode;
  /** Required permissions untuk akses page */
  requiredPermissions?: Array<{ module: string; action: string }>;
  /** Required roles untuk akses page */
  requiredRoles?: string[];
  /** Required location access */
  requiredLocationId?: string;
  /** Redirect URL kalau gak punya akses */
  redirectTo?: string;
  /** Show error page instead of redirect */
  showErrorPage?: boolean;
  /** Custom error message */
  errorMessage?: string;
  /** Loading component */
  loadingComponent?: ReactNode;
}

export function ProtectedPage({
  children,
  requiredPermissions = [],
  requiredRoles = [],
  requiredLocationId,
  redirectTo = "/dashboard",
  showErrorPage = false,
  errorMessage,
  loadingComponent,
}: ProtectedPageProps) {
  const { status } = useSession();
  const router = useRouter();
  const {
    isLoading,
    isAuthenticated,
    hasPermission,
    hasRole,
    canAccessLocation,
    isSuperAdmin,
  } = useRBAC();

  // Check access
  const hasRequiredPermissions = requiredPermissions.every(({ module, action }) =>
    hasPermission(module, action)
  );

  const hasRequiredRoles = requiredRoles.length === 0 || requiredRoles.some(role => hasRole(role));

  const hasLocationAccess = !requiredLocationId || 
    isSuperAdmin() || 
    canAccessLocation(requiredLocationId);

  const hasAccess = hasRequiredPermissions && hasRequiredRoles && hasLocationAccess;

  // Handle redirect
  useEffect(() => {
    if (!isLoading && isAuthenticated && !hasAccess && !showErrorPage) {
      router.push(redirectTo);
    }
  }, [isLoading, isAuthenticated, hasAccess, showErrorPage, redirectTo, router]);

  // Show loading
  if (isLoading || status === "loading") {
    return loadingComponent || (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Not authenticated - akan di-handle sama middleware
  if (!isAuthenticated) {
    return null;
  }

  // No access - show error page
  if (!hasAccess && showErrorPage) {
    return <AccessDeniedPage errorMessage={errorMessage} />;
  }

  // No access - redirect (handled by useEffect)
  if (!hasAccess) {
    return null;
  }

  // Has access - render children
  return <>{children}</>;
}

/**
 * Access Denied Page Component
 */
function AccessDeniedPage({ errorMessage }: { errorMessage?: string }) {
  const router = useRouter();

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8 p-8">
        <div className="text-center">
          <ShieldX className="mx-auto h-24 w-24 text-red-500" />
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            Akses Ditolak
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            {errorMessage || "Anda tidak memiliki permission untuk mengakses halaman ini"}
          </p>
        </div>

        <div className="space-y-4">
          <Button
            onClick={() => router.back()}
            variant="outline"
            className="w-full"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Kembali
          </Button>
          
          <Button
            onClick={() => router.push("/dashboard")}
            className="w-full"
          >
            Ke Dashboard
          </Button>
        </div>
      </div>
    </div>
  );
}

/**
 * HOC untuk protect pages
 * 
 * Ini alternative approach kalau mau pake HOC pattern.
 */
export function withPageProtection<P extends object>(
  Component: React.ComponentType<P>,
  protectionConfig: Omit<ProtectedPageProps, "children">
) {
  return function ProtectedComponent(props: P) {
    return (
      <ProtectedPage {...protectionConfig}>
        <Component {...props} />
      </ProtectedPage>
    );
  };
}

/**
 * Hook untuk check page access
 * 
 * Ini useful kalau mau handle protection logic manually.
 */
export function usePageAccess({
  requiredPermissions = [],
  requiredRoles = [],
  requiredLocationId,
}: {
  requiredPermissions?: Array<{ module: string; action: string }>;
  requiredRoles?: string[];
  requiredLocationId?: string;
}) {
  const {
    isLoading,
    isAuthenticated,
    hasPermission,
    hasRole,
    canAccessLocation,
    isSuperAdmin,
  } = useRBAC();

  const hasRequiredPermissions = requiredPermissions.every(({ module, action }) =>
    hasPermission(module, action)
  );

  const hasRequiredRoles = requiredRoles.length === 0 || requiredRoles.some(role => hasRole(role));

  const hasLocationAccess = !requiredLocationId || 
    isSuperAdmin() || 
    canAccessLocation(requiredLocationId);

  const hasAccess = hasRequiredPermissions && hasRequiredRoles && hasLocationAccess;

  return {
    isLoading,
    isAuthenticated,
    hasAccess,
    hasRequiredPermissions,
    hasRequiredRoles,
    hasLocationAccess,
  };
}
