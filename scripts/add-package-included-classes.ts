// Script untuk menambahkan included_classes JSONB field ke packages table
// Run dengan: npx tsx scripts/add-package-included-classes.ts

import { db } from "../src/lib/db";
import { sql } from "drizzle-orm";

async function addPackageIncludedClasses() {
  try {
    console.log("🚀 Menambahkan included_classes field ke packages table...");

    // Step 1: Tambahkan kolom JSONB baru
    console.log("📝 Menambahkan kolom included_classes...");
    
    await db.execute(sql`
      ALTER TABLE package 
      ADD COLUMN IF NOT EXISTS included_classes JSONB DEFAULT '[]'::jsonb
    `);

    console.log("✅ Kolom included_classes berhasil ditambahkan");

    // Step 2: Verifikasi kolom sudah ada
    console.log("🔍 Memverifikasi kolom baru...");
    
    const columnExists = await db.execute(sql`
      SELECT column_name, data_type, column_default
      FROM information_schema.columns 
      WHERE table_name = 'package' 
      AND column_name = 'included_classes';
    `);

    if (columnExists.rows.length > 0) {
      console.log("✅ Verifikasi berhasil:");
      console.table(columnExists.rows);
    } else {
      console.log("❌ Verifikasi gagal: Kolom tidak ditemukan");
      return;
    }

    // Step 3: Test insert sample data
    console.log("🧪 Testing dengan sample data...");
    
    const sampleIncludedClasses = [
      {
        id: "class_123",
        name: "Yoga Beginner",
        category: "Yoga",
        included_at: new Date().toISOString()
      },
      {
        id: "class_456", 
        name: "Pilates Advanced",
        category: "Pilates",
        included_at: new Date().toISOString()
      }
    ];

    // Cek apakah ada package untuk testing
    const testPackages = await db.execute(sql`
      SELECT id, name FROM package LIMIT 1;
    `);

    if (testPackages.rows.length > 0) {
      const testPackageId = testPackages.rows[0].id;
      console.log(`📦 Testing dengan package: ${testPackages.rows[0].name}`);
      
      // Update package dengan sample included_classes
      await db.execute(sql`
        UPDATE package 
        SET included_classes = ${JSON.stringify(sampleIncludedClasses)}::jsonb
        WHERE id = ${testPackageId};
      `);

      // Verify update
      const updatedPackage = await db.execute(sql`
        SELECT name, included_classes 
        FROM package 
        WHERE id = ${testPackageId};
      `);

      console.log("✅ Test update berhasil:");
      console.log("Package:", updatedPackage.rows[0].name);
      console.log("Included Classes:", JSON.stringify(updatedPackage.rows[0].included_classes, null, 2));

      // Reset ke array kosong
      await db.execute(sql`
        UPDATE package 
        SET included_classes = '[]'::jsonb
        WHERE id = ${testPackageId};
      `);
      console.log("🔄 Reset test data ke array kosong");

    } else {
      console.log("📋 Tidak ada package untuk testing, tapi kolom sudah siap digunakan");
    }

    console.log("\n🎉 Migration berhasil!");
    console.log("\n📝 Yang sudah ditambahkan:");
    console.log("   ✅ Kolom included_classes (JSONB) di packages table");
    console.log("   ✅ Default value: [] (array kosong)");
    console.log("   ✅ TypeScript interface: PackageIncludedClass");
    console.log("   ✅ Service layer support");
    console.log("   ✅ API validation schema");

    console.log("\n🔧 Next Steps:");
    console.log("   1. Update package form dengan class selector");
    console.log("   2. Implementasi multi-select component");
    console.log("   3. Test end-to-end workflow");

  } catch (error) {
    console.error("💥 Migration gagal:", error);
    throw error;
  }
}

// Jalankan migration
addPackageIncludedClasses()
  .then(() => {
    console.log("✅ Migration script berhasil dijalankan");
    process.exit(0);
  })
  .catch((error) => {
    console.error("💥 Migration script gagal:", error);
    process.exit(1);
  });
