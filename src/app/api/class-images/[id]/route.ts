import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { ClassImageService } from "@/lib/services/class-image.service";

/**
 * API Routes untuk Individual Class Image Operations
 * 
 * Handle GET, PUT, DELETE untuk single class image berdasarkan ID.
 * <PERSON>gi<PERSON><PERSON> pattern yang sama dengan classes/[id] dan class-categories/[id].
 */

// Validation schemas
const paramsSchema = z.object({
  id: z.string().min(1, "ID is required"),
});

const updateClassImageSchema = z.object({
  image_url: z.string().min(1, "Image URL is required").refine((url) => {
    // Accept both full URLs and local paths
    return url.startsWith('http') || url.startsWith('https') || url.startsWith('/');
  }, "Invalid image URL format").optional(),
});

/**
 * GET /api/class-images/[id]
 * Get a single class image by ID
 * 
 * Params:
 * - id: class image ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Validate params
    const resolvedParams = await params;
    const validatedParams = paramsSchema.parse(resolvedParams);

    // Get image by ID
    const imageResult = await ClassImageService.getById(validatedParams.id);

    if (!imageResult) {
      return NextResponse.json(
        { success: false, error: "Class image not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: imageResult,
    });
  } catch (error) {
    console.error("GET /api/class-images/[id] error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: "Invalid parameters", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/class-images/[id]
 * Update a class image
 * 
 * Params:
 * - id: class image ID
 * Body:
 * - image_url: new image URL (optional)
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Validate params
    const resolvedParams = await params;
    const validatedParams = paramsSchema.parse(resolvedParams);
    
    // Validate request body
    const body = await request.json();
    const validatedData = updateClassImageSchema.parse(body);

    // Update image
    const updatedImage = await ClassImageService.update(
      validatedParams.id,
      validatedData
    );

    if (!updatedImage) {
      return NextResponse.json(
        { success: false, error: "Class image not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: updatedImage,
      message: "Class image updated successfully",
    });
  } catch (error) {
    console.error("PUT /api/class-images/[id] error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error && error.message.includes("Invalid image URL")) {
      return NextResponse.json(
        { success: false, error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/class-images/[id]
 * Delete a class image
 * 
 * Params:
 * - id: class image ID
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Validate params
    const resolvedParams = await params;
    const validatedParams = paramsSchema.parse(resolvedParams);

    // Delete image
    await ClassImageService.delete(validatedParams.id);

    return NextResponse.json({
      success: true,
      message: "Class image deleted successfully",
    });
  } catch (error) {
    console.error("DELETE /api/class-images/[id] error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error && error.message.includes("not found")) {
      return NextResponse.json(
        { success: false, error: "Class image not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}
