import { db } from "@/lib/db";
import { membership_plan_locations, membership_plans, locations } from "@/lib/db/schema";
import { eq, and, inArray } from "drizzle-orm";
import { createId } from "@paralleldrive/cuid2";
import { BaseService } from "../core/base-service";

export interface MembershipPlanLocationAssignment {
  membershipPlanId: string;
  locationIds: string[];
}

export interface MembershipPlanWithLocations {
  id: string;
  name: string;
  description: string | null;
  price: number | null;
  currency: string | null;
  duration_value: number | null;
  duration_unit: string | null;
  is_active: boolean;
  assignedLocations: Array<{
    id: string;
    name: string | null;
  }>;
}

export class MembershipPlanLocationService extends BaseService {
  /**
   * Get all membership plans with their assigned locations for a tenant
   */
  static async getMembershipPlansWithLocations(tenantId: number): Promise<MembershipPlanWithLocations[]> {
    try {
      // Get all membership plans for the tenant
      const plans = await db
        .select()
        .from(membership_plans)
        .where(eq(membership_plans.tenantId, tenantId));

      // Get all location assignments for these plans
      const planIds = plans.map(p => p.id);
      const assignments = planIds.length > 0 ? await db
        .select({
          membershipPlanId: membership_plan_locations.membership_plan_id,
          locationId: membership_plan_locations.location_id,
          locationName: locations.name,
        })
        .from(membership_plan_locations)
        .innerJoin(locations, eq(membership_plan_locations.location_id, locations.id))
        .where(
          and(
            inArray(membership_plan_locations.membership_plan_id, planIds),
            eq(membership_plan_locations.tenantId, tenantId)
          )
        ) : [];

      // Combine plans with their assigned locations
      return plans.map(plan => ({
        id: plan.id,
        name: plan.name,
        description: plan.description,
        price: plan.price,
        currency: plan.currency,
        duration_value: plan.duration_value,
        duration_unit: plan.duration_unit,
        is_active: plan.is_active,
        assignedLocations: assignments
          .filter(a => a.membershipPlanId === plan.id)
          .map(a => ({
            id: a.locationId,
            name: a.locationName,
          })),
      }));
    } catch (error) {
      console.error("Error getting membership plans with locations:", error);
      throw new Error("Failed to fetch membership plans with locations");
    }
  }

  /**
   * Get assigned locations for a specific membership plan
   */
  static async getAssignedLocations(membershipPlanId: string, tenantId: number) {
    try {
      return await db
        .select({
          id: locations.id,
          name: locations.name,
        })
        .from(membership_plan_locations)
        .innerJoin(locations, eq(membership_plan_locations.location_id, locations.id))
        .where(
          and(
            eq(membership_plan_locations.membership_plan_id, membershipPlanId),
            eq(membership_plan_locations.tenantId, tenantId)
          )
        );
    } catch (error) {
      console.error("Error getting assigned locations:", error);
      throw new Error("Failed to fetch assigned locations");
    }
  }

  /**
   * Assign membership plan to multiple locations
   */
  static async assignToLocations(
    membershipPlanId: string,
    locationIds: string[],
    tenantId: number
  ) {
    try {
      // Validate that membership plan belongs to tenant
      const plan = await db
        .select()
        .from(membership_plans)
        .where(
          and(
            eq(membership_plans.id, membershipPlanId),
            eq(membership_plans.tenantId, tenantId)
          )
        );

      if (plan.length === 0) {
        throw new Error("Membership plan not found or access denied");
      }

      // Validate that all locations belong to tenant
      const validLocations = await db
        .select({ id: locations.id })
        .from(locations)
        .where(
          and(
            inArray(locations.id, locationIds),
            eq(locations.tenantId, tenantId)
          )
        );

      if (validLocations.length !== locationIds.length) {
        throw new Error("Some locations not found or access denied");
      }

      // Remove existing assignments
      await db
        .delete(membership_plan_locations)
        .where(
          and(
            eq(membership_plan_locations.membership_plan_id, membershipPlanId),
            eq(membership_plan_locations.tenantId, tenantId)
          )
        );

      // Add new assignments
      if (locationIds.length > 0) {
        const assignments = locationIds.map(locationId => ({
          id: createId(),
          membership_plan_id: membershipPlanId,
          location_id: locationId,
          tenantId,
        }));

        await db.insert(membership_plan_locations).values(assignments);
      }

      return { success: true };
    } catch (error) {
      console.error("Error assigning to locations:", error);
      throw new Error("Failed to assign membership plan to locations");
    }
  }

  /**
   * Remove membership plan from specific locations
   */
  static async removeFromLocations(
    membershipPlanId: string,
    locationIds: string[],
    tenantId: number
  ) {
    try {
      await db
        .delete(membership_plan_locations)
        .where(
          and(
            eq(membership_plan_locations.membership_plan_id, membershipPlanId),
            inArray(membership_plan_locations.location_id, locationIds),
            eq(membership_plan_locations.tenantId, tenantId)
          )
        );

      return { success: true };
    } catch (error) {
      console.error("Error removing from locations:", error);
      throw new Error("Failed to remove membership plan from locations");
    }
  }

  /**
   * Get all available locations for a tenant (for assignment UI)
   */
  static async getAvailableLocations(tenantId: number) {
    try {
      return await db
        .select({
          id: locations.id,
          name: locations.name,
        })
        .from(locations)
        .where(eq(locations.tenantId, tenantId));
    } catch (error) {
      console.error("Error getting available locations:", error);
      throw new Error("Failed to fetch available locations");
    }
  }

  /**
   * Bulk update location assignments for multiple membership plans
   */
  static async bulkUpdateAssignments(
    assignments: MembershipPlanLocationAssignment[],
    tenantId: number
  ) {
    try {
      // Process each assignment
      for (const assignment of assignments) {
        await this.assignToLocations(
          assignment.membershipPlanId,
          assignment.locationIds,
          tenantId
        );
      }

      return { success: true };
    } catch (error) {
      console.error("Error bulk updating assignments:", error);
      throw new Error("Failed to bulk update location assignments");
    }
  }
}
