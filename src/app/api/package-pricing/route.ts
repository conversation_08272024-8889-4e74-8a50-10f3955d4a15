import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { z } from "zod";
import { packagePricingService } from "@/lib/services/package-pricing.service";

// Validation schemas
const createPackagePricingSchema = z.object({
  packageId: z.string().min(1, "Package ID is required"),
  pricingGroupId: z.string().min(1, "Pricing group ID is required"),
  price: z.number().min(0, "Price cannot be negative").optional(),
  creditAmount: z.number().int().min(0, "Credit amount cannot be negative").optional(),
  currency: z.string().min(1, "Currency is required").max(10, "Currency code too long").optional(),
}).refine(
  (data) => data.price !== undefined || data.creditAmount !== undefined,
  {
    message: "Either price or credit amount must be specified",
    path: ["price"],
  }
);

const querySchema = z.object({
  search: z.string().optional(),
  packageId: z.string().optional(),
  pricingGroupId: z.string().optional(),
  tenantId: z.string().optional(),
  minPrice: z.string().optional(),
  maxPrice: z.string().optional(),
  currency: z.string().optional(),
  limit: z.string().optional(),
  offset: z.string().optional(),
});

const bulkOperationSchema = z.object({
  action: z.enum(["create", "delete", "update"]),
  data: z.array(createPackagePricingSchema).optional(),
  ids: z.array(z.string().min(1)).optional(),
  updates: z.array(z.object({
    id: z.string().min(1),
    data: z.object({
      packageId: z.string().optional(),
      pricingGroupId: z.string().optional(),
      price: z.number().min(0).optional(),
      creditAmount: z.number().int().min(0).optional(),
      currency: z.string().max(10).optional(),
    })
  })).optional(),
}).refine((data) => {
  if (data.action === "create" && (!data.data || data.data.length === 0)) {
    return false;
  }
  if (data.action === "delete" && (!data.ids || data.ids.length === 0)) {
    return false;
  }
  if (data.action === "update" && (!data.updates || data.updates.length === 0)) {
    return false;
  }
  return true;
}, {
  message: "Invalid bulk operation data for the specified action",
});

/**
 * GET /api/package-pricing
 * Get package pricing for the current tenant with optional filtering
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const tenantIdParam = searchParams.get("tenantId");
    const queryParams = Object.fromEntries(searchParams.entries());

    // For now, require tenantId in query params since session.user doesn't have tenantId
    const tenantId = tenantIdParam ? parseInt(tenantIdParam) : null;

    if (!tenantId) {
      return NextResponse.json({ error: "Tenant ID required in query parameters" }, { status: 400 });
    }

    const validatedQuery = querySchema.parse(queryParams);

    // Build filters
    const filters = {
      search: validatedQuery.search,
      packageId: validatedQuery.packageId,
      pricingGroupId: validatedQuery.pricingGroupId,
      minPrice: validatedQuery.minPrice ? parseFloat(validatedQuery.minPrice) : undefined,
      maxPrice: validatedQuery.maxPrice ? parseFloat(validatedQuery.maxPrice) : undefined,
      currency: validatedQuery.currency,
    };

    console.log(`💰 [GET /api/package-pricing] Fetching package pricing for tenant ${tenantId}:`, filters);

    const packagePricing = await packagePricingService.getByTenant(tenantId, filters);

    return NextResponse.json({
      success: true,
      data: packagePricing,
      meta: {
        total: packagePricing.length,
        filters,
      }
    });
  } catch (error) {
    console.error("💰 [GET /api/package-pricing] Error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid query parameters", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to fetch package pricing" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/package-pricing
 * Create a new package pricing
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = createPackagePricingSchema.parse(body);

    console.log(`💰 [POST /api/package-pricing] Creating package pricing:`, validatedData);

    const packagePricing = await packagePricingService.create(validatedData);

    return NextResponse.json({
      success: true,
      data: packagePricing,
      message: "Package pricing created successfully"
    }, { status: 201 });
  } catch (error) {
    console.error("💰 [POST /api/package-pricing] Error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid package pricing data", details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error && error.message.includes("already exists")) {
      return NextResponse.json(
        { error: error.message },
        { status: 409 }
      );
    }

    return NextResponse.json(
      { error: "Failed to create package pricing" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/package-pricing/bulk
 * Bulk operations for package pricing (create, update, delete)
 */
export async function DELETE(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = bulkOperationSchema.parse(body);

    console.log(`💰 [DELETE /api/package-pricing] Bulk operation:`, validatedData);

    if (validatedData.action === "delete") {
      const results = await packagePricingService.bulkOperation({
        ids: validatedData.ids,
        action: "delete"
      });

      return NextResponse.json({
        success: true,
        data: results,
        message: `Successfully deleted ${validatedData.ids.length} package pricing records`
      });
    }

    return NextResponse.json(
      { error: "Invalid bulk operation action" },
      { status: 400 }
    );
  } catch (error) {
    console.error("💰 [DELETE /api/package-pricing] Error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid bulk operation data", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to perform bulk operation" },
      { status: 500 }
    );
  }
}
