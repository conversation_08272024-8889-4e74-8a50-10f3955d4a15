# Docker Deployment Guide

This guide provides comprehensive Docker deployment configurations following FAANG-level best practices for scalable, production-ready Next.js applications.

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │    │   Application   │    │    Database     │
│     (Nginx)     │◄──►│   (Next.js)     │◄──►│  (PostgreSQL)   │
│                 │    │                 │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Monitoring    │    │     Caching     │    │    Logging      │
│  (Prometheus)   │    │    (Redis)      │    │   (Fluentd)     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📁 File Structure

```
├── docker-compose.yml          # Base configuration
├── docker-compose.dev.yml      # Development overrides
├── docker-compose.prod.yml     # Production overrides
├── Dockerfile                  # Multi-stage optimized build
├── nginx/
│   ├── nginx.conf             # Base nginx configuration
│   ├── dev.conf               # Development nginx config
│   └── prod.conf              # Production nginx config
├── monitoring/
│   └── prometheus.yml         # Monitoring configuration
├── scripts/
│   └── deploy.sh              # Deployment automation
└── .env.example               # Environment variables template
```

## 🚀 Quick Start

### Prerequisites

- Docker 20.10+
- Docker Compose 2.0+
- Existing PostgreSQL container named `docker-postgis-1`

### Development Environment

1. **Setup environment variables:**
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your development values
   ```

2. **Deploy development stack:**
   ```bash
   chmod +x scripts/deploy.sh
   ./scripts/deploy.sh development
   ```

3. **Access services:**
   - Application: http://localhost:3000
   - PgAdmin: http://localhost:5050
   - Redis: localhost:6379

### Production Environment

1. **Setup environment variables:**
   ```bash
   cp .env.example .env.production
   # Edit .env.production with your production values
   ```

2. **Deploy production stack:**
   ```bash
   ./scripts/deploy.sh production
   ```

3. **Access services:**
   - Application: http://localhost
   - Monitoring: http://localhost:9090

## 🔧 Configuration Details

### Multi-Stage Dockerfile

The optimized Dockerfile includes:
- **Base stage**: Common Node.js setup
- **Dependencies stage**: Separate prod/dev dependencies
- **Builder stage**: Application build
- **Development stage**: Hot reload and debugging
- **Production stage**: Minimal runtime image

### Environment-Specific Configurations

#### Development Features
- Hot reload with volume mounting
- Source code synchronization
- Development debugging ports
- PgAdmin for database management
- Relaxed security settings
- Enhanced logging

#### Production Features
- Multi-replica deployment
- Health checks and monitoring
- Resource limits and reservations
- Security hardening (read-only filesystem, non-root user)
- SSL/TLS configuration
- Rate limiting and caching
- Log aggregation

### Nginx Configuration

#### Load Balancing
- Least connections algorithm
- Health checks with failover
- Connection pooling
- Request buffering

#### Caching Strategy
- Static assets: 1 year cache
- API responses: 5 minutes cache
- Next.js static files: 1 year cache
- Proxy cache with stale-while-revalidate

#### Security Features
- Rate limiting (API: 10r/s, Auth: 1r/s)
- Security headers (CSP, HSTS, etc.)
- Connection limiting
- Request size limits

## 📊 Monitoring and Observability

### Health Checks
- Application health endpoint
- Database connectivity checks
- Nginx status monitoring
- Redis health verification

### Metrics Collection
- Prometheus for metrics aggregation
- Custom application metrics
- Infrastructure monitoring
- Performance tracking

### Logging
- Structured JSON logging
- Log rotation and retention
- Centralized log aggregation
- Error tracking and alerting

## 🔒 Security Best Practices

### Container Security
- Non-root user execution
- Read-only filesystem
- Minimal attack surface
- Security context constraints

### Network Security
- Isolated networks
- Service-to-service communication
- External network restrictions
- SSL/TLS encryption

### Data Protection
- Environment variable encryption
- Secrets management
- Database connection security
- Input validation and sanitization

## 🚀 Scaling and Performance

### Horizontal Scaling
```bash
# Scale application instances
docker-compose -f docker-compose.yml -f docker-compose.prod.yml \
  up --scale app=5 -d
```

### Resource Management
- CPU and memory limits
- Resource reservations
- Quality of Service classes
- Auto-scaling capabilities

### Performance Optimizations
- Multi-stage builds for smaller images
- Layer caching optimization
- Gzip compression
- Static asset optimization

## 🛠️ Maintenance and Operations

### Deployment Commands

```bash
# Development deployment
./scripts/deploy.sh development

# Production deployment
./scripts/deploy.sh production

# View logs
docker-compose logs -f app

# Scale services
docker-compose up --scale app=3 -d

# Update services
docker-compose pull && docker-compose up -d

# Backup database
docker exec postgres-container pg_dump -U user database > backup.sql
```

### Health Monitoring

```bash
# Check service health
docker-compose ps

# View resource usage
docker stats

# Check logs
docker-compose logs --tail=100 app
```

### Troubleshooting

#### Common Issues

1. **Database Connection Failed**
   ```bash
   # Check PostgreSQL container
   docker ps | grep postgis
   # Verify network connectivity
   docker network ls
   ```

2. **Application Won't Start**
   ```bash
   # Check environment variables
   docker-compose config
   # View detailed logs
   docker-compose logs app
   ```

3. **Performance Issues**
   ```bash
   # Monitor resource usage
   docker stats
   # Check nginx access logs
   docker-compose logs nginx
   ```

## 🔄 CI/CD Integration

### GitHub Actions Example

```yaml
name: Deploy to Production
on:
  push:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Deploy to production
        run: |
          ./scripts/deploy.sh production
```

### Environment Promotion

```bash
# Test in staging
./scripts/deploy.sh staging

# Promote to production
./scripts/deploy.sh production
```

## 📈 Performance Benchmarks

Expected performance metrics:
- **Cold start**: < 5 seconds
- **Response time**: < 200ms (95th percentile)
- **Throughput**: > 1000 RPS
- **Memory usage**: < 512MB per instance
- **CPU usage**: < 50% under normal load

## 🆘 Support and Troubleshooting

For issues and support:
1. Check the logs: `docker-compose logs`
2. Verify configuration: `docker-compose config`
3. Test connectivity: `docker-compose exec app ping database`
4. Review resource usage: `docker stats`

## 📚 Additional Resources

- [Docker Best Practices](https://docs.docker.com/develop/best-practices/)
- [Next.js Deployment](https://nextjs.org/docs/deployment)
- [Nginx Configuration](https://nginx.org/en/docs/)
- [Prometheus Monitoring](https://prometheus.io/docs/)