import { NextRequest, NextResponse } from "next/server";
import { BlogService } from "@/lib/services/blog.service";
import { updateBlogPostSchema } from "@/lib/validations/blog";
import { withRBAC } from "@/lib/middleware/rbac-middleware";
import { z } from "zod";

interface RouteParams {
  params: Promise<{ id: string }>;
}

// GET /api/blog-posts/[id] - Get single blog post
export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  return withRBAC(
    async () => {
      try {
        const { id } = await params;

        if (!id) {
          return NextResponse.json(
            { error: "Blog post ID is required" },
            { status: 400 }
          );
        }

        const blogPost = await BlogService.getById(id);

        if (!blogPost) {
          return NextResponse.json(
            { error: "Blog post not found" },
            { status: 404 }
          );
        }

        return NextResponse.json({
          success: true,
          data: blogPost,
        });
      } catch (error) {
        console.error("Error fetching blog post:", error);
        return NextResponse.json(
          { 
            error: error instanceof Error ? error.message : "Failed to fetch blog post",
            success: false 
          },
          { status: 500 }
        );
      }
    },
    { module: "blog", action: "read" }
  )(request);
}

// PUT /api/blog-posts/[id] - Update blog post
export async function PUT(
  request: NextRequest,
  { params }: RouteParams
) {
  return withRBAC(
    async () => {
      try {
        const { id } = await params;
        const body = await request.json();

        if (!id) {
          return NextResponse.json(
            { error: "Blog post ID is required" },
            { status: 400 }
          );
        }

        // Validate request body
        const validatedData = updateBlogPostSchema.parse(body);

        // If slug is being updated, check availability
        if (validatedData.slug) {
          const isSlugAvailable = await BlogService.isSlugAvailable(
            body.tenantId || 1, // Should get from context in real implementation
            validatedData.slug,
            id
          );

          if (!isSlugAvailable) {
            return NextResponse.json(
              { error: "Slug already exists" },
              { status: 400 }
            );
          }
        }

        // Update blog post
        const updatedPost = await BlogService.update(id, validatedData);

        // Fetch the updated post with relations
        const blogPost = await BlogService.getById(updatedPost.id);

        return NextResponse.json({
          success: true,
          data: blogPost,
          message: "Blog post updated successfully",
        });
      } catch (error) {
        console.error("Error updating blog post:", error);
        
        if (error instanceof z.ZodError) {
          return NextResponse.json(
            { 
              error: "Validation failed",
              details: error.errors,
              success: false 
            },
            { status: 400 }
          );
        }

        return NextResponse.json(
          { 
            error: error instanceof Error ? error.message : "Failed to update blog post",
            success: false 
          },
          { status: 500 }
        );
      }
    },
    { module: "blog", action: "update" }
  )(request);
}

// DELETE /api/blog-posts/[id] - Delete blog post
export async function DELETE(
  request: NextRequest,
  { params }: RouteParams
) {
  return withRBAC(
    async () => {
      try {
        const { id } = await params;

        if (!id) {
          return NextResponse.json(
            { error: "Blog post ID is required" },
            { status: 400 }
          );
        }

        await BlogService.delete(id);

        return NextResponse.json({
          success: true,
          message: "Blog post deleted successfully",
        });
      } catch (error) {
        console.error("Error deleting blog post:", error);
        return NextResponse.json(
          { 
            error: error instanceof Error ? error.message : "Failed to delete blog post",
            success: false 
          },
          { status: 500 }
        );
      }
    },
    { module: "blog", action: "delete" }
  )(request);
}
