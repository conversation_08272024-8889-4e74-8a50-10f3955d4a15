import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { RoleService } from "@/lib/services/role.service";
import { withRBAC } from "@/lib/middleware/rbac-middleware";

/**
 * API Routes untuk Roles
 * 
 * <PERSON><PERSON><PERSON><PERSON> pattern yang sama dengan class-categories dan classes.
 * Semua validation menggunakan Zod, error handling yang proper, dan RBAC protection.
 */

// Validation schemas
const searchParamsSchema = z.object({
  tenantId: z.string().optional().transform((val) => {
    if (!val || val === 'null') return null;
    const parsed = parseInt(val, 10);
    return isNaN(parsed) ? null : parsed;
  }),
  search: z.string().optional(),
  isSystemRole: z.string().optional().transform((val) => {
    if (!val) return undefined;
    return val === 'true';
  }),
  limit: z.string().transform((val) => parseInt(val, 10)).optional(),
  offset: z.string().transform((val) => parseInt(val, 10)).optional(),
});

const createRoleSchema = z.object({
  tenantId: z.number().int().positive("Tenant ID must be positive").optional().nullable(),
  name: z.string().min(1, "Name is required").max(100, "Name too long"),
  display_name: z.string().min(1, "Display name is required").max(255, "Display name too long"),
  description: z.string().max(255, "Description too long").optional(),
  is_system_role: z.boolean().optional(),
  hierarchy_level: z.number().int().min(0).max(100).optional(),
  permissions: z.array(z.string()).optional(),
});

/**
 * GET /api/roles
 * Search roles with filtering and pagination
 */
export const GET = withRBAC(
  async (request: NextRequest) => {
    try {
      const { searchParams } = new URL(request.url);
      
      // Parse and validate query parameters
      const rawParams = {
        tenantId: searchParams.get("tenantId"),
        search: searchParams.get("search"),
        isSystemRole: searchParams.get("isSystemRole"),
        limit: searchParams.get("limit"),
        offset: searchParams.get("offset"),
      };

      // Remove null values
      const cleanParams = Object.fromEntries(
        Object.entries(rawParams).filter(([_, value]) => value !== null)
      );

      const validatedParams = searchParamsSchema.parse(cleanParams);

      // Call service
      const result = await RoleService.searchRoles(
        validatedParams.tenantId,
        validatedParams.search,
        validatedParams.isSystemRole,
        validatedParams.limit || 20,
        validatedParams.offset || 0
      );

      return NextResponse.json({
        success: true,
        data: result,
      });
    } catch (error) {
      console.error("GET /api/roles error:", error);
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { success: false, error: "Invalid parameters", details: error.errors },
          { status: 400 }
        );
      }

      return NextResponse.json(
        { success: false, error: "Internal server error" },
        { status: 500 }
      );
    }
  },
  { module: "roles", action: "read" }
);

/**
 * POST /api/roles
 * Create a new role
 */
export const POST = withRBAC(
  async (request: NextRequest) => {
    try {
      const body = await request.json();

      console.log("POST /api/roles - Request body:", JSON.stringify(body, null, 2));

      // Validate request body
      const validatedData = createRoleSchema.parse(body);

      console.log("POST /api/roles - Validated data:", JSON.stringify(validatedData, null, 2));

      // Call service untuk create
      const roleResult = await RoleService.create(validatedData);

      return NextResponse.json({
        success: true,
        data: roleResult,
        message: "Role created successfully",
      }, { status: 201 });
    } catch (error) {
      console.error("POST /api/roles error:", error);

      if (error instanceof z.ZodError) {
        console.error("Zod validation errors:", JSON.stringify(error.errors, null, 2));
        return NextResponse.json(
          { success: false, error: "Validation failed", details: error.errors },
          { status: 400 }
        );
      }

      return NextResponse.json(
        { success: false, error: "Internal server error" },
        { status: 500 }
      );
    }
  },
  { module: "roles", action: "create" }
);
