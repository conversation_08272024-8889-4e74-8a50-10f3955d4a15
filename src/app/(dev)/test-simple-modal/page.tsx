"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ClassLevelForm } from "@/components/forms/class-level-form";

export default function TestSimpleModalPage() {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleOpenModal = () => {
    console.log('Opening simple modal...');
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    console.log('Closing simple modal...');
    setIsModalOpen(false);
  };

  const handleSubmit = async (data: any) => {
    console.log('Form submitted with data:', data);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    console.log('Form submission completed');
    setIsModalOpen(false);
  };

  return (
    <div className="container mx-auto py-8">
      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>Test Simple Modal (Without Zustand)</CardTitle>
          <CardDescription>
            Test basic modal functionality tanpa global state management.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <h3 className="text-lg font-semibold">Current State:</h3>
            <div className="p-3 bg-gray-100 rounded-md">
              <p><strong>Modal Open:</strong> {isModalOpen ? 'true' : 'false'}</p>
            </div>
          </div>

          <div className="space-y-2">
            <h3 className="text-lg font-semibold">Test Button:</h3>
            <Button onClick={handleOpenModal} variant="outline">
              Open Simple Modal
            </Button>
          </div>

          <div className="space-y-2">
            <h3 className="text-lg font-semibold">Instructions:</h3>
            <div className="p-3 bg-blue-50 rounded-md text-sm">
              <ul className="list-disc list-inside space-y-1">
                <li>Click the button to open the modal</li>
                <li>Fill out the form inside the modal</li>
                <li>Submit the form to see console logs</li>
                <li>Modal should close automatically after submission</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Simple Modal */}
      <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Create Class Level</DialogTitle>
          </DialogHeader>
          <ClassLevelForm
            onSubmit={handleSubmit}
            onCancel={handleCloseModal}
            tenantId={1}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}
