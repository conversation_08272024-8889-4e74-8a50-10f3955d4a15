"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { 
  Calendar, 
  Eye, 
  User, 
  Tag,
  Star,
  ArrowLeft,
  Share2,
  Clock,
  ChevronRight
} from "lucide-react";
import { format } from "date-fns";
import Link from "next/link";
import Image from "next/image";
import { useParams } from "next/navigation";

// Mock data for single blog post (will be replaced with API calls)
const mockBlogPost = {
  id: "1",
  title: "Getting Started with Our Fitness Programs",
  slug: "getting-started-fitness-programs",
  excerpt: "Discover the perfect fitness program for your goals. From beginner-friendly routines to advanced training, we have something for everyone.",
  content: `
    <h2>Welcome to Your Fitness Journey</h2>
    <p>Starting a fitness journey can be both exciting and overwhelming. With so many options available, it's important to find the right program that matches your goals, fitness level, and lifestyle.</p>
    
    <h3>Choosing the Right Program</h3>
    <p>Our fitness center offers a variety of programs designed to meet different needs:</p>
    <ul>
      <li><strong>Beginner Programs:</strong> Perfect for those new to fitness or returning after a break</li>
      <li><strong>Strength Training:</strong> Focus on building muscle and increasing overall strength</li>
      <li><strong>Cardio Programs:</strong> Improve cardiovascular health and endurance</li>
      <li><strong>Flexibility & Mobility:</strong> Enhance range of motion and prevent injuries</li>
    </ul>
    
    <h3>Getting Started</h3>
    <p>Before beginning any fitness program, we recommend:</p>
    <ol>
      <li>Consulting with our fitness professionals</li>
      <li>Setting realistic and achievable goals</li>
      <li>Starting slowly and gradually increasing intensity</li>
      <li>Listening to your body and allowing for rest days</li>
    </ol>
    
    <h3>Support Along the Way</h3>
    <p>Remember, you're not alone in this journey. Our team of certified trainers is here to guide you every step of the way. We offer:</p>
    <ul>
      <li>Personal training sessions</li>
      <li>Group fitness classes</li>
      <li>Nutrition counseling</li>
      <li>Progress tracking and assessments</li>
    </ul>
    
    <p>Ready to get started? Contact us today to schedule your initial consultation and take the first step towards a healthier, stronger you!</p>
  `,
  featured_image: "https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=1200&h=600&fit=crop",
  category: {
    id: "1",
    name: "Fitness",
    slug: "fitness",
    color: "#3B82F6"
  },
  tags: ["fitness", "beginner", "workout", "health", "training"],
  status: "published" as const,
  is_featured: true,
  published_at: new Date("2024-01-15"),
  author: {
    id: "1",
    name: "Sarah Johnson",
    email: "<EMAIL>"
  },
  view_count: 1250,
  seo_title: "Getting Started with Our Fitness Programs - Complete Guide",
  seo_description: "Discover the perfect fitness program for your goals. From beginner-friendly routines to advanced training, we have something for everyone.",
  tenantId: 1,
  created_at: new Date("2024-01-15"),
  updated_at: new Date("2024-01-15")
};

const relatedPosts = [
  {
    id: "2",
    title: "Nutrition Tips for Better Performance",
    slug: "nutrition-tips-better-performance",
    excerpt: "Learn how proper nutrition can enhance your workout performance.",
    featured_image: "https://images.unsplash.com/photo-1490645935967-10de6ba17061?w=400&h=200&fit=crop",
    category: { name: "Nutrition", color: "#10B981" },
    published_at: new Date("2024-01-10")
  },
  {
    id: "3",
    title: "New Equipment Available",
    slug: "new-equipment-cardio-machines",
    excerpt: "Check out our latest cardio equipment with cutting-edge technology.",
    featured_image: "https://images.unsplash.com/photo-1534438327276-14e5300c3a48?w=400&h=200&fit=crop",
    category: { name: "Equipment", color: "#F59E0B" },
    published_at: new Date("2024-01-08")
  }
];

export default function BlogPostPage() {
  const params = useParams();
  const slug = params.slug as string;
  const [readingTime, setReadingTime] = useState(0);

  // Calculate reading time
  useEffect(() => {
    const wordsPerMinute = 200;
    const words = mockBlogPost.content.replace(/<[^>]*>/g, '').trim().split(/\s+/).length;
    const minutes = Math.ceil(words / wordsPerMinute);
    setReadingTime(minutes);
  }, []);

  // Handle share functionality
  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: mockBlogPost.title,
          text: mockBlogPost.excerpt,
          url: window.location.href,
        });
      } catch (error) {
        // Fallback to copying URL
        navigator.clipboard.writeText(window.location.href);
      }
    } else {
      // Fallback to copying URL
      navigator.clipboard.writeText(window.location.href);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Breadcrumb */}
      <div className="bg-white border-b">
        <div className="container mx-auto px-4 py-4">
          <nav className="flex items-center space-x-2 text-sm text-gray-600">
            <Link href="/blog" className="hover:text-blue-600">
              Blog
            </Link>
            <ChevronRight className="h-4 w-4" />
            {mockBlogPost.category && (
              <>
                <span className="hover:text-blue-600">
                  {mockBlogPost.category.name}
                </span>
                <ChevronRight className="h-4 w-4" />
              </>
            )}
            <span className="text-gray-900 truncate">{mockBlogPost.title}</span>
          </nav>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Back Button */}
          <div className="mb-6">
            <Link href="/blog">
              <Button variant="ghost" className="pl-0">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Blog
              </Button>
            </Link>
          </div>

          {/* Article Header */}
          <article className="bg-white rounded-lg shadow-sm overflow-hidden">
            {/* Featured Image */}
            {mockBlogPost.featured_image && (
              <div className="relative h-64 md:h-96 w-full">
                <Image
                  src={mockBlogPost.featured_image}
                  alt={mockBlogPost.title}
                  fill
                  className="object-cover"
                  priority
                />
                {mockBlogPost.is_featured && (
                  <div className="absolute top-4 left-4">
                    <Badge className="bg-yellow-500 text-white">
                      <Star className="h-3 w-3 mr-1 fill-current" />
                      Featured
                    </Badge>
                  </div>
                )}
              </div>
            )}

            {/* Article Content */}
            <div className="p-6 md:p-8">
              {/* Meta Information */}
              <div className="flex flex-wrap items-center gap-4 mb-6 text-sm text-gray-600">
                {mockBlogPost.category && (
                  <Badge 
                    variant="outline"
                    style={{ 
                      backgroundColor: `${mockBlogPost.category.color}20`,
                      borderColor: mockBlogPost.category.color,
                      color: mockBlogPost.category.color
                    }}
                  >
                    {mockBlogPost.category.name}
                  </Badge>
                )}
                <span className="flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  {format(mockBlogPost.published_at, 'MMMM dd, yyyy')}
                </span>
                <span className="flex items-center gap-1">
                  <User className="h-4 w-4" />
                  {mockBlogPost.author.name}
                </span>
                <span className="flex items-center gap-1">
                  <Eye className="h-4 w-4" />
                  {mockBlogPost.view_count} views
                </span>
                <span className="flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  {readingTime} min read
                </span>
              </div>

              {/* Title */}
              <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
                {mockBlogPost.title}
              </h1>

              {/* Excerpt */}
              {mockBlogPost.excerpt && (
                <p className="text-xl text-gray-600 mb-6 leading-relaxed">
                  {mockBlogPost.excerpt}
                </p>
              )}

              {/* Share Button */}
              <div className="flex items-center gap-4 mb-8">
                <Button onClick={handleShare} variant="outline" size="sm">
                  <Share2 className="h-4 w-4 mr-2" />
                  Share
                </Button>
              </div>

              <Separator className="mb-8" />

              {/* Article Content */}
              <div 
                className="prose prose-lg max-w-none"
                dangerouslySetInnerHTML={{ __html: mockBlogPost.content }}
              />

              {/* Tags */}
              {mockBlogPost.tags.length > 0 && (
                <div className="mt-8 pt-8 border-t">
                  <h3 className="text-lg font-semibold mb-4">Tags</h3>
                  <div className="flex flex-wrap gap-2">
                    {mockBlogPost.tags.map((tag) => (
                      <Badge key={tag} variant="secondary">
                        <Tag className="h-3 w-3 mr-1" />
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Author Info */}
              <div className="mt-8 pt-8 border-t">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                    <User className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">{mockBlogPost.author.name}</h3>
                    <p className="text-gray-600">Fitness Expert & Trainer</p>
                  </div>
                </div>
              </div>
            </div>
          </article>

          {/* Related Posts */}
          {relatedPosts.length > 0 && (
            <section className="mt-12">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Related Posts</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {relatedPosts.map((post) => (
                  <Card key={post.id} className="overflow-hidden hover:shadow-lg transition-shadow">
                    {post.featured_image && (
                      <div className="relative h-48 w-full">
                        <Image
                          src={post.featured_image}
                          alt={post.title}
                          fill
                          className="object-cover"
                        />
                      </div>
                    )}
                    <CardHeader>
                      <div className="flex items-center gap-2 mb-2">
                        <Badge 
                          variant="outline"
                          style={{ 
                            backgroundColor: `${post.category.color}20`,
                            borderColor: post.category.color,
                            color: post.category.color
                          }}
                        >
                          {post.category.name}
                        </Badge>
                        <span className="text-sm text-gray-500 flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          {format(post.published_at, 'MMM dd, yyyy')}
                        </span>
                      </div>
                      <CardTitle className="line-clamp-2 hover:text-blue-600 transition-colors">
                        <Link href={`/blog/${post.slug}`}>
                          {post.title}
                        </Link>
                      </CardTitle>
                      <CardDescription className="line-clamp-2">
                        {post.excerpt}
                      </CardDescription>
                    </CardHeader>
                  </Card>
                ))}
              </div>
            </section>
          )}
        </div>
      </div>
    </div>
  );
}
