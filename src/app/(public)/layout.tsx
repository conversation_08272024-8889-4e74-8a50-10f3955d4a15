import { ReactNode } from "react";
import { Metadata } from "next";

interface PublicLayoutProps {
  children: ReactNode;
}

export const metadata: Metadata = {
  title: {
    template: "%s | Your Fitness Center",
    default: "Your Fitness Center - Blog & News",
  },
  description: "Stay updated with the latest fitness tips, nutrition advice, and gym announcements",
  keywords: ["fitness", "gym", "health", "nutrition", "workout", "training"],
  authors: [{ name: "Your Fitness Center" }],
  openGraph: {
    type: "website",
    locale: "en_US",
    siteName: "Your Fitness Center",
  },
  twitter: {
    card: "summary_large_image",
  },
};

export default function PublicLayout({ children }: PublicLayoutProps) {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Public pages content */}
      <main>{children}</main>
      
      {/* Footer could be added here */}
      <footer className="bg-white border-t mt-auto">
        <div className="container mx-auto px-4 py-6">
          <div className="text-center text-gray-600 text-sm">
            © 2024 Your Fitness Center. All rights reserved.
          </div>
        </div>
      </footer>
    </div>
  );
}
