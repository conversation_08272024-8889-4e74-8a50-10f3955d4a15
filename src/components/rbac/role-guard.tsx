"use client";

import { ReactNode } from "react";
import { useRBAC } from "@/lib/hooks/use-rbac";

interface RoleGuardProps {
  /** Single role yang dip<PERSON><PERSON>an */
  role?: string;
  /** Multiple roles (salah satu harus match) */
  roles?: string[];
  /** Content yang ditampilkan jika user punya role */
  children: ReactNode;
  /** Fallback content jika user tidak punya role */
  fallback?: ReactNode;
  /** Loading state content */
  loading?: ReactNode;
}

/**
 * Role Guard Component
 * 
 * Component untuk conditional rendering berdasarkan user roles.
 * Menggunakan useRBAC hook untuk check role permissions.
 * 
 * Usage:
 * ```tsx
 * <RoleGuard role="admin">
 *   <AdminPanel />
 * </RoleGuard>
 * 
 * <RoleGuard roles={["admin", "moderator"]}>
 *   <ModeratorPanel />
 * </RoleGuard>
 * 
 * <RoleGuard 
 *   role="premium" 
 *   fallback={<UpgradePrompt />}
 * >
 *   <PremiumFeature />
 * </RoleGuard>
 * ```
 */
export function RoleGuard({
  role,
  roles,
  children,
  fallback = null,
  loading = null,
}: RoleGuardProps) {
  const { hasRole, hasAnyRole, isLoading } = useRBAC();

  // Show loading state
  if (isLoading) {
    return <>{loading}</>;
  }

  // Check single role
  if (role) {
    const hasRequiredRole = hasRole(role);
    return hasRequiredRole ? <>{children}</> : <>{fallback}</>;
  }

  // Check multiple roles (any match)
  if (roles && roles.length > 0) {
    const hasAnyRequiredRole = hasAnyRole(roles);
    return hasAnyRequiredRole ? <>{children}</> : <>{fallback}</>;
  }

  // No role specified, show fallback
  return <>{fallback}</>;
}
