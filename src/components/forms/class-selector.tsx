"use client";

import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Search, X, BookOpen, Users, Check } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { useClassSearch } from "@/lib/hooks/queries/use-class-queries";
import { Class } from "@/lib/db/schema";

interface ClassSelectorProps {
  tenantId: number;
  selectedClasses: Array<{
    id: string;
    name: string;
    category?: string;
    included_at: string;
  }>;
  onSelectionChange: (classes: Array<{
    id: string;
    name: string;
    category?: string;
    included_at: string;
  }>) => void;
  maxSelections?: number;
  placeholder?: string;
}

export function ClassSelector({
  tenantId,
  selectedClasses,
  onSelectionChange,
  maxSelections = 50,
  placeholder = "Search and select classes..."
}: ClassSelectorProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [isOpen, setIsOpen] = useState(false);



  // Fetch classes untuk tenant ini - gunakan useClassSearch untuk semua case
  // Kalau searchQuery kosong, tetap akan return semua classes untuk tenant
  const { data: searchResult, isLoading, error } = useClassSearch(
    tenantId && tenantId > 0 ? tenantId : 1, // Fallback ke tenant 1 jika invalid
    undefined, // categoryId
    undefined, // subcategoryId
    searchQuery || undefined, // searchTerm - kalau kosong jadi undefined
    50 // limit - ambil lebih banyak untuk selection
  );

  // Extract classes array dari search result
  const classes = searchResult?.classes || [];

  // Show error jika ada (optional - bisa dihapus di production)
  useEffect(() => {
    if (error) {
      console.error("❌ ClassSelector Error:", error);
    }
  }, [error]);



  // Filter classes yang belum dipilih
  const availableClasses = classes.filter((cls: Class) =>
    !selectedClasses.some(selected => selected.id === cls.id)
  );

  // Handle class selection
  const handleSelectClass = (classItem: Class) => {
    if (selectedClasses.length >= maxSelections) {
      return; // Sudah mencapai limit
    }

    const newClass = {
      id: classItem.id,
      name: classItem.name,
      category: classItem.categoryId || "Uncategorized",
      included_at: new Date().toISOString(),
    };

    onSelectionChange([...selectedClasses, newClass]);
    setSearchQuery(""); // Clear search setelah select
  };

  // Handle remove class
  const handleRemoveClass = (classId: string) => {
    onSelectionChange(selectedClasses.filter(cls => cls.id !== classId));
  };

  // Handle select all classes
  const handleSelectAll = () => {
    const remainingSlots = maxSelections - selectedClasses.length;
    const classesToAdd = availableClasses.slice(0, remainingSlots).map((cls: Class) => ({
      id: cls.id,
      name: cls.name,
      category: cls.categoryId || "Uncategorized",
      included_at: new Date().toISOString(),
    }));

    onSelectionChange([...selectedClasses, ...classesToAdd]);
  };

  return (
    <div className="space-y-4 relative">
      {/* Header dengan info */}
      <div className="flex items-center justify-between">
        <Label className="flex items-center gap-2">
          <BookOpen className="h-4 w-4" />
          Included Classes
          {selectedClasses.length > 0 && (
            <Badge variant="secondary" className="text-xs">
              {selectedClasses.length} selected
            </Badge>
          )}
        </Label>
        
        {availableClasses.length > 0 && selectedClasses.length < maxSelections && (
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={handleSelectAll}
            className="text-xs"
          >
            <Users className="h-3 w-3 mr-1" />
            Select All ({Math.min(availableClasses.length, maxSelections - selectedClasses.length)})
          </Button>
        )}
      </div>

      {/* Search input */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        <Input
          type="text"
          placeholder={placeholder}
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          onFocus={() => setIsOpen(true)}
          className="pl-10"
        />

        {/* Available classes dropdown */}
        {isOpen && (searchQuery || availableClasses.length > 0) && (
          <Card
            className="border shadow-lg absolute top-full left-0 right-0 z-20 mt-1"
            onClick={(e) => e.stopPropagation()}
          >
            <CardContent className="p-2 max-h-60 overflow-y-auto">
              {isLoading ? (
                <div className="flex items-center justify-center py-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                  <span className="ml-2 text-sm text-gray-600">Loading classes...</span>
                </div>
              ) : availableClasses.length === 0 ? (
                <div className="text-center py-4 text-sm text-gray-500">
                  {searchQuery ? "No classes found matching your search" : "No available classes"}
                </div>
              ) : (
                <div className="space-y-1">
                  {availableClasses.map((cls: Class) => (
                    <button
                      key={cls.id}
                      type="button"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleSelectClass(cls);
                      }}
                      disabled={selectedClasses.length >= maxSelections}
                      className="w-full text-left p-2 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium text-sm">{cls.name}</div>
                          {cls.categoryId && (
                            <div className="text-xs text-gray-500">Category: {cls.categoryId}</div>
                          )}
                        </div>
                        <BookOpen className="h-4 w-4 text-gray-400" />
                      </div>
                    </button>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>

      {/* Selected classes */}
      {selectedClasses.length > 0 && (
        <div className="space-y-2">
          <Label className="text-sm text-gray-600">Selected Classes:</Label>
          <div className="flex flex-wrap gap-2">
            {selectedClasses.map((cls) => (
              <motion.div
                key={cls.id}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                className="flex items-center gap-1"
              >
                <Badge variant="default" className="flex items-center gap-1">
                  <Check className="h-3 w-3" />
                  {cls.name}
                  {cls.category && (
                    <span className="text-xs opacity-75">({cls.category})</span>
                  )}
                  <button
                    type="button"
                    onClick={() => handleRemoveClass(cls.id)}
                    className="ml-1 hover:bg-red-500 hover:text-white rounded-full p-0.5 transition-colors"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              </motion.div>
            ))}
          </div>
        </div>
      )}



      {/* Click outside to close */}
      {isOpen && (
        <div
          className="fixed inset-0 z-10"
          onClick={() => setIsOpen(false)}
        />
      )}

      {/* Info text */}
      <p className="text-xs text-gray-500">
        Select classes that will be included in this package. 
        {maxSelections && ` Maximum ${maxSelections} classes allowed.`}
        {selectedClasses.length >= maxSelections && (
          <span className="text-orange-600 font-medium"> Limit reached!</span>
        )}
      </p>
    </div>
  );
}
