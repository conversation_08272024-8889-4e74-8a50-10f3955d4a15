import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { ClassImageService } from "@/lib/services/class-image.service";

/**
 * API Routes untuk Class Images
 * 
 * <PERSON><PERSON><PERSON><PERSON> pattern yang sama dengan classes, class-categories, dan class-subcategories.
 * Semua validation menggunakan Zod, error handling yang proper.
 */

// Validation schemas
const searchParamsSchema = z.object({
  classId: z.string().min(1, "Class ID is required"),
});

const createClassImageSchema = z.object({
  class_id: z.string().min(1, "Class ID is required"),
  image_url: z.string().min(1, "Image URL is required").refine((url) => {
    // Accept both full URLs and local paths
    return url.startsWith('http') || url.startsWith('https') || url.startsWith('/');
  }, "Invalid image URL format"),
});

/**
 * GET /api/class-images
 * Get images for a specific class
 * 
 * Query parameters:
 * - classId: required untuk get images by class
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Parse and validate query parameters
    const rawParams = {
      classId: searchParams.get("classId"),
    };

    // Remove null values
    const cleanParams = Object.fromEntries(
      Object.entries(rawParams).filter(([_, value]) => value !== null)
    );

    const validatedParams = searchParamsSchema.parse(cleanParams);

    // Call service
    const images = await ClassImageService.getByClassId(validatedParams.classId);

    return NextResponse.json({
      success: true,
      data: {
        images,
        total: images.length,
      },
    });
  } catch (error) {
    console.error("GET /api/class-images error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: "Invalid parameters", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/class-images
 * Create a new class image
 * 
 * Body harus contain:
 * - class_id: ID class yang akan dikasih image
 * - image_url: URL image yang valid
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate request body
    const validatedData = createClassImageSchema.parse(body);

    // Call service untuk create
    const imageResult = await ClassImageService.create(validatedData);

    return NextResponse.json({
      success: true,
      data: imageResult,
      message: "Class image created successfully",
    }, { status: 201 });
  } catch (error) {
    console.error("POST /api/class-images error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      if (error.message.includes("Class with ID") && error.message.includes("not found")) {
        return NextResponse.json(
          { success: false, error: "Class not found" },
          { status: 404 }
        );
      }
      
      if (error.message.includes("Invalid image URL")) {
        return NextResponse.json(
          { success: false, error: error.message },
          { status: 400 }
        );
      }
    }

    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}
