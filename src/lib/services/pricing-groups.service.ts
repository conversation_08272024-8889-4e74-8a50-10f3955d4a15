import { sql, eq, and, SQL } from "drizzle-orm";
import { pricing_groups } from "@/lib/db/schema";
import { BaseService, CreateEntityData, UpdateEntityData } from "@/lib/core/base-service";
import { PricingGroup, NewPricingGroup } from "@/lib/db/schema";

// Extend base interfaces for pricing group-specific data
export interface CreatePricingGroupData extends CreateEntityData {
  tenantId: number;
  name: string;
  description?: string;
  discountPercentage?: number;
  isActive?: boolean;
  isDefault?: boolean;
  sortOrder?: number;
}

export interface UpdatePricingGroupData extends UpdateEntityData {
  tenantId?: number;
  name?: string;
  description?: string;
  discountPercentage?: number;
  isActive?: boolean;
  isDefault?: boolean;
  sortOrder?: number;
}

export interface PricingGroupStats {
  total: number;
  active: number;
  inactive: number;
  default: number;
  withDiscount: number;
  averageDiscount: number;
  byTenant: Record<number, {
    total: number;
    active: number;
    default: number;
  }>;
}

// Pricing Groups Service extending BaseService
export class PricingGroupService extends BaseService<PricingGroup, CreatePricingGroupData, UpdatePricingGroupData> {
  constructor() {
    super(pricing_groups, 'Pricing Group');
  }

  // Implement abstract method for filter conditions
  protected buildFilterConditions(filters: Record<string, any>): SQL[] {
    const conditions: SQL[] = [];

    if (filters.isActive !== undefined) {
      conditions.push(sql`${pricing_groups.isActive} = ${filters.isActive}`);
    }

    if (filters.isDefault !== undefined) {
      conditions.push(sql`${pricing_groups.isDefault} = ${filters.isDefault}`);
    }

    if (filters.hasDiscount !== undefined) {
      if (filters.hasDiscount) {
        conditions.push(sql`${pricing_groups.discountPercentage} > 0`);
      } else {
        conditions.push(sql`${pricing_groups.discountPercentage} = 0 OR ${pricing_groups.discountPercentage} IS NULL`);
      }
    }

    if (filters.search) {
      conditions.push(
        sql`(LOWER(${pricing_groups.name}) LIKE LOWER(${'%' + filters.search + '%'}) OR 
            LOWER(${pricing_groups.description}) LIKE LOWER(${'%' + filters.search + '%'}))`
      );
    }

    return conditions;
  }

  // Override create to handle sort order and default group logic
  async create(data: CreatePricingGroupData): Promise<PricingGroup> {
    // Get the next sort order if not provided
    let sortOrder = data.sortOrder;
    if (sortOrder === undefined) {
      const [maxOrder] = await this.db
        .select({ max: sql<number>`max(${pricing_groups.sortOrder})` })
        .from(pricing_groups)
        .where(eq(pricing_groups.tenantId, data.tenantId));
      
      sortOrder = (maxOrder.max || 0) + 1;
    }

    // If this is set as default, unset other defaults for this tenant
    if (data.isDefault) {
      await this.db
        .update(pricing_groups)
        .set({ isDefault: false, updatedAt: new Date() })
        .where(
          and(
            eq(pricing_groups.tenantId, data.tenantId),
            eq(pricing_groups.isDefault, true)
          )
        );
    }

    return super.create({
      ...data,
      sortOrder,
    });
  }

  // Override update to handle default group logic
  async update(id: string, data: UpdatePricingGroupData): Promise<PricingGroup> {
    // If this is being set as default, unset other defaults for this tenant
    if (data.isDefault) {
      const current = await this.getById(id);
      if (current?.tenantId) {
        await this.db
          .update(pricing_groups)
          .set({ isDefault: false, updatedAt: new Date() })
          .where(
            and(
              eq(pricing_groups.tenantId, current.tenantId),
              eq(pricing_groups.isDefault, true)
            )
          );
      }
    }

    return super.update(id, data);
  }

  // Get active pricing groups by tenant
  async getActiveByTenantId(tenantId: number): Promise<PricingGroup[]> {
    return this.getAll({
      tenantId,
      filters: { isActive: true },
      orderField: 'sortOrder',
      orderBy: 'asc',
    });
  }

  // Get default pricing group by tenant
  async getDefaultByTenantId(tenantId: number): Promise<PricingGroup | null> {
    const [result] = await this.db
      .select()
      .from(pricing_groups)
      .where(
        and(
          eq(pricing_groups.tenantId, tenantId),
          eq(pricing_groups.isDefault, true)
        )
      );
    
    return result || null;
  }

  // Reorder pricing groups
  async reorder(tenantId: number, reorderData: { id: string; sortOrder: number }[]): Promise<PricingGroup[]> {
    return await this.db.transaction(async (tx) => {
      const updatedPricingGroups = [];
      
      for (const item of reorderData) {
        const [updated] = await tx
          .update(pricing_groups)
          .set({
            sortOrder: item.sortOrder,
            updatedAt: new Date(),
          })
          .where(
            and(
              eq(pricing_groups.id, item.id),
              eq(pricing_groups.tenantId, tenantId)
            )
          )
          .returning();
        
        if (updated) {
          updatedPricingGroups.push(updated);
        }
      }
      
      return updatedPricingGroups;
    });
  }

  // Toggle active status
  async toggleActive(id: string): Promise<PricingGroup> {
    const current = await this.getById(id);
    if (!current) {
      throw new Error("Pricing group not found");
    }

    return this.update(id, { isActive: !current.isActive });
  }

  // Toggle default status
  async toggleDefault(id: string): Promise<PricingGroup> {
    const current = await this.getById(id);
    if (!current) {
      throw new Error("Pricing group not found");
    }

    return this.update(id, { isDefault: !current.isDefault });
  }

  // Duplicate pricing group
  async duplicate(id: string, newName?: string): Promise<PricingGroup> {
    const original = await this.getById(id);
    if (!original) {
      throw new Error("Pricing group not found");
    }

    const duplicatedData: CreatePricingGroupData = {
      tenantId: original.tenantId!,
      name: newName || `${original.name} (Copy)`,
      description: original.description,
      discountPercentage: original.discountPercentage,
      isActive: false, // Start as inactive
      isDefault: false, // Start as non-default
    };

    return this.create(duplicatedData);
  }

  // Get enhanced statistics
  async getStats(tenantId?: number): Promise<PricingGroupStats> {
    const baseStats = await super.getStats(tenantId);
    
    let whereClause = sql`1=1`;
    if (tenantId) {
      whereClause = sql`${pricing_groups.tenantId} = ${tenantId}`;
    }

    const [activeCount] = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(pricing_groups)
      .where(sql`${whereClause} AND ${pricing_groups.isActive} = true`);

    const [inactiveCount] = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(pricing_groups)
      .where(sql`${whereClause} AND ${pricing_groups.isActive} = false`);

    const [defaultCount] = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(pricing_groups)
      .where(sql`${whereClause} AND ${pricing_groups.isDefault} = true`);

    const [withDiscountCount] = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(pricing_groups)
      .where(sql`${whereClause} AND ${pricing_groups.discountPercentage} > 0`);

    const [avgDiscount] = await this.db
      .select({ avg: sql<number>`avg(${pricing_groups.discountPercentage})` })
      .from(pricing_groups)
      .where(sql`${whereClause} AND ${pricing_groups.discountPercentage} > 0`);

    // Get stats by tenant if not filtered by specific tenant
    const byTenant: Record<number, { total: number; active: number; default: number }> = {};
    if (!tenantId) {
      const tenantStats = await this.db
        .select({
          tenantId: pricing_groups.tenantId,
          total: sql<number>`count(*)`,
          active: sql<number>`sum(case when ${pricing_groups.isActive} then 1 else 0 end)`,
          default: sql<number>`sum(case when ${pricing_groups.isDefault} then 1 else 0 end)`,
        })
        .from(pricing_groups)
        .groupBy(pricing_groups.tenantId);

      tenantStats.forEach(stat => {
        if (stat.tenantId) {
          byTenant[stat.tenantId] = {
            total: stat.total,
            active: stat.active,
            default: stat.default,
          };
        }
      });
    }

    return {
      total: baseStats.total,
      active: activeCount.count,
      inactive: inactiveCount.count,
      default: defaultCount.count,
      withDiscount: withDiscountCount.count,
      averageDiscount: Math.round(avgDiscount.avg || 0),
      byTenant,
    };
  }

  // Bulk operations specific to pricing groups
  async bulkToggleActive(ids: string[], isActive: boolean): Promise<PricingGroup[]> {
    return this.bulkUpdate(ids, { isActive });
  }

  async bulkToggleDefault(ids: string[], isDefault: boolean): Promise<PricingGroup[]> {
    // If setting multiple as default, we need to handle this carefully
    if (isDefault && ids.length > 1) {
      throw new Error("Only one pricing group can be set as default per tenant");
    }
    
    return this.bulkUpdate(ids, { isDefault });
  }

  // Search pricing groups
  async search(query: string, tenantId?: number): Promise<PricingGroup[]> {
    return super.search(query, ['name', 'description'], { tenantId });
  }

  // Validate pricing group data
  protected validateCreateData(data: CreatePricingGroupData): void {
    if (!data.name?.trim()) {
      throw new Error("Pricing group name is required");
    }
    if (data.discountPercentage !== undefined && (data.discountPercentage < 0 || data.discountPercentage > 100)) {
      throw new Error("Discount percentage must be between 0 and 100");
    }
  }

  protected validateUpdateData(data: UpdatePricingGroupData): void {
    if (data.name !== undefined && !data.name?.trim()) {
      throw new Error("Pricing group name cannot be empty");
    }
    if (data.discountPercentage !== undefined && (data.discountPercentage < 0 || data.discountPercentage > 100)) {
      throw new Error("Discount percentage must be between 0 and 100");
    }
  }
}
