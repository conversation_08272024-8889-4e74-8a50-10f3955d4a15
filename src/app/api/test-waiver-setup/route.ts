import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { waiver_forms } from "@/lib/db/schema";
import { createId } from "@paralleldrive/cuid2";

export async function GET() {
  try {
    console.log('🧪 Setting up test waiver forms...');
    
    // Sample waiver form content
    const liabilityWaiverContent = JSON.stringify({
      title: "Liability Waiver",
      description: "Please complete this liability waiver before participating in activities.",
      fields: [
        {
          id: "fullName",
          type: "text",
          label: "Full Name",
          required: true,
          placeholder: "Enter your full legal name"
        },
        {
          id: "dateOfBirth",
          type: "text",
          label: "Date of Birth",
          required: true,
          placeholder: "MM/DD/YYYY"
        },
        {
          id: "emergencyContact",
          type: "text",
          label: "Emergency Contact Name",
          required: true,
          placeholder: "Enter emergency contact name"
        },
        {
          id: "emergencyPhone",
          type: "text",
          label: "Emergency Contact Phone",
          required: true,
          placeholder: "Enter emergency contact phone number"
        },
        {
          id: "medicalConditions",
          type: "textarea",
          label: "Medical Conditions",
          required: false,
          placeholder: "List any medical conditions we should be aware of"
        },
        {
          id: "acknowledgment",
          type: "checkbox",
          label: "I acknowledge that I understand the risks involved and agree to participate at my own risk",
          required: true
        },
        {
          id: "signature",
          type: "signature",
          label: "Digital Signature",
          required: true,
          placeholder: "Type your full name as digital signature"
        }
      ],
      terms: "By signing this waiver, I acknowledge that I understand the inherent risks involved in the activities and agree to participate at my own risk. I release the organization from any liability for injuries or damages that may occur during participation."
    });

    const photoReleaseContent = JSON.stringify({
      title: "Photo Release Form",
      description: "Permission to use photos and videos for marketing purposes.",
      fields: [
        {
          id: "participantName",
          type: "text",
          label: "Participant Name",
          required: true,
          placeholder: "Enter participant name"
        },
        {
          id: "parentGuardian",
          type: "text",
          label: "Parent/Guardian Name (if under 18)",
          required: false,
          placeholder: "Enter parent/guardian name if applicable"
        },
        {
          id: "photoPermission",
          type: "checkbox",
          label: "I grant permission to use my photos/videos for marketing and promotional purposes",
          required: true
        },
        {
          id: "socialMediaPermission",
          type: "checkbox",
          label: "I grant permission to use my photos/videos on social media platforms",
          required: false
        },
        {
          id: "signature",
          type: "signature",
          label: "Digital Signature",
          required: true,
          placeholder: "Type your full name as digital signature"
        }
      ],
      terms: "I grant permission for the organization to use photographs and videos of me for marketing, promotional, and educational purposes across various media platforms."
    });

    // Create waiver forms for tenant 1
    const waiverForms = [
      {
        id: createId(),
        tenantId: 1,
        name: "Liability Waiver",
        description: "Required liability waiver for all participants",
        content: liabilityWaiverContent,
        version: "1.0",
        isActive: true,
        isRequired: true,
        expiryDays: 365,
        sortOrder: 1
      },
      {
        id: createId(),
        tenantId: 1,
        name: "Photo Release",
        description: "Permission to use photos and videos",
        content: photoReleaseContent,
        version: "1.0",
        isActive: true,
        isRequired: false,
        expiryDays: 730,
        sortOrder: 2
      },
      {
        id: createId(),
        tenantId: 2,
        name: "General Waiver",
        description: "General waiver for tenant 2",
        content: liabilityWaiverContent,
        version: "1.0",
        isActive: true,
        isRequired: true,
        expiryDays: 365,
        sortOrder: 1
      }
    ];

    // Insert waiver forms
    await db.insert(waiver_forms).values(waiverForms);

    console.log('✅ Test waiver forms created successfully');

    return NextResponse.json({
      success: true,
      message: "Test waiver forms created successfully",
      waiverForms: waiverForms.map(w => ({
        id: w.id,
        name: w.name,
        tenantId: w.tenantId
      }))
    });
  } catch (error) {
    console.error('🧪 Error setting up test waiver forms:', error);
    return NextResponse.json({ error: 'Failed to setup test waiver forms' }, { status: 500 });
  }
}
