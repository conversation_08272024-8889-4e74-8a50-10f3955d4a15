import { db } from "../db";
import { customer_addresses, customers, type CustomerAddress, type NewCustomerAddress } from "../db/schema";
import { eq, and, desc } from "drizzle-orm";
import { createId } from "@paralleldrive/cuid2";

export class CustomerAddressService {
  /**
   * Get addresses by customer ID
   */
  static async getByCustomerId(customerId: string): Promise<CustomerAddress[]> {
    return await db
      .select()
      .from(customer_addresses)
      .where(eq(customer_addresses.customerId, customerId))
      .orderBy(desc(customer_addresses.createdAt));
  }

  /**
   * Get primary address for customer
   */
  static async getPrimaryAddress(customerId: string): Promise<CustomerAddress | null> {
    const [address] = await db
      .select()
      .from(customer_addresses)
      .where(
        and(
          eq(customer_addresses.customerId, customerId),
          eq(customer_addresses.is_primary, true)
        )
      )
      .limit(1);

    return address || null;
  }

  /**
   * Get address by ID
   */
  static async getById(id: string): Promise<CustomerAddress | null> {
    const [address] = await db
      .select()
      .from(customer_addresses)
      .where(eq(customer_addresses.id, id))
      .limit(1);

    return address || null;
  }

  /**
   * Create customer address
   */
  static async create(data: Omit<NewCustomerAddress, "id" | "createdAt" | "updatedAt">): Promise<CustomerAddress> {
    // If this is set as primary, unset other primary addresses for this customer
    if (data.is_primary && data.customerId) {
      await this.unsetPrimaryAddresses(data.customerId);
    }

    const [address] = await db
      .insert(customer_addresses)
      .values({
        ...data,
        id: createId(),
      })
      .returning();

    return address;
  }

  /**
   * Update customer address
   */
  static async update(
    id: string,
    data: Partial<Omit<NewCustomerAddress, "id" | "customerId" | "createdAt">>
  ): Promise<CustomerAddress | null> {
    // If setting as primary, get the customer ID first and unset other primary addresses
    if (data.is_primary) {
      const existingAddress = await this.getById(id);
      if (existingAddress?.customerId) {
        await this.unsetPrimaryAddresses(existingAddress.customerId);
      }
    }

    const [address] = await db
      .update(customer_addresses)
      .set({
        ...data,
        updatedAt: new Date(),
      })
      .where(eq(customer_addresses.id, id))
      .returning();

    return address || null;
  }

  /**
   * Delete customer address
   */
  static async delete(id: string): Promise<boolean> {
    const result = await db
      .delete(customer_addresses)
      .where(eq(customer_addresses.id, id));

    return result.rowCount > 0;
  }

  /**
   * Unset all primary addresses for a customer
   */
  static async unsetPrimaryAddresses(customerId: string): Promise<void> {
    await db
      .update(customer_addresses)
      .set({ is_primary: false })
      .where(eq(customer_addresses.customerId, customerId));
  }

  /**
   * Set address as primary
   */
  static async setPrimary(id: string): Promise<CustomerAddress | null> {
    const address = await this.getById(id);
    if (!address?.customerId) return null;

    // Unset other primary addresses
    await this.unsetPrimaryAddresses(address.customerId);

    // Set this address as primary
    return await this.update(id, { is_primary: true });
  }

  /**
   * Get customer with addresses
   */
  static async getCustomerWithAddresses(customerId: string) {
    const result = await db
      .select({
        customer: customers,
        address: customer_addresses,
      })
      .from(customers)
      .leftJoin(customer_addresses, eq(customers.id, customer_addresses.customerId))
      .where(eq(customers.id, customerId));

    if (result.length === 0) return null;

    const customer = result[0].customer;
    const addresses = result
      .filter(row => row.address !== null)
      .map(row => row.address!);

    return {
      customer,
      addresses,
    };
  }

  /**
   * Bulk create addresses for customer
   */
  static async bulkCreate(
    customerId: string,
    addressesData: Omit<NewCustomerAddress, "id" | "customerId" | "createdAt" | "updatedAt">[]
  ): Promise<CustomerAddress[]> {
    const addressesWithIds = addressesData.map((data, index) => ({
      ...data,
      id: createId(),
      customerId,
      // Only first address is primary if none specified
      is_primary: data.is_primary ?? (index === 0),
    }));

    // If any address is set as primary, unset existing primary addresses
    const hasPrimary = addressesWithIds.some(addr => addr.is_primary);
    if (hasPrimary) {
      await this.unsetPrimaryAddresses(customerId);
    }

    return await db
      .insert(customer_addresses)
      .values(addressesWithIds)
      .returning();
  }

  /**
   * Delete all addresses for customer
   */
  static async deleteAllForCustomer(customerId: string): Promise<boolean> {
    const result = await db
      .delete(customer_addresses)
      .where(eq(customer_addresses.customerId, customerId));

    return result.rowCount > 0;
  }
}
