"use client";

import { useState, useMemo } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Checkbox } from "@/components/ui/checkbox";
import {
  DollarSign,
  Edit,
  Trash2,
  Plus,
  Search,
  AlertCircle,
  Copy,
  MoreHorizontal,
  Package,
  Users,
  Coins,
  Calendar,
  Globe,
  GripVertical
} from "lucide-react";
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
} from '@dnd-kit/core';
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { PackagePricing } from "@/lib/db/schema";
import {
  usePackagePricingList,
  useDeletePackagePricing,
  useBulkPackagePricingOperation,
  useDuplicatePackagePricing,
  useReorderPackagePricing,
  PackagePricingWithDetails
} from "@/lib/hooks/queries/use-package-pricing-queries";
import { DeleteConfirmationDialog } from "@/components/ui/delete-confirmation-dialog";
import { DeleteSuccessToast } from "@/components/ui/delete-success-toast";

// Sortable Item Component
interface SortablePackagePricingItemProps {
  pricing: PackagePricingWithDetails;
  isSelected: boolean;
  onSelect: (checked: boolean) => void;
  onEdit?: (pricing: PackagePricingWithDetails) => void;
  onDelete: (pricing: PackagePricingWithDetails) => void;
  onDuplicate: (pricing: PackagePricingWithDetails) => void;
  formatPrice: (price: number | null, currency: string | null) => string | null;
  formatCredits: (credits: number | null) => string | null;
}

function SortablePackagePricingItem({
  pricing,
  isSelected,
  onSelect,
  onEdit,
  onDelete,
  onDuplicate,
  formatPrice,
  formatCredits,
}: SortablePackagePricingItemProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: pricing.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      className={`border rounded-lg p-4 transition-all duration-200 ${
        isDragging
          ? 'bg-gray-50 dark:bg-gray-800 shadow-lg scale-105 z-50'
          : 'hover:bg-gray-50 dark:hover:bg-gray-800'
      }`}
    >
      <div className="flex items-center gap-3">
        {/* Drag Handle */}
        <div
          {...attributes}
          {...listeners}
          className="cursor-grab active:cursor-grabbing p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded transition-colors"
          title="Drag to reorder"
        >
          <GripVertical className="h-4 w-4 text-gray-400" />
        </div>

        {/* Checkbox */}
        <Checkbox
          checked={isSelected}
          onCheckedChange={onSelect}
        />

        {/* Content */}
        <div className="flex-1">
          {/* Package and Pricing Group Info */}
          <div className="flex items-center gap-2 mb-1">
            <h3 className="font-medium text-sm">
              {pricing.package?.name || "Unknown Package"}
            </h3>
            <span className="text-gray-400">→</span>
            <Badge variant="outline" className="text-xs">
              {pricing.pricingGroup?.name || "Unknown Group"}
            </Badge>
          </div>

          {/* Pricing Details */}
          <div className="flex items-center gap-4 mb-2">
            {pricing.price !== null && (
              <div className="flex items-center gap-1">
                <DollarSign className="h-3 w-3 text-green-600" />
                <span className="text-sm font-medium text-green-600">
                  {formatPrice(pricing.price, pricing.currency)}
                </span>
              </div>
            )}
            {pricing.credit_amount !== null && (
              <div className="flex items-center gap-1">
                <Coins className="h-3 w-3 text-blue-600" />
                <span className="text-sm font-medium text-blue-600">
                  {formatCredits(pricing.credit_amount)}
                </span>
              </div>
            )}
            {pricing.currency && (
              <div className="flex items-center gap-1">
                <Globe className="h-3 w-3 text-gray-500" />
                <span className="text-sm text-gray-500">
                  {pricing.currency}
                </span>
              </div>
            )}
          </div>

          {/* Metadata */}
          <div className="flex items-center gap-4 text-xs text-gray-500">
            <div className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              <span>Created {new Date(pricing.createdAt).toLocaleDateString()}</span>
            </div>
          </div>
        </div>

        {/* Actions */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            {onEdit && (
              <DropdownMenuItem onClick={() => onEdit(pricing)}>
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </DropdownMenuItem>
            )}
            <DropdownMenuItem onClick={() => onDuplicate(pricing)}>
              <Copy className="h-4 w-4 mr-2" />
              Duplicate
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => onDelete(pricing)}
              className="text-destructive"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
}

interface PackagePricingListProps {
  onEdit?: (packagePricing: PackagePricingWithDetails) => void;
  onAdd?: () => void;
  showActions?: boolean;
  tenantId?: number;
}

export function PackagePricingList({
  onEdit,
  onAdd,
  showActions = true,
  tenantId,
}: PackagePricingListProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [deleteError, setDeleteError] = useState<string | null>(null);
  const [deleteConfirmation, setDeleteConfirmation] = useState<{
    isOpen: boolean;
    packagePricing: PackagePricingWithDetails | null;
  }>({ isOpen: false, packagePricing: null });
  const [showDeleteSuccess, setShowDeleteSuccess] = useState(false);
  const [deletedPricingName, setDeletedPricingName] = useState("");

  const { data: packagePricing = [], isLoading, error } = usePackagePricingList({
    search: searchQuery || undefined,
  }, tenantId || 1);

  // Sort package pricing by sortOrder for consistent ordering
  const sortedPackagePricing = useMemo(() => {
    return [...packagePricing].sort((a, b) => (a.sortOrder || 0) - (b.sortOrder || 0));
  }, [packagePricing]);

  // Drag and drop sensors
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );
  
  const deletePackagePricingMutation = useDeletePackagePricing();
  const bulkOperationMutation = useBulkPackagePricingOperation();
  const duplicateMutation = useDuplicatePackagePricing();
  const reorderPackagePricingMutation = useReorderPackagePricing();

  const handleDeleteClick = (pricing: PackagePricingWithDetails) => {
    setDeleteConfirmation({ isOpen: true, packagePricing: pricing });
  };

  const handleDeleteConfirm = async () => {
    if (!deleteConfirmation.packagePricing) return;

    try {
      await deletePackagePricingMutation.mutateAsync(deleteConfirmation.packagePricing.id);
      const packageName = deleteConfirmation.packagePricing.package?.name || 'Unknown Package';
      const displayName = `${packageName} - ${deleteConfirmation.packagePricing.pricingGroup?.name}`;
      setDeletedPricingName(displayName);
      setShowDeleteSuccess(true);
      setDeleteConfirmation({ isOpen: false, packagePricing: null });
    } catch (error) {
      setDeleteError(error instanceof Error ? error.message : "Failed to delete package pricing");
    }
  };

  const handleDuplicate = async (pricing: PackagePricingWithDetails) => {
    try {
      await duplicateMutation.mutateAsync({ 
        id: pricing.id,
      });
    } catch (error) {
      setDeleteError(error instanceof Error ? error.message : "Failed to duplicate package pricing");
    }
  };

  const handleSelectItem = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedItems([...selectedItems, id]);
    } else {
      setSelectedItems(selectedItems.filter(item => item !== id));
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedItems(packagePricing.map(pricing => pricing.id));
    } else {
      setSelectedItems([]);
    }
  };

  const handleBulkOperation = async (action: 'delete') => {
    if (selectedItems.length === 0) return;

    try {
      await bulkOperationMutation.mutateAsync({ ids: selectedItems, action });
      setSelectedItems([]);
    } catch (error) {
      setDeleteError(error instanceof Error ? error.message : `Failed to ${action} package pricing`);
    }
  };

  // Handle drag end for reordering
  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;

    if (!over || active.id === over.id) {
      return;
    }

    const oldIndex = sortedPackagePricing.findIndex((item) => item.id === active.id);
    const newIndex = sortedPackagePricing.findIndex((item) => item.id === over.id);

    if (oldIndex === -1 || newIndex === -1) {
      return;
    }

    // Create the new order to determine sort orders
    const newOrder = arrayMove(sortedPackagePricing, oldIndex, newIndex);

    // Create the reorder data with new sort orders
    const reorderData = newOrder.map((item, index) => ({
      id: item.id,
      sortOrder: index,
    }));

    // The optimistic update is now handled in the React Query hook
    reorderPackagePricingMutation.mutate({
      tenantId: tenantId || 1,
      items: reorderData,
    });
  };

  const formatPrice = (price: number | null, currency: string | null) => {
    if (price === null) return null;
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency || 'USD',
    }).format(price);
  };

  const formatCredits = (credits: number | null) => {
    if (credits === null) return null;
    return `${credits} credits`;
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Error loading package pricing: {error.message}
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              Package Pricing
            </CardTitle>
            <CardDescription>
              Manage pricing for packages across different pricing groups
            </CardDescription>
          </div>
          {showActions && onAdd && (
            <Button onClick={onAdd}>
              <Plus className="h-4 w-4 mr-2" />
              Add Pricing
            </Button>
          )}
        </div>

        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search packages or pricing groups..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Bulk Actions */}
        {selectedItems.length > 0 && (
          <div className="flex items-center gap-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <span className="text-sm text-blue-700 dark:text-blue-300">
              {selectedItems.length} selected
            </span>
            <Button
              variant="destructive"
              size="sm"
              onClick={() => handleBulkOperation('delete')}
            >
              <Trash2 className="h-4 w-4 mr-1" />
              Delete
            </Button>
          </div>
        )}
      </CardHeader>

      <CardContent>
        {deleteError && (
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{deleteError}</AlertDescription>
          </Alert>
        )}

        {/* Package Pricing List */}
        {sortedPackagePricing.length === 0 ? (
          <div className="text-center py-8">
            <DollarSign className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              {searchQuery ? "No pricing found" : "No package pricing yet"}
            </h3>
            <p className="text-gray-500 dark:text-gray-400 mb-4">
              {searchQuery
                ? "Try adjusting your search terms."
                : "Get started by creating your first package pricing."
              }
            </p>
            {showActions && onAdd && !searchQuery && (
              <Button onClick={onAdd}>
                <Plus className="h-4 w-4 mr-2" />
                Add Package Pricing
              </Button>
            )}
          </div>
        ) : (
          <div className="space-y-2">
            {/* Select All */}
            <div className="flex items-center gap-2 p-2 border-b">
              <Checkbox
                checked={selectedItems.length === sortedPackagePricing.length}
                onCheckedChange={handleSelectAll}
              />
              <span className="text-sm text-gray-600">Select All</span>
              {sortedPackagePricing.length > 1 && (
                <span className="text-xs text-gray-500 ml-auto">
                  Drag items to reorder
                </span>
              )}
            </div>

            {/* Drag and Drop Context */}
            <DndContext
              sensors={sensors}
              collisionDetection={closestCenter}
              onDragEnd={handleDragEnd}
            >
              <SortableContext
                items={sortedPackagePricing.map(item => item.id)}
                strategy={verticalListSortingStrategy}
              >
                <div className="space-y-2">
                  {sortedPackagePricing.map((pricing) => (
                    <SortablePackagePricingItem
                      key={pricing.id}
                      pricing={pricing}
                      isSelected={selectedItems.includes(pricing.id)}
                      onSelect={(checked) => handleSelectItem(pricing.id, checked)}
                      onEdit={onEdit}
                      onDelete={handleDeleteClick}
                      onDuplicate={handleDuplicate}
                      formatPrice={formatPrice}
                      formatCredits={formatCredits}
                    />
                  ))}
                </div>
              </SortableContext>
            </DndContext>
          </div>
        )}

      </CardContent>

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationDialog
        isOpen={deleteConfirmation.isOpen}
        onClose={() => setDeleteConfirmation({ isOpen: false, packagePricing: null })}
        onConfirm={handleDeleteConfirm}
        title="Delete Package Pricing"
        description={`Are you sure you want to delete the pricing for "${deleteConfirmation.packagePricing?.package?.name || 'Unknown Package'}" in "${deleteConfirmation.packagePricing?.pricingGroup?.name}"? This action cannot be undone.`}
        isLoading={deletePackagePricingMutation.isPending}
      />

      {/* Delete Success Toast */}
      <DeleteSuccessToast
        isOpen={showDeleteSuccess}
        onClose={() => setShowDeleteSuccess(false)}
        entityName="Package Pricing"
        deletedName={deletedPricingName}
      />
    </Card>
  );
}
