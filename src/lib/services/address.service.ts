import { db } from "../db";
import { addresses, tenants, type Address, type NewAddress } from "../db/schema";
import { eq, and, desc, like } from "drizzle-orm";
import { createId } from "@paralleldrive/cuid2";

export class AddressService {
  /**
   * Get addresses by tenant ID
   */
  static async getByTenantId(tenantId: number): Promise<Address[]> {
    return await db
      .select()
      .from(addresses)
      .where(eq(addresses.tenantId, tenantId))
      .orderBy(desc(addresses.createdAt));
  }

  /**
   * Get address by ID
   */
  static async getById(id: string): Promise<Address | null> {
    const [address] = await db
      .select()
      .from(addresses)
      .where(eq(addresses.id, id))
      .limit(1);

    return address || null;
  }

  /**
   * Create address
   */
  static async create(data: Omit<NewAddress, "id" | "createdAt">): Promise<Address> {
    const [address] = await db
      .insert(addresses)
      .values({
        ...data,
        id: createId(),
      })
      .returning();

    return address;
  }

  /**
   * Update address
   */
  static async update(
    id: string,
    data: Partial<Omit<NewAddress, "id" | "tenantId" | "createdAt">>
  ): Promise<Address | null> {
    const [address] = await db
      .update(addresses)
      .set(data)
      .where(eq(addresses.id, id))
      .returning();

    return address || null;
  }

  /**
   * Delete address
   */
  static async delete(id: string): Promise<boolean> {
    const result = await db
      .delete(addresses)
      .where(eq(addresses.id, id));

    return result.rowCount > 0;
  }

  /**
   * Get address with tenant info
   */
  static async getWithTenant(id: string) {
    const result = await db
      .select({
        address: addresses,
        tenant: tenants,
      })
      .from(addresses)
      .leftJoin(tenants, eq(addresses.tenantId, tenants.id))
      .where(eq(addresses.id, id))
      .limit(1);

    return result[0] || null;
  }

  /**
   * Search addresses by city or state
   */
  static async searchByLocation(tenantId: number, query: string): Promise<Address[]> {
    return await db
      .select()
      .from(addresses)
      .where(
        and(
          eq(addresses.tenantId, tenantId),
          // Search in city, state, or country
          // Note: This is a simplified search. In production, you might want to use full-text search
        )
      )
      .orderBy(desc(addresses.createdAt));
  }

  /**
   * Get addresses by country
   */
  static async getByCountry(tenantId: number, country: string): Promise<Address[]> {
    return await db
      .select()
      .from(addresses)
      .where(
        and(
          eq(addresses.tenantId, tenantId),
          eq(addresses.country, country)
        )
      )
      .orderBy(desc(addresses.createdAt));
  }

  /**
   * Get addresses by state
   */
  static async getByState(tenantId: number, state: string): Promise<Address[]> {
    return await db
      .select()
      .from(addresses)
      .where(
        and(
          eq(addresses.tenantId, tenantId),
          eq(addresses.state, state)
        )
      )
      .orderBy(desc(addresses.createdAt));
  }

  /**
   * Get addresses by postal code
   */
  static async getByPostalCode(tenantId: number, postalCode: string): Promise<Address[]> {
    return await db
      .select()
      .from(addresses)
      .where(
        and(
          eq(addresses.tenantId, tenantId),
          eq(addresses.postalCode, postalCode)
        )
      )
      .orderBy(desc(addresses.createdAt));
  }

  /**
   * Mark address as accepted
   */
  static async markAsAccepted(id: string): Promise<Address | null> {
    const [address] = await db
      .update(addresses)
      .set({
        acceptedAt: new Date(),
      })
      .where(eq(addresses.id, id))
      .returning();

    return address || null;
  }

  /**
   * Get accepted addresses for tenant
   */
  static async getAcceptedAddresses(tenantId: number): Promise<Address[]> {
    return await db
      .select()
      .from(addresses)
      .where(
        and(
          eq(addresses.tenantId, tenantId),
          // Check if acceptedAt is not null
        )
      )
      .orderBy(desc(addresses.acceptedAt));
  }

  /**
   * Get pending addresses for tenant
   */
  static async getPendingAddresses(tenantId: number): Promise<Address[]> {
    return await db
      .select()
      .from(addresses)
      .where(
        and(
          eq(addresses.tenantId, tenantId),
          // Check if acceptedAt is null
        )
      )
      .orderBy(desc(addresses.createdAt));
  }

  /**
   * Bulk create addresses
   */
  static async bulkCreate(addressesData: Omit<NewAddress, "id" | "createdAt">[]): Promise<Address[]> {
    const addressesWithIds = addressesData.map(data => ({
      ...data,
      id: createId(),
    }));

    return await db
      .insert(addresses)
      .values(addressesWithIds)
      .returning();
  }

  /**
   * Get address statistics for tenant
   */
  static async getStats(tenantId: number) {
    const [totalAddresses] = await db
      .select({ count: addresses.id })
      .from(addresses)
      .where(eq(addresses.tenantId, tenantId));

    const [acceptedAddresses] = await db
      .select({ count: addresses.id })
      .from(addresses)
      .where(
        and(
          eq(addresses.tenantId, tenantId),
          // acceptedAt is not null
        )
      );

    return {
      totalAddresses: totalAddresses?.count || 0,
      acceptedAddresses: acceptedAddresses?.count || 0,
      pendingAddresses: (totalAddresses?.count || 0) - (acceptedAddresses?.count || 0),
    };
  }

  /**
   * Format address as string
   */
  static formatAddress(address: Address): string {
    const parts = [
      address.addressLine1,
      address.addressLine2,
      address.city,
      address.state,
      address.postalCode,
      address.country,
    ].filter(Boolean);

    return parts.join(", ");
  }

  /**
   * Validate postal code format (basic validation)
   */
  static validatePostalCode(postalCode: string, country: string): boolean {
    // Basic validation - in production, you'd want more sophisticated validation
    const patterns: Record<string, RegExp> = {
      US: /^\d{5}(-\d{4})?$/,
      CA: /^[A-Z]\d[A-Z] \d[A-Z]\d$/,
      UK: /^[A-Z]{1,2}\d[A-Z\d]? \d[A-Z]{2}$/,
      // Add more patterns as needed
    };

    const pattern = patterns[country.toUpperCase()];
    return pattern ? pattern.test(postalCode) : true; // Default to true if no pattern
  }
}
