import { NextRequest, NextResponse } from "next/server";
import { headers } from "next/headers";
import { stripe } from "@/lib/stripe/config";
import { addCredits } from "@/lib/stripe/utils";
import { getCreditPackageByPriceId } from "@/lib/stripe/config";
import { sendCreditPurchaseEmail } from "@/lib/email/send";
import { db } from "@/lib/db";
import { users } from "@/lib/db/schema";
import { eq } from "drizzle-orm";

export async function POST(request: NextRequest) {
  const body = await request.text();
  const headersList = headers();
  const signature = headersList.get("stripe-signature");

  if (!signature) {
    return NextResponse.json(
      { error: "Missing stripe signature" },
      { status: 400 }
    );
  }

  let event;

  try {
    event = stripe.webhooks.constructEvent(
      body,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET!
    );
  } catch (error) {
    console.error("Webhook signature verification failed:", error);
    return NextResponse.json(
      { error: "Invalid signature" },
      { status: 400 }
    );
  }

  try {
    switch (event.type) {
      case "checkout.session.completed": {
        const session = event.data.object;
        
        if (session.metadata?.type === "credit_purchase") {
          const userId = session.metadata.userId;
          const organizationId = session.metadata.organizationId || undefined;
          const credits = parseInt(session.metadata.credits);
          
          if (!userId || !credits) {
            console.error("Missing required metadata in checkout session");
            break;
          }

          // Get payment intent to find the price
          const paymentIntent = await stripe.paymentIntents.retrieve(
            session.payment_intent as string
          );

          // Add credits to user/organization
          await addCredits({
            userId,
            organizationId: organizationId || undefined,
            credits,
            description: `Credit purchase - ${credits} credits`,
            stripePaymentIntentId: paymentIntent.id,
            type: "purchase",
          });

          // Send confirmation email
          try {
            const [user] = await db
              .select()
              .from(users)
              .where(eq(users.id, userId))
              .limit(1);

            if (user) {
              await sendCreditPurchaseEmail(
                user.email,
                user.name || "User",
                credits,
                session.amount_total || 0
              );
            }
          } catch (emailError) {
            console.error("Failed to send purchase confirmation email:", emailError);
            // Don't fail the webhook for email errors
          }

          console.log(`Successfully added ${credits} credits to user ${userId}`);
        }
        break;
      }

      case "payment_intent.succeeded": {
        const paymentIntent = event.data.object;
        console.log(`Payment succeeded: ${paymentIntent.id}`);
        break;
      }

      case "payment_intent.payment_failed": {
        const paymentIntent = event.data.object;
        console.log(`Payment failed: ${paymentIntent.id}`);
        break;
      }

      case "customer.subscription.created":
      case "customer.subscription.updated":
      case "customer.subscription.deleted": {
        // Handle subscription events if you add subscription billing later
        console.log(`Subscription event: ${event.type}`);
        break;
      }

      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return NextResponse.json({ received: true });
  } catch (error) {
    console.error("Webhook processing error:", error);
    return NextResponse.json(
      { error: "Webhook processing failed" },
      { status: 500 }
    );
  }
}
