import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { ClassSubcategoryService } from "@/lib/services/class-subcategory.service";

// Validation schemas
const searchParamsSchema = z.object({
  tenantId: z.string().transform((val) => parseInt(val, 10)),
  categoryId: z.string().optional(),
  search: z.string().optional(),
  limit: z.string().transform((val) => parseInt(val, 10)).optional(),
  offset: z.string().transform((val) => parseInt(val, 10)).optional(),
});

const createClassSubcategorySchema = z.object({
  tenantId: z.number().int().positive("Tenant ID must be positive"),
  categoryId: z.string().min(1, "Category ID is required"),
  name: z.string().min(1, "Name is required").max(255, "Name too long"),
});

/**
 * GET /api/class-subcategories
 * Search class subcategories with filtering and pagination
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Parse and validate query parameters
    const rawParams = {
      tenantId: searchParams.get("tenantId"),
      categoryId: searchParams.get("categoryId"),
      search: searchParams.get("search"),
      limit: searchParams.get("limit"),
      offset: searchParams.get("offset"),
    };

    // Remove null values
    const cleanParams = Object.fromEntries(
      Object.entries(rawParams).filter(([_, value]) => value !== null)
    );

    const validatedParams = searchParamsSchema.parse(cleanParams);

    // Call service
    const result = await ClassSubcategoryService.searchSubcategories(
      validatedParams.tenantId,
      validatedParams.categoryId,
      validatedParams.search,
      validatedParams.limit || 20,
      validatedParams.offset || 0
    );

    return NextResponse.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error("GET /api/class-subcategories error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: "Invalid parameters", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/class-subcategories
 * Create a new class subcategory
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate request body
    const validatedData = createClassSubcategorySchema.parse(body);

    // Call service untuk create
    const subcategory = await ClassSubcategoryService.create(validatedData);

    return NextResponse.json({
      success: true,
      data: subcategory,
      message: "Class subcategory created successfully",
    }, { status: 201 });
  } catch (error) {
    console.error("POST /api/class-subcategories error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * Helper function untuk get all subcategories by tenant
 * Digunakan kalau tidak ada search parameters
 */
async function getAllSubcategories(tenantId: string) {
  try {
    const subcategories = await ClassSubcategoryService.getByTenantId(parseInt(tenantId, 10));
    
    return NextResponse.json({
      success: true,
      data: {
        subcategories,
        total: subcategories.length,
        hasMore: false,
      },
    });
  } catch (error) {
    console.error("Error getting all subcategories:", error);
    throw error;
  }
}
