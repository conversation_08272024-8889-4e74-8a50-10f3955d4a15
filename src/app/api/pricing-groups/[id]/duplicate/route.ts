import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { z } from "zod";
import { PricingGroupService } from "@/lib/services/pricing-groups.service";

const duplicateSchema = z.object({
  name: z.string().min(1).max(255).optional(),
});

const pricingGroupService = new PricingGroupService();

// POST /api/pricing-groups/[id]/duplicate - Duplicate pricing group
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id: pricingGroupId } = await params;
    if (!pricingGroupId) {
      return NextResponse.json(
        { error: "Pricing group ID is required" },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { name } = duplicateSchema.parse(body);

    const duplicatedPricingGroup = await pricingGroupService.duplicate(pricingGroupId, name);

    return NextResponse.json(
      {
        message: "Pricing group duplicated successfully",
        data: duplicatedPricingGroup
      },
      { status: 201 }
    );
  } catch (error) {
    console.error("Error duplicating pricing group:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid data", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to duplicate pricing group" },
      { status: 500 }
    );
  }
}
