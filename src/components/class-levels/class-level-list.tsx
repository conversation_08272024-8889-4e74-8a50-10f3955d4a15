"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Checkbox } from "@/components/ui/checkbox";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  GraduationCap,
  Edit,
  Trash2,
  Plus,
  Search,
  Building,
  AlertCircle,
  GripVertical,
  Eye,
  EyeOff,
  MoreHorizontal
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { ClassLevel } from "@/lib/db/schema";
import { 
  useClassLevels, 
  useDeleteClassLevel, 
  useToggleClassLevelActive,
  useReorderClassLevels,
  useBulkClassLevelOperation
} from "@/lib/hooks/queries/use-class-level-queries";
import { DeleteConfirmationDialog } from "@/components/ui/delete-confirmation-dialog";
import { DeleteSuccessToast } from "@/components/ui/delete-success-toast";

interface ClassLevelListProps {
  onEdit?: (classLevel: ClassLevel) => void;
  onAdd?: () => void;
  showActions?: boolean;
  tenantId?: number;
}

export function ClassLevelList({
  onEdit,
  onAdd,
  showActions = true,
  tenantId,
}: ClassLevelListProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [deleteError, setDeleteError] = useState<string | null>(null);
  const [deleteConfirmation, setDeleteConfirmation] = useState<{
    isOpen: boolean;
    classLevel: ClassLevel | null;
  }>({ isOpen: false, classLevel: null });
  const [showDeleteSuccess, setShowDeleteSuccess] = useState(false);
  const [deletedClassLevelName, setDeletedClassLevelName] = useState("");

  const { data: classLevels = [], isLoading, error } = useClassLevels();
  const deleteClassLevelMutation = useDeleteClassLevel();
  const toggleActiveMutation = useToggleClassLevelActive();
  const reorderMutation = useReorderClassLevels();
  const bulkOperationMutation = useBulkClassLevelOperation();

  // Filter class levels based on tenant and search query
  const filteredClassLevels = classLevels.filter((classLevel) => {
    // Filter by tenant if specified
    if (tenantId && classLevel.tenantId !== tenantId) {
      return false;
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      const name = classLevel.name?.toLowerCase() || "";
      const description = classLevel.description?.toLowerCase() || "";
      
      return name.includes(query) || description.includes(query);
    }

    return true;
  });

  const handleDeleteClick = (classLevel: ClassLevel) => {
    setDeleteConfirmation({ isOpen: true, classLevel });
  };

  const handleDeleteConfirm = async () => {
    if (!deleteConfirmation.classLevel) return;

    const classLevelName = deleteConfirmation.classLevel.name || "Unknown class level";

    try {
      setDeleteError(null);
      await deleteClassLevelMutation.mutateAsync(deleteConfirmation.classLevel.id);

      // Close confirmation dialog
      setDeleteConfirmation({ isOpen: false, classLevel: null });

      // Show success toast
      setDeletedClassLevelName(classLevelName);
      setShowDeleteSuccess(true);

    } catch (error) {
      setDeleteError(error instanceof Error ? error.message : "Failed to delete class level");
    }
  };

  const handleDeleteCancel = () => {
    setDeleteConfirmation({ isOpen: false, classLevel: null });
  };

  const handleToggleActive = async (classLevel: ClassLevel) => {
    try {
      await toggleActiveMutation.mutateAsync(classLevel.id);
    } catch (error) {
      setDeleteError(error instanceof Error ? error.message : "Failed to toggle class level status");
    }
  };

  const handleSelectItem = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedItems([...selectedItems, id]);
    } else {
      setSelectedItems(selectedItems.filter(item => item !== id));
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedItems(filteredClassLevels.map(cl => cl.id));
    } else {
      setSelectedItems([]);
    }
  };

  const handleBulkOperation = async (action: 'activate' | 'deactivate' | 'delete') => {
    if (selectedItems.length === 0) return;

    try {
      await bulkOperationMutation.mutateAsync({ ids: selectedItems, action });
      setSelectedItems([]);
    } catch (error) {
      setDeleteError(error instanceof Error ? error.message : `Failed to ${action} class levels`);
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="py-8">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Failed to load class levels. Please try again.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <GraduationCap className="h-5 w-5" />
              Class Levels
            </CardTitle>
            <CardDescription>
              Manage class levels and their ordering
            </CardDescription>
          </div>
          {showActions && onAdd && (
            <Button onClick={onAdd}>
              <Plus className="h-4 w-4 mr-2" />
              Add Class Level
            </Button>
          )}
        </div>

        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search class levels..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Bulk Actions */}
        {selectedItems.length > 0 && (
          <div className="flex items-center gap-2 p-3 bg-blue-50 rounded-lg">
            <span className="text-sm text-blue-700">
              {selectedItems.length} item(s) selected
            </span>
            <div className="flex gap-2 ml-auto">
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleBulkOperation('activate')}
                disabled={bulkOperationMutation.isPending}
              >
                <Eye className="h-4 w-4 mr-1" />
                Activate
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleBulkOperation('deactivate')}
                disabled={bulkOperationMutation.isPending}
              >
                <EyeOff className="h-4 w-4 mr-1" />
                Deactivate
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleBulkOperation('delete')}
                disabled={bulkOperationMutation.isPending}
                className="text-red-600 hover:text-red-700"
              >
                <Trash2 className="h-4 w-4 mr-1" />
                Delete
              </Button>
            </div>
          </div>
        )}

        {/* Delete Error */}
        {deleteError && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{deleteError}</AlertDescription>
          </Alert>
        )}
      </CardHeader>

      <CardContent>
        {/* Class Level List */}
        {filteredClassLevels.length === 0 ? (
          <div className="text-center py-8">
            <GraduationCap className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              {searchQuery ? "No class levels found" : "No class levels yet"}
            </h3>
            <p className="text-gray-500 dark:text-gray-400 mb-4">
              {searchQuery 
                ? "Try adjusting your search terms."
                : "Get started by adding your first class level."
              }
            </p>
            {showActions && onAdd && !searchQuery && (
              <Button onClick={onAdd}>
                <Plus className="h-4 w-4 mr-2" />
                Add Class Level
              </Button>
            )}
          </div>
        ) : (
          <div className="space-y-2">
            {/* Select All */}
            <div className="flex items-center gap-2 p-2 border-b">
              <Checkbox
                checked={selectedItems.length === filteredClassLevels.length}
                onCheckedChange={handleSelectAll}
              />
              <span className="text-sm text-gray-600">Select All</span>
            </div>

            {filteredClassLevels.map((classLevel, index) => (
              <div
                key={classLevel.id}
                className="border rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
              >
                <div className="flex items-center gap-3">
                  {/* Checkbox */}
                  <Checkbox
                    checked={selectedItems.includes(classLevel.id)}
                    onCheckedChange={(checked) => handleSelectItem(classLevel.id, checked as boolean)}
                  />

                  {/* Drag Handle */}
                  <GripVertical className="h-4 w-4 text-gray-400 cursor-grab" />

                  {/* Content */}
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <GraduationCap className="h-4 w-4 text-gray-500" />
                      <h4 className="font-medium text-gray-900 dark:text-gray-100">
                        {classLevel.name}
                      </h4>
                      <Badge variant={classLevel.isActive ? "default" : "secondary"}>
                        {classLevel.isActive ? "Active" : "Inactive"}
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        Order: {classLevel.sortOrder}
                      </Badge>
                    </div>
                    
                    {classLevel.description && (
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                        {classLevel.description}
                      </p>
                    )}

                    <div className="flex items-center gap-2">
                      <Badge variant="secondary" className="text-xs">
                        Tenant ID: {classLevel.tenantId}
                      </Badge>
                    </div>
                  </div>

                  {/* Active Toggle */}
                  <div className="flex items-center gap-2">
                    <Switch
                      checked={classLevel.isActive}
                      onCheckedChange={() => handleToggleActive(classLevel)}
                      disabled={toggleActiveMutation.isPending}
                    />
                  </div>

                  {/* Actions */}
                  {showActions && (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        {onEdit && (
                          <DropdownMenuItem onClick={() => onEdit(classLevel)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuItem onClick={() => handleToggleActive(classLevel)}>
                          {classLevel.isActive ? (
                            <>
                              <EyeOff className="h-4 w-4 mr-2" />
                              Deactivate
                            </>
                          ) : (
                            <>
                              <Eye className="h-4 w-4 mr-2" />
                              Activate
                            </>
                          )}
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem 
                          onClick={() => handleDeleteClick(classLevel)}
                          className="text-red-600"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Summary */}
        {filteredClassLevels.length > 0 && (
          <div className="mt-4 pt-4 border-t text-sm text-gray-500 dark:text-gray-400">
            Showing {filteredClassLevels.length} of {classLevels.length} class levels
          </div>
        )}
      </CardContent>

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationDialog
        isOpen={deleteConfirmation.isOpen}
        onClose={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        title="Delete Class Level"
        description="Are you sure you want to delete this class level? This action cannot be undone."
        itemName={deleteConfirmation.classLevel?.name || ""}
        isLoading={deleteClassLevelMutation.isPending}
      />

      {/* Delete Success Toast */}
      <DeleteSuccessToast
        isVisible={showDeleteSuccess}
        onClose={() => setShowDeleteSuccess(false)}
        itemName={deletedClassLevelName}
      />
    </Card>
  );
}
