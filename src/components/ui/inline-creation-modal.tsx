"use client";

import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, AlertCircle } from "lucide-react";
import { useInlineCreationStore, getEntityDisplayName } from "@/lib/stores/inline-creation-store";
import { useCreateClassLevel } from "@/lib/hooks/queries/use-class-level-queries";
import { useCreateClassCategory } from "@/lib/hooks/queries/use-class-category-queries";
import { useCreateClassSubcategory } from "@/lib/hooks/queries/use-class-subcategory-queries";
import { useCreateLocation } from "@/lib/hooks/queries/use-location-queries";
import { useCreateFacility } from "@/lib/hooks/queries/use-facility-queries";
import { useCreateClass } from '@/lib/hooks/queries/use-class-queries';
import { useCreatePackage } from '@/lib/hooks/queries/use-package-queries';
import { useCreatePackageCategory } from '@/lib/hooks/queries/use-package-category-queries';
import { useCreatePackageLocation } from '@/lib/hooks/queries/use-package-location-queries';
import { useCreatePackagePricing } from '@/lib/hooks/queries/use-package-pricing-queries';
import { useCreatePackagePurchaseOptions } from '@/lib/hooks/queries/use-package-purchase-options-queries';

/**
 * Global Inline Creation Modal Component
 * 
 * This component provides a centralized modal for creating entities inline
 * without navigating away from the current form. It uses Zustand store for
 * state management and can be configured for different entity types.
 * 
 * Usage:
 * 1. Add this component to your layout or main app component
 * 2. Use the useInlineCreationStore hook to open modals from any form
 * 3. Configure the modal with entity type, form component, and hooks
 */
export function InlineCreationModal() {
  const {
    isOpen,
    config,
    isCreating,
    error,
    closeModal,
    createEntity,
    setError,
  } = useInlineCreationStore();

  // Initialize all possible mutation hooks
  const createClassMutation = useCreateClass();
  const createClassLevelMutation = useCreateClassLevel();
  const createClassCategoryMutation = useCreateClassCategory();
  const createClassSubcategoryMutation = useCreateClassSubcategory();
  const createLocationMutation = useCreateLocation();
  const createFacilityMutation = useCreateFacility();
  const createPackageMutation = useCreatePackage();
  const createPackageCategoryMutation = useCreatePackageCategory();
  const createPackageLocationMutation = useCreatePackageLocation();
  const createPackagePricingMutation = useCreatePackagePricing();
  const createPackagePurchaseOptionsMutation = useCreatePackagePurchaseOptions();
  const createPackageCategoryRelMutation = useCreatePackage();
  const createPackageCustomerSegmentsMutation = useCreatePackage();
  // const createPackageScheduleAvailabilityMutation = useCreatePackage
  // const createPackageScheduleAvailabilityMutation = useCreatePackageScheduleAvailability();
  // const createPackageIncludedClassMutation = useCreatePackageIncludedClass();


  console.log('InlineCreationModal: Render called with state:', { isOpen, config, isCreating, error });

  // Don't render if no config
  if (!config) {
    console.log('InlineCreationModal: No config, returning null');
    return null;
  }

  const { title, formComponent: FormComponent, additionalProps = {} } = config;

  // Get the appropriate mutation based on entity type
  const getMutation = () => {
    switch (config.entityType) {
      case 'class':
        return createClassMutation;
      case 'class-level':
        return createClassLevelMutation;
      case 'class-category':
        return createClassCategoryMutation;
      case 'class-subcategory':
        return createClassSubcategoryMutation;
      case 'location':
        return createLocationMutation;
      case 'facility':
        return createFacilityMutation;
      default:
        throw new Error(`No mutation found for entity type: ${config.entityType}`);
    }
  };

  // Handle form submission
  const handleSubmit = async (data: any) => {
    try {
      const mutation = getMutation();
      await createEntity(data, mutation);
    } catch (error) {
      // Error is already handled in the store
      console.error('Failed to create entity:', error);
    }
  };

  // Handle form cancellation
  const handleCancel = () => {
    setError(null);
    closeModal();
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && handleCancel()}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {title}
            {isCreating && (
              <Loader2 className="h-4 w-4 animate-spin" />
            )}
          </DialogTitle>
        </DialogHeader>

        {/* Error Display */}
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Form Component */}
        <div className="relative">
          {/* Loading Overlay */}
          {isCreating && (
            <div className="absolute inset-0 bg-background/50 backdrop-blur-sm z-10 flex items-center justify-center">
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                <Loader2 className="h-4 w-4 animate-spin" />
                Creating {getEntityDisplayName(config.entityType).toLowerCase()}...
              </div>
            </div>
          )}

          {/* Render the form component */}
          <FormComponent
            tenantId={config.tenantId}
            onSubmit={handleSubmit}
            onCancel={handleCancel}
            className="border-0 shadow-none"
            disabled={isCreating}
            {...additionalProps}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
}

/**
 * Hook for easy inline creation modal management
 * 
 * This hook provides a simplified interface for opening inline creation modals
 * with proper configuration and type safety.
 */
export function useInlineCreation() {
  const { openModal } = useInlineCreationStore();

  return {
    openModal,
  };
}
