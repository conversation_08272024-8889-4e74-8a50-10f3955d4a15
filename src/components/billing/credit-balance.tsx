"use client";

import { useEffect, useState } from "react";
import { CreditCard, TrendingUp, TrendingDown, RefreshCw } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { formatCredits } from "@/lib/stripe/config";
import { ComponentLoading } from "@/components/ui/loading";

interface CreditBalanceProps {
  userId: string;
  organizationId?: string;
}

interface CreditStats {
  balance: number;
  monthlyUsage: number;
  totalPurchased: number;
  totalUsed: number;
}

export function CreditBalance({ userId, organizationId }: CreditBalanceProps) {
  const [stats, setStats] = useState<CreditStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  const fetchCreditStats = async () => {
    try {
      const response = await fetch(`/api/credits/stats?${organizationId ? `organizationId=${organizationId}` : ''}`);
      if (response.ok) {
        const data = await response.json();
        setStats(data);
      }
    } catch (error) {
      console.error("Failed to fetch credit stats:", error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchCreditStats();
  };

  useEffect(() => {
    fetchCreditStats();
  }, [userId, organizationId]);

  if (loading) {
    return <ComponentLoading text="Loading credit balance..." />;
  }

  if (!stats) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-muted-foreground">
            Failed to load credit balance
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {/* Current Balance */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Current Balance</CardTitle>
          <CreditCard className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-primary">
            {formatCredits(stats.balance)}
          </div>
          <p className="text-xs text-muted-foreground">
            Available credits
          </p>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleRefresh}
            disabled={refreshing}
            className="mt-2 h-6 px-2"
          >
            <RefreshCw className={`h-3 w-3 ${refreshing ? 'animate-spin' : ''}`} />
          </Button>
        </CardContent>
      </Card>

      {/* Monthly Usage */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">This Month</CardTitle>
          <TrendingDown className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            {formatCredits(stats.monthlyUsage)}
          </div>
          <p className="text-xs text-muted-foreground">
            Credits used this month
          </p>
        </CardContent>
      </Card>

      {/* Total Purchased */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Purchased</CardTitle>
          <TrendingUp className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-600">
            {formatCredits(stats.totalPurchased)}
          </div>
          <p className="text-xs text-muted-foreground">
            All-time purchases
          </p>
        </CardContent>
      </Card>

      {/* Total Used */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Used</CardTitle>
          <TrendingDown className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-orange-600">
            {formatCredits(stats.totalUsed)}
          </div>
          <p className="text-xs text-muted-foreground">
            All-time usage
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
