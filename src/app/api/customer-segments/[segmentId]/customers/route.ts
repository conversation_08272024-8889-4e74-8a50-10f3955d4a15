import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { z } from "zod";
import { customerSegmentsService } from "@/lib/services/customer-segments.service";

// Validation schema for query parameters
const querySchema = z.object({
  tenantId: z.string().transform((val) => parseInt(val)),
});

/**
 * GET /api/customer-segments/[segmentId]/customers
 * Get customers in a specific segment
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ segmentId: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { segmentId } = await params;
    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    // Validate query parameters
    const { tenantId } = querySchema.parse(queryParams);

    if (!tenantId || tenantId <= 0) {
      return NextResponse.json(
        { error: "Valid tenant ID is required" },
        { status: 400 }
      );
    }

    console.log(`👥 [GET /api/customer-segments/${segmentId}/customers] Fetching customers for segment ${segmentId}, tenant ${tenantId}`);

    const customers = await customerSegmentsService.getCustomersInSegment(segmentId, tenantId);

    return NextResponse.json({
      success: true,
      data: customers,
      meta: {
        total: customers.length,
        segmentId,
        tenantId,
      }
    });
  } catch (error) {
    console.error(`👥 [GET /api/customer-segments/${params}/customers] Error:`, error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid query parameters", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to fetch customers in segment" },
      { status: 500 }
    );
  }
}
