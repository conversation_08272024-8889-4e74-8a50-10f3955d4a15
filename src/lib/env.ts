import { z } from "zod";

// Schema untuk validasi environment variables
const envSchema = z.object({
  // Database
  DATABASE_URL: z.string().min(1, "DATABASE_URL is required"),
  
  // Authentication
  NEXTAUTH_SECRET: z.string().min(1, "NEXTAUTH_SECRET is required"),
  NEXTAUTH_URL: z.string().url("NEXTAUTH_URL must be a valid URL").optional(),
  
  // OAuth Providers
  GOOGLE_CLIENT_ID: z.string().optional(),
  GOOGLE_CLIENT_SECRET: z.string().optional(),
  
  // Email Service
  RESEND_API_KEY: z.string().optional(),
  BREVO_API_KEY: z.string().optional(),
  FROM_EMAIL: z.string().email("FROM_EMAIL must be a valid email").optional(),
  
  // Stripe
  STRIPE_SECRET_KEY: z.string().optional(),
  STRIPE_PUBLISHABLE_KEY: z.string().optional(),
  STRIPE_WEBHOOK_SECRET: z.string().optional(),
  
  // Cloudflare
  CLOUDFLARE_ACCOUNT_ID: z.string().optional(),
  CLOUDFLARE_API_TOKEN: z.string().optional(),
  CLOUDFLARE_KV_NAMESPACE_ID: z.string().optional(),
  CLOUDFLARE_D1_DATABASE_ID: z.string().optional(),
  
  // Turnstile
  TURNSTILE_SECRET_KEY: z.string().optional(),
  TURNSTILE_SITE_KEY: z.string().optional(),
  
  // App Configuration
  NODE_ENV: z.enum(["development", "production", "test"]).default("development"),
  APP_URL: z.string().url("APP_URL must be a valid URL").optional(),
  
  // WebAuthn
  WEBAUTHN_RP_ID: z.string().optional(),
  WEBAUTHN_RP_NAME: z.string().optional(),
  WEBAUTHN_ORIGIN: z.string().url("WEBAUTHN_ORIGIN must be a valid URL").optional(),
});

// Type untuk environment variables yang sudah divalidasi
export type Env = z.infer<typeof envSchema>;

// Fungsi untuk memvalidasi dan mendapatkan environment variables
function getEnv(): Env {
  try {
    return envSchema.parse(process.env);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const missingVars = error.errors.map(err => `${err.path.join('.')}: ${err.message}`);
      throw new Error(`Environment validation failed:\n${missingVars.join('\n')}`);
    }
    throw error;
  }
}

// Export environment variables yang sudah divalidasi
export const env = getEnv();

// Helper functions untuk development
export const isDevelopment = env.NODE_ENV === "development";
export const isProduction = env.NODE_ENV === "production";
export const isTest = env.NODE_ENV === "test";

// Database URL dengan fallback untuk development
export const getDatabaseUrl = () => {
  if (isDevelopment && !env.DATABASE_URL) {
    return "file:./dev.db"; // SQLite untuk development
  }
  return env.DATABASE_URL;
};

// App URL dengan fallback
export const getAppUrl = () => {
  if (env.APP_URL) return env.APP_URL;
  if (isDevelopment) return "http://localhost:3000";
  return "https://your-app.com"; // Ganti dengan domain production Anda
};

// WebAuthn configuration
export const getWebAuthnConfig = () => ({
  rpID: env.WEBAUTHN_RP_ID || (isDevelopment ? "localhost" : "your-app.com"),
  rpName: env.WEBAUTHN_RP_NAME || "Your SaaS App",
  origin: env.WEBAUTHN_ORIGIN || getAppUrl(),
});
