import { db } from "@/lib/db";
import { roles, permissions, user_roles, role_permissions, user_location_access, rbac_activity_logs, users, type Role, type NewRole, type UserRole, type NewUserRole, type Permission, type RolePermission, type NewRolePermission, type UserLocationAccess, type NewUserLocationAccess, type PermissionData } from "@/lib/db/schema";
import { eq, and, ilike, desc, sql, inArray } from "drizzle-orm";
import { createId } from "@paralleldrive/cuid2";

/**
 * Role Service
 * 
 * Service untuk manage RBAC system mengikuti pola yang sama dengan ClassService.
 * Ini kayak "manager" yang handle semua operasi CRUD untuk roles, permissions, dan assignments.
 * 
 * Pattern ini udah terbukti berhasil tanpa bug di class categories dan subcategories.
 */

// Interface untuk search results
export interface RoleSearchResult {
  roles: Role[];
  total: number;
  hasMore: boolean;
}

export interface UserRoleSearchResult {
  userRoles: (UserRole & { user: { name: string | null; email: string } })[];
  total: number;
  hasMore: boolean;
}




/**
 * RoleService - Service untuk manage roles dan permissions
 * 
 * Menggunakan static methods pattern yang simple dan proven.
 * Tidak pakai BaseService yang kompleks, langsung ke database dengan Drizzle ORM.
 */
export class RoleService {
  /**
   * Search roles dengan filtering dan pagination
   * 
   * Ini method utama untuk ambil data roles dengan berbagai filter:
   * - tenantId: optional untuk tenant isolation (null untuk system roles)
   * - search: optional, search berdasarkan nama role
   * - isSystemRole: optional, filter system vs custom roles
   * - limit & offset: untuk pagination
   */
  static async searchRoles(
    tenantId?: number | null,
    search?: string,
    isSystemRole?: boolean,
    limit = 20,
    offset = 0
  ): Promise<RoleSearchResult> {
    try {
      // Build where conditions
      let whereConditions: any = eq(roles.is_active, true);

      // Filter by tenant if provided (null untuk system roles)
      if (tenantId !== undefined) {
        if (tenantId === null) {
          whereConditions = and(whereConditions, sql`${roles.tenantId} IS NULL`);
        } else {
          whereConditions = and(whereConditions, eq(roles.tenantId, tenantId));
        }
      }

      // Filter by system role if provided
      if (isSystemRole !== undefined) {
        whereConditions = and(
          whereConditions,
          eq(roles.is_system_role, isSystemRole)
        ) as any;
      }

      // Kalau ada search term, tambahin filter nama
      if (search && search.trim()) {
        whereConditions = and(
          whereConditions,
          ilike(roles.name, `%${search.trim()}%`)
        ) as any;
      }

      // Query dengan pagination - ambil +1 untuk cek hasMore
      const roleResults = await db
        .selectDistinct()
        .from(roles)
        .where(whereConditions)
        .orderBy(roles.hierarchy_level, desc(roles.createdAt))
        .limit(limit + 1) // +1 untuk cek hasMore
        .offset(offset);

      // Cek apakah ada data lebih
      const hasMore = roleResults.length > limit;
      if (hasMore) {
        roleResults.pop(); // Hapus item extra
      }

      // Count total untuk metadata
      const totalResult = await db
        .select({ count: sql<number>`count(*)` })
        .from(roles)
        .where(whereConditions);

      const total = Number(totalResult[0]?.count || 0);

      return {
        roles: roleResults,
        total,
        hasMore,
      };
    } catch (error) {
      console.error("Error searching roles:", error);
      throw error;
    }
  }

  /**
   * Get role by ID
   */
  static async getById(id: string): Promise<Role | null> {
    try {
      const result = await db
        .select()
        .from(roles)
        .where(eq(roles.id, id))
        .limit(1);

      return result[0] || null;
    } catch (error) {
      console.error("Error getting role by ID:", error);
      throw error;
    }
  }

  /**
   * Create new role
   */
  static async create(data: {
    tenantId?: number | null;
    name: string;
    display_name: string;
    description?: string;
    is_system_role?: boolean;
    hierarchy_level?: number;
    permissions?: string[];
  }): Promise<Role> {
    // Use explicit transaction to ensure data persistence
    return await db.transaction(async (tx) => {
      try {
        const { permissions, ...roleBasicData } = data;

        const roleData: NewRole = {
          id: createId(),
          tenantId: roleBasicData.tenantId || null,
          name: roleBasicData.name,
          display_name: roleBasicData.display_name,
          description: roleBasicData.description || null,
          is_system_role: roleBasicData.is_system_role || false,
          hierarchy_level: roleBasicData.hierarchy_level || 50,
          permissions: [],
          is_active: true,
        };

        console.log("🔄 Inserting role into database with transaction:", roleData);
        const result = await tx.insert(roles).values(roleData).returning();
        const createdRole = result[0];

        if (!createdRole) {
          throw new Error("Failed to create role - no result returned from database");
        }

        console.log("✅ Role inserted successfully in transaction:", {
          id: createdRole.id,
          name: createdRole.name,
          display_name: createdRole.display_name
        });


        
        

        // Assign permissions if provided (within same transaction)
        if (permissions && permissions.length > 0) {
          console.log("🔐 Assigning permissions to role within transaction:", permissions);

          // Delete existing permissions first
          await tx.delete(role_permissions).where(eq(role_permissions.roleId, createdRole.id));

          // Insert new permissions
          if (permissions.length > 0) {
            const permissionData = permissions.map(permissionId => ({
              id: createId(),
              roleId: createdRole.id,
              permissionId,
            }));

            await tx.insert(role_permissions).values(permissionData);
            console.log(`✅ Assigned ${permissions.length} permissions to role`);
          }
        }

        // Verify the role was created by querying within the same transaction
        
        const verifyResult = await tx
          .select()
          .from(roles)
          .where(eq(roles.id, createdRole.id))
          .limit(1);

        if (verifyResult.length === 0) {
          throw new Error("Role verification failed - role not found after insert");
        }

        console.log("✅ Role creation verified within transaction");

        // Return the created role directly (most reliable)
        console.log("✅ Returning created role from transaction");
        return createdRole;
      } catch (error) {
        console.error("❌ Error creating role in transaction:", error);
        throw error;
      }
    });
  }

  /**
   * Assign permissions to role
   * 
   */
  static async assignPermissions(roleId: string, permissionIds: string[]): Promise<void> {
    try {
      // First, remove existing permissions
      await db.delete(role_permissions).where(eq(role_permissions.roleId, roleId));

      // Then add new permissions
      if (permissionIds.length > 0) {
        const rolePermissionData = permissionIds.map(permissionId => ({
          id: createId(),
          roleId,
          permissionId,
        }));

        await db.insert(role_permissions).values(rolePermissionData);
      }
    } catch (error) {
      console.error("Error assigning permissions to role:", error);
      throw error;
    }
  }

  /**
   * Update role
   */
  static async update(
    id: string,
    data: {
      name?: string;
      display_name?: string;
      description?: string;
      hierarchy_level?: number;
      permissions?: string[];
      is_active?: boolean;
    }
  ): Promise<Role> {
    try {
      // Extract permissions from data
      const { permissions, ...updateData } = data;

      // Update role basic info
      const result = await db
        .update(roles)
        .set({
          ...updateData,
          updatedAt: new Date(),
        })
        .where(eq(roles.id, id))
        .returning();

      if (result.length === 0) {
        throw new Error("Role not found");
      }

      // Update permissions if provided
      if (permissions !== undefined) {
        await this.assignPermissions(id, permissions);
      }

      // Return updated role with permissions
      const updatedRole = await this.getById(id);
      if (!updatedRole) {
        throw new Error("Role not found after update");
      }
      return updatedRole;
    } catch (error) {
      console.error("Error updating role:", error);
      throw error;
    }
  }

  /**
   * Delete role (soft delete)
   */
  static async delete(id: string): Promise<void> {
    try {
      await db
        .update(roles)
        .set({
          is_active: false,
          updatedAt: new Date(),
        })
        .where(eq(roles.id, id));
    } catch (error) {
      console.error("Error deleting role:", error);
      throw error;
    }
  }

  /**
   * Get all system roles (built-in roles)
   */
  static async getSystemRoles(): Promise<Role[]> {
    try {
      return await db
        .select()
        .from(roles)
        .where(and(eq(roles.is_system_role, true), eq(roles.is_active, true)))
        .orderBy(roles.hierarchy_level);
    } catch (error) {
      console.error("Error getting system roles:", error);
      throw error;
    }
  }

  /**
   * Get roles by tenant ID
   */
  static async getByTenantId(tenantId: number): Promise<Role[]> {
    try {
      return await db
        .select()
        .from(roles)
        .where(and(eq(roles.tenantId, tenantId), eq(roles.is_active, true)))
        .orderBy(roles.hierarchy_level, desc(roles.createdAt));
    } catch (error) {
      console.error("Error getting roles by tenant:", error);
      throw error;
    }
  }

  /**
   * Assign role to user
   */
  static async assignRole(data: {
    userId: string;
    roleId: string;
    tenantId?: number | null;
    assignedBy?: string;
    expiresAt?: Date;
  }): Promise<UserRole> {
    try {
      // Check if assignment already exists
      const existing = await db
        .select()
        .from(user_roles)
        .where(
          and(
            eq(user_roles.userId, data.userId),
            eq(user_roles.roleId, data.roleId),
            data.tenantId ? eq(user_roles.tenantId, data.tenantId) : sql`${user_roles.tenantId} IS NULL`
          )
        )
        .limit(1);

      if (existing.length > 0) {
        // Update existing assignment
        const result = await db
          .update(user_roles)
          .set({
            is_active: true,
            expiresAt: data.expiresAt || null,
            updatedAt: new Date(),
          })
          .where(eq(user_roles.id, existing[0].id))
          .returning();

        return result[0];
      }

      // Create new assignment
      const assignmentData: NewUserRole = {
        id: createId(),
        userId: data.userId,
        roleId: data.roleId,
        tenantId: data.tenantId || null,
        assignedBy: data.assignedBy || null,
        expiresAt: data.expiresAt || null,
        is_active: true,
      };

      const result = await db.insert(user_roles).values(assignmentData).returning();
      return result[0];
    } catch (error) {
      console.error("Error assigning role:", error);
      throw error;
    }
  }

  /**
   * Revoke role from user
   */
  static async revokeRole(userId: string, roleId: string, tenantId?: number | null): Promise<void> {
    try {
      await db
        .update(user_roles)
        .set({
          is_active: false,
          updatedAt: new Date(),
        })
        .where(
          and(
            eq(user_roles.userId, userId),
            eq(user_roles.roleId, roleId),
            tenantId ? eq(user_roles.tenantId, tenantId) : sql`${user_roles.tenantId} IS NULL`
          )
        );
    } catch (error) {
      console.error("Error revoking role:", error);
      throw error;
    }
  }

  /**
   * Get user roles with user info
   */
  static async getUserRoles(
    tenantId?: number | null,
    userId?: string,
    limit = 20,
    offset = 0
  ): Promise<UserRoleSearchResult> {
    try {
      // Build where conditions
      let whereConditions: any = eq(user_roles.is_active, true);

      if (tenantId !== undefined) {
        if (tenantId === null) {
          whereConditions = and(whereConditions, sql`${user_roles.tenantId} IS NULL`);
        } else {
          whereConditions = and(whereConditions, eq(user_roles.tenantId, tenantId));
        }
      }

      if (userId) {
        whereConditions = and(whereConditions, eq(user_roles.userId, userId));
      }

      // Query dengan join ke users table
      const userRoleResults = await db
        .select({
          id: user_roles.id,
          userId: user_roles.userId,
          roleId: user_roles.roleId,
          tenantId: user_roles.tenantId,
          assignedBy: user_roles.assignedBy,
          assignedAt: user_roles.assignedAt,
          expiresAt: user_roles.expiresAt,
          is_active: user_roles.is_active,
          createdAt: user_roles.createdAt,
          updatedAt: user_roles.updatedAt,
          user: {
            name: users.name,
            email: users.email,
          },
        })
        .from(user_roles)
        .innerJoin(users, eq(user_roles.userId, users.id))
        .where(whereConditions)
        .orderBy(desc(user_roles.assignedAt))
        .limit(limit + 1)
        .offset(offset);

      // Cek apakah ada data lebih
      const hasMore = userRoleResults.length > limit;
      if (hasMore) {
        userRoleResults.pop();
      }

      // Count total
      const totalResult = await db
        .select({ count: sql<number>`count(*)` })
        .from(user_roles)
        .innerJoin(users, eq(user_roles.userId, users.id))
        .where(whereConditions);

      const total = Number(totalResult[0]?.count || 0);

      return {
        userRoles: userRoleResults,
        total,
        hasMore,
      };
    } catch (error) {
      console.error("Error getting user roles:", error);
      throw error;
    }
  }
}
