import { sql, eq, and, SQL } from "drizzle-orm";
import { customers, customer_addresses, customer_waivers } from "@/lib/db/schema";
import { BaseService, CreateEntityData, UpdateEntityData } from "@/lib/core/base-service";
import { Customer, NewCustomer, CustomerAddress, NewCustomerAddress, CustomerWaiver, NewCustomerWaiver } from "@/lib/db/schema";
import { createId } from "@paralleldrive/cuid2";
import { db } from "@/lib/db";

// Extend base interfaces for customer-specific data
export interface CreateCustomerData extends CreateEntityData {
  tenantId: number;
  locationId?: string;
  email: string;
  mobileNumber?: string;
  mobileCountryCode?: string;
  firstName: string;
  lastName?: string;
  dateOfBirth?: string;
  gender?: string;
  pricingGroupId?: string;
  isActive?: boolean;
  notes?: string;
  // Address fields
  addressLine1?: string;
  addressLine2?: string;
  city?: string;
  state?: string;
  zip?: string;
  country?: string;
  // Waiver fields
  waiverFormIds?: string; // Comma-separated waiver form IDs
}

export interface UpdateCustomerData extends UpdateEntityData {
  tenantId?: number;
  locationId?: string;
  email?: string;
  mobileNumber?: string;
  mobileCountryCode?: string;
  firstName?: string;
  lastName?: string;
  dateOfBirth?: string;
  gender?: string;
  pricingGroupId?: string;
  isActive?: boolean;
  notes?: string;
  // Address fields
  addressLine1?: string;
  addressLine2?: string;
  city?: string;
  state?: string;
  zip?: string;
  country?: string;
  // Waiver fields
  waiverFormIds?: string[];
}

export interface CustomerStats {
  total: number;
  active: number;
  inactive: number;
  byLocation: Record<string, number>;
  byPricingGroup: Record<string, number>;
  byTenant: Record<number, {
    total: number;
    active: number;
  }>;
}

// Customer Service extending BaseService
export class CustomerService extends BaseService<Customer, CreateCustomerData, UpdateCustomerData> {
  constructor() {
    super(customers, 'Customer');
  }

  // Override create to handle address and waivers
  async create(data: CreateCustomerData): Promise<Customer> {
    const { addressLine1, addressLine2, city, state, zip, country, waiverFormIds, ...customerData } = data;

    // Create customer first
    const customer = await super.create(customerData);

    // Create address if any address field is provided
    if (addressLine1 || addressLine2 || city || state || zip || country) {
      await db.insert(customer_addresses).values({
        id: createId(),
        customerId: customer.id,
        addressLine1,
        addressLine2,
        city,
        state,
        zip,
        country,
        is_primary: true, // First address is always primary
      });
    }

    // Create customer waivers if waiver form IDs are provided
    if (waiverFormIds && waiverFormIds.length > 0) {
      const waiverData = waiverFormIds.map(waiverFormId => ({
        id: createId(),
        customerId: customer.id,
        waiverFormId,
        acknowledged: false, // Customer hasn't signed yet
        signed_at: null,
      }));

      await db.insert(customer_waivers).values(waiverData);
    }

    return customer;
  }

  // Override update to handle address and waivers
  async update(id: string, data: UpdateCustomerData): Promise<Customer> {
    const { addressLine1, addressLine2, city, state, zip, country, waiverFormIds, ...customerData } = data;

    // Update customer first
    const customer = await super.update(id, customerData);

    if (!customer) throw new Error("Customer not found");

    // Handle address update if any address field is provided
    if (addressLine1 !== undefined || addressLine2 !== undefined || city !== undefined ||
        state !== undefined || zip !== undefined || country !== undefined) {

      // Check if customer has existing primary address
      const [existingAddress] = await db
        .select()
        .from(customer_addresses)
        .where(and(
          eq(customer_addresses.customerId, id),
          eq(customer_addresses.is_primary, true)
        ))
        .limit(1);

      if (existingAddress) {
        // Update existing address
        await db
          .update(customer_addresses)
          .set({
            addressLine1,
            addressLine2,
            city,
            state,
            zip,
            country,
            updatedAt: new Date(),
          })
          .where(eq(customer_addresses.id, existingAddress.id));
      } else if (addressLine1 || addressLine2 || city || state || zip || country) {
        // Create new address if any field has value
        await db.insert(customer_addresses).values({
          id: createId(),
          customerId: id,
          addressLine1,
          addressLine2,
          city,
          state,
          zip,
          country,
          is_primary: true,
        });
      }
    }

    // Handle waiver forms update if provided
    if (waiverFormIds !== undefined) {
      // Delete existing customer waivers
      await db
        .delete(customer_waivers)
        .where(eq(customer_waivers.customerId, id));

      // Create new customer waivers if any are provided
      if (waiverFormIds.length > 0) {
        const waiverData = waiverFormIds.map(waiverFormId => ({
          id: createId(),
          customerId: id,
          waiverFormId,
          acknowledged: false, // Customer hasn't signed yet
          signed_at: null,
        }));

        await db.insert(customer_waivers).values(waiverData);
      }
    }

    return customer;
  }

  // Override getById to include address and waiver data
  async getById(id: string): Promise<Customer | null> {
    const customer = await super.getById(id);
    if (!customer) return null;

    // Get primary address
    const [address] = await db
      .select()
      .from(customer_addresses)
      .where(and(
        eq(customer_addresses.customerId, id),
        eq(customer_addresses.is_primary, true)
      ))
      .limit(1);

    // Get customer waivers
    const waivers = await db
      .select()
      .from(customer_waivers)
      .where(eq(customer_waivers.customerId, id));

    // Merge address and waiver fields into customer object
    const customerWithExtras = {
      ...customer,
      // Address fields
      addressLine1: address?.addressLine1 || undefined,
      addressLine2: address?.addressLine2 || undefined,
      city: address?.city || undefined,
      state: address?.state || undefined,
      zip: address?.zip || undefined,
      country: address?.country || undefined,
      // Waiver fields
      waiverFormIds: waivers.map(w => w.waiverFormId).filter(Boolean) as string[],
    };

    console.log('🔗 API returning customer with address and waivers:', customerWithExtras);
    return customerWithExtras as any; // Use any to allow extra fields
  }

  // Get customer with primary address data
  async getByIdWithAddress(id: string): Promise<(Customer & { address?: CustomerAddress }) | null> {
    const customer = await this.getById(id);
    if (!customer) return null;

    // Get primary address
    const [address] = await db
      .select()
      .from(customer_addresses)
      .where(and(
        eq(customer_addresses.customerId, id),
        eq(customer_addresses.is_primary, true)
      ))
      .limit(1);

    return {
      ...customer,
      address: address || undefined,
    };
  }

  // Implement abstract method for filter conditions
  protected buildFilterConditions(filters: Record<string, any>): SQL[] {
    const conditions: SQL[] = [];

    if (filters.isActive !== undefined) {
      conditions.push(sql`${customers.isActive} = ${filters.isActive}`);
    }

    if (filters.locationId) {
      conditions.push(sql`${customers.locationId} = ${filters.locationId}`);
    }

    if (filters.pricingGroupId) {
      conditions.push(sql`${customers.pricingGroupId} = ${filters.pricingGroupId}`);
    }

    if (filters.search) {
      const searchTerm = `%${filters.search.toLowerCase()}%`;
      conditions.push(
        sql`(LOWER(${customers.firstName}) LIKE ${searchTerm} OR
            LOWER(${customers.lastName}) LIKE ${searchTerm} OR
            LOWER(${customers.email}) LIKE ${searchTerm})`
      );
    }

    return conditions;
  }

  // Get active customers by tenant
  async getActiveByTenantId(tenantId: number): Promise<Customer[]> {
    return this.getAll({
      tenantId,
      filters: { isActive: true },
      orderField: 'firstName',
      orderBy: 'asc',
    });
  }

  // Get customers by location
  async getByLocationId(locationId: string): Promise<Customer[]> {
    return this.getAll({
      filters: { locationId },
      orderField: 'firstName',
      orderBy: 'asc',
    });
  }

  // Get customers by pricing group
  async getByPricingGroupId(pricingGroupId: string): Promise<Customer[]> {
    return this.getAll({
      filters: { pricingGroupId },
      orderField: 'firstName',
      orderBy: 'asc',
    });
  }

  // Toggle active status
  async toggleActive(id: string): Promise<Customer> {
    const current = await this.getById(id);
    if (!current) {
      throw new Error("Customer not found");
    }

    return this.update(id, { isActive: !current.isActive });
  }

  // Duplicate customer
  async duplicate(id: string, newEmail?: string): Promise<Customer> {
    const original = await this.getById(id);
    if (!original) {
      throw new Error("Customer not found");
    }

    const duplicatedData: CreateCustomerData = {
      tenantId: original.tenantId!,
      locationId: original.locationId!,
      email: newEmail || `copy_${Date.now()}_${original.email}`,
      mobileNumber: original.mobileNumber ?? "",
      mobileCountryCode: original.mobileCountryCode ?? "",
      firstName: original.firstName,
      lastName: original.lastName ?? "",
      dateOfBirth: original.dateOfBirth!,
      gender: original.gender!,
      pricingGroupId: original.pricingGroupId ?? "",
      isActive: false, // Start as inactive
      notes: original.notes ?? "",
    };

    return this.create(duplicatedData);
  }

  // Validate customer data
  protected validateCreateData(data: CreateCustomerData): void {
    if (!data.firstName?.trim()) {
      throw new Error("Customer first name is required");
    }
    if (!data.email?.trim()) {
      throw new Error("Customer email is required");
    }
    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(data.email)) {
      throw new Error("Invalid email format");
    }
  }

  protected validateUpdateData(data: UpdateCustomerData): void {
    if (data.firstName !== undefined && !data.firstName?.trim()) {
      throw new Error("Customer first name cannot be empty");
    }
    if (data.email !== undefined && !data.email?.trim()) {
      throw new Error("Customer email cannot be empty");
    }
    if (data.email !== undefined) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(data.email)) {
        throw new Error("Invalid email format");
      }
    }
  }

  // Search customers
  async searchCustomers(query: string, tenantId?: number): Promise<Customer[]> {
    return super.search(query, ['firstName', 'lastName', 'email'], { tenantId });
  }

  // Bulk operations specific to customers
  async bulkToggleActive(ids: string[], isActive: boolean): Promise<Customer[]> {
    return this.bulkUpdate(ids, { isActive });
  }

  // Get enhanced statistics
  async getCustomerStats(tenantId?: number): Promise<CustomerStats> {
    const baseStats = await super.getStats(tenantId);

    let whereClause = sql`1=1`;
    if (tenantId) {
      whereClause = sql`${customers.tenantId} = ${tenantId}`;
    }

    const [activeCount] = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(customers)
      .where(sql`${whereClause} AND ${customers.isActive} = true`);
    

    const [inactiveCount] = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(customers)
      .where(sql`${whereClause} AND ${customers.isActive} = false`);

    // Get stats by location
    const locationStats = await this.db
      .select({
        locationId: customers.locationId,
        count: sql<number>`count(*)`,
      })
      .from(customers)
      .where(whereClause)
      .groupBy(customers.locationId);

    const byLocation: Record<string, number> = {};
    locationStats.forEach(stat => {
      if (stat.locationId) {
        byLocation[stat.locationId] = stat.count;
      }
    });

    // Get stats by pricing group
    const pricingGroupStats = await this.db
      .select({
        pricingGroupId: customers.pricingGroupId,
        count: sql<number>`count(*)`,
      })
      .from(customers)
      .where(whereClause)
      .groupBy(customers.pricingGroupId);

    const byPricingGroup: Record<string, number> = {};
    pricingGroupStats.forEach(stat => {
      if (stat.pricingGroupId) {
        byPricingGroup[stat.pricingGroupId] = stat.count;
      }
    });

    // Get stats by tenant if not filtered by specific tenant
    
    const byTenant: Record<number, { total: number; active: number }> = {};
    if (!tenantId) {
      const tenantStats = await this.db
        .select({
          tenantId: customers.tenantId,
          total: sql<number>`count(*)`,
          active: sql<number>`sum(case when ${customers.isActive} then 1 else 0 end)`,
        })
        .from(customers)
        .groupBy(customers.tenantId);

      tenantStats.forEach(stat => {
        if (stat.tenantId) {
          byTenant[stat.tenantId] = {
            total: stat.total,
            active: stat.active,
          };
        }
      });
    }

    return {
      total: baseStats.total,
      active: activeCount.count,
      inactive: inactiveCount.count,
      byLocation,
      byPricingGroup,
      byTenant,
    };
  }
}
