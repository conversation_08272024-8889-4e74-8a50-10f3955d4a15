import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { z } from "zod";
import { WaiverFormService } from "@/lib/services/waiver-forms.service";

// Validation schema for updating waiver form
const updateWaiverFormSchema = z.object({
  tenantId: z.number().int().positive().optional(),
  name: z.string().min(1).max(255).optional(),
  description: z.string().max(1000).optional(),
  content: z.string().min(1).optional(),
  version: z.string().max(50).optional(),
  isActive: z.boolean().optional(),
  isRequired: z.boolean().optional(),
  expiryDays: z.number().int().min(1).optional(),
  sortOrder: z.number().int().min(0).optional(),
});

const waiverFormService = new WaiverFormService();

// PUT /api/waiver-forms/[id] - Update waiver form
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id: waiverFormId } = await params;
    if (!waiverFormId) {
      return NextResponse.json(
        { error: "Waiver form ID is required" },
        { status: 400 }
      );
    }

    const body = await request.json();
    const validatedData = updateWaiverFormSchema.parse(body);

    const updatedWaiverForm = await waiverFormService.update(waiverFormId, validatedData);

    return NextResponse.json(
      { 
        message: "Waiver form updated successfully", 
        data: updatedWaiverForm 
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error updating waiver form:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid data", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to update waiver form" },
      { status: 500 }
    );
  }
}

// DELETE /api/waiver-forms/[id] - Delete waiver form
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id: waiverFormId } = await params;
    if (!waiverFormId) {
      return NextResponse.json(
        { error: "Waiver form ID is required" },
        { status: 400 }
      );
    }

    const deletedWaiverForm = await waiverFormService.delete(waiverFormId);

    return NextResponse.json(
      {
        message: "Waiver form deleted successfully",
        data: deletedWaiverForm
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error deleting waiver form:", error);
    return NextResponse.json(
      { error: "Failed to delete waiver form" },
      { status: 500 }
    );
  }
}

// GET /api/waiver-forms/[id] - Get single waiver form
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id: waiverFormId } = await params;
    if (!waiverFormId) {
      return NextResponse.json(
        { error: "Waiver form ID is required" },
        { status: 400 }
      );
    }

    const waiverForm = await waiverFormService.getById(waiverFormId);

    if (!waiverForm) {
      return NextResponse.json(
        { error: "Waiver form not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { data: waiverForm },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error fetching waiver form:", error);
    return NextResponse.json(
      { error: "Failed to fetch waiver form" },
      { status: 500 }
    );
  }
}

// PATCH /api/waiver-forms/[id] - Toggle status (active/required)
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id: waiverFormId } = await params;
    if (!waiverFormId) {
      return NextResponse.json(
        { error: "Waiver form ID is required" },
        { status: 400 }
      );
    }

    const { searchParams } = new URL(request.url);
    const action = searchParams.get("action");

    let updatedWaiverForm;
    let message;

    switch (action) {
      case "toggle-active":
        updatedWaiverForm = await waiverFormService.toggleActive(waiverFormId);
        message = `Waiver form ${updatedWaiverForm.isActive ? 'activated' : 'deactivated'} successfully`;
        break;
      case "toggle-required":
        updatedWaiverForm = await waiverFormService.toggleRequired(waiverFormId);
        message = `Waiver form ${updatedWaiverForm.isRequired ? 'marked as required' : 'marked as optional'} successfully`;
        break;
      default:
        return NextResponse.json(
          { error: "Invalid action. Use 'toggle-active' or 'toggle-required'" },
          { status: 400 }
        );
    }

    return NextResponse.json(
      {
        message,
        data: updatedWaiverForm
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error toggling waiver form status:", error);
    return NextResponse.json(
      { error: "Failed to toggle waiver form status" },
      { status: 500 }
    );
  }
}
