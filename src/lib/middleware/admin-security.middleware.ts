import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { RBACHelpers } from "@/lib/auth/rbac-integration";
import { z } from "zod";
import { headers } from "next/headers";

/**
 * Enterprise-Grade Admin Security Middleware
 * 
 * Implements FAANG-level security standards for admin endpoints:
 * - Multi-layer authentication validation
 * - Rate limiting per user and IP
 * - CSRF protection
 * - Session integrity verification
 * - Security audit logging
 * - Input sanitization
 */

// Rate limiting store (in production, use Redis)
const rateLimitStore = new Map<string, { count: number; resetTime: number; blocked: boolean }>();
const securityAuditLog = new Map<string, Array<{ timestamp: number; action: string; ip: string; userAgent: string }>>();

// Security configuration
const SECURITY_CONFIG = {
  RATE_LIMIT: {
    ADMIN_ENDPOINTS: {
      maxRequests: 100, // per hour
      windowMs: 60 * 60 * 1000, // 1 hour
      blockDuration: 15 * 60 * 1000, // 15 minutes block
    },
    SENSITIVE_OPERATIONS: {
      maxRequests: 10, // per hour for create/update/delete
      windowMs: 60 * 60 * 1000,
      blockDuration: 30 * 60 * 1000, // 30 minutes block
    }
  },
  SESSION: {
    maxAge: 8 * 60 * 60 * 1000, // 8 hours for admin sessions
    refreshThreshold: 30 * 60 * 1000, // refresh if expires in 30 minutes
  },
  SECURITY_HEADERS: {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Permissions-Policy': 'camera=(), microphone=(), geolocation=(), payment=()',
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',
  }
};

/**
 * Rate limiting implementation
 */
function checkRateLimit(
  identifier: string, 
  config: typeof SECURITY_CONFIG.RATE_LIMIT.ADMIN_ENDPOINTS
): { allowed: boolean; remaining: number; resetTime: number } {
  const now = Date.now();
  const record = rateLimitStore.get(identifier);

  // Check if currently blocked
  if (record?.blocked && now < record.resetTime) {
    return { allowed: false, remaining: 0, resetTime: record.resetTime };
  }

  // Reset or initialize record
  if (!record || now > record.resetTime) {
    rateLimitStore.set(identifier, { 
      count: 1, 
      resetTime: now + config.windowMs,
      blocked: false 
    });
    return { allowed: true, remaining: config.maxRequests - 1, resetTime: now + config.windowMs };
  }

  // Check if limit exceeded
  if (record.count >= config.maxRequests) {
    record.blocked = true;
    record.resetTime = now + config.blockDuration;
    return { allowed: false, remaining: 0, resetTime: record.resetTime };
  }

  // Increment counter
  record.count++;
  return { 
    allowed: true, 
    remaining: config.maxRequests - record.count, 
    resetTime: record.resetTime 
  };
}

/**
 * Security audit logging
 */
function logSecurityEvent(
  userId: string,
  action: string,
  ip: string,
  userAgent: string,
  additional?: Record<string, any>
) {
  const log = securityAuditLog.get(userId) || [];
  log.push({
    timestamp: Date.now(),
    action,
    ip,
    userAgent,
    ...additional
  });
  
  // Keep only last 100 events per user
  if (log.length > 100) {
    log.splice(0, log.length - 100);
  }
  
  securityAuditLog.set(userId, log);
  
  // Log to console for monitoring (in production, use proper logging service)
  console.log(`🔒 SECURITY AUDIT: ${userId} - ${action} from ${ip}`, additional);
}

/**
 * Validate session integrity
 */
async function validateSessionIntegrity(session: any, request: NextRequest): Promise<boolean> {
  try {
    // Check session age
    const sessionAge = Date.now() - (session.user.iat * 1000 || 0);
    if (sessionAge > SECURITY_CONFIG.SESSION.maxAge) {
      console.warn(`🚨 Session expired for user ${session.user.id}`);
      return false;
    }

    // Validate session structure
    if (!session.user.id || !session.user.email || !session.user.roles) {
      console.warn(`🚨 Invalid session structure for user ${session.user.id}`);
      return false;
    }

    // Check for session hijacking indicators
    const userAgent = request.headers.get('user-agent') || '';
    const expectedUserAgent = session.user.userAgent;
    
    if (expectedUserAgent && userAgent !== expectedUserAgent) {
      console.warn(`🚨 User agent mismatch for user ${session.user.id}`);
      // In production, you might want to invalidate the session
    }

    return true;
  } catch (error) {
    console.error('Session validation error:', error);
    return false;
  }
}

/**
 * CSRF Protection
 */
function validateCSRFToken(request: NextRequest, session: any): boolean {
  // Skip CSRF for GET requests
  if (request.method === 'GET') return true;

  const csrfToken = request.headers.get('x-csrf-token');
  const sessionCSRF = session.user.csrfToken;

  if (!csrfToken || !sessionCSRF || csrfToken !== sessionCSRF) {
    console.warn(`🚨 CSRF token validation failed for user ${session.user.id}`);
    return false;
  }

  return true;
}

/**
 * Input sanitization
 */
function sanitizeInput(data: any): any {
  if (typeof data === 'string') {
    // Remove potentially dangerous characters
    return data
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '')
      .trim();
  }
  
  if (Array.isArray(data)) {
    return data.map(sanitizeInput);
  }
  
  if (data && typeof data === 'object') {
    const sanitized: any = {};
    for (const [key, value] of Object.entries(data)) {
      sanitized[key] = sanitizeInput(value);
    }
    return sanitized;
  }
  
  return data;
}

/**
 * Main admin security middleware
 */
export async function withAdminSecurity(
  handler: (request: NextRequest, context: any) => Promise<NextResponse>,
  options: {
    requiredPermissions?: string[];
    requiredRoles?: string[];
    sensitiveOperation?: boolean;
    skipCSRF?: boolean;
  } = {}
) {
  return async (request: NextRequest, context?: any) => {
    const startTime = Date.now();
    const ip = request.headers.get('x-forwarded-for') || 
               request.headers.get('x-real-ip') || 
               'unknown';
    const userAgent = request.headers.get('user-agent') || '';

    try {
      // 1. Rate Limiting
      const rateLimitConfig = options.sensitiveOperation 
        ? SECURITY_CONFIG.RATE_LIMIT.SENSITIVE_OPERATIONS
        : SECURITY_CONFIG.RATE_LIMIT.ADMIN_ENDPOINTS;
      
      const ipRateLimit = checkRateLimit(`ip:${ip}`, rateLimitConfig);
      if (!ipRateLimit.allowed) {
        return NextResponse.json(
          { 
            error: "Rate limit exceeded", 
            retryAfter: Math.ceil((ipRateLimit.resetTime - Date.now()) / 1000)
          },
          { 
            status: 429,
            headers: {
              'Retry-After': Math.ceil((ipRateLimit.resetTime - Date.now()) / 1000).toString(),
              'X-RateLimit-Remaining': '0',
              'X-RateLimit-Reset': ipRateLimit.resetTime.toString(),
            }
          }
        );
      }

      // 2. Authentication
      const session = await auth();
      if (!session?.user) {
        logSecurityEvent('anonymous', 'unauthorized_access_attempt', ip, userAgent, {
          endpoint: request.url,
          method: request.method
        });
        
        return NextResponse.json(
          { error: "Authentication required" },
          { status: 401, headers: SECURITY_CONFIG.SECURITY_HEADERS }
        );
      }

      // 3. Session Integrity Validation
      const isValidSession = await validateSessionIntegrity(session, request);
      if (!isValidSession) {
        logSecurityEvent(session.user.id, 'invalid_session_detected', ip, userAgent);
        
        return NextResponse.json(
          { error: "Session invalid or expired" },
          { status: 401, headers: SECURITY_CONFIG.SECURITY_HEADERS }
        );
      }

      // 4. User-specific Rate Limiting
      const userRateLimit = checkRateLimit(`user:${session.user.id}`, rateLimitConfig);
      if (!userRateLimit.allowed) {
        logSecurityEvent(session.user.id, 'rate_limit_exceeded', ip, userAgent);
        
        return NextResponse.json(
          { 
            error: "User rate limit exceeded",
            retryAfter: Math.ceil((userRateLimit.resetTime - Date.now()) / 1000)
          },
          { status: 429 }
        );
      }

      // 5. RBAC Authorization
      const isSuperAdmin = RBACHelpers.isSuperAdmin(session);
      const isTenantAdmin = RBACHelpers.isTenantAdmin(session);

      if (!isSuperAdmin && !isTenantAdmin) {
        logSecurityEvent(session.user.id, 'insufficient_permissions', ip, userAgent, {
          requiredRoles: ['super_admin', 'tenant_admin'],
          userRoles: session.user.roles
        });
        
        return NextResponse.json(
          { error: "Insufficient permissions" },
          { status: 403, headers: SECURITY_CONFIG.SECURITY_HEADERS }
        );
      }

      // 6. Additional Permission Checks
      if (options.requiredPermissions) {
        const hasPermissions = options.requiredPermissions.every(permission => {
          const [module, action] = permission.split('.');
          return RBACHelpers.hasPermission(session, module, action);
        });

        if (!hasPermissions) {
          logSecurityEvent(session.user.id, 'missing_specific_permissions', ip, userAgent, {
            requiredPermissions: options.requiredPermissions,
            userPermissions: session.user.permissions
          });
          
          return NextResponse.json(
            { error: "Missing required permissions" },
            { status: 403 }
          );
        }
      }

      // 7. CSRF Protection
      if (!options.skipCSRF && !validateCSRFToken(request, session)) {
        logSecurityEvent(session.user.id, 'csrf_validation_failed', ip, userAgent);
        
        return NextResponse.json(
          { error: "CSRF validation failed" },
          { status: 403 }
        );
      }

      // 8. Input Sanitization (for non-GET requests)
      if (request.method !== 'GET') {
        try {
          const body = await request.json();
          const sanitizedBody = sanitizeInput(body);
          
          // Replace request body with sanitized version
          const sanitizedRequest = new NextRequest(request.url, {
            method: request.method,
            headers: request.headers,
            body: JSON.stringify(sanitizedBody),
          });
          
          request = sanitizedRequest;
        } catch (error) {
          // If body parsing fails, continue with original request
        }
      }

      // 9. Security Audit Logging
      logSecurityEvent(session.user.id, `admin_${request.method.toLowerCase()}`, ip, userAgent, {
        endpoint: request.url,
        processingTime: Date.now() - startTime
      });

      // 10. Execute Handler with Security Context
      const securityContext = {
        session,
        ip,
        userAgent,
        rateLimitRemaining: userRateLimit.remaining,
        isSuperAdmin,
        isTenantAdmin,
        ...context
      };

      const response = await handler(request, securityContext);

      // Add security headers to response
      Object.entries(SECURITY_CONFIG.SECURITY_HEADERS).forEach(([key, value]) => {
        response.headers.set(key, value);
      });

      // Add rate limit headers
      response.headers.set('X-RateLimit-Remaining', userRateLimit.remaining.toString());
      response.headers.set('X-RateLimit-Reset', userRateLimit.resetTime.toString());

      return response;

    } catch (error) {
      console.error('🚨 Admin security middleware error:', error);
      
      // Log security error
      const userId = 'unknown';
      logSecurityEvent(userId, 'security_middleware_error', ip, userAgent, {
        error: error instanceof Error ? error.message : 'Unknown error'
      });

      return NextResponse.json(
        { error: "Internal security error" },
        { status: 500, headers: SECURITY_CONFIG.SECURITY_HEADERS }
      );
    }
  };
}

/**
 * Utility function to generate CSRF token
 */
export function generateCSRFToken(): string {
  return Array.from(crypto.getRandomValues(new Uint8Array(32)))
    .map(b => b.toString(16).padStart(2, '0'))
    .join('');
}

/**
 * Export security utilities
 */
export const AdminSecurityUtils = {
  checkRateLimit,
  logSecurityEvent,
  validateSessionIntegrity,
  validateCSRFToken,
  sanitizeInput,
  generateCSRFToken,
  SECURITY_CONFIG,
};
