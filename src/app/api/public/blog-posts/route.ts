import { NextRequest, NextResponse } from "next/server";
import { BlogService } from "@/lib/services/blog.service";

// GET /api/public/blog-posts - Public endpoint for published blog posts
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    const tenantId = searchParams.get("tenantId");
    const search = searchParams.get("search");
    const category_id = searchParams.get("category_id");
    const tags = searchParams.get("tags")?.split(',').filter(Boolean);
    const limit = searchParams.get("limit");
    const offset = searchParams.get("offset");
    const featured = searchParams.get("featured");

    if (!tenantId) {
      return NextResponse.json(
        { error: "Tenant ID is required" },
        { status: 400 }
      );
    }

    const filters = {
      ...(search && { search }),
      ...(category_id && { category_id }),
      ...(tags && { tags }),
      ...(featured === "true" && { is_featured: true }),
    };

    const pagination = {
      ...(limit && { limit: parseInt(limit) }),
      ...(offset && { offset: parseInt(offset) }),
    };

    // Only return published posts for public API
    const blogPosts = await BlogService.getPublishedPosts(
      parseInt(tenantId),
      filters,
      pagination
    );

    return NextResponse.json({
      success: true,
      data: blogPosts,
      count: blogPosts.length,
    });
  } catch (error) {
    console.error("Error fetching public blog posts:", error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : "Failed to fetch blog posts",
        success: false 
      },
      { status: 500 }
    );
  }
}
