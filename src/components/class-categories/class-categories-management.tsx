"use client";

import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  FolderOpen,
  Plus,
  Search,
  Edit,
  Trash2,
  Loader2,
  AlertCircle,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { ClassCategoryForm } from "@/components/forms/class-category-form";
import {
  useClassCategorySearch,
  useCreateClassCategory,
  useUpdateClassCategory,
  useDeleteClassCategory,
} from "@/lib/hooks/queries/use-class-category-queries";
import { type ClassCategory } from "@/lib/db/schema";
import { SuccessToast } from "@/components/ui/success-toast";
  

/**
 * Class Categories Management Component
 * 
 * Component utama untuk manage class categories dengan fitur lengkap:
 * - List/search categories
 * - Create new category
 * - Edit existing category
 * - Delete category
 * - Success animations dan user feedback
 */

interface ClassCategoriesManagementProps {
  tenantId: number;
  className?: string;
}

export function ClassCategoriesManagement({
  tenantId,
  className = "",
}: ClassCategoriesManagementProps) {
  // State management
  const [searchTerm, setSearchTerm] = useState("");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingCategory, setEditingCategory] = useState<ClassCategory | null>(null);
  const [deletingCategory, setDeletingCategory] = useState<ClassCategory | null>(null);
  const [showSuccessToast, setShowSuccessToast] = useState(false);
  const [createdCategory, setCreatedCategory] = useState<ClassCategory | null>(null);

  // Queries
  const {
    data: searchResult,
    isLoading,
    error,
    refetch,
  } = useClassCategorySearch(tenantId, searchTerm, 50, 0);

  // Mutations
  const createMutation = useCreateClassCategory();
  const updateMutation = useUpdateClassCategory();
  const deleteMutation = useDeleteClassCategory();

  // Handlers
  const handleSearch = (value: string) => {
    setSearchTerm(value);
  };

  const handleCreateCategory = async (data: { tenantId: number; name: string }) => {
    try {
      const newCategory = await createMutation.mutateAsync({
        tenantId: data.tenantId,
        name: data.name,
      });

      setIsCreateDialogOpen(false);
      setCreatedCategory(newCategory);
      setShowSuccessToast(true);

      // Hide success toast after duration
      setTimeout(() => {
        setShowSuccessToast(false);
      }, 4000);
    } catch (error) {
      console.error("Create category error:", error);
      // Error handling bisa ditambahkan nanti kalau diperlukan
    }
  };

  const handleEditCategory = async (data: { tenantId: number; name: string }) => {
    if (!editingCategory) return;

    try {
      const updatedCategory = await updateMutation.mutateAsync({
        id: editingCategory.id,
        tenantId: editingCategory.tenantId,
        data: {
          name: data.name,
        },
      });

      setEditingCategory(null);
      setCreatedCategory(updatedCategory);
      setShowSuccessToast(true);

      // Hide success toast after duration
      setTimeout(() => {
        setShowSuccessToast(false);
      }, 4000);
    } catch (error) {
      console.error("Update category error:", error);
      // Error handling bisa ditambahkan nanti kalau diperlukan
    }
  };

  const handleDeleteCategory = async () => {
    if (!deletingCategory) return;

    try {
      await deleteMutation.mutateAsync({
        id: deletingCategory.id,
        tenantId: deletingCategory.tenantId,
      });

      setDeletingCategory(null);
      setShowSuccessToast(true);

      // Hide success toast after duration
      setTimeout(() => {
        setShowSuccessToast(false);
      }, 4000);
    } catch (error) {
      console.error("Delete category error:", error);
      // Error handling bisa ditambahkan nanti kalau diperlukan
    }
  };



  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading class categories...</span>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Failed to load class categories. Please try again.
          <Button
            variant="outline"
            size="sm"
            onClick={() => refetch()}
            className="ml-2"
          >
            Retry
          </Button>
        </AlertDescription>
      </Alert>
    );
  }

  const categories = searchResult?.categories || [];

  return (
    <div className={`space-y-6 ${className}`}>

      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl font-bold flex items-center gap-2">
            <FolderOpen className="h-6 w-6" />
            Class Categories
          </h1>
          <p className="text-muted-foreground">
            Organize your classes into categories for better management
          </p>
        </div>
        <Button onClick={() => setIsCreateDialogOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Add Category
        </Button>
      </div>

      {/* Search */}
      <div className="relative max-w-md">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input
          placeholder="Search categories..."
          value={searchTerm}
          onChange={(e) => handleSearch(e.target.value)}
          className="pl-10"
        />
      </div>

      {/* Categories Grid */}
      <motion.div
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
      >
        <AnimatePresence>
          {categories.map((category: ClassCategory) => (
            <motion.div
              key={category.id}
              layout
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              transition={{ duration: 0.2 }}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Card className="hover:shadow-lg transition-all duration-200">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <CardTitle className="text-lg">{category.name}</CardTitle>
                    <div className="flex gap-1">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setEditingCategory(category)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setDeletingCategory(category)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between text-xs text-muted-foreground">
                    <span>
                      Created: {new Date(category.createdAt).toLocaleDateString()}
                    </span>
                    <Badge variant="secondary">Active</Badge>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </AnimatePresence>
      </motion.div>

      {/* Empty State */}
      {categories.length === 0 && !isLoading && (
        <motion.div
          className="text-center py-12"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          <FolderOpen className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium mb-2">No class categories found</h3>
          <p className="text-muted-foreground mb-4">
            {searchTerm
              ? "No categories match your search criteria."
              : "Get started by creating your first class category."}
          </p>
          {!searchTerm && (
            <Button onClick={() => setIsCreateDialogOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Create First Category
            </Button>
          )}
        </motion.div>
      )}

      {/* Create Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Create Class Category</DialogTitle>
            <DialogDescription>
              Add a new category to organize your classes
            </DialogDescription>
          </DialogHeader>
          <ClassCategoryForm
            onSubmit={handleCreateCategory}
            onCancel={() => setIsCreateDialogOpen(false)}
            tenantId={tenantId}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Dialog */}
      <Dialog open={!!editingCategory} onOpenChange={() => setEditingCategory(null)}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Class Category</DialogTitle>
            <DialogDescription>
              Update category information
            </DialogDescription>
          </DialogHeader>
          {editingCategory && (
            <ClassCategoryForm
              category={editingCategory}
              onSubmit={handleEditCategory}
              onCancel={() => setEditingCategory(null)}
              tenantId={tenantId}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation */}
      <AlertDialog open={!!deletingCategory} onOpenChange={() => setDeletingCategory(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Class Category</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{deletingCategory?.name}"? This action cannot be undone.
              {/* TODO: Add warning if category is being used by classes */}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteCategory}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Success Animations */}
      <SuccessToast
        isVisible={showSuccessToast}
        onClose={() => setShowSuccessToast(false)}
        title="Class Category Success!"
        description={createdCategory ?
          `Category "${createdCategory.name}" has been processed successfully.` :
          "Your class category operation completed successfully."
        }
        type="general"
        action={{
          label: "View All Categories",
          onClick: () => setShowSuccessToast(false)
        }}
      />
    </div>
  );
}
