import { db } from "@/lib/db";
import { roles, permissions, role_permissions, users, user_roles } from "@/lib/db/schema";
import { createId } from "@paralleldrive/cuid2";
import { eq, and } from "drizzle-orm";

/**
 * RB<PERSON> Seeder
 * 
 * Script untuk seed initial RBAC data ke database.
 * Ini kayak "installer" yang setup basic roles dan permissions.
 * 
 * Jalankan dengan: npm run seed:rbac
 */

// Default system roles
const systemRoles = [
  {
    id: createId(),
    name: "super_admin",
    display_name: "Super Administrator",
    description: "Full system access across all tenants",
    is_system_role: true,
    hierarchy_level: 0,
    tenantId: null,
  },
  {
    id: createId(),
    name: "tenant_admin",
    display_name: "Tenant Administrator",
    description: "Full access within their tenant",
    is_system_role: true,
    hierarchy_level: 10,
    tenantId: null,
  },
  {
    id: createId(),
    name: "instructor",
    display_name: "Instructor/Trainer",
    description: "Limited access to classes and customers they're assigned to",
    is_system_role: true,
    hierarchy_level: 20,
    tenantId: null,
  },
  {
    id: createId(),
    name: "customer",
    display_name: "Customer",
    description: "Access only to their own data and bookings",
    is_system_role: true,
    hierarchy_level: 30,
    tenantId: null,
  },
];

// Default system permissions
const systemPermissions = [
  // System permissions
  { module: "system", action: "manage", display_name: "Manage System", description: "Full system management access" },
  
  // Tenant permissions
  { module: "tenant", action: "manage", display_name: "Manage Tenant", description: "Manage tenant settings and data" },
  { module: "tenant", action: "read", display_name: "View Tenant", description: "View tenant information" },
  
  // User permissions
  { module: "users", action: "create", display_name: "Create Users", description: "Create new users" },
  { module: "users", action: "read", display_name: "View Users", description: "View user information" },
  { module: "users", action: "update", display_name: "Update Users", description: "Update user information" },
  { module: "users", action: "delete", display_name: "Delete Users", description: "Delete users" },
  { module: "users", action: "manage", display_name: "Manage Users", description: "Full user management" },
  
  // Role permissions
  { module: "roles", action: "create", display_name: "Create Roles", description: "Create new roles" },
  { module: "roles", action: "read", display_name: "View Roles", description: "View role information" },
  { module: "roles", action: "update", display_name: "Update Roles", description: "Update role information" },
  { module: "roles", action: "delete", display_name: "Delete Roles", description: "Delete roles" },
  { module: "roles", action: "assign", display_name: "Assign Roles", description: "Assign roles to users" },
  { module: "roles", action: "revoke", display_name: "Revoke Roles", description: "Revoke roles from users" },

  // Permission permissions
  { module: "permissions", action: "create", display_name: "Create Permissions", description: "Create new permissions" },
  { module: "permissions", action: "read", display_name: "View Permissions", description: "View permission information" },
  { module: "permissions", action: "update", display_name: "Update Permissions", description: "Update permission information" },
  { module: "permissions", action: "delete", display_name: "Delete Permissions", description: "Delete permissions" },
  { module: "permissions", action: "manage", display_name: "Manage Permissions", description: "Full permission management" },
  
  // Class permissions
  { module: "classes", action: "create", display_name: "Create Classes", description: "Create new classes" },
  { module: "classes", action: "read", display_name: "View Classes", description: "View class information" },
  { module: "classes", action: "update", display_name: "Update Classes", description: "Update class information" },
  { module: "classes", action: "delete", display_name: "Delete Classes", description: "Delete classes" },
  { module: "classes", action: "manage", display_name: "Manage Classes", description: "Full class management" },

  // Class Schedule permissions
  { module: "class-schedules", action: "create", display_name: "Create Class Schedules", description: "Create new class schedules" },
  { module: "class-schedules", action: "read", display_name: "View Class Schedules", description: "View class schedule information" },
  { module: "class-schedules", action: "update", display_name: "Update Class Schedules", description: "Update class schedule information" },
  { module: "class-schedules", action: "delete", display_name: "Delete Class Schedules", description: "Delete class schedules" },
  { module: "class-schedules", action: "manage", display_name: "Manage Class Schedules", description: "Full class schedule management" },
  
  // Customer permissions
  { module: "customers", action: "create", display_name: "Create Customers", description: "Create new customers" },
  { module: "customers", action: "read", display_name: "View Customers", description: "View customer information" },
  { module: "customers", action: "update", display_name: "Update Customers", description: "Update customer information" },
  { module: "customers", action: "delete", display_name: "Delete Customers", description: "Delete customers" },
  { module: "customers", action: "manage", display_name: "Manage Customers", description: "Full customer management" },
  
  // Package permissions
  { module: "packages", action: "create", display_name: "Create Packages", description: "Create new packages" },
  { module: "packages", action: "read", display_name: "View Packages", description: "View package information" },
  { module: "packages", action: "update", display_name: "Update Packages", description: "Update package information" },
  { module: "packages", action: "delete", display_name: "Delete Packages", description: "Delete packages" },
  { module: "packages", action: "manage", display_name: "Manage Packages", description: "Full package management" },
  
  // Location permissions
  { module: "locations", action: "create", display_name: "Create Locations", description: "Create new locations" },
  { module: "locations", action: "read", display_name: "View Locations", description: "View location information" },
  { module: "locations", action: "update", display_name: "Update Locations", description: "Update location information" },
  { module: "locations", action: "delete", display_name: "Delete Locations", description: "Delete locations" },
  { module: "locations", action: "manage", display_name: "Manage Locations", description: "Full location management" },
  
  // Equipment permissions
  { module: "equipment", action: "create", display_name: "Create Equipment", description: "Create new equipment" },
  { module: "equipment", action: "read", display_name: "View Equipment", description: "View equipment information" },
  { module: "equipment", action: "update", display_name: "Update Equipment", description: "Update equipment information" },
  { module: "equipment", action: "delete", display_name: "Delete Equipment", description: "Delete equipment" },
  { module: "equipment", action: "manage", display_name: "Manage Equipment", description: "Full equipment management" },

  // Facility permissions
  { module: "facilities", action: "create", display_name: "Create Facilities", description: "Create new facilities" },
  { module: "facilities", action: "read", display_name: "View Facilities", description: "View facility information" },
  { module: "facilities", action: "update", display_name: "Update Facilities", description: "Update facility information" },
  { module: "facilities", action: "delete", display_name: "Delete Facilities", description: "Delete facilities" },
  { module: "facilities", action: "manage", display_name: "Manage Facilities", description: "Full facility management" },
  
  // Booking permissions
  { module: "bookings", action: "create", display_name: "Create Bookings", description: "Create new bookings" },
  { module: "bookings", action: "read", display_name: "View Bookings", description: "View booking information" },
  { module: "bookings", action: "update", display_name: "Update Bookings", description: "Update booking information" },
  { module: "bookings", action: "delete", display_name: "Delete Bookings", description: "Delete bookings" },
  { module: "bookings", action: "manage", display_name: "Manage Bookings", description: "Full booking management" },
  
  // Report permissions
  { module: "reports", action: "read", display_name: "View Reports", description: "View reports and analytics" },
  { module: "reports", action: "export", display_name: "Export Reports", description: "Export reports and data" },
  
  // Admin permissions
  { module: "admin", action: "access", display_name: "Admin Access", description: "Access admin dashboard" },
];

// Role-Permission mappings
const rolePermissionMappings = {
  super_admin: [
    "system.manage",
    "tenant.manage", "tenant.read",
    "users.manage", "roles.create", "roles.read", "roles.update", "roles.delete", "roles.assign", "roles.revoke",
    "permissions.create", "permissions.read", "permissions.update", "permissions.delete", "permissions.manage",
    "classes.manage", "class-schedules.manage", "customers.manage", "packages.manage", "locations.manage", "equipment.manage", "facilities.manage",
    "bookings.manage", "reports.read", "reports.export", "admin.access"
  ],
  tenant_admin: [
    "tenant.read",
    "users.create", "users.read", "users.update", "users.delete",
    "roles.read", "roles.assign", "roles.revoke",
    "permissions.read", "permissions.create", "permissions.update", "permissions.delete",
    "classes.manage", "class-schedules.manage", "customers.manage", "packages.manage", "locations.manage", "equipment.manage", "facilities.manage",
    "bookings.manage", "reports.read", "reports.export", "admin.access"
  ],
  instructor: [
    "classes.read", "classes.update",
    "class-schedules.read", "class-schedules.update",
    "customers.read", "customers.update",
    "bookings.read", "bookings.create", "bookings.update",
    "packages.read", "locations.read", "equipment.read"
  ],
  customer: [
    "bookings.read", "bookings.create",
    "classes.read", "class-schedules.read", "packages.read", "locations.read"
  ],
};

/**
 * Main seeder function
 */
export async function seedRBAC() {
  try {
    console.log("🌱 Starting RBAC seeding...");

    // 1. Seed system permissions
    console.log("📝 Seeding permissions...");
    const permissionIds: Record<string, string> = {};

    for (const permData of systemPermissions) {
      // Check if permission already exists
      const existingPermission = await db
        .select()
        .from(permissions)
        .where(and(
          eq(permissions.module, permData.module),
          eq(permissions.action, permData.action)
        ))
        .limit(1);

      let permissionId: string;

      if (existingPermission.length > 0) {
        // Use existing permission ID
        permissionId = existingPermission[0].id;
      } else {
        // Create new permission
        permissionId = createId();
        await db.insert(permissions).values({
          id: permissionId,
          module: permData.module,
          action: permData.action,
          display_name: permData.display_name,
          description: permData.description,
          is_system_permission: true,
        });
      }

      permissionIds[`${permData.module}.${permData.action}`] = permissionId;
    }

    // 2. Seed system roles
    console.log("👥 Seeding roles...");
    const roleIds: Record<string, string> = {};
    
    for (const roleData of systemRoles) {
      roleIds[roleData.name] = roleData.id;
      
      await db.insert(roles).values(roleData).onConflictDoNothing();
    }

    // 3. Seed role-permission mappings
    console.log("🔗 Seeding role-permission mappings...");

    for (const [roleName, permissionKeys] of Object.entries(rolePermissionMappings)) {
      const roleId = roleIds[roleName];

      if (!roleId) {
        console.warn(`⚠️ Role not found: ${roleName}`);
        continue;
      }

      // Clear existing role-permission mappings for this role
      await db.delete(role_permissions).where(eq(role_permissions.roleId, roleId));

      // Add new mappings
      for (const permissionKey of permissionKeys) {
        const permissionId = permissionIds[permissionKey];

        if (!permissionId) {
          console.warn(`⚠️ Permission not found: ${permissionKey}`);
          continue;
        }

        await db.insert(role_permissions).values({
          id: createId(),
          roleId,
          permissionId,
        });
      }
    }

    console.log("✅ RBAC seeding completed successfully!");
    
    // Print summary
    console.log("\n📊 Summary:");
    console.log(`- ${systemRoles.length} system roles created`);
    console.log(`- ${systemPermissions.length} permissions created`);
    console.log(`- Role-permission mappings established`);
    
    console.log("\n🎯 Default Roles:");
    systemRoles.forEach(role => {
      console.log(`- ${role.display_name} (${role.name})`);
    });

  } catch (error) {
    console.error("❌ RBAC seeding failed:", error);
    throw error;
  }
}

/**
 * Assign super admin role to first user
 */
export async function assignSuperAdminToFirstUser() {
  try {
    console.log("👑 Assigning super admin role to first user...");

    // Get first user
    const firstUser = await db.select().from(users).limit(1);
    
    if (firstUser.length === 0) {
      console.log("⚠️ No users found, skipping super admin assignment");
      return;
    }

    // Get super admin role
    const superAdminRole = await db
      .select()
      .from(roles)
      .where(eq(roles.name, "super_admin"))
      .limit(1);

    if (superAdminRole.length === 0) {
      console.log("⚠️ Super admin role not found");
      return;
    }

    // Check if assignment already exists
    const existingAssignment = await db
      .select()
      .from(user_roles)
      .where(eq(user_roles.userId, firstUser[0].id))
      .limit(1);

    if (existingAssignment.length > 0) {
      console.log("ℹ️ User already has role assignment");
      return;
    }

    // Assign super admin role
    await db.insert(user_roles).values({
      id: createId(),
      userId: firstUser[0].id,
      roleId: superAdminRole[0].id,
      tenantId: null, // Super admin tidak terikat tenant
      is_active: true,
    });

    console.log(`✅ Super admin role assigned to user: ${firstUser[0].email}`);

  } catch (error) {
    console.error("❌ Super admin assignment failed:", error);
    throw error;
  }
}

// Run seeder jika dipanggil langsung
if (require.main === module) {
  seedRBAC()
    .then(() => assignSuperAdminToFirstUser())
    .then(() => {
      console.log("🎉 All RBAC setup completed!");
      process.exit(0);
    })
    .catch((error) => {
      console.error("💥 RBAC setup failed:", error);
      process.exit(1);
    });
}
