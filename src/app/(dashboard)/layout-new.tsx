import { getCurrentUser } from "@/lib/auth/utils";
import { redirect } from "next/navigation";
import { DashboardLayoutRedesigned } from "@/components/dashboard/layout-redesigned";

export default async function DashboardLayoutNew({
  children,
}: {
  children: React.ReactNode;
}) {
  const user = await getCurrentUser();
  
  if (!user) {
    redirect("/auth/signin");
  }

  return (
    <DashboardLayoutRedesigned user={user}>
      {children}
    </DashboardLayoutRedesigned>
  );
}
