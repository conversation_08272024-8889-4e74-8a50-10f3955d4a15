import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { EquipmentInstanceWithRelations, CreateEquipmentInstanceData, UpdateEquipmentInstanceData } from '@/lib/services/equipment-instances.service';

// Query Keys
export const equipmentInstanceKeys = {
  all: ['equipment-instances'] as const,
  lists: () => [...equipmentInstanceKeys.all, 'list'] as const,
  list: (filters: Record<string, any>) => [...equipmentInstanceKeys.lists(), { filters }] as const,
  details: () => [...equipmentInstanceKeys.all, 'detail'] as const,
  detail: (id: string) => [...equipmentInstanceKeys.details(), id] as const,
  byEquipment: (equipmentId: string) => [...equipmentInstanceKeys.all, 'equipment', equipmentId] as const,
  byLocation: (locationId: string) => [...equipmentInstanceKeys.all, 'location', locationId] as const,
  stats: () => [...equipmentInstanceKeys.all, 'stats'] as const,
};

// API Functions
const equipmentInstanceApi = {
  getAll: async (): Promise<EquipmentInstanceWithRelations[]> => {
    const response = await fetch('/api/equipment-instances');
    if (!response.ok) {
      throw new Error(`Failed to fetch equipment instances: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data || [];
  },

  getByEquipment: async (equipmentId: string): Promise<EquipmentInstanceWithRelations[]> => {
    const response = await fetch(`/api/equipment-instances?equipmentId=${equipmentId}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch equipment instances: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data || [];
  },

  getByLocation: async (locationId: string): Promise<EquipmentInstanceWithRelations[]> => {
    const response = await fetch(`/api/equipment-instances?locationId=${locationId}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch equipment instances: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data || [];
  },

  getById: async (id: string): Promise<EquipmentInstanceWithRelations> => {
    const response = await fetch(`/api/equipment-instances/${id}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch equipment instance: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data;
  },

  create: async (data: CreateEquipmentInstanceData): Promise<EquipmentInstanceWithRelations> => {
    const response = await fetch('/api/equipment-instances', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to create equipment instance');
    }
    const result = await response.json();
    return result.data;
  },

  update: async ({ instanceId, data }: {
    instanceId: string;
    data: UpdateEquipmentInstanceData;
  }): Promise<EquipmentInstanceWithRelations> => {
    const response = await fetch(`/api/equipment-instances/${instanceId}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to update equipment instance');
    }
    const result = await response.json();
    return result.data;
  },

  delete: async (instanceId: string): Promise<EquipmentInstanceWithRelations> => {
    const response = await fetch(`/api/equipment-instances/${instanceId}`, {
      method: 'DELETE',
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to delete equipment instance');
    }
    const result = await response.json();
    return result.data;
  },
};

// Query Hooks
export function useEquipmentInstances() {
  const query = useQuery({
    queryKey: equipmentInstanceKeys.lists(),
    queryFn: async () => {
      const response = await fetch('/api/equipment-instances');
      if (!response.ok) {
        throw new Error(`Failed to fetch equipment instances: ${response.statusText}`);
      }
      const data = await response.json();
      
      if (data.data && Array.isArray(data.data)) {
        return data.data;
      } else if (Array.isArray(data)) {
        return data;
      } else {
        return [];
      }
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: false,
  });

  const instances = Array.isArray(query.data) ? query.data : [];

  return {
    data: instances,
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error?.message || null,
    refetch: query.refetch,
  };
}

export function useEquipmentInstancesByEquipment(equipmentId: string) {
  return useQuery({
    queryKey: equipmentInstanceKeys.byEquipment(equipmentId),
    queryFn: () => equipmentInstanceApi.getByEquipment(equipmentId),
    enabled: !!equipmentId,
    staleTime: 5 * 60 * 1000,
  });
}

export function useEquipmentInstancesByLocation(locationId: string) {
  return useQuery({
    queryKey: equipmentInstanceKeys.byLocation(locationId),
    queryFn: () => equipmentInstanceApi.getByLocation(locationId),
    enabled: !!locationId,
    staleTime: 5 * 60 * 1000,
  });
}

export function useEquipmentInstance(id: string) {
  return useQuery({
    queryKey: equipmentInstanceKeys.detail(id),
    queryFn: () => equipmentInstanceApi.getById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
}

// Mutation Hooks
export function useCreateEquipmentInstance() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: equipmentInstanceApi.create,
    onSuccess: (newInstance) => {
      // Update cache with new instance
      queryClient.setQueryData(equipmentInstanceKeys.detail(newInstance.id), newInstance);

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: equipmentInstanceKeys.lists() });
      if (newInstance.equipmentId) {
        queryClient.invalidateQueries({
          queryKey: equipmentInstanceKeys.byEquipment(newInstance.equipmentId)
        });
      }
      if (newInstance.locationId) {
        queryClient.invalidateQueries({
          queryKey: equipmentInstanceKeys.byLocation(newInstance.locationId)
        });
      }
    },
  });
}

export function useUpdateEquipmentInstance() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: equipmentInstanceApi.update,
    onSuccess: (updatedInstance) => {
      // Update cache with updated instance
      queryClient.setQueryData(equipmentInstanceKeys.detail(updatedInstance.id), updatedInstance);

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: equipmentInstanceKeys.lists() });
      if (updatedInstance.equipmentId) {
        queryClient.invalidateQueries({
          queryKey: equipmentInstanceKeys.byEquipment(updatedInstance.equipmentId)
        });
      }
      if (updatedInstance.locationId) {
        queryClient.invalidateQueries({
          queryKey: equipmentInstanceKeys.byLocation(updatedInstance.locationId)
        });
      }
    },
  });
}

export function useDeleteEquipmentInstance() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: equipmentInstanceApi.delete,
    onMutate: async (instanceId: string) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: equipmentInstanceKeys.all });

      // Get the instance data before deletion for rollback
      const previousInstance = queryClient.getQueryData(equipmentInstanceKeys.detail(instanceId)) as EquipmentInstanceWithRelations;

      // Get all instances to update the list optimistically
      const previousInstances = queryClient.getQueryData(equipmentInstanceKeys.lists()) as EquipmentInstanceWithRelations[] | undefined;

      // Optimistically remove from all instances list
      if (previousInstances) {
        const updatedInstances = previousInstances.filter(instance => instance.id !== instanceId);
        queryClient.setQueryData(equipmentInstanceKeys.lists(), updatedInstances);
      }

      // Remove from detail cache
      queryClient.removeQueries({ queryKey: equipmentInstanceKeys.detail(instanceId) });

      return { previousInstance, previousInstances };
    },
    onError: (error, instanceId, context) => {
      // Restore the previous state if deletion failed
      if (context?.previousInstances) {
        queryClient.setQueryData(equipmentInstanceKeys.lists(), context.previousInstances);
      }
      if (context?.previousInstance) {
        queryClient.setQueryData(equipmentInstanceKeys.detail(instanceId), context.previousInstance);
      }
    },
    onSuccess: (deletedInstance) => {
      // Invalidate and refetch related queries to ensure consistency
      queryClient.invalidateQueries({ queryKey: equipmentInstanceKeys.lists() });

      if (deletedInstance.equipmentId) {
        queryClient.invalidateQueries({
          queryKey: equipmentInstanceKeys.byEquipment(deletedInstance.equipmentId)
        });
      }
      if (deletedInstance.locationId) {
        queryClient.invalidateQueries({
          queryKey: equipmentInstanceKeys.byLocation(deletedInstance.locationId)
        });
      }
    },
  });
}
