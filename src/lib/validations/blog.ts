import { z } from "zod";

// Blog Category Schemas
export const createBlogCategorySchema = z.object({
  tenantId: z.number().int().positive("Tenant ID harus valid"),
  name: z.string()
    .min(1, "Nama kategori wajib diisi")
    .max(255, "Nama kategori terlalu panjang"),
  slug: z.string()
    .min(1, "Slug kategori wajib diisi")
    .max(255, "Slug kategori terlalu panjang")
    .regex(/^[a-z0-9-]+$/, "Slug hanya boleh huruf kecil, angka, dan dash")
    .transform(val => val.toLowerCase()),
  description: z.string()
    .max(1000, "Deskripsi terlalu panjang")
    .optional(),
  color: z.string()
    .regex(/^#[0-9A-Fa-f]{6}$/, "Warna harus dalam format hex (#RRGGBB)")
    .optional(),
  is_active: z.boolean().default(true),
});

export const updateBlogCategorySchema = createBlogCategorySchema
  .omit({ tenantId: true })
  .partial();

// Blog Post Schemas - Base schema without .refine() for .omit() to work
const baseBlogPostSchema = z.object({
  tenantId: z.number().int().positive("Tenant ID harus valid"),
  title: z.string()
    .min(1, "Judul post wajib diisi")
    .max(500, "Judul post terlalu panjang"),
  slug: z.string()
    .optional()
    .refine((val) => !val || val === "" || (val.length >= 1 && val.length <= 500 && /^[a-z0-9-]+$/.test(val.toLowerCase())), {
      message: "Slug harus berisi 1-500 karakter dengan huruf kecil, angka, dan dash saja"
    })
    .transform(val => val ? val.toLowerCase() : val),
  content: z.string()
    .min(1, "Konten post wajib diisi"),
  excerpt: z.string()
    .max(1000, "Excerpt terlalu panjang")
    .optional(),
  featured_image: z.string()
    .optional()
    .refine((val) => !val || val === "" || z.string().url().safeParse(val).success, {
      message: "URL gambar tidak valid"
    }),
  category_id: z.string()
    .optional(),
  tags: z.array(z.string().min(1).max(50))
    .max(10, "Maksimal 10 tags")
    .default([]),
  status: z.enum(["draft", "published"], {
    errorMap: () => ({ message: "Status harus draft atau published" })
  }).default("draft"),
  is_featured: z.boolean().default(false),
  published_at: z.date().optional(),
  author_id: z.string().min(1, "Author ID wajib diisi"),
  seo_title: z.string()
    .max(255, "SEO title terlalu panjang")
    .optional(),
  seo_description: z.string()
    .max(500, "SEO description terlalu panjang")
    .optional(),
});

// Create schema with refinement
export const createBlogPostSchema = baseBlogPostSchema.refine((data) => {
  // If status is published, published_at should be set
  if (data.status === "published" && !data.published_at) {
    return true; // Will be set automatically in service
  }
  return true;
}, {
  message: "Published posts harus memiliki tanggal publikasi",
  path: ["published_at"]
});

// Update schema - use base schema for .omit() then add refinement
export const updateBlogPostSchema = baseBlogPostSchema
  .omit({ tenantId: true, author_id: true })
  .partial()
  .refine((data) => {
    // If status is published, published_at should be set
    if (data.status === "published" && !data.published_at) {
      return true; // Will be set automatically in service
    }
    return true;
  }, {
    message: "Published posts harus memiliki tanggal publikasi",
    path: ["published_at"]
  });

// Query/Filter Schemas
export const blogPostFiltersSchema = z.object({
  search: z.string().optional(),
  category_id: z.string().optional(),
  status: z.enum(["draft", "published"]).optional(),
  is_featured: z.boolean().optional(),
  author_id: z.string().optional(),
  tags: z.array(z.string()).optional(),
  limit: z.number().int().positive().max(100).default(20),
  offset: z.number().int().min(0).default(0),
});

export const blogCategoryFiltersSchema = z.object({
  search: z.string().optional(),
  is_active: z.boolean().optional(),
});

// Public API Schemas (for frontend without auth)
export const publicBlogPostFiltersSchema = z.object({
  search: z.string().optional(),
  category_id: z.string().optional(),
  tags: z.array(z.string()).optional(),
  limit: z.number().int().positive().max(50).default(20),
  offset: z.number().int().min(0).default(0),
});

// Slug validation schema
export const slugSchema = z.object({
  slug: z.string()
    .min(1, "Slug wajib diisi")
    .max(500, "Slug terlalu panjang")
    .regex(/^[a-z0-9-]+$/, "Slug hanya boleh huruf kecil, angka, dan dash"),
  tenantId: z.number().int().positive(),
  excludeId: z.string().optional(),
});

// Image upload schema
export const imageUploadSchema = z.object({
  file: z.instanceof(File, { message: "File harus berupa gambar" }),
  tenantId: z.number().int().positive(),
}).refine((data) => {
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
  return allowedTypes.includes(data.file.type);
}, {
  message: "File harus berupa gambar (JPEG, PNG, atau WebP)",
  path: ["file"]
}).refine((data) => {
  const maxSize = 5 * 1024 * 1024; // 5MB
  return data.file.size <= maxSize;
}, {
  message: "Ukuran file maksimal 5MB",
  path: ["file"]
});

// SEO validation helpers
export const seoValidation = {
  title: (title: string) => {
    const length = title.length;
    if (length < 30) return { status: 'warning', message: 'Judul SEO sebaiknya minimal 30 karakter' };
    if (length > 60) return { status: 'warning', message: 'Judul SEO sebaiknya maksimal 60 karakter' };
    return { status: 'good', message: 'Panjang judul SEO sudah optimal' };
  },
  
  description: (description: string) => {
    const length = description.length;
    if (length < 120) return { status: 'warning', message: 'Deskripsi SEO sebaiknya minimal 120 karakter' };
    if (length > 160) return { status: 'warning', message: 'Deskripsi SEO sebaiknya maksimal 160 karakter' };
    return { status: 'good', message: 'Panjang deskripsi SEO sudah optimal' };
  },
  
  slug: (slug: string) => {
    if (slug.length > 75) return { status: 'warning', message: 'Slug sebaiknya tidak terlalu panjang' };
    if (!/^[a-z0-9-]+$/.test(slug)) return { status: 'error', message: 'Slug hanya boleh huruf kecil, angka, dan dash' };
    return { status: 'good', message: 'Format slug sudah benar' };
  }
};

// Content validation helpers
export const contentValidation = {
  readingTime: (content: string) => {
    const wordsPerMinute = 200;
    const words = content.trim().split(/\s+/).length;
    const minutes = Math.ceil(words / wordsPerMinute);
    return minutes;
  },
  
  wordCount: (content: string) => {
    return content.trim().split(/\s+/).length;
  },
  
  hasImages: (content: string) => {
    return /<img[^>]+>/i.test(content);
  },
  
  hasHeadings: (content: string) => {
    return /<h[1-6][^>]*>/i.test(content);
  }
};

// Type exports
export type CreateBlogCategoryData = z.infer<typeof createBlogCategorySchema>;
export type UpdateBlogCategoryData = z.infer<typeof updateBlogCategorySchema>;
export type CreateBlogPostData = z.infer<typeof createBlogPostSchema>;
export type UpdateBlogPostData = z.infer<typeof updateBlogPostSchema>;
export type BlogPostFilters = z.infer<typeof blogPostFiltersSchema>;
export type BlogCategoryFilters = z.infer<typeof blogCategoryFiltersSchema>;
export type PublicBlogPostFilters = z.infer<typeof publicBlogPostFiltersSchema>;
export type SlugValidation = z.infer<typeof slugSchema>;
export type ImageUpload = z.infer<typeof imageUploadSchema>;
