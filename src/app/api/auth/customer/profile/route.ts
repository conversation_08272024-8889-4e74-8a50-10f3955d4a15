import { NextRequest } from "next/server";
import { z } from "zod";
import { 
  withCustomerAuth,
  createAuthenticatedResponse, 
  createErrorResponse,
  CustomerAuthContext 
} from "@/lib/middleware/customer-auth.middleware";
import { customers, db } from "@/lib/db";
import { eq, and } from "drizzle-orm";
import { rateLimitConfig } from "@/lib/security/rate-limit.config";

/**
 * Customer Profile Management
 * 
 * GET /api/auth/customer/profile - Get customer profile
 * PUT /api/auth/customer/profile - Update customer profile
 * 
 * Protected endpoint requiring valid JWT token
 */

const updateProfileSchema = z.object({
  firstName: z.string().min(1).max(255).optional(),
  lastName: z.string().min(1).max(255).optional(),
  displayName: z.string().min(1).max(255).optional(),
  dateOfBirth: z.string().optional(),
  gender: z.string().max(50).optional(),
  mobileCountryCode: z.string().max(10).optional(),
  mobileNumber: z.string().max(30).optional(),
  preferences: z.object({
    language: z.string().optional(),
    timezone: z.string().optional(),
    notifications: z.object({
      email: z.boolean().optional(),
      sms: z.boolean().optional(),
      push: z.boolean().optional(),
    }).optional(),
    privacy: z.object({
      shareDataWithPartners: z.boolean().optional(),
      allowAnalytics: z.boolean().optional(),
    }).optional(),
  }).optional(),
  marketingEmails: z.boolean().optional(),
});

// GET - Retrieve customer profile
async function getProfileHandler(
  request: NextRequest,
  context: CustomerAuthContext
) {
  try {
    // Get full customer profile from database
    const [customer] = await db
      .select({
        id: customers.id,
        email: customers.email,
        firstName: customers.firstName,
        lastName: customers.lastName,
        displayName: customers.displayName,
        image: customers.image,
        dateOfBirth: customers.dateOfBirth,
        gender: customers.gender,
        mobileCountryCode: customers.mobileCountryCode,
        mobileNumber: customers.mobileNumber,
        membershipType: customers.membershipType,
        membershipExpiry: customers.membershipExpiry,
        isEmailVerified: customers.isEmailVerified,
        preferences: customers.preferences,
        marketingEmails: customers.marketingEmails,
        termsAcceptedAt: customers.termsAcceptedAt,
        termsVersion: customers.termsVersion,
        privacyAcceptedAt: customers.privacyAcceptedAt,
        privacyVersion: customers.privacyVersion,
        lastLoginAt: customers.lastLoginAt,
        createdAt: customers.createdAt,
      })
      .from(customers)
      .where(and(
        eq(customers.id, context.customer.id),
        eq(customers.tenantId, context.customer.tenantId),
        eq(customers.isActive, true)
      ))
      .limit(1);

    if (!customer) {
      return createErrorResponse(
        "Customer not found",
        "CUSTOMER_NOT_FOUND",
        404
      );
    }

    const responseData = {
      success: true,
      customer: {
        ...customer,
        // Don't expose sensitive fields
        tenantId: context.customer.tenantId,
      },
    };

    return createAuthenticatedResponse(responseData, 200);

  } catch (error) {
    console.error("Get profile error:", error);
    return createErrorResponse(
      "Failed to retrieve profile",
      "PROFILE_ERROR",
      500
    );
  }
}

// PUT - Update customer profile
async function updateProfileHandler(
  request: NextRequest,
  context: CustomerAuthContext
) {
  try {
    // Parse and validate request body
    const body = await request.json();
    const validatedData = updateProfileSchema.parse(body);

    // Prepare update data
    const updateData: any = {
      updatedAt: new Date(),
    };

    // Map validated fields to database fields
    if (validatedData.firstName !== undefined) {
      updateData.firstName = validatedData.firstName;
    }
    if (validatedData.lastName !== undefined) {
      updateData.lastName = validatedData.lastName;
    }
    if (validatedData.displayName !== undefined) {
      updateData.displayName = validatedData.displayName;
    }
    if (validatedData.dateOfBirth !== undefined) {
      updateData.dateOfBirth = validatedData.dateOfBirth ? new Date(validatedData.dateOfBirth) : null;
    }
    if (validatedData.gender !== undefined) {
      updateData.gender = validatedData.gender;
    }
    if (validatedData.mobileCountryCode !== undefined) {
      updateData.mobileCountryCode = validatedData.mobileCountryCode;
    }
    if (validatedData.mobileNumber !== undefined) {
      updateData.mobileNumber = validatedData.mobileNumber;
    }
    if (validatedData.preferences !== undefined) {
      updateData.preferences = validatedData.preferences;
    }
    if (validatedData.marketingEmails !== undefined) {
      updateData.marketingEmails = validatedData.marketingEmails;
    }

    // Update customer profile
    const [updatedCustomer] = await db
      .update(customers)
      .set(updateData)
      .where(and(
        eq(customers.id, context.customer.id),
        eq(customers.tenantId, context.customer.tenantId)
      ))
      .returning({
        id: customers.id,
        email: customers.email,
        firstName: customers.firstName,
        lastName: customers.lastName,
        displayName: customers.displayName,
        image: customers.image,
        dateOfBirth: customers.dateOfBirth,
        gender: customers.gender,
        mobileCountryCode: customers.mobileCountryCode,
        mobileNumber: customers.mobileNumber,
        membershipType: customers.membershipType,
        isEmailVerified: customers.isEmailVerified,
        preferences: customers.preferences,
        marketingEmails: customers.marketingEmails,
        updatedAt: customers.updatedAt,
      });

    if (!updatedCustomer) {
      return createErrorResponse(
        "Failed to update profile",
        "UPDATE_ERROR",
        500
      );
    }

    const responseData = {
      success: true,
      message: "Profile updated successfully",
      customer: {
        ...updatedCustomer,
        tenantId: context.customer.tenantId,
      },
    };

    return createAuthenticatedResponse(responseData, 200);

  } catch (error) {
    console.error("Update profile error:", error);

    if (error instanceof z.ZodError) {
      return createErrorResponse(
        "Invalid request data",
        "INVALID_INPUT",
        400
      );
    }

    return createErrorResponse(
      "Failed to update profile",
      "UPDATE_ERROR",
      500
    );
  }
}

export const GET = withCustomerAuth(getProfileHandler, {
  requireAuth: true,
  rateLimit: rateLimitConfig.api.profile,
});

export const PUT = withCustomerAuth(updateProfileHandler, {
  requireAuth: true,
  rateLimit: rateLimitConfig.api.profile,
});
