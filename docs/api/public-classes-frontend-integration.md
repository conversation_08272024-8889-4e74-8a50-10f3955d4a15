# 📚 Dokumentasi Frontend Integration untuk Public Classes API

Halo teman-teman! Kali ini kita akan belajar cara mengintegrasikan endpoint API `/api/public/v1/classes` di frontend website kita. Dokumentasi ini dibuat dengan gaya yang mudah dipahami, jadi jangan khawatir kalau masih pemula ya! 😊

## 🎯 Overview

Endpoint `/api/public/v1/classes` adalah API publik yang memungkinkan kita mengambil data classes dari backend. **Mengapa kita butuh API publik?** Karena ini memungkinkan aplikasi eksternal atau frontend terpisah untuk mengakses data classes dengan aman menggunakan API key authentication.

## 📋 1. Struktur Response API

### Response Format Standar

Semua response dari endpoint ini mengikuti format standar yang konsisten:

```typescript
// Response untuk List Classes
{
  "success": true,
  "data": {
    "classes": [
      {
        "id": "cls_abc123",
        "name": "Yoga Pemula",
        "description": "Kelas yoga untuk pemula yang ingin belajar dasar-dasar yoga",
        "category": "Yoga", // ✅ Nama kategori (user-friendly)
        "categoryId": "cat_yoga123", // ✅ ID kategori untuk reference
        "startDate": "2024-01-15T09:00:00.000Z",
        "endDate": "2024-01-15T10:30:00.000Z",
        "isActive": true,
        "createdAt": "2024-01-01T00:00:00.000Z",
        "updatedAt": "2024-01-01T00:00:00.000Z"
      }
    ]
  },
  "meta": {
    "total": 25,
    "page": 1,
    "limit": 20,
    "pageCount": 2
  }
}

// Response untuk Single Class (dengan parameter id)
{
  "success": true,
  "data": {
    "id": "cls_abc123",
    "name": "Yoga Pemula",
    "description": "Kelas yoga untuk pemula",
    "category": "Yoga", // ✅ Nama kategori (user-friendly)
    "categoryId": "cat_yoga123", // ✅ ID kategori untuk reference
    "startDate": "2024-01-15T09:00:00.000Z",
    "endDate": "2024-01-15T10:30:00.000Z",
    "isActive": true,
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}

// Response Error
{
  "success": false,
  "error": "Unauthorized"
}
```

**Mengapa struktur ini bagus?**
- Konsisten dengan semua endpoint lain di aplikasi
- Field `success` memudahkan pengecekan status
- Field `meta` memberikan informasi pagination yang lengkap
- Error handling yang jelas dan mudah dipahami

## 🔑 2. Authentication & Headers

Endpoint ini menggunakan API key authentication. **Mengapa API key?** Karena lebih aman untuk akses publik dan mudah di-manage.

```typescript
const API_KEY = 'pk_9b4d8a5f3e9ec298be21ac0589d0346d0289f717d9ca3841093498ef4530c64b';

const headers = {
  'Content-Type': 'application/json',
  'x-api-key': API_KEY
};
```

## 📝 3. Query Parameters

Endpoint mendukung berbagai query parameters untuk filtering dan pagination:

| Parameter | Type | Required | Default | Deskripsi |
|-----------|------|----------|---------|-----------|
| `tenantId` | string | No | - | Filter berdasarkan tenant (multi-tenancy) |
| `limit` | number | No | 20 | Jumlah data per halaman |
| `page` | number | No | 1 | Nomor halaman |
| `id` | string | No | - | Ambil data class tertentu berdasarkan ID |

**Contoh penggunaan:**
```
GET /api/public/v1/classes?tenantId=1&limit=10&page=2
GET /api/public/v1/classes?id=cls_abc123
```

## 🏗️ 4. TypeScript Types

Pertama, kita definisikan types yang sesuai dengan response API:

```typescript
// src/types/public-api.ts
export interface PublicClassDTO {
  id: string;
  name: string;
  description: string;
  category: string;    // ✅ Nama kategori (user-friendly)
  categoryId: string;  // ✅ ID kategori untuk reference
  startDate: string;   // ISO string
  endDate: string;     // ISO string
  isActive: boolean;
  createdAt: string;   // ISO string
  updatedAt: string;   // ISO string
}

export interface PublicClassesResponse {
  success: boolean;
  data: {
    classes: PublicClassDTO[];
  };
  meta: {
    total: number;
    page: number;
    limit: number;
    pageCount: number;
  };
}

export interface PublicClassResponse {
  success: boolean;
  data: PublicClassDTO;
}

export interface PublicApiError {
  success: false;
  error: string;
}

// Query parameters
export interface PublicClassesParams {
  tenantId?: string;
  limit?: number;
  page?: number;
  id?: string;
}
```

## 🎣 5. Custom Hooks Implementation

Sekarang kita buat custom hooks mengikuti pola `createEntityHooks` yang sudah ada di project:

```typescript
// src/lib/hooks/queries/use-public-classes-queries.ts
import { useQuery, UseQueryOptions } from '@tanstack/react-query';
import { 
  PublicClassDTO, 
  PublicClassesResponse, 
  PublicClassResponse, 
  PublicClassesParams 
} from '@/types/public-api';

// API Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';
const API_KEY = process.env.NEXT_PUBLIC_CLASSES_API_KEY || 'pk_9b4d8a5f3e9ec298be21ac0589d0346d0289f717d9ca3841093498ef4530c64b';

// Query Keys Factory
export const publicClassesKeys = {
  all: ['public-classes'] as const,
  lists: () => [...publicClassesKeys.all, 'list'] as const,
  list: (params: PublicClassesParams) => [...publicClassesKeys.lists(), params] as const,
  details: () => [...publicClassesKeys.all, 'detail'] as const,
  detail: (id: string) => [...publicClassesKeys.details(), id] as const,
};

// API Functions
const publicClassesApi = {
  async getClasses(params: PublicClassesParams = {}): Promise<PublicClassDTO[]> {
    const searchParams = new URLSearchParams();
    
    if (params.tenantId) searchParams.append('tenantId', params.tenantId);
    if (params.limit) searchParams.append('limit', params.limit.toString());
    if (params.page) searchParams.append('page', params.page.toString());

    const response = await fetch(
      `${API_BASE_URL}/api/public/v1/classes?${searchParams.toString()}`,
      {
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': API_KEY,
        },
      }
    );

    if (!response.ok) {
      throw new Error(`Failed to fetch classes: ${response.statusText}`);
    }

    const result: PublicClassesResponse = await response.json();
    
    if (!result.success) {
      throw new Error(result.error || 'Failed to fetch classes');
    }

    return result.data.classes;
  },

  async getClassById(id: string): Promise<PublicClassDTO> {
    const response = await fetch(
      `${API_BASE_URL}/api/public/v1/classes?id=${id}`,
      {
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': API_KEY,
        },
      }
    );

    if (!response.ok) {
      throw new Error(`Failed to fetch class: ${response.statusText}`);
    }

    const result: PublicClassResponse = await response.json();
    
    if (!result.success) {
      throw new Error(result.error || 'Class not found');
    }

    return result.data;
  },
};

// Custom Hooks
export function usePublicClasses(
  params: PublicClassesParams = {},
  options?: Omit<UseQueryOptions<PublicClassDTO[]>, 'queryKey' | 'queryFn'>
) {
  return useQuery({
    queryKey: publicClassesKeys.list(params),
    queryFn: () => publicClassesApi.getClasses(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000,   // 10 minutes
    retry: (failureCount, error: any) => {
      // Jangan retry untuk 4xx errors
      if (error?.status >= 400 && error?.status < 500) {
        return false;
      }
      return failureCount < 3;
    },
    refetchOnWindowFocus: false,
    ...options,
  });
}

export function usePublicClass(
  id: string,
  options?: Omit<UseQueryOptions<PublicClassDTO>, 'queryKey' | 'queryFn'>
) {
  return useQuery({
    queryKey: publicClassesKeys.detail(id),
    queryFn: () => publicClassesApi.getClassById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
    ...options,
  });
}
```

**Mengapa pola ini bagus?**
- Mengikuti factory pattern yang sudah ada di project
- Query keys yang terstruktur untuk cache management
- Error handling yang robust
- TypeScript support yang lengkap
- Konfigurasi caching yang optimal

## 🚨 6. Error Handling

Error handling yang comprehensive sangat penting untuk UX yang baik:

```typescript
// src/lib/hooks/queries/use-public-classes-queries.ts (lanjutan)

// Error Types
export interface PublicClassesError {
  message: string;
  status?: number;
  code?: string;
}

// Enhanced hooks with better error handling
export function usePublicClassesWithErrorHandling(params: PublicClassesParams = {}) {
  const query = usePublicClasses(params);
  
  return {
    ...query,
    // Enhanced error information
    errorMessage: query.error?.message || null,
    isUnauthorized: query.error?.message?.includes('Unauthorized') || false,
    isNotFound: query.error?.message?.includes('not found') || false,
    isNetworkError: query.error?.message?.includes('fetch') || false,
    
    // Helper methods
    retry: query.refetch,
    hasData: !!query.data && query.data.length > 0,
  };
}
```

**Tips Troubleshooting:**
1. **401 Unauthorized**: Periksa API key di environment variables
2. **404 Not Found**: Pastikan endpoint URL benar
3. **Network Error**: Periksa koneksi internet dan CORS configuration
4. **Empty Data**: Periksa parameter tenantId dan pastikan ada data di database

## 🎨 7. Contoh Penggunaan di Komponen React

Mari kita lihat cara menggunakan hooks ini di komponen React:

```tsx
// src/components/public/ClassesList.tsx
'use client';

import React from 'react';
import { usePublicClassesWithErrorHandling } from '@/lib/hooks/queries/use-public-classes-queries';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Loader2, AlertCircle, RefreshCw } from 'lucide-react';

interface ClassesListProps {
  tenantId?: string;
  limit?: number;
}

export function ClassesList({ tenantId = '1', limit = 10 }: ClassesListProps) {
  const {
    data: classes,
    isLoading,
    isError,
    errorMessage,
    isUnauthorized,
    isNetworkError,
    hasData,
    retry,
  } = usePublicClassesWithErrorHandling({ tenantId, limit });

  // Loading State
  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Memuat data classes...</span>
      </div>
    );
  }

  // Error States
  if (isError) {
    return (
      <Card className="border-red-200 bg-red-50">
        <CardContent className="p-6">
          <div className="flex items-center space-x-2 text-red-600">
            <AlertCircle className="h-5 w-5" />
            <span className="font-medium">
              {isUnauthorized 
                ? 'API key tidak valid' 
                : isNetworkError 
                ? 'Koneksi bermasalah' 
                : 'Terjadi kesalahan'}
            </span>
          </div>
          <p className="text-sm text-red-500 mt-2">{errorMessage}</p>
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => retry()}
            className="mt-4"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Coba Lagi
          </Button>
        </CardContent>
      </Card>
    );
  }

  // Empty State
  if (!hasData) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <p className="text-gray-500">Belum ada classes tersedia</p>
        </CardContent>
      </Card>
    );
  }

  // Success State
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {classes?.map((classItem) => (
        <Card key={classItem.id} className="hover:shadow-lg transition-shadow">
          <CardHeader>
            <CardTitle className="text-lg">{classItem.name}</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 text-sm mb-4">
              {classItem.description}
            </p>
            <div className="space-y-2 text-sm">
              <p><strong>Kategori:</strong> {classItem.category}</p>
              <p><strong>Mulai:</strong> {new Date(classItem.startDate).toLocaleDateString('id-ID')}</p>
              <p><strong>Selesai:</strong> {new Date(classItem.endDate).toLocaleDateString('id-ID')}</p>
              <p>
                <span className={`px-2 py-1 rounded-full text-xs ${
                  classItem.isActive 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-gray-100 text-gray-800'
                }`}>
                  {classItem.isActive ? 'Aktif' : 'Tidak Aktif'}
                </span>
              </p>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
```

## 🔄 8. Caching Strategy

Implementasi caching menggunakan TanStack Query yang optimal:

```typescript
// src/lib/providers/query-provider.tsx (konfigurasi global)
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000,      // 5 menit - data dianggap fresh
      gcTime: 10 * 60 * 1000,        // 10 menit - cache cleanup
      retry: (failureCount, error: any) => {
        if (error?.status >= 400 && error?.status < 500) {
          return false; // Jangan retry untuk client errors
        }
        return failureCount < 3;
      },
      refetchOnWindowFocus: false,    // Tidak refetch saat focus window
      refetchOnReconnect: true,       // Refetch saat koneksi kembali
    },
  },
});
```

**Mengapa strategi caching ini bagus?**
- **5 menit staleTime**: Data classes tidak berubah terlalu sering
- **10 menit gcTime**: Memberikan waktu cukup untuk navigasi user
- **No window focus refetch**: Menghindari request yang tidak perlu
- **Reconnect refetch**: Memastikan data terbaru saat koneksi kembali

## 🚀 9. Advanced Usage

### Pagination dengan Infinite Query

```typescript
// src/lib/hooks/queries/use-public-classes-queries.ts (tambahan)
import { useInfiniteQuery } from '@tanstack/react-query';

export function useInfinitePublicClasses(
  params: Omit<PublicClassesParams, 'page'> = {}
) {
  return useInfiniteQuery({
    queryKey: [...publicClassesKeys.lists(), 'infinite', params],
    queryFn: async ({ pageParam = 1 }) => {
      const response = await publicClassesApi.getClasses({
        ...params,
        page: pageParam,
      });
      return {
        classes: response,
        nextPage: response.length === (params.limit || 20) ? pageParam + 1 : undefined,
      };
    },
    getNextPageParam: (lastPage) => lastPage.nextPage,
    staleTime: 5 * 60 * 1000,
  });
}
```

### Real-time Updates dengan Polling

```typescript
export function usePublicClassesRealtime(params: PublicClassesParams = {}) {
  return usePublicClasses(params, {
    refetchInterval: 30 * 1000, // Poll setiap 30 detik
    refetchIntervalInBackground: false, // Hanya poll saat tab aktif
  });
}
```

## 📚 10. Best Practices & Tips

### Environment Variables
```bash
# .env.local
NEXT_PUBLIC_API_URL=http://localhost:3000
NEXT_PUBLIC_CLASSES_API_KEY=pk_9b4d8a5f3e9ec298be21ac0589d0346d0289f717d9ca3841093498ef4530c64b
```

### Common Pitfalls & Solutions

1. **API Key Exposure**: Jangan commit API key ke repository
2. **CORS Issues**: Pastikan domain frontend sudah dikonfigurasi di CORS
3. **Cache Invalidation**: Gunakan query keys yang konsisten
4. **Error Boundaries**: Wrap komponen dengan error boundary untuk error handling global

## 🎉 Kesimpulan

Dengan implementasi ini, kamu sudah punya:
- ✅ Integration yang robust dengan Public Classes API
- ✅ Error handling yang comprehensive
- ✅ Caching strategy yang optimal
- ✅ TypeScript support yang lengkap
- ✅ Komponen React yang reusable

**Mengapa pola ini recommended?**
- Mengikuti best practices FAANG-level
- Konsisten dengan arsitektur project yang sudah ada
- Mudah di-maintain dan di-extend
- Performance yang optimal dengan caching
- Developer experience yang baik dengan TypeScript

Selamat coding! 🚀

## 🔧 11. Implementasi dengan createEntityHooks Pattern

Untuk konsistensi dengan pola yang sudah ada di project, kita juga bisa menggunakan `createEntityHooks` factory:

```typescript
// src/lib/hooks/queries/use-public-classes-entity-hooks.ts
import { createEntityHooks, BaseApiInterface } from '@/lib/core/base-query-hooks';
import { QueryOptions } from '@/lib/core/base-service';
import { PublicClassDTO } from '@/types/public-api';

// Implementasi BaseApiInterface untuk Public Classes
const publicClassesEntityApi: BaseApiInterface<PublicClassDTO, never, never> = {
  async getAll(options?: QueryOptions): Promise<PublicClassDTO[]> {
    const params = new URLSearchParams();

    if (options?.filters?.tenantId) {
      params.append('tenantId', options.filters.tenantId.toString());
    }
    if (options?.limit) {
      params.append('limit', options.limit.toString());
    }
    if (options?.offset) {
      const page = Math.floor(options.offset / (options.limit || 20)) + 1;
      params.append('page', page.toString());
    }

    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}/api/public/v1/classes?${params.toString()}`,
      {
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': process.env.NEXT_PUBLIC_CLASSES_API_KEY!,
        },
      }
    );

    if (!response.ok) {
      throw new Error(`Failed to fetch classes: ${response.statusText}`);
    }

    const result = await response.json();
    return result.success ? result.data.classes : [];
  },

  async getById(id: string): Promise<PublicClassDTO> {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_API_URL}/api/public/v1/classes?id=${id}`,
      {
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': process.env.NEXT_PUBLIC_CLASSES_API_KEY!,
        },
      }
    );

    if (!response.ok) {
      throw new Error(`Failed to fetch class: ${response.statusText}`);
    }

    const result = await response.json();
    if (!result.success) {
      throw new Error(result.error || 'Class not found');
    }

    return result.data;
  },

  async getByTenant(tenantId: number, options?: QueryOptions): Promise<PublicClassDTO[]> {
    return this.getAll({
      ...options,
      filters: { ...options?.filters, tenantId }
    });
  },

  // Public API tidak support create/update/delete
  async create(): Promise<PublicClassDTO> {
    throw new Error('Create operation not supported in public API');
  },

  async update(): Promise<PublicClassDTO> {
    throw new Error('Update operation not supported in public API');
  },

  async delete(): Promise<void> {
    throw new Error('Delete operation not supported in public API');
  },

  async bulkOperation(): Promise<PublicClassDTO[]> {
    throw new Error('Bulk operations not supported in public API');
  },
};

// Create hooks menggunakan factory pattern
const baseHooks = createEntityHooks('public-classes', publicClassesEntityApi);

// Export hooks dengan nama yang descriptive
export const usePublicClassesEntity = baseHooks.useEntities;
export const usePublicClassEntity = baseHooks.useEntity;
export const usePublicClassesByTenant = baseHooks.useEntitiesByTenant;
export const usePublicClassesSearch = baseHooks.useEntitySearch;

// Custom hook untuk compatibility dengan existing pattern
export function usePublicClassesWithPagination(
  tenantId: number,
  limit: number = 20,
  page: number = 1
) {
  const offset = (page - 1) * limit;

  return usePublicClassesEntity({
    filters: { tenantId },
    limit,
    offset,
  });
}
```

**Mengapa menggunakan createEntityHooks pattern?**
- Konsistensi dengan pola yang sudah ada di project
- Automatic cache invalidation yang sudah teruji
- Query keys yang terstruktur dan konsisten
- Error handling yang sudah di-standardize
- Mudah untuk extend dan maintain

## 🧪 12. Testing & Development

### Unit Testing dengan Jest & React Testing Library

```typescript
// src/lib/hooks/queries/__tests__/use-public-classes-queries.test.ts
import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { usePublicClasses } from '../use-public-classes-queries';

// Mock fetch
global.fetch = jest.fn();

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('usePublicClasses', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should fetch classes successfully', async () => {
    const mockClasses = [
      {
        id: 'cls_123',
        name: 'Test Class',
        description: 'Test Description',
        category: 'Fitness',
        startDate: '2024-01-01T00:00:00.000Z',
        endDate: '2024-01-01T01:00:00.000Z',
        isActive: true,
        createdAt: '2024-01-01T00:00:00.000Z',
        updatedAt: '2024-01-01T00:00:00.000Z',
      },
    ];

    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: true,
      json: async () => ({
        success: true,
        data: { classes: mockClasses },
        meta: { total: 1, page: 1, limit: 20, pageCount: 1 },
      }),
    });

    const { result } = renderHook(
      () => usePublicClasses({ tenantId: '1' }),
      { wrapper: createWrapper() }
    );

    await waitFor(() => {
      expect(result.current.isSuccess).toBe(true);
    });

    expect(result.current.data).toEqual(mockClasses);
  });

  it('should handle API errors', async () => {
    (fetch as jest.Mock).mockResolvedValueOnce({
      ok: false,
      statusText: 'Unauthorized',
    });

    const { result } = renderHook(
      () => usePublicClasses({ tenantId: '1' }),
      { wrapper: createWrapper() }
    );

    await waitFor(() => {
      expect(result.current.isError).toBe(true);
    });

    expect(result.current.error?.message).toContain('Unauthorized');
  });
});
```

### Development Tools & Debugging

```typescript
// src/lib/hooks/queries/use-public-classes-queries.ts (tambahan untuk development)

// Development helper untuk debugging
export function usePublicClassesDebug(params: PublicClassesParams = {}) {
  const query = usePublicClasses(params);

  // Log query state changes in development
  React.useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log('Public Classes Query State:', {
        isLoading: query.isLoading,
        isError: query.isError,
        isSuccess: query.isSuccess,
        dataLength: query.data?.length || 0,
        error: query.error?.message,
        queryKey: publicClassesKeys.list(params),
      });
    }
  }, [query.isLoading, query.isError, query.isSuccess, query.data, query.error]);

  return query;
}

// Performance monitoring
export function usePublicClassesWithMetrics(params: PublicClassesParams = {}) {
  const startTime = React.useRef<number>();
  const query = usePublicClasses(params);

  React.useEffect(() => {
    if (query.isLoading && !startTime.current) {
      startTime.current = Date.now();
    }

    if (query.isSuccess && startTime.current) {
      const duration = Date.now() - startTime.current;
      console.log(`Public Classes API call took ${duration}ms`);

      // Send metrics to monitoring service
      if (typeof window !== 'undefined' && window.gtag) {
        window.gtag('event', 'api_call_duration', {
          event_category: 'performance',
          event_label: 'public_classes',
          value: duration,
        });
      }

      startTime.current = undefined;
    }
  }, [query.isLoading, query.isSuccess]);

  return query;
}
```

## 🔒 13. Security Considerations

### API Key Management

```typescript
// src/lib/config/api-config.ts
export const getApiConfig = () => {
  const apiKey = process.env.NEXT_PUBLIC_CLASSES_API_KEY;

  if (!apiKey) {
    throw new Error(
      'NEXT_PUBLIC_CLASSES_API_KEY is not configured. ' +
      'Please add it to your environment variables.'
    );
  }

  if (process.env.NODE_ENV === 'development' && !apiKey.startsWith('pk_')) {
    console.warn(
      'API key should start with "pk_" prefix for better security practices'
    );
  }

  return {
    apiKey,
    baseUrl: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000',
  };
};

// Rate limiting awareness
export const API_RATE_LIMITS = {
  requestsPerHour: 100,
  burstLimit: 10,
  retryAfterMs: 60000, // 1 minute
} as const;
```

### Request Interceptors untuk Security

```typescript
// src/lib/api/interceptors.ts
export const createSecureApiClient = () => {
  const { apiKey, baseUrl } = getApiConfig();

  return {
    async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
      const url = `${baseUrl}${endpoint}`;

      const response = await fetch(url, {
        ...options,
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': apiKey,
          // Security headers
          'X-Requested-With': 'XMLHttpRequest',
          'Cache-Control': 'no-cache',
          ...options.headers,
        },
      });

      // Handle rate limiting
      if (response.status === 429) {
        const retryAfter = response.headers.get('Retry-After');
        throw new Error(
          `Rate limit exceeded. Retry after ${retryAfter || '60'} seconds.`
        );
      }

      if (!response.ok) {
        throw new Error(`API request failed: ${response.statusText}`);
      }

      return response.json();
    },
  };
};
```

## 📊 14. Monitoring & Analytics

### Performance Monitoring

```typescript
// src/lib/hooks/queries/use-public-classes-queries.ts (tambahan monitoring)
import { useQuery } from '@tanstack/react-query';

export function usePublicClassesWithAnalytics(params: PublicClassesParams = {}) {
  return useQuery({
    queryKey: publicClassesKeys.list(params),
    queryFn: async () => {
      const startTime = performance.now();

      try {
        const result = await publicClassesApi.getClasses(params);

        // Track successful API calls
        if (typeof window !== 'undefined' && window.gtag) {
          window.gtag('event', 'api_success', {
            event_category: 'api',
            event_label: 'public_classes',
            custom_map: {
              duration: Math.round(performance.now() - startTime),
              result_count: result.length,
            },
          });
        }

        return result;
      } catch (error) {
        // Track API errors
        if (typeof window !== 'undefined' && window.gtag) {
          window.gtag('event', 'api_error', {
            event_category: 'api',
            event_label: 'public_classes',
            custom_map: {
              error_message: error instanceof Error ? error.message : 'Unknown error',
              duration: Math.round(performance.now() - startTime),
            },
          });
        }

        throw error;
      }
    },
    staleTime: 5 * 60 * 1000,
    gcTime: 10 * 60 * 1000,
  });
}
```

## 🎯 15. Migration Guide

Jika kamu sudah punya implementasi lama dan ingin migrate ke pola baru:

### Step 1: Update Types
```typescript
// Ganti dari types lama
interface OldClassType {
  // old structure
}

// Ke types baru
import { PublicClassDTO } from '@/types/public-api';
```

### Step 2: Update Hooks Usage
```typescript
// Dari:
const { data, loading, error } = useOldClassesHook();

// Ke:
const { data, isLoading, isError, error } = usePublicClasses();
```

### Step 3: Update Error Handling
```typescript
// Dari:
if (error) {
  return <div>Error: {error}</div>;
}

// Ke:
if (isError) {
  return <div>Error: {error?.message}</div>;
}
```

## 🚀 16. Next Steps & Roadmap

### Immediate Improvements
1. **Implement Infinite Scrolling**: Untuk better UX pada list panjang
2. **Add Search Functionality**: Filter berdasarkan nama atau kategori
3. **Implement Optimistic Updates**: Untuk operasi yang akan datang
4. **Add Offline Support**: Dengan service worker dan cache

### Future Enhancements
1. **Real-time Updates**: WebSocket integration untuk live updates
2. **Advanced Filtering**: Multiple filters dan sorting options
3. **Export Functionality**: Download data dalam format CSV/PDF
4. **Bulk Operations**: Select multiple items untuk batch operations

Dengan dokumentasi ini, kamu sudah siap untuk mengimplementasikan Public Classes API dengan confidence! 💪

Happy coding! 🎉
