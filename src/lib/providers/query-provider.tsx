"use client";

import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { useState } from "react";
import { GlobalErrorBoundary, NetworkStatus } from "@/components/global/error-boundary";

export function QueryProvider({ children }: { children: React.ReactNode }) {
  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            // With SSR, we usually want to set some default staleTime
            // above 0 to avoid refetching immediately on the client
            staleTime: 60 * 1000, // 1 minute
            gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
            retry: (failureCount, error: any) => {
              // Don't retry on 4xx errors
              if (error?.status >= 400 && error?.status < 500) {
                return false;
              }
              return failureCount < 3;
            },
            refetchOnWindowFocus: false,
            refetchOnReconnect: true,
          },
          mutations: {
            retry: false,
            onError: (error: any) => {
              console.error("Mutation error:", error);
              // You can add global error handling here
              // For example, show a toast notification
            },
          },
        },
      })
  );

  return (
    <QueryClientProvider client={queryClient}>
      <GlobalErrorBoundary>
        {/* NetworkStatus - only show in production or when explicitly needed */}
        {process.env.NODE_ENV === "production" && <NetworkStatus />}
        {children}
      </GlobalErrorBoundary>
      {/* DevTools will be added later */}
    </QueryClientProvider>
  );
}
