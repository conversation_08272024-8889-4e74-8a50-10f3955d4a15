import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { db } from "@/lib/db";
import { organizations, users, tenants } from "@/lib/db/schema";
import { eq, count, isNotNull } from "drizzle-orm";

// GET /api/organizations/stats - Get organization statistics
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Only admins can view organization stats
    if (session.user.role !== "admin") {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Get total organizations
    const [totalOrganizationsResult] = await db
      .select({ count: count() })
      .from(organizations);

    // Get active organizations (assuming all are active for now)
    const [activeOrganizationsResult] = await db
      .select({ count: count() })
      .from(organizations);

    // Get total members across all organizations
    const [totalMembersResult] = await db
      .select({ count: count() })
      .from(users)
      .where(isNotNull(users.organizationId));

    // Get total tenants across all organizations
    const [totalTenantsResult] = await db
      .select({ count: count() })
      .from(tenants);

    const stats = {
      totalOrganizations: totalOrganizationsResult.count,
      activeOrganizations: activeOrganizationsResult.count,
      totalMembers: totalMembersResult.count,
      totalTenants: totalTenantsResult.count,
    };

    return NextResponse.json(stats);
  } catch (error) {
    console.error("Error fetching organization stats:", error);
    return NextResponse.json(
      { error: "Failed to fetch organization stats" },
      { status: 500 }
    );
  }
}
