/**
 * API Key Authentication Service
 * 
 * FAANG-level implementation of API key authentication with:
 * - Secure key generation and hashing
 * - Rate limiting per API key
 * - Permission-based authorization
 * - Audit logging
 * - Key rotation support
 */

import { createHash, randomBytes } from 'crypto';
import { eq, and, lt, gt } from 'drizzle-orm';
import { db } from '@/lib/db';
import { api_keys, api_key_usage, audit_logs } from '@/lib/db/schema';
import { createId } from '@paralleldrive/cuid2';
import { 
  APIKey, 
  APIKeyValidationResult, 
  APIPermission, 
  RateLimit,
  APIKeyError 
} from '../auth-types';
import { RateLimiterService } from './rate-limiter-service';
import { AuditService } from './audit-service';

export class APIKeyService {
  private rateLimiter: RateLimiterService;
  private auditService: AuditService;

  constructor() {
    this.rateLimiter = new RateLimiterService();
    this.auditService = new AuditService();
  }

  /**
   * Generate a new API key with secure random generation
   */
  async generateAPIKey(data: {
    name: string;
    tenantId: number;
    permissions: APIPermission[];
    rateLimit: RateLimit;
    expiresAt?: Date;
  }): Promise<{ apiKey: APIKey; plainKey: string }> {
    console.log(`🔑 [APIKeyService] Generating new API key for tenant ${data.tenantId}`);

    // Generate secure random key
    const plainKey = this.generateSecureKey();
    const hashedKey = this.hashKey(plainKey);

    // Create API key record
    const apiKeyData = {
      id: createId(),
      name: data.name,
      key_hash: hashedKey,
      tenant_id: data.tenantId,
      permissions: JSON.stringify(data.permissions),
      rate_limit: JSON.stringify(data.rateLimit),
      is_active: true,
      expires_at: data.expiresAt || null,
      created_at: new Date(),
      updated_at: new Date(),
    };

    const [apiKey] = await db.insert(api_keys).values(apiKeyData).returning();

    // Log API key creation
    await this.auditService.log({
      action: 'api_key_created',
      resource: 'api_key',
      resourceId: apiKey.id,
      tenantId: data.tenantId,
      details: { name: data.name, permissions: data.permissions },
    });

    const result: APIKey = {
      id: apiKey.id,
      key: hashedKey, // Return hashed version
      name: apiKey.name,
      tenantId: apiKey.tenant_id,
      permissions: JSON.parse(apiKey.permissions),
      rateLimit: JSON.parse(apiKey.rate_limit),
      isActive: apiKey.is_active,
      expiresAt: apiKey.expires_at,
      lastUsedAt: apiKey.last_used_at,
      createdAt: apiKey.created_at,
      updatedAt: apiKey.updated_at,
    };

    console.log(`✅ [APIKeyService] API key generated successfully: ${apiKey.id}`);
    return { apiKey: result, plainKey };
  }

  /**
   * Validate API key and check permissions
   */
  async validateAPIKey(
    plainKey: string,
    requiredPermission?: { resource: string; action: string }
  ): Promise<APIKeyValidationResult> {
    try {
      const hashedKey = this.hashKey(plainKey);

      // Find API key by hash
      const [apiKeyRecord] = await db
        .select()
        .from(api_keys)
        .where(and(
          eq(api_keys.key_hash, hashedKey),
          eq(api_keys.is_active, true)
        ))
        .limit(1);

      if (!apiKeyRecord) {
        return {
          isValid: false,
          error: 'Invalid API key',
        };
      }

      // Check expiration
      if (apiKeyRecord.expires_at && new Date() > apiKeyRecord.expires_at) {
        return {
          isValid: false,
          error: 'API key has expired',
        };
      }

      const apiKey: APIKey = {
        id: apiKeyRecord.id,
        key: hashedKey,
        name: apiKeyRecord.name,
        tenantId: apiKeyRecord.tenant_id,
        permissions: JSON.parse(apiKeyRecord.permissions),
        rateLimit: JSON.parse(apiKeyRecord.rate_limit),
        isActive: apiKeyRecord.is_active,
        expiresAt: apiKeyRecord.expires_at,
        lastUsedAt: apiKeyRecord.last_used_at,
        createdAt: apiKeyRecord.created_at,
        updatedAt: apiKeyRecord.updated_at,
      };

      // Check rate limits
      const rateLimitStatus = await this.rateLimiter.checkRateLimit(
        apiKey.id,
        apiKey.rateLimit
      );

      if (rateLimitStatus.isBlocked) {
        return {
          isValid: false,
          error: 'Rate limit exceeded',
          rateLimitStatus,
        };
      }

      // Check permissions if required
      if (requiredPermission) {
        const hasPermission = this.checkPermission(
          apiKey.permissions,
          requiredPermission.resource,
          requiredPermission.action
        );

        if (!hasPermission) {
          return {
            isValid: false,
            error: `Insufficient permissions for ${requiredPermission.resource}:${requiredPermission.action}`,
          };
        }
      }

      // Update last used timestamp
      await this.updateLastUsed(apiKey.id);

      // Record usage
      await this.recordUsage(apiKey.id, requiredPermission);

      return {
        isValid: true,
        apiKey,
        rateLimitStatus,
      };
    } catch (error) {
      console.error('API key validation error:', error);
      return {
        isValid: false,
        error: 'Internal validation error',
      };
    }
  }

  /**
   * Rotate API key (generate new key, keep same permissions)
   */
  async rotateAPIKey(apiKeyId: string): Promise<{ apiKey: APIKey; plainKey: string }> {
    console.log(`🔄 [APIKeyService] Rotating API key: ${apiKeyId}`);

    const [existingKey] = await db
      .select()
      .from(api_keys)
      .where(eq(api_keys.id, apiKeyId))
      .limit(1);

    if (!existingKey) {
      throw new APIKeyError('API key not found', 'API_KEY_NOT_FOUND', 404);
    }

    // Generate new key
    const plainKey = this.generateSecureKey();
    const hashedKey = this.hashKey(plainKey);

    // Update the key hash
    const [updatedKey] = await db
      .update(api_keys)
      .set({
        key_hash: hashedKey,
        updated_at: new Date(),
      })
      .where(eq(api_keys.id, apiKeyId))
      .returning();

    // Log rotation
    await this.auditService.log({
      action: 'api_key_rotated',
      resource: 'api_key',
      resourceId: apiKeyId,
      tenantId: updatedKey.tenant_id,
      details: { rotatedAt: new Date() },
    });

    const result: APIKey = {
      id: updatedKey.id,
      key: hashedKey,
      name: updatedKey.name,
      tenantId: updatedKey.tenant_id,
      permissions: JSON.parse(updatedKey.permissions),
      rateLimit: JSON.parse(updatedKey.rate_limit),
      isActive: updatedKey.is_active,
      expiresAt: updatedKey.expires_at,
      lastUsedAt: updatedKey.last_used_at,
      createdAt: updatedKey.created_at,
      updatedAt: updatedKey.updated_at,
    };

    console.log(`✅ [APIKeyService] API key rotated successfully: ${apiKeyId}`);
    return { apiKey: result, plainKey };
  }

  /**
   * Revoke API key
   */
  async revokeAPIKey(apiKeyId: string): Promise<void> {
    console.log(`🚫 [APIKeyService] Revoking API key: ${apiKeyId}`);

    const [updatedKey] = await db
      .update(api_keys)
      .set({
        is_active: false,
        updated_at: new Date(),
      })
      .where(eq(api_keys.id, apiKeyId))
      .returning();

    if (!updatedKey) {
      throw new APIKeyError('API key not found', 'API_KEY_NOT_FOUND', 404);
    }

    // Log revocation
    await this.auditService.log({
      action: 'api_key_revoked',
      resource: 'api_key',
      resourceId: apiKeyId,
      tenantId: updatedKey.tenant_id,
      details: { revokedAt: new Date() },
    });

    console.log(`✅ [APIKeyService] API key revoked successfully: ${apiKeyId}`);
  }

  /**
   * Get API key usage statistics
   */
  async getUsageStats(apiKeyId: string, days: number = 30): Promise<{
    totalRequests: number;
    requestsByDay: Array<{ date: string; count: number }>;
    topEndpoints: Array<{ endpoint: string; count: number }>;
    errorRate: number;
  }> {
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const usage = await db
      .select()
      .from(api_key_usage)
      .where(and(
        eq(api_key_usage.api_key_id, apiKeyId),
        gt(api_key_usage.created_at, startDate)
      ));

    const totalRequests = usage.length;
    const errorCount = usage.filter(u => !u.success).length;
    const errorRate = totalRequests > 0 ? (errorCount / totalRequests) * 100 : 0;

    // Group by day
    const requestsByDay = usage.reduce((acc, u) => {
      const date = u.created_at.toISOString().split('T')[0];
      acc[date] = (acc[date] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Group by endpoint
    const endpointCounts = usage.reduce((acc, u) => {
      acc[u.endpoint] = (acc[u.endpoint] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalRequests,
      requestsByDay: Object.entries(requestsByDay).map(([date, count]) => ({ date, count })),
      topEndpoints: Object.entries(endpointCounts)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 10)
        .map(([endpoint, count]) => ({ endpoint, count })),
      errorRate,
    };
  }

  /**
   * Generate secure random API key
   */
  private generateSecureKey(): string {
    const prefix = 'pk_'; // Public key prefix
    const randomPart = randomBytes(32).toString('hex');
    return `${prefix}${randomPart}`;
  }

  /**
   * Hash API key using SHA-256
   */
  private hashKey(key: string): string {
    return createHash('sha256').update(key).digest('hex');
  }

  /**
   * Check if API key has required permission
   */
  private checkPermission(
    permissions: APIPermission[],
    resource: string,
    action: string
  ): boolean {
    return permissions.some(permission => 
      permission.resource === resource && 
      permission.actions.includes(action)
    );
  }

  /**
   * Update last used timestamp
   */
  private async updateLastUsed(apiKeyId: string): Promise<void> {
    await db
      .update(api_keys)
      .set({ last_used_at: new Date() })
      .where(eq(api_keys.id, apiKeyId));
  }

  /**
   * Record API key usage
   */
  private async recordUsage(
    apiKeyId: string,
    permission?: { resource: string; action: string }
  ): Promise<void> {
    await db.insert(api_key_usage).values({
      id: createId(),
      api_key_id: apiKeyId,
      endpoint: permission ? `${permission.resource}:${permission.action}` : 'unknown',
      method: 'GET', // Default method, should be passed from request
      success: true,
      created_at: new Date(),
    });
  }
}
