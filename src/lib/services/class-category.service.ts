import { db } from "@/lib/db";
import { class_categories, type ClassCategory, type NewClassCategory } from "@/lib/db/schema";
import { eq, and, ilike, desc, sql } from "drizzle-orm";
import { createId } from "@paralleldrive/cuid2";

/**
 * Class Category Service
 *
 * Service untuk manage class categories dengan pattern simple seperti EquipmentService.
 * Ini kayak "manager" yang handle semua operasi CRUD untuk class categories.
 */

// Interface untuk search results
export interface ClassCategorySearchResult {
  categories: ClassCategory[];
  total: number;
  hasMore: boolean;
}

/**
 * ClassCategoryService - Service untuk manage class categories
 *
 * Ini adalah "otak" dari class categories management. Semua operasi database
 * untuk class categories dihandle di sini dengan pattern simple seperti EquipmentService.
 */
export class ClassCategoryService {

  /**
   * Search class categories dengan filtering dan pagination
   */
  static async searchCategories(
    tenantId: number,
    search?: string,
    limit = 20,
    offset = 0
  ): Promise<ClassCategorySearchResult> {
    try {
      // Build where conditions
      let whereConditions = eq(class_categories.tenantId, tenantId);

      // Kalau ada search term, tambahin filter nama
      if (search && search.trim()) {
        whereConditions = and(
          whereConditions,
          ilike(class_categories.name, `%${search.trim()}%`)
        ) as any;
      }

      // Query dengan pagination
      const categories = await db
        .select()
        .from(class_categories)
        .where(whereConditions)
        .orderBy(desc(class_categories.createdAt))
        .limit(limit + 1) // +1 untuk cek hasMore
        .offset(offset);

      // Cek apakah ada data lebih
      const hasMore = categories.length > limit;
      if (hasMore) {
        categories.pop(); // Hapus item extra
      }

      // Count total untuk metadata
      const totalResult = await db
        .select({ count: sql<number>`count(*)` })
        .from(class_categories)
        .where(whereConditions);

      const total = Number(totalResult[0]?.count || 0);

      return {
        categories,
        total,
        hasMore,
      };
    } catch (error) {
      console.error("Error searching class categories:", error);
      throw error;
    }
  }

  /**
   * Get all class categories by tenant ID
   */
  static async getByTenantId(tenantId: number): Promise<ClassCategory[]> {
    return await db
      .select()
      .from(class_categories)
      .where(eq(class_categories.tenantId, tenantId))
      .orderBy(desc(class_categories.createdAt));
  }

  /**
   * Get class category by ID
   */
  static async getById(id: string): Promise<ClassCategory | null> {
    const [category] = await db
      .select()
      .from(class_categories)
      .where(eq(class_categories.id, id))
      .limit(1);
    return category || null;
  }

  /**
   * Create class category
   */
  static async create(data: { tenantId: number; name: string }): Promise<ClassCategory> {
    const [category] = await db
      .insert(class_categories)
      .values({
        id: createId(),
        tenantId: data.tenantId,
        name: data.name,
      })
      .returning();
    return category;
  }

  /**
   * Update class category
   */
  static async update(id: string, data: { name: string }): Promise<ClassCategory | null> {
    const [category] = await db
      .update(class_categories)
      .set({ name: data.name })
      .where(eq(class_categories.id, id))
      .returning();
    return category || null;
  }

  /**
   * Delete class category
   */
  static async delete(id: string): Promise<ClassCategory> {
    // First check if category exists
    const existingCategory = await this.getById(id);
    if (!existingCategory) {
      throw new Error("Class category not found");
    }

    // Delete the category and return the deleted record
    const [deletedCategory] = await db
      .delete(class_categories)
      .where(eq(class_categories.id, id))
      .returning();

    if (!deletedCategory) {
      throw new Error("Failed to delete class category");
    }

    return deletedCategory;
  }

}
