-- Performance optimization indexes for production
-- Run this manually in production for better performance

-- Business Profiles indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_business_profiles_tenant_id 
ON business_profiles(tenant_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_business_profiles_business_name 
ON business_profiles(business_name);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_business_profiles_created_at 
ON business_profiles(created_at DESC);

-- Addresses indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_addresses_tenant_id 
ON addresses(tenant_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_addresses_country 
ON addresses(country);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_addresses_state 
ON addresses(state);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_addresses_city 
ON addresses(city);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_addresses_postal_code 
ON addresses(postal_code);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_addresses_accepted_at 
ON addresses(accepted_at) WHERE accepted_at IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_addresses_created_at 
ON addresses(created_at DESC);

-- Settings indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_settings_tenant_id 
ON settings(tenant_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_settings_currency 
ON settings(currency);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_settings_time_zone 
ON settings(time_zone);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_settings_accepted_at 
ON settings(accepted_at) WHERE accepted_at IS NOT NULL;

-- Tenants indexes (if not already exist)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tenants_organization_id 
ON tenants(organization_id);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tenants_subdomain 
ON tenants(subdomain);

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tenants_custom_domain 
ON tenants(custom_domain) WHERE custom_domain IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tenants_is_active 
ON tenants(is_active) WHERE is_active = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tenants_created_at 
ON tenants(created_at DESC);

-- Users indexes for tenant relationship
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_tenant_id 
ON users(tenant_id) WHERE tenant_id IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_organization_tenant 
ON users(organization_id, tenant_id);

-- Composite indexes for common queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_business_profiles_tenant_active 
ON business_profiles(tenant_id, created_at DESC) 
WHERE tenant_id IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_addresses_tenant_status 
ON addresses(tenant_id, accepted_at, created_at DESC) 
WHERE tenant_id IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_settings_tenant_accepted 
ON settings(tenant_id, accepted_at) 
WHERE tenant_id IS NOT NULL;

-- Full-text search indexes (PostgreSQL specific)
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_business_profiles_search 
ON business_profiles USING gin(to_tsvector('english', 
  coalesce(business_name, '') || ' ' || 
  coalesce(company_registered_name, '') || ' ' || 
  coalesce(business_description, '')
));

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_addresses_search 
ON addresses USING gin(to_tsvector('english', 
  coalesce(address_line_1, '') || ' ' || 
  coalesce(address_line_2, '') || ' ' || 
  coalesce(city, '') || ' ' || 
  coalesce(state, '') || ' ' || 
  coalesce(country, '')
));

-- Partial indexes for better performance on filtered queries
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_business_profiles_with_logo 
ON business_profiles(tenant_id, created_at DESC) 
WHERE business_logo IS NOT NULL;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_business_profiles_whatsapp_enabled 
ON business_profiles(tenant_id) 
WHERE show_whatsapp_floating = true;

CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_addresses_pending 
ON addresses(tenant_id, created_at DESC) 
WHERE accepted_at IS NULL;

-- Statistics update for better query planning
ANALYZE business_profiles;
ANALYZE addresses;
ANALYZE settings;
ANALYZE tenants;
ANALYZE users;
