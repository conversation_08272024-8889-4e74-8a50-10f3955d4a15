import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { MembershipPlanLocationService } from "@/lib/services/membership-plan-location-service";
import { withRBAC } from "@/lib/middleware/rbac-middleware";

async function getAssignedLocationsHandler(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user?.tenantId) {
      return NextResponse.json(
        { error: "Tenant ID required" },
        { status: 400 }
      );
    }

    const membershipPlanId = params.id;
    const tenantId = parseInt(session.user.tenantId);

    const data = await MembershipPlanLocationService.getAssignedLocations(
      membershipPlanId,
      tenantId
    );

    return NextResponse.json({
      success: true,
      data,
    });
  } catch (error) {
    console.error("Error fetching assigned locations:", error);
    return NextResponse.json(
      { error: "Failed to fetch assigned locations" },
      { status: 500 }
    );
  }
}

// Protect with RBAC - require packages.manage permission
export const GET = withRBAC(getAssignedLocationsHandler, {
  module: "packages",
  action: "manage"
});
