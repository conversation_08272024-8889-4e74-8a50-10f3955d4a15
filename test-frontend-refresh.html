<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Role Management Frontend</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .button:hover { background: #0056b3; }
        .result { margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 4px; white-space: pre-wrap; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Role Management Frontend Test</h1>
        
        <div class="section">
            <h2>1. List Current Roles</h2>
            <button class="button" onclick="listRoles()">List Roles</button>
            <div id="rolesList" class="result"></div>
        </div>

        <div class="section">
            <h2>2. Create New Role</h2>
            <button class="button" onclick="createRole()">Create Test Role</button>
            <div id="createResult" class="result"></div>
        </div>

        <div class="section">
            <h2>3. Verify Role Creation</h2>
            <button class="button" onclick="verifyCreation()">Verify & List Again</button>
            <div id="verifyResult" class="result"></div>
        </div>
    </div>

    <script>
        let lastRoleCount = 0;
        let createdRoleId = null;

        async function listRoles() {
            try {
                const response = await fetch('/api/test-roles');
                const data = await response.json();
                
                if (data.success) {
                    lastRoleCount = data.data.totalRoles;
                    document.getElementById('rolesList').innerHTML = 
                        `✅ Found ${data.data.totalRoles} roles:\n` +
                        data.data.roles.map(r => `- ${r.display_name} (${r.name})`).join('\n');
                    document.getElementById('rolesList').className = 'result success';
                } else {
                    throw new Error(data.error);
                }
            } catch (error) {
                document.getElementById('rolesList').innerHTML = `❌ Error: ${error.message}`;
                document.getElementById('rolesList').className = 'result error';
            }
        }

        async function createRole() {
            try {
                const response = await fetch('/api/test-role-creation', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                const data = await response.json();
                
                if (data.success) {
                    createdRoleId = data.data.id;
                    document.getElementById('createResult').innerHTML = 
                        `✅ Role created successfully!\n` +
                        `ID: ${data.data.id}\n` +
                        `Name: ${data.data.name}\n` +
                        `Display Name: ${data.data.display_name}`;
                    document.getElementById('createResult').className = 'result success';
                } else {
                    throw new Error(data.error);
                }
            } catch (error) {
                document.getElementById('createResult').innerHTML = `❌ Error: ${error.message}`;
                document.getElementById('createResult').className = 'result error';
            }
        }

        async function verifyCreation() {
            try {
                const response = await fetch('/api/test-roles');
                const data = await response.json();
                
                if (data.success) {
                    const newCount = data.data.totalRoles;
                    const countDiff = newCount - lastRoleCount;
                    
                    let message = `✅ Current count: ${newCount} roles\n`;
                    message += `Previous count: ${lastRoleCount} roles\n`;
                    message += `Difference: ${countDiff > 0 ? '+' : ''}${countDiff}\n\n`;
                    
                    if (createdRoleId) {
                        const foundRole = data.data.roles.find(r => r.id === createdRoleId);
                        if (foundRole) {
                            message += `✅ Created role found: ${foundRole.display_name}`;
                        } else {
                            message += `❌ Created role NOT found in list!`;
                        }
                    }
                    
                    message += '\n\nAll roles:\n' + data.data.roles.map(r => `- ${r.display_name} (${r.name})`).join('\n');
                    
                    document.getElementById('verifyResult').innerHTML = message;
                    document.getElementById('verifyResult').className = 'result success';
                } else {
                    throw new Error(data.error);
                }
            } catch (error) {
                document.getElementById('verifyResult').innerHTML = `❌ Error: ${error.message}`;
                document.getElementById('verifyResult').className = 'result error';
            }
        }

        // Auto-load roles on page load
        window.onload = () => {
            listRoles();
        };
    </script>
</body>
</html>
