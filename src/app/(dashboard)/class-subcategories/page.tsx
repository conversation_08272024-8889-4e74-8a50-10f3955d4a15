import { Metadata } from "next";
import { ClassSubcategoriesManagement } from "@/components/class-subcategories/class-subcategories-management";

export const metadata: Metadata = {
  title: "Class Subcategories | Dashboard",
  description: "Manage your class subcategories to organize classes within categories",
};

export default function ClassSubcategoriesPage() {
  // In a real app, you would get the tenant ID from the session or context
  const tenantId = 1; // This should come from your auth/session system

  return (
    <div className="container mx-auto py-6">
      <ClassSubcategoriesManagement tenantId={tenantId} />
    </div>
  );
}
