"use client";

import { useEffect, useState } from "react";
import { Users, CreditCard, TrendingUp, Activity } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { ComponentLoading } from "@/components/ui/loading";
import { formatCredits, formatPrice } from "@/lib/stripe/config";

interface AdminStats {
  totalUsers: number;
  activeUsers: number;
  totalCreditsIssued: number;
  totalRevenue: number;
  newUsersThisMonth: number;
  creditsUsedThisMonth: number;
  revenueThisMonth: number;
  apiCallsThisMonth: number;
}

export function AdminStats() {
  const [stats, setStats] = useState<AdminStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await fetch("/api/admin/stats");
        if (response.ok) {
          const data = await response.json();
          setStats(data);
        }
      } catch (error) {
        console.error("Failed to fetch admin stats:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  if (loading) {
    return <ComponentLoading text="Loading admin statistics..." />;
  }

  if (!stats) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-muted-foreground">
            Failed to load statistics
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {/* Total Users */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Users</CardTitle>
          <Users className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.totalUsers.toLocaleString()}</div>
          <p className="text-xs text-muted-foreground">
            {stats.activeUsers} active users
          </p>
          <div className="text-xs text-green-600 mt-1">
            +{stats.newUsersThisMonth} this month
          </div>
        </CardContent>
      </Card>

      {/* Total Revenue */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
          <CreditCard className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatPrice(stats.totalRevenue)}</div>
          <p className="text-xs text-muted-foreground">
            All-time revenue
          </p>
          <div className="text-xs text-green-600 mt-1">
            +{formatPrice(stats.revenueThisMonth)} this month
          </div>
        </CardContent>
      </Card>

      {/* Credits Issued */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Credits Issued</CardTitle>
          <TrendingUp className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{formatCredits(stats.totalCreditsIssued)}</div>
          <p className="text-xs text-muted-foreground">
            Total credits sold
          </p>
          <div className="text-xs text-orange-600 mt-1">
            -{formatCredits(stats.creditsUsedThisMonth)} used this month
          </div>
        </CardContent>
      </Card>

      {/* API Calls */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">API Calls</CardTitle>
          <Activity className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.apiCallsThisMonth.toLocaleString()}</div>
          <p className="text-xs text-muted-foreground">
            This month
          </p>
          <div className="text-xs text-blue-600 mt-1">
            Active usage
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
