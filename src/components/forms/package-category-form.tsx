"use client";

import { z } from "zod";
import { Package, Building, Hash, FileText } from "lucide-react";
import { createFormComponent } from "@/lib/core/base-form";
import { PackageCategory } from "@/lib/db/schema";
import { useTenants } from "@/lib/hooks/queries/use-tenant-queries";

// Form validation schema
const packageCategorySchema = z.object({
  tenantId: z.number().int().positive("Tenant is required"),
  name: z.string().min(1, "Name is required").max(255, "Name must be less than 255 characters"),
  description: z.string().max(255, "Description must be less than 255 characters").optional(),
});

type PackageCategoryData = z.infer<typeof packageCategorySchema>;

// Get tenants for dropdown
function useTenantOptions() {
  const { data: tenantsData, isLoading } = useTenants();
  const tenants = tenantsData?.tenants || [];
  
  return {
    options: tenants.map(tenant => ({
      value: tenant.id,
      label: tenant.name,
    })),
    isLoading,
  };
}

// Form configuration
const formConfig = {
  schema: packageCategorySchema,
  defaultValues: {
    tenantId: 1,
    name: "",
    description: "",
  },
  fields: [
    {
      name: "tenantId" as const,
      label: "Tenant",
      type: "select" as const,
      placeholder: "Select tenant",
      required: true,
      icon: Building,
      options: [], // Will be populated dynamically
    },
    {
      name: "name" as const,
      label: "Category Name",
      type: "text" as const,
      placeholder: "Enter package category name (e.g., Fitness Classes, Personal Training, Memberships)",
      required: true,
      icon: Package,
      description: "A unique name for this package category",
    },
    {
      name: "description" as const,
      label: "Description",
      type: "textarea" as const,
      placeholder: "Enter description for this package category",
      icon: FileText,
      description: "Describe what types of packages belong to this category",
    },
  ],
  sections: [
    {
      title: "Basic Information",
      description: "General details about the package category",
      fields: ["tenantId", "name", "description"] as const,
    },
  ],
};

// Create the form component using the factory
const BasePackageCategoryForm = createFormComponent<PackageCategory, PackageCategoryData>(
  "Package Category",
  formConfig
);

// Enhanced form component with tenant options
interface PackageCategoryFormProps {
  entity?: PackageCategory;
  onSubmit: (data: PackageCategoryData) => Promise<void>;
  onCancel: () => void;
  tenantId?: number;
  className?: string;
  title?: string;
  description?: string;
}

export function PackageCategoryForm({ tenantId, ...props }: PackageCategoryFormProps) {
  const { options: tenantOptions, isLoading: tenantsLoading } = useTenantOptions();

  // Update form config with tenant options and default tenantId
  const enhancedConfig = {
    ...formConfig,
    defaultValues: {
      ...formConfig.defaultValues,
      tenantId: tenantId || formConfig.defaultValues.tenantId,
    },
    fields: formConfig.fields.map(field => 
      field.name === "tenantId" 
        ? { ...field, options: tenantOptions }
        : field
    ),
  };

  // Create enhanced form component
  const EnhancedForm = createFormComponent<PackageCategory, PackageCategoryData>(
    "Package Category",
    enhancedConfig
  );

  if (tenantsLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return <EnhancedForm {...props} icon={Package} />;
}

// Export types for external use
export type { PackageCategoryData };
