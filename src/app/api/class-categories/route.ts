import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { z } from "zod";
import { ClassCategoryService } from "@/lib/services/class-category.service";

/**
 * Class Categories API Routes
 * 
 * API endpoints untuk manage class categories dengan proper validation dan error handling.
 * Ini kayak "pintu masuk" untuk semua operasi class categories dari frontend.
 */

// Validation schemas
const createClassCategorySchema = z.object({
  tenantId: z.number().int().positive("Tenant ID must be positive"),
  name: z.string().min(1, "Name is required").max(255, "Name too long"),
});

const searchClassCategoriesSchema = z.object({
  tenantId: z.string().transform((val) => parseInt(val, 10)),
  search: z.string().optional(),
  limit: z.string().transform((val) => parseInt(val, 10)).optional(),
  offset: z.string().transform((val) => parseInt(val, 10)).optional(),
});

/**
 * GET /api/class-categories
 * 
 * Endpoint untuk search/list class categories dengan filtering dan pagination
 */
export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Parse dan validate query parameters
    const { searchParams } = new URL(request.url);
    const queryParams = {
      tenantId: searchParams.get("tenantId"),
      search: searchParams.get("search") || undefined,
      limit: searchParams.get("limit") || undefined,
      offset: searchParams.get("offset") || undefined,
    };

    // Validate parameters
    const validatedParams = searchClassCategoriesSchema.parse(queryParams);

    // Call service
    const result = await ClassCategoryService.searchCategories(
      validatedParams.tenantId,
      validatedParams.search,
      validatedParams.limit || 20,
      validatedParams.offset || 0
    );

    return NextResponse.json({
      success: true,
      data: result,
    });

  } catch (error) {
    console.error("GET /api/class-categories error:", error);

    // Handle validation errors
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: "Invalid parameters",
          details: error.errors,
        },
        { status: 400 }
      );
    }

    // Handle service errors
    if (error instanceof Error) {
      return NextResponse.json(
        {
          success: false,
          error: error.message,
        },
        { status: 400 }
      );
    }

    // Generic error
    return NextResponse.json(
      {
        success: false,
        error: "Internal server error",
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/class-categories
 * 
 * Endpoint untuk create class category baru
 */
export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();

    // Validate request data
    const validatedData = createClassCategorySchema.parse(body);

    // Call service untuk create
    const category = await ClassCategoryService.create(validatedData);

    return NextResponse.json({
      success: true,
      data: category,
      message: "Class category created successfully",
    }, { status: 201 });

  } catch (error) {
    console.error("POST /api/class-categories error:", error);

    // Handle validation errors
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: "Invalid data",
          details: error.errors,
        },
        { status: 400 }
      );
    }

    // Handle service errors
    if (error instanceof Error) {
      // Check for specific business logic errors
      if (error.message.includes("already exists")) {
        return NextResponse.json(
          {
            success: false,
            error: error.message,
          },
          { status: 409 } // Conflict
        );
      }

      return NextResponse.json(
        {
          success: false,
          error: error.message,
        },
        { status: 400 }
      );
    }

    // Generic error
    return NextResponse.json(
      {
        success: false,
        error: "Internal server error",
      },
      { status: 500 }
    );
  }
}

/**
 * Helper function untuk get all categories (untuk dropdown)
 * Ini bisa dipanggil dengan query parameter ?all=true
 */
export async function getAllCategories(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json(
        { success: false, error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get("tenantId");

    if (!tenantId) {
      return NextResponse.json(
        { success: false, error: "Tenant ID is required" },
        { status: 400 }
      );
    }

    const categories = await ClassCategoryService.getByTenantId(parseInt(tenantId, 10));

    return NextResponse.json({
      success: true,
      data: categories,
    });

  } catch (error) {
    console.error("getAllCategories error:", error);
    return NextResponse.json(
      {
        success: false,
        error: "Failed to fetch categories",
      },
      { status: 500 }
    );
  }
}
