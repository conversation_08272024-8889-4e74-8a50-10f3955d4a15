"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useInlineCreation } from "@/lib/hooks/use-inline-creation";
import { useInlineCreationStore } from "@/lib/stores/inline-creation-store";

export default function TestModalPage() {
  const { openClassLevelCreation, openClassCategoryCreation, openLocationCreation } = useInlineCreation(1);
  const { isOpen, config } = useInlineCreationStore();

  const handleTestClassLevel = () => {
    console.log('Test button clicked: Class Level');
    openClassLevelCreation({
      onSuccess: (newEntity) => {
        console.log('Success callback called:', newEntity);
      },
    });
  };

  const handleTestClassCategory = () => {
    console.log('Test button clicked: Class Category');
    openClassCategoryCreation({
      onSuccess: (newEntity) => {
        console.log('Success callback called:', newEntity);
      },
    });
  };

  const handleTestLocation = () => {
    console.log('Test button clicked: Location');
    openLocationCreation({
      onSuccess: (newEntity) => {
        console.log('Success callback called:', newEntity);
      },
    });
  };

  return (
    <div className="container mx-auto py-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Test Inline Creation Modal</CardTitle>
          <CardDescription>
            Test the inline creation functionality for different entities
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button onClick={handleTestClassLevel} variant="outline">
              Test Class Level Creation
            </Button>
            
            <Button onClick={handleTestClassCategory} variant="outline">
              Test Class Category Creation
            </Button>
            
            <Button onClick={handleTestLocation} variant="outline">
              Test Location Creation
            </Button>
          </div>
          
          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <h3 className="font-semibold mb-2">Current Modal State:</h3>
            <p><strong>Is Open:</strong> {isOpen ? 'Yes' : 'No'}</p>
            <p><strong>Entity Type:</strong> {config?.entityType || 'None'}</p>
            <p><strong>Title:</strong> {config?.title || 'None'}</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
