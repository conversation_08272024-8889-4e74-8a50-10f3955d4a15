import { NextRequest, NextResponse } from "next/server";
import { BlogService } from "@/lib/services/blog.service";
import { updateBlogCategorySchema } from "@/lib/validations/blog";
import { withRBAC } from "@/lib/middleware/rbac-middleware";
import { z } from "zod";

interface RouteParams {
  params: Promise<{ id: string }>;
}

// GET /api/blog-categories/[id] - Get single blog category
export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  return withRBAC(
    async () => {
      try {
        const { id } = await params;

        if (!id) {
          return NextResponse.json(
            { error: "Blog category ID is required" },
            { status: 400 }
          );
        }

        const category = await BlogService.getCategoryById(id);

        if (!category) {
          return NextResponse.json(
            { error: "Blog category not found" },
            { status: 404 }
          );
        }

        return NextResponse.json({
          success: true,
          data: category,
        });
      } catch (error) {
        console.error("Error fetching blog category:", error);
        return NextResponse.json(
          { 
            error: error instanceof Error ? error.message : "Failed to fetch blog category",
            success: false 
          },
          { status: 500 }
        );
      }
    },
    { module: "blog", action: "read" }
  )(request);
}

// PUT /api/blog-categories/[id] - Update blog category
export async function PUT(
  request: NextRequest,
  { params }: RouteParams
) {
  return withRBAC(
    async () => {
      try {
        const { id } = await params;
        const body = await request.json();

        if (!id) {
          return NextResponse.json(
            { error: "Blog category ID is required" },
            { status: 400 }
          );
        }

        // Validate request body
        const validatedData = updateBlogCategorySchema.parse(body);

        // Update blog category
        const updatedCategory = await BlogService.updateCategory(id, validatedData);

        return NextResponse.json({
          success: true,
          data: updatedCategory,
          message: "Blog category updated successfully",
        });
      } catch (error) {
        console.error("Error updating blog category:", error);
        
        if (error instanceof z.ZodError) {
          return NextResponse.json(
            { 
              error: "Validation failed",
              details: error.errors,
              success: false 
            },
            { status: 400 }
          );
        }

        return NextResponse.json(
          { 
            error: error instanceof Error ? error.message : "Failed to update blog category",
            success: false 
          },
          { status: 500 }
        );
      }
    },
    { module: "blog", action: "update" }
  )(request);
}

// DELETE /api/blog-categories/[id] - Delete blog category
export async function DELETE(
  request: NextRequest,
  { params }: RouteParams
) {
  return withRBAC(
    async () => {
      try {
        const { id } = await params;

        if (!id) {
          return NextResponse.json(
            { error: "Blog category ID is required" },
            { status: 400 }
          );
        }

        await BlogService.deleteCategory(id);

        return NextResponse.json({
          success: true,
          message: "Blog category deleted successfully",
        });
      } catch (error) {
        console.error("Error deleting blog category:", error);
        return NextResponse.json(
          { 
            error: error instanceof Error ? error.message : "Failed to delete blog category",
            success: false 
          },
          { status: 500 }
        );
      }
    },
    { module: "blog", action: "delete" }
  )(request);
}
