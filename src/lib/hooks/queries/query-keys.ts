/**
 * Global Query Keys Factory
 * Centralized query key management untuk seluruh aplikasi
 */

// Base query keys untuk semua entities
export const queryKeys = {
  // Users
  users: {
    all: ['users'] as const,
    lists: () => [...queryKeys.users.all, 'list'] as const,
    list: (filters: Record<string, any>) => [...queryKeys.users.lists(), { filters }] as const,
    details: () => [...queryKeys.users.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.users.details(), id] as const,
    profile: () => [...queryKeys.users.all, 'profile'] as const,
    organizations: (userId: string) => [...queryKeys.users.all, 'organizations', userId] as const,
    tenants: (userId: string) => [...queryKeys.users.all, 'tenants', userId] as const,
    stats: () => [...queryKeys.users.all, 'stats'] as const,
    search: (query: string) => [...queryKeys.users.all, 'search', query] as const,
  },

  // Organizations
  organizations: {
    all: ['organizations'] as const,
    lists: () => [...queryKeys.organizations.all, 'list'] as const,
    list: (filters: Record<string, any>) => [...queryKeys.organizations.lists(), { filters }] as const,
    details: () => [...queryKeys.organizations.all, 'detail'] as const,
    detail: (id: number) => [...queryKeys.organizations.details(), id] as const,
    members: (id: number) => [...queryKeys.organizations.all, 'members', id] as const,
    tenants: (id: number) => [...queryKeys.organizations.all, 'tenants', id] as const,
    stats: () => [...queryKeys.organizations.all, 'stats'] as const,
    billing: (id: number) => [...queryKeys.organizations.all, 'billing', id] as const,
  },

  // Tenants
  tenants: {
    all: ['tenants'] as const,
    lists: () => [...queryKeys.tenants.all, 'list'] as const,
    list: (filters: Record<string, any>) => [...queryKeys.tenants.lists(), { filters }] as const,
    details: () => [...queryKeys.tenants.all, 'detail'] as const,
    detail: (id: number) => [...queryKeys.tenants.details(), id] as const,
    bySubdomain: (subdomain: string) => [...queryKeys.tenants.all, 'subdomain', subdomain] as const,
    byDomain: (domain: string) => [...queryKeys.tenants.all, 'domain', domain] as const,
    users: (id: number) => [...queryKeys.tenants.all, 'users', id] as const,
    businessProfile: (id: number) => [...queryKeys.tenants.all, 'business-profile', id] as const,
    addresses: (id: number) => [...queryKeys.tenants.all, 'addresses', id] as const,
    settings: (id: number) => [...queryKeys.tenants.all, 'settings', id] as const,
    stats: () => [...queryKeys.tenants.all, 'stats'] as const,
    analytics: (id: number) => [...queryKeys.tenants.all, 'analytics', id] as const,
  },

  // Business Profiles
  businessProfiles: {
    all: ['business-profiles'] as const,
    lists: () => [...queryKeys.businessProfiles.all, 'list'] as const,
    list: (filters: Record<string, any>) => [...queryKeys.businessProfiles.lists(), { filters }] as const,
    details: () => [...queryKeys.businessProfiles.all, 'detail'] as const,
    detail: (tenantId: number) => [...queryKeys.businessProfiles.details(), tenantId] as const,
    byTenant: (tenantId: number) => [...queryKeys.businessProfiles.all, 'tenant', tenantId] as const,
    stats: () => [...queryKeys.businessProfiles.all, 'stats'] as const,
  },

  // Addresses
  addresses: {
    all: ['addresses'] as const,
    lists: () => [...queryKeys.addresses.all, 'list'] as const,
    list: (filters: Record<string, any>) => [...queryKeys.addresses.lists(), { filters }] as const,
    details: () => [...queryKeys.addresses.all, 'detail'] as const,
    detail: (id: number) => [...queryKeys.addresses.details(), id] as const,
    byTenant: (tenantId: number) => [...queryKeys.addresses.all, 'tenant', tenantId] as const,
    paginated: (tenantId: number, page: number) => [...queryKeys.addresses.all, 'paginated', tenantId, page] as const,
    stats: (tenantId: number) => [...queryKeys.addresses.all, 'stats', tenantId] as const,
  },

  // Locations
  locations: {
    all: ['locations'] as const,
    lists: () => [...queryKeys.locations.all, 'list'] as const,
    list: (filters: Record<string, any>) => [...queryKeys.locations.lists(), { filters }] as const,
    details: () => [...queryKeys.locations.all, 'detail'] as const,
    detail: (id: string | number) => [...queryKeys.locations.details(), id] as const,
    byTenant: (tenantId: number) => [...queryKeys.locations.all, 'tenant', tenantId] as const,
    paginated: (tenantId: number, page: number) => [...queryKeys.locations.all, 'paginated', tenantId, page] as const,
    stats: (tenantId: number) => [...queryKeys.locations.all, 'stats', tenantId] as const,
  },

  // Equipment
  equipment: {
    all: ['equipment'] as const,
    lists: () => [...queryKeys.equipment.all, 'list'] as const,
    list: (filters: Record<string, any>) => [...queryKeys.equipment.lists(), { filters }] as const,
    details: () => [...queryKeys.equipment.all, 'detail'] as const,
    detail: (id: string) => [...queryKeys.equipment.details(), id] as const,
    byTenant: (tenantId: number) => [...queryKeys.equipment.all, 'tenant', tenantId] as const,
    paginated: (tenantId: number, page: number) => [...queryKeys.equipment.all, 'paginated', tenantId, page] as const,
    stats: (tenantId: number) => [...queryKeys.equipment.all, 'stats', tenantId] as const,
  },

  // Settings
  settings: {
    all: ['settings'] as const,
    global: () => [...queryKeys.settings.all, 'global'] as const,
    byTenant: (tenantId: number) => [...queryKeys.settings.all, 'tenant', tenantId] as const,
    byUser: (userId: string) => [...queryKeys.settings.all, 'user', userId] as const,
    byOrganization: (orgId: number) => [...queryKeys.settings.all, 'organization', orgId] as const,
  },

  // Credits & Billing
  billing: {
    all: ['billing'] as const,
    credits: (userId: string) => [...queryKeys.billing.all, 'credits', userId] as const,
    packages: () => [...queryKeys.billing.all, 'packages'] as const,
    transactions: (userId: string) => [...queryKeys.billing.all, 'transactions', userId] as const,
    usage: (userId: string) => [...queryKeys.billing.all, 'usage', userId] as const,
  },

  // Analytics
  analytics: {
    all: ['analytics'] as const,
    dashboard: () => [...queryKeys.analytics.all, 'dashboard'] as const,
    users: (period: string) => [...queryKeys.analytics.all, 'users', period] as const,
    organizations: (period: string) => [...queryKeys.analytics.all, 'organizations', period] as const,
    tenants: (period: string) => [...queryKeys.analytics.all, 'tenants', period] as const,
    api: (period: string) => [...queryKeys.analytics.all, 'api', period] as const,
  },

  // API Keys
  apiKeys: {
    all: ['api-keys'] as const,
    byUser: (userId: string) => [...queryKeys.apiKeys.all, 'user', userId] as const,
    byOrganization: (orgId: number) => [...queryKeys.apiKeys.all, 'organization', orgId] as const,
    usage: (keyId: string) => [...queryKeys.apiKeys.all, 'usage', keyId] as const,
  },

  // Notifications
  notifications: {
    all: ['notifications'] as const,
    byUser: (userId: string) => [...queryKeys.notifications.all, 'user', userId] as const,
    unread: (userId: string) => [...queryKeys.notifications.all, 'unread', userId] as const,
    settings: (userId: string) => [...queryKeys.notifications.all, 'settings', userId] as const,
  },

  // File Uploads
  uploads: {
    all: ['uploads'] as const,
    byUser: (userId: string) => [...queryKeys.uploads.all, 'user', userId] as const,
    byType: (type: string) => [...queryKeys.uploads.all, 'type', type] as const,
    presignedUrl: (filename: string) => [...queryKeys.uploads.all, 'presigned', filename] as const,
  },

  // Search
  search: {
    all: ['search'] as const,
    global: (query: string) => [...queryKeys.search.all, 'global', query] as const,
    users: (query: string) => [...queryKeys.search.all, 'users', query] as const,
    organizations: (query: string) => [...queryKeys.search.all, 'organizations', query] as const,
    tenants: (query: string) => [...queryKeys.search.all, 'tenants', query] as const,
    businessProfiles: (query: string) => [...queryKeys.search.all, 'business-profiles', query] as const,
  },
} as const;

// Helper functions untuk invalidation patterns
export const invalidationPatterns = {
  // Invalidate all user-related queries
  invalidateUserQueries: (queryClient: any, userId?: string) => {
    queryClient.invalidateQueries({ queryKey: queryKeys.users.all });
    if (userId) {
      queryClient.invalidateQueries({ queryKey: queryKeys.users.detail(userId) });
      queryClient.invalidateQueries({ queryKey: queryKeys.users.organizations(userId) });
      queryClient.invalidateQueries({ queryKey: queryKeys.users.tenants(userId) });
    }
  },

  // Invalidate all organization-related queries
  invalidateOrganizationQueries: (queryClient: any, orgId?: number) => {
    queryClient.invalidateQueries({ queryKey: queryKeys.organizations.all });
    if (orgId) {
      queryClient.invalidateQueries({ queryKey: queryKeys.organizations.detail(orgId) });
      queryClient.invalidateQueries({ queryKey: queryKeys.organizations.members(orgId) });
      queryClient.invalidateQueries({ queryKey: queryKeys.organizations.tenants(orgId) });
    }
  },

  // Invalidate all tenant-related queries
  invalidateTenantQueries: (queryClient: any, tenantId?: number) => {
    queryClient.invalidateQueries({ queryKey: queryKeys.tenants.all });
    if (tenantId) {
      queryClient.invalidateQueries({ queryKey: queryKeys.tenants.detail(tenantId) });
      queryClient.invalidateQueries({ queryKey: queryKeys.tenants.users(tenantId) });
      queryClient.invalidateQueries({ queryKey: queryKeys.tenants.businessProfile(tenantId) });
      queryClient.invalidateQueries({ queryKey: queryKeys.tenants.addresses(tenantId) });
      queryClient.invalidateQueries({ queryKey: queryKeys.tenants.settings(tenantId) });
    }
  },

  // Invalidate all business profile queries
  invalidateBusinessProfileQueries: (queryClient: any, tenantId?: number) => {
    queryClient.invalidateQueries({ queryKey: queryKeys.businessProfiles.all });
    if (tenantId) {
      queryClient.invalidateQueries({ queryKey: queryKeys.businessProfiles.detail(tenantId) });
      queryClient.invalidateQueries({ queryKey: queryKeys.businessProfiles.byTenant(tenantId) });
    }
  },

  // Invalidate all address queries
  invalidateAddressQueries: (queryClient: any, tenantId?: number) => {
    queryClient.invalidateQueries({ queryKey: queryKeys.addresses.all });
    if (tenantId) {
      queryClient.invalidateQueries({ queryKey: queryKeys.addresses.byTenant(tenantId) });
      queryClient.invalidateQueries({ queryKey: queryKeys.addresses.stats(tenantId) });
    }
  },

  // Invalidate all location queries
  invalidateLocationQueries: (queryClient: any, tenantId?: number) => {
    queryClient.invalidateQueries({ queryKey: queryKeys.locations.all });
    if (tenantId) {
      queryClient.invalidateQueries({ queryKey: queryKeys.locations.byTenant(tenantId) });
      queryClient.invalidateQueries({ queryKey: queryKeys.locations.stats(tenantId) });
    }
  },

  // Invalidate all equipment queries
  invalidateEquipmentQueries: (queryClient: any, tenantId?: number) => {
    queryClient.invalidateQueries({ queryKey: queryKeys.equipment.all });
    if (tenantId) {
      queryClient.invalidateQueries({ queryKey: queryKeys.equipment.byTenant(tenantId) });
      queryClient.invalidateQueries({ queryKey: queryKeys.equipment.stats(tenantId) });
    }
  },

  // Invalidate all stats queries
  invalidateStatsQueries: (queryClient: any) => {
    queryClient.invalidateQueries({ queryKey: queryKeys.users.stats() });
    queryClient.invalidateQueries({ queryKey: queryKeys.organizations.stats() });
    queryClient.invalidateQueries({ queryKey: queryKeys.tenants.stats() });
    queryClient.invalidateQueries({ queryKey: queryKeys.businessProfiles.stats() });
  },

  // Invalidate all search queries
  invalidateSearchQueries: (queryClient: any) => {
    queryClient.invalidateQueries({ queryKey: queryKeys.search.all });
  },

  // Invalidate all billing queries
  invalidateBillingQueries: (queryClient: any, userId?: string) => {
    queryClient.invalidateQueries({ queryKey: queryKeys.billing.all });
    if (userId) {
      queryClient.invalidateQueries({ queryKey: queryKeys.billing.credits(userId) });
      queryClient.invalidateQueries({ queryKey: queryKeys.billing.transactions(userId) });
      queryClient.invalidateQueries({ queryKey: queryKeys.billing.usage(userId) });
    }
  },
};

// Type helpers
export type QueryKey = typeof queryKeys;
export type UserQueryKey = typeof queryKeys.users;
export type OrganizationQueryKey = typeof queryKeys.organizations;
export type TenantQueryKey = typeof queryKeys.tenants;
export type BusinessProfileQueryKey = typeof queryKeys.businessProfiles;
export type AddressQueryKey = typeof queryKeys.addresses;
export type LocationQueryKey = typeof queryKeys.locations;
export type SettingsQueryKey = typeof queryKeys.settings;
export type EquipmentQueryKey = typeof queryKeys.equipment;

// Export individual query key factories for backward compatibility
export const userKeys = queryKeys.users;
export const organizationKeys = queryKeys.organizations;
export const tenantKeys = queryKeys.tenants;
export const businessProfileKeys = queryKeys.businessProfiles;
export const addressKeys = queryKeys.addresses;
export const locationKeys = queryKeys.locations;
export const settingsKeys = queryKeys.settings;
export const billingKeys = queryKeys.billing;
export const analyticsKeys = queryKeys.analytics;
export const apiKeysKeys = queryKeys.apiKeys;
export const notificationKeys = queryKeys.notifications;
export const uploadKeys = queryKeys.uploads;
export const searchKeys = queryKeys.search;
export const equipmentKeys = queryKeys.equipment;
