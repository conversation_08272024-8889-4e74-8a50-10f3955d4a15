import { NextRequest, NextResponse } from "next/server";
import { RoleService } from "@/lib/services/role.service";

// Test endpoint to verify role creation works
export async function POST(request: NextRequest) {
  try {
    console.log("🧪 Testing role creation...");
    
    const testRoleData = {
      name: `test_role_${Date.now()}`,
      display_name: "Test Role",
      description: "A test role created via API",
      is_system_role: false,
      hierarchy_level: 60,
      tenantId: 1,
      permissions: []
    };

    console.log("📝 Creating test role with data:", testRoleData);
    
    // Test role creation
    const createdRole = await RoleService.create(testRoleData);
    
    console.log("✅ Role creation successful:", {
      id: createdRole.id,
      name: createdRole.name,
      display_name: createdRole.display_name
    });

    return NextResponse.json({
      success: true,
      message: "Role creation is working!",
      data: {
        id: createdRole.id,
        name: createdRole.name,
        display_name: createdRole.display_name,
        description: createdRole.description,
        is_system_role: createdRole.is_system_role,
        hierarchy_level: createdRole.hierarchy_level,
        tenantId: createdRole.tenantId
      }
    });
  } catch (error) {
    console.error("❌ Role creation test failed:", error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}
