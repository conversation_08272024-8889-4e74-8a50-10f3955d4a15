import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { tagService } from "@/lib/services/tag.service";
import { auth } from "@/lib/auth/config";

// Validation schema for updates
const updateTagSchema = z.object({
  name: z.string().min(1, "Name is required").max(100, "Name must be less than 100 characters").optional(),
  slug: z.string().optional(),
  description: z.string().optional(),
  color: z.string().regex(/^#[0-9A-F]{6}$/i, "Color must be a valid hex color").optional(),
  icon: z.string().optional(),
  category: z.string().optional(),
  isActive: z.boolean().optional(),
  sortOrder: z.number().int().min(0).optional(),
  metadata: z.record(z.any()).optional(),
});

/**
 * GET /api/tags/[id]
 * Get a specific tag by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = params;
    console.log(`🏷️ [GET /api/tags/${id}] Fetching tag`);

    const tag = await tagService.getById(id);

    if (!tag) {
      return NextResponse.json({ error: "Tag not found" }, { status: 404 });
    }

    // For now, skip tenant verification since we don't have tenantId in session
    // TODO: Add proper tenant verification when session includes tenantId

    return NextResponse.json({
      success: true,
      data: tag
    });
  } catch (error) {
    console.error(`🏷️ [GET /api/tags/${params.id}] Error:`, error);
    return NextResponse.json(
      { error: "Failed to fetch tag" },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/tags/[id]
 * Update a specific tag
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = params;
    const body = await request.json();
    const validatedData = updateTagSchema.parse(body);

    console.log(`🏷️ [PUT /api/tags/${id}] Updating tag:`, validatedData);

    // Verify tag exists
    const existingTag = await tagService.getById(id);
    if (!existingTag) {
      return NextResponse.json({ error: "Tag not found" }, { status: 404 });
    }

    // TODO: Add proper tenant verification when session includes tenantId

    const updatedTag = await tagService.update(id, validatedData);

    return NextResponse.json({
      success: true,
      data: updatedTag,
      message: "Tag updated successfully"
    });
  } catch (error) {
    console.error(`🏷️ [PUT /api/tags/${params.id}] Error:`, error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid tag data", details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error && error.message.includes("already exists")) {
      return NextResponse.json(
        { error: error.message },
        { status: 409 }
      );
    }

    return NextResponse.json(
      { error: "Failed to update tag" },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/tags/[id]
 * Delete a specific tag
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = params;
    console.log(`🏷️ [DELETE /api/tags/${id}] Deleting tag`);

    // Verify tag exists
    const existingTag = await tagService.getById(id);
    if (!existingTag) {
      return NextResponse.json({ error: "Tag not found" }, { status: 404 });
    }

    // TODO: Add proper tenant verification when session includes tenantId

    await tagService.delete(id);

    return NextResponse.json({
      success: true,
      message: "Tag deleted successfully"
    });
  } catch (error) {
    console.error(`🏷️ [DELETE /api/tags/${params.id}] Error:`, error);
    return NextResponse.json(
      { error: "Failed to delete tag" },
      { status: 500 }
    );
  }
}
