import { db } from "@/lib/db";
import { package_locations, packages, locations, PackageLocation, NewPackageLocation } from "@/lib/db/schema";
import { eq, and, sql } from "drizzle-orm";

export interface PackageLocationWithDetails extends PackageLocation {
  package?: {
    id: string;
    name: string;
    description?: string;
  };
  location?: {
    id: string;
    name: string;
    address?: string;
  };
}

export interface PackageLocationFilters {
  packageId?: string;
  locationId?: string;
  search?: string;
}

export class PackageLocationService {
  private buildFilterConditions(filters: PackageLocationFilters): string[] {
    const conditions: string[] = [];

    // Ensure filters is not null/undefined
    if (!filters) {
      return conditions;
    }

    if (filters.packageId) {
      conditions.push(`package_id = '${filters.packageId}'`);
    }

    if (filters.locationId) {
      conditions.push(`location_id = '${filters.locationId}'`);
    }

    if (filters.search) {
      // Search in package name or location name through joins
      conditions.push(`(
        EXISTS (
          SELECT 1 FROM packages p
          WHERE p.id = package_locations.package_id
          AND LOWER(p.name) LIKE LOWER('%${filters.search}%')
        ) OR
        EXISTS (
          SELECT 1 FROM locations l
          WHERE l.id = package_locations.location_id
          AND LOWER(l.name) LIKE LOWER('%${filters.search}%')
        )
      )`);
    }

    return conditions;
  }

  async getWithDetails(filters: PackageLocationFilters = {}): Promise<PackageLocationWithDetails[]> {
    console.log(`📍 [PackageLocationService] Getting package locations with details:`, filters);

    try {
      // Get basic package locations first
      let query = db.select().from(package_locations);

      // Apply basic filters
      if (filters.packageId) {
        query = query.where(eq(package_locations.package_id, filters.packageId));
      }
      if (filters.locationId) {
        query = query.where(eq(package_locations.location_id, filters.locationId));
      }

      const packageLocationResults = await query;

      // Get package and location details separately
      const packageIds = [...new Set(packageLocationResults.map(pl => pl.package_id))];
      const locationIds = [...new Set(packageLocationResults.map(pl => pl.location_id))];

      const [packageDetails, locationDetails] = await Promise.all([
        packageIds.length > 0 ? db.select().from(packages).where(sql`id IN (${packageIds.map(id => `'${id}'`).join(',')})`) : [],
        locationIds.length > 0 ? db.select().from(locations).where(sql`id IN (${locationIds.map(id => `'${id}'`).join(',')})`) : []
      ]);

      // Combine results
      const results: PackageLocationWithDetails[] = packageLocationResults.map(pl => ({
        ...pl,
        package: packageDetails.find(p => p.id === pl.package_id) ? {
          id: packageDetails.find(p => p.id === pl.package_id)!.id,
          name: packageDetails.find(p => p.id === pl.package_id)!.name,
          description: packageDetails.find(p => p.id === pl.package_id)!.description,
        } : undefined,
        location: locationDetails.find(l => l.id === pl.location_id) ? {
          id: locationDetails.find(l => l.id === pl.location_id)!.id,
          name: locationDetails.find(l => l.id === pl.location_id)!.name,
          address: locationDetails.find(l => l.id === pl.location_id)!.address,
        } : undefined,
      }));

      // Apply search filter if needed
      let filteredResults = results;
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        filteredResults = results.filter(pl =>
          pl.package?.name?.toLowerCase().includes(searchLower) ||
          pl.location?.name?.toLowerCase().includes(searchLower) ||
          pl.location?.address?.toLowerCase().includes(searchLower)
        );
      }

      console.log(`✅ [PackageLocationService] Found ${filteredResults.length} package locations with details`);
      return filteredResults;
    } catch (error) {
      console.error(`❌ [PackageLocationService] Error getting package locations with details:`, error);
      throw error;
    }
  }

  async getByPackageId(packageId: string): Promise<PackageLocationWithDetails[]> {
    console.log(`📍 [PackageLocationService] Getting locations for package:`, packageId);
    return this.getWithDetails({ packageId });
  }

  async getByLocationId(locationId: string): Promise<PackageLocationWithDetails[]> {
    console.log(`📍 [PackageLocationService] Getting packages for location:`, locationId);
    return this.getWithDetails({ locationId });
  }

  async addPackageToLocation(packageId: string, locationId: string): Promise<PackageLocation> {
    console.log(`📍 [PackageLocationService] Adding package to location:`, { packageId, locationId });

    try {
      // Check if relationship already exists
      const existing = await db
        .select()
        .from(package_locations)
        .where(
          and(
            eq(package_locations.package_id, packageId),
            eq(package_locations.location_id, locationId)
          )
        );

      if (existing.length > 0) {
        console.log(`⚠️ [PackageLocationService] Package location relationship already exists`);
        return existing[0];
      }

      const [packageLocation] = await db
        .insert(package_locations)
        .values({
          package_id: packageId,
          location_id: locationId,
        })
        .returning();

      console.log(`✅ [PackageLocationService] Package location relationship created:`, packageLocation);
      return packageLocation;
    } catch (error) {
      console.error(`❌ [PackageLocationService] Error adding package to location:`, error);
      throw error;
    }
  }

  async removePackageFromLocation(packageId: string, locationId: string): Promise<void> {
    console.log(`📍 [PackageLocationService] Removing package from location:`, { packageId, locationId });

    try {
      await db
        .delete(package_locations)
        .where(
          and(
            eq(package_locations.package_id, packageId),
            eq(package_locations.location_id, locationId)
          )
        );

      console.log(`✅ [PackageLocationService] Package location relationship removed`);
    } catch (error) {
      console.error(`❌ [PackageLocationService] Error removing package from location:`, error);
      throw error;
    }
  }

  async bulkAddLocationsToPackage(packageId: string, locationIds: string[]): Promise<PackageLocation[]> {
    console.log(`📍 [PackageLocationService] Bulk adding locations to package:`, { packageId, locationIds });

    try {
      // Remove existing relationships for this package
      await db
        .delete(package_locations)
        .where(eq(package_locations.package_id, packageId));

      // Add new relationships
      if (locationIds.length === 0) {
        console.log(`✅ [PackageLocationService] No locations to add`);
        return [];
      }

      const values = locationIds.map(locationId => ({
        package_id: packageId,
        location_id: locationId,
      }));

      const results = await db
        .insert(package_locations)
        .values(values)
        .returning();

      console.log(`✅ [PackageLocationService] Bulk added ${results.length} package location relationships`);
      return results;
    } catch (error) {
      console.error(`❌ [PackageLocationService] Error bulk adding locations to package:`, error);
      throw error;
    }
  }

  async bulkAddPackagesToLocation(locationId: string, packageIds: string[]): Promise<PackageLocation[]> {
    console.log(`📍 [PackageLocationService] Bulk adding packages to location:`, { locationId, packageIds });

    try {
      // Remove existing relationships for this location
      await db
        .delete(package_locations)
        .where(eq(package_locations.location_id, locationId));

      // Add new relationships
      if (packageIds.length === 0) {
        console.log(`✅ [PackageLocationService] No packages to add`);
        return [];
      }

      const values = packageIds.map(packageId => ({
        package_id: packageId,
        location_id: locationId,
      }));

      const results = await db
        .insert(package_locations)
        .values(values)
        .returning();

      console.log(`✅ [PackageLocationService] Bulk added ${results.length} package location relationships`);
      return results;
    } catch (error) {
      console.error(`❌ [PackageLocationService] Error bulk adding packages to location:`, error);
      throw error;
    }
  }
}

export const packageLocationService = new PackageLocationService();
