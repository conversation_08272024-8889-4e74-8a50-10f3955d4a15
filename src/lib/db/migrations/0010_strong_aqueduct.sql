CREATE TABLE "customers" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"tenant_id" integer,
	"email" varchar(255) NOT NULL,
	"mobile_country_code" varchar(5),
	"mobile_number" varchar(30),
	"name" varchar(255),
	"date_of_birth" date,
	"gender" varchar(50),
	"pricing_group_id" varchar(255),
	"is_active" boolean DEFAULT true,
	"created_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updated_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT "customers_email_unique" UNIQUE("email")
);
--> statement-breakpoint
ALTER TABLE "customers" ADD CONSTRAINT "customers_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "customers" ADD CONSTRAINT "customers_pricing_group_id_pricing_groups_id_fk" FOREIGN KEY ("pricing_group_id") REFERENCES "public"."pricing_groups"("id") ON DELETE cascade ON UPDATE no action;