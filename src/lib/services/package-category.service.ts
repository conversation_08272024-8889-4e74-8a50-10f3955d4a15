import { sql, eq, and, ilike, SQL } from "drizzle-orm";
import { package_categories, type PackageCategory, type NewPackageCategory } from "@/lib/db/schema";
import { BaseService, CreateEntityData, UpdateEntityData } from "@/lib/core/base-service";
import { createId } from "@paralleldrive/cuid2";
import { db } from "@/lib/db";

// Package Category data transfer objects
export interface CreatePackageCategoryData extends CreateEntityData {
  tenantId: number;
  name: string;
  description?: string;
}

export interface UpdatePackageCategoryData extends UpdateEntityData {
  name?: string;
  description?: string;
}

export interface PackageCategoryFilters {
  search?: string;
  tenantId?: number;
}

export interface PackageCategoryStats {
  total: number;
  byTenant: Record<number, number>;
}

/**
 * PackageCategoryService - Package category management
 *
 * Features:
 * - CRUD operations for package categories
 * - Search and filtering
 * - Tenant-specific categories
 * - Statistics and analytics
 */
export class PackageCategoryService extends BaseService<PackageCategory, CreatePackageCategoryData, UpdatePackageCategoryData> {
  constructor() {
    super(package_categories, "PackageCategory");
  }

  /**
   * Create a new package category
   */
  async create(data: CreatePackageCategoryData): Promise<PackageCategory> {
    console.log(`📦 [PackageCategoryService] Creating package category:`, data);

    this.validateCreateData(data);

    const categoryData: NewPackageCategory = {
      id: createId(),
      tenantId: data.tenantId,
      name: data.name,
      description: data.description || null,
    };

    const [category] = await db.insert(package_categories).values(categoryData).returning();
    console.log(`✅ [PackageCategoryService] Package category created:`, category);
    return category;
  }

  /**
   * Update package category
   */
  async update(id: string, data: UpdatePackageCategoryData): Promise<PackageCategory> {
    console.log(`📦 [PackageCategoryService] Updating package category ${id}:`, data);

    this.validateUpdateData(data);

    const updateData: Partial<NewPackageCategory> = {};
    if (data.name !== undefined) updateData.name = data.name;
    if (data.description !== undefined) updateData.description = data.description;

    const [updatedCategory] = await db
      .update(package_categories)
      .set({
        ...updateData,
        updatedAt: new Date(),
      })
      .where(eq(package_categories.id, id))
      .returning();

    if (!updatedCategory) {
      throw new Error("Package category not found");
    }

    console.log(`✅ [PackageCategoryService] Package category updated:`, updatedCategory);
    return updatedCategory;
  }

  /**
   * Get package categories by tenant with filters
   */
  async getByTenant(tenantId: number, filters: PackageCategoryFilters = {}): Promise<PackageCategory[]> {
    console.log(`📦 [PackageCategoryService] Getting package categories for tenant ${tenantId}:`, filters);

    const conditions = [eq(package_categories.tenantId, tenantId)];

    if (filters.search) {
      conditions.push(
        sql`(${ilike(package_categories.name, `%${filters.search}%`)} OR ${ilike(package_categories.description, `%${filters.search}%`)})`
      );
    }

    const result = await db
      .select()
      .from(package_categories)
      .where(and(...conditions))
      .orderBy(package_categories.name);

    console.log(`✅ [PackageCategoryService] Found ${result.length} package categories`);
    return result;
  }

  /**
   * Get package category statistics
   */
  async getStats(tenantId?: number): Promise<PackageCategoryStats> {
    console.log(`📦 [PackageCategoryService] Getting package category statistics for tenant:`, tenantId);

    let whereClause = sql`1=1`;
    if (tenantId) {
      whereClause = sql`${package_categories.tenantId} = ${tenantId}`;
    }

    // Get total count
    const [totalCount] = await db
      .select({ count: sql<number>`count(*)` })
      .from(package_categories)
      .where(whereClause);

    // Get count by tenant
    const tenantCounts = await db
      .select({
        tenantId: package_categories.tenantId,
        count: sql<number>`count(*)`
      })
      .from(package_categories)
      .where(tenantId ? whereClause : sql`1=1`)
      .groupBy(package_categories.tenantId);

    const byTenant = tenantCounts.reduce((acc, { tenantId: tid, count }) => {
      acc[tid] = count;
      return acc;
    }, {} as Record<number, number>);

    const stats = {
      total: totalCount.count,
      byTenant,
    };

    console.log(`✅ [PackageCategoryService] Package category statistics:`, stats);
    return stats;
  }

  /**
   * Search package categories
   */
  async searchCategories(query: string, tenantId?: number): Promise<PackageCategory[]> {
    console.log(`📦 [PackageCategoryService] Searching package categories:`, { query, tenantId });

    return this.getByTenant(tenantId || 0, { search: query });
  }

  /**
   * Check if category name exists for tenant
   */
  async nameExists(name: string, tenantId: number, excludeId?: string): Promise<boolean> {
    const conditions = [
      eq(package_categories.tenantId, tenantId),
      ilike(package_categories.name, name)
    ];

    if (excludeId) {
      conditions.push(sql`${package_categories.id} != ${excludeId}`);
    }

    const [existing] = await db
      .select({ id: package_categories.id })
      .from(package_categories)
      .where(and(...conditions))
      .limit(1);

    return !!existing;
  }

  /**
   * Build filter conditions for search
   */
  protected buildFilterConditions(filters: Record<string, any>): SQL[] {
    const conditions: SQL[] = [];

    if (filters.search) {
      conditions.push(
        sql`(${ilike(package_categories.name, `%${filters.search}%`)} OR ${ilike(package_categories.description, `%${filters.search}%`)})`
      );
    }

    if (filters.tenantId) {
      conditions.push(eq(package_categories.tenantId, filters.tenantId));
    }

    return conditions;
  }

  /**
   * Validate create data
   */
  protected validateCreateData(data: CreatePackageCategoryData): void {
    if (!data.name?.trim()) {
      throw new Error("Package category name is required");
    }
    if (data.name.length > 255) {
      throw new Error("Package category name must be less than 255 characters");
    }
    if (data.description && data.description.length > 255) {
      throw new Error("Package category description must be less than 255 characters");
    }
    if (!data.tenantId || data.tenantId <= 0) {
      throw new Error("Valid tenant ID is required");
    }
  }

  /**
   * Validate update data
   */
  protected validateUpdateData(data: UpdatePackageCategoryData): void {
    if (data.name !== undefined && !data.name?.trim()) {
      throw new Error("Package category name cannot be empty");
    }
    if (data.name && data.name.length > 255) {
      throw new Error("Package category name must be less than 255 characters");
    }
    if (data.description && data.description.length > 255) {
      throw new Error("Package category description must be less than 255 characters");
    }
  }
}

// Export singleton instance
export const packageCategoryService = new PackageCategoryService();
