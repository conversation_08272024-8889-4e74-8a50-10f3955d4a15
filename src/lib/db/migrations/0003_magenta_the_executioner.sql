CREATE TABLE "addresses" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"tenant_id" integer,
	"address_line_1" varchar(255),
	"address_line_2" varchar(255),
	"city" varchar(255),
	"state" varchar(255),
	"zip" varchar(255),
	"country" varchar(255),
	"postal_code" varchar(255),
	"accepted_at" timestamp,
	"created_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL
);
--> statement-breakpoint
CREATE TABLE "business_profiles" (
	"id" serial PRIMARY KEY NOT NULL,
	"tenant_id" integer,
	"business_name" varchar(255) NOT NULL,
	"business_logo" text,
	"company_registered_name" varchar(255),
	"company_registered_no" varchar(255),
	"business_industry" varchar(255),
	"contact_email" varchar(30),
	"show_whatsapp_floating" boolean DEFAULT false NOT NULL,
	"created_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updated_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL
);
--> statement-breakpoint
CREATE TABLE "settings" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"tenant_id" integer,
	"currency" varchar(50),
	"time_zone" varchar(50),
	"accepted_at" timestamp,
	"created_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL
);
--> statement-breakpoint
ALTER TABLE "addresses" ADD CONSTRAINT "addresses_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "business_profiles" ADD CONSTRAINT "business_profiles_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "settings" ADD CONSTRAINT "settings_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;