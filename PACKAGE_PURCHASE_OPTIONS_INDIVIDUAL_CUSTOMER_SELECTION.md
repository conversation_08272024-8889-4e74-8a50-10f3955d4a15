# 👤 Package Purchase Options - Individual Customer Selection Implementation

## 🎉 **Apa yang Baru Kita Implementasikan?**

Halo teman-teman! Jadi gini, kita baru saja mengimplementasikan fitur **Individual Customer Selection** yang super canggih untuk Package Purchase Options! Ini adalah upgrade yang sangat powerful yang memungkinkan admin untuk melakukan targeting customer secara individual dan spesifik!

## 🔍 **Konsep Hybrid Targeting System**

### **🔴 Sebelum (Segment-based Only):**
```
Package targeting:
✅ VIP Members (45 customers) ← Semua VIP
✅ Downtown Customers (234 customers) ← Semua di Downtown
```

### **🟢 Sesudah (Hybrid Targeting System):**
```
Package targeting:
📊 Segment Targeting:
✅ VIP Members (45 customers)

PLUS

👤 Individual Targeting:
✅ Budi Santoso (<EMAIL>) - VIP Member
✅ Ani Wijaya (<EMAIL>) - Regular Member  
✅ Citra Dewi (<EMAIL>) - Student Member

Total: 48 customers (45 VIP + 3 individual)
```

## 🏗️ **Arsitektur Individual Customer Selection**

### **1. Individual Customer Selection Service**
**File**: `src/lib/services/individual-customer-selection.service.ts`

```typescript
class IndividualCustomerSelectionService {
  // Search customers dengan advanced filtering
  async searchCustomers(filters: CustomerSearchFilters): Promise<{
    customers: CustomerSearchResult[];
    total: number;
    hasMore: boolean;
  }>
  
  // Get customers by IDs (untuk display selected customers)
  async getCustomersByIds(customerIds: string[], tenantId: number): Promise<CustomerSearchResult[]>
  
  // Save individual customer targeting untuk package
  async saveIndividualCustomerTargeting(data: IndividualCustomerTargeting): Promise<void>
  
  // Get individual customer targeting untuk package
  async getIndividualCustomerTargeting(packageId: string, tenantId: number): Promise<CustomerSearchResult[]>
  
  // Get package targeting statistics
  async getPackageTargetingStats(packageId: string, tenantId: number): Promise<PackageCustomerTargetingStats>
}
```

### **2. TanStack Query Hooks**
**File**: `src/lib/hooks/queries/use-individual-customer-selection-queries.ts`

```typescript
// Main hooks
useCustomerSearch(filters: CustomerSearchFilters) // Search customers dengan pagination
useCustomersByIds(customerIds: string[], tenantId: number) // Get selected customers
useIndividualCustomerTargeting(packageId: string, tenantId: number) // Get package targeting
useSaveIndividualCustomerTargeting() // Save targeting mutation

// Advanced hooks
useCustomerSearchWithDebounce() // Search dengan debouncing
useInfiniteCustomerSearch() // Infinite scroll untuk large datasets
usePackageTargetingStats() // Get targeting statistics
```

### **3. Individual Customer Selector Component**
**File**: `src/components/forms/individual-customer-selector.tsx`

```typescript
<IndividualCustomerSelector
  tenantId={tenantId}
  selectedCustomerIds={selectedCustomerIds}
  onSelectionChange={onSelectionChange}
  maxSelections={100}
  placeholder="Search and select customers..."
/>
```

### **4. API Routes**
```
GET /api/customers/search?tenantId=1&search=budi
GET /api/customers/by-ids?tenantId=1&customerIds[]=123&customerIds[]=456
POST /api/packages/individual-customer-targeting
GET /api/packages/{id}/individual-customer-targeting?tenantId=1
GET /api/packages/{id}/targeting-stats?tenantId=1
```

**Note**: Routes menggunakan `[id]` parameter untuk konsistensi dengan existing Next.js dynamic routes.

## 🎯 **Fitur-fitur Individual Customer Selection**

### **1. Advanced Customer Search**
```typescript
// Search dengan multiple criteria
const searchFilters = {
  tenantId: 1,
  search: "budi", // Search by name or email
  isActive: true, // Filter by active status
  pricingGroupId: "vip123", // Filter by pricing group
  locationId: "downtown456", // Filter by location
  limit: 20,
  offset: 0
};
```

### **2. Rich Customer Information Display**
```typescript
interface CustomerSearchResult {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber?: string;
  isActive: boolean;
  pricingGroupName?: string; // "VIP Member", "Regular Member"
  locationName?: string; // "Downtown Fitness Center"
  membershipStatus: 'active' | 'inactive' | 'suspended';
  joinDate: Date;
  tenantId: number;
}
```

### **3. Searchable Multi-Select Dropdown**
- **Autocomplete functionality** dengan debouncing
- **Customer avatars** dengan initials
- **Rich information display** (name, email, pricing group, location)
- **Pagination support** untuk large customer lists
- **Real-time search** dengan minimum 2 characters

### **4. Selected Customers Management**
- **Visual customer cards** dengan avatar dan info
- **Easy removal** dengan X button
- **Animated additions/removals** dengan Framer Motion
- **Customer count display** (5/100 selected)
- **Maximum selection limit** (configurable)

## 🎨 **UI/UX Features**

### **1. Customer Search Interface**
```typescript
// Search dropdown dengan Command component
<Command>
  <CommandInput placeholder="Search customers by name or email..." />
  <CommandList>
    <CommandGroup>
      {customers.map(customer => (
        <CommandItem>
          <Avatar>{customer.initials}</Avatar>
          <div>
            <span>{customer.name}</span>
            <Badge>{customer.pricingGroup}</Badge>
            <div>{customer.email} • {customer.location}</div>
          </div>
        </CommandItem>
      ))}
    </CommandGroup>
  </CommandList>
</Command>
```

### **2. Selected Customers Display**
```typescript
// Animated customer cards
<AnimatePresence>
  {selectedCustomers.map(customer => (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.95 }}
    >
      <Avatar>{customer.initials}</Avatar>
      <div>{customer.name}</div>
      <Badge>{customer.pricingGroup}</Badge>
      <Button onClick={remove}>×</Button>
    </motion.div>
  ))}
</AnimatePresence>
```

### **3. Loading States & Error Handling**
- **Search loading** dengan spinner
- **Selected customers loading** dengan skeleton
- **Error states** dengan retry options
- **Empty states** dengan helpful messages

## 🔒 **Security & Tenant Isolation**

### **1. Tenant-Aware Customer Search**
```typescript
// Semua queries di-filter berdasarkan tenant
const conditions: SQL[] = [eq(customers.tenantId, tenantId)];

// Customer search results hanya untuk tenant tertentu
const customerResults = await db
  .select()
  .from(customers)
  .where(and(...conditions))
  .orderBy(customers.firstName, customers.lastName);
```

### **2. Individual Customer Targeting Validation**
```typescript
// Validate selected customers belong to tenant
const customerCheck = await db
  .select({ id: customers.id })
  .from(customers)
  .where(and(
    inArray(customers.id, selectedCustomerIds),
    eq(customers.tenantId, tenantId) // 🔒 Tenant validation
  ));

if (customerCheck.length !== selectedCustomerIds.length) {
  throw new Error("Some selected customers do not belong to the current tenant");
}
```

### **3. API Security**
```typescript
// Authentication required
const session = await auth();
if (!session?.user) {
  return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
}

// Tenant ID validation
if (!tenantId || tenantId <= 0) {
  return NextResponse.json(
    { error: "Valid tenant ID is required" },
    { status: 400 }
  );
}
```

## 🔄 **Data Flow Individual Customer Selection**

### **Package Creation dengan Individual Targeting:**
```
1. User buka form create package
2. User isi basic package info
3. User pilih segment targeting (VIP Members)
4. User search individual customers:
   - Type "budi" → API search customers
   - Select "Budi Santoso" → Add to selection
   - Type "ani" → API search customers  
   - Select "Ani Wijaya" → Add to selection
5. Submit form:
   - Create package
   - Create purchase options dengan segment targeting
   - Save individual customer targeting
6. Success animation!
```

### **Customer Search Flow:**
```
1. User type di search box (min 2 characters)
2. Debounced search (300ms delay)
3. API call: GET /api/customers/search?tenantId=1&search=budi
4. Display results dengan rich information
5. User click customer → Add to selection
6. Update form state dengan customer IDs
```

### **Package Targeting Resolution:**
```
1. System get package targeting:
   - Segment targeting: VIP Members (45 customers)
   - Individual targeting: [budi123, ani456, citra789]
2. Resolve total targeted customers:
   - VIP Members: 45 customers
   - Individual customers: 3 customers
   - Total: 48 customers (with potential overlap)
3. Display targeting summary to admin
```

## 📊 **Database Integration**

### **Existing Tables Used:**
```sql
-- Customers table (primary data source)
customers:
- id, tenantId, firstName, lastName, email
- pricingGroupId, locationId, isActive, createdAt

-- Package Customer Segments table (junction for individual targeting)
package_customer_segements:
- package_id (link to packages)
- customer_id (link to customers)

-- Pricing Groups & Locations (for rich display)
pricing_groups: name, description
locations: name, city, state
```

### **Individual Customer Targeting Storage:**
```sql
-- Individual targeting disimpan di package_customer_segements
INSERT INTO package_customer_segements (package_id, customer_id) VALUES
('package123', 'budi456'),
('package123', 'ani789'),
('package123', 'citra012');
```

## 🎯 **Business Benefits**

### **1. Granular Targeting Control**
- **Segment + Individual** targeting dalam satu package
- **Precise customer selection** untuk special offers
- **Flexible targeting options** untuk berbagai use cases

### **2. Enhanced User Experience**
- **Intuitive search interface** dengan autocomplete
- **Rich customer information** untuk informed decisions
- **Visual feedback** dengan animations dan loading states

### **3. Scalable Architecture**
- **Efficient search** dengan pagination dan debouncing
- **Optimized queries** dengan proper indexing
- **Tenant isolation** untuk multi-tenant security

### **4. Advanced Use Cases**
```
Use Case Examples:
✅ "VIP Package untuk semua VIP Members + 5 special customers"
✅ "Trial Package untuk New Customers + referral customers"
✅ "Corporate Package untuk existing corporate clients + new prospects"
✅ "Birthday Special untuk semua customers + birthday customers this month"
```

## 🧪 **Testing Scenarios**

### **✅ Functional Testing:**

1. **Customer Search:**
   ```
   - Search dengan nama: "budi" → Show Budi Santoso
   - Search dengan email: "ani@" → Show Ani Wijaya
   - Search dengan partial: "cit" → Show Citra Dewi
   - Empty search → Show placeholder
   ```

2. **Customer Selection:**
   ```
   - Select customer → Add to selection list
   - Select same customer → No duplicate
   - Remove customer → Remove from selection
   - Max selection limit → Disable search when reached
   ```

3. **Package Creation:**
   ```
   - Create package dengan segment + individual targeting
   - Verify both targeting types saved correctly
   - Edit package → Load existing individual targeting
   - Update targeting → Save changes correctly
   ```

### **✅ Security Testing:**

1. **Tenant Isolation:**
   ```
   - Login as Tenant A → Only see Tenant A customers
   - Search customers → Only Tenant A results
   - Select customers → Only Tenant A customers allowed
   ```

2. **Cross-Tenant Protection:**
   ```
   - Try to select customer from Tenant B → Validation error
   - API call dengan wrong tenant → 403 Forbidden
   - Package targeting → Only tenant customers accessible
   ```

## 🚀 **Performance Optimizations**

### **1. Search Optimization**
```typescript
// Debounced search untuk reduce API calls
const [debouncedSearchTerm, setDebouncedSearchTerm] = useState(searchTerm);
useEffect(() => {
  const timer = setTimeout(() => {
    setDebouncedSearchTerm(searchTerm);
  }, 300);
  return () => clearTimeout(timer);
}, [searchTerm]);
```

### **2. Query Optimization**
```typescript
// Efficient customer search dengan proper indexing
const customerResults = await db
  .select({
    // Select only needed fields
    id: customers.id,
    firstName: customers.firstName,
    lastName: customers.lastName,
    email: customers.email,
    // Join dengan pricing groups dan locations
    pricingGroupName: pricing_groups.name,
    locationName: locations.name,
  })
  .from(customers)
  .leftJoin(pricing_groups, eq(customers.pricingGroupId, pricing_groups.id))
  .leftJoin(locations, eq(customers.locationId, locations.id))
  .where(and(...conditions))
  .limit(20) // Pagination
  .offset(offset);
```

### **3. Caching Strategy**
```typescript
// TanStack Query caching
staleTime: 2 * 60 * 1000, // 2 minutes untuk search results
staleTime: 5 * 60 * 1000, // 5 minutes untuk selected customers
keepPreviousData: true, // Keep previous results while loading
```

## 🔧 **Troubleshooting: Dynamic Routes Conflict**

### **Masalah yang Ditemui:**
```
Error: You cannot use different slug names for the same dynamic path ('id' !== 'packageId').
```

### **Penyebab:**
Next.js tidak mengizinkan penggunaan parameter dynamic yang berbeda untuk path yang sama:
```
❌ Konflik:
src/app/api/packages/[id]/route.ts          ← Existing route
src/app/api/packages/[packageId]/route.ts   ← New route (KONFLIK!)
```

### **Solusi yang Diterapkan:**
1. **Konsistensi Parameter**: Gunakan `[id]` untuk semua dynamic routes packages
2. **Pindahkan Routes**: Pindahkan routes dari `[packageId]` ke `[id]`
3. **Update Parameter Names**: Update parameter destructuring di route handlers

```typescript
// ✅ Correct implementation
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id: packageId } = await params;
  // Use packageId for business logic
}
```

### **Final Structure:**
```
src/app/api/packages/
├── [id]/
│   ├── route.ts                           ← Existing package CRUD
│   ├── individual-customer-targeting/
│   │   └── route.ts                       ← Individual targeting
│   └── targeting-stats/
│       └── route.ts                       ← Targeting statistics
├── individual-customer-targeting/
│   └── route.ts                           ← Save targeting
└── route.ts                               ← Package list/create
```

## ✅ **Status: IMPLEMENTED & FIXED**

Individual Customer Selection untuk Package Purchase Options telah berhasil diimplementasikan dengan:

- ✅ **Advanced customer search** dengan autocomplete dan filtering
- ✅ **Rich UI/UX** dengan customer avatars, badges, dan animations
- ✅ **Hybrid targeting system** (segment + individual)
- ✅ **Tenant-aware architecture** dengan proper isolation
- ✅ **Performance optimized** dengan debouncing dan pagination
- ✅ **Comprehensive security** dengan validation di semua layer
- ✅ **Scalable architecture** untuk future enhancements
- ✅ **Dynamic routes conflict resolved** dengan konsistensi parameter

**Sistem targeting customer sekarang sangat powerful dan granular!** 🎉
