import { z } from "zod";

export class ValidationService {
  /**
   * Email validation with advanced rules
   */
  static validateEmail(email: string): { valid: boolean; error?: string } {
    const emailRegex = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
    
    if (!emailRegex.test(email)) {
      return { valid: false, error: "Invalid email format" };
    }

    // Check for common disposable email domains
    const disposableDomains = [
      "10minutemail.com", "tempmail.org", "guerrillamail.com", 
      "mailinator.com", "throwaway.email"
    ];
    
    const domain = email.split('@')[1]?.toLowerCase();
    if (disposableDomains.includes(domain)) {
      return { valid: false, error: "Disposable email addresses are not allowed" };
    }

    return { valid: true };
  }

  /**
   * Phone number validation with international support
   */
  static validatePhoneNumber(phone: string, country?: string): { valid: boolean; error?: string; formatted?: string } {
    // Remove all non-digit characters except +
    const cleaned = phone.replace(/[^\d+]/g, '');
    
    // Basic international format validation
    const internationalRegex = /^\+[1-9]\d{1,14}$/;
    
    if (!internationalRegex.test(cleaned)) {
      return { 
        valid: false, 
        error: "Phone number must be in international format (e.g., +1234567890)" 
      };
    }

    // Country-specific validation
    const countryPatterns: Record<string, { pattern: RegExp; format: string }> = {
      US: { pattern: /^\+1[2-9]\d{2}[2-9]\d{2}\d{4}$/, format: "+1 (XXX) XXX-XXXX" },
      GB: { pattern: /^\+44[1-9]\d{8,9}$/, format: "+44 XXXX XXXXXX" },
      DE: { pattern: /^\+49[1-9]\d{10,11}$/, format: "+49 XXX XXXXXXXX" },
      FR: { pattern: /^\+33[1-9]\d{8}$/, format: "+33 X XX XX XX XX" },
      AU: { pattern: /^\+61[2-9]\d{8}$/, format: "+61 X XXXX XXXX" },
      CA: { pattern: /^\+1[2-9]\d{2}[2-9]\d{2}\d{4}$/, format: "+1 (XXX) XXX-XXXX" },
    };

    if (country && countryPatterns[country]) {
      const pattern = countryPatterns[country];
      if (!pattern.pattern.test(cleaned)) {
        return { 
          valid: false, 
          error: `Invalid phone number format for ${country}. Expected: ${pattern.format}` 
        };
      }
    }

    return { valid: true, formatted: cleaned };
  }

  /**
   * Address validation
   */
  static validateAddress(address: {
    addressLine1?: string;
    city?: string;
    state?: string;
    country?: string;
    postalCode?: string;
  }): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validate postal code based on country
    if (address.postalCode && address.country) {
      const postalValidation = this.validatePostalCode(address.postalCode, address.country);
      if (!postalValidation.valid) {
        errors.push(postalValidation.error!);
      }
    }

    // Basic address line validation
    if (address.addressLine1 && address.addressLine1.length < 5) {
      errors.push("Address line 1 must be at least 5 characters long");
    }

    // City validation
    if (address.city && !/^[a-zA-Z\s\-'\.]+$/.test(address.city)) {
      errors.push("City name contains invalid characters");
    }

    // State validation (basic)
    if (address.state && address.state.length < 2) {
      errors.push("State must be at least 2 characters long");
    }

    return { valid: errors.length === 0, errors };
  }

  /**
   * Postal code validation by country
   */
  static validatePostalCode(postalCode: string, country: string): { valid: boolean; error?: string } {
    const patterns: Record<string, { pattern: RegExp; example: string }> = {
      US: { pattern: /^\d{5}(-\d{4})?$/, example: "12345 or 12345-6789" },
      CA: { pattern: /^[A-Z]\d[A-Z] \d[A-Z]\d$/, example: "A1A 1A1" },
      GB: { pattern: /^[A-Z]{1,2}\d[A-Z\d]? \d[A-Z]{2}$/i, example: "SW1A 1AA" },
      DE: { pattern: /^\d{5}$/, example: "12345" },
      FR: { pattern: /^\d{5}$/, example: "75001" },
      AU: { pattern: /^\d{4}$/, example: "2000" },
      JP: { pattern: /^\d{3}-\d{4}$/, example: "123-4567" },
      IN: { pattern: /^\d{6}$/, example: "110001" },
      BR: { pattern: /^\d{5}-?\d{3}$/, example: "01234-567" },
      MX: { pattern: /^\d{5}$/, example: "12345" },
    };

    const countryPattern = patterns[country.toUpperCase()];
    if (!countryPattern) {
      return { valid: true }; // Allow if no pattern defined
    }

    if (!countryPattern.pattern.test(postalCode)) {
      return { 
        valid: false, 
        error: `Invalid postal code format for ${country}. Expected format: ${countryPattern.example}` 
      };
    }

    return { valid: true };
  }

  /**
   * Business registration number validation
   */
  static validateBusinessRegistration(regNumber: string, country: string): { valid: boolean; error?: string } {
    const patterns: Record<string, { pattern: RegExp; name: string }> = {
      US: { pattern: /^\d{2}-\d{7}$/, name: "EIN (XX-XXXXXXX)" },
      GB: { pattern: /^\d{8}$/, name: "Company Number (8 digits)" },
      DE: { pattern: /^HRB \d+$/, name: "Handelsregister (HRB XXXXX)" },
      FR: { pattern: /^\d{9}$/, name: "SIREN (9 digits)" },
      AU: { pattern: /^\d{3} \d{3} \d{3}$/, name: "ACN (XXX XXX XXX)" },
      CA: { pattern: /^\d{9}$/, name: "Business Number (9 digits)" },
    };

    const countryPattern = patterns[country.toUpperCase()];
    if (!countryPattern) {
      return { valid: true }; // Allow if no pattern defined
    }

    if (!countryPattern.pattern.test(regNumber)) {
      return { 
        valid: false, 
        error: `Invalid business registration format for ${country}. Expected: ${countryPattern.name}` 
      };
    }

    return { valid: true };
  }

  /**
   * Website URL validation
   */
  static validateWebsiteUrl(url: string): { valid: boolean; error?: string; normalized?: string } {
    try {
      // Add protocol if missing
      let normalizedUrl = url;
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        normalizedUrl = `https://${url}`;
      }

      const urlObj = new URL(normalizedUrl);
      
      // Check for valid protocols
      if (!['http:', 'https:'].includes(urlObj.protocol)) {
        return { valid: false, error: "URL must use HTTP or HTTPS protocol" };
      }

      // Check for valid hostname
      if (!urlObj.hostname || urlObj.hostname.length < 3) {
        return { valid: false, error: "Invalid hostname" };
      }

      // Check for localhost in production
      if (process.env.NODE_ENV === 'production' && 
          (urlObj.hostname === 'localhost' || urlObj.hostname.startsWith('127.'))) {
        return { valid: false, error: "Localhost URLs are not allowed in production" };
      }

      return { valid: true, normalized: normalizedUrl };
    } catch {
      return { valid: false, error: "Invalid URL format" };
    }
  }

  /**
   * Business name validation
   */
  static validateBusinessName(name: string): { valid: boolean; error?: string } {
    // Check length
    if (name.length < 2) {
      return { valid: false, error: "Business name must be at least 2 characters long" };
    }

    if (name.length > 255) {
      return { valid: false, error: "Business name must be less than 255 characters" };
    }

    // Check for valid characters (letters, numbers, spaces, common punctuation)
    const validPattern = /^[a-zA-Z0-9\s\-'&.,()]+$/;
    if (!validPattern.test(name)) {
      return { valid: false, error: "Business name contains invalid characters" };
    }

    // Check for reserved words
    const reservedWords = ['admin', 'api', 'www', 'mail', 'support', 'help', 'test'];
    const lowerName = name.toLowerCase();
    if (reservedWords.some(word => lowerName.includes(word))) {
      return { valid: false, error: "Business name cannot contain reserved words" };
    }

    return { valid: true };
  }

  /**
   * Comprehensive validation for business profile
   */
  static validateBusinessProfile(data: {
    business_name: string;
    business_website?: string;
    whatsapp_number?: string;
    company_registered_name?: string;
  }): { valid: boolean; errors: Record<string, string> } {
    const errors: Record<string, string> = {};

    // Validate business name
    const nameValidation = this.validateBusinessName(data.business_name);
    if (!nameValidation.valid) {
      errors.business_name = nameValidation.error!;
    }

    // Validate website if provided
    if (data.business_website) {
      const websiteValidation = this.validateWebsiteUrl(data.business_website);
      if (!websiteValidation.valid) {
        errors.business_website = websiteValidation.error!;
      }
    }

    // Validate WhatsApp number if provided
    if (data.whatsapp_number) {
      const phoneValidation = this.validatePhoneNumber(data.whatsapp_number);
      if (!phoneValidation.valid) {
        errors.whatsapp_number = phoneValidation.error!;
      }
    }

    // Validate registered name if provided
    if (data.company_registered_name) {
      const regNameValidation = this.validateBusinessName(data.company_registered_name);
      if (!regNameValidation.valid) {
        errors.company_registered_name = regNameValidation.error!;
      }
    }

    return { valid: Object.keys(errors).length === 0, errors };
  }

  /**
   * Sanitize input to prevent XSS
   */
  static sanitizeInput(input: string): string {
    return input
      .replace(/[<>]/g, '') // Remove < and >
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/on\w+=/gi, '') // Remove event handlers
      .trim();
  }

  /**
   * Validate file upload
   */
  static validateFileUpload(file: File, options: {
    maxSize?: number;
    allowedTypes?: string[];
    maxDimensions?: { width: number; height: number };
  } = {}): { valid: boolean; error?: string } {
    const {
      maxSize = 2 * 1024 * 1024, // 2MB default
      allowedTypes = ['image/jpeg', 'image/png', 'image/webp'],
    } = options;

    // Check file size
    if (file.size > maxSize) {
      return { 
        valid: false, 
        error: `File size must be less than ${Math.round(maxSize / 1024 / 1024)}MB` 
      };
    }

    // Check file type
    if (!allowedTypes.includes(file.type)) {
      return { 
        valid: false, 
        error: `File type not supported. Allowed types: ${allowedTypes.join(', ')}` 
      };
    }

    return { valid: true };
  }
}
