import { NextRequest, NextResponse } from "next/server";
import { VoucherService } from "@/lib/services/voucher.service";
import { z } from "zod";
import { withRBAC } from "@/lib/middleware/rbac-middleware";

// Validation schema for voucher validation request
const validateVoucherSchema = z.object({
  code: z.string().min(1, "Voucher code is required"),
  customerId: z.string().min(1, "Customer ID is required"),
  tenantId: z.number().int().positive("Tenant ID is required"),
  orderAmount: z.number().positive("Order amount must be positive"),
  context: z.object({
    package_id: z.string().optional(),
    class_id: z.string().optional(),
    location_id: z.string().optional(),
  }).optional().default({}),
});

// POST /api/vouchers/validate - Validate voucher for use
export async function POST(request: NextRequest) {
  return withRBAC(
    async () => {
      try {
        const body = await request.json();
        
        // Validate request body
        const validatedData = validateVoucherSchema.parse(body);
        const { code, customerId, tenantId, orderAmount, context } = validatedData;

        // Validate voucher
        const validationResult = await VoucherService.validateVoucherForUse(
          code,
          customerId,
          tenantId,
          orderAmount,
          context
        );

        if (!validationResult.is_valid) {
          return NextResponse.json({
            success: false,
            data: validationResult,
            message: validationResult.error_message || "Voucher is not valid",
          }, { status: 400 });
        }

        return NextResponse.json({
          success: true,
          data: validationResult,
          message: "Voucher is valid",
        });
      } catch (error) {
        console.error("Error validating voucher:", error);
        
        
        if (error instanceof z.ZodError) {
          return NextResponse.json(
            { 
              error: "Validation failed",
              details: error.errors,
              success: false 
            },
            { status: 400 }
          );
        }

        return NextResponse.json(
          { 
            error: error instanceof Error ? error.message : "Failed to validate voucher",
            success: false 
          },
          { status: 500 }
        );
      }
    },
    { module: "vouchers", action: "use" }
  )(request);
}
