import { NextRequest, NextResponse } from "next/server";
import { withCORS, getCORSConfig, applyCORSHeaders } from '@/lib/security/cors.config';
import { ClassScheduleService } from "@/lib/services/class-schedule.service";
import { z } from "zod";

/**
 * Public Class Schedules API v1 Route
 *
 * Public endpoint untuk mengambil class schedules dengan API key authentication.
 * Endpoint ini include field name dan description yang baru ditambahkan.
 *
 * Mengapa dibuat endpoint v1 terpisah?
 * - Versioning untuk backward compatibility
 * - API key authentication untuk security
 * - Include field baru (name, description) dalam response
 * - Rate limiting dan audit logging
 */

// Simple API key authentication function
async function authenticateApiKey(request: NextRequest) {
  // Try both X-API-Key and x-api-key headers (case-insensitive)
  const apiKey = request.headers.get('X-API-Key') ||
                 request.headers.get('x-api-key') ||
                 request.headers.get('X-Api-Key');

  // For now, use the same API key validation as package-pricing
  const validApiKey = 'pk_9b4d8a5f3e9ec298be21ac0589d0346d0289f717d9ca3841093498ef4530c64b';

  console.log('🔍 API Key Debug:', {
    'X-API-Key': request.headers.get('X-API-Key'),
    'x-api-key': request.headers.get('x-api-key'),
    'X-Api-Key': request.headers.get('X-Api-Key'),
    'resolved': apiKey ? 'found' : 'not found'
  });

  if (!apiKey) {
    return {
      success: false,
      error: 'Missing API key. Please provide X-API-Key or x-api-key header.'
    };
  }

  if (apiKey !== validApiKey) {
    return {
      success: false,
      error: 'Invalid API key'
    };
  }

  return {
    success: true
  };
}

// Helper function untuk add CORS headers
function addCORSHeaders(response: NextResponse, request?: NextRequest): NextResponse {
  const origin = request?.headers.get('origin');
  
  // Debug logging
  console.log('🔍 CORS Debug:', {
    origin,
    method: request?.method,
    userAgent: request?.headers.get('user-agent')?.substring(0, 50),
    timestamp: new Date().toISOString()
  });
  
  const allowedOrigins = [
    'http://localhost:3000',
    'http://localhost:3001',
    'http://localhost:3002',
    'http://localhost:3003',
    'https://localhost:3000',
    'https://localhost:3001',
    'https://localhost:3002',
    'https://localhost:3003'
  ];
  
  // Set origin
  if (origin && allowedOrigins.includes(origin)) {
    response.headers.set('Access-Control-Allow-Origin', origin);
  } else if (!origin) {
    response.headers.set('Access-Control-Allow-Origin', 'http://localhost:3002');
  } else {
    // For development, allow unknown origins
    response.headers.set('Access-Control-Allow-Origin', origin);
  }
  
  response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-API-Key, x-api-key, X-Tenant-ID, Accept, Origin, X-Requested-With');
  response.headers.set('Access-Control-Allow-Credentials', 'true');
  
  console.log('📤 CORS Headers set:', {
    'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
    'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers')
  });
  
  return response;
}

// Schema untuk validasi query parameters
const ClassScheduleFiltersSchema = z.object({
  tenantId: z.string().transform((val) => {
    const parsed = parseInt(val, 10);
    if (isNaN(parsed) || parsed <= 0) {
      throw new Error("Invalid tenant ID");
    }
    return parsed;
  }),
  classId: z.string().optional(),
  locationId: z.string().optional(),
  facilityId: z.string().optional(),
  staffId: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  search: z.string().optional(),
  is_private: z.string().optional().transform((val) => {
    if (val === undefined || val === null) return undefined;
    if (val === "true") return true;
    if (val === "false") return false;
    throw new Error("Invalid is_private parameter. Must be 'true' or 'false'");
  }),
});

// Schema untuk pagination
const PaginationSchema = z.object({
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(20),
  offset: z.number().int().min(0).default(0),
});

// Combined schema untuk request validation
const combinedSchema = z.object({
  // Class schedule filters
  tenantId: z.string().transform((val) => {
    const parsed = parseInt(val, 10);
    if (isNaN(parsed) || parsed <= 0) {
      throw new Error("Invalid tenant ID");
    }
    return parsed;
  }),
  classId: z.string().optional(),
  locationId: z.string().optional(),
  facilityId: z.string().optional(),
  staffId: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  search: z.string().optional(),
  is_private: z.string().optional().transform((val) => {
    if (val === undefined || val === null) return undefined;
    if (val === "true") return true;
    if (val === "false") return false;
    throw new Error("Invalid is_private parameter. Must be 'true' or 'false'");
  }),
  enhanced: z.string().optional().transform((val) => {
    if (val === undefined || val === null) return false;
    if (val === "true") return true;
    if (val === "false") return false;
    throw new Error("Invalid enhanced parameter. Must be 'true' or 'false'");
  }),

  // Pagination
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(20),
  offset: z.number().int().min(0).default(0),
});

// Handle CORS preflight
export async function OPTIONS(request: NextRequest) {
  console.log('🔍 OPTIONS request received for class-schedules v1');
  
  const origin = request.headers.get('origin');
  const requestHeaders = request.headers.get('access-control-request-headers');
  
  console.log('🔍 Preflight request details:', {
    origin,
    requestHeaders,
    method: request.method
  });
  
  const response = new NextResponse(null, { status: 200 });
  
  // Set CORS headers manually untuk memastikan semua header yang diperlukan diizinkan
  const allowedOrigins = [
    'http://localhost:3000',
    'http://localhost:3001',
    'http://localhost:3002',
    'http://localhost:3003',
    'https://localhost:3000',
    'https://localhost:3001',
    'https://localhost:3002',
    'https://localhost:3003'
  ];
  
  // Set origin
  if (origin && allowedOrigins.includes(origin)) {
    response.headers.set('Access-Control-Allow-Origin', origin);
  } else if (!origin) {
    response.headers.set('Access-Control-Allow-Origin', 'http://localhost:3002');
  } else {
    // For development, allow unknown origins
    response.headers.set('Access-Control-Allow-Origin', origin);
  }
  
  // Set methods
  response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  
  // Set headers - include both X-API-Key and x-api-key untuk case-insensitive support
  const allowedHeaders = [
    'Content-Type',
    'Authorization',
    'X-API-Key',
    'x-api-key',
    'X-Tenant-ID',
    'Accept',
    'Origin',
    'X-Requested-With'
  ];
  
  // If client requested specific headers, include those too
  if (requestHeaders) {
    const requestedHeaders = requestHeaders.split(',').map(h => h.trim());
    requestedHeaders.forEach(header => {
      if (!allowedHeaders.some(h => h.toLowerCase() === header.toLowerCase())) {
        allowedHeaders.push(header);
      }
    });
  }
  
  response.headers.set('Access-Control-Allow-Headers', allowedHeaders.join(', '));
  response.headers.set('Access-Control-Allow-Credentials', 'true');
  response.headers.set('Access-Control-Max-Age', '86400');
  
  console.log('📤 OPTIONS response headers:', {
    'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
    'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
    'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers'),
    'Access-Control-Allow-Credentials': response.headers.get('Access-Control-Allow-Credentials')
  });
  
  return response;
}

/**
 * GET /api/public/v1/class-schedules
 *
 * Public endpoint untuk mengambil class schedules dengan API key authentication.
 * Include field name dan description dalam response.
 * Dengan API key, endpoint ini bisa mengakses private schedules juga.
 *
 * Query Parameters:
 * - tenantId: required, tenant ID untuk isolation
 * - classId: optional, filter berdasarkan class
 * - locationId: optional, filter berdasarkan location
 * - facilityId: optional, filter berdasarkan facility
 * - staffId: optional, filter berdasarkan instructor
 * - startDate: optional, filter berdasarkan start date (YYYY-MM-DD)
 * - endDate: optional, filter berdasarkan end date (YYYY-MM-DD)
 * - search: optional, search berdasarkan nama class atau instructor
 * - is_private: optional, filter berdasarkan status private (true/false, default: all)
 * - page: optional, page number (default: 1)
 * - limit: optional, items per page (default: 20, max: 100)
 *
 * Headers:
 * - X-API-Key: required, API key untuk authentication
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate API key
    const authResult = await authenticateApiKey(request);
    if (!authResult.success) {
      const errorResponse = NextResponse.json(
        {
          error: authResult.error,
          success: false
        },
        { status: 401 }
      );
      
      return addCORSHeaders(errorResponse, request);
    }

    const { searchParams } = new URL(request.url);
    
    // Convert URLSearchParams to plain object untuk validation
    const params: Record<string, any> = {};
    searchParams.forEach((value, key) => {
      // Convert numeric parameters
      if (key === 'page' || key === 'limit' || key === 'offset') {
        const numValue = parseInt(value, 10);
        if (!isNaN(numValue)) {
          params[key] = numValue;
        }
      } else {
        params[key] = value;
      }
    });

    // Set defaults for pagination
    if (!params.page) params.page = 1;
    if (!params.limit) params.limit = 20;
    if (!params.offset) params.offset = (params.page - 1) * params.limit;

    // Validate query parameters
    const validatedParams = combinedSchema.parse(params);

    // Check if enhanced response is requested
    const enhanced = validatedParams.enhanced === true;

    // Call service method - use enhanced method if requested
    const result = enhanced
      ? await ClassScheduleService.searchPublicSchedulesEnhanced(
          validatedParams.tenantId,
          validatedParams.classId,
          validatedParams.locationId,
          validatedParams.facilityId,
          validatedParams.staffId,
          validatedParams.startDate,
          validatedParams.endDate,
          validatedParams.search,
          validatedParams.limit,
          validatedParams.offset
        )
      : await ClassScheduleService.searchSchedules(
          validatedParams.tenantId,
          validatedParams.classId,
          validatedParams.locationId,
          validatedParams.facilityId,
          validatedParams.staffId,
          validatedParams.startDate,
          validatedParams.endDate,
          validatedParams.search,
          validatedParams.is_private,
          validatedParams.limit,
          validatedParams.offset
        );

    // Format response dengan metadata pagination
    const response = {
      success: true,
      data: {
        schedules: result.schedules,
        pagination: {
          page: validatedParams.page,
          limit: validatedParams.limit,
          total: result.total,
          totalPages: Math.ceil(result.total / validatedParams.limit),
          hasMore: result.hasMore,
        }
      },
      meta: {
        timestamp: new Date().toISOString(),
        version: "v1",
        endpoint: "/api/public/v1/class-schedules"
      }
    };

    const jsonResponse = NextResponse.json(response);
    return addCORSHeaders(jsonResponse, request);
  } catch (error) {
    console.error("Error fetching public class schedules v1:", error);

    // Handle validation errors
    if (error instanceof z.ZodError) {
      const errorResponse = NextResponse.json(
        {
          error: "Invalid parameters",
          details: error.errors,
          success: false
        },
        { status: 400 }
      );
      
      return addCORSHeaders(errorResponse, request);
    }

    // Handle custom validation errors (dari transform)
    if (error instanceof Error && error.message.includes("Invalid is_private parameter")) {
      const errorResponse = NextResponse.json(
        {
          error: error.message,
          success: false
        },
        { status: 400 }
      );
      
      return addCORSHeaders(errorResponse, request);
    }

    // Handle other errors
    const errorResponse = NextResponse.json(
      {
        error: error instanceof Error ? error.message : "Failed to fetch class schedules",
        success: false
      },
      { status: 500 }
    );
    
    return addCORSHeaders(errorResponse, request);
  }
}
