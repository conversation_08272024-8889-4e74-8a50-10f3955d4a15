# Customer OAuth Deployment Guide

## Overview

Production deployment guide for the Customer Google OAuth authentication system with security best practices and monitoring setup.

## Environment Configuration

### Required Environment Variables

```bash
# Database
DATABASE_URL="postgresql://user:password@host:port/database"

# JWT Configuration
JWT_SECRET="your-super-secure-jwt-secret-minimum-32-characters"
JWT_ISSUER="your-app-name"
JWT_AUDIENCE="customer"
JWT_ACCESS_TOKEN_EXPIRY="1h"
JWT_REFRESH_TOKEN_EXPIRY="30d"

# Google OAuth
GOOGLE_CLIENT_ID="your-google-client-id.apps.googleusercontent.com"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# Application URLs
NEXTAUTH_URL="https://api.yourdomain.com"
FRONTEND_URL="https://app.yourdomain.com"
ADMIN_URL="https://admin.yourdomain.com"

# Mobile Deep Links
MOBILE_DEEP_LINK_SCHEME="yourapp"

# Security
NODE_ENV="production"
CORS_ORIGIN="https://app.yourdomain.com,https://admin.yourdomain.com"

# Monitoring (Optional)
SENTRY_DSN="your-sentry-dsn"
LOG_LEVEL="info"
```

### Google OAuth Setup

1. **Create Google Cloud Project**
   ```bash
   # Go to Google Cloud Console
   # Create new project or select existing
   # Enable Google+ API and Google OAuth2 API
   ```

2. **Configure OAuth Consent Screen**
   ```
   Application Type: Web Application
   Application Name: Your App Name
   User Support Email: <EMAIL>
   Developer Contact: <EMAIL>
   
   Scopes:
   - openid
   - email
   - profile
   
   Test Users: (for development)
   - Add test email addresses
   ```

3. **Create OAuth 2.0 Credentials**
   ```
   Application Type: Web Application
   Name: Your App OAuth Client
   
   Authorized JavaScript Origins:
   - https://app.yourdomain.com
   - https://admin.yourdomain.com
   
   Authorized Redirect URIs:
   - https://api.yourdomain.com/api/auth/customer/google/callback
   - https://app.yourdomain.com/auth/callback
   ```
****
## Database Migration

### Production Migration

```bash
# 1. Backup existing database
pg_dump $DATABASE_URL > backup_$(date +%Y%m%d_%H%M%S).sql

# 2. Run migrations
npm run db:migrate

# 3. Verify migration
npm run db:status

# 4. Test critical paths
npm run test:integration
```

### Migration Rollback Plan

```bash
# If migration fails, rollback:
psql $DATABASE_URL < backup_YYYYMMDD_HHMMSS.sql

# Or use migration rollback:
npm run db:rollback
```

## Security Checklist

### Pre-Deployment Security

- [ ] **JWT Secret**: Use cryptographically secure random string (32+ chars)
- [ ] **Database**: Use SSL/TLS connection with certificate validation
- [ ] **CORS**: Configure strict origin allowlist
- [ ] **Rate Limiting**: Verify rate limits are appropriate for production load
- [ ] **Input Validation**: All inputs validated and sanitized
- [ ] **Error Handling**: No sensitive data exposed in error messages
- [ ] **Logging**: Audit logs configured for security events
- [ ] **HTTPS**: All endpoints use HTTPS with valid certificates
- [ ] **Headers**: Security headers configured (HSTS, CSP, etc.)

### Google OAuth Security

- [ ] **Client Secret**: Stored securely, not in code
- [ ] **Redirect URIs**: Only production URLs whitelisted
- [ ] **Scopes**: Minimal required scopes only
- [ ] **State Parameter**: CSRF protection enabled
- [ ] **PKCE**: Enabled for mobile clients

## Deployment Steps

### 1. Infrastructure Setup

```bash
# Using Docker
docker build -t customer-oauth-api .
docker run -d \
  --name customer-oauth-api \
  -p 3000:3000 \
  --env-file .env.production \
  customer-oauth-api

# Using PM2
npm install -g pm2
pm2 start ecosystem.config.js --env production
```

### 2. Load Balancer Configuration

```nginx
# Nginx configuration
upstream api_backend {
    server 127.0.0.1:3000;
    server 127.0.0.1:3001;
    server 127.0.0.1:3002;
}

server {
    listen 443 ssl http2;
    server_name api.yourdomain.com;

    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;

    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-Frame-Options DENY always;
    add_header X-XSS-Protection "1; mode=block" always;

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=auth:10m rate=10r/m;
    limit_req_zone $binary_remote_addr zone=api:10m rate=100r/m;

    location /api/auth/customer {
        limit_req zone=auth burst=5 nodelay;
        proxy_pass http://api_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /api {
        limit_req zone=api burst=20 nodelay;
        proxy_pass http://api_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 3. Database Configuration

```sql
-- Create production database user
CREATE USER oauth_api WITH PASSWORD 'secure-password';

-- Grant minimal required permissions
GRANT CONNECT ON DATABASE production_db TO oauth_api;
GRANT USAGE ON SCHEMA public TO oauth_api;
GRANT SELECT, INSERT, UPDATE, DELETE ON ALL TABLES IN SCHEMA public TO oauth_api;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO oauth_api;

-- Enable connection pooling
ALTER SYSTEM SET max_connections = 200;
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
```

## Monitoring & Observability

### Health Checks

```typescript
// app/api/health/route.ts
import { NextResponse } from 'next/server';
import { db } from '@/lib/db';

export async function GET() {
  try {
    // Check database connection
    await db.execute('SELECT 1');
    
    // Check Google OAuth configuration
    const hasGoogleConfig = !!(
      process.env.GOOGLE_CLIENT_ID && 
      process.env.GOOGLE_CLIENT_SECRET
    );

    return NextResponse.json({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      checks: {
        database: 'ok',
        googleOAuth: hasGoogleConfig ? 'ok' : 'error',
      },
    });
  } catch (error) {
    return NextResponse.json(
      {
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: 'Database connection failed',
      },
      { status: 503 }
    );
  }
}
```

### Metrics Collection

```typescript
// lib/monitoring/metrics.ts
import { createPrometheusMetrics } from 'prom-client';

export const metrics = {
  authAttempts: new Counter({
    name: 'auth_attempts_total',
    help: 'Total authentication attempts',
    labelNames: ['method', 'status', 'tenant_id'],
  }),
  
  tokenValidations: new Counter({
    name: 'token_validations_total',
    help: 'Total token validations',
    labelNames: ['status', 'error_code'],
  }),
  
  oauthFlows: new Counter({
    name: 'oauth_flows_total',
    help: 'Total OAuth flows initiated',
    labelNames: ['client_type', 'status'],
  }),
  
  responseTime: new Histogram({
    name: 'http_request_duration_seconds',
    help: 'HTTP request duration in seconds',
    labelNames: ['method', 'route', 'status_code'],
    buckets: [0.1, 0.5, 1, 2, 5],
  }),
};
```

### Logging Configuration

```typescript
// lib/logging/logger.ts
import winston from 'winston';

export const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: {
    service: 'customer-oauth-api',
    environment: process.env.NODE_ENV,
  },
  transports: [
    new winston.transports.File({
      filename: 'logs/error.log',
      level: 'error',
    }),
    new winston.transports.File({
      filename: 'logs/combined.log',
    }),
    new winston.transports.Console({
      format: winston.format.simple(),
    }),
  ],
});

// Security event logging
export function logSecurityEvent(event: {
  type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  customerId?: string;
  tenantId?: number;
  ipAddress: string;
  details: Record<string, any>;
}) {
  logger.warn('Security Event', {
    ...event,
    timestamp: new Date().toISOString(),
  });
}
```

## Performance Optimization

### Database Optimization

```sql
-- Create indexes for performance
CREATE INDEX CONCURRENTLY idx_customer_sessions_customer_tenant 
ON customer_sessions(customer_id, tenant_id) 
WHERE is_active = true;

CREATE INDEX CONCURRENTLY idx_customer_sessions_expires_at 
ON customer_sessions(expires_at) 
WHERE is_active = true;

CREATE INDEX CONCURRENTLY idx_customer_auth_logs_created_at 
ON customer_auth_logs(created_at);

CREATE INDEX CONCURRENTLY idx_customer_oauth_challenges_state 
ON customer_oauth_challenges(state) 
WHERE expires_at > NOW();

-- Analyze tables
ANALYZE customer_sessions;
ANALYZE customer_auth_logs;
ANALYZE customer_oauth_challenges;
```

### Caching Strategy

```typescript
// lib/cache/redis.ts
import Redis from 'ioredis';

const redis = new Redis(process.env.REDIS_URL);

export const cache = {
  // Cache OAuth states (10 minutes)
  async setOAuthState(state: string, data: any) {
    await redis.setex(`oauth:state:${state}`, 600, JSON.stringify(data));
  },

  async getOAuthState(state: string) {
    const data = await redis.get(`oauth:state:${state}`);
    return data ? JSON.parse(data) : null;
  },

  // Cache customer sessions (1 hour)
  async setCustomerSession(sessionId: string, data: any) {
    await redis.setex(`session:${sessionId}`, 3600, JSON.stringify(data));
  },

  async getCustomerSession(sessionId: string) {
    const data = await redis.get(`session:${sessionId}`);
    return data ? JSON.parse(data) : null;
  },
};
```

## Backup & Recovery

### Automated Backups

```bash
#!/bin/bash
# backup-script.sh

DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/backups"
DB_NAME="production_db"

# Create database backup
pg_dump $DATABASE_URL > $BACKUP_DIR/db_backup_$DATE.sql

# Compress backup
gzip $BACKUP_DIR/db_backup_$DATE.sql

# Upload to S3 (optional)
aws s3 cp $BACKUP_DIR/db_backup_$DATE.sql.gz s3://your-backup-bucket/

# Clean old backups (keep 30 days)
find $BACKUP_DIR -name "db_backup_*.sql.gz" -mtime +30 -delete

echo "Backup completed: db_backup_$DATE.sql.gz"
```

### Recovery Procedures

```bash
# 1. Stop application
pm2 stop customer-oauth-api

# 2. Restore database
gunzip -c backup_file.sql.gz | psql $DATABASE_URL

# 3. Verify data integrity
npm run db:verify

# 4. Start application
pm2 start customer-oauth-api

# 5. Run health checks
curl https://api.yourdomain.com/api/health
```

## Post-Deployment Verification

### Smoke Tests

```bash
#!/bin/bash
# smoke-tests.sh

API_BASE="https://api.yourdomain.com"

echo "Running post-deployment smoke tests..."

# Test health endpoint
curl -f $API_BASE/api/health || exit 1

# Test OAuth init endpoint
curl -f -X POST $API_BASE/api/auth/customer/google/init \
  -H "Content-Type: application/json" \
  -d '{"tenantId": 1, "clientType": "web"}' || exit 1

# Test rate limiting
for i in {1..12}; do
  curl -s $API_BASE/api/auth/customer/google/init \
    -X POST \
    -H "Content-Type: application/json" \
    -d '{"tenantId": 1, "clientType": "web"}'
done

echo "Smoke tests completed successfully!"
```

### Monitoring Alerts

```yaml
# alerts.yml (Prometheus AlertManager)
groups:
  - name: customer-oauth-api
    rules:
      - alert: HighErrorRate
        expr: rate(auth_attempts_total{status="failed"}[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: High authentication error rate

      - alert: DatabaseConnectionFailed
        expr: up{job="customer-oauth-api"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: Customer OAuth API is down

      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: High response time detected
```

## Troubleshooting

### Common Issues

1. **OAuth Callback Errors**
   ```bash
   # Check Google OAuth configuration
   curl "https://www.googleapis.com/oauth2/v1/tokeninfo?access_token=YOUR_TOKEN"
   
   # Verify redirect URIs in Google Console
   # Check CORS configuration
   ```

2. **JWT Token Issues**
   ```bash
   # Verify JWT secret
   echo $JWT_SECRET | wc -c  # Should be 32+ characters
   
   # Check token expiry (using jose)
   node -e "
   const { decodeJwt } = require('jose');
   console.log(decodeJwt('YOUR_TOKEN'));
   "
   ```

3. **Database Connection Issues**
   ```bash
   # Test database connection
   psql $DATABASE_URL -c "SELECT version();"
   
   # Check connection pool
   psql $DATABASE_URL -c "SELECT count(*) FROM pg_stat_activity;"
   ```

### Log Analysis

```bash
# Search for authentication errors
grep "auth_failed" logs/combined.log | tail -20

# Monitor OAuth flows
grep "oauth_" logs/combined.log | grep "$(date +%Y-%m-%d)"

# Check security events
grep "Security Event" logs/combined.log | grep "critical\|high"
```
