# 👥 User Role Assignment - Panduan Lengkap

## 🎯 Apa itu User Role Assignment?

User Role Assignment adalah fitur untuk mengelola pemberian dan pencabutan role kepada user dalam sistem RBAC. Fitur ini memungkinkan admin untuk:

- 🔍 **Mencari user** berdasar<PERSON> nama atau email
- 🎭 **Assign role** kepada user tertentu
- ❌ **Revoke role** dari user
- 👀 **Melihat role** yang sudah dimiliki user
- 🚀 **Bulk operations** untuk efisiensi

---

## 🏗️ Arsitektur Implementasi

### 1. **Database Schema**
```sql
-- Table user_roles untuk menyimpan assignment
user_roles (
  id, userId, roleId, tenantId,
  assignedBy, assignedAt, expiresAt,
  is_active, createdAt, updatedAt
)
```

### 2. **Service Layer**
- `UserService.searchUsersForRoleAssignment()` - Search users dengan pagination
- `RoleService.assignRole()` - Assign role ke user
- `RoleService.revokeRole()` - Revoke role dari user
- `RoleService.getUserRoles()` - Get role assignments user

### 3. **API Endpoints**
- `GET /api/users/search` - Search users untuk role assignment
- `POST /api/roles/assign` - Assign role ke user
- `POST /api/roles/revoke` - Revoke role dari user
- `POST /api/roles/bulk-assign` - Bulk assign roles
- `GET /api/roles/user-roles` - Get user role assignments

### 4. **Frontend Components**
- `UserRoleAssignment` - Main component untuk role assignment
- `useUserSearch` - Hook untuk search users
- `useAssignRole` - Hook untuk assign role
- `useRevokeRole` - Hook untuk revoke role

---

## 🚀 Cara Menggunakan

### 1. **Akses Halaman Role Management**
```
http://localhost:3000/role-management
```

### 2. **Pilih Tab "User Assignments"**
- Klik tab ketiga "User Assignments"
- Interface akan menampilkan form search user

### 3. **Search dan Pilih User**
```typescript
// Ketik nama atau email user di search box
// Contoh: "john" atau "<EMAIL>"
// Klik user dari hasil search untuk memilih
```

### 4. **Assign Role**
```typescript
// Setelah pilih user:
// 1. Lihat "Current Roles" yang sudah dimiliki
// 2. Pilih role baru dari dropdown "Assign New Role"
// 3. Klik "Assign Role"
```

### 5. **Revoke Role**
```typescript
// Di section "Current Roles":
// 1. Klik tombol "Revoke" di samping role
// 2. Role akan dicabut dari user
```

---

## 🔐 Keamanan & Validasi

### 1. **RBAC Protection**
- Semua API endpoint dilindungi dengan `withRBAC` middleware
- User harus memiliki permission `roles.assign` dan `roles.revoke`

### 2. **Tenant Isolation**
- User hanya bisa assign role dalam tenant mereka
- Superadmin bisa assign role ke semua tenant

### 3. **Role Hierarchy**
- User tidak bisa assign role dengan level lebih tinggi dari role mereka
- Validasi hierarchy level di backend

### 4. **Audit Logging**
- Semua assignment/revocation dicatat dengan `assignedBy`
- Timestamp `assignedAt` untuk tracking

---

## 🎨 UI/UX Features

### 1. **User Search**
- Real-time search dengan debouncing
- Tampilan avatar, nama, email, dan current role
- Pagination untuk hasil banyak

### 2. **Role Management**
- Visual display current roles dengan tanggal assignment
- Dropdown role yang tersedia (exclude yang sudah assigned)
- One-click revoke dengan konfirmasi

### 3. **Notifications**
- Success toast untuk operasi berhasil
- Error alerts untuk validasi dan error handling
- Loading states untuk UX yang smooth

### 4. **Responsive Design**
- Mobile-friendly interface
- Consistent dengan design system project

---

## 🧪 Testing

### 1. **Test Basic Functionality**
```bash
# 1. <NAME_EMAIL> (superadmin)
# 2. Buka /role-management
# 3. Klik tab "User Assignments"
# 4. Search user dan assign role
# 5. Verify role assignment berhasil
```

### 2. **Test Tenant Isolation**
```bash
# 1. Login sebagai tenant admin
# 2. Verify hanya bisa assign role dalam tenant
# 3. Verify tidak bisa assign system roles
```

### 3. **Test Role Hierarchy**
```bash
# 1. Login sebagai instructor
# 2. Verify tidak bisa assign tenant_admin role
# 3. Verify hanya bisa assign role level lebih rendah
```

---

## 🔧 Troubleshooting

### 1. **"No users found"**
- Pastikan ada users di database
- Check tenant isolation settings
- Verify search query format

### 2. **"Cannot assign role"**
- Check user permissions (`roles.assign`)
- Verify role hierarchy levels
- Check tenant access rights

### 3. **"Role already assigned"**
- User sudah memiliki role tersebut
- Check di "Current Roles" section
- Gunakan revoke dulu jika perlu update

---

## 📈 Next Steps

Setelah implementasi User Role Assignment selesai, sistem RBAC sudah lengkap dengan:

✅ **Role Management** - Create, edit, delete roles  
✅ **Permission Management** - Assign permissions ke roles  
✅ **User Role Assignment** - Assign/revoke roles ke users  
✅ **RBAC Middleware** - API protection  
✅ **Frontend Integration** - Complete UI untuk semua operasi  

Sistem siap untuk production dengan security dan scalability yang proper! 🎉
