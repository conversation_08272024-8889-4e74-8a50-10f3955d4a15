import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { z } from "zod";
import { PricingGroupService } from "@/lib/services/pricing-groups.service";

// Validation schema for creating pricing group
export const createPricingGroupSchema = z.object({
  tenantId: z.number().int().positive("Tenant ID must be positive"),
  name: z.string().min(1, "Name is required").max(255, "Name too long"),
  description: z.string().max(1000, "Description too long").optional(),
  discountPercentage: z.number().int().min(0, "Discount must be at least 0").max(100, "Discount cannot exceed 100").optional(),
  isActive: z.boolean().optional(),
  isDefault: z.boolean().optional(),
  sortOrder: z.number().int().min(0).optional(),
});

// Validation schema for bulk operations
export const bulkOperationSchema = z.object({
  ids: z.array(z.string().min(1)),
  action: z.enum(["activate", "deactivate", "set-default", "unset-default", "delete"]),
});

const pricingGroupService = new PricingGroupService();

// GET /api/pricing-groups - Get pricing groups list
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const tenantIdParam = searchParams.get("tenantId");
    const activeOnly = searchParams.get("activeOnly") === "true";
    const defaultOnly = searchParams.get("defaultOnly") === "true";
    const hasDiscount = searchParams.get("hasDiscount");
    const search = searchParams.get("search");
    const limit = searchParams.get("limit");
    const offset = searchParams.get("offset");

    let pricingGroups;
    const options: any = {
      orderField: 'sortOrder',
      orderBy: 'asc',
    };

    if (limit) options.limit = parseInt(limit);
    if (offset) options.offset = parseInt(offset);

    if (tenantIdParam) {
      const tenantIdNum = parseInt(tenantIdParam);
      if (isNaN(tenantIdNum)) {
        return NextResponse.json(
          { error: "Invalid tenant ID" },
          { status: 400 }
        );
      }
      
      options.tenantId = tenantIdNum;
      
      if (activeOnly && defaultOnly) {
        const defaultGroup = await pricingGroupService.getDefaultByTenantId(tenantIdNum);
        pricingGroups = defaultGroup ? [defaultGroup] : [];
      } else if (activeOnly) {
        pricingGroups = await pricingGroupService.getActiveByTenantId(tenantIdNum);
      } else {
        if (search) {
          pricingGroups = await pricingGroupService.search(search, tenantIdNum);
        } else {
          // Apply filters
          const filters: any = {};
          if (defaultOnly) filters.isDefault = true;
          if (hasDiscount !== null) filters.hasDiscount = hasDiscount === "true";
          
          options.filters = filters;
          pricingGroups = await pricingGroupService.getByTenantId(tenantIdNum, options);
        }
      }
    } else {
      if (search) {
        pricingGroups = await pricingGroupService.search(search);
      } else {
        // Apply filters
        const filters: any = {};
        if (activeOnly) filters.isActive = true;
        if (defaultOnly) filters.isDefault = true;
        if (hasDiscount !== null) filters.hasDiscount = hasDiscount === "true";
        
        options.filters = filters;
        pricingGroups = await pricingGroupService.getAll(options);
      }
    }

    return NextResponse.json({ data: pricingGroups });
  } catch (error) {
    console.error("Error fetching pricing groups:", error);
    return NextResponse.json(
      { error: "Failed to fetch pricing groups" },
      { status: 500 }
    );
  }
}

// POST /api/pricing-groups - Create pricing group
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = createPricingGroupSchema.parse(body);

    const data = await pricingGroupService.create(validatedData);

    return NextResponse.json({ data }, { status: 201 });
  } catch (error) {
    console.error("Error creating pricing group:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to create pricing group" },
      { status: 500 }
    );
  }
}

// PUT /api/pricing-groups - Bulk operations
export async function PUT(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = bulkOperationSchema.parse(body);

    let result;
    switch (validatedData.action) {
      case "activate":
        result = await pricingGroupService.bulkToggleActive(validatedData.ids, true);
        break;
      case "deactivate":
        result = await pricingGroupService.bulkToggleActive(validatedData.ids, false);
        break;
      case "set-default":
        if (validatedData.ids.length > 1) {
          return NextResponse.json(
            { error: "Only one pricing group can be set as default" },
            { status: 400 }
          );
        }
        result = await pricingGroupService.bulkToggleDefault(validatedData.ids, true);
        break;
      case "unset-default":
        result = await pricingGroupService.bulkToggleDefault(validatedData.ids, false);
        break;
      case "delete":
        result = await pricingGroupService.bulkDelete(validatedData.ids);
        break;
      default:
        return NextResponse.json(
          { error: "Invalid action" },
          { status: 400 }
        );
    }

    return NextResponse.json({
      message: `Successfully ${validatedData.action}d ${result.length} pricing groups`,
      data: result
    });
  } catch (error) {
    console.error("Error in bulk operation:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to perform bulk operation" },
      { status: 500 }
    );
  }
}
