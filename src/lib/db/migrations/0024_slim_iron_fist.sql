CREATE TABLE "membership_plan_locations" (
	"id" varchar(255) PRIMARY KEY NOT NULL,
	"membership_plan_id" varchar(255) NOT NULL,
	"location_id" varchar(255) NOT NULL,
	"tenant_id" integer NOT NULL,
	"created_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	"updated_at" timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
	CONSTRAINT "membership_plan_locations_membership_plan_id_location_id_unique" UNIQUE("membership_plan_id","location_id")
);
--> statement-breakpoint
ALTER TABLE "membership_plan_locations" ADD CONSTRAINT "membership_plan_locations_membership_plan_id_membership_plans_id_fk" FOREIGN KEY ("membership_plan_id") REFERENCES "public"."membership_plans"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "membership_plan_locations" ADD CONSTRAINT "membership_plan_locations_location_id_locations_id_fk" FOREIGN KEY ("location_id") REFERENCES "public"."locations"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "membership_plan_locations" ADD CONSTRAINT "membership_plan_locations_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "idx_membership_plan_locations_plan_id" ON "membership_plan_locations" USING btree ("membership_plan_id");--> statement-breakpoint
CREATE INDEX "idx_membership_plan_locations_location_id" ON "membership_plan_locations" USING btree ("location_id");--> statement-breakpoint
CREATE INDEX "idx_membership_plan_locations_tenant_id" ON "membership_plan_locations" USING btree ("tenant_id");