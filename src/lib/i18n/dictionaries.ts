import { Locale } from './config';

// English translations
const en = {
  common: {
    loading: 'Loading...',
    error: 'Error',
    success: 'Success',
    cancel: 'Cancel',
    save: 'Save',
    delete: 'Delete',
    edit: 'Edit',
    create: 'Create',
    update: 'Update',
    search: 'Search',
    filter: 'Filter',
    sort: 'Sort',
    next: 'Next',
    previous: 'Previous',
    close: 'Close',
    open: 'Open',
    yes: 'Yes',
    no: 'No',
    confirm: 'Confirm',
    back: 'Back',
    continue: 'Continue',
    submit: 'Submit',
    reset: 'Reset',
    clear: 'Clear',
  },
  navigation: {
    home: 'Home',
    dashboard: 'Dashboard',
    settings: 'Settings',
    billing: 'Billing',
    profile: 'Profile',
    admin: 'Admin',
    docs: 'Documentation',
    pricing: 'Pricing',
    about: 'About',
    contact: 'Contact',
    signIn: 'Sign In',
    signUp: 'Sign Up',
    signOut: 'Sign Out',
  },
  auth: {
    signIn: 'Sign In',
    signUp: 'Sign Up',
    signOut: 'Sign Out',
    email: 'Email',
    password: 'Password',
    confirmPassword: 'Confirm Password',
    forgotPassword: 'Forgot Password?',
    rememberMe: 'Remember me',
    dontHaveAccount: "Don't have an account?",
    alreadyHaveAccount: 'Already have an account?',
    signInWithGoogle: 'Continue with Google',
    signInWithGitHub: 'Continue with GitHub',
    orContinueWith: 'Or continue with',
    enterEmailAndPassword: 'Enter your email and password to access your account',
    createAccount: 'Create your account to get started',
    invalidCredentials: 'Invalid email or password',
    accountCreated: 'Account created successfully',
    signedIn: 'Signed in successfully',
    signedOut: 'Signed out successfully',
  },
  dashboard: {
    title: 'Dashboard',
    welcome: 'Welcome back',
    overview: 'Overview',
    recentActivity: 'Recent Activity',
    quickActions: 'Quick Actions',
    statistics: 'Statistics',
  },
  billing: {
    title: 'Billing',
    credits: 'Credits',
    usage: 'Usage',
    plans: 'Plans',
    invoices: 'Invoices',
    paymentMethod: 'Payment Method',
    billingHistory: 'Billing History',
    currentPlan: 'Current Plan',
    upgradePlan: 'Upgrade Plan',
    cancelSubscription: 'Cancel Subscription',
  },
  settings: {
    title: 'Settings',
    general: 'General',
    security: 'Security',
    notifications: 'Notifications',
    appearance: 'Appearance',
    language: 'Language',
    timezone: 'Timezone',
    profile: 'Profile',
    account: 'Account',
    privacy: 'Privacy',
    preferences: 'Preferences',
  },
  errors: {
    notFound: 'Page not found',
    unauthorized: 'Unauthorized',
    forbidden: 'Forbidden',
    internalError: 'Internal server error',
    networkError: 'Network error',
    validationError: 'Validation error',
    somethingWentWrong: 'Something went wrong',
    tryAgain: 'Please try again',
  },
  meta: {
    title: 'Your SaaS App - Modern SaaS Template',
    description: 'A comprehensive Next.js 15 SaaS template with authentication, billing, and multi-tenancy',
    keywords: 'SaaS, Next.js, React, TypeScript, Tailwind CSS, Authentication, Billing, Multi-tenancy',
  },
} as const;

// Indonesian translations
const id: typeof en = {
  common: {
    loading: 'Memuat...',
    error: 'Error',
    success: 'Berhasil',
    cancel: 'Batal',
    save: 'Simpan',
    delete: 'Hapus',
    edit: 'Edit',
    create: 'Buat',
    update: 'Perbarui',
    search: 'Cari',
    filter: 'Filter',
    sort: 'Urutkan',
    next: 'Selanjutnya',
    previous: 'Sebelumnya',
    close: 'Tutup',
    open: 'Buka',
    yes: 'Ya',
    no: 'Tidak',
    confirm: 'Konfirmasi',
    back: 'Kembali',
    continue: 'Lanjutkan',
    submit: 'Kirim',
    reset: 'Reset',
    clear: 'Bersihkan',
  },
  navigation: {
    home: 'Beranda',
    dashboard: 'Dashboard',
    settings: 'Pengaturan',
    billing: 'Tagihan',
    profile: 'Profil',
    admin: 'Admin',
    docs: 'Dokumentasi',
    pricing: 'Harga',
    about: 'Tentang',
    contact: 'Kontak',
    signIn: 'Masuk',
    signUp: 'Daftar',
    signOut: 'Keluar',
  },
  auth: {
    signIn: 'Masuk',
    signUp: 'Daftar',
    signOut: 'Keluar',
    email: 'Email',
    password: 'Kata Sandi',
    confirmPassword: 'Konfirmasi Kata Sandi',
    forgotPassword: 'Lupa Kata Sandi?',
    rememberMe: 'Ingat saya',
    dontHaveAccount: 'Belum punya akun?',
    alreadyHaveAccount: 'Sudah punya akun?',
    signInWithGoogle: 'Lanjutkan dengan Google',
    signInWithGitHub: 'Lanjutkan dengan GitHub',
    orContinueWith: 'Atau lanjutkan dengan',
    enterEmailAndPassword: 'Masukkan email dan kata sandi untuk mengakses akun Anda',
    createAccount: 'Buat akun Anda untuk memulai',
    invalidCredentials: 'Email atau kata sandi tidak valid',
    accountCreated: 'Akun berhasil dibuat',
    signedIn: 'Berhasil masuk',
    signedOut: 'Berhasil keluar',
  },
  dashboard: {
    title: 'Dashboard',
    welcome: 'Selamat datang kembali',
    overview: 'Ringkasan',
    recentActivity: 'Aktivitas Terbaru',
    quickActions: 'Aksi Cepat',
    statistics: 'Statistik',
  },
  billing: {
    title: 'Tagihan',
    credits: 'Kredit',
    usage: 'Penggunaan',
    plans: 'Paket',
    invoices: 'Faktur',
    paymentMethod: 'Metode Pembayaran',
    billingHistory: 'Riwayat Tagihan',
    currentPlan: 'Paket Saat Ini',
    upgradePlan: 'Upgrade Paket',
    cancelSubscription: 'Batalkan Langganan',
  },
  settings: {
    title: 'Pengaturan',
    general: 'Umum',
    security: 'Keamanan',
    notifications: 'Notifikasi',
    appearance: 'Tampilan',
    language: 'Bahasa',
    timezone: 'Zona Waktu',
    profile: 'Profil',
    account: 'Akun',
    privacy: 'Privasi',
    preferences: 'Preferensi',
  },
  errors: {
    notFound: 'Halaman tidak ditemukan',
    unauthorized: 'Tidak diizinkan',
    forbidden: 'Dilarang',
    internalError: 'Error server internal',
    networkError: 'Error jaringan',
    validationError: 'Error validasi',
    somethingWentWrong: 'Terjadi kesalahan',
    tryAgain: 'Silakan coba lagi',
  },
  meta: {
    title: 'Aplikasi SaaS Anda - Template SaaS Modern',
    description: 'Template Next.js 15 SaaS yang komprehensif dengan autentikasi, tagihan, dan multi-tenancy',
    keywords: 'SaaS, Next.js, React, TypeScript, Tailwind CSS, Autentikasi, Tagihan, Multi-tenancy',
  },
} as const;

const dictionaries = {
  en,
  id,
} as const;

export type Dictionary = typeof en;

export async function getDictionary(locale: Locale): Promise<Dictionary> {
  return dictionaries[locale];
}

export { dictionaries };