"use client";

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useSession } from "next-auth/react";
import { MembershipPlanLocationAssignment } from "@/lib/services/membership-plan-location-service";

export function useMembershipPlanLocations() {
  const { data: session } = useSession();
  const queryClient = useQueryClient();
  const tenantId = session?.user?.tenantId;

  // Get membership plans with their assigned locations
  const {
    data: membershipPlansWithLocations,
    isLoading: isLoadingPlans,
    error: plansError,
  } = useQuery({
    queryKey: ["membership-plans-with-locations", tenantId],
    queryFn: async () => {
      const response = await fetch(`/api/membership-plans/locations?tenantId=${tenantId}`);
      if (!response.ok) {
        throw new Error("Failed to fetch membership plans with locations");
      }
      return response.json();
    },
    enabled: !!tenantId,
  });

  // Get available locations for assignment
  const {
    data: availableLocations,
    isLoading: isLoadingLocations,
    error: locationsError,
  } = useQuery({
    queryKey: ["available-locations", tenantId],
    queryFn: async () => {
      const response = await fetch(`/api/locations?tenantId=${tenantId}`);
      if (!response.ok) {
        throw new Error("Failed to fetch available locations");
      }
      return response.json();
    },
    enabled: !!tenantId,
  });

  // Assign membership plan to locations
  const assignToLocationsMutation = useMutation({
    mutationFn: async ({
      membershipPlanId,
      locationIds,
    }: {
      membershipPlanId: string;
      locationIds: string[];
    }) => {
      const response = await fetch("/api/membership-plans/locations/assign", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          membershipPlanId,
          locationIds,
          tenantId,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to assign locations");
      }

      return response.json();
    },
    onSuccess: () => {
      // Invalidate and refetch membership plans with locations
      queryClient.invalidateQueries({
        queryKey: ["membership-plans-with-locations", tenantId],
      });
    },
  });

  // Remove membership plan from locations
  const removeFromLocationsMutation = useMutation({
    mutationFn: async ({
      membershipPlanId,
      locationIds,
    }: {
      membershipPlanId: string;
      locationIds: string[];
    }) => {
      const response = await fetch("/api/membership-plans/locations/remove", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          membershipPlanId,
          locationIds,
          tenantId,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to remove from locations");
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["membership-plans-with-locations", tenantId],
      });
    },
  });

  // Bulk update assignments
  const bulkUpdateAssignmentsMutation = useMutation({
    mutationFn: async (assignments: MembershipPlanLocationAssignment[]) => {
      const response = await fetch("/api/membership-plans/locations/bulk-update", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          assignments,
          tenantId,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to bulk update assignments");
      }

      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["membership-plans-with-locations", tenantId],
      });
    },
  });

  return {
    // Data
    membershipPlansWithLocations: membershipPlansWithLocations?.data || [],
    availableLocations: availableLocations?.data || [],
    
    // Loading states
    isLoadingPlans,
    isLoadingLocations,
    isLoading: isLoadingPlans || isLoadingLocations,
    
    // Errors
    plansError,
    locationsError,
    error: plansError || locationsError,
    
    // Mutations
    assignToLocations: assignToLocationsMutation.mutate,
    removeFromLocations: removeFromLocationsMutation.mutate,
    bulkUpdateAssignments: bulkUpdateAssignmentsMutation.mutate,
    
    // Mutation states
    isAssigning: assignToLocationsMutation.isPending,
    isRemoving: removeFromLocationsMutation.isPending,
    isBulkUpdating: bulkUpdateAssignmentsMutation.isPending,
    
    // Mutation errors
    assignError: assignToLocationsMutation.error,
    removeError: removeFromLocationsMutation.error,
    bulkUpdateError: bulkUpdateAssignmentsMutation.error,
  };
}

// Hook for getting assigned locations for a specific membership plan
export function useAssignedLocations(membershipPlanId: string) {
  const { data: session } = useSession();
  const tenantId = session?.user?.tenantId;

  return useQuery({
    queryKey: ["assigned-locations", membershipPlanId, tenantId],
    queryFn: async () => {
      const response = await fetch(
        `/api/membership-plans/${membershipPlanId}/locations?tenantId=${tenantId}`
      );
      if (!response.ok) {
        throw new Error("Failed to fetch assigned locations");
      }
      return response.json();
    },
    enabled: !!membershipPlanId && !!tenantId,
  });
}
