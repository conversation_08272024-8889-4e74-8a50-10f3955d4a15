"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { DeleteConfirmationDialog } from "@/components/ui/delete-confirmation-dialog";
import { DeleteSuccessToast } from "@/components/ui/delete-success-toast";
import {
  Wrench,
  Edit,
  Trash2,
  Plus,
  Search,
  Building,
  AlertCircle,
  Package
} from "lucide-react";
import { Equipment } from "@/lib/db/schema";
import { useDeleteEquipment, useEquipments } from "@/lib/hooks/queries/use-equipment-queries";

interface EquipmentListProps {
  tenantId?: number;
  onEdit?: (eqEdit: Equipment) => void;
  onAdd?: () => void;
  showActions?: boolean;
  className?: string;
}

export function EquipmentList({
  tenantId,
  onEdit,
  onAdd,
  showActions = true,
  className = "",
}: EquipmentListProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [deleteError, setDeleteError] = useState<string | null>(null);
  const [deleteConfirmation, setDeleteConfirmation] = useState<{
    isOpen: boolean;
    equipment: Equipment | null;
  }>({ isOpen: false, equipment: null });
  const [showDeleteSuccess, setShowDeleteSuccess] = useState(false);
  const [deletedEquipmentName, setDeletedEquipmentName] = useState("");

  const { data: equipments = [], isLoading, error } = useEquipments();
  const deleteEquipmentMutation = useDeleteEquipment();

  // Filter data by tenant and search query
  const filteredEquipments = equipments.filter((equipment) => {
    const matchesTenant = !tenantId || equipment.tenantId === tenantId;
    const matchesSearch = !searchQuery || 
      equipment.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
      equipment.default_display_name?.toLowerCase().includes(searchQuery.toLowerCase()); 
    
    return matchesTenant && matchesSearch;
  });

const handleDeleteClick = (equipment: Equipment) => {
    setDeleteConfirmation({ isOpen: true,  equipment});
  };

  const handleDeleteConfirm = async () => {
    if (!deleteConfirmation.equipment) return;

    const equipmentName = deleteConfirmation.equipment.name || "Unknown equipment";

    try {
      setDeleteError(null);
      await deleteEquipmentMutation.mutateAsync(deleteConfirmation.equipment.id);

      // Close confirmation dialog
      setDeleteConfirmation({ isOpen: false, equipment: null });

      // Show success toast
      setDeletedEquipmentName(equipmentName);
      setShowDeleteSuccess(true);

    } catch (error) {
      setDeleteError(error instanceof Error ? error.message : "Failed to delete equipment");
    }
  };

  const handleDeleteCancel = () => {
    setDeleteConfirmation({ isOpen: false, equipment: null });
  };

  const formatEquipmentInfo = (equipment: Equipment) => {
    const parts = [
      equipment.default_display_name,
      `ID: ${equipment.id.slice(-8)}` // Show last 8 characters of ID
    ].filter(Boolean);

    return parts.join(" • ");
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        <CardContent className="p-6">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Failed to load locations: {error}
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Wrench className="h-5 w-5" />
              Equipment
            </CardTitle>
            <CardDescription>
              Manage equipment inventory and information
            </CardDescription>
          </div>
          {showActions && onAdd && (
            <Button onClick={onAdd} size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Add Location
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {deleteError && (
          <Alert variant="destructive" className="mb-4">
            <AlertDescription>{deleteError}</AlertDescription>
          </Alert>
        )}

        {/* Search */}
        <div className="relative mb-4">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search equipment..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Equipment List */}
        {filteredEquipments.length === 0 ? (
          <div className="text-center py-8">
            <Wrench className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              {searchQuery ? "No equipment found" : "No equipment yet"}
            </h3>
            <p className="text-gray-500 dark:text-gray-400 mb-4">
              {searchQuery
                ? "Try adjusting your search terms."
                : "Get started by adding your first equipment."
              }
            </p>
            {showActions && onAdd && !searchQuery && (
              <Button onClick={onAdd}>
                <Plus className="h-4 w-4 mr-2" />
                Add Equipment
              </Button>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            {filteredEquipments.map((equipment) => (
              <div
                key={equipment.id}
                className="border rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <Package className="h-4 w-4 text-gray-500" />
                      <h4 className="font-medium text-gray-900 dark:text-gray-100">
                        {equipment.name}
                      </h4>
                    </div>

                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                      {formatEquipmentInfo(equipment)}
                    </p>

                    {equipment.default_display_name && (
                      <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                        <Building className="h-3 w-3" />
                        Display: {equipment.default_display_name}
                      </div>
                    )}

                    <div className="flex items-center gap-2 mt-2">
                      <Badge variant="secondary" className="text-xs">
                        Tenant ID: {equipment.tenantId}
                      </Badge>
                    </div>
                  </div>

                  {showActions && (
                    <div className="flex items-center gap-2 ml-4">
                      {onEdit && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => onEdit(equipment)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                      )}
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDeleteClick(equipment)}
                        disabled={deleteEquipmentMutation.isPending}
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Summary */}
        {filteredEquipments.length > 0 && (
          <div className="mt-4 pt-4 border-t text-sm text-gray-500 dark:text-gray-400">
            Showing {filteredEquipments.length} of {equipments.length} equipment
          </div>
        )}
      </CardContent>

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationDialog
        isOpen={deleteConfirmation.isOpen}
        onClose={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        title="Delete Equipment"
        description="Are you sure you want to delete this equipment? This action cannot be undone."
        itemName={deleteConfirmation.equipment?.name || ""}
        isLoading={deleteEquipmentMutation.isPending}
      />

      {/* Delete Success Toast */}
      <DeleteSuccessToast
        isVisible={showDeleteSuccess}
        onClose={() => setShowDeleteSuccess(false)}
        itemName={deletedEquipmentName}
      />
    </Card>
  );
}
