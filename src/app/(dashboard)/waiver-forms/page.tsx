"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { 
  FileText, 
  Plus, 
  Building, 
  BarChart3,
  AlertCircle,
  CheckCircle,
  Eye,
  EyeOff,
  Shield,
  ShieldOff
} from "lucide-react";
import { WaiverFormForm } from "@/components/forms/waiver-form-form";
import { WaiverFormList } from "@/components/waiver-forms/waiver-form-list";
import { SuccessToast } from "@/components/ui/success-toast";
import { SuccessModal } from "@/components/ui/success-modal";
import { Confetti } from "@/components/ui/confetti";
import { WaiverForm, NewWaiverForm } from "@/lib/db/schema";
import { 
  useWaiverForms, 
  useCreateWaiverForm, 
  useUpdateWaiverForm,
  useWaiverFormStats
} from "@/lib/hooks/queries/use-waiver-form-queries";
import { useTenants } from "@/lib/hooks/queries/use-tenant-queries";

export default function WaiverFormsPage() {
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingWaiverForm, setEditingWaiverForm] = useState<WaiverForm | null>(null);
  const [selectedTenantId, setSelectedTenantId] = useState<number | null>(null);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [submitSuccess, setSubmitSuccess] = useState<string | null>(null);
  
  // Animation states
  const [showSuccessToast, setShowSuccessToast] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showConfetti, setShowConfetti] = useState(false);
  const [createdWaiverForm, setCreatedWaiverForm] = useState<WaiverForm | null>(null);

  const { data: waiverForms = [], isLoading: waiverFormsLoading } = useWaiverForms();
  const { data: tenantsData, isLoading: tenantsLoading } = useTenants();
  const { data: stats, isLoading: statsLoading } = useWaiverFormStats();
  const tenants = tenantsData?.tenants || [];
  const createWaiverFormMutation = useCreateWaiverForm();
  const updateWaiverFormMutation = useUpdateWaiverForm();

  // Calculate stats by tenant
  const statsByTenant = tenants.reduce((acc, tenant) => {
    const tenantWaiverForms = waiverForms.filter(wf => wf.tenantId === tenant.id);
    acc[tenant.id] = {
      total: tenantWaiverForms.length,
      active: tenantWaiverForms.filter(wf => wf.isActive).length,
      required: tenantWaiverForms.filter(wf => wf.isRequired).length,
    };
    return acc;
  }, {} as Record<number, { total: number; active: number; required: number }>);

  const handleCreateWaiverForm = async (data: Omit<NewWaiverForm, "id" | "createdAt" | "updatedAt">) => {
    try {
      setSubmitError(null);
      const newWaiverForm = await createWaiverFormMutation.mutateAsync(data);
      
      setCreatedWaiverForm(newWaiverForm);
      setIsFormOpen(false);
      
      // Start celebration sequence
      setTimeout(() => {
        setShowConfetti(true);
        setShowSuccessToast(true);
        setShowSuccessModal(true);
      }, 300);
      
      setTimeout(() => setShowConfetti(false), 4000);
      
    } catch (error) {
      setSubmitError(error instanceof Error ? error.message : "Failed to create waiver form");
    }
  };

  const handleUpdateWaiverForm = async (data: Omit<NewWaiverForm, "id" | "createdAt" | "updatedAt">) => {
    if (!editingWaiverForm) return;

    try {
      setSubmitError(null);
      await updateWaiverFormMutation.mutateAsync({ 
        id: editingWaiverForm.id, 
        data: data
      });
      setSubmitSuccess("Waiver form updated successfully!");
      setEditingWaiverForm(null);
      setTimeout(() => setSubmitSuccess(null), 3000);
    } catch (error) {
      setSubmitError(error instanceof Error ? error.message : "Failed to update waiver form");
    }
  };

  const handleEdit = (waiverForm: WaiverForm) => {
    setEditingWaiverForm(waiverForm);
  };

  const handleAdd = () => {
    setEditingWaiverForm(null);
    setIsFormOpen(true);
  };

  const handleCloseForm = () => {
    setIsFormOpen(false);
    setEditingWaiverForm(null);
    setSubmitError(null);
  };

  const handleCloseSuccessAnimations = () => {
    setShowSuccessToast(false);
    setShowSuccessModal(false);
    setShowConfetti(false);
    setCreatedWaiverForm(null);
  };

  const handleViewWaiverForm = () => {
    handleCloseSuccessAnimations();
  };

  const handleAddAnother = () => {
    handleCloseSuccessAnimations();
    setIsFormOpen(true);
  };

  if (waiverFormsLoading || tenantsLoading || statsLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Waiver Forms</h1>
          <p className="text-gray-500 dark:text-gray-400">
            Manage waiver forms and their settings
          </p>
        </div>
        <Button onClick={handleAdd}>
          <Plus className="h-4 w-4 mr-2" />
          Add Waiver Form
        </Button>
      </div>

      {/* Success/Error Messages */}
      {submitSuccess && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>{submitSuccess}</AlertDescription>
        </Alert>
      )}

      {submitError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{submitError}</AlertDescription>
        </Alert>
      )}

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Waiver Forms</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.total || 0}</div>
            <p className="text-xs text-muted-foreground">
              Across all tenants
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Forms</CardTitle>
            <Eye className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.active || 0}</div>
            <p className="text-xs text-muted-foreground">
              Currently available
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Inactive Forms</CardTitle>
            <EyeOff className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.inactive || 0}</div>
            <p className="text-xs text-muted-foreground">
              Currently hidden
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Required Forms</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.required || 0}</div>
            <p className="text-xs text-muted-foreground">
              Mandatory for customers
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Optional Forms</CardTitle>
            <ShieldOff className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.optional || 0}</div>
            <p className="text-xs text-muted-foreground">
              Not mandatory
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="all" className="space-y-4">
        <TabsList>
          <TabsTrigger value="all">All Waiver Forms</TabsTrigger>
          {tenants.map((tenant) => {
            const tenantStats = statsByTenant[tenant.id];
            return (
              <TabsTrigger key={tenant.id} value={`tenant-${tenant.id}`}>
                {tenant.name}
                <Badge variant="secondary" className="ml-2">
                  {tenantStats?.total || 0}
                </Badge>
              </TabsTrigger>
            );
          })}
        </TabsList>

        <TabsContent value="all">
          <WaiverFormList
            onEdit={handleEdit}
            onAdd={handleAdd}
            showActions={true}
          />
        </TabsContent>

        {tenants.map((tenant) => (
          <TabsContent key={tenant.id} value={`tenant-${tenant.id}`}>
            <WaiverFormList
              tenantId={tenant.id}
              onEdit={handleEdit}
              onAdd={() => {
                setSelectedTenantId(tenant.id);
                handleAdd();
              }}
              showActions={true}
            />
          </TabsContent>
        ))}
      </Tabs>

      {/* Create/Edit Dialog */}
      <Dialog open={isFormOpen || !!editingWaiverForm} onOpenChange={handleCloseForm}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editingWaiverForm ? "Edit Waiver Form" : "Add New Waiver Form"}
            </DialogTitle>
          </DialogHeader>
          <WaiverFormForm
            entity={editingWaiverForm || undefined}
            tenantId={selectedTenantId || editingWaiverForm?.tenantId || (tenants[0]?.id ?? 1)}
            onSubmit={editingWaiverForm ? handleUpdateWaiverForm : handleCreateWaiverForm}
            onCancel={handleCloseForm}
            className="border-0 shadow-none"
          />
        </DialogContent>
      </Dialog>

      {/* Success Animations */}
      <Confetti isActive={showConfetti} />
      
      <SuccessToast
        isVisible={showSuccessToast}
        onClose={() => setShowSuccessToast(false)}
        title="Waiver Form Created Successfully!"
        description={createdWaiverForm ? 
          `${createdWaiverForm.name} has been added to your waiver forms.` : 
          undefined
        }
        type="general"
        action={{
          label: "View All Waiver Forms",
          onClick: handleViewWaiverForm
        }}
      />

      <SuccessModal
        isOpen={showSuccessModal}
        onClose={handleCloseSuccessAnimations}
        title="🎉 Waiver Form Created Successfully!"
        description="Your new waiver form has been saved and is now available for use."
        locationData={createdWaiverForm ? {
          address: createdWaiverForm.name || "",
          city: createdWaiverForm.description || "Waiver Form",
          country: `Status: ${createdWaiverForm.isActive ? 'Active' : 'Inactive'} | ${createdWaiverForm.isRequired ? 'Required' : 'Optional'}`
        } : undefined}
        actions={{
          primary: {
            label: "Add Another Waiver Form",
            onClick: handleAddAnother
          },
          secondary: {
            label: "View All Waiver Forms",
            onClick: handleViewWaiverForm
          }
        }}
      />
    </div>
  );
}
