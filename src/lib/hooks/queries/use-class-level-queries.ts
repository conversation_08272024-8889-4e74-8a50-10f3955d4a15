import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { ClassLevel, NewClassLevel } from '@/lib/db/schema';

// Query Keys
export const classLevelKeys = {
  all: ['class-levels'] as const,
  lists: () => [...classLevelKeys.all, 'list'] as const,
  list: (filters: Record<string, any>) => [...classLevelKeys.lists(), { filters }] as const,
  details: () => [...classLevelKeys.all, 'detail'] as const,
  detail: (id: string) => [...classLevelKeys.details(), id] as const,
  byTenant: (tenantId: number) => [...classLevelKeys.all, 'tenant', tenantId] as const,
  stats: (tenantId?: number) => [...classLevelKeys.all, 'stats', tenantId] as const,
};

// API Functions
const classLevelApi = {
  getAll: async (): Promise<ClassLevel[]> => {
    const response = await fetch('/api/class-levels');
    if (!response.ok) {
      throw new Error(`Failed to fetch class levels: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data || [];
  },

  getByTenant: async (tenantId: number, activeOnly = false): Promise<ClassLevel[]> => {
    const params = new URLSearchParams({
      tenantId: tenantId.toString(),
      ...(activeOnly && { activeOnly: 'true' })
    });
    const response = await fetch(`/api/class-levels?${params}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch class levels: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data || [];
  },

  getById: async (id: string): Promise<ClassLevel> => {
    const response = await fetch(`/api/class-levels/${id}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch class level: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data;
  },

  create: async (data: Omit<NewClassLevel, "id" | "createdAt" | "updatedAt">): Promise<ClassLevel> => {
    const response = await fetch('/api/class-levels', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to create class level');
    }
    const result = await response.json();
    return result.data;
  },

  update: async ({ classLevelId, data }: {
    classLevelId: string;
    data: Partial<NewClassLevel>;
  }): Promise<ClassLevel> => {
    const response = await fetch(`/api/class-levels/${classLevelId}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to update class level');
    }
    const result = await response.json();
    return result.data;
  },

  delete: async (classLevelId: string): Promise<ClassLevel> => {
    const response = await fetch(`/api/class-levels/${classLevelId}`, {
      method: 'DELETE',
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to delete class level');
    }
    const result = await response.json();
    return result.data;
  },

  toggleActive: async (classLevelId: string): Promise<ClassLevel> => {
    const response = await fetch(`/api/class-levels/${classLevelId}`, {
      method: 'PATCH',
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to toggle class level status');
    }
    const result = await response.json();
    return result.data;
  },

  reorder: async (tenantId: number, items: { id: string; sortOrder: number }[]): Promise<ClassLevel[]> => {
    const response = await fetch('/api/class-levels/reorder', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ tenantId, items }),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to reorder class levels');
    }
    const result = await response.json();
    return result.data;
  },

  bulkOperation: async (ids: string[], action: 'activate' | 'deactivate' | 'delete'): Promise<ClassLevel[]> => {
    const response = await fetch('/api/class-levels', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ ids, action }),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to perform bulk operation');
    }
    const result = await response.json();
    return result.data;
  },

  getStats: async (tenantId?: number) => {
    const params = tenantId ? `?tenantId=${tenantId}` : '';
    const response = await fetch(`/api/class-levels/stats${params}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch class level stats: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data;
  },
};

// Query Hooks
export function useClassLevels() {
  const query = useQuery({
    queryKey: classLevelKeys.lists(),
    queryFn: classLevelApi.getAll,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: false,
  });

  const classLevels = Array.isArray(query.data) ? query.data : [];

  return {
    data: classLevels,
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error?.message || null,
    refetch: query.refetch,
  };
}

export function useClassLevelsByTenant(tenantId: number, activeOnly = false) {
  return useQuery({
    queryKey: classLevelKeys.byTenant(tenantId),
    queryFn: () => classLevelApi.getByTenant(tenantId, activeOnly),
    enabled: !!tenantId,
    staleTime: 5 * 60 * 1000,
  });
}

export function useClassLevel(id: string) {
  return useQuery({
    queryKey: classLevelKeys.detail(id),
    queryFn: () => classLevelApi.getById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
}

export function useClassLevelStats(tenantId?: number) {
  return useQuery({
    queryKey: classLevelKeys.stats(tenantId),
    queryFn: () => classLevelApi.getStats(tenantId),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

// Mutation Hooks
export function useCreateClassLevel() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: classLevelApi.create,
    onSuccess: (newClassLevel) => {
      // Update cache with new class level
      queryClient.setQueryData(classLevelKeys.detail(newClassLevel.id), newClassLevel);

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: classLevelKeys.lists() });
      if (newClassLevel.tenantId) {
        queryClient.invalidateQueries({
          queryKey: classLevelKeys.byTenant(newClassLevel.tenantId)
        });
        queryClient.invalidateQueries({
          queryKey: classLevelKeys.stats(newClassLevel.tenantId)
        });
      }
    },
  });
}

export function useUpdateClassLevel() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: classLevelApi.update,
    onSuccess: (updatedClassLevel) => {
      // Update cache with updated class level
      queryClient.setQueryData(classLevelKeys.detail(updatedClassLevel.id), updatedClassLevel);

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: classLevelKeys.lists() });
      if (updatedClassLevel.tenantId) {
        queryClient.invalidateQueries({
          queryKey: classLevelKeys.byTenant(updatedClassLevel.tenantId)
        });
        queryClient.invalidateQueries({
          queryKey: classLevelKeys.stats(updatedClassLevel.tenantId)
        });
      }
    },
  });
}

export function useDeleteClassLevel() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: classLevelApi.delete,
    onMutate: async (classLevelId: string) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: classLevelKeys.all });

      // Get the class level data before deletion for rollback
      const previousClassLevel = queryClient.getQueryData(classLevelKeys.detail(classLevelId)) as ClassLevel;

      // Get all class levels to update the list optimistically
      const previousClassLevels = queryClient.getQueryData(classLevelKeys.lists()) as ClassLevel[] | undefined;

      // Optimistically remove from all class levels list
      if (previousClassLevels) {
        const updatedClassLevels = previousClassLevels.filter(cl => cl.id !== classLevelId);
        queryClient.setQueryData(classLevelKeys.lists(), updatedClassLevels);
      }

      // Remove from detail cache
      queryClient.removeQueries({ queryKey: classLevelKeys.detail(classLevelId) });

      return { previousClassLevel, previousClassLevels };
    },
    onError: (error, classLevelId, context) => {
      // Restore the previous state if deletion failed
      if (context?.previousClassLevels) {
        queryClient.setQueryData(classLevelKeys.lists(), context.previousClassLevels);
      }
      if (context?.previousClassLevel) {
        queryClient.setQueryData(classLevelKeys.detail(classLevelId), context.previousClassLevel);
      }
    },
    onSuccess: (deletedClassLevel) => {
      // Invalidate and refetch related queries to ensure consistency
      queryClient.invalidateQueries({ queryKey: classLevelKeys.lists() });

      if (deletedClassLevel.tenantId) {
        queryClient.invalidateQueries({
          queryKey: classLevelKeys.byTenant(deletedClassLevel.tenantId)
        });
        queryClient.invalidateQueries({
          queryKey: classLevelKeys.stats(deletedClassLevel.tenantId)
        });
      }
    },
  });
}

export function useToggleClassLevelActive() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: classLevelApi.toggleActive,
    onSuccess: (updatedClassLevel) => {
      // Update cache with updated class level
      queryClient.setQueryData(classLevelKeys.detail(updatedClassLevel.id), updatedClassLevel);

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: classLevelKeys.lists() });
      if (updatedClassLevel.tenantId) {
        queryClient.invalidateQueries({
          queryKey: classLevelKeys.byTenant(updatedClassLevel.tenantId)
        });
        queryClient.invalidateQueries({
          queryKey: classLevelKeys.stats(updatedClassLevel.tenantId)
        });
      }
    },
  });
}

export function useReorderClassLevels() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ tenantId, items }: { tenantId: number; items: { id: string; sortOrder: number }[] }) =>
      classLevelApi.reorder(tenantId, items),
    onSuccess: (updatedClassLevels, { tenantId }) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: classLevelKeys.lists() });
      queryClient.invalidateQueries({ queryKey: classLevelKeys.byTenant(tenantId) });
    },
  });
}

export function useBulkClassLevelOperation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ ids, action }: { ids: string[]; action: 'activate' | 'deactivate' | 'delete' }) =>
      classLevelApi.bulkOperation(ids, action),
    onSuccess: () => {
      // Invalidate all related queries
      queryClient.invalidateQueries({ queryKey: classLevelKeys.all });
    },
  });
}
