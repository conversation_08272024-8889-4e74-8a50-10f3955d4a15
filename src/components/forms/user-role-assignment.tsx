"use client";

import { useState } from "react";
import { useSession } from "next-auth/react";
import { Search, UserPlus, X, Shield, AlertCircle, Check } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useUserSearch, useAssignRole, useRevokeRole, useUserRoleAssignments } from "@/lib/hooks/queries/use-user-role-queries";
import { useRoleSearch } from "@/lib/hooks/queries/use-role-queries";
import { User, Role } from "@/lib/db/schema";

interface UserRoleAssignmentProps {
  onSuccess?: (message: string) => void;
}

export function UserRoleAssignment({ onSuccess }: UserRoleAssignmentProps) {
  const { data: session } = useSession();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const [selectedRole, setSelectedRole] = useState<string>("");
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [isSuccess, setIsSuccess] = useState(false);

  // Get current tenant ID
  const currentTenantId = session?.user?.tenantId || null;

  // Queries
  const { data: userSearchResult, isLoading: isSearchingUsers } = useUserSearch(
    searchQuery,
    currentTenantId,
    { enabled: searchQuery.length > 0 }
  );

  const { data: rolesResult, isLoading: isLoadingRoles } = useRoleSearch(
    "",
    currentTenantId,
    { enabled: true }
  );

  const { data: userRoles, refetch: refetchUserRoles } = useUserRoleAssignments(
    selectedUser?.id || "",
    currentTenantId,
    { enabled: !!selectedUser }
  );

  // Mutations
  const assignRoleMutation = useAssignRole();
  const revokeRoleMutation = useRevokeRole();

  // Get available roles (exclude already assigned ones)
  const availableRoles = rolesResult?.roles?.filter(role => 
    !userRoles?.some(ur => ur.roleId === role.id && ur.is_active)
  ) || [];

  // Handle user selection
  const handleUserSelect = (user: User) => {
    setSelectedUser(user);
    setSearchQuery(user.name || user.email);
    setSelectedRole("");
    setSubmitError(null);
  };

  // Handle role assignment
  const handleAssignRole = async () => {
    if (!selectedUser || !selectedRole) {
      setSubmitError("Please select both user and role");
      return;
    }

    try {
      setSubmitError(null);
      
      await assignRoleMutation.mutateAsync({
        userId: selectedUser.id,
        roleId: selectedRole,
        tenantId: currentTenantId,
        assignedBy: session?.user?.id,
      });

      setIsSuccess(true);
      setTimeout(() => setIsSuccess(false), 2000);
      setSelectedRole("");
      refetchUserRoles();
      onSuccess?.(`Role assigned to ${selectedUser.name || selectedUser.email} successfully!`);
    } catch (error) {
      console.error("Error assigning role:", error);
      
      let errorMessage = "Failed to assign role. Please try again.";
      if (error instanceof Error) {
        if (error.message.includes("already assigned")) {
          errorMessage = "This role is already assigned to the user.";
        } else if (error.message.includes("hierarchy")) {
          errorMessage = "You cannot assign a role with higher privileges than your own.";
        } else {
          errorMessage = error.message;
        }
      }
      
      setSubmitError(errorMessage);
    }
  };

  // Handle role revocation
  const handleRevokeRole = async (roleId: string, roleName: string) => {
    if (!selectedUser) return;

    try {
      setSubmitError(null);
      
      await revokeRoleMutation.mutateAsync({
        userId: selectedUser.id,
        roleId,
        tenantId: currentTenantId,
      });

      setIsSuccess(true);
      setTimeout(() => setIsSuccess(false), 2000);
      refetchUserRoles();
      onSuccess?.(`Role ${roleName} revoked from ${selectedUser.name || selectedUser.email} successfully!`);
    } catch (error) {
      console.error("Error revoking role:", error);
      
      let errorMessage = "Failed to revoke role. Please try again.";
      if (error instanceof Error) {
        errorMessage = error.message;
      }
      
      setSubmitError(errorMessage);
    }
  };

  // Get user initials for avatar
  const getUserInitials = (user: User) => {
    if (user.name) {
      return user.name.split(' ').map(n => n[0]).join('').toUpperCase();
    }
    return user.email.charAt(0).toUpperCase();
  };

  return (
    <div className="space-y-6">
      {/* Error Alert */}
      {submitError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{submitError}</AlertDescription>
        </Alert>
      )}

      {/* Success Alert */}
      {isSuccess && (
        <Alert className="border-green-200 bg-green-50 text-green-800">
          <Check className="h-4 w-4" />
          <AlertDescription>
            Role assignment updated successfully!
          </AlertDescription>
        </Alert>
      )}

      {/* User Search */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <UserPlus className="h-5 w-5" />
            Search Users
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search users by name or email..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Search Results */}
          {searchQuery && userSearchResult && (
            <div className="mt-4 space-y-2 max-h-60 overflow-y-auto">
              {userSearchResult.users.map((user) => (
                <div
                  key={user.id}
                  className="flex items-center gap-3 p-3 rounded-lg border hover:bg-gray-50 cursor-pointer"
                  onClick={() => handleUserSelect(user)}
                >
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={user.image || ""} alt={user.name || ""} />
                    <AvatarFallback>{getUserInitials(user)}</AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <div className="font-medium">{user.name || "Unnamed User"}</div>
                    <div className="text-sm text-muted-foreground">{user.email}</div>
                  </div>
                  <Badge variant="outline">{user.role}</Badge>
                </div>
              ))}
              
              {userSearchResult.users.length === 0 && (
                <div className="text-center py-4 text-muted-foreground">
                  No users found matching "{searchQuery}"
                </div>
              )}
            </div>
          )}

          {isSearchingUsers && (
            <div className="mt-4 text-center py-4 text-muted-foreground">
              Searching users...
            </div>
          )}
        </CardContent>
      </Card>

      {/* Selected User & Role Assignment */}
      {selectedUser && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Role Assignment for {selectedUser.name || selectedUser.email}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Current Roles */}
            <div>
              <h4 className="font-medium mb-3">Current Roles</h4>
              {userRoles && userRoles.length > 0 ? (
                <div className="space-y-2">
                  {userRoles.filter(ur => ur.is_active).map((userRole) => {
                    const role = rolesResult?.roles?.find(r => r.id === userRole.roleId);
                    return (
                      <div key={userRole.id} className="flex items-center justify-between p-3 border rounded-lg">
                        <div>
                          <div className="font-medium">{role?.display_name || "Unknown Role"}</div>
                          <div className="text-sm text-muted-foreground">
                            Assigned: {new Date(userRole.assignedAt).toLocaleDateString()}
                          </div>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleRevokeRole(userRole.roleId, role?.display_name || "Role")}
                          disabled={revokeRoleMutation.isPending}
                        >
                          <X className="h-4 w-4" />
                          Revoke
                        </Button>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <div className="text-muted-foreground">No roles assigned</div>
              )}
            </div>

            {/* Assign New Role */}
            <div>
              <h4 className="font-medium mb-3">Assign New Role</h4>
              <div className="flex gap-3">
                <Select value={selectedRole} onValueChange={setSelectedRole}>
                  <SelectTrigger className="flex-1">
                    <SelectValue placeholder="Select a role to assign" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableRoles.map((role) => (
                      <SelectItem key={role.id} value={role.id}>
                        <div>
                          <div className="font-medium">{role.display_name}</div>
                          <div className="text-sm text-muted-foreground">{role.description}</div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Button
                  onClick={handleAssignRole}
                  disabled={!selectedRole || assignRoleMutation.isPending}
                >
                  {assignRoleMutation.isPending ? "Assigning..." : "Assign Role"}
                </Button>
              </div>
              
              {availableRoles.length === 0 && (
                <div className="text-sm text-muted-foreground mt-2">
                  All available roles have been assigned to this user.
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
