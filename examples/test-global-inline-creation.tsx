/**
 * Test Global Inline Creation Implementation
 * 
 * File ini untuk testing implementasi global inline creation
 * yang sudah dibuat dengan Zustand store.
 */

"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { SelectWithInlineCreation, mapToSelectOptions } from "@/components/ui/select-with-inline-creation";
import { useClassCategoriesByTenant } from "@/lib/hooks/queries/use-class-category-queries";
import { useClassLevelsByTenant } from "@/lib/hooks/queries/use-class-level-queries";
import { useLocations } from "@/lib/hooks/queries/use-location-queries";

const testSchema = z.object({
  tenantId: z.number().int().positive(),
  name: z.string().min(1, "Name is required").max(255),
  categoryId: z.string().min(1, "Category is required"),
  level_id: z.string().optional(),
  location_id: z.string().optional(),
});

type TestFormData = z.infer<typeof testSchema>;

interface TestFormProps {
  tenantId: number;
  onSubmit: (data: TestFormData) => Promise<void>;
  onCancel: () => void;
}

export function TestGlobalInlineCreationForm({ 
  tenantId, 
  onSubmit, 
  onCancel 
}: TestFormProps) {
  const [submitError, setSubmitError] = useState<string | null>(null);

  // Data queries dengan refetch capabilities
  const { 
    data: categories = [], 
    refetch: refetchCategories 
  } = useClassCategoriesByTenant(tenantId);
  
  const { 
    data: classLevels = [], 
    refetch: refetchClassLevels 
  } = useClassLevelsByTenant(tenantId, true);
  
  const { 
    data: locations = [], 
    refetch: refetchLocations 
  } = useLocations({ tenantId });

  const form = useForm<TestFormData>({
    resolver: zodResolver(testSchema),
    defaultValues: {
      tenantId,
      name: "",
      categoryId: "",
      level_id: undefined,
      location_id: undefined,
    },
    mode: "onChange",
  });

  const handleFormSubmit = async (data: TestFormData) => {
    try {
      setSubmitError(null);
      await onSubmit(data);
    } catch (error) {
      setSubmitError(error instanceof Error ? error.message : "An error occurred");
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>Test Global Inline Creation</CardTitle>
        <CardDescription>
          Testing form dengan global inline creation menggunakan Zustand store.
        </CardDescription>
      </CardHeader>

      <CardContent>
        <form onSubmit={form.handleSubmit(handleFormSubmit)} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Left Column */}
            <div className="space-y-4">
              {/* Name */}
              <div className="space-y-2">
                <Label htmlFor="name">Name *</Label>
                <Input
                  id="name"
                  {...form.register("name")}
                  placeholder="Enter name"
                />
                {form.formState.errors.name && (
                  <p className="text-sm text-red-600">{form.formState.errors.name.message}</p>
                )}
              </div>

              {/* Class Category dengan Global Inline Creation */}
              <div className="space-y-2">
                <Label htmlFor="categoryId">Class Category *</Label>
                <SelectWithInlineCreation
                  value={form.watch("categoryId")}
                  onValueChange={(value) => form.setValue("categoryId", value)}
                  options={mapToSelectOptions(categories)}
                  entityType="class-category"
                  tenantId={tenantId}
                  placeholder="Select class category"
                  onEntityCreated={(newCategory) => {
                    form.setValue("categoryId", newCategory.id);
                  }}
                  refetchData={refetchCategories}
                />
                {form.formState.errors.categoryId && (
                  <p className="text-sm text-red-600">{form.formState.errors.categoryId.message}</p>
                )}
              </div>
            </div>

            {/* Right Column */}
            <div className="space-y-4">
              {/* Class Level dengan Global Inline Creation */}
              <div className="space-y-2">
                <Label htmlFor="level_id">Class Level (Optional)</Label>
                <SelectWithInlineCreation
                  value={form.watch("level_id") || "not_set"}
                  onValueChange={(value) => form.setValue("level_id", value === "not_set" ? undefined : value)}
                  options={mapToSelectOptions(classLevels)}
                  entityType="class-level"
                  tenantId={tenantId}
                  placeholder="Select class level"
                  emptyOption={{ value: "not_set", label: "No level set" }}
                  onEntityCreated={(newLevel) => {
                    form.setValue("level_id", newLevel.id);
                  }}
                  refetchData={refetchClassLevels}
                />
              </div>

              {/* Location dengan Global Inline Creation */}
              <div className="space-y-2">
                <Label htmlFor="location_id">Location (Optional)</Label>
                <SelectWithInlineCreation
                  value={form.watch("location_id") || "not_set"}
                  onValueChange={(value) => form.setValue("location_id", value === "not_set" ? undefined : value)}
                  options={mapToSelectOptions(locations)}
                  entityType="location"
                  tenantId={tenantId}
                  placeholder="Select location"
                  emptyOption={{ value: "not_set", label: "No location set" }}
                  onEntityCreated={(newLocation) => {
                    form.setValue("location_id", newLocation.id);
                  }}
                  refetchData={refetchLocations}
                />
              </div>
            </div>
          </div>

          {/* Error Display */}
          {submitError && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600">{submitError}</p>
            </div>
          )}

          {/* Form Actions */}
          <div className="flex gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={!form.formState.isValid}
              className="flex-1"
            >
              Submit Test
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}

/**
 * Usage Example:
 * 
 * 1. Add InlineCreationModal to your layout:
 * ```tsx
 * import { InlineCreationModal } from '@/components/ui/inline-creation-modal';
 * 
 * export default function Layout({ children }) {
 *   return (
 *     <div>
 *       {children}
 *       <InlineCreationModal />
 *     </div>
 *   );
 * }
 * ```
 * 
 * 2. Use the test form:
 * ```tsx
 * <TestGlobalInlineCreationForm
 *   tenantId={1}
 *   onSubmit={async (data) => console.log(data)}
 *   onCancel={() => console.log('cancelled')}
 * />
 * ```
 * 
 * Expected Behavior:
 * - Clicking "Add New Class Category" opens modal
 * - Creating new category auto-selects it
 * - Same for Class Level and Location
 * - All handled by global Zustand store
 * - No individual modal state management needed
 */
