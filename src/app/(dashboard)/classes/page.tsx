import { Metadata } from "next";
import { ClassesManagement } from "@/components/classes/classes-management";

/**
 * Classes Dashboard Page
 * 
 * Page untuk manage classes dengan full CRUD functionality.
 * Mengikuti pattern yang sama dengan class-categories dan class-subcategories pages.
 */

export const metadata: Metadata = {
  title: "Classes | Dashboard",
  description: "Manage your classes and organize them by categories and subcategories",
};

export default function ClassesPage() {
  // In a real app, you would get the tenant ID from the session or context
  // Untuk sekarang kita hardcode dulu, nanti bisa diambil dari auth system
  const tenantId = 1; // This should come from your auth/session system

  return (
    <div className="container mx-auto py-6">
      <ClassesManagement tenantId={tenantId} />
    </div>
  );
}
