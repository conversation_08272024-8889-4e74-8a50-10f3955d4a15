/**
 * Fix Sessions Table Migration Conflict
 * 
 * This script resolves the conflict between the existing sessions table
 * and the new schema definition by safely dropping and recreating the table.
 * 
 * Since NextAuth is configured to use JWT strategy, the sessions table
 * is not actively used for session storage, making this operation safe.
 */

import { db } from '@/lib/db';
import { sql } from 'drizzle-orm';

async function fixSessionsTable() {
  console.log('🔧 Starting sessions table migration fix...');

  try {
    // Check if the old sessions table exists
    const tableExists = await db.execute(sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'sessions'
      );
    `);

    const exists = tableExists.rows[0]?.exists;
    console.log(`📋 Sessions table exists: ${exists}`);

    if (exists) {
      console.log('🗑️ Dropping existing sessions table...');
      
      // Drop the existing sessions table
      await db.execute(sql`DROP TABLE IF EXISTS sessions CASCADE;`);
      
      console.log('✅ Successfully dropped existing sessions table');
    }

    // Check if there are any foreign key constraints that reference the sessions table
    const constraints = await db.execute(sql`
      SELECT 
        tc.constraint_name,
        tc.table_name,
        kcu.column_name,
        ccu.table_name AS foreign_table_name,
        ccu.column_name AS foreign_column_name
      FROM 
        information_schema.table_constraints AS tc 
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
        JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
      WHERE 
        tc.constraint_type = 'FOREIGN KEY' 
        AND (ccu.table_name = 'sessions' OR tc.table_name = 'sessions');
    `);

    if (constraints.rows.length > 0) {
      console.log('🔗 Found foreign key constraints related to sessions table:');
      constraints.rows.forEach((row: any) => {
        console.log(`  - ${row.table_name}.${row.column_name} -> ${row.foreign_table_name}.${row.foreign_column_name}`);
      });
    }

    console.log('✅ Sessions table migration fix completed successfully!');
    console.log('');
    console.log('📝 Next steps:');
    console.log('1. Run: npm run db:generate');
    console.log('2. Run: npm run db:migrate');
    console.log('');
    console.log('ℹ️ Note: Since NextAuth is using JWT strategy, the sessions table');
    console.log('   is not used for active session storage, so this operation is safe.');

  } catch (error) {
    console.error('❌ Error fixing sessions table:', error);
    throw error;
  }
}

// Run the fix if this script is executed directly
if (require.main === module) {
  fixSessionsTable()
    .then(() => {
      console.log('🎉 Migration fix completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Migration fix failed:', error);
      process.exit(1);
    });
}

export { fixSessionsTable };
