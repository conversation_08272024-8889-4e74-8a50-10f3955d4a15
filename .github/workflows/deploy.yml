name: Deploy to Production

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

jobs:
  test:
    name: Test & Build
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run linting
        run: npm run lint

      - name: Run type checking
        run: npx tsc --noEmit

      - name: Run tests
        run: echo "Tests will be implemented later"
        env:
          CI: true

      - name: Setup PostgreSQL
        uses: harmon758/postgresql-action@v1
        with:
          postgresql version: '14'
          postgresql db: 'test_db'
          postgresql user: 'test_user'
          postgresql password: 'test_password'

      - name: Build application
        run: npm run build
        env:
          NODE_ENV: production
          DB_HOST: "localhost"
          DB_PORT: "5432"
          DB_USER: "test_user"
          DB_PASSWORD: "test_password"
          DB_NAME: "test_db"
          DATABASE_URL: "postgresql://test_user:test_password@localhost:5432/test_db"
          NEXTAUTH_SECRET: "test-secret-key"
          NEXTAUTH_URL: "http://localhost:3000"
          AUTH_SECRET: "test-secret-key"
          STRIPE_SECRET_KEY: "sk_test_fake"
          STRIPE_PUBLISHABLE_KEY: "pk_test_fake"
          NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: "pk_test_fake"
          STRIPE_WEBHOOK_SECRET: "whsec_fake"
          APP_URL: "http://localhost:3000"

  deploy:
    name: Deploy Notification
    runs-on: ubuntu-latest
    needs: test
    if: github.ref == 'refs/heads/main'

    steps:
      - name: Deployment Ready
        run: |
          echo "✅ Build successful! Ready for deployment."
          echo "🚀 You can now deploy to your preferred platform:"
          echo "   - Vercel: Connect your GitHub repo"
          echo "   - Cloudflare Pages: Connect your GitHub repo"
          echo "   - Netlify: Connect your GitHub repo"
          echo "   - Docker: Use the provided Dockerfile"
