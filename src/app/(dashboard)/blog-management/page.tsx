"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  Plus, 
  Search, 
  Filter, 
  More<PERSON><PERSON><PERSON><PERSON>, 
  Edit, 
  Trash2, 
  <PERSON>, 
  <PERSON><PERSON>,
  FileText,
  TrendingUp,
  Users,
  Calendar,
  Star,
  Loader2,
  AlertCircle,
  CheckCircle2
} from "lucide-react";
import { BlogPostForm } from "@/components/forms/blog-post-form";
import { 
  useBlogPostsByTenant, 
  useDeleteBlogPost, 
  useBlogCategoriesByTenant,
  type BlogPostWithAuthor 
} from "@/lib/hooks/queries/use-blog-queries";
import { toast } from "sonner";
import { format } from "date-fns";

export default function BlogPage() {
  // Hardcode tenantId untuk development
  const tenantId = 1;
  
  // State management untuk UI
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [categoryFilter, setCategoryFilter] = useState<string>("all");
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [selectedPost, setSelectedPost] = useState<BlogPostWithAuthor | undefined>();

  // Data fetching dengan proper filters
  const { 
    data: blogPosts = [], 
    isLoading, 
    error 
  } = useBlogPostsByTenant(tenantId, {
    filters: {
      search: searchQuery || undefined,
      status: statusFilter !== "all" ? statusFilter as "draft" | "published" : undefined,
      category_id: categoryFilter !== "all" ? categoryFilter : undefined,
    }
  });

  const { data: categories = [] } = useBlogCategoriesByTenant(tenantId, {
    filters: { is_active: true }
  });

  // Mutations dengan proper error handling
  const deletePostMutation = useDeleteBlogPost();

  // Event handlers
  const handleCreatePost = () => {
    setSelectedPost(undefined);
    setIsFormOpen(true);
  };

  const handleEditPost = (post: BlogPostWithAuthor) => {
    setSelectedPost(post);
    setIsFormOpen(true);
  };

  const handleDeletePost = async (post: BlogPostWithAuthor) => {
    if (!confirm(`Yakin ingin menghapus post "${post.title}"?`)) return;
    
    try {
      await deletePostMutation.mutateAsync(post.id);
      toast.success("Post berhasil dihapus");
    } catch (error) {
      toast.error("Gagal menghapus post");
    }
  };

  const handleCopySlug = (slug: string) => {
    navigator.clipboard.writeText(slug);
    toast.success("Slug berhasil disalin");
  };

  // Helper functions
  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case "published":
        return "bg-green-100 text-green-800";
      case "draft":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case "published":
        return "Published";
      case "draft":
        return "Draft";
      default:
        return status;
    }
  };

  // Error state handling
  if (error) {
    return (
      <div className="container mx-auto py-6">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Gagal memuat blog posts. Silakan coba lagi nanti.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header dengan Search dan Filters */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Blog Management</h1>
          <p className="text-gray-600">Kelola blog posts dan konten untuk website</p>
        </div>
        
        <Button onClick={handleCreatePost}>
          <Plus className="mr-2 h-4 w-4" />
          Add Post
        </Button>
      </div>

      {/* Search dan Filter Controls */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search Input */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Cari blog post berdasarkan judul atau konten..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            {/* Status Filter */}
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="published">Published</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
              </SelectContent>
            </Select>

            {/* Category Filter */}
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-full sm:w-48">
                <SelectValue placeholder="Filter by category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Blog Posts Table */}
      <Card>
        <CardHeader>
          <CardTitle>Blog Posts</CardTitle>
          <CardDescription>
            {blogPosts.length} post{blogPosts.length !== 1 ? 's' : ''} ditemukan
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">Loading blog posts...</span>
            </div>
          ) : blogPosts.length === 0 ? (
            <div className="text-center py-8">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Tidak ada blog posts</h3>
              <p className="text-gray-500 mb-4">
                {searchQuery || statusFilter !== "all" || categoryFilter !== "all"
                  ? "Coba sesuaikan filter atau kata kunci pencarian."
                  : "Mulai dengan membuat blog post pertama Anda."}
              </p>
              {!searchQuery && statusFilter === "all" && categoryFilter === "all" && (
                <Button onClick={handleCreatePost}>
                  <Plus className="mr-2 h-4 w-4" />
                  Create First Post
                </Button>
              )}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Title</TableHead>
                    <TableHead>Category</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Author</TableHead>
                    <TableHead>Views</TableHead>
                    <TableHead>Published</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {blogPosts.map((post) => (
                    <TableRow key={post.id}>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="font-medium flex items-center gap-2">
                            {post.title}
                            {post.is_featured && (
                              <Star className="h-4 w-4 text-yellow-500 fill-current" />
                            )}
                          </div>
                          <div className="text-sm text-gray-500 font-mono">
                            /{post.slug}
                          </div>
                          {post.excerpt && (
                            <div className="text-sm text-gray-500 line-clamp-2">
                              {post.excerpt}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        {post.category ? (
                          <Badge 
                            variant="outline"
                            style={{ 
                              backgroundColor: post.category.color ? `${post.category.color}20` : undefined,
                              borderColor: post.category.color || undefined 
                            }}
                          >
                            {post.category.name}
                          </Badge>
                        ) : (
                          <span className="text-gray-400">No category</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <Badge className={getStatusBadgeColor(post.status)}>
                          {getStatusLabel(post.status)}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div className="font-medium">{post.author.name}</div>
                          <div className="text-gray-500">{post.author.email}</div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Eye className="h-4 w-4 text-gray-400" />
                          <span className="text-sm">{post.view_count}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        {post.published_at ? (
                          <div className="text-sm">
                            {format(new Date(post.published_at), 'MMM dd, yyyy')}
                          </div>
                        ) : (
                          <span className="text-gray-400">Not published</span>
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => handleEditPost(post)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleCopySlug(post.slug)}>
                              <Copy className="mr-2 h-4 w-4" />
                              Copy Slug
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => handleDeletePost(post)}
                              className="text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Blog Post Form Dialog */}
      <BlogPostForm
        open={isFormOpen}
        onClose={() => {
          setIsFormOpen(false);
          setSelectedPost(undefined);
        }}
        title={selectedPost ? "Edit Blog Post" : "Add New Blog Post"}
        description={selectedPost ? "Update blog post content and settings" : "Create a new blog post with content and SEO settings"}
        blogPost={selectedPost}
        tenantId={tenantId}
      />
    </div>
  );
}
