import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { auth } from "@/lib/auth/config";
import { db } from "@/lib/db";
import { users } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { generatePasswordResetToken, generateEmailVerificationToken } from "@/lib/auth/utils";
import { sendPasswordResetEmail, sendVerificationEmail } from "@/lib/email/send";
import { RBACHelpers } from "@/lib/auth/rbac-integration";

const userActionSchema = z.object({
  action: z.string().min(1, "Action is required"),
});

// ✅ PATCH /api/admin/users/[userId] - User actions (admin only)
export async function PATCH(
  request: NextRequest,
  { params }: { params: { userId: string } }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Only super admins and tenant admins can perform user actions
    const isSuperAdmin = RBACHelpers.isSuperAdmin(session);
    const isTenantAdmin = RBACHelpers.isTenantAdmin(session);

    if (!isSuperAdmin && !isTenantAdmin) {
      return NextResponse.json({ error: "Forbidden - Admin access required" }, { status: 403 });
    }

    const { userId } = params;
    const body = await request.json();
    const { action } = userActionSchema.parse(body);

    // Get user
    const [user] = await db
      .select()
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);

    if (!user) {
      return NextResponse.json(
        { error: "User not found" },
        { status: 404 }
      );
    }

    // Handle different actions
    if (action === "activate") {
      await db
        .update(users)
        .set({ isActive: true, updatedAt: new Date() })
        .where(eq(users.id, userId));
    } else if (action === "deactivate") {
      await db
        .update(users)
        .set({ isActive: false, updatedAt: new Date() })
        .where(eq(users.id, userId));
    } else if (action.startsWith("change_role:")) {
      // ✅ Handle dynamic role changes: "change_role:super_admin", "change_role:tenant_admin", etc.
      const newRole = action.replace("change_role:", "");
      await db
        .update(users)
        .set({ role: newRole, updatedAt: new Date() })
        .where(eq(users.id, userId));
    } else if (action === "promote") {
      // Legacy support: promote to tenant_admin
      await db
        .update(users)
        .set({ role: "tenant_admin", updatedAt: new Date() })
        .where(eq(users.id, userId));
    } else if (action === "demote") {
      // Legacy support: demote to customer
      await db
        .update(users)
        .set({ role: "customer", updatedAt: new Date() })
        .where(eq(users.id, userId));

    } else if (action === "reset_password") {
      const resetToken = await generatePasswordResetToken(user.email);
      if (resetToken) {
        await sendPasswordResetEmail(user.email, resetToken);
      }
    } else if (action === "send_verification") {
      if (!user.emailVerified) {
        const verificationToken = await generateEmailVerificationToken(user.email);
        await sendVerificationEmail(user.email, user.name || "User", verificationToken);
      }
    } else {
      return NextResponse.json(
        { error: "Invalid action" },
        { status: 400 }
      );
    }

      return NextResponse.json({ success: true });
    } catch (error) {
      console.error("Error performing user action:", error);

      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { error: "Validation failed", details: error.errors },
          { status: 400 }
        );
      }

      return NextResponse.json(
        { error: "Internal server error" },
        { status: 500 }
      );
    }
}
