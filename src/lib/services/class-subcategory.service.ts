import { db } from "@/lib/db";
import { class_subcategories, type ClassSubcategory, type NewClassSubcategory } from "@/lib/db/schema";
import { eq, and, ilike, desc, sql } from "drizzle-orm";
import { createId } from "@paralleldrive/cuid2";

/**
 * Class Subcategory Service
 * 
 * Service untuk manage class subcategories dengan pattern simple seperti ClassCategoryService.
 */

// Interface untuk search results
export interface ClassSubcategorySearchResult {
  subcategories: ClassSubcategory[];
  total: number;
  hasMore: boolean;
}

/**
 * ClassSubcategoryService - Service untuk manage class subcategories
 */
export class ClassSubcategoryService {
  /**
   * Search class subcategories dengan filtering dan pagination
   */
  static async searchSubcategories(
    tenantId: number,
    categoryId?: string,
    search?: string,
    limit = 20,
    offset = 0
  ): Promise<ClassSubcategorySearchResult> {
    try {


      // Build where conditions
      let whereConditions = eq(class_subcategories.tenantId, tenantId);

      // Filter by category if provided
      if (categoryId) {
        whereConditions = and(
          whereConditions,
          eq(class_subcategories.categoryId, categoryId)
        ) as any;
      }

      // Kalau ada search term, tambahin filter nama
      if (search && search.trim()) {
        whereConditions = and(
          whereConditions,
          ilike(class_subcategories.name, `%${search.trim()}%`)
        ) as any;
      }

      // Query dengan pagination
      const subcategories = await db
        .select()
        .from(class_subcategories)
        .where(whereConditions)
        .orderBy(desc(class_subcategories.createdAt))
        .limit(limit + 1) // +1 untuk cek hasMore
        .offset(offset);

      // Cek apakah ada data lebih
      const hasMore = subcategories.length > limit;
      if (hasMore) {
        subcategories.pop(); // Hapus item extra
      }

      // Count total untuk metadata
      const totalResult = await db
        .select({ count: sql<number>`count(*)` })
        .from(class_subcategories)
        .where(whereConditions);

      const total = Number(totalResult[0]?.count || 0);

      return {
        subcategories,
        total,
        hasMore,
      };
    } catch (error) {
      console.error("Error searching class subcategories:", error);
      throw error;
    }
  }

  /**
   * Get all class subcategories by tenant ID
   */
  static async getByTenantId(tenantId: number): Promise<ClassSubcategory[]> {
    return await db
      .select()
      .from(class_subcategories)
      .where(eq(class_subcategories.tenantId, tenantId))
      .orderBy(desc(class_subcategories.createdAt));
  }

  /**
   * Get class subcategories by category ID
   */
  static async getByCategoryId(categoryId: string): Promise<ClassSubcategory[]> {
    return await db
      .select()
      .from(class_subcategories)
      .where(eq(class_subcategories.categoryId, categoryId))
      .orderBy(desc(class_subcategories.createdAt));
  }

  /**
   * Get class subcategory by ID
   */
  static async getById(id: string): Promise<ClassSubcategory | null> {
    const [subcategory] = await db
      .select()
      .from(class_subcategories)
      .where(eq(class_subcategories.id, id))
      .limit(1);
    return subcategory || null;
  }

  /**
   * Create class subcategory
   */
  static async create(data: { tenantId: number; categoryId: string; name: string }): Promise<ClassSubcategory> {
    const [subcategory] = await db
      .insert(class_subcategories)
      .values({
        id: createId(),
        tenantId: data.tenantId,
        categoryId: data.categoryId,
        name: data.name,
      })
      .returning();
    return subcategory;
  }

  /**
   * Update class subcategory
   */
  static async update(id: string, data: { name: string }): Promise<ClassSubcategory | null> {
    const [subcategory] = await db
      .update(class_subcategories)
      .set({ name: data.name })
      .where(eq(class_subcategories.id, id))
      .returning();
    return subcategory || null;
  }

  /**
   * Delete class subcategory
   */
  static async delete(id: string): Promise<ClassSubcategory> {
    // First check if subcategory exists
    const existingSubcategory = await this.getById(id);
    if (!existingSubcategory) {
      throw new Error("Class subcategory not found");
    }

    // Delete the subcategory and return the deleted record
    const [deletedSubcategory] = await db
      .delete(class_subcategories)
      .where(eq(class_subcategories.id, id))
      .returning();

    if (!deletedSubcategory) {
      throw new Error("Failed to delete class subcategory");
    }

    return deletedSubcategory;
  }
}
