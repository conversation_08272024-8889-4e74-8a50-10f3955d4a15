"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Checkbox } from "@/components/ui/checkbox";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  DollarSign,
  Edit,
  Trash2,
  Plus,
  Search,
  Building,
  AlertCircle,
  GripVertical,
  Eye,
  EyeOff,
  MoreHorizontal,
  Star,
  StarOff,
  Copy,
  Percent
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { PricingGroup } from "@/lib/db/schema";
import { 
  usePricingGroups, 
  useDeletePricingGroup, 
  useTogglePricingGroupActive,
  useTogglePricingGroupDefault,
  useReorderPricingGroups,
  useBulkPricingGroupOperation,
  useDuplicatePricingGroup
} from "@/lib/hooks/queries/use-pricing-group-queries";
import { DeleteConfirmationDialog } from "@/components/ui/delete-confirmation-dialog";
import { DeleteSuccessToast } from "@/components/ui/delete-success-toast";

interface PricingGroupListProps {
  onEdit?: (pricingGroup: PricingGroup) => void;
  onAdd?: () => void;
  showActions?: boolean;
  tenantId?: number;
}

export function PricingGroupList({
  onEdit,
  onAdd,
  showActions = true,
  tenantId,
}: PricingGroupListProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [deleteError, setDeleteError] = useState<string | null>(null);
  const [deleteConfirmation, setDeleteConfirmation] = useState<{
    isOpen: boolean;
    pricingGroup: PricingGroup | null;
  }>({ isOpen: false, pricingGroup: null });
  const [showDeleteSuccess, setShowDeleteSuccess] = useState(false);
  const [deletedPricingGroupName, setDeletedPricingGroupName] = useState("");

  const { data: pricingGroups = [], isLoading, error } = usePricingGroups({
    tenantId,
    filters: searchQuery ? { search: searchQuery } : undefined,
  });
  const deletePricingGroupMutation = useDeletePricingGroup();
  const toggleActiveMutation = useTogglePricingGroupActive();
  const toggleDefaultMutation = useTogglePricingGroupDefault();
  const reorderMutation = useReorderPricingGroups();
  const bulkOperationMutation = useBulkPricingGroupOperation();
  const duplicateMutation = useDuplicatePricingGroup();

  const handleDeleteClick = (pricingGroup: PricingGroup) => {
    setDeleteConfirmation({ isOpen: true, pricingGroup });
  };

  const handleDeleteConfirm = async () => {
    if (!deleteConfirmation.pricingGroup) return;

    const pricingGroupName = deleteConfirmation.pricingGroup.name || "Unknown pricing group";

    try {
      setDeleteError(null);
      await deletePricingGroupMutation.mutateAsync(deleteConfirmation.pricingGroup.id);

      setDeleteConfirmation({ isOpen: false, pricingGroup: null });
      setDeletedPricingGroupName(pricingGroupName);
      setShowDeleteSuccess(true);

    } catch (error) {
      setDeleteError(error instanceof Error ? error.message : "Failed to delete pricing group");
    }
  };

  const handleDeleteCancel = () => {
    setDeleteConfirmation({ isOpen: false, pricingGroup: null });
  };

  const handleToggleActive = async (pricingGroup: PricingGroup) => {
    try {
      await toggleActiveMutation.mutateAsync(pricingGroup.id);
    } catch (error) {
      setDeleteError(error instanceof Error ? error.message : "Failed to toggle pricing group status");
    }
  };

  const handleToggleDefault = async (pricingGroup: PricingGroup) => {
    try {
      await toggleDefaultMutation.mutateAsync(pricingGroup.id);
    } catch (error) {
      setDeleteError(error instanceof Error ? error.message : "Failed to toggle pricing group default status");
    }
  };

  const handleDuplicate = async (pricingGroup: PricingGroup) => {
    try {
      await duplicateMutation.mutateAsync({ 
        id: pricingGroup.id, 
        name: `${pricingGroup.name} (Copy)` 
      });
    } catch (error) {
      setDeleteError(error instanceof Error ? error.message : "Failed to duplicate pricing group");
    }
  };

  const handleSelectItem = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedItems([...selectedItems, id]);
    } else {
      setSelectedItems(selectedItems.filter(item => item !== id));
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedItems(pricingGroups.map(pg => pg.id));
    } else {
      setSelectedItems([]);
    }
  };

  const handleBulkOperation = async (action: 'activate' | 'deactivate' | 'set-default' | 'unset-default' | 'delete') => {
    if (selectedItems.length === 0) return;

    try {
      await bulkOperationMutation.mutateAsync({ ids: selectedItems, action });
      setSelectedItems([]);
    } catch (error) {
      setDeleteError(error instanceof Error ? error.message : `Failed to ${action} pricing groups`);
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="py-8">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Failed to load pricing groups. Please try again.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              Pricing Groups
            </CardTitle>
            <CardDescription>
              Manage pricing groups and their discount settings
            </CardDescription>
          </div>
          {showActions && onAdd && (
            <Button onClick={onAdd}>
              <Plus className="h-4 w-4 mr-2" />
              Add Pricing Group
            </Button>
          )}
        </div>

        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search pricing groups..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Bulk Actions */}
        {selectedItems.length > 0 && (
          <div className="flex items-center gap-2 p-3 bg-blue-50 rounded-lg">
            <span className="text-sm text-blue-700">
              {selectedItems.length} item(s) selected
            </span>
            <div className="flex gap-2 ml-auto">
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleBulkOperation('activate')}
                disabled={bulkOperationMutation.isPending}
              >
                <Eye className="h-4 w-4 mr-1" />
                Activate
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleBulkOperation('deactivate')}
                disabled={bulkOperationMutation.isPending}
              >
                <EyeOff className="h-4 w-4 mr-1" />
                Deactivate
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleBulkOperation('set-default')}
                disabled={bulkOperationMutation.isPending || selectedItems.length > 1}
                title={selectedItems.length > 1 ? "Only one item can be set as default" : ""}
              >
                <Star className="h-4 w-4 mr-1" />
                Set Default
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleBulkOperation('delete')}
                disabled={bulkOperationMutation.isPending}
                className="text-red-600 hover:text-red-700"
              >
                <Trash2 className="h-4 w-4 mr-1" />
                Delete
              </Button>
            </div>
          </div>
        )}

        {/* Delete Error */}
        {deleteError && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{deleteError}</AlertDescription>
          </Alert>
        )}
      </CardHeader>

      <CardContent>
        {/* Pricing Group List */}
        {pricingGroups.length === 0 ? (
          <div className="text-center py-8">
            <DollarSign className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              {searchQuery ? "No pricing groups found" : "No pricing groups yet"}
            </h3>
            <p className="text-gray-500 dark:text-gray-400 mb-4">
              {searchQuery 
                ? "Try adjusting your search terms."
                : "Get started by adding your first pricing group."
              }
            </p>
            {showActions && onAdd && !searchQuery && (
              <Button onClick={onAdd}>
                <Plus className="h-4 w-4 mr-2" />
                Add Pricing Group
              </Button>
            )}
          </div>
        ) : (
          <div className="space-y-2">
            {/* Select All */}
            <div className="flex items-center gap-2 p-2 border-b">
              <Checkbox
                checked={selectedItems.length === pricingGroups.length}
                onCheckedChange={handleSelectAll}
              />
              <span className="text-sm text-gray-600">Select All</span>
            </div>

            {pricingGroups.map((pricingGroup) => (
              <div
                key={pricingGroup.id}
                className="border rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
              >
                <div className="flex items-center gap-3">
                  {/* Checkbox */}
                  <Checkbox
                    checked={selectedItems.includes(pricingGroup.id)}
                    onCheckedChange={(checked) => handleSelectItem(pricingGroup.id, checked as boolean)}
                  />

                  {/* Drag Handle */}
                  <GripVertical className="h-4 w-4 text-gray-400 cursor-grab" />

                  {/* Content */}
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <DollarSign className="h-4 w-4 text-gray-500" />
                      <h4 className="font-medium text-gray-900 dark:text-gray-100">
                        {pricingGroup.name}
                      </h4>
                      <Badge variant={pricingGroup.isActive ? "default" : "secondary"}>
                        {pricingGroup.isActive ? "Active" : "Inactive"}
                      </Badge>
                      <Badge variant={pricingGroup.isDefault ? "destructive" : "outline"}>
                        {pricingGroup.isDefault ? "Default" : "Standard"}
                      </Badge>
                      {(pricingGroup.discountPercentage ?? 0) > 0 && (
                        <Badge variant="outline" className="text-xs flex items-center gap-1">
                          <Percent className="h-3 w-3" />
                          {pricingGroup.discountPercentage}% off
                        </Badge>
                      )}
                      <Badge variant="outline" className="text-xs">
                        Order: {pricingGroup.sortOrder}
                      </Badge>
                    </div>
                    
                    {pricingGroup.description && (
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                        {pricingGroup.description}
                      </p>
                    )}

                    <div className="flex items-center gap-2">
                      <Badge variant="secondary" className="text-xs">
                        Tenant ID: {pricingGroup.tenantId}
                      </Badge>
                    </div>
                  </div>

                  {/* Active Toggle */}
                  <div className="flex items-center gap-2">
                    <Switch
                      checked={pricingGroup.isActive}
                      onCheckedChange={() => handleToggleActive(pricingGroup)}
                      disabled={toggleActiveMutation.isPending}
                    />
                  </div>

                  {/* Default Toggle */}
                  <div className="flex items-center gap-2">
                    <Switch
                      checked={pricingGroup.isDefault}
                      onCheckedChange={() => handleToggleDefault(pricingGroup)}
                      disabled={toggleDefaultMutation.isPending}
                    />
                    <span className="text-xs text-gray-500">
                      {pricingGroup.isDefault ? "Default" : "Standard"}
                    </span>
                  </div>

                  {/* Actions */}
                  {showActions && (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        {onEdit && (
                          <DropdownMenuItem onClick={() => onEdit(pricingGroup)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuItem onClick={() => handleDuplicate(pricingGroup)}>
                          <Copy className="h-4 w-4 mr-2" />
                          Duplicate
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleToggleActive(pricingGroup)}>
                          {pricingGroup.isActive ? (
                            <>
                              <EyeOff className="h-4 w-4 mr-2" />
                              Deactivate
                            </>
                          ) : (
                            <>
                              <Eye className="h-4 w-4 mr-2" />
                              Activate
                            </>
                          )}
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleToggleDefault(pricingGroup)}>
                          {pricingGroup.isDefault ? (
                            <>
                              <StarOff className="h-4 w-4 mr-2" />
                              Unset Default
                            </>
                          ) : (
                            <>
                              <Star className="h-4 w-4 mr-2" />
                              Set as Default
                            </>
                          )}
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem 
                          onClick={() => handleDeleteClick(pricingGroup)}
                          className="text-red-600"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Summary */}
        {pricingGroups.length > 0 && (
          <div className="mt-4 pt-4 border-t text-sm text-gray-500 dark:text-gray-400">
            Showing {pricingGroups.length} pricing group(s)
          </div>
        )}
      </CardContent>

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationDialog
        isOpen={deleteConfirmation.isOpen}
        onClose={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        title="Delete Pricing Group"
        description="Are you sure you want to delete this pricing group? This action cannot be undone."
        itemName={deleteConfirmation.pricingGroup?.name || ""}
        isLoading={deletePricingGroupMutation.isPending}
      />

      {/* Delete Success Toast */}
      <DeleteSuccessToast
        isVisible={showDeleteSuccess}
        onClose={() => setShowDeleteSuccess(false)}
        itemName={deletedPricingGroupName}
      />
    </Card>
  );
}
