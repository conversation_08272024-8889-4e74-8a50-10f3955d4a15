"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rig<PERSON> } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Building2, MapPin, Settings, BarChart3, Users, Globe, Zap, Database, Table } from "lucide-react";
import { BusinessProfileTanStackForm } from "@/components/forms/business-profile-tanstack-form";
import { AddressesTable } from "@/components/tables/addresses-table";
import { VirtualAddressListWithSearch } from "@/components/virtual/virtual-address-list";
import { SettingsPanel } from "@/components/settings/settings-panel";

// Import TanStack Query hooks
import { use<PERSON>reate<PERSON><PERSON><PERSON>, useU<PERSON><PERSON><PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/lib/hooks/queries/use-address-queries";
import { useTenants } from "@/lib/hooks/use-tenants";
import { useBusinessProfile } from "@/lib/hooks/queries/use-business-profile-queries";
import { useAddresses, useAddressStats } from "@/lib/hooks/queries/use-address-queries";
import { Address } from "@/lib/db/schema";

export default function BusinessTanStackDashboard() {
  const [selectedTenantId, setSelectedTenantId] = useState<number | null>(null);
  const [addressViewMode, setAddressViewMode] = useState<"table" | "virtual">("table");

  // Get user's tenants
  const { tenants } = useTenants();
  
  // Get business profile for selected tenant
  const { data: profile, isLoading: profileLoading, error: profileError } = useBusinessProfile(
    selectedTenantId
  );

  // Get addresses for selected tenant
  const { data: addresses = [], isLoading: addressesLoading } = useAddresses(
    selectedTenantId
  );

  // Get address statistics
  const { data: addressStats } = useAddressStats(selectedTenantId);

  // TanStack mutations for addresses
  const createAddressMutation = useCreateAddress();
  const updateAddressMutation = useUpdateAddress();
  const deleteAddressMutation = useDeleteAddress();
  const acceptAddressMutation = useAcceptAddress();

  // Set default tenant if available
  useState(() => {
    if (tenants.length > 0 && !selectedTenantId) {
      setSelectedTenantId(tenants[0].id);
    }
  });

  const selectedTenant = tenants.find(t => t.id === selectedTenantId);

  // Handler functions for TanStack mutations
  const handleAddressCreate = async (data: any) => {
    await createAddressMutation.mutateAsync(data);
  };

  const handleAddressUpdate = async (id: string, data: any) => {
    await updateAddressMutation.mutateAsync({ id, data });
  };

  const handleAddressDelete = async (id: string) => {
    await deleteAddressMutation.mutateAsync(id);
  };

  const handleAddressAccept = async (id: string) => {
    await acceptAddressMutation.mutateAsync(id);
  };

  const handleSettingsUpdate = async (data: any) => {
    // This will be handled by SettingsPanel internally with TanStack
    console.log("Settings update:", data);
  };

  if (tenants.length === 0) {
    return (
      <div className="container mx-auto py-6">
        <Card>
          <CardContent className="text-center py-12">
            <Building2 className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">No tenants found</h3>
            <p className="text-muted-foreground mb-4">
              You need to be assigned to a tenant to manage business information.
            </p>
            <Button variant="outline">Contact Administrator</Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Zap className="h-8 w-8 text-primary" />
            TanStack Business Management
          </h1>
          <p className="text-muted-foreground">
            Powered by TanStack Query, Table, Form, and Virtual
          </p>
        </div>
        
        {/* Tenant Selector */}
        {tenants.length > 1 && (
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">Tenant:</span>
            <Select
              value={selectedTenantId?.toString() || ""}
              onValueChange={(value) => setSelectedTenantId(parseInt(value))}
            >
              <SelectTrigger className="w-[200px]">
                <SelectValue placeholder="Select tenant" />
              </SelectTrigger>
              <SelectContent>
                {tenants.map((tenant) => (
                  <SelectItem key={tenant.id} value={tenant.id.toString()}>
                    {tenant.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        )}
      </div>

      {/* Current Tenant Info */}
      {selectedTenant && (
        <Card className="mb-6">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Globe className="h-5 w-5" />
                <div>
                  <CardTitle>{selectedTenant.name}</CardTitle>
                  <CardDescription>
                    {selectedTenant.subdomain}.yourdomain.com
                    {selectedTenant.customDomain && ` • ${selectedTenant.customDomain}`}
                  </CardDescription>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant={selectedTenant.isActive ? "default" : "secondary"}>
                  {selectedTenant.isActive ? "Active" : "Inactive"}
                </Badge>
                <Badge variant="outline" className="bg-primary/10">
                  <Zap className="h-3 w-3 mr-1" />
                  TanStack Powered
                </Badge>
              </div>
            </div>
          </CardHeader>
        </Card>
      )}

      {/* TanStack Features Overview */}
      <div className="grid gap-4 md:grid-cols-4 mb-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">TanStack Query</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Active</div>
            <p className="text-xs text-muted-foreground">
              Caching & synchronization
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">TanStack Table</CardTitle>
            <Table className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{addresses.length}</div>
            <p className="text-xs text-muted-foreground">
              Rows in data table
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">TanStack Form</CardTitle>
            <Building2 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {profile ? "Valid" : "Empty"}
            </div>
            <p className="text-xs text-muted-foreground">
              Form validation status
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">TanStack Virtual</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Ready</div>
            <p className="text-xs text-muted-foreground">
              Virtual scrolling enabled
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      {selectedTenantId && (
        <Tabs defaultValue="profile" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="profile" className="flex items-center gap-2">
              <Building2 className="h-4 w-4" />
              TanStack Form
            </TabsTrigger>
            <TabsTrigger value="addresses" className="flex items-center gap-2">
              <Table className="h-4 w-4" />
              TanStack Table
            </TabsTrigger>
            <TabsTrigger value="virtual" className="flex items-center gap-2">
              <BarChart3 className="h-4 w-4" />
              TanStack Virtual
            </TabsTrigger>
            <TabsTrigger value="analytics" className="flex items-center gap-2">
              <Database className="h-4 w-4" />
              TanStack Query
            </TabsTrigger>
          </TabsList>

          <TabsContent value="profile">
            <div className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Zap className="h-5 w-5 text-primary" />
                    TanStack Form Demo
                  </CardTitle>
                  <CardDescription>
                    Advanced form with real-time validation, async validation, and optimistic updates
                  </CardDescription>
                </CardHeader>
              </Card>
              
              <BusinessProfileTanStackForm
                tenantId={selectedTenantId}
                profile={profile}
                onSuccess={() => {
                  // TanStack Query will automatically update the cache
                }}
              />
            </div>
          </TabsContent>

          <TabsContent value="addresses">
            <div className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Table className="h-5 w-5 text-primary" />
                    TanStack Table Demo
                  </CardTitle>
                  <CardDescription>
                    Advanced data table with sorting, filtering, pagination, and column visibility
                  </CardDescription>
                </CardHeader>
              </Card>

              <AddressesTable
                tenantId={selectedTenantId}
                onEdit={(address) => {
                  console.log("Edit address:", address);
                }}
                onAdd={() => {
                  console.log("Add new address");
                }}
                onAddressCreate={handleAddressCreate}
                onAddressUpdate={handleAddressUpdate}
                onAddressDelete={handleAddressDelete}
                onAddressAccept={handleAddressAccept}
              />
            </div>
          </TabsContent>

          <TabsContent value="virtual">
            <div className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="h-5 w-5 text-primary" />
                    TanStack Virtual Demo
                  </CardTitle>
                  <CardDescription>
                    Virtual scrolling for large datasets with infinite loading
                  </CardDescription>
                </CardHeader>
              </Card>

              <VirtualAddressListWithSearch
                tenantId={selectedTenantId}
                onEdit={(address) => {
                  console.log("Edit address:", address);
                }}
                onDelete={(address) => {
                  handleAddressDelete(address.id);
                }}
                onAccept={(address) => {
                  handleAddressAccept(address.id);
                }}
              />
            </div>
          </TabsContent>

          <TabsContent value="analytics">
            <div className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Database className="h-5 w-5 text-primary" />
                    TanStack Query Demo
                  </CardTitle>
                  <CardDescription>
                    Real-time data synchronization with caching and background updates
                  </CardDescription>
                </CardHeader>
              </Card>

              <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Profile Status</CardTitle>
                    <Building2 className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {profileLoading ? "Loading..." : profile ? "Complete" : "Incomplete"}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      {profileError ? "Error loading" : "Business profile status"}
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Addresses</CardTitle>
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {addressesLoading ? "..." : addressStats?.totalAddresses || 0}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      {addressStats?.acceptedAddresses || 0} accepted
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Cache Status</CardTitle>
                    <Database className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">Active</div>
                    <p className="text-xs text-muted-foreground">
                      TanStack Query caching
                    </p>
                  </CardContent>
                </Card>
              </div>

              {/* Query Status Indicators */}
              <Card>
                <CardHeader>
                  <CardTitle>Query Status</CardTitle>
                  <CardDescription>
                    Real-time status of TanStack Query operations
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Business Profile Query</span>
                      <Badge variant={profileLoading ? "secondary" : profile ? "default" : "destructive"}>
                        {profileLoading ? "Loading" : profile ? "Success" : "No Data"}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Addresses Query</span>
                      <Badge variant={addressesLoading ? "secondary" : "default"}>
                        {addressesLoading ? "Loading" : "Success"}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Address Stats Query</span>
                      <Badge variant={addressStats ? "default" : "secondary"}>
                        {addressStats ? "Cached" : "Loading"}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      )}
    </div>
  );
}
