"use client";

import React from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Plus } from "lucide-react";
import { useInlineCreation } from "@/lib/hooks/use-inline-creation";
import { EntityType } from "@/lib/stores/inline-creation-store";

export interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
}

export interface SelectWithInlineCreationProps {
  // Select props
  value?: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  
  // Options
  options: SelectOption[];
  emptyOption?: {
    value: string;
    label: string;
  };
  
  // Inline creation props
  entityType: EntityType;
  tenantId: number;
  onEntityCreated?: (newEntity: any) => void;
  refetchData?: () => void;
  
  // Styling
  className?: string;
  triggerClassName?: string;
  contentClassName?: string;
}

/**
 * Select component with inline creation functionality
 * 
 * This component provides a dropdown select with an "Add New" option
 * that opens an inline creation modal for the specified entity type.
 * 
 * Features:
 * - Standard select functionality
 * - Inline creation modal integration
 * - Auto-selection of newly created items
 * - Customizable styling and options
 * - Type-safe entity configuration
 * 
 * Usage:
 * ```tsx
 * <SelectWithInlineCreation
 *   value={selectedValue}
 *   onValueChange={setSelectedValue}
 *   options={items.map(item => ({ value: item.id, label: item.name }))}
 *   entityType="class-level"
 *   tenantId={tenantId}
 *   placeholder="Select class level"
 *   emptyOption={{ value: "none", label: "No level set" }}
 *   onEntityCreated={(newEntity) => setSelectedValue(newEntity.id)}
 *   refetchData={refetchClassLevels}
 * />
 * ```
 */
export function SelectWithInlineCreation({
  value,
  onValueChange,
  placeholder = "Select an option",
  disabled = false,
  options = [],
  emptyOption,
  entityType,
  tenantId,
  onEntityCreated,
  refetchData,
  className = "",
  triggerClassName = "",
  contentClassName = "",
}: SelectWithInlineCreationProps) {
  const { openInlineCreation } = useInlineCreation(tenantId);

  // Handle value change with inline creation support
  const handleValueChange = (selectedValue: string) => {
    console.log('SelectWithInlineCreation: handleValueChange called with:', selectedValue);

    if (selectedValue === "add-new") {
      console.log('SelectWithInlineCreation: Opening inline creation for:', entityType);

      // Open inline creation modal
      openInlineCreation(entityType, {
        onSuccess: (newEntity) => {
          console.log('SelectWithInlineCreation: Entity created successfully:', newEntity);

          // Auto-select the newly created entity
          onValueChange(newEntity.id);

          // Call custom success handler if provided
          if (onEntityCreated) {
            onEntityCreated(newEntity);
          }
        },
        refetchHook: refetchData,
      });
    } else {
      // Normal value change
      onValueChange(selectedValue);
    }
  };

  // Get entity display name for the "Add New" option
  const getAddNewLabel = () => {
    const entityNames: Record<EntityType, string> = {
      'pricing-group': 'Pricing Group',
      'class': 'Class',
      'facility': 'Facility',
      'class-level': 'Class Level',
      'class-category': 'Class Category',
      'class-subcategory': 'Class Subcategory',
      'location': 'Location',
      'equipment': 'Equipment',
      'membership-plan': 'Membership Plan',
      'waiver-form': 'Waiver Form',
    };
    
    return `Add New ${entityNames[entityType] || 'Item'}`;
  };

  return (
    <div className={className}>
      <Select
        value={value}
        onValueChange={handleValueChange}
        disabled={disabled}
      >
        <SelectTrigger className={triggerClassName}>
          <SelectValue placeholder={placeholder} />
        </SelectTrigger>
        <SelectContent className={contentClassName}>
        {/* Empty option (e.g., "No level set", "None selected") */}
        {emptyOption && (
          <SelectItem value={emptyOption.value}>
            {emptyOption.label}
          </SelectItem>
        )}
        
        {/* Regular options */}
        {options.map((option) => (
          <SelectItem 
            key={option.value} 
            value={option.value}
            disabled={option.disabled}
          >
            {option.label}
          </SelectItem>
        ))}
        
        {/* Separator and Add New option */}
        {options.length > 0 && (
          <div className="border-t border-border my-1" />
        )}
        
        <SelectItem 
          value="add-new" 
          className="text-primary font-medium"
        >
          <div className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            {getAddNewLabel()}
          </div>
        </SelectItem>
      </SelectContent>
    </Select>
    </div>
  );
}

/**
 * Utility function to convert data arrays to SelectOption format
 *
 * @param items - Array of objects with id and name properties
 * @param valueKey - Key to use for the value (default: 'id')
 * @param labelKey - Key to use for the label (default: 'name')
 * @returns Array of SelectOption objects
 */
export function mapToSelectOptions<T extends Record<string, any>>(
  items: T[] | undefined | null,
  valueKey: keyof T = 'id',
  labelKey: keyof T = 'name'
): SelectOption[] {
  if (!items || !Array.isArray(items)) {
    return [];
  }

  return items.map(item => ({
    value: String(item[valueKey]),
    label: String(item[labelKey]),
    disabled: item.disabled || false,
  }));
}

/**
 * Hook for managing select state with inline creation
 * 
 * @param initialValue - Initial selected value
 * @returns Object with value, onChange handler, and utility methods
 */
export function useSelectWithInlineCreation(initialValue?: string) {
  const [value, setValue] = React.useState<string | undefined>(initialValue);

  const handleValueChange = React.useCallback((newValue: string) => {
    setValue(newValue);
  }, []);

  const reset = React.useCallback(() => {
    setValue(undefined);
  }, []);

  const setToEmpty = React.useCallback((emptyValue: string = "none") => {
    setValue(emptyValue);
  }, []);

  return {
    value,
    onValueChange: handleValueChange,
    setValue,
    reset,
    setToEmpty,
  };
}
