# Public API Deployment & Operations Guide

## Overview

This guide provides comprehensive instructions for deploying and operating the Package Pricing Public API in production environments following FAANG-level operational excellence standards.

## Prerequisites

### System Requirements
- **Node.js**: 18.x or higher
- **PostgreSQL**: 14.x or higher
- **Redis**: 6.x or higher
- **Memory**: Minimum 2GB RAM (4GB+ recommended)
- **CPU**: 2+ cores recommended
- **Storage**: 20GB+ available space

### Environment Variables
```bash
# Database Configuration
DATABASE_URL="postgresql://user:password@host:port/database"
POSTGRES_HOST="127.0.0.1"
POSTGRES_PORT="5433"
POSTGRES_USER="citizix_user"
POSTGRES_PASSWORD="S3cret"
POSTGRES_DB="saas_app"

# Redis Configuration
REDIS_HOST="localhost"
REDIS_PORT="6379"
REDIS_PASSWORD=""

# API Configuration
API_VERSION="1.0.0"
NODE_ENV="production"
PORT="3000"

# Security
JWT_SECRET="your-super-secret-jwt-key"
API_KEY_SECRET="your-api-key-encryption-secret"

# Monitoring
DATADOG_API_KEY="your-datadog-api-key"
SENTRY_DSN="your-sentry-dsn"

# External Services
STRIPE_SECRET_KEY="your-stripe-secret-key"
SENDGRID_API_KEY="your-sendgrid-api-key"
```

## Database Setup

### 1. Database Migration
```bash
# Generate migration files
npm run db:generate

# Apply migrations
npm run db:migrate

# Verify migration
npm run db:studio
```

### 2. Database Optimization
```sql
-- Create indexes for public API tables
CREATE INDEX CONCURRENTLY idx_api_keys_tenant_id ON api_keys(tenant_id);
CREATE INDEX CONCURRENTLY idx_api_keys_key_hash ON api_keys(key_hash);
CREATE INDEX CONCURRENTLY idx_api_keys_active ON api_keys(is_active) WHERE is_active = true;

CREATE INDEX CONCURRENTLY idx_api_key_usage_api_key_id ON api_key_usage(api_key_id);
CREATE INDEX CONCURRENTLY idx_api_key_usage_created_at ON api_key_usage(created_at);

CREATE INDEX CONCURRENTLY idx_audit_logs_tenant_id ON audit_logs(tenant_id);
CREATE INDEX CONCURRENTLY idx_audit_logs_timestamp ON audit_logs(timestamp);
CREATE INDEX CONCURRENTLY idx_audit_logs_action ON audit_logs(action);

-- Optimize package pricing queries
CREATE INDEX CONCURRENTLY idx_package_pricing_tenant_lookup 
ON package_pricing(package_id, pricing_group_id) 
INCLUDE (price, credit_amount, currency);
```

### 3. Connection Pooling
```typescript
// Database connection pool configuration
const poolConfig = {
  max: 20,          // Maximum connections
  min: 5,           // Minimum connections
  idle: 10000,      // Idle timeout (10s)
  acquire: 60000,   // Acquire timeout (60s)
  evict: 1000,      // Eviction interval (1s)
};
```

## Redis Setup

### 1. Redis Configuration
```redis
# redis.conf optimizations
maxmemory 1gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
appendonly yes
appendfsync everysec
```

### 2. Redis Clustering (Production)
```bash
# Redis Cluster setup for high availability
redis-cli --cluster create \
  127.0.0.1:7000 127.0.0.1:7001 127.0.0.1:7002 \
  127.0.0.1:7003 127.0.0.1:7004 127.0.0.1:7005 \
  --cluster-replicas 1
```

## Application Deployment

### 1. Docker Deployment
```dockerfile
# Dockerfile
FROM node:18-alpine AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

FROM node:18-alpine AS runtime
WORKDIR /app
COPY --from=builder /app/node_modules ./node_modules
COPY . .
RUN npm run build

EXPOSE 3000
CMD ["npm", "start"]
```

```yaml
# docker-compose.yml
version: '3.8'
services:
  api:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_HOST=${REDIS_HOST}
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    
  postgres:
    image: postgres:14
    environment:
      POSTGRES_DB: ${POSTGRES_DB}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped
    
  redis:
    image: redis:6-alpine
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
```

### 2. Kubernetes Deployment
```yaml
# k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: public-api
spec:
  replicas: 3
  selector:
    matchLabels:
      app: public-api
  template:
    metadata:
      labels:
        app: public-api
    spec:
      containers:
      - name: api
        image: your-registry/public-api:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: api-secrets
              key: database-url
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /api/public/v1/health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/public/v1/health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
```

## Load Balancing & Auto-scaling

### 1. NGINX Configuration
```nginx
upstream api_backend {
    least_conn;
    server api1:3000 max_fails=3 fail_timeout=30s;
    server api2:3000 max_fails=3 fail_timeout=30s;
    server api3:3000 max_fails=3 fail_timeout=30s;
}

server {
    listen 80;
    server_name api.example.com;
    
    # Rate limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req zone=api burst=20 nodelay;
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    
    location /api/public/v1/ {
        proxy_pass http://api_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Timeouts
        proxy_connect_timeout 5s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # Buffer settings
        proxy_buffering on;
        proxy_buffer_size 4k;
        proxy_buffers 8 4k;
    }
    
    location /health {
        access_log off;
        proxy_pass http://api_backend/api/public/v1/health;
    }
}
```

### 2. Auto-scaling Configuration
```yaml
# hpa.yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: public-api-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: public-api
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
```

## Monitoring & Observability

### 1. Health Checks
```bash
# Basic health check
curl -f http://localhost:3000/api/public/v1/health || exit 1

# Detailed health monitoring
curl -s http://localhost:3000/api/public/v1/health | jq '.status'
```

### 2. Metrics Collection
```typescript
// Prometheus metrics endpoint
app.get('/metrics', async (req, res) => {
  const metrics = metricsService.exportPrometheus();
  res.set('Content-Type', 'text/plain');
  res.send(metrics);
});
```

### 3. Logging Configuration
```typescript
// Structured logging setup
const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' }),
    new winston.transports.Console({
      format: winston.format.simple()
    })
  ]
});
```

## Security Configuration

### 1. SSL/TLS Setup
```bash
# Let's Encrypt certificate
certbot --nginx -d api.example.com

# Or use custom certificates
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout /etc/ssl/private/api.key \
  -out /etc/ssl/certs/api.crt
```

### 2. Firewall Configuration
```bash
# UFW firewall rules
ufw allow 22/tcp    # SSH
ufw allow 80/tcp    # HTTP
ufw allow 443/tcp   # HTTPS
ufw allow from 10.0.0.0/8 to any port 5432  # PostgreSQL (internal)
ufw allow from 10.0.0.0/8 to any port 6379  # Redis (internal)
ufw enable
```

### 3. API Key Management
```bash
# Generate initial API keys for testing
npm run api-key:generate -- --name "Test Key" --tenant-id 1

# Rotate API keys
npm run api-key:rotate -- --key-id "key_123"

# Revoke compromised keys
npm run api-key:revoke -- --key-id "key_456"
```

## Backup & Disaster Recovery

### 1. Database Backups
```bash
#!/bin/bash
# backup-db.sh
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="backup_${DATE}.sql"

pg_dump $DATABASE_URL > $BACKUP_FILE
gzip $BACKUP_FILE

# Upload to S3
aws s3 cp ${BACKUP_FILE}.gz s3://your-backup-bucket/db-backups/

# Cleanup local files older than 7 days
find . -name "backup_*.sql.gz" -mtime +7 -delete
```

### 2. Redis Backups
```bash
#!/bin/bash
# backup-redis.sh
redis-cli --rdb /tmp/dump.rdb
gzip /tmp/dump.rdb
aws s3 cp /tmp/dump.rdb.gz s3://your-backup-bucket/redis-backups/
```

### 3. Disaster Recovery Plan
1. **RTO (Recovery Time Objective)**: 15 minutes
2. **RPO (Recovery Point Objective)**: 1 hour
3. **Backup Schedule**: Every 6 hours
4. **Cross-region replication**: Enabled
5. **Automated failover**: Configured

## Performance Optimization

### 1. Database Query Optimization
```sql
-- Analyze slow queries
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;

-- Optimize package pricing queries
EXPLAIN ANALYZE 
SELECT pp.*, p.name as package_name, pg.name as pricing_group_name
FROM package_pricing pp
JOIN packages p ON pp.package_id = p.id
JOIN pricing_groups pg ON pp.pricing_group_id = pg.id
WHERE p.tenant_id = $1;
```

### 2. Caching Strategy
```typescript
// Redis caching for frequently accessed data
const cacheKey = `package_pricing:${tenantId}:${filters}`;
const cached = await redis.get(cacheKey);

if (cached) {
  return JSON.parse(cached);
}

const data = await fetchFromDatabase();
await redis.setex(cacheKey, 300, JSON.stringify(data)); // 5 min TTL
return data;
```

### 3. Connection Pool Tuning
```typescript
// Optimize database connections
const poolConfig = {
  max: Math.min(20, parseInt(process.env.DB_POOL_MAX) || 10),
  min: Math.max(2, parseInt(process.env.DB_POOL_MIN) || 2),
  idle: 10000,
  acquire: 30000,
  evict: 1000,
};
```

## Troubleshooting

### Common Issues

#### 1. High Response Times
```bash
# Check database connections
SELECT count(*) FROM pg_stat_activity;

# Check Redis memory usage
redis-cli info memory

# Check application metrics
curl http://localhost:3000/metrics | grep response_time
```

#### 2. Rate Limit Issues
```bash
# Check rate limit status
redis-cli keys "rate_limit:*" | head -10

# Reset rate limits for debugging
redis-cli del "rate_limit:api_key_123:minute"
```

#### 3. Authentication Failures
```bash
# Check API key status
psql -c "SELECT name, is_active, last_used_at FROM api_keys WHERE id = 'key_123';"

# Check audit logs
psql -c "SELECT * FROM audit_logs WHERE action LIKE '%auth%' ORDER BY timestamp DESC LIMIT 10;"
```

## Maintenance Procedures

### 1. Rolling Updates
```bash
# Zero-downtime deployment
kubectl set image deployment/public-api api=your-registry/public-api:v2.0.0
kubectl rollout status deployment/public-api
```

### 2. Database Maintenance
```bash
# Vacuum and analyze
psql -c "VACUUM ANALYZE;"

# Reindex
psql -c "REINDEX DATABASE saas_app;"

# Update statistics
psql -c "ANALYZE;"
```

### 3. Log Rotation
```bash
# Logrotate configuration
/var/log/api/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 api api
    postrotate
        systemctl reload api
    endscript
}
```

This deployment guide provides a comprehensive foundation for operating the Public API in production environments with enterprise-grade reliability, security, and performance.
