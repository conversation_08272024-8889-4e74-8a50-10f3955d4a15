import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { z } from "zod";
import { FacilityService } from "@/lib/services/facility.service";

// Validation schema for update
const updateFacilitySchema = z.object({
  name: z.string().min(1, "Name is required").max(255, "Name too long").optional(),
  description: z.string().max(500, "Description too long").optional(),
  isActive: z.boolean().optional(),
  images: z.array(z.string()).optional(), // Allow any string, not just URLs
});

// GET /api/facilities/[id] - Get single facility
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id: facilityId } = await params;
    if (!facilityId) {
      return NextResponse.json(
        { error: "Facility ID is required" },
        { status: 400 }
      );
    }

    const facility = await FacilityService.getById(facilityId);

    if (!facility) {
      return NextResponse.json(
        { error: "Facility not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({ facility }, { status: 200 });
  } catch (error) {
    console.error("Error fetching facility:", error);
    return NextResponse.json(
      { error: "Failed to fetch facility" },
      { status: 500 }
    );
  }
}

// PUT /api/facilities/[id] - Update facility
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id: facilityId } = await params;
    if (!facilityId) {
      return NextResponse.json(
        { error: "Facility ID is required" },
        { status: 400 }
      );
    }

    console.log('PUT /api/facilities/[id] - facilityId:', facilityId);
    console.log('PUT /api/facilities/[id] - request method:', request.method);
    console.log('PUT /api/facilities/[id] - content-type:', request.headers.get('content-type'));

    let body;
    try {
      const rawBody = await request.text();
      console.log('PUT /api/facilities/[id] - raw body:', rawBody);

      if (!rawBody.trim()) {
        return NextResponse.json(
          { error: "Empty request body" },
          { status: 400 }
        );
      }

      body = JSON.parse(rawBody);
      console.log('PUT /api/facilities/[id] - parsed body:', body);
    } catch (error) {
      console.log('PUT /api/facilities/[id] - JSON parse error:', error);
      return NextResponse.json(
        { error: "Invalid JSON in request body" },
        { status: 400 }
      );
    }

    const validatedData = updateFacilitySchema.parse(body);

    // Validate facility data
    const validationErrors = FacilityService.validateFacilityData(validatedData);
    if (validationErrors.length > 0) {
      return NextResponse.json(
        { error: "Validation error", details: validationErrors },
        { status: 400 }
      );
    }

    const updatedFacility = await FacilityService.update(facilityId, validatedData);

    if (!updatedFacility) {
      return NextResponse.json(
        { error: "Facility not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { 
        message: "Facility updated successfully", 
        facility: updatedFacility 
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error updating facility:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid data", details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to update facility" },
      { status: 500 }
    );
  }
}

// DELETE /api/facilities/[id] - Delete facility
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id: facilityId } = await params;
    if (!facilityId) {
      return NextResponse.json(
        { error: "Facility ID is required" },
        { status: 400 }
      );
    }

    const deletedFacility = await FacilityService.delete(facilityId);

    return NextResponse.json(
      {
        message: "Facility deleted successfully",
        facility: deletedFacility
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Error deleting facility:", error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to delete facility" },
      { status: 500 }
    );
  }
}

// PATCH /api/facilities/[id] - Toggle facility active status
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id: facilityId } = await params;
    if (!facilityId) {
      return NextResponse.json(
        { error: "Facility ID is required" },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { action } = body;

    if (action === "toggle-status") {
      const updatedFacility = await FacilityService.toggleActiveStatus(facilityId);
      
      if (!updatedFacility) {
        return NextResponse.json(
          { error: "Facility not found" },
          { status: 404 }
        );
      }

      return NextResponse.json(
        {
          message: `Facility ${updatedFacility.isActive ? 'activated' : 'deactivated'} successfully`,
          facility: updatedFacility
        },
        { status: 200 }
      );
    }

    return NextResponse.json(
      { error: "Invalid action" },
      { status: 400 }
    );
  } catch (error) {
    console.error("Error in facility action:", error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to perform action" },
      { status: 500 }
    );
  }
}
