import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { UserLocationAccess } from '@/lib/db/schema';

/**
 * Location Access Queries Hooks
 * 
 * Hooks untuk manage state dan data fetching location access menggunakan TanStack Query.
 * Pattern ini mengikuti use-role-queries.ts dan use-permission-queries.ts.
 */

// Query Keys
export const locationAccessKeys = {
  all: ['location-access'] as const,
  lists: () => [...locationAccessKeys.all, 'list'] as const,
  list: (filters: Record<string, any>) => [...locationAccessKeys.lists(), { filters }] as const,
  byTenant: (tenantId: number) => [...locationAccessKeys.all, 'tenant', tenantId] as const,
  byUser: (userId: string, tenantId: number) => [...locationAccessKeys.all, 'user', userId, tenantId] as const,
  byLocation: (locationId: string, tenantId: number) => [...locationAccessKeys.all, 'location', locationId, tenantId] as const,
  search: (params: any) => [...locationAccessKeys.all, 'search', params] as const,
  userAccessible: (userId: string, tenantId: number) => [...locationAccessKeys.all, 'user-accessible', userId, tenantId] as const,
  check: (userId: string, locationId: string, level: string) => [...locationAccessKeys.all, 'check', userId, locationId, level] as const,
};

// API Functions
const locationAccessApi = {
  search: async (
    tenantId: number,
    userId?: string,
    locationId?: string,
    limit = 20,
    offset = 0
  ) => {
    const params = new URLSearchParams({
      tenantId: tenantId.toString(),
      limit: limit.toString(),
      offset: offset.toString(),
    });
    
    if (userId) {
      params.append('userId', userId);
    }
    if (locationId) {
      params.append('locationId', locationId);
    }

    const response = await fetch(`/api/location-access?${params}`);
    if (!response.ok) {
      throw new Error(`Failed to search location access: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data;
  },

  grantAccess: async (data: {
    userId: string;
    locationId: string;
    tenantId: number;
    access_level: "full" | "read_only" | "restricted";
    assignedBy?: string;
  }): Promise<UserLocationAccess> => {
    const response = await fetch('/api/location-access/grant', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      throw new Error(`Failed to grant location access: ${response.statusText}`);
    }
    const result = await response.json();
    return result.data;
  },

  revokeAccess: async (data: {
    userId: string;
    locationId: string;
  }): Promise<void> => {
    const response = await fetch('/api/location-access/revoke', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      throw new Error(`Failed to revoke location access: ${response.statusText}`);
    }
  },

  getUserLocationAccess: async (userId: string, tenantId: number): Promise<UserLocationAccess[]> => {
    const params = new URLSearchParams({
      userId,
      tenantId: tenantId.toString(),
    });

    const response = await fetch(`/api/location-access/user?${params}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch user location access: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data;
  },

  getUserAccessibleLocations: async (userId: string, tenantId: number): Promise<{
    locationId: string;
    locationName: string;
    access_level: string;
  }[]> => {
    const params = new URLSearchParams({
      userId,
      tenantId: tenantId.toString(),
    });

    const response = await fetch(`/api/location-access/user-accessible?${params}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch user accessible locations: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data;
  },

  checkUserLocationAccess: async (
    userId: string,
    locationId: string,
    requiredLevel: "full" | "read_only" | "restricted" = "read_only"
  ): Promise<boolean> => {
    const params = new URLSearchParams({
      userId,
      locationId,
      requiredLevel,
    });

    const response = await fetch(`/api/location-access/check?${params}`);
    if (!response.ok) {
      throw new Error(`Failed to check user location access: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data.hasAccess;
  },

  bulkGrantAccess: async (data: {
    userId: string;
    locationIds: string[];
    tenantId: number;
    access_level: "full" | "read_only" | "restricted";
    assignedBy?: string;
  }): Promise<UserLocationAccess[]> => {
    const response = await fetch('/api/location-access/bulk-grant', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      throw new Error(`Failed to bulk grant location access: ${response.statusText}`);
    }
    const result = await response.json();
    return result.data;
  },

  bulkRevokeAccess: async (data: {
    userId: string;
    locationIds: string[];
  }): Promise<void> => {
    const response = await fetch('/api/location-access/bulk-revoke', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      throw new Error(`Failed to bulk revoke location access: ${response.statusText}`);
    }
  },
};

// Query Hooks
export function useLocationAccessSearch(
  tenantId: number,
  userId?: string,
  locationId?: string,
  limit = 20,
  offset = 0
) {
  return useQuery({
    queryKey: locationAccessKeys.search({ tenantId, userId, locationId, limit, offset }),
    queryFn: () => locationAccessApi.search(tenantId, userId, locationId, limit, offset),
    enabled: !!tenantId,
    staleTime: 2 * 60 * 1000,
  });
}

export function useUserLocationAccess(userId: string, tenantId: number) {
  return useQuery({
    queryKey: locationAccessKeys.byUser(userId, tenantId),
    queryFn: () => locationAccessApi.getUserLocationAccess(userId, tenantId),
    enabled: !!userId && !!tenantId,
    staleTime: 5 * 60 * 1000,
  });
}

export function useUserAccessibleLocations(userId: string, tenantId: number) {
  return useQuery({
    queryKey: locationAccessKeys.userAccessible(userId, tenantId),
    queryFn: () => locationAccessApi.getUserAccessibleLocations(userId, tenantId),
    enabled: !!userId && !!tenantId,
    staleTime: 5 * 60 * 1000,
  });
}

export function useCheckUserLocationAccess(
  userId: string,
  locationId: string,
  requiredLevel: "full" | "read_only" | "restricted" = "read_only"
) {
  return useQuery({
    queryKey: locationAccessKeys.check(userId, locationId, requiredLevel),
    queryFn: () => locationAccessApi.checkUserLocationAccess(userId, locationId, requiredLevel),
    enabled: !!userId && !!locationId,
    staleTime: 2 * 60 * 1000,
  });
}

// Mutation Hooks
export function useGrantLocationAccess() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: locationAccessApi.grantAccess,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: locationAccessKeys.all });
    },
  });
}

export function useRevokeLocationAccess() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: locationAccessApi.revokeAccess,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: locationAccessKeys.all });
    },
  });
}

export function useBulkGrantLocationAccess() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: locationAccessApi.bulkGrantAccess,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: locationAccessKeys.all });
    },
  });
}

export function useBulkRevokeLocationAccess() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: locationAccessApi.bulkRevokeAccess,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: locationAccessKeys.all });
    },
  });
}

// Export types
export interface LocationAccessSearchParams {
  tenantId: number;
  userId?: string;
  locationId?: string;
  limit?: number;
  offset?: number;
}

export interface LocationAccessGrantData {
  userId: string;
  locationId: string;
  tenantId: number;
  access_level: "full" | "read_only" | "restricted";
  assignedBy?: string;
}

export interface LocationAccessRevokeData {
  userId: string;
  locationId: string;
}

export interface BulkLocationAccessGrantData {
  userId: string;
  locationIds: string[];
  tenantId: number;
  access_level: "full" | "read_only" | "restricted";
  assignedBy?: string;
}

export interface BulkLocationAccessRevokeData {
  userId: string;
  locationIds: string[];
}
