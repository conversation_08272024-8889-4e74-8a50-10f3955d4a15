import { business_profiles } from '@/lib/db/schema';
import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { z } from "zod";
import { LocationService } from "@/lib/services/locations.service";

// Validation schema
const createLocationsSchema = z.object({
  tenantId: z.number().int().positive(),
  name: z.string().max(255).optional(),
  addressLine1: z.string().max(255).optional(),
  addressLine2: z.string().max(255).optional(),
  city: z.string().max(255).optional(),
  state: z.string().max(255).optional(),
  country: z.string().max(255).optional(),
  postalCode: z.string().max(255).optional(),
});

const updateLocationsSchema = createLocationsSchema.partial().omit({ tenantId: true });

// GET /api/locations - Get locations
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get("tenantId");
    const country = searchParams.get("country");
    const city = searchParams.get("city");
    const name = searchParams.get("name");
    const postalCode = searchParams.get("postalCode");

    let locations;

    // If tenantId is provided, validate and filter by tenant

    if (tenantId && tenantId !== "null" && tenantId !== "undefined" && !tenantId.includes("object")) {
      const tenantIdNum = parseInt(tenantId);
      if (isNaN(tenantIdNum) || tenantIdNum <= 0) {
        return NextResponse.json(
          { error: "tenantId must be a valid positive number" },
          { status: 400 }
        );
      }

      if (country) {
        locations = await LocationService.getLocationsByCountry(country);
      } else if (name) {
        locations = await LocationService.getLocationsByName(name);
      } else if (city) {
        locations = await LocationService.getLocationsByPostalCode(city);
      } else {
        locations = await LocationService.getByTenantId(tenantIdNum);
      }
    } else {
      // No tenantId provided, return all locations
      locations = await LocationService.getAll();
    }

    return NextResponse.json({ locations });
  } catch (error) {
    console.error("Error fetching locations:", error);
    return NextResponse.json(
      { error: "Failed to fetch locations" },
      { status: 500 }
    );
  }
}

// POST /api/location - Create location
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = createLocationsSchema.parse(body);

    // Check if user has permission to create location for this tenant
    if (session.user.role !== "admin") {
      // Add tenant ownership check here
    }

    const location = await LocationService.create(validatedData);

    return NextResponse.json({ location }, { status: 201 });
  } catch (error) {
    console.error("Error creating location:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to create location" },
      { status: 500 }
    );
  }
}

// POST /api/locaations/bulk - Bulk create addresses
export async function POST_BULK(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { locations: locationsData } = body;

    if (!Array.isArray(locationsData) || locationsData.length === 0) {
      return NextResponse.json(
        { error: "locations array is required and cannot be empty" },
        { status: 400 }
      );
    }

    // Validate each location
    const validateLocations = locationsData.map(addr => 
      createLocationsSchema.parse(addr)
    );

    // Check permissions for each tenant
    if (session.user.role !== "admin") {
      // Add tenant ownership checks here
    }

    const locations = await LocationService.bulkCreate(validateLocations);

    return NextResponse.json({ locations }, { status: 201 });
  } catch (error) {
    console.error("Error bulk creating locations:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to bulk create locations" },
      { status: 500 }
    );
  }
}
