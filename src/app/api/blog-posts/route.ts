import { NextRequest, NextResponse } from "next/server";
import { BlogService } from "@/lib/services/blog.service";
import { createBlogPostSchema, updateBlogPostSchema, blogPostFiltersSchema } from "@/lib/validations/blog";
import { withRBAC } from "@/lib/middleware/rbac-middleware";
import { z } from "zod";

// GET /api/blog-posts - List blog posts with filters
export async function GET(request: NextRequest) {
  return withRBAC(
    async () => {
      try {
        const { searchParams } = new URL(request.url);
        
        // Parse and validate query parameters
        const rawFilters = {
          tenantId: searchParams.get("tenantId"),
          search: searchParams.get("search"),
          category_id: searchParams.get("category_id"),
          status: searchParams.get("status"),
          is_featured: searchParams.get("is_featured"),
          author_id: searchParams.get("author_id"),
          tags: searchParams.get("tags")?.split(',').filter(Boolean),
          limit: searchParams.get("limit"),
          offset: searchParams.get("offset"),
        };

        if (!rawFilters.tenantId) {
          return NextResponse.json(
            { error: "Tenant ID is required" },
            { status: 400 }
          );
        }

        const filters = {
          tenantId: parseInt(rawFilters.tenantId),
          ...(rawFilters.search && { search: rawFilters.search }),
          ...(rawFilters.category_id && { category_id: rawFilters.category_id }),
          ...(rawFilters.status && { status: rawFilters.status }),
          ...(rawFilters.is_featured !== null && { is_featured: rawFilters.is_featured === "true" }),
          ...(rawFilters.author_id && { author_id: rawFilters.author_id }),
          ...(rawFilters.tags && { tags: rawFilters.tags }),
          ...(rawFilters.limit && { limit: parseInt(rawFilters.limit) }),
          ...(rawFilters.offset && { offset: parseInt(rawFilters.offset) }),
        };

        const blogPosts = await BlogService.getByTenantWithFilters(
          filters.tenantId,
          {
            search: filters.search,
            category_id: filters.category_id,
            status: filters.status as "draft" | "published",
            is_featured: filters.is_featured,
            author_id: filters.author_id,
            tags: filters.tags,
          },
          {
            limit: filters.limit,
            offset: filters.offset,
          }
        );

        return NextResponse.json({
          success: true,
          data: blogPosts,
          count: blogPosts.length,
        });
      } catch (error) {
        console.error("Error fetching blog posts:", error);
        return NextResponse.json(
          { 
            error: error instanceof Error ? error.message : "Failed to fetch blog posts",
            success: false 
          },
          { status: 500 }
        );
      }
    },
    { module: "blog", action: "read" }
  )(request);
}

// POST /api/blog-posts - Create new blog post
export async function POST(request: NextRequest) {
  return withRBAC(
    async () => {
      try {
        const body = await request.json();
        
        // Validate request body
        const validatedData = createBlogPostSchema.parse(body);

        // Generate slug if not provided
        if (!validatedData.slug) {
          validatedData.slug = BlogService.generateSlug(validatedData.title);
        }

        // Check slug availability
        const isSlugAvailable = await BlogService.isSlugAvailable(
          validatedData.tenantId,
          validatedData.slug
        );

        if (!isSlugAvailable) {
          // Append timestamp to make unique
          validatedData.slug = `${validatedData.slug}-${Date.now()}`;
        }

        // Create blog post
        const blogPost = await BlogService.create(validatedData);

        // Fetch the created post with relations
        const createdPost = await BlogService.getById(blogPost.id);

        return NextResponse.json({
          success: true,
          data: createdPost,
          message: "Blog post created successfully",
        }, { status: 201 });
      } catch (error) {
        console.error("Error creating blog post:", error);
        
        if (error instanceof z.ZodError) {
          return NextResponse.json(
            { 
              error: "Validation failed",
              details: error.errors,
              success: false 
            },
            { status: 400 }
          );
        }

        return NextResponse.json(
          { 
            error: error instanceof Error ? error.message : "Failed to create blog post",
            success: false 
          },
          { status: 500 }
        );
      }
    },
    { module: "blog", action: "create" }
  )(request);
}

// PUT /api/blog-posts/bulk - Bulk operations
export async function PUT(request: NextRequest) {
  return withRBAC(
    async () => {
      try {
        const body = await request.json();
        const { operation, ids, data } = body;

        if (!operation || !ids || !Array.isArray(ids)) {
          return NextResponse.json(
            { error: "Invalid bulk operation request" },
            { status: 400 }
          );
        }

        let results = [];

        switch (operation) {
          case "delete":
            for (const id of ids) {
              await BlogService.delete(id);
            }
            results = ids.map((id: string) => ({ id, status: "deleted" }));
            break;

          case "publish":
            for (const id of ids) {
              const updated = await BlogService.update(id, { 
                status: "published",
                published_at: new Date()
              });
              results.push(updated);
            }
            break;

          case "unpublish":
            for (const id of ids) {
              const updated = await BlogService.update(id, {
                status: "draft"
              });
              results.push(updated);
            }
            break;

          case "feature":
            for (const id of ids) {
              const updated = await BlogService.update(id, { is_featured: true });
              results.push(updated);
            }
            break;

          case "unfeature":
            for (const id of ids) {
              const updated = await BlogService.update(id, { is_featured: false });
              results.push(updated);
            }
            break;

          case "update":
            if (!data) {
              return NextResponse.json(
                { error: "Update data is required for bulk update" },
                { status: 400 }
              );
            }
            
            const validatedUpdateData = updateBlogPostSchema.parse(data);
            
            for (const id of ids) {
              const updated = await BlogService.update(id, validatedUpdateData);
              results.push(updated);
            }
            break;

          default:
            return NextResponse.json(
              { error: `Unknown operation: ${operation}` },
              { status: 400 }
            );
        }

        return NextResponse.json({
          success: true,
          data: results,
          message: `Bulk ${operation} completed successfully`,
        });
      } catch (error) {
        console.error("Error in bulk operation:", error);
        
        if (error instanceof z.ZodError) {
          return NextResponse.json(
            { 
              error: "Validation failed",
              details: error.errors,
              success: false 
            },
            { status: 400 }
          );
        }

        return NextResponse.json(
          { 
            error: error instanceof Error ? error.message : "Failed to perform bulk operation",
            success: false 
          },
          { status: 500 }
        );
      }
    },
    { module: "blog", action: "update" }
  )(request);
}
