/**
 * JavaScript SDK for Package Pricing Public API
 * 
 * Enterprise-grade SDK with:
 * - TypeScript support
 * - Automatic retries with exponential backoff
 * - Rate limit handling
 * - Request/response interceptors
 * - Comprehensive error handling
 */

import { 
  PublicPackagePricing, 
  PackagePricingCreateRequest, 
  PackagePricingUpdateRequest,
  PackagePricingBulkCreateRequest,
  PackagePricingFilters,
  APIResponse,
  SDKConfig,
  SDKResponse 
} from '../types';

interface RetryConfig {
  maxRetries: number;
  backoffMultiplier: number;
  maxBackoffSeconds: number;
}

interface RateLimitConfig {
  enableBackoff: boolean;
  maxBackoffSeconds: number;
}

export class PackagePricingSDK {
  private config: Required<SDKConfig>;
  private baseHeaders: Record<string, string>;

  constructor(config: SDKConfig) {
    this.config = {
      apiKey: config.apiKey,
      baseUrl: config.baseUrl || 'https://api.example.com/public/v1',
      timeout: config.timeout || 30000,
      retryConfig: {
        maxRetries: 3,
        backoffMultiplier: 2,
        maxBackoffSeconds: 30,
        ...config.retryConfig,
      },
      rateLimitConfig: {
        enableBackoff: true,
        maxBackoffSeconds: 60,
        ...config.rateLimitConfig,
      },
    };

    this.baseHeaders = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${this.config.apiKey}`,
      'User-Agent': 'PackagePricingSDK/1.0.0',
      'X-API-Version': 'v1',
    };
  }

  /**
   * List package pricing with filters and pagination
   */
  async list(filters?: PackagePricingFilters & {
    page?: number;
    limit?: number;
    sort?: string;
    order?: 'asc' | 'desc';
  }): Promise<SDKResponse<PublicPackagePricing[]>> {
    const params = new URLSearchParams();
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params.append(key, value.toString());
        }
      });
    }

    const url = `${this.config.baseUrl}/package-pricing${params.toString() ? `?${params.toString()}` : ''}`;
    
    return this.request<PublicPackagePricing[]>('GET', url);
  }

  /**
   * Get package pricing by ID
   */
  async get(id: string): Promise<SDKResponse<PublicPackagePricing>> {
    const url = `${this.config.baseUrl}/package-pricing/${encodeURIComponent(id)}`;
    return this.request<PublicPackagePricing>('GET', url);
  }

  /**
   * Create new package pricing
   */
  async create(data: PackagePricingCreateRequest): Promise<SDKResponse<PublicPackagePricing>> {
    const url = `${this.config.baseUrl}/package-pricing`;
    return this.request<PublicPackagePricing>('POST', url, data);
  }

  /**
   * Create multiple package pricing records
   */
  async bulkCreate(data: PackagePricingBulkCreateRequest): Promise<SDKResponse<{
    items: PublicPackagePricing[];
    created: number;
    message: string;
  }>> {
    const url = `${this.config.baseUrl}/package-pricing`;
    return this.request('POST', url, data);
  }

  /**
   * Update package pricing
   */
  async update(id: string, data: PackagePricingUpdateRequest): Promise<SDKResponse<PublicPackagePricing>> {
    const url = `${this.config.baseUrl}/package-pricing/${encodeURIComponent(id)}`;
    return this.request<PublicPackagePricing>('PUT', url, data);
  }

  /**
   * Delete package pricing
   */
  async delete(id: string): Promise<SDKResponse<{
    id: string;
    message: string;
    deletedAt: string;
  }>> {
    const url = `${this.config.baseUrl}/package-pricing/${encodeURIComponent(id)}`;
    return this.request('DELETE', url);
  }

  /**
   * Check API health
   */
  async health(): Promise<SDKResponse<any>> {
    const url = `${this.config.baseUrl}/health`;
    return this.request('GET', url);
  }

  /**
   * Make HTTP request with retry logic and error handling
   */
  private async request<T>(
    method: string,
    url: string,
    body?: any
  ): Promise<SDKResponse<T>> {
    let lastError: Error | null = null;
    
    for (let attempt = 0; attempt <= this.config.retryConfig.maxRetries; attempt++) {
      try {
        const response = await this.makeRequest(method, url, body);
        
        // Handle rate limiting
        if (response.status === 429) {
          if (this.config.rateLimitConfig.enableBackoff && attempt < this.config.retryConfig.maxRetries) {
            const retryAfter = this.getRetryAfter(response);
            await this.sleep(Math.min(retryAfter * 1000, this.config.rateLimitConfig.maxBackoffSeconds * 1000));
            continue;
          }
        }

        // Parse response
        const apiResponse: APIResponse<T> = await response.json();
        
        return {
          ...apiResponse,
          raw: response,
        };

      } catch (error) {
        lastError = error instanceof Error ? error : new Error('Unknown error');
        
        // Don't retry on client errors (4xx)
        if (error instanceof Error && error.message.includes('4')) {
          break;
        }
        
        // Calculate backoff delay
        if (attempt < this.config.retryConfig.maxRetries) {
          const delay = Math.min(
            Math.pow(this.config.retryConfig.backoffMultiplier, attempt) * 1000,
            this.config.retryConfig.maxBackoffSeconds * 1000
          );
          await this.sleep(delay);
        }
      }
    }

    throw lastError || new Error('Request failed after all retries');
  }

  /**
   * Make actual HTTP request
   */
  private async makeRequest(method: string, url: string, body?: any): Promise<Response> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

    try {
      const response = await fetch(url, {
        method,
        headers: this.baseHeaders,
        body: body ? JSON.stringify(body) : undefined,
        signal: controller.signal,
      });

      clearTimeout(timeoutId);
      return response;

    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('Request timeout');
      }
      
      throw error;
    }
  }

  /**
   * Get retry delay from response headers
   */
  private getRetryAfter(response: Response): number {
    const retryAfter = response.headers.get('Retry-After');
    if (retryAfter) {
      const seconds = parseInt(retryAfter, 10);
      if (!isNaN(seconds)) {
        return seconds;
      }
    }
    return 60; // Default 1 minute
  }

  /**
   * Sleep for specified milliseconds
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Update API key
   */
  updateApiKey(apiKey: string): void {
    this.config.apiKey = apiKey;
    this.baseHeaders['Authorization'] = `Bearer ${apiKey}`;
  }

  /**
   * Update base URL
   */
  updateBaseUrl(baseUrl: string): void {
    this.config.baseUrl = baseUrl;
  }

  /**
   * Get current configuration
   */
  getConfig(): Readonly<Required<SDKConfig>> {
    return { ...this.config };
  }
}

/**
 * SDK Error Classes
 */
export class SDKError extends Error {
  constructor(
    message: string,
    public readonly code: string,
    public readonly statusCode?: number,
    public readonly response?: Response
  ) {
    super(message);
    this.name = 'SDKError';
  }
}

export class ValidationError extends SDKError {
  constructor(message: string, public readonly errors: any[]) {
    super(message, 'VALIDATION_ERROR', 400);
    this.name = 'ValidationError';
  }
}

export class AuthenticationError extends SDKError {
  constructor(message: string = 'Authentication failed') {
    super(message, 'AUTHENTICATION_ERROR', 401);
    this.name = 'AuthenticationError';
  }
}

export class RateLimitError extends SDKError {
  constructor(
    message: string = 'Rate limit exceeded',
    public readonly retryAfter: number
  ) {
    super(message, 'RATE_LIMIT_ERROR', 429);
    this.name = 'RateLimitError';
  }
}

export class NotFoundError extends SDKError {
  constructor(message: string = 'Resource not found') {
    super(message, 'NOT_FOUND_ERROR', 404);
    this.name = 'NotFoundError';
  }
}

/**
 * Factory function to create SDK instance
 */
export function createPackagePricingSDK(config: SDKConfig): PackagePricingSDK {
  return new PackagePricingSDK(config);
}

/**
 * Default export
 */
export default PackagePricingSDK;
