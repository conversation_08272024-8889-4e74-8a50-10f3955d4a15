"use client";

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { createEntityHooks } from "@/lib/core/base-query-hooks";
import type { ClassScheduleWithRelations } from "@/lib/services/class-schedule.service";
import type { ClassSchedule } from "@/lib/db/schema";

/**
 * TanStack Query Hooks untuk Class Schedules
 * 
 * File ini berisi semua hooks yang diperlukan untuk manage class schedules.
 * Menggunakan createEntityHooks factory untuk consistency dan reusability.
 * 
 * Kenapa pakai pattern ini?
 * - Konsisten dengan hooks lain yang sudah ada
 * - Automatic caching dan invalidation
 * - Optimistic updates untuk better UX
 * - Error handling yang proper
 * - Type safety dengan TypeScript
 */

// Query Keys - ini penting untuk caching strategy
export const classScheduleKeys = {
  all: ['class-schedules'] as const,
  lists: () => [...classScheduleKeys.all, 'list'] as const,
  list: (filters: Record<string, any>) => [...classScheduleKeys.lists(), { filters }] as const,
  details: () => [...classScheduleKeys.all, 'detail'] as const,
  detail: (id: string) => [...classScheduleKeys.details(), id] as const,
  byTenant: (tenantId: number) => [...classScheduleKeys.all, 'tenant', tenantId] as const,
  byClass: (classId: string) => [...classScheduleKeys.all, 'class', classId] as const,
  byLocation: (locationId: string) => [...classScheduleKeys.all, 'location', locationId] as const,
  byInstructor: (staffId: string) => [...classScheduleKeys.all, 'instructor', staffId] as const,
  byDateRange: (startDate: string, endDate: string) => [...classScheduleKeys.all, 'dateRange', startDate, endDate] as const,
  search: (params: any) => [...classScheduleKeys.all, 'search', params] as const,
};

// Type definitions untuk API calls
export interface ClassScheduleFormData {
  tenant_id: number;
  class_id: string;
  name?: string;
  description?: string;
  location_id?: string;
  facility_id?: string;
  staff_id?: string;
  start_date?: string;
  end_date?: string;
  start_time?: string;
  end_time?: string;
  duration: number;
  calender_color?: string;
  repeat_rule?: "none" | "daily" | "weekly" | "monthly";
  pax?: number;
  waitlist?: number;
  allow_classpass?: boolean;
  is_private?: boolean;
  publish_now?: boolean;
  publish_at?: string;
  auto_cancel_if_minimum_not_met?: boolean;
  booking_window_start?: string;
  booking_window_end?: string;
  check_in_window_start?: string;
  check_in_window_end?: string;
  late_cancellation_rule?: string;
}

export interface ClassScheduleUpdateData extends Partial<Omit<ClassScheduleFormData, 'tenant_id'>> {}

export interface ClassScheduleSearchResult {
  schedules: ClassSchedule[];
  total: number;
  hasMore: boolean;
}

/**
 * API Endpoints Configuration
 *
 * Mengapa pisahkan endpoint public dan private?
 * - Public endpoint: Untuk operasi READ-ONLY dengan API key authentication
 * - Private endpoint: Untuk operasi CRUD dengan session authentication
 * - Memisahkan concerns antara public access dan admin operations
 */
const publicScheduleUrl = '/api/public/v1/class-schedules'; // For GET operations (API key auth)
const privateScheduleUrl = '/api/class-schedules'; // For POST/PUT/DELETE operations (session auth)

// API Functions - ini yang handle komunikasi dengan backend
const classScheduleApi = {
  getAll: async (): Promise<ClassSchedule[]> => {
    const response = await fetch(publicScheduleUrl,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': process.env.NEXT_PUBLIC_CLASSES_API_KEY!
        }
      }
    );
    if (!response.ok) {
      throw new Error(`Failed to fetch class schedules: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data?.schedules || [];
  },

  getByTenant: async (tenantId: number): Promise<ClassSchedule[]> => {
    const params = new URLSearchParams({
      tenantId: tenantId.toString(),
    });
    const response = await fetch(`${publicScheduleUrl}?${params}`,
      {
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': process.env.NEXT_PUBLIC_CLASSES_API_KEY!
        }
      }
    );
    if (!response.ok) {
      throw new Error(`Failed to fetch class schedules: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data?.schedules || [];
  },

  getById: async (id: string): Promise<ClassScheduleWithRelations> => {
    // Use private endpoint for getById since it might need authentication
    const response = await fetch(`${privateScheduleUrl}/${id}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      }
    );
    if (!response.ok) {
      throw new Error(`Failed to fetch class schedule: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data;
  },

  search: async (query: string, options?: any): Promise<ClassSchedule[]> => {
    const params = new URLSearchParams({
      searchTerm: query,
    });

    // Add options if provided
    if (options?.tenantId) params.append('tenantId', options.tenantId.toString());
    if (options?.classId) params.append('classId', options.classId);
    if (options?.name) params.append('name', options.name);
    if (options?.description) params.append('description', options.description);
    if (options?.locationId) params.append('locationId', options.locationId);
    if (options?.facilityId) params.append('facilityId', options.facilityId);
    if (options?.staffId) params.append('staffId', options.staffId);
    if (options?.startDate) params.append('startDate', options.startDate);
    if (options?.endDate) params.append('endDate', options.endDate);
    if (options?.limit) params.append('limit', options.limit.toString());
    if (options?.offset) params.append('offset', options.offset.toString());

    const response = await fetch(`${publicScheduleUrl}?${params}`, {
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': process.env.NEXT_PUBLIC_CLASSES_API_KEY!
      }
    });
    if (!response.ok) {
      throw new Error(`Failed to search class schedules: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data?.schedules || [];
  },

  // Keep the advanced search as a separate method
  searchAdvanced: async (
    tenantId: number,
    classId?: string,
    name?: string,
    description?: string,
    locationId?: string,
    facilityId?: string,
    staffId?: string,
    startDate?: string,
    endDate?: string,
    searchTerm?: string,
    limit = 20,
    offset = 0
  ): Promise<ClassScheduleSearchResult> => {
    const params = new URLSearchParams({
      tenantId: tenantId.toString(),
      limit: limit.toString(),
      offset: offset.toString(),
    });

    if (classId) params.append('classId', classId);
    if (name) params.append('name', name);
    if (description) params.append('description', description);
    if (locationId) params.append('locationId', locationId);
    if (facilityId) params.append('facilityId', facilityId);
    if (staffId) params.append('staffId', staffId);
    if (startDate) params.append('startDate', startDate);
    if (endDate) params.append('endDate', endDate);
    if (searchTerm) params.append('search', searchTerm);

    const response = await fetch(`${publicScheduleUrl}?${params}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': process.env.NEXT_PUBLIC_CLASSES_API_KEY!
        }
      }
    );
    if (!response.ok) {
      throw new Error(`Failed to search class schedules: ${response.statusText}`);
    }
    const data = await response.json();

    // Handle both success response format and direct data format
    if (data.success && data.data) {
      return data.data;
    } else if (data.schedules) {
      // Direct format from service
      return data;
    } else {
      // Fallback to ensure we always return valid structure
      return {
        schedules: [],
        total: 0,
        hasMore: false,
      };
    }
  },

  create: async (data: ClassScheduleFormData): Promise<ClassSchedule> => {
    const response = await fetch(`${privateScheduleUrl}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      throw new Error(`Failed to create class schedule: ${response.statusText}`);
    }
    const result = await response.json();
    return result.data;
  },

  update: async ({ id, data }: { id: string; data: ClassScheduleUpdateData }): Promise<ClassSchedule> => {
    const response = await fetch(`${privateScheduleUrl}/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      throw new Error(`Failed to update class schedule: ${response.statusText}`);
    }
    const result = await response.json();
    return result.data;
  },

  delete: async (id: string): Promise<ClassSchedule> => {
    const response = await fetch(`${privateScheduleUrl}/${id}`, {
      method: 'DELETE',
      headers: {
        'Content-Type': 'application/json',
      }
    });
    if (!response.ok) {
      throw new Error(`Failed to delete class schedule: ${response.statusText}`);
    }
    const result = await response.json();
    return result.data;
  },

  // Additional API methods specific to class schedules
  getByClass: async (classId: string): Promise<ClassSchedule[]> => {
    const params = new URLSearchParams({ classId });
    const response = await fetch(`${publicScheduleUrl}?${params}`,
      { headers: { 
        'Content-Type': 'application/json',
        'x-api-key': process.env.NEXT_PUBLIC_CLASSES_API_KEY!
       } }
    );
    if (!response.ok) {
      throw new Error(`Failed to fetch class schedules: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data?.schedules || [];
  },

  getByLocation: async (locationId: string): Promise<ClassSchedule[]> => {
    const params = new URLSearchParams({ locationId });
    const response = await fetch(`${publicScheduleUrl}?${params}`,
      { headers: { 
        'Content-Type': 'application/json',
        'x-api-key': process.env.NEXT_PUBLIC_CLASSES_API_KEY!
       } }
    );
    if (!response.ok) {
      throw new Error(`Failed to fetch class schedules: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data?.schedules || [];
  },

  getByInstructor: async (staffId: string): Promise<ClassSchedule[]> => {
    const params = new URLSearchParams({ staffId });
    const response = await fetch(`${publicScheduleUrl}?${params}`,
      {
        method: 'GET', 
        headers: { 
        'Content-Type': 'application/json',
        'x-api-key': process.env.NEXT_PUBLIC_CLASSES_API_KEY!
       } 
      }
    );
    if (!response.ok) {
      throw new Error(`Failed to fetch class schedules: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data?.schedules || [];
  },

  getByDateRange: async (tenantId: number, startDate: string, endDate: string): Promise<ClassScheduleWithRelations[]> => {
    const params = new URLSearchParams({
      tenantId: tenantId.toString(),
      startDate,
      endDate,
    });
    const response = await fetch(`${publicScheduleUrl}?${params}`,
      {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': process.env.NEXT_PUBLIC_CLASSES_API_KEY!,
        }
      }
    );
    if (!response.ok) {
      throw new Error(`Failed to fetch class schedules: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data?.schedules || [];
  },

  // Required by BaseApiInterface
  bulkOperation: async (data: any): Promise<ClassSchedule[]> => {
    const response = await fetch(`${privateScheduleUrl}/bulk`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': process.env.NEXT_PUBLIC_CLASSES_API_KEY!,
      },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      throw new Error(`Failed to perform bulk operation: ${response.statusText}`);
    }
    const result = await response.json();
    return result.data || [];
  },

  getStats: async (tenantId?: number): Promise<any> => {
    const params = new URLSearchParams();
    if (tenantId) {
      params.append('tenantId', tenantId.toString());
    }
    const response = await fetch(`${publicScheduleUrl}/stats?${params}`,
      { 
        method: 'GET',
        headers: { 
          'Content-Type': 'application/json',
          'x-api-key': process.env.NEXT_PUBLIC_CLASSES_API_KEY!
         } 
      }
    );
    if (!response.ok) {
      throw new Error(`Failed to fetch class schedule stats: ${response.statusText}`);
    }
    return response.json();
  },
};



const baseHooks = createEntityHooks('class-schedules', classScheduleApi);

// Export base hooks with class schedule-specific names
export const useClassSchedules = baseHooks.useEntities;
export const useClassSchedule = baseHooks.useEntity;
export const useClassSchedulesByTenant = baseHooks.useEntitiesByTenant;
export const useClassScheduleStats = baseHooks.useEntityStats;
export const useClassScheduleSearch = baseHooks.useEntitySearch;
export const useBulkClassScheduleOperation = baseHooks.useBulkOperation;

// Enhanced mutation hooks with proper cache invalidation and optimistic updates
export function useCreateClassSchedule() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: classScheduleApi.create,
    // Optimistic update
    onMutate: async (newSchedule: ClassScheduleFormData) => {
      // Cancel any outgoing refetches (so they don't overwrite our optimistic update)
      await queryClient.cancelQueries({ queryKey: classScheduleKeys.all });

      // Snapshot the previous value
      const previousSchedules = queryClient.getQueryData(classScheduleKeys.all);

      // Optimistically update to the new value
      const optimisticSchedule = {
        id: `temp-${Date.now()}`,
        ...newSchedule,
        created_at: new Date(),
        updated_at: new Date(),
      };

      // Update all relevant caches optimistically
      queryClient.setQueryData(classScheduleKeys.all, (old: any) =>
        old ? [...old, optimisticSchedule] : [optimisticSchedule]
      );

      // Return a context object with the snapshotted value
      return { previousSchedules, optimisticSchedule };
    },
    // If the mutation succeeds, update cache with real data
    onSuccess: (newSchedule) => {
      // Replace optimistic schedule with real schedule
      queryClient.setQueryData(classScheduleKeys.detail(newSchedule.id), newSchedule);

      // Invalidate and refetch ALL class schedule queries
      queryClient.invalidateQueries({ queryKey: classScheduleKeys.all });

      console.log('✅ Schedule created successfully, cache invalidated');
    },
    // If the mutation fails, use the context returned from onMutate to roll back
    onError: (err, _newSchedule, context) => {
      if (context?.previousSchedules) {
        queryClient.setQueryData(classScheduleKeys.all, context.previousSchedules);
      }
      console.error('❌ Failed to create schedule, rolled back optimistic update:', err);
    },
    // Always refetch after error or success
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: classScheduleKeys.all });
    },
  });
}

export function useUpdateClassSchedule() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: classScheduleApi.update,
    // Optimistic update
    onMutate: async ({ id, data }: { id: string; data: ClassScheduleUpdateData }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: classScheduleKeys.all });

      // Snapshot the previous value
      const previousSchedule = queryClient.getQueryData(classScheduleKeys.detail(id));

      // Optimistically update to the new value
      if (previousSchedule) {
        const optimisticSchedule = { ...previousSchedule, ...data, updated_at: new Date() };
        queryClient.setQueryData(classScheduleKeys.detail(id), optimisticSchedule);
      }

      return { previousSchedule, id };
    },
    // If the mutation succeeds, update cache with real data
    onSuccess: (updatedSchedule) => {
      queryClient.setQueryData(classScheduleKeys.detail(updatedSchedule.id), updatedSchedule);

      // Invalidate and refetch ALL class schedule queries
      queryClient.invalidateQueries({ queryKey: classScheduleKeys.all });

      console.log('✅ Schedule updated successfully, cache invalidated');
    },
    // If the mutation fails, roll back
    onError: (err, _variables, context) => {
      if (context?.previousSchedule && context?.id) {
        queryClient.setQueryData(classScheduleKeys.detail(context.id), context.previousSchedule);
      }
      console.error('❌ Failed to update schedule, rolled back optimistic update:', err);
    },
    // Always refetch after error or success
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: classScheduleKeys.all });
    },
  });
}

export function useDeleteClassSchedule() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: classScheduleApi.delete,
    // Optimistic update
    onMutate: async (scheduleId: string) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: classScheduleKeys.all });

      // Snapshot the previous value
      const previousSchedule = queryClient.getQueryData(classScheduleKeys.detail(scheduleId));

      // Optimistically remove from cache
      queryClient.removeQueries({ queryKey: classScheduleKeys.detail(scheduleId) });

      return { previousSchedule, scheduleId };
    },
    // If the mutation succeeds
    onSuccess: () => {
      // Invalidate and refetch ALL class schedule queries
      queryClient.invalidateQueries({ queryKey: classScheduleKeys.all });

      console.log('✅ Schedule deleted successfully, cache invalidated');
    },
    // If the mutation fails, restore the schedule
    onError: (err, _variables, context) => {
      if (context?.previousSchedule && context?.scheduleId) {
        queryClient.setQueryData(classScheduleKeys.detail(context.scheduleId), context.previousSchedule);
      }
      console.error('❌ Failed to delete schedule, restored optimistic update:', err);
    },
    // Always refetch after error or success
    onSettled: () => {
      queryClient.invalidateQueries({ queryKey: classScheduleKeys.all });
    },
  });
}

// Additional hooks specific to class schedules
export function useClassSchedulesByClass(classId: string) {
  return useQuery({
    queryKey: classScheduleKeys.byClass(classId),
    queryFn: () => classScheduleApi.getByClass(classId),
    enabled: !!classId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useClassSchedulesByLocation(locationId: string) {
  return useQuery({
    queryKey: classScheduleKeys.byLocation(locationId),
    queryFn: () => classScheduleApi.getByLocation(locationId),
    enabled: !!locationId,
    staleTime: 5 * 60 * 1000,
  });
}

export function useClassSchedulesByInstructor(staffId: string) {
  return useQuery({
    queryKey: classScheduleKeys.byInstructor(staffId),
    queryFn: () => classScheduleApi.getByInstructor(staffId),
    enabled: !!staffId,
    staleTime: 5 * 60 * 1000,
  });
}

export function useClassSchedulesByDateRange(
  tenantId: number,
  startDate: string,
  endDate: string
) {
  return useQuery({
    queryKey: classScheduleKeys.byDateRange(startDate, endDate),
    queryFn: () => classScheduleApi.getByDateRange(tenantId, startDate, endDate),
    enabled: !!tenantId && !!startDate && !!endDate,
    staleTime: 2 * 60 * 1000, // 2 minutes for calendar data
  });
}

export function useClassScheduleSearchAdvanced(
  tenantId: number,
  classId?: string,
  name?: string,
  description?: string,
  locationId?: string,
  facilityId?: string,
  staffId?: string,
  startDate?: string,
  endDate?: string,
  searchTerm?: string,
  limit = 20,
  offset = 0
) {
  return useQuery({
    queryKey: classScheduleKeys.search({
      tenantId,
      classId,
      name,
      description,
      locationId,
      facilityId,
      staffId,
      startDate,
      endDate,
      searchTerm,
      limit,
      offset,
    }),
    queryFn: () => classScheduleApi.searchAdvanced(
      tenantId,
      classId,
      name,
      description,
      locationId,
      facilityId,
      staffId,
      startDate,
      endDate,
      searchTerm,
      limit,
      offset
    ),
    enabled: !!tenantId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}
