# 🚀 Role Management Quick Reference

> **Cheat Sheet untuk Developer - <PERSON><PERSON><PERSON> ke Informasi Penting**

---

## 🔧 Quick Fix Commands

### Test All CRUD Operations
```bash
curl -X POST "http://localhost:3002/api/test-crud-complete" -H "Content-Type: application/json"
```

### Test Individual Operations
```bash
# CREATE
curl -X POST "http://localhost:3002/api/test-role-creation" -H "Content-Type: application/json"

# UPDATE  
curl -X POST "http://localhost:3002/api/test-role-update" -H "Content-Type: application/json" \
  -d '{"roleId":"ROLE_ID","updateData":{"display_name":"New Name"}}'

# DELETE
curl -X POST "http://localhost:3002/api/test-role-delete" -H "Content-Type: application/json" \
  -d '{"roleId":"ROLE_ID"}'
```

---

## 🎯 Common Code Patterns

### ✅ Correct RBAC Middleware Usage

```typescript
// For static routes (/api/roles)
export const POST = withRBAC(
  async (request: NextRequest, context: RBACContext) => {
    // Your logic here
  },
  { module: "roles", action: "create" }
);

// For dynamic routes (/api/roles/[id])  
export const PUT = withRBACParams(
  async (request: NextRequest, context: RBACContext, { params }: { params: { id: string } }) => {
    // Your logic here with params.id
  },
  { module: "roles", action: "update" }
);
```

### ✅ Proper Error Handling

```typescript
try {
  const result = await RoleService.update(params.id, validatedData);
  return NextResponse.json({
    success: true,
    data: result,
    message: "Role updated successfully",
  });
} catch (error) {
  console.error("API Error:", error);
  
  if (error instanceof z.ZodError) {
    return NextResponse.json(
      { success: false, error: "Validation failed", details: error.errors },
      { status: 400 }
    );
  }

  return NextResponse.json(
    { success: false, error: "Internal server error" },
    { status: 500 }
  );
}
```

### ✅ Frontend Data Access

```typescript
// Correct way to access roles data
const {
  data: rolesData,
  isLoading,
  error,
  refetch,
} = useRoleSearch(undefined, undefined, undefined, 100, 0);

const roles = rolesData?.roles || []; // NOT rolesData?.data?.roles

// Proper cache refresh
const forceRefresh = async () => {
  queryClient.removeQueries({ queryKey: roleKeys.all });
  queryClient.invalidateQueries({ queryKey: roleKeys.all });
  await refetch();
};
```

---

## 🚨 Troubleshooting Quick Fixes

### RBAC_INTERNAL_ERROR
```typescript
// ❌ Wrong - causes RBAC_INTERNAL_ERROR
export const PUT = withRBAC(
  async (request, context, { params }) => { ... }
)

// ✅ Fix - use withRBACParams for dynamic routes
export const PUT = withRBACParams(
  async (request, context, { params }) => { ... }
)
```

### Authentication Issues
```bash
# Login as admin
Email: <EMAIL>
Password: password

# Check session in browser DevTools
localStorage.getItem('next-auth.session-token')
```

### Data Not Refreshing
```typescript
// ✅ Force refresh after mutations
const handleSuccess = async () => {
  await forceRefresh();
  setSuccessMessage("Operation successful!");
};
```

---

## 📋 API Endpoints Reference

| Method | Endpoint | Middleware | Purpose |
|--------|----------|------------|---------|
| GET | `/api/roles` | `withRBAC` | List roles |
| POST | `/api/roles` | `withRBAC` | Create role |
| GET | `/api/roles/[id]` | `withRBAC` | Get role by ID |
| PUT | `/api/roles/[id]` | `withRBACParams` | Update role |
| DELETE | `/api/roles/[id]` | `withRBACParams` | Delete role |

---

## 🔍 Debug Commands

### Check Database State
```sql
-- See all roles
SELECT id, name, display_name, is_active, created_at 
FROM roles 
ORDER BY created_at DESC;

-- Check specific role
SELECT * FROM roles WHERE id = 'ROLE_ID';
```

### Enable Debug Logging
```typescript
// Add to API endpoints
console.log("🔍 Debug:", {
  method: request.method,
  url: request.url,
  params: params,
  userId: token?.sub
});
```

---

## 💡 Best Practices Checklist

- [ ] ✅ Use `withRBACParams` for dynamic routes
- [ ] ✅ Use `withRBAC` for static routes  
- [ ] ✅ Always validate input with Zod
- [ ] ✅ Use transactions for database operations
- [ ] ✅ Implement proper error handling
- [ ] ✅ Use soft delete (`is_active = false`)
- [ ] ✅ Invalidate cache after mutations
- [ ] ✅ Log security events for audit

---

## 🎯 Performance Tips

### Database
```typescript
// ✅ Use transactions
return await db.transaction(async (tx) => {
  // Multiple operations here
});

// ✅ Use proper indexes
// Add to schema: index('idx_roles_tenant_active').on(roles.tenantId, roles.is_active)
```

### Frontend
```typescript
// ✅ Debounced search
const debouncedSearch = useDebounce(searchTerm, 300);

// ✅ Pagination
const { data } = useRoleSearch(undefined, search, undefined, 20, offset);
```

---

## 🔐 Security Checklist

- [ ] ✅ RBAC middleware on all protected endpoints
- [ ] ✅ Input validation with Zod schemas
- [ ] ✅ SQL injection prevention (Drizzle ORM)
- [ ] ✅ XSS prevention (proper JSON responses)
- [ ] ✅ Admin bypass <NAME_EMAIL>
- [ ] ✅ Audit logging for sensitive operations

---

## 📞 Emergency Contacts

### Critical Issues
1. **Check server logs** first
2. **Test with curl commands** above
3. **Verify database state** with SQL queries
4. **Restart development server** if needed

### Common Solutions
- **RBAC_INTERNAL_ERROR**: Use `withRBACParams` for dynamic routes
- **Authentication failed**: <NAME_EMAIL>
- **Data not refreshing**: Check cache invalidation
- **Validation errors**: Check Zod schemas

---

*Quick Reference v1.0.0 - Last Updated: 2025-01-19*
