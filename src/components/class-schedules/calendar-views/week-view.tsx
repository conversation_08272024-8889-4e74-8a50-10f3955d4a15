"use client";

import { useMemo } from "react";
import { motion } from "framer-motion";
import { Plus, Clock, Users, MapPin, Edit, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { type ClassSchedule } from "@/lib/db/schema";

/**
 * WeekView Component
 * 
 * Komponen untuk menampilkan calendar dalam view mingguan.
 * Menampilkan time slots dengan detail yang lebih baik.
 */

interface CalendarEvent extends ClassSchedule {
  class_name?: string;
  location_name?: string;
  instructor_name?: string;
}

interface WeekViewProps {
  currentDate: Date;
  schedulesByDate: Record<string, CalendarEvent[]>;
  onDateClick: (date: Date, time?: string) => void;
  onEventClick: (event: CalendarEvent) => void;
  onEditEvent: (event: CalendarEvent) => void;
  onDeleteEvent: (event: CalendarEvent) => void;
  getClassName: (classId: string) => string;
  getLocationName: (locationId: string | null) => string;
  formatTime: (time: Date | string | null) => string;
}

export function WeekView({
  currentDate,
  schedulesByDate,
  onDateClick,
  onEventClick,
  onEditEvent,
  onDeleteEvent,
  getClassName,
  getLocationName,
  formatTime,
}: WeekViewProps) {
  // Generate week days
  const weekDays = useMemo(() => {
    const startOfWeek = new Date(currentDate);
    startOfWeek.setDate(startOfWeek.getDate() - startOfWeek.getDay());
    
    const days = [];
    for (let i = 0; i < 7; i++) {
      const day = new Date(startOfWeek);
      day.setDate(day.getDate() + i);
      days.push({
        date: day,
        dateString: day.toISOString().split('T')[0],
        isToday: day.toDateString() === new Date().toDateString(),
        dayName: day.toLocaleDateString('en-US', { weekday: 'short' }),
        dayNumber: day.getDate(),
      });
    }
    
    return days;
  }, [currentDate]);

  // Generate time slots (6 AM to 10 PM)
  const timeSlots = useMemo(() => {
    const slots = [];
    for (let hour = 6; hour <= 22; hour++) {
      slots.push({
        hour,
        time: `${hour.toString().padStart(2, '0')}:00`,
        displayTime: new Date(0, 0, 0, hour).toLocaleTimeString('en-US', { 
          hour: 'numeric',
          hour12: true 
        }),
      });
    }
    return slots;
  }, []);

  return (
    <TooltipProvider>
      <div className="week-view">
        {/* Week Header */}
        <div className="grid grid-cols-8 border-b sticky top-0 bg-background z-10">
          {/* Time column header */}
          <div className="p-3 border-r bg-muted/50"></div>
          
          {/* Day headers */}
          {weekDays.map((day) => (
            <div
              key={day.dateString}
              className={`
                p-3 text-center border-r bg-muted/50
                ${day.isToday ? 'bg-primary/10 border-primary/20' : ''}
              `}
            >
              <div className="text-sm font-medium">{day.dayName}</div>
              <div
                className={`
                  text-lg font-semibold mt-1
                  ${day.isToday ? 'bg-primary text-primary-foreground rounded-full w-8 h-8 flex items-center justify-center mx-auto' : ''}
                `}
              >
                {day.dayNumber}
              </div>
            </div>
          ))}
        </div>

        {/* Time Grid */}
        <div className="relative">
          {timeSlots.map((slot, slotIndex) => (
            <div key={slot.hour} className="grid grid-cols-8 border-b min-h-[60px]">
              {/* Time Label */}
              <div className="p-2 border-r bg-muted/20 text-xs text-muted-foreground text-right">
                {slot.displayTime}
              </div>
              
              {/* Day Columns */}
              {weekDays.map((day) => {
                const daySchedules = schedulesByDate[day.dateString] || [];
                const slotSchedules = daySchedules.filter(schedule => {
                  if (!schedule.start_time) return false;
                  const scheduleHour = new Date(schedule.start_time).getHours();
                  return scheduleHour === slot.hour;
                });

                return (
                  <motion.div
                    key={`${day.dateString}-${slot.hour}`}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ duration: 0.2, delay: slotIndex * 0.02 }}
                    className={`
                      border-r p-1 relative cursor-pointer
                      hover:bg-muted/30 transition-colors
                      ${day.isToday ? 'bg-primary/5' : ''}
                      ${slotSchedules.length > 0 ? (
                        slotSchedules.length > 1 ? 'bg-red-50' :
                        'bg-green-50'
                      ) : ''}
                    `}
                    onClick={() => onDateClick(day.date, slot.time)}
                  >
                    {/* Quick Add Button */}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="absolute top-1 right-1 h-6 w-6 p-0 opacity-0 hover:opacity-100 transition-opacity"
                      onClick={(e) => {
                        e.stopPropagation();
                        onDateClick(day.date, slot.time);
                      }}
                    >
                      <Plus className="h-3 w-3" />
                    </Button>

                    {/* Schedules */}
                    <div className="space-y-1">
                      {slotSchedules.map((schedule, scheduleIndex) => (
                        <Tooltip key={schedule.id}>
                          <TooltipTrigger asChild>
                            <motion.div
                              initial={{ opacity: 0, x: -20 }}
                              animate={{ opacity: 1, x: 0 }}
                              transition={{ duration: 0.2, delay: scheduleIndex * 0.1 }}
                              className={`
                                text-xs p-2 rounded cursor-pointer
                                hover:shadow-md transition-all
                                group relative
                              `}
                              style={{
                                backgroundColor: schedule.calender_color || '#3b82f6',
                                color: 'white',
                              }}
                              onClick={(e) => {
                                e.stopPropagation();
                                onEventClick(schedule);
                              }}
                            >
                              <div className="space-y-1">
                                <div className="font-medium truncate">
                                  {schedule.class_name || getClassName(schedule.class_id)}
                                </div>
                                <div className="flex items-center gap-1 text-xs opacity-90">
                                  <Clock className="h-3 w-3" />
                                  {formatTime(schedule.start_time)} - {formatTime(schedule.end_time)}
                                </div>
                                {schedule.location_id && (
                                  <div className="flex items-center gap-1 text-xs opacity-90">
                                    <MapPin className="h-3 w-3" />
                                    {getLocationName(schedule.location_id)}
                                  </div>
                                )}
                                <div className="flex items-center gap-1 text-xs opacity-90">
                                  <Users className="h-3 w-3" />
                                  {schedule.pax}
                                </div>
                              </div>
                              
                              {/* Quick Actions */}
                              <div className="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity flex gap-1">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-4 w-4 p-0 text-white hover:bg-white/20"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    onEditEvent(schedule);
                                  }}
                                >
                                  <Edit className="h-3 w-3" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-4 w-4 p-0 text-white hover:bg-white/20"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    onDeleteEvent(schedule);
                                  }}
                                >
                                  <Trash2 className="h-3 w-3" />
                                </Button>
                              </div>
                            </motion.div>
                          </TooltipTrigger>
                          <TooltipContent side="top" className="max-w-xs">
                            <div className="space-y-1">
                              <div className="font-medium">
                                {schedule.class_name || getClassName(schedule.class_id)}
                              </div>
                              <div className="flex items-center gap-2 text-xs">
                                <Clock className="h-3 w-3" />
                                {formatTime(schedule.start_time)} - {formatTime(schedule.end_time)}
                              </div>
                              {schedule.location_id && (
                                <div className="flex items-center gap-2 text-xs">
                                  <MapPin className="h-3 w-3" />
                                  {getLocationName(schedule.location_id)}
                                </div>
                              )}
                              <div className="flex items-center gap-2 text-xs">
                                <Users className="h-3 w-3" />
                                Capacity: {schedule.pax} | Waitlist: {schedule.waitlist}
                              </div>
                              {schedule.is_private && (
                                <Badge variant="secondary" className="text-xs">
                                  Private
                                </Badge>
                              )}
                              {schedule.allow_classpass && (
                                <Badge variant="outline" className="text-xs">
                                  Class Pass
                                </Badge>
                              )}
                            </div>
                          </TooltipContent>
                        </Tooltip>
                      ))}
                    </div>
                  </motion.div>
                );
              })}
            </div>
          ))}
        </div>
      </div>
    </TooltipProvider>
  );
}
