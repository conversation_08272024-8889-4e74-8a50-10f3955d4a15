import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { z } from "zod";
import { packageService } from "@/lib/services/package.service";

// Validation schemas
const createPackageSchema = z.object({
  tenantId: z.number().int().positive("Tenant ID must be positive"),
  name: z.string().min(1, "Package name is required").max(255, "Package name too long"),
  description: z.string().optional(),
  isActive: z.boolean().optional(),
  is_private: z.boolean().optional(),
  validity_date: z.string().optional(),
  validity_duration: z.number().int().min(0, "Validity duration cannot be negative").optional(),
  schedule_availability: z.array(z.object({
    id: z.string(),
    start_date: z.string().optional(),
    start_time: z.string().optional(),
    end_date: z.string().optional(),
    end_time: z.string().optional(),
  })).optional(),
  included_classes: z.array(z.object({
    id: z.string(),
    name: z.string(),
    category: z.string().optional(),
    included_at: z.string(),
  })).optional(),
});

const querySchema = z.object({
  search: z.string().optional(),
  tenantId: z.string().optional(),
  isActive: z.string().optional(),
  is_private: z.string().optional(),
  limit: z.string().optional(),
  offset: z.string().optional(),
});

const bulkOperationSchema = z.object({
  ids: z.array(z.string().min(1)),
  action: z.enum(["delete"]),
});

/**
 * GET /api/packages
 * Get packages with optional filtering
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const tenantIdParam = searchParams.get("tenantId");
    const queryParams = Object.fromEntries(searchParams.entries());

    const validatedQuery = querySchema.parse(queryParams);

    // Build filters
    const filters = {
      search: validatedQuery.search,
      isActive: validatedQuery.isActive ? validatedQuery.isActive === 'true' : undefined,
      is_private: validatedQuery.is_private ? validatedQuery.is_private === 'true' : undefined,
    };

    console.log(`📦 [GET /api/packages] Fetching packages:`, filters);

    // Always use getByTenant method
    const tenantId = tenantIdParam ? parseInt(tenantIdParam) : 0;
    const packages = await packageService.getByTenant(tenantId, filters);

    return NextResponse.json({
      success: true,
      data: packages,
      meta: {
        total: packages.length,
        filters,
      }
    });
  } catch (error) {
    console.error("📦 [GET /api/packages] Error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid query parameters", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to fetch packages" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/packages
 * Create a new package
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = createPackageSchema.parse(body);

    console.log(`📦 [POST /api/packages] Creating package:`, validatedData);

    const pkg = await packageService.create(validatedData);

    return NextResponse.json({
      success: true,
      data: pkg,
      message: "Package created successfully"
    }, { status: 201 });
  } catch (error) {
    console.error("📦 [POST /api/packages] Error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid package data", details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error && error.message.includes("already exists")) {
      return NextResponse.json(
        { error: error.message },
        { status: 409 }
      );
    }

    return NextResponse.json(
      { error: "Failed to create package" },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/packages
 * Bulk delete packages
 */
export async function DELETE(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = bulkOperationSchema.parse(body);

    console.log(`📦 [DELETE /api/packages] Bulk operation:`, validatedData);

    if (validatedData.action === "delete") {
      const results = await packageService.bulkOperation({
        ids: validatedData.ids,
        action: "delete"
      });

      return NextResponse.json({
        success: true,
        data: results,
        message: `Successfully deleted ${validatedData.ids.length} packages`
      });
    }

    return NextResponse.json(
      { error: "Invalid bulk operation action" },
      { status: 400 }
    );
  } catch (error) {
    console.error("📦 [DELETE /api/packages] Error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid bulk operation data", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to perform bulk operation" },
      { status: 500 }
    );
  }
}
