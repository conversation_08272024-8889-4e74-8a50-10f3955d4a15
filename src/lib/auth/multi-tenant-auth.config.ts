import NextAuth from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import CredentialsProvider from "next-auth/providers/credentials";
import { z } from "zod";
import { tenantContextService } from "@/lib/services/tenant-context.service";
import { customerAuthService, customerLoginSchema } from "@/lib/services/customer-auth.service";
import { headers } from "next/headers";

/**
 * Enterprise Multi-Tenant NextAuth Configuration
 * 
 * Implements FAANG-level authentication with:
 * - Automatic tenant context detection
 * - Google OAuth for customers only
 * - Tenant-scoped user isolation
 * - Comprehensive security and audit logging
 */

// Rate limiting store (use Redis in production)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

function checkRateLimit(identifier: string, maxAttempts = 5, windowMs = 15 * 60 * 1000): boolean {
  const now = Date.now();
  const record = rateLimitStore.get(identifier);

  if (!record || now > record.resetTime) {
    rateLimitStore.set(identifier, { count: 1, resetTime: now + windowMs });
    return true;
  }

  if (record.count >= maxAttempts) {
    return false;
  }

  record.count++;
  return true;
}

export const multiTenantAuthConfig = {
  secret: process.env.NEXTAUTH_SECRET,
  trustHost: true,
  
  providers: [
    // Google OAuth Provider (ONLY for customers)
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      allowDangerousEmailAccountLinking: false, // Enhanced security
      authorization: {
        params: {
          prompt: "consent",
          access_type: "offline",
          response_type: "code",
          scope: "openid email profile",
        },
      },
      profile(profile) {
        console.log("🔍 Google OAuth profile received:", {
          id: profile.sub,
          email: profile.email,
          name: profile.name,
          emailVerified: profile.email_verified,
        });

        return {
          id: profile.sub,
          email: profile.email,
          name: profile.name,
          given_name: profile.given_name,
          family_name: profile.family_name,
          image: profile.picture,
          email_verified: profile.email_verified,
          locale: profile.locale,
        };
      },
    }),

    // Credentials Provider (for customers with email/password)
    CredentialsProvider({
      name: "customer-credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" },
        rememberMe: { label: "Remember Me", type: "checkbox" },
      },
      async authorize(credentials, req) {
        try {
          const clientIP = req?.headers?.['x-forwarded-for'] as string || 
                          req?.headers?.['x-real-ip'] as string || 
                          'unknown';

          console.log("🔐 Customer credential auth attempt:", {
            email: credentials?.email,
            clientIP,
            userAgent: req?.headers?.['user-agent'],
          });

          // Get tenant context from current request
          const tenantContext = await tenantContextService.getCurrentTenantContext();
          if (!tenantContext) {
            console.log("❌ No tenant context found");
            return null;
          }

          // Rate limiting per tenant and IP
          const rateLimitKey = `customer-auth:${tenantContext.id}:${clientIP}:${credentials?.email}`;
          if (!checkRateLimit(rateLimitKey, 5, 15 * 60 * 1000)) {
            console.log("❌ Rate limit exceeded for customer auth");
            throw new Error("Too many login attempts. Please try again later.");
          }

          // Validate input
          const { email, password, rememberMe } = customerLoginSchema.parse(credentials);

          // Authenticate customer
          const authResult = await customerAuthService.authenticateWithCredentials(
            email,
            password,
            tenantContext.id,
            clientIP
          );

          if (!authResult.success || !authResult.customer) {
            console.log("❌ Customer authentication failed:", authResult.error);
            return null;
          }

          console.log("✅ Customer authentication successful");

          return {
            id: authResult.customer.id,
            email: authResult.customer.email,
            name: authResult.customer.displayName,
            image: authResult.customer.image,
            // Custom fields for session
            userType: "customer",
            tenantId: authResult.customer.tenantId,
            firstName: authResult.customer.firstName,
            lastName: authResult.customer.lastName,
            membershipType: authResult.customer.membershipType,
            isEmailVerified: authResult.customer.isEmailVerified,
            preferences: authResult.customer.preferences,
            rememberMe: rememberMe || false,
          };
        } catch (error) {
          console.error("❌ Customer credential auth error:", error);
          return null;
        }
      },
    }),
  ],

  session: {
    strategy: "jwt" as const,
    maxAge: 30 * 24 * 60 * 60, // 30 days
    updateAge: 24 * 60 * 60, // 24 hours
  },

  // Enhanced security settings
  cookies: {
    sessionToken: {
      name: process.env.NODE_ENV === 'production' 
        ? `__Secure-next-auth.session-token` 
        : `next-auth.session-token`,
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: process.env.NODE_ENV === "production",
        domain: process.env.NODE_ENV === "production" 
          ? process.env.COOKIE_DOMAIN 
          : undefined,
      },
    },
    callbackUrl: {
      name: process.env.NODE_ENV === 'production' 
        ? `__Secure-next-auth.callback-url` 
        : `next-auth.callback-url`,
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: process.env.NODE_ENV === "production",
      },
    },
    csrfToken: {
      name: process.env.NODE_ENV === 'production' 
        ? `__Host-next-auth.csrf-token` 
        : `next-auth.csrf-token`,
      options: {
        httpOnly: true,
        sameSite: "lax",
        path: "/",
        secure: process.env.NODE_ENV === "production",
      },
    },
  },

  callbacks: {
    async jwt({ token, user, account, trigger, session }) {
      try {
        // Get tenant context
        const tenantContext = await tenantContextService.getCurrentTenantContext();
        
        if (user && tenantContext) {
          // Initial sign in
          token.userType = user.userType || "customer";
          token.tenantId = tenantContext.id;
          token.tenantName = tenantContext.name;
          token.tenantDomain = tenantContext.domain;
          
          // Customer-specific data
          if (user.userType === "customer") {
            token.firstName = user.firstName;
            token.lastName = user.lastName;
            token.membershipType = user.membershipType;
            token.isEmailVerified = user.isEmailVerified;
            token.preferences = user.preferences;
            token.rememberMe = user.rememberMe;
          }
        }

        // Handle Google OAuth
        if (account?.provider === "google" && user && tenantContext) {
          console.log("🔐 Processing Google OAuth for customer");
          
          try {
            const clientIP = headers().get('x-forwarded-for') || 
                            headers().get('x-real-ip') || 
                            'unknown';

            const authResult = await customerAuthService.authenticateWithGoogle(
              {
                id: user.id,
                email: user.email!,
                name: user.name!,
                given_name: user.firstName,
                family_name: user.lastName,
                picture: user.image,
                email_verified: user.email_verified || true,
                locale: user.locale,
              },
              account,
              tenantContext.id,
              clientIP
            );

            if (authResult.success && authResult.customer) {
              token.userType = "customer";
              token.tenantId = authResult.customer.tenantId;
              token.firstName = authResult.customer.firstName;
              token.lastName = authResult.customer.lastName;
              token.membershipType = authResult.customer.membershipType;
              token.isEmailVerified = authResult.customer.isEmailVerified;
              token.preferences = authResult.customer.preferences;
              token.sub = authResult.customer.id; // Update user ID to customer ID
            } else {
              console.error("❌ Google OAuth customer auth failed:", authResult.error);
              throw new Error(authResult.error || "Google authentication failed");
            }
          } catch (error) {
            console.error("❌ Error processing Google OAuth:", error);
            throw error;
          }
        }

        // Store tenant context in token for validation
        if (tenantContext) {
          token.tenantId = tenantContext.id;
          token.tenantName = tenantContext.name;
          token.tenantDomain = tenantContext.domain;
        }

        return token;
      } catch (error) {
        console.error("❌ JWT callback error:", error);
        throw error;
      }
    },

    async session({ session, token }) {
      try {
        console.log("📋 Building session for user:", {
          id: token.sub,
          email: token.email,
          userType: token.userType,
          tenantId: token.tenantId,
        });

        if (token && session.user) {
          // Basic user info
          session.user.id = token.sub as string;
          session.user.email = token.email as string;
          session.user.name = token.name as string;
          session.user.image = token.image as string;
          
          // Multi-tenant context
          session.user.userType = token.userType as string;
          session.user.tenantId = token.tenantId as number;
          session.user.tenantName = token.tenantName as string;
          session.user.tenantDomain = token.tenantDomain as string;
          
          // Customer-specific data
          if (token.userType === "customer") {
            session.user.firstName = token.firstName as string;
            session.user.lastName = token.lastName as string;
            session.user.membershipType = token.membershipType as string;
            session.user.isEmailVerified = token.isEmailVerified as boolean;
            session.user.preferences = token.preferences as any;
          }
        }

        console.log("✅ Session built successfully");
        return session;
      } catch (error) {
        console.error("❌ Session callback error:", error);
        throw error;
      }
    },

    async signIn({ user, account, profile, email, credentials }) {
      try {
        console.log("🚪 SignIn callback:", {
          provider: account?.provider,
          userEmail: user.email,
          userType: user.userType,
        });

        // Get tenant context
        const tenantContext = await tenantContextService.getCurrentTenantContext();
        if (!tenantContext) {
          console.log("❌ No tenant context in signIn callback");
          return false;
        }

        if (!tenantContext.isActive) {
          console.log("❌ Tenant is inactive:", tenantContext.id);
          return false;
        }

        // Rate limiting for OAuth attempts
        if (account?.provider && user.email) {
          const clientIP = headers().get('x-forwarded-for') || 
                          headers().get('x-real-ip') || 
                          'unknown';
          
          const rateLimitKey = `oauth:${account.provider}:${tenantContext.id}:${clientIP}:${user.email}`;
          if (!checkRateLimit(rateLimitKey, 10, 15 * 60 * 1000)) {
            console.log("❌ OAuth rate limit exceeded");
            return false;
          }
        }

        // Google OAuth specific validation
        if (account?.provider === "google") {
          const tenantConfig = await tenantContextService.getTenantOAuthConfig(tenantContext.id);
          
          if (!tenantConfig.allowGoogleOAuth) {
            console.log("❌ Google OAuth disabled for tenant:", tenantContext.id);
            return false;
          }

          if (!profile?.email_verified) {
            console.log("❌ Google email not verified");
            return false;
          }

          // Check allowed domains
          if (tenantConfig.allowedDomains.length > 0) {
            const emailDomain = user.email?.split('@')[1];
            if (emailDomain && !tenantConfig.allowedDomains.includes(emailDomain)) {
              console.log("❌ Email domain not allowed:", emailDomain);
              return false;
            }
          }
        }

        // Credentials provider validation
        if (account?.provider === "customer-credentials") {
          // Additional validation already done in authorize function
          return true;
        }

        return true;
      } catch (error) {
        console.error("❌ SignIn callback error:", error);
        return false;
      }
    },

    async redirect({ url, baseUrl }) {
      try {
        console.log("🔀 Redirect callback:", { url, baseUrl });
        
        const tenantContext = await tenantContextService.getCurrentTenantContext();
        const tenantBaseUrl = tenantContext?.domain 
          ? `${process.env.NODE_ENV === 'production' ? 'https' : 'http'}://${tenantContext.domain}`
          : baseUrl;

        // Allows relative callback URLs
        if (url.startsWith("/")) {
          return `${tenantBaseUrl}${url}`;
        }

        // Allows callback URLs on the same origin or tenant domain
        try {
          const urlObj = new URL(url);
          const baseUrlObj = new URL(tenantBaseUrl);
          
          if (urlObj.origin === baseUrlObj.origin || urlObj.origin === baseUrl) {
            return url;
          }
        } catch {
          // Invalid URL, fallback to default
        }

        // Default redirect to customer dashboard
        return `${tenantBaseUrl}/dashboard`;
      } catch (error) {
        console.error("❌ Redirect callback error:", error);
        return `${baseUrl}/dashboard`;
      }
    },
  },

  pages: {
    signIn: "/auth/signin",
    error: "/auth/error",
    verifyRequest: "/auth/verify-request",
  },

  events: {
    async signIn({ user, account, isNewUser }) {
      try {
        const tenantContext = await tenantContextService.getCurrentTenantContext();
        
        console.log(`✅ Customer ${user.email} signed in with ${account?.provider}${isNewUser ? ' (new customer)' : ''} on tenant ${tenantContext?.name}`);
        
        // Log to audit system (implement based on your needs)
        // await auditLogger.log({
        //   event: 'customer_signin',
        //   userId: user.id,
        //   email: user.email,
        //   provider: account?.provider,
        //   tenantId: tenantContext?.id,
        //   isNewUser,
        //   timestamp: new Date(),
        //   ipAddress: headers().get('x-forwarded-for'),
        //   userAgent: headers().get('user-agent'),
        // });
      } catch (error) {
        console.error("❌ SignIn event error:", error);
      }
    },

    async signOut({ session }) {
      try {
        console.log(`👋 Customer ${session?.user?.email} signed out from tenant ${session?.user?.tenantName}`);
        
        // Log to audit system
        // await auditLogger.log({
        //   event: 'customer_signout',
        //   userId: session?.user?.id,
        //   email: session?.user?.email,
        //   tenantId: session?.user?.tenantId,
        //   timestamp: new Date(),
        // });
      } catch (error) {
        console.error("❌ SignOut event error:", error);
      }
    },

    async createUser({ user }) {
      try {
        const tenantContext = await tenantContextService.getCurrentTenantContext();
        
        console.log(`👤 New customer created: ${user.email} on tenant ${tenantContext?.name}`);
        
        // Send welcome email or trigger onboarding
        // await emailService.sendWelcomeEmail(user.email, user.name, tenantContext);
      } catch (error) {
        console.error("❌ CreateUser event error:", error);
      }
    },
  },

  debug: process.env.NODE_ENV === "development",

  logger: {
    error(code, metadata) {
      console.error("❌ NextAuth Error:", code, metadata);
      
      // Send to error monitoring service in production
      if (process.env.NODE_ENV === 'production') {
        // await errorMonitoring.captureException(new Error(`NextAuth Error: ${code}`), {
        //   extra: metadata,
        //   tags: { component: 'nextauth' },
        // });
      }
    },
    warn(code) {
      console.warn("⚠️ NextAuth Warning:", code);
    },
    debug(code, metadata) {
      if (process.env.NODE_ENV === "development") {
        console.log("🐛 NextAuth Debug:", code, metadata);
      }
    },
  },
};

export const { handlers, auth, signIn, signOut } = NextAuth(multiTenantAuthConfig);