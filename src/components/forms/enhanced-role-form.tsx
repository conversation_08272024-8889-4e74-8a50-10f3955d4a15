"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Shield, Users, Settings, ChevronDown, ChevronRight } from "lucide-react";
import {
  useCreateRole,
  useUpdateRole,
} from "@/lib/hooks/queries/use-role-queries";
import {
  useGroupedPermissions,
} from "@/lib/hooks/queries/use-permission-queries";
import { type Role, type Permission } from "@/lib/db/schema";

// Form validation schema
const roleFormSchema = z.object({
  name: z.string().min(1, "Role name is required").regex(/^[a-z_]+$/, "Role name must be lowercase with underscores only"),
  display_name: z.string().min(1, "Display name is required"),
  description: z.string().optional(),
  is_system_role: z.boolean().default(false),
  hierarchy_level: z.number().min(0).max(100),
  permissions: z.array(z.string()).default([]),
});

type RoleFormValues = z.infer<typeof roleFormSchema>;

interface EnhancedRoleFormProps {
  role?: Role;
  tenantId?: number | null;
  onSubmit: () => void;
  onCancel: () => void;
}

export function EnhancedRoleForm({ role, tenantId, onSubmit, onCancel }: EnhancedRoleFormProps) {
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([]);
  const [expandedModules, setExpandedModules] = useState<Set<string>>(new Set());

  // Data fetching
  const { data: groupedPermissions = {}, isLoading: permissionsLoading } = useGroupedPermissions();

  // Mutations
  const createMutation = useCreateRole();
  const updateMutation = useUpdateRole();

  // Form setup
  const form = useForm<RoleFormValues>({
    resolver: zodResolver(roleFormSchema),
    defaultValues: {
      name: role?.name || "",
      display_name: role?.display_name || "",
      description: role?.description || "",
      is_system_role: role?.is_system_role || false,
      hierarchy_level: role?.hierarchy_level || 50,
      permissions: [],
    },
  });

  const { register, handleSubmit, formState: { errors }, watch, setValue } = form;

  // Initialize selected permissions when role data is available
  useEffect(() => {
    if (role?.permissions) {
      const permissionIds = role.permissions.map(p => p.id);
      setSelectedPermissions(permissionIds);
      setValue("permissions", permissionIds);
    }
  }, [role, setValue]);

  // Handle permission selection
  const handlePermissionToggle = (permissionId: string) => {
    const newSelected = selectedPermissions.includes(permissionId)
      ? selectedPermissions.filter(id => id !== permissionId)
      : [...selectedPermissions, permissionId];
    
    setSelectedPermissions(newSelected);
    setValue("permissions", newSelected);
  };

  // Handle module selection (select/deselect all permissions in module)
  const handleModuleToggle = (module: string) => {
    const modulePermissions = groupedPermissions[module] || [];
    const modulePermissionIds = modulePermissions.map(p => p.id);
    const allSelected = modulePermissionIds.every(id => selectedPermissions.includes(id));
    
    let newSelected: string[];
    if (allSelected) {
      // Deselect all permissions in this module
      newSelected = selectedPermissions.filter(id => !modulePermissionIds.includes(id));
    } else {
      // Select all permissions in this module
      newSelected = [...new Set([...selectedPermissions, ...modulePermissionIds])];
    }
    
    setSelectedPermissions(newSelected);
    setValue("permissions", newSelected);
  };

  // Toggle module expansion
  const toggleModuleExpansion = (module: string) => {
    const newExpanded = new Set(expandedModules);
    if (newExpanded.has(module)) {
      newExpanded.delete(module);
    } else {
      newExpanded.add(module);
    }
    setExpandedModules(newExpanded);
  };

  const handleFormSubmit = async (data: RoleFormValues) => {
    try {
      setSubmitError(null);

      const roleData = {
        name: data.name,
        display_name: data.display_name,
        description: data.description,
        is_system_role: data.is_system_role,
        hierarchy_level: data.hierarchy_level,
        tenantId: data.is_system_role ? null : tenantId,
        permissions: data.permissions,
      };

      if (role) {
        // Update existing role
        console.log("🔄 Updating role:", role.id, roleData);
        await updateMutation.mutateAsync({
          id: role.id,
          data: roleData,
        });
        console.log("✅ Role updated successfully");
      } else {
        // Create new role
        console.log("➕ Creating new role:", roleData);
        const createdRole = await createMutation.mutateAsync(roleData);
        console.log("✅ Role created successfully:", createdRole);
      }

      onSubmit();
    } catch (error: any) {
      console.error("Error submitting role:", error);
      setSubmitError(error.message || "An error occurred while saving the role");
    }
  };

  const isLoading = createMutation.isPending || updateMutation.isPending;

  const getModuleColor = (module: string) => {
    const colors = {
      system: "bg-red-100 text-red-800",
      tenant: "bg-blue-100 text-blue-800",
      users: "bg-green-100 text-green-800",
      roles: "bg-purple-100 text-purple-800",
      classes: "bg-orange-100 text-orange-800",
      "class-schedules": "bg-yellow-100 text-yellow-800",
      customers: "bg-pink-100 text-pink-800",
      packages: "bg-indigo-100 text-indigo-800",
      locations: "bg-teal-100 text-teal-800",
      equipment: "bg-cyan-100 text-cyan-800",
      facilities: "bg-lime-100 text-lime-800",
      bookings: "bg-amber-100 text-amber-800",
      reports: "bg-slate-100 text-slate-800",
      admin: "bg-gray-100 text-gray-800",
    };
    return colors[module as keyof typeof colors] || "bg-gray-100 text-gray-800";
  };

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
      {/* Error Alert */}
      {submitError && (
        <Alert variant="destructive">
          <AlertDescription>{submitError}</AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="basic" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="basic" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Basic Info
          </TabsTrigger>
          <TabsTrigger value="permissions" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Permissions
          </TabsTrigger>
        </TabsList>

        <TabsContent value="basic" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Role Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Role Name */}
              <div className="space-y-2">
                <Label htmlFor="name">Role Name *</Label>
                <Input
                  id="name"
                  placeholder="e.g., content_manager"
                  {...register("name")}
                  className={errors.name ? "border-red-500" : ""}
                />
                {errors.name && (
                  <p className="text-sm text-red-500">{errors.name.message}</p>
                )}
                <p className="text-sm text-muted-foreground">
                  Use lowercase letters and underscores only. This will be used internally.
                </p>
              </div>

              {/* Display Name */}
              <div className="space-y-2">
                <Label htmlFor="display_name">Display Name *</Label>
                <Input
                  id="display_name"
                  placeholder="e.g., Content Manager"
                  {...register("display_name")}
                  className={errors.display_name ? "border-red-500" : ""}
                />
                {errors.display_name && (
                  <p className="text-sm text-red-500">{errors.display_name.message}</p>
                )}
              </div>

              {/* Description */}
              <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  placeholder="Describe the role's responsibilities..."
                  {...register("description")}
                  rows={3}
                />
              </div>

              {/* Hierarchy Level */}
              <div className="space-y-2">
                <Label htmlFor="hierarchy_level">Hierarchy Level</Label>
                <Input
                  id="hierarchy_level"
                  type="number"
                  min="0"
                  max="100"
                  {...register("hierarchy_level", { valueAsNumber: true })}
                  className={errors.hierarchy_level ? "border-red-500" : ""}
                />
                {errors.hierarchy_level && (
                  <p className="text-sm text-red-500">{errors.hierarchy_level.message}</p>
                )}
                <p className="text-sm text-muted-foreground">
                  Lower numbers have higher priority (0 = highest, 100 = lowest)
                </p>
              </div>

              {/* System Role */}
              <div className="flex items-center space-x-2">
                <Switch
                  id="is_system_role"
                  checked={watch("is_system_role")}
                  onCheckedChange={(checked) => setValue("is_system_role", checked)}
                />
                <Label htmlFor="is_system_role">System Role</Label>
              </div>
              <p className="text-sm text-muted-foreground">
                System roles are available across all tenants and cannot be deleted.
              </p>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="permissions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                Assign Permissions
                <Badge variant="secondary">
                  {selectedPermissions.length} selected
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {permissionsLoading ? (
                <div className="space-y-2">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="h-8 bg-muted rounded animate-pulse" />
                  ))}
                </div>
              ) : (
                <ScrollArea className="h-96">
                  <div className="space-y-4">
                    {Object.entries(groupedPermissions).map(([module, permissions]) => {
                      const modulePermissionIds = permissions.map(p => p.id);
                      const selectedCount = modulePermissionIds.filter(id => 
                        selectedPermissions.includes(id)
                      ).length;
                      const allSelected = selectedCount === modulePermissionIds.length;
                      const someSelected = selectedCount > 0 && selectedCount < modulePermissionIds.length;
                      const isExpanded = expandedModules.has(module);

                      return (
                        <div key={module} className="border rounded-lg p-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <Button
                                type="button"
                                variant="ghost"
                                size="sm"
                                className="h-6 w-6 p-0"
                                onClick={() => toggleModuleExpansion(module)}
                              >
                                {isExpanded ? (
                                  <ChevronDown className="h-4 w-4" />
                                ) : (
                                  <ChevronRight className="h-4 w-4" />
                                )}
                              </Button>
                              
                              <Checkbox
                                checked={allSelected}
                                ref={(el) => {
                                  if (el) el.indeterminate = someSelected;
                                }}
                                onCheckedChange={() => handleModuleToggle(module)}
                              />
                              
                              <div className="flex items-center gap-2">
                                <span className="font-medium">
                                  {module.charAt(0).toUpperCase() + module.slice(1)}
                                </span>
                                <Badge className={getModuleColor(module)}>
                                  {selectedCount}/{permissions.length}
                                </Badge>
                              </div>
                            </div>
                          </div>

                          {isExpanded && (
                            <div className="mt-3 ml-6 space-y-2">
                              {permissions.map((permission) => (
                                <div key={permission.id} className="flex items-center gap-3">
                                  <Checkbox
                                    checked={selectedPermissions.includes(permission.id)}
                                    onCheckedChange={() => handlePermissionToggle(permission.id)}
                                  />
                                  <div className="flex-1">
                                    <div className="text-sm font-medium">
                                      {permission.display_name}
                                    </div>
                                    <div className="text-xs text-muted-foreground">
                                      {permission.description}
                                    </div>
                                    <code className="text-xs bg-muted px-1 py-0.5 rounded">
                                      {permission.module}.{permission.action}
                                    </code>
                                  </div>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </ScrollArea>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Form Actions */}
      <div className="flex justify-end gap-3">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? "Saving..." : role ? "Update Role" : "Create Role"}
        </Button>
      </div>
    </form>
  );
}
