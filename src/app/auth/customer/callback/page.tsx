"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Loader2, CheckCircle, XCircle } from "lucide-react";
import { toast } from "sonner";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

/**
 * Customer OAuth Callback Page
 * 
 * Handles the OAuth callback from Google and exchanges the authorization code
 * for JWT tokens using our Customer OAuth system
 */
export default function CustomerOAuthCallbackPage() {
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');
  const [message, setMessage] = useState('Processing OAuth callback...');
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    const handleOAuthCallback = async () => {
      try {
        const code = searchParams.get('code');
        const state = searchParams.get('state');
        const error = searchParams.get('error');

        // Handle OAuth error from Google
        if (error) {
          const errorDescription = searchParams.get('error_description') || 'OAuth error';
          console.error('OAuth error from Google:', error, errorDescription);
          setStatus('error');
          setMessage(`OAuth error: ${errorDescription}`);
          return;
        }

        // Check for required parameters
        if (!code || !state) {
          console.error('Missing OAuth parameters:', { code: !!code, state: !!state });
          setStatus('error');
          setMessage('Missing OAuth parameters');
          return;
        }

        // Validate state parameter
        const storedState = sessionStorage.getItem('oauth_state');
        const storedTenantId = sessionStorage.getItem('oauth_tenant_id');
        const redirectPath = sessionStorage.getItem('oauth_redirect_path') || '/dashboard';

        if (!storedState || state !== storedState) {
          console.error('OAuth state mismatch:', { received: state, stored: storedState });
          setStatus('error');
          setMessage('Invalid OAuth state');
          return;
        }

        if (!storedTenantId) {
          console.error('Missing tenant ID in session storage');
          setStatus('error');
          setMessage('Missing tenant information');
          return;
        }

        console.log('🔄 Processing OAuth callback...');
        setMessage('Exchanging authorization code for tokens...');

        // Exchange authorization code for tokens
        const response = await fetch('/api/auth/customer/google/callback', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          },
          body: JSON.stringify({
            code,
            state,
            tenantId: parseInt(storedTenantId),
            clientType: 'web', // ← Fixed to use clientType (consistent with init endpoint)
            deviceId: `web-callback-${Date.now()}`
          })
        });

        console.log('📡 OAuth callback response status:', response.status);

        if (!response.ok) {
          const errorText = await response.text();
          console.error('❌ OAuth callback failed:', errorText);
          
          try {
            const errorData = JSON.parse(errorText);
            setStatus('error');
            setMessage(errorData.error || 'OAuth callback failed');
          } catch {
            setStatus('error');
            setMessage('OAuth callback failed');
          }
          return;
        }

        const data = await response.json();
        console.log('✅ OAuth callback successful:', data);

        if (data.success && data.tokens) {
          // Store tokens securely (you might want to use httpOnly cookies in production)
          localStorage.setItem('customer_access_token', data.tokens.accessToken);
          localStorage.setItem('customer_refresh_token', data.tokens.refreshToken);
          localStorage.setItem('customer_data', JSON.stringify(data.customer));

          // Clean up session storage
          sessionStorage.removeItem('oauth_state');
          sessionStorage.removeItem('oauth_tenant_id');
          sessionStorage.removeItem('oauth_redirect_path');

          setStatus('success');
          setMessage('Authentication successful! Redirecting...');

          // Show success message
          toast.success(`Welcome ${data.customer.displayName || data.customer.email}!`);

          // Redirect after a short delay
          setTimeout(() => {
            router.push(redirectPath);
          }, 2000);
        } else {
          console.error('❌ Invalid callback response:', data);
          setStatus('error');
          setMessage(data.error || 'Authentication failed');
        }

      } catch (error) {
        console.error('❌ OAuth callback error:', error);
        setStatus('error');
        setMessage('Failed to process OAuth callback');
      }
    };

    handleOAuthCallback();
  }, [searchParams, router]);

  const handleRetry = () => {
    // Clean up and redirect to login
    sessionStorage.removeItem('oauth_state');
    sessionStorage.removeItem('oauth_tenant_id');
    sessionStorage.removeItem('oauth_redirect_path');
    router.push('/auth/login');
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="flex items-center justify-center gap-2">
            {status === 'loading' && <Loader2 className="h-5 w-5 animate-spin" />}
            {status === 'success' && <CheckCircle className="h-5 w-5 text-green-500" />}
            {status === 'error' && <XCircle className="h-5 w-5 text-red-500" />}
            
            {status === 'loading' && 'Processing...'}
            {status === 'success' && 'Success!'}
            {status === 'error' && 'Error'}
          </CardTitle>
          <CardDescription>
            {message}
          </CardDescription>
        </CardHeader>
        
        {status === 'error' && (
          <CardContent>
            <Button 
              onClick={handleRetry}
              className="w-full"
              variant="outline"
            >
              Try Again
            </Button>
          </CardContent>
        )}
        
        {status === 'loading' && (
          <CardContent>
            <div className="flex justify-center">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          </CardContent>
        )}
      </Card>
    </div>
  );
}
