"use client";

import { useForm } from "@tanstack/react-form";
import { zodValidator } from "@tanstack/zod-form-adapter";
import { z } from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Upload, User as UserIcon, Loader2, AlertCircle, Mail, Shield } from "lucide-react";
import { User } from "@/lib/db/schema";
import { useCreateUser, useUpdateUser } from "@/lib/hooks/queries/use-user-queries";
import { ValidationService } from "@/lib/services/validation.service";
import { useState } from "react";

const userSchema = z.object({
  name: z.string()
    .min(1, "Name is required")
    .max(255, "Name too long"),
  email: z.string()
    .email("Invalid email format")
    .refine((email) => ValidationService.validateEmail(email).valid, {
      message: "Invalid email or disposable email not allowed",
    }),
  role: z.enum(["admin", "user"], {
    required_error: "Role is required",
  }),
  image: z.string().url().optional().or(z.literal("")),
  organizationId: z.number().optional(),
  tenantId: z.number().optional(),
});

type UserFormData = z.infer<typeof userSchema>;

interface UserFormProps {
  user?: User | null;
  organizationId?: number;
  tenantId?: number;
  onSuccess?: (user: User) => void;
  onCancel?: () => void;
}

export function UserForm({ 
  user, 
  organizationId,
  tenantId,
  onSuccess,
  onCancel 
}: UserFormProps) {
  const [isUploading, setIsUploading] = useState(false);
  
  const createMutation = useCreateUser();
  const updateMutation = useUpdateUser();

  const form = useForm({
    defaultValues: {
      name: user?.name || "",
      email: user?.email || "",
      role: (user?.role as "admin" | "user") || "user",
      image: user?.image || "",
      organizationId: user?.organizationId || organizationId,
      tenantId: user?.tenantId || tenantId,
    } as UserFormData,
    onSubmit: async ({ value }) => {
      try {
        let result;
        if (user) {
          result = await updateMutation.mutateAsync({ id: user.id, data: value });
        } else {
          result = await createMutation.mutateAsync(value);
        }
        onSuccess?.(result);
      } catch (error) {
        console.error("Form submission error:", error);
      }
    },
    validatorAdapter: zodValidator,
  });

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsUploading(true);
    try {
      const formData = new FormData();
      formData.append("file", file);
      formData.append("folder", "avatars");

      const response = await fetch("/api/upload", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Upload failed");
      }

      const result = await response.json();
      form.setFieldValue("image", result.file.url);
    } catch (error) {
      console.error("Error uploading image:", error);
    } finally {
      setIsUploading(false);
    }
  };

  const isSubmitting = createMutation.isPending || updateMutation.isPending;

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <UserIcon className="h-5 w-5" />
          {user ? "Edit User" : "Create User"}
        </CardTitle>
        <CardDescription>
          {user ? "Update user information" : "Add a new user to the system"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            form.handleSubmit();
          }}
          className="space-y-6"
        >
          {/* Avatar Upload   */}
          <div className="flex items-center gap-4">
            <Avatar className="h-20 w-20">
              <AvatarImage src={form.getFieldValue("image")} alt="User Avatar" />
              <AvatarFallback>
                <UserIcon className="h-8 w-8" />
              </AvatarFallback>
            </Avatar>
            <div>
              <label htmlFor="avatar-upload" className="cursor-pointer">
                <Button type="button" variant="outline" disabled={isUploading}>
                  {isUploading ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Upload className="h-4 w-4 mr-2" />
                  )}
                  Upload Avatar
                </Button>
              </label>
              <input
                id="avatar-upload"
                type="file"
                accept="image/*"
                className="hidden"
                onChange={handleImageUpload}
              />
              <p className="text-sm text-muted-foreground mt-1">
                PNG, JPG up to 2MB
              </p>
            </div>
          </div>

          {/* Name */}
          <form.Field
            name="name"
            validators={{
              onChange: userSchema.shape.name,
            }}
            children={(field) => (
              <div className="space-y-2">
                <Label htmlFor={field.name}>Full Name *</Label>
                <Input
                  id={field.name}
                  name={field.name}
                  value={field.state.value}
                  onBlur={field.handleBlur}
                  onChange={(e) => field.handleChange(e.target.value)}
                  placeholder="John Doe"
                />
                {field.state.meta.errors.length > 0 && (
                  <div className="flex items-center gap-2 text-sm text-destructive">
                    <AlertCircle className="h-4 w-4" />
                    {field.state.meta.errors[0]}
                  </div>
                )}
              </div>
            )}
          />

          {/* Email */}
          <form.Field
            name="email"
            validators={{
              onChangeAsyncDebounceMs: 500,
              onChangeAsync: async ({ value }) => {
                if (!value) return;
                const validation = ValidationService.validateEmail(value);
                if (!validation.valid) {
                  return validation.error;
                }
                
                // Check if email is already taken (skip for existing user)
                if (!user || user.email !== value) {
                  try {
                    const response = await fetch(`/api/users/check-email?email=${encodeURIComponent(value)}`);
                    if (response.ok) {
                      const data = await response.json();
                      if (!data.available) {
                        return "Email is already taken";
                      }
                    }
                  } catch (error) {
                    console.error("Error checking email:", error);
                  }
                }
                
                return undefined;
              },
            }}
            children={(field) => (
              <div className="space-y-2">
                <Label htmlFor={field.name} className="flex items-center gap-2">
                  <Mail className="h-4 w-4" />
                  Email Address *
                </Label>
                <Input
                  id={field.name}
                  name={field.name}
                  type="email"
                  value={field.state.value}
                  onBlur={field.handleBlur}
                  onChange={(e) => field.handleChange(e.target.value)}
                  placeholder="<EMAIL>"
                />
                {field.state.meta.isValidating && (
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    Validating email...
                  </div>
                )}
                {field.state.meta.errors.length > 0 && (
                  <div className="flex items-center gap-2 text-sm text-destructive">
                    <AlertCircle className="h-4 w-4" />
                    {field.state.meta.errors[0]}
                  </div>
                )}
              </div>
            )}
          />

          {/* Role */}
          <form.Field
            name="role"
            validators={{
              onChange: userSchema.shape.role,
            }}
            children={(field) => (
              <div className="space-y-2">
                <Label htmlFor={field.name} className="flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  Role *
                </Label>
                <Select
                  value={field.state.value}
                  onValueChange={field.handleChange}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="user">User</SelectItem>
                    <SelectItem value="admin">Admin</SelectItem>
                  </SelectContent>
                </Select>
                {field.state.meta.errors.length > 0 && (
                  <div className="flex items-center gap-2 text-sm text-destructive">
                    <AlertCircle className="h-4 w-4" />
                    {field.state.meta.errors[0]}
                  </div>
                )}
              </div>
            )}
          />

          {/* Organization ID (if applicable) */}
          {organizationId && (
            <form.Field
              name="organizationId"
              children={(field) => (
                <div className="space-y-2">
                  <Label htmlFor={field.name}>Organization ID</Label>
                  <Input
                    id={field.name}
                    name={field.name}
                    type="number"
                    value={field.state.value?.toString() || ""}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(parseInt(e.target.value) || undefined)}
                    placeholder="Organization ID"
                    disabled
                  />
                </div>
              )}
            />
          )}

          {/* Tenant ID (if applicable) */}
          {tenantId && (
            <form.Field
              name="tenantId"
              children={(field) => (
                <div className="space-y-2">
                  <Label htmlFor={field.name}>Tenant ID</Label>
                  <Input
                    id={field.name}
                    name={field.name}
                    type="number"
                    value={field.state.value?.toString() || ""}
                    onBlur={field.handleBlur}
                    onChange={(e) => field.handleChange(parseInt(e.target.value) || undefined)}
                    placeholder="Tenant ID"
                    disabled
                  />
                </div>
              )}
            />
          )}

          {/* Submit Buttons */}
          <div className="flex gap-2 pt-4">
            {onCancel && (
              <Button type="button" variant="outline" onClick={onCancel}>
                Cancel
              </Button>
            )}
            
            <form.Subscribe
              selector={(state) => [state.canSubmit, state.isSubmitting]}
              children={([canSubmit, isSubmitting]) => (
                <Button 
                  type="submit" 
                  className="flex-1" 
                  disabled={!canSubmit || isSubmitting}
                >
                  {isSubmitting ? (
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  ) : null}
                  {user ? "Update User" : "Create User"}
                </Button>
              )}
            />
          </div>

          {/* Form State Debug (Development only) */}
          {process.env.NODE_ENV === 'development' && (
            <form.Subscribe
              selector={(state) => [state.errors, state.isValid]}
              children={([errors, isValid]) => (
                <div className="mt-4 p-4 bg-muted rounded-lg">
                  <p className="text-sm font-medium">Form State (Dev)</p>
                  <p className="text-sm">Valid: {isValid ? "✅" : "❌"}</p>
                  {errors.length > 0 && (
                    <div className="text-sm text-destructive">
                      Errors: {errors.join(", ")}
                    </div>
                  )}
                </div>
              )}
            />
          )}
        </form>
      </CardContent>
    </Card>
  );
}
