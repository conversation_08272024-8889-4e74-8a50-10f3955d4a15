// DTO for Class
export interface ClassDTO {
  id: string;
  name: string;
  description: string;
  category: string; // Keep for backward compatibility (categoryId)
  categoryName?: string; // Add category name for user-friendly display
  startDate: string;
  endDate: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// Enhanced DTO with category details for public API
export interface PublicClassDTO {
  id: string;
  name: string;
  description: string;
  category: string; // Category name (user-friendly)
  categoryId: string; // Category ID for reference
  startDate: string;
  endDate: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}
