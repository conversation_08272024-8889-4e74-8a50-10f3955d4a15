import { NextRequest, NextResponse } from "next/server";
import { VoucherService } from "@/lib/services/voucher.service";

// Test endpoint to simulate frontend voucher operations
export async function POST(request: NextRequest) {
  try {
    console.log("🧪 Testing voucher frontend integration...");
    
    // Step 1: Simulate initial voucher list fetch (like useVouchersByTenant)
    console.log("\n1️⃣ Simulating initial voucher list fetch...");
    const initialVouchers = await VoucherService.getByTenantWithFilters(1, {
      search: undefined,
      type: undefined,
      is_active: undefined,
    });
    console.log(`Initial vouchers count: ${initialVouchers.length}`);

    // Step 2: Create a new voucher (like useCreateVoucher)
    console.log("\n2️⃣ Creating new voucher...");
    const voucherData = {
      tenantId: 1,
      code: `FRONTEND_${Date.now()}`,
      name: "Frontend Test Voucher",
      description: "Voucher created for frontend integration test",
      type: "percentage" as const,
      value: 20,
      currency: "USD",
      usage_limit: 50,
      usage_limit_per_customer: 1,
      valid_from: new Date(),
      valid_until: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      is_active: true,
      is_public: true,
      auto_apply: false,
      restrictions: {
        min_purchase_amount: 25,
        max_discount_amount: 50,
        first_time_customers_only: false,
        existing_customers_only: false,
      }
    };

    const createdVoucher = await VoucherService.create(voucherData);
    console.log("✅ Voucher created:", {
      id: createdVoucher.id,
      code: createdVoucher.code,
      name: createdVoucher.name
    });

    // Step 3: Simulate refetch after create (what should happen after cache invalidation)
    console.log("\n3️⃣ Simulating refetch after create...");
    const afterCreateVouchers = await VoucherService.getByTenantWithFilters(1, {
      search: undefined,
      type: undefined,
      is_active: undefined,
    });
    console.log(`Vouchers count after create: ${afterCreateVouchers.length}`);

    // Step 4: Test with filters (like frontend search/filter)
    console.log("\n4️⃣ Testing with filters...");
    const filteredVouchers = await VoucherService.getByTenantWithFilters(1, {
      search: "Frontend",
      type: undefined,
      is_active: true,
    });
    console.log(`Filtered vouchers count: ${filteredVouchers.length}`);

    // Step 5: Verify created voucher appears in all relevant queries
    const createdVoucherInInitial = initialVouchers.find(v => v.id === createdVoucher.id);
    const createdVoucherInAfter = afterCreateVouchers.find(v => v.id === createdVoucher.id);
    const createdVoucherInFiltered = filteredVouchers.find(v => v.id === createdVoucher.id);

    console.log("Voucher visibility:", {
      inInitialList: !!createdVoucherInInitial,
      inAfterCreateList: !!createdVoucherInAfter,
      inFilteredList: !!createdVoucherInFiltered
    });

    // Step 6: Test different filter combinations
    console.log("\n5️⃣ Testing different filter combinations...");
    const activeVouchers = await VoucherService.getByTenantWithFilters(1, {
      is_active: true
    });
    const percentageVouchers = await VoucherService.getByTenantWithFilters(1, {
      type: "percentage"
    });
    
    console.log("Filter results:", {
      activeCount: activeVouchers.length,
      percentageCount: percentageVouchers.length,
      createdVoucherInActive: !!activeVouchers.find(v => v.id === createdVoucher.id),
      createdVoucherInPercentage: !!percentageVouchers.find(v => v.id === createdVoucher.id)
    });

    return NextResponse.json({
      success: true,
      message: "Frontend integration test completed",
      data: {
        initialCount: initialVouchers.length,
        afterCreateCount: afterCreateVouchers.length,
        countDifference: afterCreateVouchers.length - initialVouchers.length,
        createdVoucher: {
          id: createdVoucher.id,
          code: createdVoucher.code,
          name: createdVoucher.name,
          tenantId: createdVoucher.tenantId
        },
        visibility: {
          inInitialList: !!createdVoucherInInitial,
          inAfterCreateList: !!createdVoucherInAfter,
          inFilteredList: !!createdVoucherInFiltered
        },
        filterTests: {
          activeCount: activeVouchers.length,
          percentageCount: percentageVouchers.length,
          createdVoucherInActive: !!activeVouchers.find(v => v.id === createdVoucher.id),
          createdVoucherInPercentage: !!percentageVouchers.find(v => v.id === createdVoucher.id)
        },
        analysis: {
          createWorking: afterCreateVouchers.length > initialVouchers.length,
          voucherPersisted: !!createdVoucherInAfter,
          filtersWorking: !!createdVoucherInFiltered,
          expectedBehavior: "Voucher should appear in all relevant lists after creation"
        }
      }
    });
  } catch (error) {
    console.error("❌ Frontend integration test failed:", error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}
