"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import { Package, Plus, Search, Filter, BarChart3 } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Package as PackageType } from "@/lib/db/schema";
import { PackageForm } from "@/components/forms/package-form";
import { PackageList } from "@/components/packages/package-list";
import { usePackageList, useCreatePackage, useUpdatePackage, PackageFilters } from "@/lib/hooks/queries/use-package-queries";
import {
  usePackagePurchaseOptionsByPackage,
  useCreatePackagePurchaseOptions,
  useUpdatePackagePurchaseOptions
} from "@/lib/hooks/queries/use-package-purchase-options-queries";
import {
  useIndividualCustomerTargeting,
  useSaveIndividualCustomerTargeting
} from "@/lib/hooks/queries/use-individual-customer-selection-queries";
import { useTenants } from "@/lib/hooks/queries/use-tenant-queries";
import { toast } from "sonner";
import { SuccessModal } from "@/components/ui/success-modal";
import { Confetti } from "@/components/ui/confetti";

export default function PackagesPage() {
  const [selectedTenantId, setSelectedTenantId] = useState<number | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [visibilityFilter, setVisibilityFilter] = useState<string>("all");
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [editingPackage, setEditingPackage] = useState<PackageType | null>(null);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [showConfetti, setShowConfetti] = useState(false);

  // Fetch data
  const { data: tenants = [], isLoading: tenantsLoading } = useTenants();

  // Ensure tenants is always an array
  const safetenants = Array.isArray(tenants) ? tenants : [];
  
  // Build filters
  const filters: PackageFilters = {
    search: searchQuery || undefined,
    isActive: statusFilter === "active" ? true : statusFilter === "inactive" ? false : undefined,
    is_private: visibilityFilter === "private" ? true : visibilityFilter === "public" ? false : undefined,
  };

  const { data: packages = [], isLoading } = usePackageList(filters, selectedTenantId || (safetenants[0]?.id ?? 1));
  
  // Mutations
  const createPackageMutation = useCreatePackage();
  const updatePackageMutation = useUpdatePackage();
  const createPurchaseOptionsMutation = useCreatePackagePurchaseOptions();
  const updatePurchaseOptionsMutation = useUpdatePackagePurchaseOptions();
  const saveIndividualCustomerTargetingMutation = useSaveIndividualCustomerTargeting();

  // Get existing purchase options for editing package
  const { data: existingPurchaseOptions } = usePackagePurchaseOptionsByPackage(
    editingPackage?.id || "",
    { enabled: !!editingPackage?.id }
  );

  // Get existing individual customer targeting for editing package
  const { data: existingIndividualTargeting = [] } = useIndividualCustomerTargeting(
    editingPackage?.id || "",
    editingPackage?.tenantId || selectedTenantId || (safetenants[0]?.id ?? 1),
    { enabled: !!editingPackage?.id }
  );

  // Calculate stats by tenant
  const statsByTenant = safetenants.reduce((acc, tenant) => {
    const tenantPackages = packages.filter(p => p.tenantId === tenant.id);
    acc[tenant.id] = {
      total: tenantPackages.length,
      active: tenantPackages.filter(p => p.isActive).length,
      private: tenantPackages.filter(p => p.is_private).length,
    };
    return acc;
  }, {} as Record<number, { total: number; active: number; private: number }>);

  // Overall stats
  const totalStats = {
    total: packages.length,
    active: packages.filter(p => p.isActive).length,
    inactive: packages.filter(p => !p.isActive).length,
    private: packages.filter(p => p.is_private).length,
    public: packages.filter(p => !p.is_private).length,
  };

  const handleCreatePackage = async (data: any) => {
    try {
      // Extract purchase options from form data
      const { purchaseOptions, ...packageData } = data;

      // Create the package first
      const createdPackage = await createPackageMutation.mutateAsync(packageData);

      // Create purchase options if provided
      if (purchaseOptions && createdPackage) {
        const purchaseOptionsData = {
          packageId: createdPackage.id,
          purchaseLimit: purchaseOptions.purchaseLimit,
          restrictTo: purchaseOptions.restrictTo,
          transferable: purchaseOptions.transferable,
          specifySoldAtLocation: purchaseOptions.specifySoldAtLocation,
          soldAtLocationId: purchaseOptions.soldAtLocationId === "all_locations" ? undefined : purchaseOptions.soldAtLocationId || undefined,
          classBookingLimit: purchaseOptions.classBookingLimit,
          showOnline: purchaseOptions.showOnline,
        };

        await createPurchaseOptionsMutation.mutateAsync(purchaseOptionsData);

        // Save individual customer targeting if provided
        if (purchaseOptions.individualCustomerIds && purchaseOptions.individualCustomerIds.length > 0) {
          await saveIndividualCustomerTargetingMutation.mutateAsync({
            packageId: createdPackage.id,
            selectedCustomerIds: purchaseOptions.individualCustomerIds,
            tenantId: createdPackage.tenantId,
          });
        }
      }

      setShowCreateForm(false);

      // Show success animation
      setSuccessMessage("Package created successfully!");
      setShowSuccessModal(true);
      setShowConfetti(true);

      // Hide confetti after 3 seconds
      setTimeout(() => setShowConfetti(false), 3000);

      toast.success("Package created successfully!");
    } catch (error) {
      console.error("Failed to create package:", error);
      toast.error("Failed to create package");
    }
  };

  const handleUpdatePackage = async (data: any) => {
    if (!editingPackage) return;

    try {
      // Extract purchase options from form data
      const { purchaseOptions, ...packageData } = data;

      // Update the package
      await updatePackageMutation.mutateAsync({ id: editingPackage.id, ...packageData });

      // Update purchase options if provided
      if (purchaseOptions) {
        const purchaseOptionsData = {
          purchaseLimit: purchaseOptions.purchaseLimit,
          restrictTo: purchaseOptions.restrictTo,
          transferable: purchaseOptions.transferable,
          specifySoldAtLocation: purchaseOptions.specifySoldAtLocation,
          soldAtLocationId: purchaseOptions.soldAtLocationId === "all_locations" ? undefined : purchaseOptions.soldAtLocationId || undefined,
          classBookingLimit: purchaseOptions.classBookingLimit,
          showOnline: purchaseOptions.showOnline,
        };

        await updatePurchaseOptionsMutation.mutateAsync({
          packageId: editingPackage.id,
          data: purchaseOptionsData
        });

        // Update individual customer targeting
        await saveIndividualCustomerTargetingMutation.mutateAsync({
          packageId: editingPackage.id,
          selectedCustomerIds: purchaseOptions.individualCustomerIds || [],
          tenantId: editingPackage.tenantId,
        });
      }

      setEditingPackage(null);

      // Show success animation
      setSuccessMessage("Package updated successfully!");
      setShowSuccessModal(true);
      setShowConfetti(true);

      // Hide confetti after 3 seconds
      setTimeout(() => setShowConfetti(false), 3000);

      toast.success("Package updated successfully!");
    } catch (error) {
      console.error("Failed to update package:", error);
      toast.error("Failed to update package");
    }
  };

  const handleDuplicatePackage = (pkg: PackageType) => {
    setEditingPackage({
      ...pkg,
      id: "", // Clear ID for duplication
      name: `${pkg.name} (Copy)`,
    } as PackageType);
  };

  const clearFilters = () => {
    setSearchQuery("");
    setStatusFilter("all");
    setVisibilityFilter("all");
  };

  const hasActiveFilters = searchQuery || statusFilter !== "all" || visibilityFilter !== "all";

  // Show loading if tenants are still loading
  if (tenantsLoading) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Package Management</h1>
          <p className="text-muted-foreground">
            Create and manage packages for your business
          </p>
        </div>
        <Button onClick={() => setShowCreateForm(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Create Package
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Packages</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalStats.total}</div>
            <p className="text-xs text-muted-foreground">
              {totalStats.active} active, {totalStats.inactive} inactive
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Packages</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalStats.active}</div>
            <p className="text-xs text-muted-foreground">
              Available for purchase
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Public Packages</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalStats.public}</div>
            <p className="text-xs text-muted-foreground">
              Visible to all customers
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Private Packages</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalStats.private}</div>
            <p className="text-xs text-muted-foreground">
              Restricted visibility
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filters
          </CardTitle>
          <CardDescription>
            Filter packages by tenant, status, and visibility
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
            {/* Tenant Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Tenant</label>
              <Select
                value={selectedTenantId?.toString() || "all"}
                onValueChange={(value) => setSelectedTenantId(value === "all" ? null : parseInt(value))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All tenants" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All tenants</SelectItem>
                  {safetenants.map((tenant) => (
                    <SelectItem key={tenant.id} value={tenant.id.toString()}>
                      {tenant.name}
                      {statsByTenant[tenant.id] && (
                        <Badge variant="secondary" className="ml-2">
                          {statsByTenant[tenant.id].total}
                        </Badge>
                      )}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Search */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Search</label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search packages..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            {/* Status Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Status</label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Visibility Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Visibility</label>
              <Select value={visibilityFilter} onValueChange={setVisibilityFilter}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Visibility</SelectItem>
                  <SelectItem value="public">Public</SelectItem>
                  <SelectItem value="private">Private</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Clear Filters */}
            <div className="space-y-2">
              <label className="text-sm font-medium">&nbsp;</label>
              <Button
                variant="outline"
                onClick={clearFilters}
                disabled={!hasActiveFilters}
                className="w-full"
              >
                Clear Filters
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Package List */}
      <Card>
        <CardHeader>
          <CardTitle>Packages</CardTitle>
          <CardDescription>
            {isLoading ? "Loading packages..." : `${packages.length} packages found`}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          ) : (
            <PackageList
              packages={packages}
              onEdit={setEditingPackage}
              onDuplicate={handleDuplicatePackage}
            />
          )}
        </CardContent>
      </Card>

      {/* Create Package Dialog */}
      <Dialog open={showCreateForm} onOpenChange={setShowCreateForm}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Create New Package</DialogTitle>
          </DialogHeader>
          <PackageForm
            onSubmit={handleCreatePackage}
            onCancel={() => setShowCreateForm(false)}
            tenantId={selectedTenantId || (safetenants[0]?.id ?? 1)}
          />
        </DialogContent>
      </Dialog>

      {/* Edit Package Dialog */}
      <Dialog open={!!editingPackage} onOpenChange={(open) => !open && setEditingPackage(null)}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editingPackage?.id ? "Edit Package" : "Duplicate Package"}
            </DialogTitle>
          </DialogHeader>
          {editingPackage && (
            <PackageForm
              entity={editingPackage}
              onSubmit={editingPackage.id ? handleUpdatePackage : handleCreatePackage}
              onCancel={() => setEditingPackage(null)}
              tenantId={editingPackage.tenantId}
              existingPurchaseOptions={existingPurchaseOptions}
              existingIndividualTargeting={existingIndividualTargeting}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Success Modal */}
      <SuccessModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        title="Success!"
        message={successMessage}
        actionLabel="Continue"
        onAction={() => setShowSuccessModal(false)}
      />

      {/* Confetti Animation */}
      {showConfetti && <Confetti />}
    </div>
  );
}
