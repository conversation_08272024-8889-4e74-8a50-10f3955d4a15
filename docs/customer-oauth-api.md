# Customer Google OAuth API Documentation

## Overview

Comprehensive Google OAuth 2.0 authentication system for customers with JWT Bearer token support, PKCE for mobile security, and FAANG-level standards.

## Base URL

```
Production: https://api.yourdomain.com
Staging: https://staging-api.yourdomain.com
Development: http://localhost:3000
```

## Authentication Flow

### Web Application Flow

1. **Initialize OAuth** → Get authorization URL
2. **User Authorization** → Redirect to Google
3. **Handle Callback** → Exchange code for tokens
4. **Use Access Token** → Make authenticated requests
5. **Refresh Token** → Get new access token when expired

### Mobile Application Flow (PKCE)

1. **Generate PKCE** → Create code verifier and challenge
2. **Initialize OAuth** → Get authorization URL with PKCE
3. **User Authorization** → Open browser/webview
4. **Handle Callback** → Exchange code + verifier for tokens
5. **Use Access Token** → Make authenticated requests
6. **Refresh Token** → Get new access token when expired

## API Endpoints

### 1. Initialize OAuth Flow

**POST** `/api/auth/customer/google/init`

Initialize Google OAuth flow for both web and mobile clients.

#### Request Body

```json
{
  "tenantId": 1,
  "clientType": "web", // "web" | "mobile"
  "redirectUri": "https://yourapp.com/auth/callback", // Optional
  "deviceId": "device-123" // Optional, recommended for mobile
}
```

#### Response

```json
{
  "success": true,
  "authUrl": "https://accounts.google.com/oauth/authorize?...",
  "state": "secure-random-state",
  "clientType": "web",
  "expiresIn": 600,
  // Mobile only:
  "codeChallenge": "base64url-encoded-challenge"
}
```

#### Error Response

```json
{
  "success": false,
  "error": {
    "code": "INVALID_INPUT",
    "message": "Invalid request data",
    "timestamp": "2024-01-15T10:30:00Z",
    "requestId": "req_123456"
  }
}
```

### 2. Handle OAuth Callback

**POST** `/api/auth/customer/google/callback`

Process OAuth authorization code and return JWT tokens.

#### Request Body

```json
{
  "code": "authorization-code-from-google",
  "state": "state-from-init-response",
  "tenantId": 1,
  "deviceType": "web", // "web" | "mobile" | "tablet"
  "deviceId": "device-123", // Optional
  // Mobile PKCE only:
  "codeVerifier": "base64url-encoded-verifier"
}
```

#### Response

```json
{
  "success": true,
  "customer": {
    "id": "cust_123456",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "displayName": "John Doe",
    "image": "https://lh3.googleusercontent.com/...",
    "tenantId": 1,
    "isEmailVerified": true,
    "membershipType": "basic"
  },
  "tokens": {
    "accessToken": "eyJhbGciOiJIUzI1NiIs...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
    "tokenType": "Bearer",
    "expiresIn": 3600
  },
  "isNewCustomer": false
}
```

### 3. Login with Email/Password

**POST** `/api/auth/customer/login`

Authenticate customer with email and password, returns JWT tokens.

#### Request Body

```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "tenantId": 1,
  "deviceType": "web",
  "deviceId": "device-123", // Optional
  "rememberMe": false
}
```

#### Response

```json
{
  "success": true,
  "customer": {
    "id": "cust_123456",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "displayName": "John Doe",
    "tenantId": 1,
    "isEmailVerified": true,
    "membershipType": "premium"
  },
  "tokens": {
    "accessToken": "eyJhbGciOiJIUzI1NiIs...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
    "tokenType": "Bearer",
    "expiresIn": 3600
  },
  "requiresEmailVerification": false
}
```

### 4. Refresh Access Token

**POST** `/api/auth/customer/refresh`

Use refresh token to get new access token.

#### Request Body

```json
{
  "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
  "deviceType": "web", // Optional
  "deviceId": "device-123" // Optional
}
```

#### Response

```json
{
  "success": true,
  "tokens": {
    "accessToken": "eyJhbGciOiJIUzI1NiIs...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIs...",
    "tokenType": "Bearer",
    "expiresIn": 3600
  }
}
```

### 5. Get Customer Profile

**GET** `/api/auth/customer/profile`

Get authenticated customer's profile information.

#### Headers

```
Authorization: Bearer eyJhbGciOiJIUzI1NiIs...
X-Tenant-ID: 1
```

#### Response

```json
{
  "success": true,
  "customer": {
    "id": "cust_123456",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "displayName": "John Doe",
    "image": "https://lh3.googleusercontent.com/...",
    "dateOfBirth": "1990-01-15",
    "gender": "male",
    "mobileCountryCode": "+1",
    "mobileNumber": "5551234567",
    "membershipType": "premium",
    "membershipExpiry": "2024-12-31T23:59:59Z",
    "isEmailVerified": true,
    "preferences": {
      "language": "en",
      "timezone": "America/New_York",
      "notifications": {
        "email": true,
        "sms": false,
        "push": true
      }
    },
    "marketingEmails": true,
    "lastLoginAt": "2024-01-15T10:30:00Z",
    "createdAt": "2023-01-15T10:30:00Z"
  }
}
```

### 6. Update Customer Profile

**PUT** `/api/auth/customer/profile`

Update authenticated customer's profile information.

#### Headers

```
Authorization: Bearer eyJhbGciOiJIUzI1NiIs...
X-Tenant-ID: 1
Content-Type: application/json
```

#### Request Body

```json
{
  "firstName": "John",
  "lastName": "Smith",
  "displayName": "John Smith",
  "dateOfBirth": "1990-01-15",
  "gender": "male",
  "mobileCountryCode": "+1",
  "mobileNumber": "5551234567",
  "preferences": {
    "language": "en",
    "timezone": "America/New_York",
    "notifications": {
      "email": true,
      "sms": false,
      "push": true
    },
    "privacy": {
      "shareDataWithPartners": false,
      "allowAnalytics": true
    }
  },
  "marketingEmails": false
}
```

### 7. Logout

**POST** `/api/auth/customer/logout`

Logout customer and revoke session.

#### Headers

```
Authorization: Bearer eyJhbGciOiJIUzI1NiIs...
X-Tenant-ID: 1
```

#### Request Body

```json
{
  "logoutAll": false, // Logout from all devices
  "reason": "logout"
}
```

#### Response

```json
{
  "success": true,
  "message": "Logged out successfully",
  "loggedOutAll": false
}
```

## Error Codes

| Code | HTTP Status | Description |
|------|-------------|-------------|
| `INVALID_CREDENTIALS` | 401 | Invalid email or password |
| `ACCOUNT_LOCKED` | 403 | Account temporarily locked |
| `EMAIL_NOT_VERIFIED` | 403 | Email verification required |
| `TOKEN_INVALID` | 401 | Invalid authentication token |
| `TOKEN_EXPIRED` | 401 | Authentication token expired |
| `REFRESH_TOKEN_INVALID` | 401 | Invalid refresh token |
| `OAUTH_ERROR` | 400 | OAuth authentication failed |
| `OAUTH_STATE_MISMATCH` | 400 | OAuth state mismatch (CSRF) |
| `RATE_LIMIT_EXCEEDED` | 429 | Too many requests |
| `INVALID_INPUT` | 400 | Invalid request data |
| `TENANT_MISMATCH` | 403 | Tenant context mismatch |
| `INTERNAL_ERROR` | 500 | Internal server error |

## Rate Limits

| Endpoint | Limit | Window |
|----------|-------|--------|
| Login | 5 requests | 15 minutes |
| OAuth | 10 requests | 5 minutes |
| Refresh | 10 requests | 1 minute |
| Profile | 30 requests | 1 minute |
| General API | 100 requests | 1 minute |

## Security Headers

All responses include security headers:

```
X-Content-Type-Options: nosniff
X-Frame-Options: DENY
X-XSS-Protection: 1; mode=block
Referrer-Policy: strict-origin-when-cross-origin
Content-Security-Policy: default-src 'none'; frame-ancestors 'none';
```

## CORS Configuration

- **Allowed Origins**: Configured frontend URLs
- **Allowed Methods**: GET, POST, PUT, DELETE, OPTIONS
- **Allowed Headers**: Content-Type, Authorization, X-Tenant-ID
- **Credentials**: Supported
- **Max Age**: 24 hours

## JWT Token Structure

### Access Token Payload

```json
{
  "sub": "cust_123456",
  "tenantId": 1,
  "email": "<EMAIL>",
  "firstName": "John",
  "lastName": "Doe",
  "displayName": "John Doe",
  "image": "https://lh3.googleusercontent.com/...",
  "membershipType": "premium",
  "isEmailVerified": true,
  "jti": "token-id",
  "iat": 1642248600,
  "exp": 1642252200,
  "iss": "saas-app",
  "aud": "customer",
  "sessionId": "session-id"
}
```

### Token Expiry

- **Access Token**: 1 hour
- **Refresh Token**: 30 days
- **OAuth State**: 10 minutes
- **PKCE Challenge**: 10 minutes

## Integration Examples

### Frontend (React/Next.js)

#### OAuth Login Component

```typescript
import { useState } from 'react';

interface OAuthResponse {
  success: boolean;
  authUrl?: string;
  state?: string;
  error?: any;
}

export function GoogleOAuthLogin({ tenantId }: { tenantId: number }) {
  const [loading, setLoading] = useState(false);

  const handleGoogleLogin = async () => {
    setLoading(true);

    try {
      // Initialize OAuth flow
      const initResponse = await fetch('/api/auth/customer/google/init', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          tenantId,
          clientType: 'web',
          redirectUri: `${window.location.origin}/auth/callback`
        })
      });

      const data: OAuthResponse = await initResponse.json();

      if (data.success && data.authUrl) {
        // Store state for callback validation
        sessionStorage.setItem('oauth_state', data.state!);

        // Redirect to Google OAuth
        window.location.href = data.authUrl;
      } else {
        console.error('OAuth initialization failed:', data.error);
      }
    } catch (error) {
      console.error('OAuth error:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <button
      onClick={handleGoogleLogin}
      disabled={loading}
      className="google-oauth-button"
    >
      {loading ? 'Connecting...' : 'Continue with Google'}
    </button>
  );
}
```

#### OAuth Callback Handler

```typescript
// pages/auth/callback.tsx or app/auth/callback/page.tsx
import { useEffect, useState } from 'react';
import { useRouter } from 'next/router';

export default function OAuthCallback() {
  const router = useRouter();
  const [status, setStatus] = useState<'loading' | 'success' | 'error'>('loading');

  useEffect(() => {
    const handleCallback = async () => {
      const { code, state } = router.query;
      const storedState = sessionStorage.getItem('oauth_state');

      if (!code || !state || state !== storedState) {
        setStatus('error');
        return;
      }

      try {
        const response = await fetch('/api/auth/customer/google/callback', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            code,
            state,
            tenantId: 1, // Get from context
            deviceType: 'web'
          })
        });

        const data = await response.json();

        if (data.success) {
          // Store tokens securely
          localStorage.setItem('access_token', data.tokens.accessToken);
          localStorage.setItem('refresh_token', data.tokens.refreshToken);

          // Clean up
          sessionStorage.removeItem('oauth_state');

          setStatus('success');

          // Redirect to dashboard
          router.push('/dashboard');
        } else {
          setStatus('error');
        }
      } catch (error) {
        console.error('Callback error:', error);
        setStatus('error');
      }
    };

    if (router.isReady) {
      handleCallback();
    }
  }, [router.isReady, router.query]);

  if (status === 'loading') return <div>Processing login...</div>;
  if (status === 'error') return <div>Login failed. Please try again.</div>;
  return <div>Login successful! Redirecting...</div>;
}
```

### Mobile (React Native/Flutter)

#### React Native Example

```typescript
import { useState } from 'react';
import { Linking } from 'react-native';
import { WebBrowser } from 'expo-web-browser';
import * as Crypto from 'expo-crypto';

export function GoogleOAuthLogin({ tenantId }: { tenantId: number }) {
  const [loading, setLoading] = useState(false);

  // Generate PKCE challenge
  const generatePKCE = async () => {
    const codeVerifier = Crypto.randomUUID() + Crypto.randomUUID();
    const codeChallenge = await Crypto.digestStringAsync(
      Crypto.CryptoDigestAlgorithm.SHA256,
      codeVerifier,
      { encoding: Crypto.CryptoEncoding.BASE64URL }
    );

    return { codeVerifier, codeChallenge };
  };

  const handleGoogleLogin = async () => {
    setLoading(true);

    try {
      const { codeVerifier } = await generatePKCE();

      // Initialize OAuth flow
      const initResponse = await fetch('/api/auth/customer/google/init', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          tenantId,
          clientType: 'mobile',
          deviceId: 'device-123' // Generate unique device ID
        })
      });

      const data = await initResponse.json();

      if (data.success && data.authUrl) {
        // Open OAuth URL in browser
        const result = await WebBrowser.openAuthSessionAsync(
          data.authUrl,
          'yourapp://oauth/callback'
        );

        if (result.type === 'success' && result.url) {
          const url = new URL(result.url);
          const code = url.searchParams.get('code');
          const state = url.searchParams.get('state');

          if (code && state) {
            await handleOAuthCallback(code, state, codeVerifier, tenantId);
          }
        }
      }
    } catch (error) {
      console.error('OAuth error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleOAuthCallback = async (
    code: string,
    state: string,
    codeVerifier: string,
    tenantId: number
  ) => {
    try {
      const response = await fetch('/api/auth/customer/google/callback', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          code,
          state,
          codeVerifier,
          tenantId,
          deviceType: 'mobile',
          deviceId: 'device-123'
        })
      });

      const data = await response.json();

      if (data.success) {
        // Store tokens securely (use Keychain/Keystore)
        await SecureStore.setItemAsync('access_token', data.tokens.accessToken);
        await SecureStore.setItemAsync('refresh_token', data.tokens.refreshToken);

        // Navigate to main app
        navigation.navigate('Dashboard');
      }
    } catch (error) {
      console.error('Callback error:', error);
    }
  };

  return (
    <TouchableOpacity onPress={handleGoogleLogin} disabled={loading}>
      <Text>{loading ? 'Connecting...' : 'Continue with Google'}</Text>
    </TouchableOpacity>
  );
}
```

### Token Management

#### Automatic Token Refresh

```typescript
class AuthService {
  private accessToken: string | null = null;
  private refreshToken: string | null = null;

  async makeAuthenticatedRequest(url: string, options: RequestInit = {}) {
    // Add access token to request
    const headers = {
      ...options.headers,
      'Authorization': `Bearer ${this.accessToken}`,
      'X-Tenant-ID': '1'
    };

    let response = await fetch(url, { ...options, headers });

    // If token expired, try to refresh
    if (response.status === 401) {
      const refreshed = await this.refreshAccessToken();

      if (refreshed) {
        // Retry request with new token
        headers['Authorization'] = `Bearer ${this.accessToken}`;
        response = await fetch(url, { ...options, headers });
      } else {
        // Refresh failed, redirect to login
        this.logout();
        throw new Error('Authentication failed');
      }
    }

    return response;
  }

  async refreshAccessToken(): Promise<boolean> {
    if (!this.refreshToken) return false;

    try {
      const response = await fetch('/api/auth/customer/refresh', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          refreshToken: this.refreshToken,
          deviceType: 'web'
        })
      });

      const data = await response.json();

      if (data.success) {
        this.accessToken = data.tokens.accessToken;
        this.refreshToken = data.tokens.refreshToken;

        // Update stored tokens
        localStorage.setItem('access_token', this.accessToken);
        localStorage.setItem('refresh_token', this.refreshToken);

        return true;
      }
    } catch (error) {
      console.error('Token refresh failed:', error);
    }

    return false;
  }

  logout() {
    this.accessToken = null;
    this.refreshToken = null;
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');

    // Redirect to login
    window.location.href = '/login';
  }
}
```
