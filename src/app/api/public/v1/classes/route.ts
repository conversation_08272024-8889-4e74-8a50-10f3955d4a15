import { NextRequest, NextResponse } from 'next/server';
import { getClassByIdQueryHandler } from '@/modules/class/application/queries/getClassByIdQueryHandler';
import { withCORS } from '@/lib/security/cors.config';
import { logger } from '@/lib/logger';
import { validateApiKey } from '@/lib/security/apiKeyAuth';
import { getAllClassesQueryHandler } from '@/modules/class/application/queries/getAllClassesQueryHandler';

// GET /api/public/v1/classes?tenantId=...&limit=...&page=...
export const GET = withCORS(async (req: NextRequest) => {
  try {
    // API Key Auth (same as package-pricing)
    const apiKey = req.headers.get('x-api-key');
    if (!validateApiKey(apiKey)) {
      return NextResponse.json({ success: false, error: 'Unauthorized' }, { status: 401 });
    }

    // Query params
    const { searchParams } = new URL(req.url);
    const tenantId = searchParams.get('tenantId');
    const limit = parseInt(searchParams.get('limit') || '20', 10);
    const page = parseInt(searchParams.get('page') || '1', 10);
    const id = searchParams.get('id');

    if (id) {
      // Get by ID
      const result = await getClassByIdQueryHandler({ id, tenantId });
      if (!result) {
        return NextResponse.json({ success: false, error: 'Class not found' }, { status: 404 });
      }
      return NextResponse.json({ success: true, data: result });
    }

    // Get all with pagination
    const { classes, meta } = await getAllClassesQueryHandler({ tenantId, limit, page });
    return NextResponse.json({ success: true, data: { classes }, meta });
  } catch (err: any) {
    logger.error('Error in GET /api/public/v1/classes', { error: err.message, stack: err.stack });
    return NextResponse.json({ success: false, error: 'Internal Server Error' }, { status: 500 });
  }
});

// Handle CORS preflight requests
export const OPTIONS = withCORS(async (req: NextRequest) => {
  return new NextResponse(null, { status: 200 });
});

// Only GET is public for now
export const POST = undefined;
export const PUT = undefined;
export const DELETE = undefined;
