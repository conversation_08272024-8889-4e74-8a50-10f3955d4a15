<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test CORS - Class Schedules API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>Test CORS - Class Schedules API v1</h1>
    
    <div class="test-section">
        <h3>API Configuration</h3>
        <p>Base URL: <span id="baseUrl">http://localhost:3000</span></p>
        <p>API Key: <code>pk_9b4d8a5f3e9ec298be21ac0589d0346d0289f717d9ca3841093498ef4530c64b</code></p>
        <p>Tenant ID: <code>1</code></p>
    </div>

    <div class="test-section">
        <h3>CORS Tests</h3>
        <button onclick="testPreflight()">Test OPTIONS (Preflight)</button>
        <button onclick="testGetRequest()">Test GET Request</button>
        <button onclick="testWithoutApiKey()">Test Without API Key</button>
        <button onclick="testInvalidApiKey()">Test Invalid API Key</button>
    </div>

    <div class="test-section">
        <h3>Results</h3>
        <div id="results"></div>
    </div>

    <script>
        const baseUrl = 'http://localhost:3000';
        const apiKey = 'pk_9b4d8a5f3e9ec298be21ac0589d0346d0289f717d9ca3841093498ef4530c64b';
        const tenantId = '1';
        
        function addResult(title, success, data) {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = success ? 'success' : 'error';
            div.style.margin = '10px 0';
            div.style.padding = '10px';
            div.style.borderRadius = '5px';
            
            div.innerHTML = `
                <h4>${title}</h4>
                <pre>${JSON.stringify(data, null, 2)}</pre>
            `;
            
            results.appendChild(div);
        }

        async function testPreflight() {
            try {
                console.log('🔍 Testing OPTIONS preflight...');
                
                const response = await fetch(`${baseUrl}/api/public/v1/class-schedules?tenantId=${tenantId}`, {
                    method: 'OPTIONS',
                    headers: {
                        'Origin': window.location.origin,
                        'Access-Control-Request-Method': 'GET',
                        'Access-Control-Request-Headers': 'x-api-key, Content-Type'
                    }
                });

                const headers = {};
                response.headers.forEach((value, key) => {
                    if (key.toLowerCase().startsWith('access-control')) {
                        headers[key] = value;
                    }
                });

                addResult('OPTIONS Preflight Test', response.ok, {
                    status: response.status,
                    statusText: response.statusText,
                    corsHeaders: headers
                });

            } catch (error) {
                addResult('OPTIONS Preflight Test', false, {
                    error: error.message,
                    type: error.name
                });
            }
        }

        async function testGetRequest() {
            try {
                console.log('🔍 Testing GET request...');
                
                const response = await fetch(`${baseUrl}/api/public/v1/class-schedules?tenantId=${tenantId}&limit=5`, {
                    method: 'GET',
                    headers: {
                        'x-api-key': apiKey,
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();
                
                const headers = {};
                response.headers.forEach((value, key) => {
                    if (key.toLowerCase().startsWith('access-control')) {
                        headers[key] = value;
                    }
                });

                addResult('GET Request Test', response.ok, {
                    status: response.status,
                    statusText: response.statusText,
                    corsHeaders: headers,
                    data: data
                });

            } catch (error) {
                addResult('GET Request Test', false, {
                    error: error.message,
                    type: error.name
                });
            }
        }

        async function testWithoutApiKey() {
            try {
                console.log('🔍 Testing without API key...');
                
                const response = await fetch(`${baseUrl}/api/public/v1/class-schedules?tenantId=${tenantId}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();

                addResult('Without API Key Test', !response.ok && response.status === 401, {
                    status: response.status,
                    statusText: response.statusText,
                    data: data
                });

            } catch (error) {
                addResult('Without API Key Test', false, {
                    error: error.message,
                    type: error.name
                });
            }
        }

        async function testInvalidApiKey() {
            try {
                console.log('🔍 Testing with invalid API key...');
                
                const response = await fetch(`${baseUrl}/api/public/v1/class-schedules?tenantId=${tenantId}`, {
                    method: 'GET',
                    headers: {
                        'x-api-key': 'invalid-key',
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();

                addResult('Invalid API Key Test', !response.ok && response.status === 401, {
                    status: response.status,
                    statusText: response.statusText,
                    data: data
                });

            } catch (error) {
                addResult('Invalid API Key Test', false, {
                    error: error.message,
                    type: error.name
                });
            }
        }

        // Auto-run tests on page load
        window.addEventListener('load', () => {
            console.log('🚀 Starting CORS tests...');
            setTimeout(() => {
                testPreflight();
                setTimeout(() => testGetRequest(), 1000);
                setTimeout(() => testWithoutApiKey(), 2000);
                setTimeout(() => testInvalidApiKey(), 3000);
            }, 500);
        });
    </script>
</body>
</html>