import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { AddressService } from "@/lib/services/address.service";

// GET /api/addresses/stats - Get address statistics
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get("tenantId");

    if (!tenantId) {
      return NextResponse.json(
        { error: "Tenant ID is required" },
        { status: 400 }
      );
    }

    const tenantIdNum = parseInt(tenantId);
    if (isNaN(tenantIdNum)) {
      return NextResponse.json(
        { error: "Invalid tenant ID" },
        { status: 400 }
      );
    }

    // Check permissions
    if (session.user.role !== "admin") {
      // Add tenant ownership check here
      // const userTenant = await getUserTenant(session.user.id);
      // if (userTenant?.id !== tenantIdNum) {
      //   return NextResponse.json({ error: "Forbidden" }, { status: 403 });
      // }
    }

    const addresses = await AddressService.getByTenantId(tenantIdNum);
    
    const stats = {
      totalAddresses: addresses.length,
      acceptedAddresses: addresses.filter(addr => addr.acceptedAt).length,
      pendingAddresses: addresses.filter(addr => !addr.acceptedAt).length,
    };

    return NextResponse.json(stats);
  } catch (error) {
    console.error("Error fetching address stats:", error);
    return NextResponse.json(
      { error: "Failed to fetch address stats" },
      { status: 500 }
    );
  }
}
