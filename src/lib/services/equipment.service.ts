import { and, desc, eq, like } from "drizzle-orm";
import { db } from "../db";
import { equipment, Equipment, NewEquipment, tenants } from "./../db/schema";
import { createId } from "@paralleldrive/cuid2";
export class EquipmentService {
  static async getByTenantId(tenantId: number): Promise<Equipment[]> {
    return await db
      .select()
      .from(equipment)
      .where(eq(equipment.tenantId, tenantId))
      .orderBy(desc(equipment.createdAt));
  }

  static async getById(id: string): Promise<Equipment | null> {
    const [equipments] = await db
      .select()
      .from(equipment)
      .where(eq(equipment.id, id))
      .limit(1);
    return equipments || null;
  }

  //create equipment
  static async create(
    data: Omit<NewEquipment, "id" | "createdAt">
  ): Promise<Equipment> {
    const [equipments] = await db
      .insert(equipment)
      .values({
        ...data,
        id: createId(),
      })
      .returning();
    return equipments;
  }

  //Update Equipment
  static async update(
    id: string,
    data: Partial<Omit<NewEquipment, "id" | "tenantId" | "createdAt">>
  ): Promise<Equipment | null> {
    const [equipments] = await db
      .update(equipment)
      .set(data)
      .where(eq(equipment.id, id))
      .returning();
    return equipments || null;
  }

  static async getWithTenant(id: string) {
    const result = await db
      .select({
        equip: equipment,
        tenant: tenants,
      })
      .from(equipment)
      .leftJoin(tenants, eq(equipment.tenantId, tenants.id))
      .where(eq(equipment.id, id))
      .limit(1);
    return result[0] || null;
  }

  // Get all equipment (admin only)
  static async getAll(): Promise<Equipment[]> {
    return await db.select().from(equipment).orderBy(desc(equipment.createdAt));
  }

  // Search locations
  static async searchEquipments(query: string): Promise<Equipment[]> {
    return await db
      .select()
      .from(equipment)
      .where(and(like(equipment.name, `%${query}%`)))
      .orderBy(desc(equipment.createdAt));
  }

    static async searchDisplayEquipments(query: string): Promise<Equipment[]> {
    return await db
      .select()
      .from(equipment)
      .where(and(like(equipment.default_display_name, `%${query}%`)))
      .orderBy(desc(equipment.createdAt));
  }

  static async delete(id: string): Promise<Equipment> {
    // First check if equipment exists
    const existingEquipment = await this.getById(id);
    if (!existingEquipment) {
      throw new Error("Equipment not found");
    }

    // Delete the equipment and return the deleted record
    const [deletedEquipment] = await db
      .delete(equipment)
      .where(eq(equipment.id, id))
      .returning();

    if (!deletedEquipment) {
      throw new Error("Failed to delete equipment");
    }

    return deletedEquipment;
  }

  static async getCreatedEquipments(tenantId: number): Promise<Equipment[]> {
    return await db
      .select()
      .from(equipment)
      .where(and(eq(equipment.tenantId, tenantId)))
      .orderBy(desc(equipment.createdAt));
  }

  static async bulkCreate(
    equipmentsData: Omit<NewEquipment, "id" | "createdAt">[]
  ): Promise<Equipment[]> {
    const equipmentsWithIds = equipmentsData.map((data) => ({
      ...data,
      id: createId(),
    }));

    return await db.insert(equipment).values(equipmentsWithIds).returning();
  }

  static async getStats(tenantId: number) {
    const [totalEquipment] = await db
      .select({ count: equipment.id })
      .from(equipment)
      .where(eq(equipment.tenantId, tenantId));

    return {
      totalEquipment: totalEquipment?.count || 0,
      activeEquipments: 0,
      inactiveEquipments: 0,
    };
  }

   static formatEquipments(equipment: Equipment): string {
    const parts = [
      equipment.id,
      equipment.name,
      equipment.default_display_name,
    ].filter(Boolean);

    return parts.join(", ");
  }



}
