version: '3.8'

# Base configuration shared across environments
services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    environment: &app-env
      - NODE_ENV=production
      - NEXT_TELEMETRY_DISABLED=1
      - DATABASE_URL=postgresql://${DB_USER:-citizix_user}:${DB_PASSWORD:-S3cret}@docker-postgis-1:5432/${DB_NAME:-saas_app}
      - NEXTAUTH_SECRET=${NEXTAUTH_SECRET}
      - NEXTAUTH_URL=${NEXTAUTH_URL}
      - AUTH_SECRET=${AUTH_SECRET}
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_CLIENT_SECRET=${GOOGLE_CLIENT_SECRET}
      - STRIPE_SECRET_KEY=${STRIPE_SECRET_KEY}
      - STRIPE_PUBLISHABLE_KEY=${STRIPE_PUBLISHABLE_KEY}
      - NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=${NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY}
      - STRIPE_WEBHOOK_SECRET=${STRIPE_WEBHOOK_SECRET}
      - RESEND_API_KEY=${RESEND_API_KEY}
      - FROM_EMAIL=${FROM_EMAIL}
      - APP_URL=${APP_URL}
    networks:
      - app-network
      - postgres-network
    depends_on:
      - db-check
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M

  # Database connection health check service
  db-check:
    image: postgres:15-alpine
    networks:
      - postgres-network
    command: >
      sh -c "
        until pg_isready -h docker-postgis-1 -p 5432 -U ${DB_USER:-citizix_user}; do
          echo 'Waiting for PostgreSQL to be ready...';
          sleep 2;
        done;
        echo 'PostgreSQL is ready!';
      "
    environment:
      - PGPASSWORD=${DB_PASSWORD:-S3cret}
    depends_on:
      - postgres-proxy

  # Proxy service to connect to external PostgreSQL container
  postgres-proxy:
    image: alpine/socat:latest
    command: tcp-listen:5432,fork,reuseaddr tcp-connect:docker-postgis-1:5432
    networks:
      - postgres-network
    restart: unless-stopped

  # Nginx reverse proxy and load balancer
  nginx:
    image: nginx:alpine
    ports:
      - "${HTTP_PORT:-80}:80"
      - "${HTTPS_PORT:-443}:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./ssl:/etc/nginx/ssl:ro
      - nginx-cache:/var/cache/nginx
    depends_on:
      - app
    networks:
      - app-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.1'
          memory: 64M

networks:
  app-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
  postgres-network:
    driver: bridge
    external: true

volumes:
  nginx-cache:
    driver: local
