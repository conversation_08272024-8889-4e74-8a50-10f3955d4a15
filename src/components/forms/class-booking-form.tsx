"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { AlertCircle, Calendar, User, CreditCard, Clock, MapPin } from "lucide-react";
import { type ClassBookingWithRelations, type CreateClassBookingData } from "@/lib/hooks/queries/use-class-booking-queries";

// Import hooks untuk dropdown data
import { useClassSchedulesByTenant } from "@/lib/hooks/queries/use-class-schedule-queries";
import { useClassesByTenant } from "@/lib/hooks/queries/use-class-queries";
import { useCustomersByTenant } from "@/lib/hooks/queries/use-customer-queries";

// Form validation schema
const classBookingSchema = z.object({
  tenantId: z.number().int().positive("Tenant is required"),
  scheduleId: z.string().min(1, "Schedule is required"),
  classId: z.string().min(1, "Class is required"),
  customerId: z.string().min(1, "Customer is required"),
  bookedByUserId: z.string().optional(),
  bookedByCustomerId: z.string().optional(),
  status: z.string().optional(),
  isWaitlist: z.boolean().optional(),
  waitlistPosition: z.number().int().min(1).optional(),
  paymentStatus: z.string().optional(),
  paymentMethod: z.string().optional(),
  creditsUsed: z.number().int().min(0).optional(),
  notes: z.string().max(255).optional(),
});

type ClassBookingFormData = z.infer<typeof classBookingSchema>;

interface ClassBookingFormProps {
  booking?: ClassBookingWithRelations;
  tenantId: number;
  onSubmit: (data: CreateClassBookingData) => Promise<void>;
  onCancel?: () => void;
  isLoading?: boolean;
  className?: string;
}

export function ClassBookingForm({
  booking,
  tenantId,
  onSubmit,
  onCancel,
  isLoading = false,
  className = "",
}: ClassBookingFormProps) {
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [isSuccess, setIsSuccess] = useState(false);
  const [selectedClassId, setSelectedClassId] = useState<string>(booking?.classId || "");

  // Fetch dropdown data
  const { data: schedules = [] } = useClassSchedulesByTenant(tenantId);
  const { data: classes = [] } = useClassesByTenant(tenantId);
  const { data: customers = [] } = useCustomersByTenant(tenantId);

  const form = useForm<ClassBookingFormData>({
    resolver: zodResolver(classBookingSchema),
    defaultValues: {
      tenantId,
      scheduleId: booking?.scheduleId || "",
      classId: booking?.classId || "",
      customerId: booking?.customerId || "",
      bookedByUserId: booking?.bookedByUserId || "",
      bookedByCustomerId: booking?.bookedByCustomerId || "",
      status: booking?.status || "booked",
      isWaitlist: booking?.isWaitlist || false,
      waitlistPosition: booking?.waitlistPosition || undefined,
      paymentStatus: booking?.paymentStatus || "",
      paymentMethod: booking?.paymentMethod || "",
      creditsUsed: booking?.creditsUsed || undefined,
      notes: booking?.notes || "",
    },
  });

  const { register, handleSubmit, formState: { errors, isSubmitting }, reset, watch, setValue } = form;

  const watchedScheduleId = watch("scheduleId");
  const watchedIsWaitlist = watch("isWaitlist");

  // Filter schedules berdasarkan class yang dipilih
  const filteredSchedules = schedules.filter(schedule => 
    !selectedClassId || schedule.classId === selectedClassId
  );

  // Auto-set classId ketika schedule dipilih
  useEffect(() => {
    if (watchedScheduleId) {
      const selectedSchedule = schedules.find(s => s.id === watchedScheduleId);
      if (selectedSchedule && selectedSchedule.classId !== selectedClassId) {
        setSelectedClassId(selectedSchedule.classId);
        setValue("classId", selectedSchedule.classId);
      }
    }
  }, [watchedScheduleId, schedules, selectedClassId, setValue]);

  const handleFormSubmit = async (data: ClassBookingFormData) => {
    try {
      setSubmitError(null);
      
      await onSubmit({
        tenantId: data.tenantId,
        scheduleId: data.scheduleId,
        classId: data.classId,
        customerId: data.customerId,
        bookedByUserId: data.bookedByUserId || undefined,
        bookedByCustomerId: data.bookedByCustomerId || undefined,
        status: data.status || "booked",
        isWaitlist: data.isWaitlist || false,
        waitlistPosition: data.isWaitlist ? data.waitlistPosition : undefined,
        paymentStatus: data.paymentStatus || undefined,
        paymentMethod: data.paymentMethod || undefined,
        creditsUsed: data.creditsUsed || undefined,
        notes: data.notes || undefined,
      });

      setIsSuccess(true);
      
      // Reset form jika create mode
      if (!booking) {
        reset();
        setSelectedClassId("");
      }
    } catch (error) {
      console.error("Error submitting class booking:", error);
      setSubmitError(error instanceof Error ? error.message : "Failed to save class booking");
    }
  };

  const isFormLoading = isLoading || isSubmitting;

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="h-5 w-5" />
          {booking ? "Edit Class Booking" : "Create Class Booking"}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          {/* Error Display */}
          {submitError && (
            <div className="flex items-center gap-2 p-3 text-sm text-red-600 bg-red-50 border border-red-200 rounded-md">
              <AlertCircle className="h-4 w-4" />
              {submitError}
            </div>
          )}

          {/* Success Display */}
          {isSuccess && (
            <div className="flex items-center gap-2 p-3 text-sm text-green-600 bg-green-50 border border-green-200 rounded-md">
              <Calendar className="h-4 w-4" />
              Class booking {booking ? "updated" : "created"} successfully!
            </div>
          )}

          {/* Two-column layout */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Left Column - Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Booking Details
              </h3>

              {/* Class Selection */}
              <div>
                <Label htmlFor="classId" className="flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  Class *
                </Label>
                <Select
                  value={selectedClassId}
                  onValueChange={(value) => {
                    setSelectedClassId(value);
                    setValue("classId", value);
                  }}
                  disabled={isFormLoading}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a class" />
                  </SelectTrigger>
                  <SelectContent>
                    {classes.map((cls) => (
                      <SelectItem key={cls.id} value={cls.id}>
                        {cls.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.classId && (
                  <p className="text-sm text-red-600 mt-1">{errors.classId.message}</p>
                )}
              </div>

              {/* Schedule Selection */}
              <div>
                <Label htmlFor="scheduleId" className="flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  Schedule *
                </Label>
                <Select
                  value={watchedScheduleId}
                  onValueChange={(value) => setValue("scheduleId", value)}
                  disabled={isFormLoading}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a schedule" />
                  </SelectTrigger>
                  <SelectContent>
                    {filteredSchedules.map((schedule) => (
                      <SelectItem key={schedule.id} value={schedule.id}>
                        {schedule.startTime ? new Date(schedule.startTime).toLocaleString() : "No time set"} 
                        {schedule.locationName && ` - ${schedule.locationName}`}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.scheduleId && (
                  <p className="text-sm text-red-600 mt-1">{errors.scheduleId.message}</p>
                )}
              </div>

              {/* Customer Selection */}
              <div>
                <Label htmlFor="customerId" className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  Customer *
                </Label>
                <Select
                  value={watch("customerId")}
                  onValueChange={(value) => setValue("customerId", value)}
                  disabled={isFormLoading}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a customer" />
                  </SelectTrigger>
                  <SelectContent>
                    {customers.map((customer) => (
                      <SelectItem key={customer.id} value={customer.id}>
                        {customer.name} ({customer.email})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {errors.customerId && (
                  <p className="text-sm text-red-600 mt-1">{errors.customerId.message}</p>
                )}
              </div>

              {/* Status */}
              <div>
                <Label htmlFor="status">Status</Label>
                <Select
                  value={watch("status")}
                  onValueChange={(value) => setValue("status", value)}
                  disabled={isFormLoading}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="booked">Booked</SelectItem>
                    <SelectItem value="checked_in">Checked In</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                    <SelectItem value="waitlisted">Waitlisted</SelectItem>
                    <SelectItem value="no_show">No Show</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                  </SelectContent>
                </Select>
                {errors.status && (
                  <p className="text-sm text-red-600 mt-1">{errors.status.message}</p>
                )}
              </div>
            </div>

            {/* Right Column - Payment & Additional Info */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium flex items-center gap-2">
                <CreditCard className="h-4 w-4" />
                Payment & Settings
              </h3>

              {/* Waitlist Toggle */}
              <div className="flex items-center justify-between">
                <Label htmlFor="isWaitlist" className="flex items-center gap-2">
                  Is Waitlist
                </Label>
                <Switch
                  id="isWaitlist"
                  checked={watchedIsWaitlist}
                  onCheckedChange={(checked) => setValue("isWaitlist", checked)}
                  disabled={isFormLoading}
                />
              </div>

              {/* Waitlist Position (conditional) */}
              {watchedIsWaitlist && (
                <div>
                  <Label htmlFor="waitlistPosition">Waitlist Position</Label>
                  <Input
                    id="waitlistPosition"
                    type="number"
                    min="1"
                    {...register("waitlistPosition", { valueAsNumber: true })}
                    placeholder="Enter position in waitlist"
                    disabled={isFormLoading}
                  />
                  {errors.waitlistPosition && (
                    <p className="text-sm text-red-600 mt-1">{errors.waitlistPosition.message}</p>
                  )}
                </div>
              )}

              {/* Payment Status */}
              <div>
                <Label htmlFor="paymentStatus">Payment Status</Label>
                <Select
                  value={watch("paymentStatus")}
                  onValueChange={(value) => setValue("paymentStatus", value)}
                  disabled={isFormLoading}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select payment status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="paid">Paid</SelectItem>
                    <SelectItem value="unpaid">Unpaid</SelectItem>
                    <SelectItem value="refunded">Refunded</SelectItem>
                  </SelectContent>
                </Select>
                {errors.paymentStatus && (
                  <p className="text-sm text-red-600 mt-1">{errors.paymentStatus.message}</p>
                )}
              </div>

              {/* Payment Method */}
              <div>
                <Label htmlFor="paymentMethod">Payment Method</Label>
                <Select
                  value={watch("paymentMethod")}
                  onValueChange={(value) => setValue("paymentMethod", value)}
                  disabled={isFormLoading}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select payment method" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="credit">Credit</SelectItem>
                    <SelectItem value="cash">Cash</SelectItem>
                    <SelectItem value="classpass">ClassPass</SelectItem>
                    <SelectItem value="card">Card</SelectItem>
                    <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
                  </SelectContent>
                </Select>
                {errors.paymentMethod && (
                  <p className="text-sm text-red-600 mt-1">{errors.paymentMethod.message}</p>
                )}
              </div>

              {/* Credits Used */}
              <div>
                <Label htmlFor="creditsUsed">Credits Used</Label>
                <Input
                  id="creditsUsed"
                  type="number"
                  min="0"
                  {...register("creditsUsed", { valueAsNumber: true })}
                  placeholder="Enter credits used"
                  disabled={isFormLoading}
                />
                {errors.creditsUsed && (
                  <p className="text-sm text-red-600 mt-1">{errors.creditsUsed.message}</p>
                )}
              </div>

              {/* Notes */}
              <div>
                <Label htmlFor="notes">Notes</Label>
                <Textarea
                  id="notes"
                  {...register("notes")}
                  placeholder="Add any additional notes..."
                  disabled={isFormLoading}
                  rows={3}
                />
                {errors.notes && (
                  <p className="text-sm text-red-600 mt-1">{errors.notes.message}</p>
                )}
              </div>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isFormLoading}
              >
                Cancel
              </Button>
            )}
            <Button
              type="submit"
              disabled={isFormLoading}
              className="min-w-[120px]"
            >
              {isFormLoading ? "Saving..." : booking ? "Update Booking" : "Create Booking"}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
