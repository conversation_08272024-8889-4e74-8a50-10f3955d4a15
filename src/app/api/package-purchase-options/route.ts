import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { z } from "zod";
import { packagePurchaseOptionsService } from "@/lib/services/package-purchase-options.service";

// Validation schemas
const createPackagePurchaseOptionsSchema = z.object({
  packageId: z.string().min(1, "Package ID is required"),
  purchaseLimit: z.number().int().min(0, "Purchase limit cannot be negative").optional(),
  restrictTo: z.string().max(255, "Restrict to value too long").optional(),
  transferable: z.boolean().optional(),
  specifySoldAtLocation: z.boolean().optional(),
  soldAtLocationId: z.string().optional(),
  classBookingLimit: z.number().int().min(0, "Class booking limit cannot be negative").optional(),
  showOnline: z.boolean().optional(),
});

const queryFiltersSchema = z.object({
  packageId: z.string().optional(),
  restrictTo: z.string().optional(),
  transferable: z.string().transform((val) => val === 'true').optional(),
  showOnline: z.string().transform((val) => val === 'true').optional(),
  locationId: z.string().optional(),
  limit: z.string().transform((val) => parseInt(val)).optional(),
  offset: z.string().transform((val) => parseInt(val)).optional(),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).optional(),
});

/**
 * GET /api/package-purchase-options
 * Get all package purchase options with optional filtering
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    // Validate query parameters
    const validatedParams = queryFiltersSchema.parse(queryParams);
    
    // Extract filters and options
    const { limit, offset, sortBy, sortOrder, ...filters } = validatedParams;
    
    const options = {
      filters,
      limit,
      offset,
      sortBy,
      sortOrder,
    };

    console.log(`📦 [GET /api/package-purchase-options] Fetching package purchase options:`, options);

    const packagePurchaseOptions = await packagePurchaseOptionsService.getAll(options);

    return NextResponse.json({
      success: true,
      data: packagePurchaseOptions,
      meta: {
        total: packagePurchaseOptions.length,
        filters,
      }
    });
  } catch (error) {
    console.error("📦 [GET /api/package-purchase-options] Error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid query parameters", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to fetch package purchase options" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/package-purchase-options
 * Create new package purchase options
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = createPackagePurchaseOptionsSchema.parse(body);

    console.log(`📦 [POST /api/package-purchase-options] Creating package purchase options:`, validatedData);

    const packagePurchaseOptions = await packagePurchaseOptionsService.create(validatedData);

    return NextResponse.json({
      success: true,
      data: packagePurchaseOptions,
      message: "Package purchase options created successfully"
    }, { status: 201 });
  } catch (error) {
    console.error("📦 [POST /api/package-purchase-options] Error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid package purchase options data", details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      if (error.message.includes("already exist")) {
        return NextResponse.json(
          { error: error.message },
          { status: 409 }
        );
      }

      if (error.message.includes("not found")) {
        return NextResponse.json(
          { error: error.message },
          { status: 404 }
        );
      }

      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to create package purchase options" },
      { status: 500 }
    );
  }
}
