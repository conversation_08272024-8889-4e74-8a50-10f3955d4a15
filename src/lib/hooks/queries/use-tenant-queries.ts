"use client";

import { useQuery, useMutation, useQueryClient, useInfiniteQuery } from "@tanstack/react-query";
import { Tenant, NewTenant } from "@/lib/db/schema";

// Import global query keys
import { tenantKeys } from "./query-keys";

// API Functions
const tenantApi = {
  getById: async (id: number): Promise<Tenant> => {
    const response = await fetch(`/api/tenants/${id}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch tenant: ${response.statusText}`);
    }
    return response.json();
  },

  getBySubdomain: async (subdomain: string): Promise<Tenant> => {
    const response = await fetch(`/api/tenants/subdomain/${subdomain}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch tenant by subdomain: ${response.statusText}`);
    }
    return response.json();
  },

  getByDomain: async (domain: string): Promise<Tenant> => {
    const response = await fetch(`/api/tenants/domain/${domain}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch tenant by domain: ${response.statusText}`);
    }
    return response.json();
  },

  getAll: async (filters?: {
    organizationId?: number;
    isActive?: boolean;
    search?: string;
    page?: number;
    limit?: number;
  }): Promise<{ tenants: Tenant[]; total: number; page: number; limit: number }> => {
    const params = new URLSearchParams();
    if (filters?.organizationId) params.append('organizationId', filters.organizationId.toString());
    if (filters?.isActive !== undefined) params.append('isActive', filters.isActive.toString());
    if (filters?.search) params.append('search', filters.search);
    if (filters?.page) params.append('page', filters.page.toString());
    if (filters?.limit) params.append('limit', filters.limit.toString());

    const response = await fetch(`/api/tenants?${params}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch tenants: ${response.statusText}`);
    }
    return response.json();
  },

  create: async (data: Omit<NewTenant, "id" | "createdAt" | "updatedAt">): Promise<Tenant> => {
    const response = await fetch('/api/tenants', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to create tenant');
    }
    return response.json();
  },

  update: async ({ id, data }: { id: number; data: Partial<NewTenant> }): Promise<Tenant> => {
    const response = await fetch(`/api/tenants/${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to update tenant');
    }
    return response.json();
  },

  delete: async (id: number): Promise<void> => {
    const response = await fetch(`/api/tenants/${id}`, {
      method: 'DELETE',
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to delete tenant');
    }
  },

  checkSubdomain: async (subdomain: string): Promise<{ available: boolean; suggestion?: string }> => {
    const response = await fetch(`/api/tenants/check-subdomain?subdomain=${encodeURIComponent(subdomain)}`);
    if (!response.ok) {
      throw new Error('Failed to check subdomain availability');
    }
    return response.json();
  },

  getTenantUsers: async (id: number) => {
    const response = await fetch(`/api/tenants/${id}/users`);
    if (!response.ok) {
      throw new Error('Failed to fetch tenant users');
    }
    return response.json();
  },

  getTenantAnalytics: async (id: number, period: string = '30d') => {
    const response = await fetch(`/api/tenants/${id}/analytics?period=${period}`);
    if (!response.ok) {
      throw new Error('Failed to fetch tenant analytics');
    }
    return response.json();
  },

  getStats: async (): Promise<{
    totalTenants: number;
    activeTenants: number;
    inactiveTenants: number;
    totalUsers: number;
    totalBusinessProfiles: number;
  }> => {
    const response = await fetch('/api/tenants/stats');
    if (!response.ok) {
      throw new Error('Failed to fetch tenant stats');
    }
    return response.json();
  },

  activateTenant: async (id: number): Promise<Tenant> => {
    const response = await fetch(`/api/tenants/${id}/activate`, {
      method: 'POST',
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to activate tenant');
    }
    return response.json();
  },

  deactivateTenant: async (id: number): Promise<Tenant> => {
    const response = await fetch(`/api/tenants/${id}/deactivate`, {
      method: 'POST',
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to deactivate tenant');
    }
    return response.json();
  },
};

// Query Hooks
export function useTenant(id: number) {
  return useQuery({
    queryKey: tenantKeys.detail(id),
    queryFn: () => tenantApi.getById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useTenantBySubdomain(subdomain: string) {
  return useQuery({
    queryKey: tenantKeys.bySubdomain(subdomain),
    queryFn: () => tenantApi.getBySubdomain(subdomain),
    enabled: !!subdomain,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

export function useTenantByDomain(domain: string) {
  return useQuery({
    queryKey: tenantKeys.byDomain(domain),
    queryFn: () => tenantApi.getByDomain(domain),
    enabled: !!domain,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

export function useTenants(filters?: {
  organizationId?: number;
  isActive?: boolean;
  search?: string;
}) {
  const query = useQuery({
    queryKey: tenantKeys.list(filters || {}),
    queryFn: () => tenantApi.getAll(filters),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });

  // Backward compatibility
  return {
    tenants: query.data?.tenants || [],
    loading: query.isLoading,
    error: query.error?.message || null,
    isLoading: query.isLoading,
    isError: query.isError,
    data: query.data,
    refetch: query.refetch,
  };
}

export function useInfiniteTenants(filters?: {
  organizationId?: number;
  isActive?: boolean;
  search?: string;
}) {
  return useInfiniteQuery({
    queryKey: [...tenantKeys.list(filters || {}), 'infinite'],
    queryFn: ({ pageParam = 1 }) => tenantApi.getAll({ ...filters, page: pageParam, limit: 20 }),
    getNextPageParam: (lastPage) => {
      const hasMore = lastPage.tenants.length === lastPage.limit;
      return hasMore ? lastPage.page + 1 : undefined;
    },
    initialPageParam: 1,
  });
}

export function useSubdomainCheck(subdomain: string) {
  return useQuery({
    queryKey: ['subdomain-check', subdomain],
    queryFn: () => tenantApi.checkSubdomain(subdomain),
    enabled: !!subdomain && subdomain.length >= 3,
    staleTime: 30 * 1000, // 30 seconds
  });
}

export function useTenantUsers(id: number) {
  return useQuery({
    queryKey: tenantKeys.users(id),
    queryFn: () => tenantApi.getTenantUsers(id),
    enabled: !!id,
    staleTime: 2 * 60 * 1000,
  });
}

export function useTenantAnalytics(id: number, period: string = '30d') {
  return useQuery({
    queryKey: tenantKeys.analytics(id),
    queryFn: () => tenantApi.getTenantAnalytics(id, period),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
}

export function useTenantStats() {
  return useQuery({
    queryKey: tenantKeys.stats(),
    queryFn: tenantApi.getStats,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Mutation Hooks
export function useCreateTenant() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: tenantApi.create,
    onSuccess: (newTenant) => {
      queryClient.invalidateQueries({ queryKey: tenantKeys.all });
      queryClient.setQueryData(tenantKeys.detail(newTenant.id), newTenant);
    },
  });
}

export function useUpdateTenant() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: tenantApi.update,
    onMutate: async ({ id, data }) => {
      await queryClient.cancelQueries({ queryKey: tenantKeys.detail(id) });
      const previousTenant = queryClient.getQueryData(tenantKeys.detail(id));
      
      queryClient.setQueryData(tenantKeys.detail(id), (old: Tenant | undefined) => {
        if (!old) return old;
        return { ...old, ...data, updatedAt: new Date() };
      });

      return { previousTenant, id };
    },
    onError: (err, variables, context) => {
      if (context?.previousTenant) {
        queryClient.setQueryData(tenantKeys.detail(context.id), context.previousTenant);
      }
    },
    onSettled: (data, error, variables) => {
      queryClient.invalidateQueries({ queryKey: tenantKeys.detail(variables.id) });
      queryClient.invalidateQueries({ queryKey: tenantKeys.lists() });
    },
  });
}

export function useDeleteTenant() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: tenantApi.delete,
    onSuccess: (_, id) => {
      queryClient.removeQueries({ queryKey: tenantKeys.detail(id) });
      queryClient.invalidateQueries({ queryKey: tenantKeys.lists() });
      queryClient.invalidateQueries({ queryKey: tenantKeys.stats() });
    },
  });
}

export function useActivateTenant() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: tenantApi.activateTenant,
    onSuccess: (updatedTenant) => {
      queryClient.setQueryData(tenantKeys.detail(updatedTenant.id), updatedTenant);
      queryClient.invalidateQueries({ queryKey: tenantKeys.lists() });
      queryClient.invalidateQueries({ queryKey: tenantKeys.stats() });
    },
  });
}

export function useDeactivateTenant() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: tenantApi.deactivateTenant,
    onSuccess: (updatedTenant) => {
      queryClient.setQueryData(tenantKeys.detail(updatedTenant.id), updatedTenant);
      queryClient.invalidateQueries({ queryKey: tenantKeys.lists() });
      queryClient.invalidateQueries({ queryKey: tenantKeys.stats() });
    },
  });
}
