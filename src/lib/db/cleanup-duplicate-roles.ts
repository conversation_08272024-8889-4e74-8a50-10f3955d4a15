import { db } from "@/lib/db";
import { roles, role_permissions, user_roles } from "@/lib/db/schema";
import { eq, sql } from "drizzle-orm";

/**
 * Cleanup duplicate roles in the database
 * This script removes duplicate roles while preserving relationships
 */
export async function cleanupDuplicateRoles() {
  try {
    console.log('🧹 Starting duplicate role cleanup...');

    // Get all roles grouped by name to find duplicates
    const duplicateRoles = await db
      .select({
        name: roles.name,
        count: sql<number>`count(*)`,
        ids: sql<string[]>`array_agg(${roles.id})`,
        first_id: sql<string>`min(${roles.id})`
      })
      .from(roles)
      .groupBy(roles.name)
      .having(sql`count(*) > 1`);

    console.log(`Found ${duplicateRoles.length} role names with duplicates`);

    for (const duplicate of duplicateRoles) {
      const roleIds = duplicate.ids;
      const keepId = duplicate.first_id;
      const deleteIds = roleIds.filter(id => id !== keepId);

      console.log(`\n📝 Processing role: ${duplicate.name}`);
      console.log(`  - Total duplicates: ${duplicate.count}`);
      console.log(`  - Keeping: ${keepId}`);
      console.log(`  - Deleting: ${deleteIds.join(', ')}`);

      // Update user_roles to point to the kept role
      for (const deleteId of deleteIds) {
        const userRoleUpdates = await db
          .update(user_roles)
          .set({ roleId: keepId })
          .where(eq(user_roles.roleId, deleteId))
          .returning();
        
        console.log(`    - Updated ${userRoleUpdates.length} user role assignments`);
      }

      // Update role_permissions to point to the kept role (avoid duplicates)
      for (const deleteId of deleteIds) {
        // First, get permissions for the role to be deleted
        const permissionsToMove = await db
          .select()
          .from(role_permissions)
          .where(eq(role_permissions.roleId, deleteId));

        // Check which permissions already exist for the kept role
        const existingPermissions = await db
          .select()
          .from(role_permissions)
          .where(eq(role_permissions.roleId, keepId));

        const existingPermissionIds = new Set(existingPermissions.map(p => p.permissionId));

        // Only move permissions that don't already exist
        for (const permission of permissionsToMove) {
          if (!existingPermissionIds.has(permission.permissionId)) {
            await db
              .update(role_permissions)
              .set({ roleId: keepId })
              .where(eq(role_permissions.id, permission.id));
          } else {
            // Delete duplicate permission assignment
            await db
              .delete(role_permissions)
              .where(eq(role_permissions.id, permission.id));
          }
        }

        console.log(`    - Processed ${permissionsToMove.length} permission assignments`);
      }

      // Delete duplicate roles
      for (const deleteId of deleteIds) {
        await db.delete(roles).where(eq(roles.id, deleteId));
      }

      console.log(`  ✅ Cleaned up ${deleteIds.length} duplicates for ${duplicate.name}`);
    }

    console.log('\n✅ Duplicate role cleanup completed!');

    // Verify cleanup
    const remainingRoles = await db.select().from(roles);
    console.log(`\n📊 Final count: ${remainingRoles.length} roles remaining`);
    
    const roleNames = remainingRoles.map(r => r.name);
    const uniqueNames = [...new Set(roleNames)];
    console.log(`📊 Unique role names: ${uniqueNames.length}`);
    
    if (roleNames.length === uniqueNames.length) {
      console.log('🎉 No more duplicates found!');
    } else {
      console.log('⚠️ Some duplicates may still exist');
    }

    // Show final role list
    console.log('\n📋 Final roles:');
    remainingRoles.forEach(role => {
      console.log(`  - ${role.display_name} (${role.name}) - Level ${role.hierarchy_level}`);
    });

  } catch (error) {
    console.error('❌ Cleanup failed:', error);
    throw error;
  }
}

// Run cleanup if called directly
if (require.main === module) {
  cleanupDuplicateRoles()
    .then(() => {
      console.log('🎉 Cleanup completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Cleanup failed:', error);
      process.exit(1);
    });
}
