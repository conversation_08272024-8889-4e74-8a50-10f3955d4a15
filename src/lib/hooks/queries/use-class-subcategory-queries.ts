import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { ClassSubcategory } from '@/lib/db/schema';

/**
 * Class Subcategory Queries Hooks
 * 
 * Hooks untuk manage state dan data fetching class subcategories menggunakan TanStack Query.
 * Pattern ini mengikuti use-class-category-queries.ts yang sudah established.
 */

// Query Keys
export const classSubcategoryKeys = {
  all: ['class-subcategories'] as const,
  lists: () => [...classSubcategoryKeys.all, 'list'] as const,
  list: (filters: Record<string, any>) => [...classSubcategoryKeys.lists(), { filters }] as const,
  details: () => [...classSubcategoryKeys.all, 'detail'] as const,
  detail: (id: string) => [...classSubcategoryKeys.details(), id] as const,
  byTenant: (tenantId: number) => [...classSubcategoryKeys.all, 'tenant', tenantId] as const,
  byCategory: (categoryId: string) => [...classSubcategoryKeys.all, 'category', categoryId] as const,
  search: (params: any) => [...classSubcategoryKeys.all, 'search', params] as const,
};

// API Functions
const classSubcategoryApi = {
  getAll: async (): Promise<ClassSubcategory[]> => {
    const response = await fetch('/api/class-subcategories');
    if (!response.ok) {
      throw new Error(`Failed to fetch class subcategories: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data?.subcategories || [];
  },

  getByTenant: async (tenantId: number): Promise<ClassSubcategory[]> => {
    const params = new URLSearchParams({
      tenantId: tenantId.toString(),
    });
    const response = await fetch(`/api/class-subcategories?${params}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch class subcategories: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data?.subcategories || [];
  },

  getByCategory: async (categoryId: string, tenantId: number): Promise<ClassSubcategory[]> => {
    const params = new URLSearchParams({
      categoryId: categoryId,
      tenantId: tenantId.toString(),
    });
    const response = await fetch(`/api/class-subcategories?${params}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch class subcategories: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data?.subcategories || [];
  },

  search: async (tenantId: number, categoryId?: string, searchTerm?: string, limit = 20, offset = 0) => {
    const params = new URLSearchParams({
      tenantId: tenantId.toString(),
      limit: limit.toString(),
      offset: offset.toString(),
    });
    if (categoryId) {
      params.append('categoryId', categoryId);
    }
    if (searchTerm) {
      params.append('search', searchTerm);
    }
    const response = await fetch(`/api/class-subcategories?${params}`);
    if (!response.ok) {
      throw new Error(`Failed to search class subcategories: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data;
  },

  getById: async (id: string): Promise<ClassSubcategory> => {
    const response = await fetch(`/api/class-subcategories/${id}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch class subcategory: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data;
  },

  create: async (data: { tenantId: number; categoryId: string; name: string }): Promise<ClassSubcategory> => {
    const response = await fetch('/api/class-subcategories', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      throw new Error(`Failed to create class subcategory: ${response.statusText}`);
    }
    const result = await response.json();
    return result.data;
  },

  update: async (id: string, tenantId: number, data: { name: string }): Promise<ClassSubcategory> => {
    const response = await fetch(`/api/class-subcategories/${id}?tenantId=${tenantId}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      throw new Error(`Failed to update class subcategory: ${response.statusText}`);
    }
    const result = await response.json();
    return result.data;
  },

  delete: async (id: string, tenantId: number): Promise<void> => {
    const response = await fetch(`/api/class-subcategories/${id}?tenantId=${tenantId}`, {
      method: 'DELETE',
    });
    if (!response.ok) {
      throw new Error(`Failed to delete class subcategory: ${response.statusText}`);
    }
  },
};

// Query Hooks
export function useClassSubcategories() {
  const query = useQuery({
    queryKey: classSubcategoryKeys.lists(),
    queryFn: classSubcategoryApi.getAll,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: false,
  });

  const classSubcategories = Array.isArray(query.data) ? query.data : [];

  return {
    data: classSubcategories,
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error?.message || null,
    refetch: query.refetch,
  };
}

export function useClassSubcategoriesByTenant(tenantId: number) {
  return useQuery({
    queryKey: classSubcategoryKeys.byTenant(tenantId),
    queryFn: () => classSubcategoryApi.getByTenant(tenantId),
    enabled: !!tenantId,
    staleTime: 5 * 60 * 1000,
  });
}

export function useClassSubcategoriesByCategory(categoryId: string, tenantId: number) {
  return useQuery({
    queryKey: classSubcategoryKeys.byCategory(categoryId),
    queryFn: () => classSubcategoryApi.getByCategory(categoryId, tenantId),
    enabled: !!categoryId && !!tenantId,
    staleTime: 5 * 60 * 1000,
  });
}

export function useClassSubcategory(id: string) {
  return useQuery({
    queryKey: classSubcategoryKeys.detail(id),
    queryFn: () => classSubcategoryApi.getById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
}

export function useClassSubcategorySearch(tenantId: number, categoryId?: string, searchTerm?: string, limit = 20, offset = 0) {
  return useQuery({
    queryKey: classSubcategoryKeys.search({ tenantId, categoryId, searchTerm, limit, offset }),
    queryFn: () => classSubcategoryApi.search(tenantId, categoryId, searchTerm, limit, offset),
    enabled: !!tenantId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

// Mutation Hooks
export function useCreateClassSubcategory() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: classSubcategoryApi.create,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: classSubcategoryKeys.all });
    },
  });
}

export function useUpdateClassSubcategory() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, tenantId, data }: { 
      id: string; 
      tenantId: number; 
      data: { name: string }
    }) => {
      return classSubcategoryApi.update(id, tenantId, data);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: classSubcategoryKeys.all });
    },
  });
}

export function useDeleteClassSubcategory() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: ({ id, tenantId }: { id: string; tenantId: number }) => {
      return classSubcategoryApi.delete(id, tenantId);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: classSubcategoryKeys.all });
    },
  });
}

// Export types
export interface ClassSubcategorySearchParams {
  tenantId: number;
  categoryId?: string;
  search?: string;
  limit?: number;
  offset?: number;
}
