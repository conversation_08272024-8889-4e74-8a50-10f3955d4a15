"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, FolderOpen, CheckCircle } from "lucide-react";
import { ClassSubcategory } from "@/lib/db/schema";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useClassCategoriesByTenant } from "@/lib/hooks/queries/use-class-category-queries";

// Validation schema
const classSubcategorySchema = z.object({
  tenantId: z.number().int().positive(),
  categoryId: z.string().min(1, "Category is required"),
  name: z.string().min(1, "Name is required").max(255),
});

type ClassSubcategoryFormData = z.infer<typeof classSubcategorySchema>;

interface ClassSubcategoryFormProps {
  subcategory?: ClassSubcategory;
  tenantId: number;
  onSubmit: (data: { tenantId: number; categoryId: string; name: string }) => Promise<void>;
  onCancel?: () => void;
  isLoading?: boolean;
  className?: string;
}

export function ClassSubcategoryForm({ 
  subcategory, 
  tenantId, 
  onSubmit, 
  onCancel, 
  isLoading = false,
  className = "" 
}: ClassSubcategoryFormProps) {
  const [isSuccess, setIsSuccess] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  // Get categories for dropdown
  const { data: categories = [] } = useClassCategoriesByTenant(tenantId);

  const form = useForm<ClassSubcategoryFormData>({
    resolver: zodResolver(classSubcategorySchema),
    defaultValues: {
      tenantId,
      categoryId: subcategory?.categoryId || "",
      name: subcategory?.name || "",
    },
  });

  const handleSubmit = async (data: ClassSubcategoryFormData) => {
    try {
      setSubmitError(null);
      await onSubmit({
        tenantId: data.tenantId,
        categoryId: data.categoryId,
        name: data.name,
      });
      setIsSuccess(true);
      setTimeout(() => setIsSuccess(false), 2000);
    } catch (error) {
      console.error("Form submission error:", error);
      setSubmitError(error instanceof Error ? error.message : "An error occurred");
    }
  };

  const isFormLoading = isLoading || form.formState.isSubmitting;

  return (
    <Card className={`w-full max-w-md mx-auto ${className}`}>
      <CardHeader className="text-center">
        <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
          <FolderOpen className="h-6 w-6 text-blue-600" />
        </div>
        <CardTitle className="text-xl font-semibold">
          {subcategory ? "Edit Class Subcategory" : "Create Class Subcategory"}
        </CardTitle>
        <CardDescription>
          {subcategory ? "Update the subcategory details" : "Add a new class subcategory to organize your classes"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
          {/* Success Alert */}
          {isSuccess && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="mb-4"
            >
              <Alert className="border-green-200 bg-green-50">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <AlertDescription className="text-green-800">
                  Class subcategory {subcategory ? "updated" : "created"} successfully!
                </AlertDescription>
              </Alert>
            </motion.div>
          )}

          {/* Error Alert */}
          {submitError && (
            <Alert variant="destructive" className="mb-4">
              <AlertDescription>{submitError}</AlertDescription>
            </Alert>
          )}

          {/* Category Selection */}
          <div className="space-y-2">
            <Label htmlFor="categoryId">Class Category</Label>
            <Select
              value={form.watch("categoryId")}
              onValueChange={(value) => form.setValue("categoryId", value)}
              disabled={isFormLoading}
            >
              <SelectTrigger className={form.formState.errors.categoryId ? "border-red-500" : ""}>
                <SelectValue placeholder="Select a class category" />
              </SelectTrigger>
              <SelectContent>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {form.formState.errors.categoryId && (
              <p className="text-sm text-red-500">
                {form.formState.errors.categoryId.message}
              </p>
            )}
          </div>

          {/* Name */}
          <div className="space-y-2">
            <Label htmlFor="name">Subcategory Name</Label>
            <Input
              id="name"
              placeholder="Enter subcategory name"
              {...form.register("name")}
              className={form.formState.errors.name ? "border-red-500" : ""}
              disabled={isFormLoading}
            />
            {form.formState.errors.name && (
              <p className="text-sm text-red-500">
                {form.formState.errors.name.message}
              </p>
            )}
          </div>

          {/* Form Actions */}
          <div className="flex gap-3 pt-4">
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isFormLoading}
                className="flex-1"
              >
                Cancel
              </Button>
            )}
            <Button
              type="submit"
              disabled={isFormLoading}
              className="flex-1"
            >
              {isFormLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {subcategory ? "Update" : "Create"} Subcategory
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
