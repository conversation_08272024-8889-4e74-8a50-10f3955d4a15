import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { TenantService } from "@/lib/services/tenant.service";
import { z } from "zod";

const updateTenantSchema = z.object({
  name: z.string().min(1).max(255).optional(),
  subdomain: z.string()
    .min(3)
    .max(63)
    .regex(/^[a-z0-9-]+$/)
    .optional(),
  customDomain: z.string().optional(),
  settings: z.record(z.any()).optional(),
  isActive: z.boolean().optional(),
});

// GET /api/tenants/[id] - Get tenant by ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    const tenantId = parseInt(id);
    if (isNaN(tenantId)) {
      return NextResponse.json({ error: "Invalid tenant ID" }, { status: 400 });
    }

    const tenant = await TenantService.getTenantWithOrganization(tenantId);
    if (!tenant) {
      return NextResponse.json({ error: "Tenant not found" }, { status: 404 });
    }

    // Check permissions
    if (
      session.user.role !== "admin" &&
      tenant.tenant.organizationId !== session.user.organizationId
    ) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    return NextResponse.json({ tenant });
  } catch (error) {
    console.error("Error fetching tenant:", error);
    return NextResponse.json(
      { error: "Failed to fetch tenant" },
      { status: 500 }
    );
  }
}

// PUT /api/tenants/[id] - Update tenant
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    const tenantId = parseInt(id);
    if (isNaN(tenantId)) {
      return NextResponse.json({ error: "Invalid tenant ID" }, { status: 400 });
    }

    // Check if tenant exists and user has permission
    const existingTenant = await TenantService.getTenantById(tenantId);
    if (!existingTenant) {
      return NextResponse.json({ error: "Tenant not found" }, { status: 404 });
    }

    if (
      session.user.role !== "admin" &&
      existingTenant.organizationId !== session.user.organizationId
    ) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const body = await request.json();
    const validatedData = updateTenantSchema.parse(body);

    const tenant = await TenantService.updateTenant(tenantId, validatedData);

    return NextResponse.json({ tenant });
  } catch (error) {
    console.error("Error updating tenant:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to update tenant" },
      { status: 500 }
    );
  }
}

// DELETE /api/tenants/[id] - Delete tenant
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    const tenantId = parseInt(id);
    if (isNaN(tenantId)) {
      return NextResponse.json({ error: "Invalid tenant ID" }, { status: 400 });
    }

    // Check if tenant exists and user has permission
    const existingTenant = await TenantService.getTenantById(tenantId);
    if (!existingTenant) {
      return NextResponse.json({ error: "Tenant not found" }, { status: 404 });
    }

    if (
      session.user.role !== "admin" &&
      existingTenant.organizationId !== session.user.organizationId
    ) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const success = await TenantService.deleteTenant(tenantId);
    if (!success) {
      return NextResponse.json(
        { error: "Failed to delete tenant" },
        { status: 500 }
      );
    }

    return NextResponse.json({ message: "Tenant deleted successfully" });
  } catch (error) {
    console.error("Error deleting tenant:", error);
    return NextResponse.json(
      { error: "Failed to delete tenant" },
      { status: 500 }
    );
  }
}
