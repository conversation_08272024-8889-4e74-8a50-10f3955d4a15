import { db } from "@/lib/db";
import { 
  class_bookings, 
  classes, 
  class_schedules, 
  customers, 
  users,
  locations,
  type ClassBooking, 
  type NewClassBooking 
} from "@/lib/db/schema";
import { eq, and, desc, asc, sql, ilike } from "drizzle-orm";
import { createId } from "@paralleldrive/cuid2";

/**
 * ClassBookingService - Service untuk manage class bookings
 * 
 * Menggunakan static methods pattern yang simple dan proven seperti LocationService dan EquipmentService.
 * Pattern ini udah terbukti berhasil tanpa bug di locations dan equipment management.
 * 
 * Features:
 * - CRUD operations untuk class bookings
 * - Search dan filtering dengan tenant isolation
 * - Status management (booked, checked_in, cancelled, etc.)
 * - Waitlist management
 * - Payment tracking
 * - Bulk operations
 * - Statistics dan analytics
 */

export interface CreateClassBookingData {
  tenantId: number;
  scheduleId: string;
  classId: string;
  customerId: string;
  bookedByUserId?: string;
  bookedByCustomerId?: string;
  status?: string;
  isWaitlist?: boolean;
  waitlistPosition?: number;
  paymentStatus?: string;
  paymentMethod?: string;
  creditsUsed?: number;
  notes?: string;
}

export interface UpdateClassBookingData {
  scheduleId?: string;
  classId?: string;
  customerId?: string;
  bookedByUserId?: string;
  bookedByCustomerId?: string;
  status?: string;
  isWaitlist?: boolean;
  waitlistPosition?: number;
  paymentStatus?: string;
  paymentMethod?: string;
  creditsUsed?: number;
  checkInTime?: Date;
  cancelTime?: Date;
  cancellationReason?: string;
  notes?: string;
}

export interface ClassBookingFilters {
  search?: string;
  tenantId?: number;
  scheduleId?: string;
  classId?: string;
  customerId?: string;
  status?: string;
  isWaitlist?: boolean;
  paymentStatus?: string;
  dateFrom?: string;
  dateTo?: string;
}

export interface ClassBookingSearchResult {
  bookings: ClassBookingWithRelations[];
  total: number;
  hasMore: boolean;
}

export interface ClassBookingWithRelations extends ClassBooking {
  className?: string;
  customerName?: string;
  customerEmail?: string;
  bookedByUserName?: string;
  bookedByCustomerName?: string;
  scheduleStartTime?: Date;
  scheduleEndTime?: Date;
  locationName?: string;
}

export interface ClassBookingStats {
  total: number;
  booked: number;
  checkedIn: number;
  cancelled: number;
  waitlisted: number;
  noShow: number;
  completed: number;
  byStatus: Record<string, number>;
  byPaymentStatus: Record<string, number>;
  byTenant: Record<number, {
    total: number;
    booked: number;
    checkedIn: number;
    cancelled: number;
  }>;
}

export class ClassBookingService {
  /**
   * Get all class bookings by tenant ID dengan proper tenant isolation
   */
  static async getByTenantId(tenantId: number): Promise<ClassBookingWithRelations[]> {
    return await db
      .select({
        id: class_bookings.id,
        tenantId: class_bookings.tenant_id,
        scheduleId: class_bookings.schedule_id,
        classId: class_bookings.class_id,
        customerId: class_bookings.customer_id,
        bookedByUserId: class_bookings.booked_by_user_id,
        bookedByCustomerId: class_bookings.booked_by_customer_id,
        status: class_bookings.status,
        isWaitlist: class_bookings.is_waitlist,
        waitlistPosition: class_bookings.waitlist_position,
        paymentStatus: class_bookings.payment_status,
        paymentMethod: class_bookings.payment_method,
        creditsUsed: class_bookings.credits_used,
        bookingTime: class_bookings.booking_time,
        checkInTime: class_bookings.check_in_time,
        cancelTime: class_bookings.cancel_time,
        cancellationReason: class_bookings.cancellation_reason,
        notes: class_bookings.notes,
        createdAt: class_bookings.createdAt,
        updatedAt: class_bookings.updatedAt,
        // Relations
        className: classes.name,
        customerName: customers.name,
        customerEmail: customers.email,
        bookedByUserName: users.name,
        scheduleStartTime: class_schedules.start_time,
        scheduleEndTime: class_schedules.end_time,
        locationName: locations.name,
      })
      .from(class_bookings)
      .leftJoin(classes, eq(class_bookings.class_id, classes.id))
      .leftJoin(customers, eq(class_bookings.customer_id, customers.id))
      .leftJoin(users, eq(class_bookings.booked_by_user_id, users.id))
      .leftJoin(class_schedules, eq(class_bookings.schedule_id, class_schedules.id))
      .leftJoin(locations, eq(class_schedules.location_id, locations.id))
      .where(eq(class_bookings.tenant_id, tenantId))
      .orderBy(desc(class_bookings.createdAt));
  }

  /**
   * Get class booking by ID
   */
  static async getById(id: string): Promise<ClassBookingWithRelations | null> {
    const [booking] = await db
      .select({
        id: class_bookings.id,
        tenantId: class_bookings.tenant_id,
        scheduleId: class_bookings.schedule_id,
        classId: class_bookings.class_id,
        customerId: class_bookings.customer_id,
        bookedByUserId: class_bookings.booked_by_user_id,
        bookedByCustomerId: class_bookings.booked_by_customer_id,
        status: class_bookings.status,
        isWaitlist: class_bookings.is_waitlist,
        waitlistPosition: class_bookings.waitlist_position,
        paymentStatus: class_bookings.payment_status,
        paymentMethod: class_bookings.payment_method,
        creditsUsed: class_bookings.credits_used,
        bookingTime: class_bookings.booking_time,
        checkInTime: class_bookings.check_in_time,
        cancelTime: class_bookings.cancel_time,
        cancellationReason: class_bookings.cancellation_reason,
        notes: class_bookings.notes,
        createdAt: class_bookings.createdAt,
        updatedAt: class_bookings.updatedAt,
        // Relations
        className: classes.name,
        customerName: customers.name,
        customerEmail: customers.email,
        bookedByUserName: users.name,
        scheduleStartTime: class_schedules.start_time,
        scheduleEndTime: class_schedules.end_time,
        locationName: locations.name,
      })
      .from(class_bookings)
      .leftJoin(classes, eq(class_bookings.class_id, classes.id))
      .leftJoin(customers, eq(class_bookings.customer_id, customers.id))
      .leftJoin(users, eq(class_bookings.booked_by_user_id, users.id))
      .leftJoin(class_schedules, eq(class_bookings.schedule_id, class_schedules.id))
      .leftJoin(locations, eq(class_schedules.location_id, locations.id))
      .where(eq(class_bookings.id, id))
      .limit(1);
    
    return booking || null;
  }

  /**
   * Create new class booking
   */
  static async create(data: CreateClassBookingData): Promise<ClassBooking> {
    const [booking] = await db
      .insert(class_bookings)
      .values({
        id: createId(),
        tenant_id: data.tenantId,
        schedule_id: data.scheduleId,
        class_id: data.classId,
        customer_id: data.customerId,
        booked_by_user_id: data.bookedByUserId || null,
        booked_by_customer_id: data.bookedByCustomerId || null,
        status: data.status || "booked",
        is_waitlist: data.isWaitlist || false,
        waitlist_position: data.waitlistPosition || null,
        payment_status: data.paymentStatus || null,
        payment_method: data.paymentMethod || null,
        credits_used: data.creditsUsed || null,
        notes: data.notes || null,
        booking_time: new Date(),
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning();
    
    return booking;
  }

  /**
   * Update class booking
   */
  static async update(
    id: string,
    data: UpdateClassBookingData
  ): Promise<ClassBooking | null> {
    const [booking] = await db
      .update(class_bookings)
      .set({
        ...data,
        updatedAt: new Date(),
      })
      .where(eq(class_bookings.id, id))
      .returning();
    
    return booking || null;
  }

  /**
   * Delete class booking
   */
  static async delete(id: string): Promise<ClassBooking> {
    // First check if booking exists
    const existingBooking = await this.getById(id);
    if (!existingBooking) {
      throw new Error("Class booking not found");
    }

    // Delete the booking and return the deleted record
    const [deletedBooking] = await db
      .delete(class_bookings)
      .where(eq(class_bookings.id, id))
      .returning();

    if (!deletedBooking) {
      throw new Error("Failed to delete class booking");
    }

    return deletedBooking;
  }

  /**
   * Search class bookings dengan filtering dan pagination
   *
   * Method utama untuk ambil data bookings dengan berbagai filter:
   * - tenantId: wajib untuk tenant isolation
   * - search: optional, search berdasarkan customer name, class name, atau notes
   * - status: optional, filter berdasarkan status booking
   * - scheduleId: optional, filter berdasarkan schedule tertentu
   * - classId: optional, filter berdasarkan class tertentu
   * - customerId: optional, filter berdasarkan customer tertentu
   * - isWaitlist: optional, filter booking waitlist atau regular
   * - paymentStatus: optional, filter berdasarkan status payment
   * - dateFrom/dateTo: optional, filter berdasarkan tanggal booking
   * - limit & offset: untuk pagination
   */
  static async searchBookings(
    filters: ClassBookingFilters,
    limit = 20,
    offset = 0
  ): Promise<ClassBookingSearchResult> {
    try {
      // Build where conditions
      const conditions = [];

      // Tenant isolation - WAJIB
      if (filters.tenantId) {
        conditions.push(eq(class_bookings.tenant_id, filters.tenantId));
      }

      // Status filter
      if (filters.status) {
        conditions.push(eq(class_bookings.status, filters.status));
      }

      // Schedule filter
      if (filters.scheduleId) {
        conditions.push(eq(class_bookings.schedule_id, filters.scheduleId));
      }

      // Class filter
      if (filters.classId) {
        conditions.push(eq(class_bookings.class_id, filters.classId));
      }

      // Customer filter
      if (filters.customerId) {
        conditions.push(eq(class_bookings.customer_id, filters.customerId));
      }

      // Waitlist filter
      if (filters.isWaitlist !== undefined) {
        conditions.push(eq(class_bookings.is_waitlist, filters.isWaitlist));
      }

      // Payment status filter
      if (filters.paymentStatus) {
        conditions.push(eq(class_bookings.payment_status, filters.paymentStatus));
      }

      // Date range filter
      if (filters.dateFrom) {
        conditions.push(sql`${class_bookings.booking_time} >= ${filters.dateFrom}`);
      }
      if (filters.dateTo) {
        conditions.push(sql`${class_bookings.booking_time} <= ${filters.dateTo}`);
      }

      // Base query dengan relations
      let query = db
        .select({
          id: class_bookings.id,
          tenantId: class_bookings.tenant_id,
          scheduleId: class_bookings.schedule_id,
          classId: class_bookings.class_id,
          customerId: class_bookings.customer_id,
          bookedByUserId: class_bookings.booked_by_user_id,
          bookedByCustomerId: class_bookings.booked_by_customer_id,
          status: class_bookings.status,
          isWaitlist: class_bookings.is_waitlist,
          waitlistPosition: class_bookings.waitlist_position,
          paymentStatus: class_bookings.payment_status,
          paymentMethod: class_bookings.payment_method,
          creditsUsed: class_bookings.credits_used,
          bookingTime: class_bookings.booking_time,
          checkInTime: class_bookings.check_in_time,
          cancelTime: class_bookings.cancel_time,
          cancellationReason: class_bookings.cancellation_reason,
          notes: class_bookings.notes,
          createdAt: class_bookings.createdAt,
          updatedAt: class_bookings.updatedAt,
          // Relations
          className: classes.name,
          customerName: customers.name,
          customerEmail: customers.email,
          bookedByUserName: users.name,
          scheduleStartTime: class_schedules.start_time,
          scheduleEndTime: class_schedules.end_time,
          locationName: locations.name,
        })
        .from(class_bookings)
        .leftJoin(classes, eq(class_bookings.class_id, classes.id))
        .leftJoin(customers, eq(class_bookings.customer_id, customers.id))
        .leftJoin(users, eq(class_bookings.booked_by_user_id, users.id))
        .leftJoin(class_schedules, eq(class_bookings.schedule_id, class_schedules.id))
        .leftJoin(locations, eq(class_schedules.location_id, locations.id));

      // Apply conditions
      if (conditions.length > 0) {
        query = query.where(and(...conditions));
      }

      // Search functionality
      if (filters.search) {
        const searchConditions = [
          ilike(customers.name, `%${filters.search}%`),
          ilike(customers.email, `%${filters.search}%`),
          ilike(classes.name, `%${filters.search}%`),
          ilike(class_bookings.notes, `%${filters.search}%`),
        ];

        if (conditions.length > 0) {
          query = query.where(and(and(...conditions), sql`(${searchConditions.join(' OR ')})`));
        } else {
          query = query.where(sql`(${searchConditions.join(' OR ')})`);
        }
      }

      // Get total count untuk pagination
      const totalQuery = db
        .select({ count: sql<number>`count(*)` })
        .from(class_bookings)
        .leftJoin(classes, eq(class_bookings.class_id, classes.id))
        .leftJoin(customers, eq(class_bookings.customer_id, customers.id))
        .leftJoin(users, eq(class_bookings.booked_by_user_id, users.id))
        .leftJoin(class_schedules, eq(class_bookings.schedule_id, class_schedules.id))
        .leftJoin(locations, eq(class_schedules.location_id, locations.id));

      if (conditions.length > 0) {
        totalQuery.where(and(...conditions));
      }

      if (filters.search) {
        const searchConditions = [
          ilike(customers.name, `%${filters.search}%`),
          ilike(customers.email, `%${filters.search}%`),
          ilike(classes.name, `%${filters.search}%`),
          ilike(class_bookings.notes, `%${filters.search}%`),
        ];

        if (conditions.length > 0) {
          totalQuery.where(and(and(...conditions), sql`(${searchConditions.join(' OR ')})`));
        } else {
          totalQuery.where(sql`(${searchConditions.join(' OR ')})`);
        }
      }

      // Execute queries
      const [bookings, [{ count: total }]] = await Promise.all([
        query
          .orderBy(desc(class_bookings.createdAt))
          .limit(limit)
          .offset(offset),
        totalQuery
      ]);

      return {
        bookings: bookings as ClassBookingWithRelations[],
        total,
        hasMore: offset + limit < total,
      };
    } catch (error) {
      console.error("Error searching class bookings:", error);
      throw new Error("Failed to search class bookings");
    }
  }

  /**
   * Get class booking statistics untuk dashboard dan analytics
   */
  static async getStats(tenantId?: number): Promise<ClassBookingStats> {
    try {
      // Base query conditions
      const conditions = tenantId ? [eq(class_bookings.tenant_id, tenantId)] : [];

      // Get total bookings
      const [totalResult] = await db
        .select({ count: sql<number>`count(*)` })
        .from(class_bookings)
        .where(conditions.length > 0 ? and(...conditions) : undefined);

      // Get bookings by status
      const statusResults = await db
        .select({
          status: class_bookings.status,
          count: sql<number>`count(*)`
        })
        .from(class_bookings)
        .where(conditions.length > 0 ? and(...conditions) : undefined)
        .groupBy(class_bookings.status);

      // Get bookings by payment status
      const paymentStatusResults = await db
        .select({
          paymentStatus: class_bookings.payment_status,
          count: sql<number>`count(*)`
        })
        .from(class_bookings)
        .where(conditions.length > 0 ? and(...conditions) : undefined)
        .groupBy(class_bookings.payment_status);

      // Get bookings by tenant (jika tidak ada tenantId filter)
      const tenantResults = tenantId ? [] : await db
        .select({
          tenantId: class_bookings.tenant_id,
          status: class_bookings.status,
          count: sql<number>`count(*)`
        })
        .from(class_bookings)
        .groupBy(class_bookings.tenant_id, class_bookings.status);

      // Process results
      const total = totalResult?.count || 0;

      const byStatus: Record<string, number> = {};
      let booked = 0, checkedIn = 0, cancelled = 0, waitlisted = 0, noShow = 0, completed = 0;

      statusResults.forEach(result => {
        const status = result.status || 'unknown';
        const count = result.count;
        byStatus[status] = count;

        switch (status) {
          case 'booked': booked = count; break;
          case 'checked_in': checkedIn = count; break;
          case 'cancelled': cancelled = count; break;
          case 'waitlisted': waitlisted = count; break;
          case 'no_show': noShow = count; break;
          case 'completed': completed = count; break;
        }
      });

      const byPaymentStatus: Record<string, number> = {};
      paymentStatusResults.forEach(result => {
        const status = result.paymentStatus || 'unknown';
        byPaymentStatus[status] = result.count;
      });

      const byTenant: Record<number, { total: number; booked: number; checkedIn: number; cancelled: number; }> = {};
      if (!tenantId) {
        tenantResults.forEach(result => {
          const tid = result.tenantId;
          if (!byTenant[tid]) {
            byTenant[tid] = { total: 0, booked: 0, checkedIn: 0, cancelled: 0 };
          }

          byTenant[tid].total += result.count;

          switch (result.status) {
            case 'booked': byTenant[tid].booked = result.count; break;
            case 'checked_in': byTenant[tid].checkedIn = result.count; break;
            case 'cancelled': byTenant[tid].cancelled = result.count; break;
          }
        });
      }

      return {
        total,
        booked,
        checkedIn,
        cancelled,
        waitlisted,
        noShow,
        completed,
        byStatus,
        byPaymentStatus,
        byTenant,
      };
    } catch (error) {
      console.error("Error getting class booking stats:", error);
      throw new Error("Failed to get class booking statistics");
    }
  }

  /**
   * Bulk operations untuk class bookings
   */
  static async bulkUpdateStatus(
    ids: string[],
    status: string,
    tenantId: number
  ): Promise<ClassBooking[]> {
    try {
      const updatedBookings = await db
        .update(class_bookings)
        .set({
          status,
          updatedAt: new Date(),
        })
        .where(
          and(
            sql`${class_bookings.id} = ANY(${ids})`,
            eq(class_bookings.tenant_id, tenantId)
          )
        )
        .returning();

      return updatedBookings;
    } catch (error) {
      console.error("Error bulk updating booking status:", error);
      throw new Error("Failed to bulk update booking status");
    }
  }

  /**
   * Bulk delete class bookings
   */
  static async bulkDelete(ids: string[], tenantId: number): Promise<ClassBooking[]> {
    try {
      const deletedBookings = await db
        .delete(class_bookings)
        .where(
          and(
            sql`${class_bookings.id} = ANY(${ids})`,
            eq(class_bookings.tenant_id, tenantId)
          )
        )
        .returning();

      return deletedBookings;
    } catch (error) {
      console.error("Error bulk deleting bookings:", error);
      throw new Error("Failed to bulk delete bookings");
    }
  }

  /**
   * Check-in customer untuk class booking
   */
  static async checkIn(id: string, tenantId: number): Promise<ClassBooking | null> {
    try {
      const [booking] = await db
        .update(class_bookings)
        .set({
          status: "checked_in",
          check_in_time: new Date(),
          updatedAt: new Date(),
        })
        .where(
          and(
            eq(class_bookings.id, id),
            eq(class_bookings.tenant_id, tenantId)
          )
        )
        .returning();

      return booking || null;
    } catch (error) {
      console.error("Error checking in booking:", error);
      throw new Error("Failed to check in booking");
    }
  }

  /**
   * Cancel class booking
   */
  static async cancel(
    id: string,
    tenantId: number,
    reason?: string
  ): Promise<ClassBooking | null> {
    try {
      const [booking] = await db
        .update(class_bookings)
        .set({
          status: "cancelled",
          cancel_time: new Date(),
          cancellation_reason: reason || null,
          updatedAt: new Date(),
        })
        .where(
          and(
            eq(class_bookings.id, id),
            eq(class_bookings.tenant_id, tenantId)
          )
        )
        .returning();

      return booking || null;
    } catch (error) {
      console.error("Error cancelling booking:", error);
      throw new Error("Failed to cancel booking");
    }
  }

  /**
   * Get bookings by schedule ID untuk melihat siapa saja yang booking class tertentu
   */
  static async getByScheduleId(scheduleId: string, tenantId: number): Promise<ClassBookingWithRelations[]> {
    return await db
      .select({
        id: class_bookings.id,
        tenantId: class_bookings.tenant_id,
        scheduleId: class_bookings.schedule_id,
        classId: class_bookings.class_id,
        customerId: class_bookings.customer_id,
        bookedByUserId: class_bookings.booked_by_user_id,
        bookedByCustomerId: class_bookings.booked_by_customer_id,
        status: class_bookings.status,
        isWaitlist: class_bookings.is_waitlist,
        waitlistPosition: class_bookings.waitlist_position,
        paymentStatus: class_bookings.payment_status,
        paymentMethod: class_bookings.payment_method,
        creditsUsed: class_bookings.credits_used,
        bookingTime: class_bookings.booking_time,
        checkInTime: class_bookings.check_in_time,
        cancelTime: class_bookings.cancel_time,
        cancellationReason: class_bookings.cancellation_reason,
        notes: class_bookings.notes,
        createdAt: class_bookings.createdAt,
        updatedAt: class_bookings.updatedAt,
        // Relations
        className: classes.name,
        customerName: customers.name,
        customerEmail: customers.email,
        bookedByUserName: users.name,
        scheduleStartTime: class_schedules.start_time,
        scheduleEndTime: class_schedules.end_time,
        locationName: locations.name,
      })
      .from(class_bookings)
      .leftJoin(classes, eq(class_bookings.class_id, classes.id))
      .leftJoin(customers, eq(class_bookings.customer_id, customers.id))
      .leftJoin(users, eq(class_bookings.booked_by_user_id, users.id))
      .leftJoin(class_schedules, eq(class_bookings.schedule_id, class_schedules.id))
      .leftJoin(locations, eq(class_schedules.location_id, locations.id))
      .where(
        and(
          eq(class_bookings.schedule_id, scheduleId),
          eq(class_bookings.tenant_id, tenantId)
        )
      )
      .orderBy(asc(class_bookings.waitlist_position), desc(class_bookings.booking_time));
  }

  /**
   * Get bookings by customer ID untuk melihat history booking customer
   */
  static async getByCustomerId(customerId: string, tenantId: number): Promise<ClassBookingWithRelations[]> {
    return await db
      .select({
        id: class_bookings.id,
        tenantId: class_bookings.tenant_id,
        scheduleId: class_bookings.schedule_id,
        classId: class_bookings.class_id,
        customerId: class_bookings.customer_id,
        bookedByUserId: class_bookings.booked_by_user_id,
        bookedByCustomerId: class_bookings.booked_by_customer_id,
        status: class_bookings.status,
        isWaitlist: class_bookings.is_waitlist,
        waitlistPosition: class_bookings.waitlist_position,
        paymentStatus: class_bookings.payment_status,
        paymentMethod: class_bookings.payment_method,
        creditsUsed: class_bookings.credits_used,
        bookingTime: class_bookings.booking_time,
        checkInTime: class_bookings.check_in_time,
        cancelTime: class_bookings.cancel_time,
        cancellationReason: class_bookings.cancellation_reason,
        notes: class_bookings.notes,
        createdAt: class_bookings.createdAt,
        updatedAt: class_bookings.updatedAt,
        // Relations
        className: classes.name,
        customerName: customers.name,
        customerEmail: customers.email,
        bookedByUserName: users.name,
        scheduleStartTime: class_schedules.start_time,
        scheduleEndTime: class_schedules.end_time,
        locationName: locations.name,
      })
      .from(class_bookings)
      .leftJoin(classes, eq(class_bookings.class_id, classes.id))
      .leftJoin(customers, eq(class_bookings.customer_id, customers.id))
      .leftJoin(users, eq(class_bookings.booked_by_user_id, users.id))
      .leftJoin(class_schedules, eq(class_bookings.schedule_id, class_schedules.id))
      .leftJoin(locations, eq(class_schedules.location_id, locations.id))
      .where(
        and(
          eq(class_bookings.customer_id, customerId),
          eq(class_bookings.tenant_id, tenantId)
        )
      )
      .orderBy(desc(class_bookings.createdAt));
  }
}
