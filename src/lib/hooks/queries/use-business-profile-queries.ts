"use client";

import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { BusinessProfile, NewBusinessProfile } from "@/lib/db/schema";

// Import global query keys
import { businessProfileKeys } from "./query-keys";

// API Functions
const businessProfileApi = {
  getByTenant: async (tenantId: number): Promise<BusinessProfile | null> => {
    const response = await fetch(`/api/business-profiles?tenantId=${tenantId}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch business profile: ${response.statusText}`);
    }
    const data = await response.json();
    return data.profile;
  },

  getAll: async (): Promise<BusinessProfile[]> => {
    const response = await fetch('/api/business-profiles');
    if (!response.ok) {
      throw new Error(`Failed to fetch business profiles: ${response.statusText}`);
    }
    const data = await response.json();
    return data.profiles;
  },

  create: async (data: Omit<NewBusinessProfile, "id" | "createdAt" | "updatedAt">): Promise<BusinessProfile> => {
    const response = await fetch('/api/business-profiles', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to create business profile');
    }
    const result = await response.json();
    return result.profile;
  },

  update: async ({ tenantId, data }: { 
    tenantId: number; 
    data: Partial<NewBusinessProfile> 
  }): Promise<BusinessProfile> => {
    const response = await fetch(`/api/business-profiles/${tenantId}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to update business profile');
    }
    const result = await response.json();
    return result.profile;
  },

  delete: async (tenantId: number): Promise<void> => {
    const response = await fetch(`/api/business-profiles/${tenantId}`, {
      method: 'DELETE',
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to delete business profile');
    }
  },

  updateLogo: async ({ tenantId, logoUrl }: { tenantId: number; logoUrl: string }): Promise<BusinessProfile> => {
    return businessProfileApi.update({ tenantId, data: { business_logo: logoUrl } });
  },

  getStats: async (): Promise<{
    totalProfiles: number;
    profilesWithLogo: number;
    profilesWithWhatsApp: number;
  }> => {
    const response = await fetch('/api/business-profiles/stats');
    if (!response.ok) {
      throw new Error('Failed to fetch business profile stats');
    }
    return response.json();
  },
};

// Query Hooks with backward compatibility
export function useBusinessProfile(tenantIdOrOptions: number | null | { tenantId?: number }) {
  const tenantId = typeof tenantIdOrOptions === 'number'
    ? tenantIdOrOptions
    : (tenantIdOrOptions?.tenantId || null);

  const query = useQuery({
    queryKey: businessProfileKeys.detail(tenantId || 0),
    queryFn: () => businessProfileApi.getByTenant(tenantId!),
    enabled: !!tenantId && tenantId > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  // Return format compatible with old hook
  return {
    profile: query.data || null,
    loading: query.isLoading,
    error: query.error?.message || null,
    isLoading: query.isLoading,
    isError: query.isError,
    data: query.data,
    refetch: query.refetch,
  };
}

export function useBusinessProfiles() {
  const query = useQuery({
    queryKey: businessProfileKeys.lists(),
    queryFn: businessProfileApi.getAll,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });

  // Return format compatible with old hook
  return {
    profiles: query.data || [],
    loading: query.isLoading,
    error: query.error?.message || null,
    isLoading: query.isLoading,
    isError: query.isError,
    data: query.data,
    refetch: query.refetch,
  };
}

export function useBusinessProfileStats() {
  return useQuery({
    queryKey: businessProfileKeys.stats(),
    queryFn: businessProfileApi.getStats,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Mutation Hooks
export function useCreateBusinessProfile() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: businessProfileApi.create,
    onSuccess: (newProfile) => {
      // Invalidate and refetch
      queryClient.invalidateQueries({ queryKey: businessProfileKeys.all });
      
      // Optimistically update the cache
      queryClient.setQueryData(
        businessProfileKeys.detail(newProfile.tenantId!),
        newProfile
      );
    },
    onError: (error) => {
      console.error('Failed to create business profile:', error);
    },
  });
}

export function useUpdateBusinessProfile() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: businessProfileApi.update,
    onMutate: async ({ tenantId, data }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: businessProfileKeys.detail(tenantId) });

      // Snapshot the previous value
      const previousProfile = queryClient.getQueryData(businessProfileKeys.detail(tenantId));

      // Optimistically update to the new value
      queryClient.setQueryData(businessProfileKeys.detail(tenantId), (old: BusinessProfile | undefined) => {
        if (!old) return old;
        return { ...old, ...data, updatedAt: new Date() };
      });

      // Return a context object with the snapshotted value
      return { previousProfile, tenantId };
    },
    onError: (_err, _variables, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousProfile) {
        queryClient.setQueryData(
          businessProfileKeys.detail(context.tenantId),
          context.previousProfile
        );
      }
    },
    onSettled: (_data, _error, variables) => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: businessProfileKeys.detail(variables.tenantId) });
      queryClient.invalidateQueries({ queryKey: businessProfileKeys.lists() });
    },
  });
}

export function useDeleteBusinessProfile() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: businessProfileApi.delete,
    onSuccess: (_, tenantId) => {
      // Remove from cache
      queryClient.removeQueries({ queryKey: businessProfileKeys.detail(tenantId) });
      
      // Invalidate lists
      queryClient.invalidateQueries({ queryKey: businessProfileKeys.lists() });
      queryClient.invalidateQueries({ queryKey: businessProfileKeys.stats() });
    },
  });
}

export function useUpdateBusinessLogo() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: businessProfileApi.updateLogo,
    onSuccess: (updatedProfile) => {
      // Update the cache
      queryClient.setQueryData(
        businessProfileKeys.detail(updatedProfile.tenantId!),
        updatedProfile
      );
      
      // Invalidate lists to ensure consistency
      queryClient.invalidateQueries({ queryKey: businessProfileKeys.lists() });
    },
  });
}
