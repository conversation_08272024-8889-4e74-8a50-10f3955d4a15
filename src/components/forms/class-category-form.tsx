"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, FolderOpen, CheckCircle } from "lucide-react";
import { ClassCategory } from "@/lib/db/schema";

// Validation schema
const classCategorySchema = z.object({
  tenantId: z.number().int().positive(),
  name: z.string().min(1, "Name is required").max(255),
});

type ClassCategoryFormData = z.infer<typeof classCategorySchema>;

interface ClassCategoryFormProps {
  category?: ClassCategory;
  tenantId: number;
  onSubmit: (data: { tenantId: number; name: string }) => Promise<void>;
  onCancel?: () => void;
  isLoading?: boolean;
  className?: string;
}

export function ClassCategoryForm({
  category,
  tenantId,
  onSubmit,
  onCancel,
  isLoading = false,
  className = "",
}: ClassCategoryFormProps) {
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [isSuccess, setIsSuccess] = useState(false);

  const form = useForm<ClassCategoryFormData>({
    resolver: zodResolver(classCategorySchema),
    defaultValues: {
      tenantId,
      name: category?.name || "",
    },
  });

  const handleSubmit = async (data: ClassCategoryFormData) => {
    try {
      setSubmitError(null);
      await onSubmit({
        tenantId: data.tenantId,
        name: data.name,
      });
      setIsSuccess(true);
      setTimeout(() => setIsSuccess(false), 2000);
    } catch (error) {
      console.error("Form submission error:", error);
      setSubmitError(error instanceof Error ? error.message : "An error occurred");
    }
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FolderOpen className="h-5 w-5" />
          {category ? "Edit Class Category" : "Create Class Category"}
        </CardTitle>
        <CardDescription>
          {category
            ? "Update the class category information"
            : "Add a new category to organize your classes"
          }
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
          {/* Success Alert */}
          {isSuccess && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className="mb-4"
            >
              <Alert className="border-green-200 bg-green-50">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <AlertDescription className="text-green-800">
                  Class category {category ? "updated" : "created"} successfully!
                </AlertDescription>
              </Alert>
            </motion.div>
          )}

          {/* Error Alert */}
          {submitError && (
            <Alert variant="destructive">
              <AlertDescription>{submitError}</AlertDescription>
            </Alert>
          )}

          {/* Category Name */}
          <div className="space-y-2">
            <Label htmlFor="name">Category Name *</Label>
            <Input
              id="name"
              placeholder="Enter class category name (e.g., Yoga, Fitness, Dance)"
              {...form.register("name")}
              className={form.formState.errors.name ? "border-red-500" : ""}
            />
            {form.formState.errors.name && (
              <p className="text-sm text-red-500">
                {form.formState.errors.name.message}
              </p>
            )}
          </div>



          {/* Buttons */}
          <div className="flex gap-2 pt-4">
            <Button
              type="submit"
              disabled={isLoading || form.formState.isSubmitting}
              className="flex-1"
            >
              {(isLoading || form.formState.isSubmitting) && (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              )}
              {category ? "Update Category" : "Create Category"}
            </Button>
            {onCancel && (
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isLoading || form.formState.isSubmitting}
              >
                Cancel
              </Button>
            )}
          </div>
        </form>
      </CardContent>
    </Card>
  );
}

// Export types dan schema
export type { ClassCategoryFormData, ClassCategoryFormProps };
export { classCategorySchema };
