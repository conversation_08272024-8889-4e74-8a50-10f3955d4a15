import { PublicClassDTO } from '../dto/ClassDTO';
import { db } from '@/lib/db';
import { classes, class_categories } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';

interface GetClassByIdQuery {
  id: string;
  tenantId?: string;
}

export async function getClassByIdQueryHandler(query: GetClassByIdQuery): Promise<PublicClassDTO | null> {
  try {
    // Build where conditions
    let whereConditions = eq(classes.id, query.id);

    if (query.tenantId) {
      whereConditions = and(
        whereConditions,
        eq(classes.tenantId, parseInt(query.tenantId, 10))
      ) as any;
    }

    // Query with JOIN to get category name
    const [classResult] = await db
      .select({
        id: classes.id,
        name: classes.name,
        description: classes.description,
        categoryId: classes.categoryId,
        categoryName: class_categories.name,
        is_active: classes.is_active,
        createdAt: classes.createdAt,
        updatedAt: classes.updatedAt,
      })
      .from(classes)
      .leftJoin(class_categories, eq(classes.categoryId, class_categories.id))
      .where(whereConditions)
      .limit(1);

    if (!classResult) return null;

    // Transform to PublicClassDTO format
    return {
      id: classResult.id,
      name: classResult.name,
      description: classResult.description || '',
      category: classResult.categoryName || 'Uncategorized', // Use category name for display
      categoryId: classResult.categoryId, // Keep categoryId for reference
      startDate: new Date().toISOString(), // TODO: Use actual dates from schema
      endDate: new Date().toISOString(), // TODO: Use actual dates from schema
      isActive: classResult.is_active,
      createdAt: classResult.createdAt.toISOString(),
      updatedAt: classResult.updatedAt.toISOString(),
    };
  } catch (error) {
    console.error('Error in getClassByIdQueryHandler:', error);
    throw error;
  }
}
