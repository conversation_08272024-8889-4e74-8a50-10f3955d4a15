"use client";

import React, { useState } from "react";
import { z } from "zod";
import { useForm } from "@tanstack/react-form";
import { DollarSign, Package, Users, CreditCard, Coins, Globe, FileText, AlertCircle, Plus } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { PackagePricing } from "@/lib/db/schema";
import { usePackagesByTenant } from "@/lib/hooks/queries/use-package-queries";
import { usePricingGroupsByTenant, useCreatePricingGroup } from "@/lib/hooks/queries/use-pricing-group-queries";
import { PricingGroupForm } from "./pricing-group-form";

// Form validation schema
const packagePricingSchema = z.object({
  packageId: z.string().min(1, "Package is required"),
  pricingGroupId: z.string().min(1, "Pricing group is required"),
  price: z.number().min(0, "Price cannot be negative").optional(),
  creditAmount: z.number().int().min(0, "Credit amount cannot be negative").optional(),
  currency: z.string().min(1, "Currency is required").max(10, "Currency code too long").default("USD"),
}).refine(
  (data) => data.price !== undefined || data.creditAmount !== undefined,
  {
    message: "Either price or credit amount must be specified",
    path: ["price"],
  }
);

type PackagePricingData = z.infer<typeof packagePricingSchema>;

// Currency options
const currencyOptions = [
  { value: "USD", label: "USD - US Dollar" },
  { value: "EUR", label: "EUR - Euro" },
  { value: "GBP", label: "GBP - British Pound" },
  { value: "JPY", label: "JPY - Japanese Yen" },
  { value: "CAD", label: "CAD - Canadian Dollar" },
  { value: "AUD", label: "AUD - Australian Dollar" },
  { value: "CHF", label: "CHF - Swiss Franc" },
  { value: "CNY", label: "CNY - Chinese Yuan" },
  { value: "INR", label: "INR - Indian Rupee" },
  { value: "IDR", label: "IDR - Indonesian Rupiah" },
];

// Get packages for dropdown
function usePackageOptions(tenantId: number) {
  const { data: packagesData, isLoading } = usePackagesByTenant(tenantId);
  const packages = packagesData || [];

  console.log('📦 [PackageOptions] Packages data:', packages);

  const options = packages.map(pkg => ({
    value: pkg.id,
    label: `${pkg.name}${pkg.description ? ` - ${pkg.description}` : ''}`,
  }));

  console.log('📦 [PackageOptions] Dropdown options:', options);

  return {
    options,
    isLoading,
  };
}

// Get pricing groups for dropdown
function usePricingGroupOptions(tenantId: number) {
  const { data: pricingGroupsData, isLoading, refetch } = usePricingGroupsByTenant(tenantId);
  const pricingGroups = pricingGroupsData || [];

  return {
    options: pricingGroups.map(group => ({
      value: group.id,
      label: `${group.name}${group.discountPercentage ? ` (${group.discountPercentage}% discount)` : ''}`,
    })),
    isLoading,
    refetch,
  };
}



// Enhanced form component with dynamic options
interface PackagePricingFormProps {
  entity?: PackagePricing;
  onSubmit: (data: PackagePricingData) => Promise<void>;
  onCancel: () => void;
  tenantId?: number;
  className?: string;
  title?: string;
  description?: string;
}

export function PackagePricingForm({
  entity,
  onSubmit,
  onCancel,
  tenantId = 1,
  className = "",
  title = "Package Pricing",
  description = "Configure pricing for packages across different pricing groups"
}: PackagePricingFormProps) {
  const [isAddPricingGroupModalOpen, setIsAddPricingGroupModalOpen] = useState(false);
  const { options: packageOptions, isLoading: packagesLoading } = usePackageOptions(tenantId);
  const { options: pricingGroupOptions, isLoading: pricingGroupsLoading, refetch: refetchPricingGroups } = usePricingGroupOptions(tenantId);
  const createPricingGroupMutation = useCreatePricingGroup();

  const form = useForm({
    defaultValues: {
      packageId: entity?.package_id || "",
      pricingGroupId: entity?.pricing_group_id || "",
      price: entity?.price || undefined,
      creditAmount: entity?.credit_amount || undefined,
      currency: entity?.currency || "USD",
    },
    onSubmit: async ({ value }) => {
      try {
        const validatedData = packagePricingSchema.parse(value);
        await onSubmit(validatedData);
      } catch (error) {
        console.error("Form submission error:", error);
      }
    },
  });

  // Handle creating new pricing group
  const handleCreatePricingGroup = async (data: any) => {
    try {
      const newPricingGroup = await createPricingGroupMutation.mutateAsync(data);

      // Refresh pricing groups list
      await refetchPricingGroups();

      // Auto-select the newly created pricing group
      form.setFieldValue("pricingGroupId", newPricingGroup.id);

      // Close modal
      setIsAddPricingGroupModalOpen(false);
    } catch (error) {
      console.error("Error creating pricing group:", error);
      // Error will be handled by the form's error handling
    }
  };

  if (packagesLoading || pricingGroupsLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <DollarSign className="h-5 w-5" />
          {title}
        </CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>

      <CardContent>
        <form
          onSubmit={(e) => {
            e.preventDefault();
            e.stopPropagation();
            form.handleSubmit();
          }}
          className="space-y-6"
        >
          {/* Package Information Section */}
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-medium">Package Selection</h3>
              <p className="text-sm text-muted-foreground">
                Select the package for pricing configuration
              </p>
            </div>

            <div className="grid grid-cols-1 gap-4">
              {/* Package */}
              <form.Field
                name="packageId"
                validators={{
                  onChange: (value) => {
                    if (!value || (typeof value === 'string' && value.trim() === '')) {
                      return 'Package is required';
                    }
                    return undefined;
                  },
                }}
                children={(field) => (
                  <div className="space-y-2">
                    <Label htmlFor={field.name} className="flex items-center gap-2">
                      <Package className="h-4 w-4" />
                      Package *
                    </Label>
                    <Select
                      value={field.state.value}
                      onValueChange={field.handleChange}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select package" />
                      </SelectTrigger>
                      <SelectContent>
                        {packageOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {field.state.meta.errors.length > 0 && (
                      <div className="flex items-center gap-2 text-sm text-destructive">
                        <AlertCircle className="h-4 w-4" />
                        {field.state.meta.errors[0]}
                      </div>
                    )}
                    <p className="text-xs text-muted-foreground">
                      Choose the package for this pricing
                    </p>
                  </div>
                )}
              />
            </div>
          </div>

          {/* Pricing Configuration Section */}
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-medium">Pricing Configuration</h3>
              <p className="text-sm text-muted-foreground">
                Select pricing group and set the price and/or credit amount
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Pricing Group */}
              <form.Field
                name="pricingGroupId"
                validators={{
                  onChange: (value) => {
                    if (!value || (typeof value === 'string' && value.trim() === '')) {
                      return 'Pricing group is required';
                    }
                    return undefined;
                  },
                }}
                children={(field) => (
                  <div className="space-y-2">
                    <Label htmlFor={field.name} className="flex items-center gap-2">
                      <Users className="h-4 w-4" />
                      Pricing Group *
                    </Label>
                    <Select
                      value={field.state.value}
                      onValueChange={(value) => {
                        if (value === "add-new") {
                          setIsAddPricingGroupModalOpen(true);
                        } else {
                          field.handleChange(value);
                        }
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select pricing group" />
                      </SelectTrigger>
                      <SelectContent>
                        {pricingGroupOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                        {pricingGroupOptions.length > 0 && (
                          <div className="border-t border-border my-1" />
                        )}
                        <SelectItem
                          value="add-new"
                          className="text-primary font-medium"
                        >
                          <div className="flex items-center gap-2">
                            <Plus className="h-4 w-4" />
                            Add New Pricing Group
                          </div>
                        </SelectItem>
                      </SelectContent>
                    </Select>
                    {field.state.meta.errors.length > 0 && (
                      <div className="flex items-center gap-2 text-sm text-destructive">
                        <AlertCircle className="h-4 w-4" />
                        {field.state.meta.errors[0]}
                      </div>
                    )}
                    <p className="text-xs text-muted-foreground">
                      Choose the pricing group that will receive this pricing
                    </p>
                  </div>
                )}
              />

              {/* Currency */}
              <form.Field
                name="currency"
                validators={{
                  onChange: (value) => {
                    if (!value || (typeof value === 'string' && value.trim() === '')) {
                      return 'Currency is required';
                    }
                    return undefined;
                  },
                }}
                children={(field) => (
                  <div className="space-y-2">
                    <Label htmlFor={field.name} className="flex items-center gap-2">
                      <Globe className="h-4 w-4" />
                      Currency *
                    </Label>
                    <Select
                      value={field.state.value}
                      onValueChange={field.handleChange}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select currency" />
                      </SelectTrigger>
                      <SelectContent>
                        {currencyOptions.map((option) => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {field.state.meta.errors.length > 0 && (
                      <div className="flex items-center gap-2 text-sm text-destructive">
                        <AlertCircle className="h-4 w-4" />
                        {field.state.meta.errors[0]}
                      </div>
                    )}
                    <p className="text-xs text-muted-foreground">
                      Currency for the price (only applicable if price is set)
                    </p>
                  </div>
                )}
              />

              {/* Price */}
              <form.Field
                name="price"
                validators={{
                  onChange: (value) => {
                    if (value !== undefined && value < 0) {
                      return "Price cannot be negative";
                    }
                    return undefined;
                  },
                }}
                children={(field) => (
                  <div className="space-y-2">
                    <Label htmlFor={field.name} className="flex items-center gap-2">
                      <DollarSign className="h-4 w-4" />
                      Price
                    </Label>
                    <Input
                      id={field.name}
                      name={field.name}
                      type="number"
                      step="0.01"
                      min="0"
                      value={field.state.value || ""}
                      onBlur={field.handleBlur}
                      onChange={(e) => {
                        const value = e.target.value;
                        field.handleChange(value ? parseFloat(value) : undefined);
                      }}
                      placeholder="Enter price (e.g., 99.99)"
                    />
                    {field.state.meta.errors.length > 0 && (
                      <div className="flex items-center gap-2 text-sm text-destructive">
                        <AlertCircle className="h-4 w-4" />
                        {field.state.meta.errors[0]}
                      </div>
                    )}
                    <p className="text-xs text-muted-foreground">
                      Monetary price for this package (leave empty if using credits only)
                    </p>
                  </div>
                )}
              />

              {/* Credit Amount */}
              <form.Field
                name="creditAmount"
                validators={{
                  onChange: (value) => {
                    if (value !== undefined && value < 0) {
                      return "Credit amount cannot be negative";
                    }
                    return undefined;
                  },
                }}
                children={(field) => (
                  <div className="space-y-2">
                    <Label htmlFor={field.name} className="flex items-center gap-2">
                      <Coins className="h-4 w-4" />
                      Credit Amount
                    </Label>
                    <Input
                      id={field.name}
                      name={field.name}
                      type="number"
                      min="0"
                      value={field.state.value || ""}
                      onBlur={field.handleBlur}
                      onChange={(e) => {
                        const value = e.target.value;
                        field.handleChange(value ? parseInt(value) : undefined);
                      }}
                      placeholder="Enter credit amount (e.g., 100)"
                    />
                    {field.state.meta.errors.length > 0 && (
                      <div className="flex items-center gap-2 text-sm text-destructive">
                        <AlertCircle className="h-4 w-4" />
                        {field.state.meta.errors[0]}
                      </div>
                    )}
                    <p className="text-xs text-muted-foreground">
                      Credit amount required for this package (leave empty if using price only)
                    </p>
                  </div>
                )}
              />
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex items-center justify-end space-x-2 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
            >
              Cancel
            </Button>
            <form.Subscribe
              selector={(state) => [state.canSubmit, state.isSubmitting]}
              children={([canSubmit, isSubmitting]) => (
                <Button
                  type="submit"
                  disabled={!canSubmit}
                  className="min-w-[100px]"
                >
                  {isSubmitting ? (
                    <div className="flex items-center gap-2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      Saving...
                    </div>
                  ) : (
                    entity ? "Update" : "Create"
                  )}
                </Button>
              )}
            />
          </div>
        </form>
      </CardContent>

      {/* Add New Pricing Group Modal */}
      <Dialog open={isAddPricingGroupModalOpen} onOpenChange={setIsAddPricingGroupModalOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Add New Pricing Group</DialogTitle>
          </DialogHeader>
          <PricingGroupForm
            tenantId={tenantId}
            onSubmit={handleCreatePricingGroup}
            onCancel={() => setIsAddPricingGroupModalOpen(false)}
          />
        </DialogContent>
      </Dialog>
    </Card>
  );
}

// Export types for external use
export type { PackagePricingData };
