import { z } from "zod";
import validator from "validator";
import { createHash } from "crypto";

/**
 * Input Validation and Sanitization for Customer Authentication
 * 
 * FAANG-level input validation with security best practices
 */

/**
 * Custom Zod validators for security
 */

// Email validation with additional security checks
export const secureEmailSchema = z
  .string()
  .min(1, "Email is required")
  .max(254, "Email is too long")
  .email("Invalid email format")
  .refine((email) => {
    // Additional validation using validator.js
    return validator.isEmail(email, {
      allow_utf8_local_part: false,
      require_tld: true,
      allow_ip_domain: false,
    });
  }, "Invalid email format")
  .refine((email) => {
    // Check for suspicious patterns
    const suspiciousPatterns = [
      /\+.*\+/, // Multiple plus signs
      /\.{2,}/, // Multiple consecutive dots
      /@.*@/, // Multiple @ symbols
      /[<>]/, // HTML brackets
    ];
    return !suspiciousPatterns.some(pattern => pattern.test(email));
  }, "Email contains suspicious characters")
  .transform((email) => email.toLowerCase().trim());

// Password validation with strength requirements
export const securePasswordSchema = z
  .string()
  .min(8, "Password must be at least 8 characters")
  .max(128, "Password is too long")
  .refine((password) => {
    // Check for minimum complexity
    const hasLowercase = /[a-z]/.test(password);
    const hasUppercase = /[A-Z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password);
    
    return hasLowercase && hasUppercase && hasNumbers && hasSpecialChar;
  }, "Password must contain at least one lowercase letter, one uppercase letter, one number, and one special character")
  .refine((password) => {
    // Check for common weak patterns
    const weakPatterns = [
      /123456/,
      /password/i,
      /qwerty/i,
      /admin/i,
      /(.)\1{3,}/, // Repeated characters
    ];
    return !weakPatterns.some(pattern => pattern.test(password));
  }, "Password contains common weak patterns");

// Tenant ID validation
export const tenantIdSchema = z
  .number()
  .int("Tenant ID must be an integer")
  .positive("Tenant ID must be positive")
  .max(999999999, "Tenant ID is too large")
  .refine((id) => {
    // Additional validation for tenant ID format
    return id > 0 && id < 1000000000;
  }, "Invalid tenant ID format");

// Device ID validation
export const deviceIdSchema = z
  .string()
  .min(1, "Device ID is required")
  .max(255, "Device ID is too long")
  .regex(/^[a-zA-Z0-9\-_]+$/, "Device ID contains invalid characters")
  .refine((deviceId) => {
    // Check for suspicious patterns
    const suspiciousPatterns = [
      /^(test|debug|admin|root)$/i,
      /script/i,
      /<.*>/,
    ];
    return !suspiciousPatterns.some(pattern => pattern.test(deviceId));
  }, "Device ID contains suspicious patterns");

// IP address validation
export const ipAddressSchema = z
  .string()
  .refine((ip) => {
    return validator.isIP(ip, 4) || validator.isIP(ip, 6);
  }, "Invalid IP address format")
  .refine((ip) => {
    // Check for private/local IPs in production
    if (process.env.NODE_ENV === "production") {
      const privateRanges = [
        /^127\./, // Loopback
        /^10\./, // Private Class A
        /^172\.(1[6-9]|2[0-9]|3[0-1])\./, // Private Class B
        /^192\.168\./, // Private Class C
        /^::1$/, // IPv6 loopback
        /^fc00:/, // IPv6 private
      ];
      
      // Allow private IPs in development/staging
      return true; // For now, allow all IPs
    }
    return true;
  }, "Invalid IP address");

// User agent validation
export const userAgentSchema = z
  .string()
  .max(1000, "User agent is too long")
  .refine((ua) => {
    // Check for suspicious patterns
    const suspiciousPatterns = [
      /<script/i,
      /javascript:/i,
      /vbscript:/i,
      /onload=/i,
      /onerror=/i,
    ];
    return !suspiciousPatterns.some(pattern => pattern.test(ua));
  }, "User agent contains suspicious content")
  .optional();

// OAuth code validation
export const oauthCodeSchema = z
  .string()
  .min(1, "OAuth code is required")
  .max(2048, "OAuth code is too long")
  .regex(/^[a-zA-Z0-9\-_\.~]+$/, "OAuth code contains invalid characters");

// OAuth state validation
export const oauthStateSchema = z
  .string()
  .min(1, "OAuth state is required")
  .max(255, "OAuth state is too long")
  .regex(/^[a-zA-Z0-9\-_]+$/, "OAuth state contains invalid characters");

// PKCE code verifier validation
export const pkceCodeVerifierSchema = z
  .string()
  .min(43, "Code verifier must be at least 43 characters")
  .max(128, "Code verifier must be at most 128 characters")
  .regex(/^[a-zA-Z0-9\-_~\.]+$/, "Code verifier contains invalid characters");

/**
 * Sanitization functions
 */

// Sanitize string input
export function sanitizeString(input: string): string {
  return validator.escape(input.trim());
}

// Sanitize HTML content
export function sanitizeHtml(input: string): string {
  // Remove all HTML tags and decode entities
  return validator.stripLow(
    validator.unescape(
      input.replace(/<[^>]*>/g, "")
    )
  );
}

// Sanitize JSON input
export function sanitizeJson(input: any): any {
  if (typeof input === "string") {
    return sanitizeString(input);
  }
  
  if (Array.isArray(input)) {
    return input.map(sanitizeJson);
  }
  
  if (input && typeof input === "object") {
    const sanitized: any = {};
    for (const [key, value] of Object.entries(input)) {
      const sanitizedKey = sanitizeString(key);
      sanitized[sanitizedKey] = sanitizeJson(value);
    }
    return sanitized;
  }
  
  return input;
}

/**
 * Rate limiting key generation
 */
export function generateRateLimitKey(
  type: string,
  identifier: string,
  additionalContext?: string
): string {
  const baseKey = `${type}:${identifier}`;
  if (additionalContext) {
    return `${baseKey}:${additionalContext}`;
  }
  return baseKey;
}

/**
 * Hash sensitive data for logging
 */
export function hashForLogging(data: string): string {
  return createHash("sha256")
    .update(data)
    .digest("hex")
    .substring(0, 8); // First 8 characters for identification
}

/**
 * Validate request size
 */
export function validateRequestSize(
  contentLength: string | null,
  maxSize: number = 1024 * 1024 // 1MB default
): { valid: boolean; reason?: string } {
  if (!contentLength) {
    return { valid: true }; // No content-length header
  }

  const size = parseInt(contentLength, 10);
  if (isNaN(size)) {
    return { valid: false, reason: "Invalid content-length header" };
  }

  if (size > maxSize) {
    return { valid: false, reason: `Request size ${size} exceeds maximum ${maxSize}` };
  }

  return { valid: true };
}

/**
 * Validate request headers for security
 */
export function validateSecurityHeaders(headers: Headers): {
  valid: boolean;
  warnings: string[];
  errors: string[];
} {
  const warnings: string[] = [];
  const errors: string[] = [];

  // Check for suspicious headers
  const suspiciousHeaders = [
    "x-forwarded-host",
    "x-original-host",
    "x-rewrite-url",
  ];

  for (const header of suspiciousHeaders) {
    if (headers.get(header)) {
      warnings.push(`Suspicious header detected: ${header}`);
    }
  }

  // Validate content-type for POST/PUT requests
  const contentType = headers.get("content-type");
  if (contentType && !contentType.includes("application/json")) {
    warnings.push(`Unexpected content-type: ${contentType}`);
  }

  // Check for XSS attempts in headers
  for (const [name, value] of headers.entries()) {
    if (/<script|javascript:|vbscript:/i.test(value)) {
      errors.push(`XSS attempt detected in header ${name}`);
    }
  }

  return {
    valid: errors.length === 0,
    warnings,
    errors,
  };
}

/**
 * Common validation schemas for API endpoints
 */
export const commonSchemas = {
  email: secureEmailSchema,
  password: securePasswordSchema,
  tenantId: tenantIdSchema,
  deviceId: deviceIdSchema,
  ipAddress: ipAddressSchema,
  userAgent: userAgentSchema,
  oauthCode: oauthCodeSchema,
  oauthState: oauthStateSchema,
  pkceCodeVerifier: pkceCodeVerifierSchema,
  
  // Pagination
  page: z.number().int().min(1).max(1000).default(1),
  limit: z.number().int().min(1).max(100).default(20),
  
  // Sorting
  sortBy: z.string().max(50).regex(/^[a-zA-Z_][a-zA-Z0-9_]*$/).optional(),
  sortOrder: z.enum(["asc", "desc"]).default("asc"),
  
  // Search
  search: z.string().max(255).optional(),
  
  // Timestamps
  timestamp: z.string().datetime().optional(),
  dateRange: z.object({
    from: z.string().datetime(),
    to: z.string().datetime(),
  }).optional(),
} as const;
