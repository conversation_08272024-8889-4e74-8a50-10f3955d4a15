import { Metada<PERSON> } from "next";
import { getCurrentUser } from "@/lib/auth/utils";
import { CreditBalance } from "@/components/billing/credit-balance";
import { DashboardOverview } from "@/components/dashboard/dashboard-overview";

export const metadata: Metadata = {
  title: "Dashboard - TanStack SaaS App",
  description: "Your TanStack-powered SaaS dashboard",
};

export default async function DashboardPage() {
  const user = await getCurrentUser();

  // Since middleware already handles auth, we can trust that user exists
  // But we still need to handle the case where user is null for type safety
  if (!user) {
    // This should rarely happen due to middleware, but handle gracefully
    return (
      <div className="container mx-auto py-6">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Authentication Required</h1>
          <p className="text-muted-foreground mb-4">Please sign in to access the dashboard.</p>
          <a href="/auth/signin" className="text-primary hover:underline">
            Sign In
          </a>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Credit Balance */}
      <CreditBalance
        userId={user.id}
        organizationId={user.organizationId}
      />

      {/* TanStack Dashboard Overview */}
      <DashboardOverview user={user} />
    </div>
  );
}
