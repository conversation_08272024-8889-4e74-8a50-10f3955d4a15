{"id": "95f99fb0-482a-4577-9ade-5f81240faa4e", "prevId": "1859c510-db5b-4cb7-9ff9-50f8108efb5d", "version": "7", "dialect": "postgresql", "tables": {"public.accounts": {"name": "accounts", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "provider_account_id": {"name": "provider_account_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "integer", "primaryKey": false, "notNull": false}, "token_type": {"name": "token_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false}, "session_state": {"name": "session_state", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"accounts_user_id_users_id_fk": {"name": "accounts_user_id_users_id_fk", "tableFrom": "accounts", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"accounts_provider_provider_account_id_pk": {"name": "accounts_provider_provider_account_id_pk", "columns": ["provider", "provider_account_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.addresses": {"name": "addresses", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": false}, "address_line_1": {"name": "address_line_1", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "address_line_2": {"name": "address_line_2", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "city": {"name": "city", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "state": {"name": "state", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "zip": {"name": "zip", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "country": {"name": "country", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "postal_code": {"name": "postal_code", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "accepted_at": {"name": "accepted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"addresses_tenant_id_tenants_id_fk": {"name": "addresses_tenant_id_tenants_id_fk", "tableFrom": "addresses", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.api_keys": {"name": "api_keys", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "last_used": {"name": "last_used", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"api_keys_user_id_users_id_fk": {"name": "api_keys_user_id_users_id_fk", "tableFrom": "api_keys", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "api_keys_organization_id_organizations_id_fk": {"name": "api_keys_organization_id_organizations_id_fk", "tableFrom": "api_keys", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"api_keys_key_unique": {"name": "api_keys_key_unique", "nullsNotDistinct": false, "columns": ["key"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.business_profiles": {"name": "business_profiles", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": false}, "business_name": {"name": "business_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "business_logo": {"name": "business_logo", "type": "text", "primaryKey": false, "notNull": false}, "company_registered_name": {"name": "company_registered_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "company_registered_no": {"name": "company_registered_no", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "business_industry": {"name": "business_industry", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "contact_email": {"name": "contact_email", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": false}, "show_whatsapp_floating": {"name": "show_whatsapp_floating", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"business_profiles_tenant_id_tenants_id_fk": {"name": "business_profiles_tenant_id_tenants_id_fk", "tableFrom": "business_profiles", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.class_categories": {"name": "class_categories", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"class_categories_tenant_id_tenants_id_fk": {"name": "class_categories_tenant_id_tenants_id_fk", "tableFrom": "class_categories", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.class_images": {"name": "class_images", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "class_id": {"name": "class_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "image_url": {"name": "image_url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "upload_at": {"name": "upload_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"class_images_class_id_classes_id_fk": {"name": "class_images_class_id_classes_id_fk", "tableFrom": "class_images", "tableTo": "classes", "columnsFrom": ["class_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.class_levels": {"name": "class_levels", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "sort_order": {"name": "sort_order", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"class_levels_tenant_id_tenants_id_fk": {"name": "class_levels_tenant_id_tenants_id_fk", "tableFrom": "class_levels", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.class_membership_plans ": {"name": "class_membership_plans ", "schema": "", "columns": {"class_id": {"name": "class_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "membership_plan_id": {"name": "membership_plan_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"class_membership_plans _class_id_classes_id_fk": {"name": "class_membership_plans _class_id_classes_id_fk", "tableFrom": "class_membership_plans ", "tableTo": "classes", "columnsFrom": ["class_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "class_membership_plans _membership_plan_id_membership_plans_id_fk": {"name": "class_membership_plans _membership_plan_id_membership_plans_id_fk", "tableFrom": "class_membership_plans ", "tableTo": "membership_plans", "columnsFrom": ["membership_plan_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"class_membership_plans _class_id_membership_plan_id_pk": {"name": "class_membership_plans _class_id_membership_plan_id_pk", "columns": ["class_id", "membership_plan_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.class_package_pricing": {"name": "class_package_pricing", "schema": "", "columns": {"class_id": {"name": "class_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "package_pricing_id": {"name": "package_pricing_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"class_package_pricing_class_id_classes_id_fk": {"name": "class_package_pricing_class_id_classes_id_fk", "tableFrom": "class_package_pricing", "tableTo": "classes", "columnsFrom": ["class_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "class_package_pricing_package_pricing_id_package_pricing_id_fk": {"name": "class_package_pricing_package_pricing_id_package_pricing_id_fk", "tableFrom": "class_package_pricing", "tableTo": "package_pricing", "columnsFrom": ["package_pricing_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"class_package_pricing_class_id_package_pricing_id_pk": {"name": "class_package_pricing_class_id_package_pricing_id_pk", "columns": ["class_id", "package_pricing_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.class_payment_options": {"name": "class_payment_options", "schema": "", "columns": {"class_id": {"name": "class_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "payment_type": {"name": "payment_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "drop_in_enable": {"name": "drop_in_enable", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "drop_in_price": {"name": "drop_in_price", "type": "integer", "primaryKey": false, "notNull": false}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"class_payment_options_class_id_classes_id_fk": {"name": "class_payment_options_class_id_classes_id_fk", "tableFrom": "class_payment_options", "tableTo": "classes", "columnsFrom": ["class_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.class_subcategories": {"name": "class_subcategories", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": true}, "category_id": {"name": "category_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"class_subcategories_tenant_id_tenants_id_fk": {"name": "class_subcategories_tenant_id_tenants_id_fk", "tableFrom": "class_subcategories", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "class_subcategories_category_id_class_categories_id_fk": {"name": "class_subcategories_category_id_class_categories_id_fk", "tableFrom": "class_subcategories", "tableTo": "class_categories", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.classes": {"name": "classes", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "category_id": {"name": "category_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "subcategory_id": {"name": "subcategory_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "duration": {"name": "duration", "type": "integer", "primaryKey": false, "notNull": false}, "duration_unit": {"name": "duration_unit", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "level_id": {"name": "level_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "delivery_mode": {"name": "delivery_mode", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "is_private": {"name": "is_private", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "custom_cancellation_policy": {"name": "custom_cancellation_policy", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "cancellation_policy_description": {"name": "cancellation_policy_description", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "location_id": {"name": "location_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "images": {"name": "images", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "items_to_bring": {"name": "items_to_bring", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "youtube_links": {"name": "youtube_links", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"classes_tenant_id_tenants_id_fk": {"name": "classes_tenant_id_tenants_id_fk", "tableFrom": "classes", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "classes_category_id_class_categories_id_fk": {"name": "classes_category_id_class_categories_id_fk", "tableFrom": "classes", "tableTo": "class_categories", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "classes_subcategory_id_class_subcategories_id_fk": {"name": "classes_subcategory_id_class_subcategories_id_fk", "tableFrom": "classes", "tableTo": "class_subcategories", "columnsFrom": ["subcategory_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "classes_level_id_class_levels_id_fk": {"name": "classes_level_id_class_levels_id_fk", "tableFrom": "classes", "tableTo": "class_levels", "columnsFrom": ["level_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "classes_location_id_locations_id_fk": {"name": "classes_location_id_locations_id_fk", "tableFrom": "classes", "tableTo": "locations", "columnsFrom": ["location_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.credit_transactions": {"name": "credit_transactions", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"credit_transactions_user_id_users_id_fk": {"name": "credit_transactions_user_id_users_id_fk", "tableFrom": "credit_transactions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "credit_transactions_organization_id_organizations_id_fk": {"name": "credit_transactions_organization_id_organizations_id_fk", "tableFrom": "credit_transactions", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.customer_addresses": {"name": "customer_addresses", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "customer_id": {"name": "customer_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "address_line_1": {"name": "address_line_1", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "address_line_2": {"name": "address_line_2", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "city": {"name": "city", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "state": {"name": "state", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "zip": {"name": "zip", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "country": {"name": "country", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "is_primary": {"name": "is_primary", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"customer_addresses_customer_id_customers_id_fk": {"name": "customer_addresses_customer_id_customers_id_fk", "tableFrom": "customer_addresses", "tableTo": "customers", "columnsFrom": ["customer_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.customer_waivers": {"name": "customer_waivers", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "customer_id": {"name": "customer_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "waiver_form_id": {"name": "waiver_form_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "signed_at": {"name": "signed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "acknowledged": {"name": "acknowledged", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"customer_waivers_customer_id_customers_id_fk": {"name": "customer_waivers_customer_id_customers_id_fk", "tableFrom": "customer_waivers", "tableTo": "customers", "columnsFrom": ["customer_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "customer_waivers_waiver_form_id_waiver_forms_id_fk": {"name": "customer_waivers_waiver_form_id_waiver_forms_id_fk", "tableFrom": "customer_waivers", "tableTo": "waiver_forms", "columnsFrom": ["waiver_form_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.customers": {"name": "customers", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": true}, "location_id": {"name": "location_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "mobile_country_code": {"name": "mobile_country_code", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "mobile_number": {"name": "mobile_number", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": false}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "date_of_birth": {"name": "date_of_birth", "type": "date", "primaryKey": false, "notNull": false}, "gender": {"name": "gender", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "pricing_group_id": {"name": "pricing_group_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"customers_tenant_id_tenants_id_fk": {"name": "customers_tenant_id_tenants_id_fk", "tableFrom": "customers", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "customers_location_id_locations_id_fk": {"name": "customers_location_id_locations_id_fk", "tableFrom": "customers", "tableTo": "locations", "columnsFrom": ["location_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "customers_pricing_group_id_pricing_groups_id_fk": {"name": "customers_pricing_group_id_pricing_groups_id_fk", "tableFrom": "customers", "tableTo": "pricing_groups", "columnsFrom": ["pricing_group_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.equipment": {"name": "equipment", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "default_display_name": {"name": "default_display_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"equipment_tenant_id_tenants_id_fk": {"name": "equipment_tenant_id_tenants_id_fk", "tableFrom": "equipment", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.equipment_instances": {"name": "equipment_instances", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "equipment_id": {"name": "equipment_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "location_id": {"name": "location_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "quantity": {"name": "quantity", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "display_name": {"name": "display_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"equipment_instances_equipment_id_equipment_id_fk": {"name": "equipment_instances_equipment_id_equipment_id_fk", "tableFrom": "equipment_instances", "tableTo": "equipment", "columnsFrom": ["equipment_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "equipment_instances_location_id_locations_id_fk": {"name": "equipment_instances_location_id_locations_id_fk", "tableFrom": "equipment_instances", "tableTo": "locations", "columnsFrom": ["location_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.invitations": {"name": "invitations", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "organization_id": {"name": "organization_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'member'"}, "invited_by": {"name": "invited_by", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "accepted_at": {"name": "accepted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"invitations_organization_id_organizations_id_fk": {"name": "invitations_organization_id_organizations_id_fk", "tableFrom": "invitations", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "invitations_invited_by_users_id_fk": {"name": "invitations_invited_by_users_id_fk", "tableFrom": "invitations", "tableTo": "users", "columnsFrom": ["invited_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"invitations_token_unique": {"name": "invitations_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.locations": {"name": "locations", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": false}, "address_line_1": {"name": "address_line_1", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "address_line_2": {"name": "address_line_2", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "city": {"name": "city", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "state": {"name": "state", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "country": {"name": "country", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "postal_code": {"name": "postal_code", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "phone_number": {"name": "phone_number", "type": "<PERSON><PERSON><PERSON>(30)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"locations_tenant_id_tenants_id_fk": {"name": "locations_tenant_id_tenants_id_fk", "tableFrom": "locations", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.membership_plans": {"name": "membership_plans", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "price": {"name": "price", "type": "integer", "primaryKey": false, "notNull": false}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "duration": {"name": "duration", "type": "integer", "primaryKey": false, "notNull": false}, "duration_unit": {"name": "duration_unit", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"membership_plans_tenant_id_tenants_id_fk": {"name": "membership_plans_tenant_id_tenants_id_fk", "tableFrom": "membership_plans", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.organization_members": {"name": "organization_members", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "organization_id": {"name": "organization_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'member'"}, "invited_by": {"name": "invited_by", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "joined_at": {"name": "joined_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"organization_members_organization_id_organizations_id_fk": {"name": "organization_members_organization_id_organizations_id_fk", "tableFrom": "organization_members", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "organization_members_user_id_users_id_fk": {"name": "organization_members_user_id_users_id_fk", "tableFrom": "organization_members", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "organization_members_invited_by_users_id_fk": {"name": "organization_members_invited_by_users_id_fk", "tableFrom": "organization_members", "tableTo": "users", "columnsFrom": ["invited_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.organizations": {"name": "organizations", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "logo": {"name": "logo", "type": "text", "primaryKey": false, "notNull": false}, "plan": {"name": "plan", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'free'"}, "credits": {"name": "credits", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "monthly_credits": {"name": "monthly_credits", "type": "integer", "primaryKey": false, "notNull": true, "default": 1000}, "last_credit_refresh": {"name": "last_credit_refresh", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"organizations_slug_unique": {"name": "organizations_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.package_categories": {"name": "package_categories", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"package_categories_tenant_id_tenants_id_fk": {"name": "package_categories_tenant_id_tenants_id_fk", "tableFrom": "package_categories", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.package_category_rel": {"name": "package_category_rel", "schema": "", "columns": {"package_id": {"name": "package_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "category_id": {"name": "category_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"package_category_rel_package_id_package_id_fk": {"name": "package_category_rel_package_id_package_id_fk", "tableFrom": "package_category_rel", "tableTo": "package", "columnsFrom": ["package_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "package_category_rel_category_id_package_categories_id_fk": {"name": "package_category_rel_category_id_package_categories_id_fk", "tableFrom": "package_category_rel", "tableTo": "package_categories", "columnsFrom": ["category_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "package_category_rel_tenant_id_tenants_id_fk": {"name": "package_category_rel_tenant_id_tenants_id_fk", "tableFrom": "package_category_rel", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"package_category_rel_package_id_category_id_pk": {"name": "package_category_rel_package_id_category_id_pk", "columns": ["package_id", "category_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.package_class_includes": {"name": "package_class_includes", "schema": "", "columns": {"package_id": {"name": "package_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "class_id": {"name": "class_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"package_class_includes_package_id_package_id_fk": {"name": "package_class_includes_package_id_package_id_fk", "tableFrom": "package_class_includes", "tableTo": "package", "columnsFrom": ["package_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "package_class_includes_class_id_classes_id_fk": {"name": "package_class_includes_class_id_classes_id_fk", "tableFrom": "package_class_includes", "tableTo": "classes", "columnsFrom": ["class_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.package_customer_segements": {"name": "package_customer_segements", "schema": "", "columns": {"package_id": {"name": "package_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "customer_id": {"name": "customer_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"package_customer_segements_package_id_package_id_fk": {"name": "package_customer_segements_package_id_package_id_fk", "tableFrom": "package_customer_segements", "tableTo": "package", "columnsFrom": ["package_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "package_customer_segements_customer_id_customers_id_fk": {"name": "package_customer_segements_customer_id_customers_id_fk", "tableFrom": "package_customer_segements", "tableTo": "customers", "columnsFrom": ["customer_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.package_display_options": {"name": "package_display_options", "schema": "", "columns": {"package_id": {"name": "package_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "use_custom_description": {"name": "use_custom_description", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "custom_description": {"name": "custom_description", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"package_display_options_package_id_package_id_fk": {"name": "package_display_options_package_id_package_id_fk", "tableFrom": "package_display_options", "tableTo": "package", "columnsFrom": ["package_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.package_locations": {"name": "package_locations", "schema": "", "columns": {"package_id": {"name": "package_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "location_id": {"name": "location_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"package_locations_package_id_package_id_fk": {"name": "package_locations_package_id_package_id_fk", "tableFrom": "package_locations", "tableTo": "package", "columnsFrom": ["package_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "package_locations_location_id_locations_id_fk": {"name": "package_locations_location_id_locations_id_fk", "tableFrom": "package_locations", "tableTo": "locations", "columnsFrom": ["location_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"package_locations_package_id_location_id_pk": {"name": "package_locations_package_id_location_id_pk", "columns": ["package_id", "location_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.package_pricing": {"name": "package_pricing", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "package_id": {"name": "package_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "pricing_group_id": {"name": "pricing_group_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "price": {"name": "price", "type": "integer", "primaryKey": false, "notNull": false}, "credit_amount": {"name": "credit_amount", "type": "integer", "primaryKey": false, "notNull": false}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"package_pricing_package_id_package_id_fk": {"name": "package_pricing_package_id_package_id_fk", "tableFrom": "package_pricing", "tableTo": "package", "columnsFrom": ["package_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "package_pricing_pricing_group_id_pricing_groups_id_fk": {"name": "package_pricing_pricing_group_id_pricing_groups_id_fk", "tableFrom": "package_pricing", "tableTo": "pricing_groups", "columnsFrom": ["pricing_group_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.package_purchase_options": {"name": "package_purchase_options", "schema": "", "columns": {"package_id": {"name": "package_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "purchase_limit": {"name": "purchase_limit", "type": "integer", "primaryKey": false, "notNull": false}, "restrict_to": {"name": "restrict_to", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "transferable": {"name": "transferable", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "specify_sold_at_location": {"name": "specify_sold_at_location", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "sold_at_location_id": {"name": "sold_at_location_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "class_booking_limit": {"name": "class_booking_limit", "type": "integer", "primaryKey": false, "notNull": false}, "show_online": {"name": "show_online", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "show_onsite": {"name": "show_onsite", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "is_private": {"name": "is_private", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"package_purchase_options_package_id_package_id_fk": {"name": "package_purchase_options_package_id_package_id_fk", "tableFrom": "package_purchase_options", "tableTo": "package", "columnsFrom": ["package_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "package_purchase_options_sold_at_location_id_locations_id_fk": {"name": "package_purchase_options_sold_at_location_id_locations_id_fk", "tableFrom": "package_purchase_options", "tableTo": "locations", "columnsFrom": ["sold_at_location_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.package": {"name": "package", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "is_private": {"name": "is_private", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "validity_date": {"name": "validity_date", "type": "date", "primaryKey": false, "notNull": false}, "validity_duration": {"name": "validity_duration", "type": "integer", "primaryKey": false, "notNull": false}, "schedule_availability": {"name": "schedule_availability", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"package_tenant_id_tenants_id_fk": {"name": "package_tenant_id_tenants_id_fk", "tableFrom": "package", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.pricing_groups": {"name": "pricing_groups", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "discount_percentage": {"name": "discount_percentage", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "is_default": {"name": "is_default", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "sort_order": {"name": "sort_order", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"pricing_groups_tenant_id_tenants_id_fk": {"name": "pricing_groups_tenant_id_tenants_id_fk", "tableFrom": "pricing_groups", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sessions": {"name": "sessions", "schema": "", "columns": {"session_token": {"name": "session_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "expires": {"name": "expires", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"sessions_user_id_users_id_fk": {"name": "sessions_user_id_users_id_fk", "tableFrom": "sessions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.settings": {"name": "settings", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": false}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "time_zone": {"name": "time_zone", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "accepted_at": {"name": "accepted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"settings_tenant_id_tenants_id_fk": {"name": "settings_tenant_id_tenants_id_fk", "tableFrom": "settings", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.stripe_customers": {"name": "stripe_customers", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "organization_id": {"name": "organization_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "stripe_customer_id": {"name": "stripe_customer_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"stripe_customers_user_id_users_id_fk": {"name": "stripe_customers_user_id_users_id_fk", "tableFrom": "stripe_customers", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "stripe_customers_organization_id_organizations_id_fk": {"name": "stripe_customers_organization_id_organizations_id_fk", "tableFrom": "stripe_customers", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"stripe_customers_stripe_customer_id_unique": {"name": "stripe_customers_stripe_customer_id_unique", "nullsNotDistinct": false, "columns": ["stripe_customer_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.stripe_payments": {"name": "stripe_payments", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "stripe_payment_intent_id": {"name": "stripe_payment_intent_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "stripe_customer_id": {"name": "stripe_customer_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "organization_id": {"name": "organization_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(3)", "primaryKey": false, "notNull": true, "default": "'usd'"}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "credits": {"name": "credits", "type": "integer", "primaryKey": false, "notNull": true}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"stripe_payments_user_id_users_id_fk": {"name": "stripe_payments_user_id_users_id_fk", "tableFrom": "stripe_payments", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "stripe_payments_organization_id_organizations_id_fk": {"name": "stripe_payments_organization_id_organizations_id_fk", "tableFrom": "stripe_payments", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"stripe_payments_stripe_payment_intent_id_unique": {"name": "stripe_payments_stripe_payment_intent_id_unique", "nullsNotDistinct": false, "columns": ["stripe_payment_intent_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tags": {"name": "tags", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": true}, "customer_id": {"name": "customer_id", "type": "text", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "custom_color": {"name": "custom_color", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"tags_tenant_id_tenants_id_fk": {"name": "tags_tenant_id_tenants_id_fk", "tableFrom": "tags", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "tags_customer_id_customers_id_fk": {"name": "tags_customer_id_customers_id_fk", "tableFrom": "tags", "tableTo": "customers", "columnsFrom": ["customer_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tenants": {"name": "tenants", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "subdomain": {"name": "subdomain", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "organization_id": {"name": "organization_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "custom_domain": {"name": "custom_domain", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "settings": {"name": "settings", "type": "jsonb", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"tenants_organization_id_organizations_id_fk": {"name": "tenants_organization_id_organizations_id_fk", "tableFrom": "tenants", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"tenants_subdomain_unique": {"name": "tenants_subdomain_unique", "nullsNotDistinct": false, "columns": ["subdomain"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.usage_logs": {"name": "usage_logs", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "organization_id": {"name": "organization_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "api_key_id": {"name": "api_key_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "endpoint": {"name": "endpoint", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "method": {"name": "method", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "status_code": {"name": "status_code", "type": "integer", "primaryKey": false, "notNull": true}, "credits_used": {"name": "credits_used", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "response_time": {"name": "response_time", "type": "integer", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"usage_logs_user_id_users_id_fk": {"name": "usage_logs_user_id_users_id_fk", "tableFrom": "usage_logs", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "usage_logs_organization_id_organizations_id_fk": {"name": "usage_logs_organization_id_organizations_id_fk", "tableFrom": "usage_logs", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "usage_logs_api_key_id_api_keys_id_fk": {"name": "usage_logs_api_key_id_api_keys_id_fk", "tableFrom": "usage_logs", "tableTo": "api_keys", "columnsFrom": ["api_key_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email_verified": {"name": "email_verified", "type": "timestamp", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'user'"}, "organization_id": {"name": "organization_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": false}, "location_id": {"name": "location_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "credits": {"name": "credits", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "monthly_credits": {"name": "monthly_credits", "type": "integer", "primaryKey": false, "notNull": true, "default": 100}, "last_credit_refresh": {"name": "last_credit_refresh", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"users_organization_id_organizations_id_fk": {"name": "users_organization_id_organizations_id_fk", "tableFrom": "users", "tableTo": "organizations", "columnsFrom": ["organization_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "users_tenant_id_tenants_id_fk": {"name": "users_tenant_id_tenants_id_fk", "tableFrom": "users", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "users_location_id_locations_id_fk": {"name": "users_location_id_locations_id_fk", "tableFrom": "users", "tableTo": "locations", "columnsFrom": ["location_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.verification_tokens": {"name": "verification_tokens", "schema": "", "columns": {"identifier": {"name": "identifier", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "expires": {"name": "expires", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"verification_tokens_identifier_token_pk": {"name": "verification_tokens_identifier_token_pk", "columns": ["identifier", "token"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.waiver_forms": {"name": "waiver_forms", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "integer", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "version": {"name": "version", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'1.0'"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "is_required": {"name": "is_required", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "expiry_days": {"name": "expiry_days", "type": "integer", "primaryKey": false, "notNull": false, "default": 365}, "sort_order": {"name": "sort_order", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {"waiver_forms_tenant_id_tenants_id_fk": {"name": "waiver_forms_tenant_id_tenants_id_fk", "tableFrom": "waiver_forms", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.webhooks": {"name": "webhooks", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "provider": {"name": "provider", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "event_type": {"name": "event_type", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "event_id": {"name": "event_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "data": {"name": "data", "type": "jsonb", "primaryKey": false, "notNull": true}, "processed": {"name": "processed", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "processed_at": {"name": "processed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "error": {"name": "error", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}