import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { ClassService } from "@/lib/services/class.service";

/**
 * API Routes untuk Classes
 * 
 * Mengikuti pattern yang sama dengan class-categories dan class-subcategories.
 * Semua validation menggunakan Zod, error handling yang proper.
 */

// Validation schemas
const searchParamsSchema = z.object({
  tenantId: z.string().transform((val) => parseInt(val, 10)),
  categoryId: z.string().optional(),
  subcategoryId: z.string().optional(),
  search: z.string().optional(),
  limit: z.string().transform((val) => parseInt(val, 10)).optional(),
  offset: z.string().transform((val) => parseInt(val, 10)).optional(),
});

const createClassSchema = z.object({
  tenantId: z.number().int().positive("Tenant ID must be positive"),
  name: z.string().min(1, "Name is required").max(255, "Name too long"),
  description: z.string().max(255, "Description too long").optional(),
  categoryId: z.string().min(1, "Category ID is required"),
  subcategoryId: z.string().optional(),
  duration_value: z.number().int().positive().optional(),
  duration_unit: z.string().max(50).optional(),
  level_id: z.string().optional(),
  delivery_mode: z.string().max(50).optional(),
  is_private: z.boolean().optional(),
  custom_cancellation_policy: z.boolean().optional(),
  cancellation_policy_description: z.string().max(255).optional(),
  is_active: z.boolean().optional(),
  location_id: z.string().optional(),
  images: z.array(z.string()).optional(),
  items_to_bring: z.array(z.object({
    id: z.string(),
    item_name: z.string().min(1, "Item name is required"),
    is_required: z.boolean(),
  })).optional(),
  youtube_links: z.array(z.object({
    id: z.string(),
    yt_url: z.string().url("Please enter a valid YouTube URL"),
  })).optional(),
  package_pricing_ids: z.array(z.string()).optional(),
});

/**
 * GET /api/classes
 * Search classes with filtering and pagination
 * 
 * Query parameters:
 * - tenantId: required untuk tenant isolation
 * - categoryId: optional filter by category
 * - subcategoryId: optional filter by subcategory
 * - search: optional search by name
 * - limit: optional pagination limit (default 20)
 * - offset: optional pagination offset (default 0)
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Parse and validate query parameters
    const rawParams = {
      tenantId: searchParams.get("tenantId"),
      categoryId: searchParams.get("categoryId"),
      subcategoryId: searchParams.get("subcategoryId"),
      search: searchParams.get("search"),
      limit: searchParams.get("limit"),
      offset: searchParams.get("offset"),
    };

    // Remove null values
    const cleanParams = Object.fromEntries(
      Object.entries(rawParams).filter(([_, value]) => value !== null)
    );

    const validatedParams = searchParamsSchema.parse(cleanParams);

    // Call service
    const result = await ClassService.searchClasses(
      validatedParams.tenantId,
      validatedParams.categoryId,
      validatedParams.subcategoryId,
      validatedParams.search,
      validatedParams.limit || 20,
      validatedParams.offset || 0
    );

    return NextResponse.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error("GET /api/classes error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: "Invalid parameters", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/classes
 * Create a new class
 * 
 * Body harus contain:
 * - tenantId: untuk tenant isolation
 * - name: nama class
 * - categoryId: ID kategori (wajib)
 * - subcategoryId: ID subkategori (optional)
 * - description: deskripsi (optional)
 * - dll sesuai schema
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate request body
    const validatedData = createClassSchema.parse(body);

    // Call service untuk create
    const classResult = await ClassService.create(validatedData);

    return NextResponse.json({
      success: true,
      data: classResult,
      message: "Class created successfully",
    }, { status: 201 });
  } catch (error) {
    console.error("POST /api/classes error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * Helper function untuk get all classes by tenant
 * Digunakan kalau tidak ada search parameters
 */
async function getAllClasses(tenantId: string) {
  try {
    const classes = await ClassService.getByTenantId(parseInt(tenantId, 10));
    
    return NextResponse.json({
      success: true,
      data: {
        classes,
        total: classes.length,
        hasMore: false,
      },
    });
  } catch (error) {
    console.error("Error getting all classes:", error);
    throw error;
  }
}
