import { db } from "@/lib/db";
import { users, user_roles, roles } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";
import { createId } from "@paralleldrive/cuid2";

/**
 * Script <NAME_EMAIL> sebagai superadmin
 * 
 * Script ini akan:
 * 1. <PERSON>i atau <NAME_EMAIL>
 * 2. Assign role super_admin ke user tersebut
 * 3. Pastikan user memiliki akses penuh ke sistem
 */

async function setupSuperAdmin() {
  try {
    console.log("🚀 Setting up superadmin...");

    // 1. <PERSON>i role super_admin
    const superAdminRole = await db
      .select()
      .from(roles)
      .where(eq(roles.name, "super_admin"))
      .limit(1);

    if (superAdminRole.length === 0) {
      throw new Error("Super admin role not found. Please run RBAC seeding first.");
    }

    const roleId = superAdminRole[0].id;
    console.log(`✅ Found super_admin role: ${roleId}`);

    // 2. <PERSON><PERSON> atau <NAME_EMAIL>
    let adminUser = await db
      .select()
      .from(users)
      .where(eq(users.email, "<EMAIL>"))
      .limit(1);

    if (adminUser.length === 0) {
      // Buat user baru
      console.log("👤 Creating <EMAIL> user...");
      
      const newUser = await db
        .insert(users)
        .values({
          id: createId(),
          email: "<EMAIL>",
          name: "Super Administrator",
          role: "admin", // Legacy role field
          tenantId: null, // Superadmin tidak terikat tenant
          organizationId: null,
          emailVerified: new Date(),
        })
        .returning();

      adminUser = newUser;
      console.log(`✅ Created user: ${adminUser[0].id}`);
    } else {
      console.log(`✅ Found existing user: ${adminUser[0].id}`);
    }

    const userId = adminUser[0].id;

    // 3. Cek apakah sudah ada role assignment
    const existingAssignment = await db
      .select()
      .from(user_roles)
      .where(
        and(
          eq(user_roles.userId, userId),
          eq(user_roles.roleId, roleId),
          eq(user_roles.is_active, true)
        )
      )
      .limit(1);

    if (existingAssignment.length > 0) {
      console.log("✅ Super admin role already assigned");
      return;
    }

    // 4. Assign super_admin role
    console.log("🔐 Assigning super_admin role...");
    
    await db
      .insert(user_roles)
      .values({
        id: createId(),
        userId,
        roleId,
        tenantId: null, // Superadmin assignment tidak terikat tenant
        assignedBy: userId, // Self-assigned
        is_active: true,
      });

    console.log("🎉 Successfully <NAME_EMAIL> as superadmin!");
    console.log("");
    console.log("📋 Summary:");
    console.log(`   Email: <EMAIL>`);
    console.log(`   User ID: ${userId}`);
    console.log(`   Role: Super Administrator`);
    console.log(`   Tenant: Global (no tenant restriction)`);
    console.log("");
    console.log("🔑 This user now has full access to all modules and permissions.");

  } catch (error) {
    console.error("❌ Error setting up superadmin:", error);
    throw error;
  }
}

// Run the script
if (require.main === module) {
  setupSuperAdmin()
    .then(() => {
      console.log("✅ Setup completed successfully");
      process.exit(0);
    })
    .catch((error) => {
      console.error("❌ Setup failed:", error);
      process.exit(1);
    });
}

export { setupSuperAdmin };
