// Test script for class items to bring functionality
// Run with: npx tsx scripts/test-class-items-to-bring.ts

import { db } from "../src/lib/db";
import { classes, class_categories } from "../src/lib/db/schema";
import { eq } from "drizzle-orm";
import { ClassService } from "../src/lib/services/class.service";

async function testClassItemsToBring() {
  try {
    console.log("🧪 Testing Class Items To Bring Functionality...");

    // Step 1: Get existing category for test
    const [existingCategory] = await db
      .select()
      .from(class_categories)
      .where(eq(class_categories.tenantId, 1))
      .limit(1);

    if (!existingCategory) {
      console.log("❌ No class categories found. Please create a category first.");
      return;
    }

    console.log(`📋 Using category: ${existingCategory.name}`);

    // Step 2: Test creating class with items to bring
    console.log("\n🔧 Test 1: Create class with items to bring");

    const testClassData = {
      tenantId: 1,
      name: "Yoga Class with Items",
      description: "Test yoga class with required items",
      categoryId: existingCategory.id,
      items_to_bring: [
        {
          id: "item_1",
          item_name: "Yoga Mat",
          is_required: true
        },
        {
          id: "item_2", 
          item_name: "Water Bottle",
          is_required: true
        },
        {
          id: "item_3",
          item_name: "Towel",
          is_required: false
        }
      ]
    };

    const createdClass = await ClassService.create(testClassData);
    console.log(`✅ Created class: ${createdClass.name}`);
    console.log(`📦 Items to bring: ${JSON.stringify(createdClass.items_to_bring, null, 2)}`);

    // Step 3: Test retrieving class with items
    console.log("\n🔧 Test 2: Retrieve class with items");

    const retrievedClass = await ClassService.getById(createdClass.id);
    if (retrievedClass) {
      console.log(`✅ Retrieved class: ${retrievedClass.name}`);
      console.log(`📦 Items to bring: ${JSON.stringify(retrievedClass.items_to_bring, null, 2)}`);
      
      // Verify items match
      const itemsMatch = JSON.stringify(createdClass.items_to_bring) === JSON.stringify(retrievedClass.items_to_bring);
      console.log(`🔍 Items match: ${itemsMatch ? '✅ Yes' : '❌ No'}`);
    } else {
      console.log("❌ Failed to retrieve class");
    }

    // Step 4: Test updating class items
    console.log("\n🔧 Test 3: Update class items");

    const updatedItems = [
      {
        id: "item_1",
        item_name: "Premium Yoga Mat",
        is_required: true
      },
      {
        id: "item_4",
        item_name: "Yoga Block",
        is_required: false
      }
    ];

    const updatedClass = await ClassService.update(createdClass.id, {
      items_to_bring: updatedItems
    });

    if (updatedClass) {
      console.log(`✅ Updated class items`);
      console.log(`📦 New items: ${JSON.stringify(updatedClass.items_to_bring, null, 2)}`);
    } else {
      console.log("❌ Failed to update class");
    }

    // Step 5: Test class with no items
    console.log("\n🔧 Test 4: Create class with no items");

    const classWithoutItems = await ClassService.create({
      tenantId: 1,
      name: "Simple Class",
      description: "Class without any required items",
      categoryId: existingCategory.id,
      items_to_bring: []
    });

    console.log(`✅ Created class without items: ${classWithoutItems.name}`);
    console.log(`📦 Items to bring: ${JSON.stringify(classWithoutItems.items_to_bring, null, 2)}`);

    // Step 6: Verify database schema
    console.log("\n🔧 Test 5: Verify database schema");

    const schemaCheck = await db.execute(`
      SELECT column_name, data_type, is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'classes' AND column_name = 'items_to_bring'
    `);

    if (schemaCheck.length > 0) {
      console.log("✅ Database schema verified:");
      console.log(`   Column: ${schemaCheck[0].column_name}`);
      console.log(`   Type: ${schemaCheck[0].data_type}`);
      console.log(`   Nullable: ${schemaCheck[0].is_nullable}`);
    } else {
      console.log("❌ items_to_bring column not found in database");
    }

    // Step 7: Test search functionality
    console.log("\n🔧 Test 6: Search classes with items");

    const searchResults = await ClassService.searchClasses(1);
    const classesWithItems = searchResults.classes.filter(cls => 
      cls.items_to_bring && cls.items_to_bring.length > 0
    );

    console.log(`✅ Found ${classesWithItems.length} classes with items to bring`);
    classesWithItems.forEach((cls, index) => {
      console.log(`   ${index + 1}. ${cls.name}: ${cls.items_to_bring?.length || 0} items`);
    });

    console.log("\n🎉 All tests completed successfully!");

    // Cleanup
    console.log("\n🧹 Cleaning up test data...");
    await ClassService.delete(createdClass.id);
    await ClassService.delete(classWithoutItems.id);
    console.log("✅ Test data cleaned up");

  } catch (error) {
    console.error("❌ Test failed:", error);
    throw error;
  }
}

// Run the test
testClassItemsToBring()
  .then(() => {
    console.log("✅ Test script completed successfully");
    process.exit(0);
  })
  .catch((error) => {
    console.error("💥 Test script failed:", error);
    process.exit(1);
  });
