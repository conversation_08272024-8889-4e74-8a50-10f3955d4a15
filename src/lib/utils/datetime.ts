/**
 * Utility functions untuk consistent datetime handling
 * Mengatasi masalah timezone inconsistency antara display dan form
 */

/**
 * Format datetime untuk datetime-local input (YYYY-MM-DDTHH:mm)
 * Konsisten dengan bagaimana waktu ditampilkan di UI
 */
export function formatDateTimeForInput(dateTime: string | Date | null): string {
  if (!dateTime) return "";

  const date = new Date(dateTime);
  
  // Format ke YYYY-MM-DDTHH:mm untuk datetime-local input
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  
  return `${year}-${month}-${day}T${hours}:${minutes}`;
}

/**
 * Format time untuk display (HH:mm)
 * Konsisten di semua komponen
 */
export function formatTimeForDisplay(time: Date | string | null): string {
  if (!time) return "";
  
  const date = typeof time === 'string' ? new Date(time) : time;
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  
  return `${hours}:${minutes}`;
}

/**
 * Format date untuk local date string (YYYY-MM-DD)
 * Timezone-safe tanpa konversi UTC
 */
export function formatDateForInput(date: Date): string {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}

/**
 * Format datetime untuk display (YYYY-MM-DD HH:mm)
 */
export function formatDateTimeForDisplay(date: string | Date | null, time: string | Date | null): string {
  if (!date || !time) return "Not set";
  
  const dateStr = typeof date === 'string' ? date : formatDateForInput(date);
  const timeStr = formatTimeForDisplay(time);
  
  return `${dateStr} ${timeStr}`;
}

/**
 * Parse datetime-local input value ke Date object
 */
export function parseDateTimeInput(dateTimeString: string): Date {
  // datetime-local format: YYYY-MM-DDTHH:mm
  // Buat Date object tanpa timezone conversion
  const [datePart, timePart] = dateTimeString.split('T');
  const [year, month, day] = datePart.split('-').map(Number);
  const [hours, minutes] = timePart.split(':').map(Number);
  
  return new Date(year, month - 1, day, hours, minutes);
}

/**
 * Combine date dan time string ke datetime string
 */
export function combineDateAndTime(date: Date, time: string): string {
  const dateStr = formatDateForInput(date);
  return `${dateStr}T${time}`;
}
