"use client";

import { useEffect, useState } from "react";
import { format } from "date-fns";
import { ArrowUpCircle, ArrowDownCircle, CreditCard, Gift, RefreshCw } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { formatCredits } from "@/lib/stripe/config";
import { ComponentLoading } from "@/components/ui/loading";

interface Transaction {
  id: string;
  type: "purchase" | "usage" | "refund" | "bonus" | "monthly_refresh";
  amount: number;
  description: string;
  createdAt: Date;
  stripePaymentIntentId?: string;
}

interface TransactionHistoryProps {
  userId: string;
  organizationId?: string;
}

const getTransactionIcon = (type: string) => {
  switch (type) {
    case "purchase":
      return <CreditCard className="h-4 w-4 text-green-600" />;
    case "usage":
      return <ArrowDownCircle className="h-4 w-4 text-red-600" />;
    case "refund":
      return <ArrowUpCircle className="h-4 w-4 text-blue-600" />;
    case "bonus":
      return <Gift className="h-4 w-4 text-purple-600" />;
    case "monthly_refresh":
      return <RefreshCw className="h-4 w-4 text-orange-600" />;
    default:
      return <CreditCard className="h-4 w-4 text-gray-600" />;
  }
};

const getTransactionBadge = (type: string) => {
  switch (type) {
    case "purchase":
      return <Badge variant="default" className="bg-green-100 text-green-800">Purchase</Badge>;
    case "usage":
      return <Badge variant="destructive">Usage</Badge>;
    case "refund":
      return <Badge variant="secondary" className="bg-blue-100 text-blue-800">Refund</Badge>;
    case "bonus":
      return <Badge variant="secondary" className="bg-purple-100 text-purple-800">Bonus</Badge>;
    case "monthly_refresh":
      return <Badge variant="secondary" className="bg-orange-100 text-orange-800">Monthly</Badge>;
    default:
      return <Badge variant="outline">{type}</Badge>;
  }
};

export function TransactionHistory({ userId, organizationId }: TransactionHistoryProps) {
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchTransactions = async () => {
      try {
        const response = await fetch(`/api/credits/transactions?${organizationId ? `organizationId=${organizationId}` : ''}`);
        if (response.ok) {
          const data = await response.json();
          setTransactions(data.map((t: any) => ({
            ...t,
            createdAt: new Date(t.createdAt),
          })));
        }
      } catch (error) {
        console.error("Failed to fetch transactions:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchTransactions();
  }, [userId, organizationId]);

  if (loading) {
    return <ComponentLoading text="Loading transaction history..." />;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Transaction History</CardTitle>
        <CardDescription>
          Your recent credit transactions and usage
        </CardDescription>
      </CardHeader>
      <CardContent>
        {transactions.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            No transactions found
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Type</TableHead>
                <TableHead>Description</TableHead>
                <TableHead className="text-right">Amount</TableHead>
                <TableHead>Date</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {transactions.map((transaction) => (
                <TableRow key={transaction.id}>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      {getTransactionIcon(transaction.type)}
                      {getTransactionBadge(transaction.type)}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">{transaction.description}</div>
                      {transaction.stripePaymentIntentId && (
                        <div className="text-xs text-muted-foreground">
                          Payment ID: {transaction.stripePaymentIntentId}
                        </div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <span className={`font-medium ${
                      transaction.amount > 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {transaction.amount > 0 ? '+' : ''}
                      {formatCredits(Math.abs(transaction.amount))}
                    </span>
                  </TableCell>
                  <TableCell className="text-muted-foreground">
                    {format(transaction.createdAt, "MMM dd, yyyy 'at' HH:mm")}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  );
}
