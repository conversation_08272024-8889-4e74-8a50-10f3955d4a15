import { db } from "@/lib/db";
import { classes, class_categories, class_subcategories, class_levels, locations, type Class, type ClassWithRelations, type NewClass, type ClassItemToBring, type ClassYoutubeLinkData } from "@/lib/db/schema";
import { eq, and, ilike, desc, sql } from "drizzle-orm";
import { createId } from "@paralleldrive/cuid2";
import { ClassPackagePricingService } from "./class-package-pricing.service";

/**
 * Class Service
 * 
 * Service untuk manage classes dengan pattern simple seperti ClassCategoryService.
 * Ini kayak "manager" yang handle semua operasi CRUD untuk classes.
 * 
 * Pattern ini udah terbukti berhasil tanpa bug di class categories dan subcategories.
 */

// Interface untuk search results
export interface ClassSearchResult {
  classes: Class[];
  total: number;
  hasMore: boolean;
}

/**
 * ClassService - Service untuk manage classes
 * 
 * Menggunakan static methods pattern yang simple dan proven.
 * Tidak pakai BaseService yang kompleks, langsung ke database dengan Drizzle ORM.
 */
export class ClassService {
  /**
   * Search classes dengan filtering dan pagination
   * 
   * Ini method utama untuk ambil data classes dengan berbagai filter:
   * - tenantId: wajib untuk tenant isolation
   * - categoryId: optional, filter berdasarkan kategori
   * - subcategoryId: optional, filter berdasarkan subkategori
   * - search: optional, search berdasarkan nama class
   * - limit & offset: untuk pagination
   */
  static async searchClasses(
    tenantId: number, 
    categoryId?: string,
    subcategoryId?: string,
    search?: string, 
    limit = 20, 
    offset = 0
  ): Promise<ClassSearchResult> {
    try {
      // Build where conditions - mulai dari tenant isolation
      let whereConditions = eq(classes.tenantId, tenantId);

      // Filter by category if provided
      if (categoryId) {
        whereConditions = and(
          whereConditions,
          eq(classes.categoryId, categoryId)
        ) as any;
      }

      // Filter by subcategory if provided
      if (subcategoryId) {
        whereConditions = and(
          whereConditions,
          eq(classes.subcategoryId, subcategoryId)
        ) as any;
      }

      // Kalau ada search term, tambahin filter nama
      if (search && search.trim()) {
        whereConditions = and(
          whereConditions,
          ilike(classes.name, `%${search.trim()}%`)
        ) as any;
      }

      // Query dengan pagination - ambil +1 untuk cek hasMore
      const classResults = await db
        .select()
        .from(classes)
        .where(whereConditions)
        .orderBy(desc(classes.createdAt))
        .limit(limit + 1) // +1 untuk cek hasMore
        .offset(offset);

      // Cek apakah ada data lebih
      const hasMore = classResults.length > limit;
      if (hasMore) {
        classResults.pop(); // Hapus item extra
      }

      // Count total untuk metadata
      const totalResult = await db
        .select({ count: sql<number>`count(*)` })
        .from(classes)
        .where(whereConditions);

      const total = Number(totalResult[0]?.count || 0);

      return {
        classes: classResults,
        total,
        hasMore,
      };
    } catch (error) {
      console.error("Error searching classes:", error);
      throw error;
    }
  }

  /**
   * Get all classes by tenant ID
   * 
   * Method simple untuk ambil semua classes dalam satu tenant.
   * Biasanya dipake untuk dropdown atau list sederhana.
   */
  static async getByTenantId(tenantId: number): Promise<Class[]> {
    return await db
      .select()
      .from(classes)
      .where(eq(classes.tenantId, tenantId))
      .orderBy(desc(classes.createdAt));
  }

  /**
   * Get classes by category ID
   * 
   * Ambil semua classes dalam kategori tertentu.
   * Berguna untuk filter berdasarkan kategori.
   */
  static async getByCategoryId(categoryId: string): Promise<Class[]> {
    return await db
      .select()
      .from(classes)
      .where(eq(classes.categoryId, categoryId))
      .orderBy(desc(classes.createdAt));
  }

  /**
   * Get classes by subcategory ID
   * 
   * Ambil semua classes dalam subkategori tertentu.
   * Berguna untuk filter berdasarkan subkategori.
   */
  static async getBySubcategoryId(subcategoryId: string): Promise<Class[]> {
    return await db
      .select()
      .from(classes)
      .where(eq(classes.subcategoryId, subcategoryId))
      .orderBy(desc(classes.createdAt));
  }

  /**
   * Get class by ID
   *
   * Ambil single class berdasarkan ID dengan package pricing relationships.
   * Return null kalau tidak ditemukan.
   */
  static async getById(id: string): Promise<ClassWithRelations | null> {
    const [classResult] = await db
      .select()
      .from(classes)
      .where(eq(classes.id, id))
      .limit(1);

    if (!classResult) return null;

    // Get package pricing relationships
    const packagePricingIds = await ClassPackagePricingService.getPackagePricingIdsByClassId(id);

    // Add package pricing IDs to the class result
    return {
      ...classResult,
      package_pricing_ids: packagePricingIds,
    };
  }

  /**
   * Create class
   *
   * Buat class baru dengan data yang diperlukan.
   * ID akan di-generate otomatis dengan createId().
   * Sekarang menggunakan semua field yang ada di schema dengan validation.
   */
  static async create(data: {
    tenantId: number;
    name: string;
    description?: string;
    categoryId: string;
    subcategoryId?: string;
    duration_value?: number;
    duration_unit?: string;
    level_id?: string;
    delivery_mode?: string;
    is_private?: boolean;
    custom_cancellation_policy?: boolean;
    cancellation_policy_description?: string;
    is_active?: boolean;
    location_id?: string;
    images?: string[];
    items_to_bring?: ClassItemToBring[];
    youtube_links?: ClassYoutubeLinkData[];
    package_pricing_ids?: string[];
    membership_plan_ids?: string[];
  }): Promise<Class> {
    // Validate foreign key references before insert

    // Check if category exists and belongs to tenant
    const [categoryExists] = await db
      .select({ id: class_categories.id })
      .from(class_categories)
      .where(and(
        eq(class_categories.id, data.categoryId),
        eq(class_categories.tenantId, data.tenantId)
      ))
      .limit(1);

    if (!categoryExists) {
      throw new Error(`Category with ID ${data.categoryId} not found or does not belong to tenant ${data.tenantId}`);
    }

    // Check if subcategory exists and belongs to category (if provided)
    if (data.subcategoryId) {
      const [subcategoryExists] = await db
        .select({ id: class_subcategories.id })
        .from(class_subcategories)
        .where(and(
          eq(class_subcategories.id, data.subcategoryId),
          eq(class_subcategories.categoryId, data.categoryId),
          eq(class_subcategories.tenantId, data.tenantId)
        ))
        .limit(1);

      if (!subcategoryExists) {
        throw new Error(`Subcategory with ID ${data.subcategoryId} not found or does not belong to category ${data.categoryId} and tenant ${data.tenantId}`);
      }
    }

    // Check if level exists and belongs to tenant (if provided)
    if (data.level_id) {
      const [levelExists] = await db
        .select({ id: class_levels.id })
        .from(class_levels)
        .where(and(
          eq(class_levels.id, data.level_id),
          eq(class_levels.tenantId, data.tenantId)
        ))
        .limit(1);

      if (!levelExists) {
        throw new Error(`Class level with ID ${data.level_id} not found or does not belong to tenant ${data.tenantId}`);
      }
    }

    // Check if location exists and belongs to tenant (if provided)
    if (data.location_id) {
      const [locationExists] = await db
        .select({ id: locations.id })
        .from(locations)
        .where(and(
          eq(locations.id, data.location_id),
          eq(locations.tenantId, data.tenantId)
        ))
        .limit(1);

      if (!locationExists) {
        throw new Error(`Location with ID ${data.location_id} not found or does not belong to tenant ${data.tenantId}`);
      }
    }

    // All validations passed, proceed with insert
    const [classResult] = await db
      .insert(classes)
      .values({
        id: createId(),
        tenantId: data.tenantId,
        name: data.name,
        description: data.description || null,
        categoryId: data.categoryId,
        subcategoryId: data.subcategoryId || null,
        duration_value: data.duration_value || null,
        duration_unit: data.duration_unit || null,
        level_id: data.level_id || null,
        delivery_mode: data.delivery_mode || null,
        is_private: data.is_private || false,
        custom_cancellation_policy: data.custom_cancellation_policy || false,
        cancellation_policy_description: data.cancellation_policy_description || null,
        is_active: data.is_active !== undefined ? data.is_active : true,
        location_id: data.location_id || null,
        images: data.images || [],
        items_to_bring: data.items_to_bring || [],
        youtube_links: data.youtube_links || [],
        membership_plan_ids: data.membership_plan_ids || [],
      })
      .returning();

    // Handle package pricing relationships
    if (data.package_pricing_ids && data.package_pricing_ids.length > 0) {
      await ClassPackagePricingService.setClassPackagePricing(classResult.id, data.package_pricing_ids);
    }

    return classResult;
  }

  /**
   * Update class
   * 
   * Update class dengan data baru.
   * Hanya field yang di-provide yang akan di-update.
   */
  static async update(id: string, data: {
    name?: string;
    description?: string;
    categoryId?: string;
    subcategoryId?: string;
    duration_value?: number;
    duration_unit?: string;
    level_id?: string;
    delivery_mode?: string;
    is_private?: boolean;
    custom_cancellation_policy?: boolean;
    cancellation_policy_description?: string;
    is_active?: boolean;
    location_id?: string;
    images?: string[];
    items_to_bring?: ClassItemToBring[];
    youtube_links?: ClassYoutubeLinkData[];
    package_pricing_ids?: string[];
    membership_plan_ids?: string[];
  }): Promise<Class | null> {
    const [classResult] = await db
      .update(classes)
      .set({
        name: data.name,
        description: data.description,
        categoryId: data.categoryId,
        subcategoryId: data.subcategoryId,
        duration_value: data.duration_value,
        duration_unit: data.duration_unit,
        level_id: data.level_id,
        delivery_mode: data.delivery_mode,
        is_private: data.is_private,
        custom_cancellation_policy: data.custom_cancellation_policy,
        cancellation_policy_description: data.cancellation_policy_description,
        is_active: data.is_active,
        location_id: data.location_id,
        images: data.images,
        items_to_bring: data.items_to_bring,
        youtube_links: data.youtube_links,
        membership_plan_ids: data.membership_plan_ids,
      })
      .where(eq(classes.id, id))
      .returning();

    // Handle package pricing relationships
    if (data.package_pricing_ids !== undefined) {
      await ClassPackagePricingService.setClassPackagePricing(id, data.package_pricing_ids);
    }

    return classResult || null;
  }

  /**
   * Delete class
   * 
   * Hapus class berdasarkan ID.
   * Return class yang dihapus untuk konfirmasi.
   */
  static async delete(id: string): Promise<Class> {
    // First check if class exists
    const existingClass = await this.getById(id);
    if (!existingClass) {
      throw new Error("Class not found");
    }

    // Delete the class and return the deleted record
    const [deletedClass] = await db
      .delete(classes)
      .where(eq(classes.id, id))
      .returning();

    if (!deletedClass) {
      throw new Error("Failed to delete class");
    }

    return deletedClass;
  }
}
