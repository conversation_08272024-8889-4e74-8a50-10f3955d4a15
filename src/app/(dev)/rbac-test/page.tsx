"use client";

import { useSession } from "next-auth/react";

/**
 * RBAC Test Page
 * 
 * Halaman untuk test semua RBAC functionality:
 * - Session data
 * - Permission checks
 * - Role checks
 * - Guards
 */

export default function RBACTestPage() {
  const { data: session, status } = useSession();

  // Simple helper functions for testing
  const hasPermission = (permission: string): boolean => {
    return session?.user?.permissions?.includes(permission) || false;
  };

  const hasRole = (role: string): boolean => {
    return session?.user?.roles?.includes(role) || false;
  };

  const hasAnyRole = (roles: string[]): boolean => {
    return roles.some(role => hasRole(role));
  };

  const canAccessLocation = (locationId: string): boolean => {
    if (hasRole("super_admin")) return true;
    return session?.user?.accessibleLocations?.includes(locationId) || false;
  };

  if (status === "loading") {
    return (
      <div className="container mx-auto p-6">
        <h1 className="text-2xl font-bold mb-6">RBAC Test Page</h1>
        <p>Loading...</p>
      </div>
    );
  }

  if (status === "unauthenticated") {
    return (
      <div className="container mx-auto p-6">
        <h1 className="text-2xl font-bold mb-6">RBAC Test Page</h1>
        <p>Please login to test RBAC functionality.</p>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-8">
      <h1 className="text-2xl font-bold mb-6">RBAC Test Page</h1>
      
      {/* Session Information */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h2 className="text-xl font-semibold mb-4">Session Information</h2>
        <div className="space-y-2">
          <p><strong>Status:</strong> {status}</p>
          <p><strong>User ID:</strong> {session?.user?.id || "N/A"}</p>
          <p><strong>Email:</strong> {session?.user?.email || "N/A"}</p>
          <p><strong>Name:</strong> {session?.user?.name || "N/A"}</p>
          <p><strong>Tenant ID:</strong> {session?.user?.tenantId || "N/A"}</p>
        </div>
      </div>

      {/* Roles */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h2 className="text-xl font-semibold mb-4">User Roles</h2>
        {session?.user?.roles && session.user.roles.length > 0 ? (
          <ul className="list-disc list-inside space-y-1">
            {session.user.roles.map((role, index) => (
              <li key={index} className="text-green-600">{role}</li>
            ))}
          </ul>
        ) : (
          <p className="text-gray-500">No roles assigned</p>
        )}
      </div>

      {/* Permissions */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h2 className="text-xl font-semibold mb-4">User Permissions</h2>
        {session?.user?.permissions && session.user.permissions.length > 0 ? (
          <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
            {session.user.permissions.map((permission, index) => (
              <span key={index} className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-sm">
                {permission}
              </span>
            ))}
          </div>
        ) : (
          <p className="text-gray-500">No permissions assigned</p>
        )}
      </div>

      {/* Location Access */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h2 className="text-xl font-semibold mb-4">Location Access</h2>
        {session?.user?.accessibleLocations && session.user.accessibleLocations.length > 0 ? (
          <ul className="list-disc list-inside space-y-1">
            {session.user.accessibleLocations.map((locationId, index) => (
              <li key={index} className="text-purple-600">Location ID: {locationId}</li>
            ))}
          </ul>
        ) : (
          <p className="text-gray-500">No location access defined</p>
        )}
      </div>

      {/* Permission Tests */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h2 className="text-xl font-semibold mb-4">Permission Tests</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <h3 className="font-medium mb-2">Role Management</h3>
            <ul className="space-y-1 text-sm">
              <li className={hasPermission("roles:create") ? "text-green-600" : "text-red-600"}>
                ✓ Create Roles: {hasPermission("roles:create") ? "Yes" : "No"}
              </li>
              <li className={hasPermission("roles:read") ? "text-green-600" : "text-red-600"}>
                ✓ Read Roles: {hasPermission("roles:read") ? "Yes" : "No"}
              </li>
              <li className={hasPermission("roles:update") ? "text-green-600" : "text-red-600"}>
                ✓ Update Roles: {hasPermission("roles:update") ? "Yes" : "No"}
              </li>
              <li className={hasPermission("roles:delete") ? "text-green-600" : "text-red-600"}>
                ✓ Delete Roles: {hasPermission("roles:delete") ? "Yes" : "No"}
              </li>
            </ul>
          </div>
          
          <div>
            <h3 className="font-medium mb-2">User Management</h3>
            <ul className="space-y-1 text-sm">
              <li className={hasPermission("users:create") ? "text-green-600" : "text-red-600"}>
                ✓ Create Users: {hasPermission("users:create") ? "Yes" : "No"}
              </li>
              <li className={hasPermission("users:read") ? "text-green-600" : "text-red-600"}>
                ✓ Read Users: {hasPermission("users:read") ? "Yes" : "No"}
              </li>
              <li className={hasPermission("users:update") ? "text-green-600" : "text-red-600"}>
                ✓ Update Users: {hasPermission("users:update") ? "Yes" : "No"}
              </li>
              <li className={hasPermission("users:delete") ? "text-green-600" : "text-red-600"}>
                ✓ Delete Users: {hasPermission("users:delete") ? "Yes" : "No"}
              </li>
            </ul>
          </div>
        </div>
      </div>

      {/* Role Tests */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h2 className="text-xl font-semibold mb-4">Role Tests</h2>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className={`p-3 rounded ${hasRole("super_admin") ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}`}>
            <h4 className="font-medium">Super Admin</h4>
            <p className="text-sm">{hasRole("super_admin") ? "Yes" : "No"}</p>
          </div>
          
          <div className={`p-3 rounded ${hasRole("tenant_admin") ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}`}>
            <h4 className="font-medium">Tenant Admin</h4>
            <p className="text-sm">{hasRole("tenant_admin") ? "Yes" : "No"}</p>
          </div>
          
          <div className={`p-3 rounded ${hasRole("manager") ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}`}>
            <h4 className="font-medium">Manager</h4>
            <p className="text-sm">{hasRole("manager") ? "Yes" : "No"}</p>
          </div>
          
          <div className={`p-3 rounded ${hasRole("staff") ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}`}>
            <h4 className="font-medium">Staff</h4>
            <p className="text-sm">{hasRole("staff") ? "Yes" : "No"}</p>
          </div>
        </div>
      </div>

      {/* Location Access Tests */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h2 className="text-xl font-semibold mb-4">Location Access Tests</h2>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          {["1", "2", "3", "4", "5"].map(locationId => (
            <div key={locationId} className={`p-3 rounded ${canAccessLocation(locationId) ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}`}>
              <h4 className="font-medium">Location {locationId}</h4>
              <p className="text-sm">{canAccessLocation(locationId) ? "Accessible" : "No Access"}</p>
            </div>
          ))}
        </div>
      </div>

      {/* Raw Session Data */}
      <div className="bg-white p-6 rounded-lg shadow">
        <h2 className="text-xl font-semibold mb-4">Raw Session Data</h2>
        <pre className="bg-gray-100 p-4 rounded text-xs overflow-auto max-h-96">
          {JSON.stringify(session, null, 2)}
        </pre>
      </div>
    </div>
  );
}
