import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { z } from "zod";
import { CustomerService } from "@/lib/services/customer.service";

// Validation schema for creating customer
export const createCustomerSchema = z.object({
  tenantId: z.number().int().positive("Tenant ID must be positive"),
  locationId: z.string().optional(),
  email: z.string().email("Invalid email format"),
  mobileNumber: z.string().optional(),
  mobileCountryCode: z.string().max(10, "Country code must be less than 10 characters").optional(),
  firstName: z.string().min(1, "First name is required").max(255, "First name too long"),
  lastName: z.string().max(255, "Last name too long").optional(),
  dateOfBirth: z.string().optional(),
  gender: z.string().max(50, "Gender too long").optional(),
  pricingGroupId: z.string().optional(),
  isActive: z.boolean().optional(),
  notes: z.string().max(1000, "Notes too long").optional(),
  // Address fields
  addressLine1: z.string().max(255, "Address line 1 too long").optional(),
  addressLine2: z.string().max(255, "Address line 2 too long").optional(),
  city: z.string().max(255, "City too long").optional(),
  state: z.string().max(255, "State too long").optional(),
  zip: z.string().max(255, "ZIP code too long").optional(),
  country: z.string().max(255, "Country too long").optional(),
  // Waiver fields
  waiverFormIds: z.array(z.string()).optional(),
});

// Validation schema for bulk operations
export const bulkOperationSchema = z.object({
  ids: z.array(z.string().min(1)),
  action: z.enum(["activate", "deactivate", "delete"]),
});

const customerService = new CustomerService();

// GET /api/customers - Get customers list
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const tenantIdParam = searchParams.get("tenantId");
    const locationId = searchParams.get("locationId");
    const pricingGroupId = searchParams.get("pricingGroupId");
    const activeOnly = searchParams.get("activeOnly") === "true";
    const search = searchParams.get("search");
    const limit = searchParams.get("limit");
    const offset = searchParams.get("offset");

    let customers;
    const options: any = {
      orderField: 'firstName',
      orderBy: 'asc',
    };

    if (limit) options.limit = parseInt(limit);
    if (offset) options.offset = parseInt(offset);

    if (tenantIdParam) {
      const tenantIdNum = parseInt(tenantIdParam);
      if (isNaN(tenantIdNum)) {
        return NextResponse.json(
          { error: "Invalid tenant ID" },
          { status: 400 }
        );
      }
      
      options.tenantId = tenantIdNum;
      
      if (activeOnly) {
        customers = await customerService.getActiveByTenantId(tenantIdNum);
      } else {
        if (search) {
          customers = await customerService.getAll({ tenantId: tenantIdNum, filters: { search } });
        } else {
          // Apply filters
          const filters: any = {};
          if (locationId) filters.locationId = locationId;
          if (pricingGroupId) filters.pricingGroupId = pricingGroupId;
          
          options.filters = filters;
          customers = await customerService.getByTenantId(tenantIdNum, options);
        }
      }
    } else {
      if (search) {
        customers = await customerService.getAll({ filters: { search } });
      } else {
        // Apply filters
        const filters: any = {};
        if (activeOnly) filters.isActive = true;
        if (locationId) filters.locationId = locationId;
        if (pricingGroupId) filters.pricingGroupId = pricingGroupId;
        
        options.filters = filters;
        customers = await customerService.getAll(options);
      }
    }

    return NextResponse.json({ data: customers });
  } catch (error) {
    console.error("Error fetching customers:", error);
    return NextResponse.json(
      { error: "Failed to fetch customers" },
      { status: 500 }
    );
  }
}

// POST /api/customers - Create customer
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = createCustomerSchema.parse(body);

    const data = await customerService.create(validatedData);

    return NextResponse.json({ data }, { status: 201 });
  } catch (error) {
    console.error("Error creating customer:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to create customer" },
      { status: 500 }
    );
  }
}

// PUT /api/customers - Bulk operations
export async function PUT(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = bulkOperationSchema.parse(body);

    let result;
    switch (validatedData.action) {
      case "activate":
        result = await customerService.bulkToggleActive(validatedData.ids, true);
        break;
      case "deactivate":
        result = await customerService.bulkToggleActive(validatedData.ids, false);
        break;
      case "delete":
        result = await customerService.bulkDelete(validatedData.ids);
        break;
      default:
        return NextResponse.json(
          { error: "Invalid action" },
          { status: 400 }
        );
    }

    return NextResponse.json({
      message: `Successfully ${validatedData.action}d ${result.length} customers`,
      data: result
    });
  } catch (error) {
    console.error("Error in bulk operation:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to perform bulk operation" },
      { status: 500 }
    );
  }
}
