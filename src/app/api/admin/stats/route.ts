import { NextRequest, NextResponse } from "next/server";
import { requireAdmin } from "@/lib/auth/utils";
import { db } from "@/lib/db";
import { users, creditTransactions, usageLogs } from "@/lib/db/schema";
import { eq, gte, count, sum } from "drizzle-orm";

export async function GET(request: NextRequest) {
  try {
    // Check admin access
    await requireAdmin();

    // Get current month start
    const startOfMonth = new Date();
    startOfMonth.setDate(1);
    startOfMonth.setHours(0, 0, 0, 0);

    // Total users
    const [totalUsersResult] = await db
      .select({ count: count() })
      .from(users);
    const totalUsers = totalUsersResult.count;

    // Active users
    const [activeUsersResult] = await db
      .select({ count: count() })
      .from(users)
      .where(eq(users.isActive, true));
    const activeUsers = activeUsersResult.count;

    // New users this month
    const [newUsersResult] = await db
      .select({ count: count() })
      .from(users)
      .where(gte(users.createdAt, startOfMonth));
    const newUsersThisMonth = newUsersResult.count;

    // Total credits issued (purchased)
    const [totalCreditsResult] = await db
      .select({ total: sum(creditTransactions.amount) })
      .from(creditTransactions)
      .where(eq(creditTransactions.type, "purchase"));
    const totalCreditsIssued = Number(totalCreditsResult.total || 0);

    // Credits used this month
    const [creditsUsedResult] = await db
      .select({ total: sum(creditTransactions.amount) })
      .from(creditTransactions)
      .where(
        eq(creditTransactions.type, "usage") &&
        gte(creditTransactions.createdAt, startOfMonth)
      );
    const creditsUsedThisMonth = Math.abs(Number(creditsUsedResult.total || 0));

    // Calculate revenue (assuming $0.01 per credit for simplicity)
    // In real implementation, you'd track actual payment amounts
    const totalRevenue = totalCreditsIssued * 1; // $0.01 per credit
    const revenueThisMonth = creditsUsedThisMonth * 1; // Simplified calculation

    // API calls this month
    const [apiCallsResult] = await db
      .select({ count: count() })
      .from(usageLogs)
      .where(gte(usageLogs.createdAt, startOfMonth));
    const apiCallsThisMonth = apiCallsResult.count;

    return NextResponse.json({
      totalUsers,
      activeUsers,
      totalCreditsIssued,
      totalRevenue,
      newUsersThisMonth,
      creditsUsedThisMonth,
      revenueThisMonth,
      apiCallsThisMonth,
    });
  } catch (error) {
    console.error("Error fetching admin stats:", error);
    
    if (error instanceof Error && error.message === "Admin access required") {
      return NextResponse.json(
        { error: "Admin access required" },
        { status: 403 }
      );
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
