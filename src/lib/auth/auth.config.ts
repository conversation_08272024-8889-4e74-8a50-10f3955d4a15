import { NextAuthConfig } from "next-auth";
import Credentials from "next-auth/providers/credentials";

/**
 * NextAuth Configuration - Edge Runtime Compatible
 *
 * Simplified config untuk edge runtime compatibility.
 * RBAC data akan di-load di client side menggunakan API routes.
 */

export const authConfig: NextAuthConfig = {
  providers: [
    Credentials({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        // Simple hardcoded user for testing
        // In production, you would validate against your database
        if (credentials?.email === "<EMAIL>" && credentials?.password === "password") {
          return {
            id: "w4k3remhjv6pdluhqguhjfgd",
            email: "<EMAIL>",
            name: "Admin User",
            image: null,
            tenantId: null
          };
        }
        return null;
      }
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      // Basic JWT handling - RBAC enrichment akan dilakukan di client side
      console.log("JWT callback - user:", user ? {
        id: user.id,
        email: user.email,
        name: user.name
      } : null);

      if (user) {
        token.id = user.id;
        token.email = user.email;
        token.name = user.name;
        token.image = user.image;
        token.tenantId = user.tenantId;
      }

      console.log("JWT callback - token result:", {
        id: token.id,
        email: token.email,
        name: token.name
      });

      return token;
    },
    async session({ session, token }) {
      // Basic session - RBAC data akan di-load di client side
      console.log("Session callback - token:", {
        id: token.id,
        email: token.email,
        name: token.name
      });

      if (token) {
        // Temporary fix: hardcode user <NAME_EMAIL>
        if (token.email === "<EMAIL>" && !token.id) {
          session.user.id = "w4k3remhjv6pdluhqguhjfgd"; // Known admin user ID
        } else {
          session.user.id = token.id as string;
        }

        session.user.email = token.email as string;
        session.user.name = token.name as string;
        session.user.image = token.image as string;
        session.user.tenantId = token.tenantId as number;
        // RBAC data akan di-load secara terpisah untuk avoid edge runtime issues
      }

      console.log("Session callback - result:", {
        userId: session.user.id,
        email: session.user.email
      });

      return session;
    },
    authorized({ auth, request: { nextUrl } }) {
      const isLoggedIn = !!auth?.user;
      const isOnDashboard = nextUrl.pathname.startsWith("/dashboard");
      const isOnAuth = nextUrl.pathname.startsWith("/auth");

      // Redirect logic
      if (isOnDashboard) {
        if (isLoggedIn) return true;
        return false; // Redirect to login page
      } else if (isOnAuth) {
        if (isLoggedIn) return Response.redirect(new URL("/dashboard", nextUrl));
        return true;
      }

      return true;
    },
  },
  pages: {
    signIn: "/auth/signin",
    signOut: "/auth/signout",
    error: "/auth/error",
  },
  session: {
    strategy: "jwt",
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  secret: process.env.NEXTAUTH_SECRET,
};
