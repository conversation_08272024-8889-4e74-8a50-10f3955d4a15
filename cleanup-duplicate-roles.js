// Cleanup script to remove duplicate roles
const { drizzle } = require('drizzle-orm/postgres-js');
const postgres = require('postgres');
const { roles, role_permissions, user_roles } = require('./src/lib/db/schema.ts');
const { eq, and, sql } = require('drizzle-orm');

// Database connection
const connectionString = process.env.DATABASE_URL || 'postgresql://citizix_user:S3cret@127.0.0.1:5433/saas_app';
const client = postgres(connectionString);
const db = drizzle(client);

async function cleanupDuplicateRoles() {
  try {
    console.log('🧹 Starting duplicate role cleanup...');

    // Get all roles grouped by name
    const duplicateRoles = await db
      .select({
        name: roles.name,
        count: sql`count(*)`,
        ids: sql`array_agg(${roles.id})`,
        first_id: sql`min(${roles.id})`
      })
      .from(roles)
      .groupBy(roles.name)
      .having(sql`count(*) > 1`);

    console.log(`Found ${duplicateRoles.length} role names with duplicates`);

    for (const duplicate of duplicateRoles) {
      const roleIds = duplicate.ids;
      const keepId = duplicate.first_id;
      const deleteIds = roleIds.filter(id => id !== keepId);

      console.log(`\n📝 Processing role: ${duplicate.name}`);
      console.log(`  - Total duplicates: ${duplicate.count}`);
      console.log(`  - Keeping: ${keepId}`);
      console.log(`  - Deleting: ${deleteIds.join(', ')}`);

      // Update user_roles to point to the kept role
      for (const deleteId of deleteIds) {
        await db
          .update(user_roles)
          .set({ roleId: keepId })
          .where(eq(user_roles.roleId, deleteId));
      }

      // Update role_permissions to point to the kept role
      for (const deleteId of deleteIds) {
        await db
          .update(role_permissions)
          .set({ roleId: keepId })
          .where(eq(role_permissions.roleId, deleteId));
      }

      // Delete duplicate roles
      for (const deleteId of deleteIds) {
        await db.delete(roles).where(eq(roles.id, deleteId));
      }

      console.log(`  ✅ Cleaned up ${deleteIds.length} duplicates for ${duplicate.name}`);
    }

    console.log('\n✅ Duplicate role cleanup completed!');

    // Verify cleanup
    const remainingRoles = await db.select().from(roles);
    console.log(`\n📊 Final count: ${remainingRoles.length} roles remaining`);
    
    const roleNames = remainingRoles.map(r => r.name);
    const uniqueNames = [...new Set(roleNames)];
    console.log(`📊 Unique role names: ${uniqueNames.length}`);
    
    if (roleNames.length === uniqueNames.length) {
      console.log('🎉 No more duplicates found!');
    } else {
      console.log('⚠️ Some duplicates may still exist');
    }

  } catch (error) {
    console.error('❌ Cleanup failed:', error);
    throw error;
  } finally {
    await client.end();
  }
}

// Run cleanup
cleanupDuplicateRoles()
  .then(() => {
    console.log('🎉 Cleanup completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Cleanup failed:', error);
    process.exit(1);
  });
