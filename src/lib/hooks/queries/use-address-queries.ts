"use client";

import { useQuery, useMutation, useQueryClient, useInfiniteQuery } from "@tanstack/react-query";
import { Address, NewAddress } from "@/lib/db/schema";

// Import global query keys
import { addressKeys } from "./query-keys";

// API Functions
const addressApi = {
  getByTenant: async (tenantId: number, filters?: {
    status?: 'accepted' | 'pending';
    country?: string;
    state?: string;
    search?: string;
  }): Promise<Address[]> => {
    const params = new URLSearchParams({ tenantId: tenantId.toString() });
    
    if (filters?.status) params.append('status', filters.status);
    if (filters?.country) params.append('country', filters.country);
    if (filters?.state) params.append('state', filters.state);
    if (filters?.search) params.append('search', filters.search);

    const response = await fetch(`/api/addresses?${params}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch addresses: ${response.statusText}`);
    }
    const data = await response.json();
    return data.addresses;
  },

  getById: async (id: string): Promise<Address> => {
    const response = await fetch(`/api/addresses/${id}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch address: ${response.statusText}`);
    }
    const data = await response.json();
    return data.address;
  },

  create: async (data: Omit<NewAddress, "id" | "createdAt">): Promise<Address> => {
    const response = await fetch('/api/addresses', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to create address');
    }
    const result = await response.json();
    return result.address;
  },

  update: async ({ id, data }: { 
    id: string; 
    data: Partial<NewAddress> 
  }): Promise<Address> => {
    const response = await fetch(`/api/addresses/${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to update address');
    }
    const result = await response.json();
    return result.address;
  },

  delete: async (id: string): Promise<void> => {
    const response = await fetch(`/api/addresses/${id}`, {
      method: 'DELETE',
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to delete address');
    }
  },

  accept: async (id: string): Promise<Address> => {
    const response = await fetch(`/api/addresses/${id}/accept`, {
      method: 'POST',
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to accept address');
    }
    const result = await response.json();
    return result.address;
  },

  bulkCreate: async (addresses: Omit<NewAddress, "id" | "createdAt">[]): Promise<Address[]> => {
    const response = await fetch('/api/addresses/bulk', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ addresses }),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to bulk create addresses');
    }
    const result = await response.json();
    return result.addresses;
  },

  getStats: async (tenantId: number): Promise<{
    totalAddresses: number;
    acceptedAddresses: number;
    pendingAddresses: number;
  }> => {
    const response = await fetch(`/api/addresses/stats?tenantId=${tenantId}`);
    if (!response.ok) {
      throw new Error('Failed to fetch address stats');
    }
    return response.json();
  },
};

// Query Hooks
export function useAddresses(tenantId: number | null, filters?: {
  status?: 'accepted' | 'pending';
  country?: string;
  state?: string;
  search?: string;
}) {
  return useQuery({
    queryKey: addressKeys.list({ tenantId: tenantId || 0, ...filters }),
    queryFn: () => addressApi.getByTenant(tenantId!, filters),
    enabled: !!tenantId && tenantId > 0,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
}

export function useAddress(id: string) {
  return useQuery({
    queryKey: addressKeys.detail(id),
    queryFn: () => addressApi.getById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useAddressStats(tenantId: number | null) {
  return useQuery({
    queryKey: addressKeys.stats(tenantId),
    queryFn: () => addressApi.getStats(tenantId!),
    enabled: !!tenantId && tenantId > 0,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Infinite Query for large address lists
export function useInfiniteAddresses(tenantId: number | null, filters?: {
  status?: 'accepted' | 'pending';
  country?: string;
  state?: string;
  search?: string;
}) {
  return useInfiniteQuery({
    queryKey: [...addressKeys.list({ tenantId, ...filters }), 'infinite'],
    queryFn: async ({ pageParam = 0 }) => {
      const params = new URLSearchParams({
        tenantId: tenantId!.toString(),
        page: pageParam.toString(),
        limit: '20'
      });
      
      if (filters?.status) params.append('status', filters.status);
      if (filters?.country) params.append('country', filters.country);
      if (filters?.state) params.append('state', filters.state);
      if (filters?.search) params.append('search', filters.search);

      const response = await fetch(`/api/addresses/paginated?${params}`);
      if (!response.ok) {
        throw new Error('Failed to fetch addresses');
      }
      return response.json();
    },
    getNextPageParam: (lastPage) => lastPage.nextPage,
    initialPageParam: 0,
    enabled: !!tenantId && tenantId > 0,
  });
}

// Mutation Hooks
export function useCreateAddress() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: addressApi.create,
    onSuccess: (newAddress) => {
      // Invalidate tenant addresses
      queryClient.invalidateQueries({ 
        queryKey: addressKeys.byTenant(newAddress.tenantId!) 
      });
      
      // Invalidate lists with filters
      queryClient.invalidateQueries({ queryKey: addressKeys.lists() });
      
      // Invalidate stats
      queryClient.invalidateQueries({ 
        queryKey: addressKeys.stats(newAddress.tenantId!) 
      });
    },
  });
}

export function useUpdateAddress() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: addressApi.update,
    onMutate: async ({ id, data }) => {
      // Cancel outgoing refetches
      await queryClient.cancelQueries({ queryKey: addressKeys.detail(id) });

      // Snapshot previous value
      const previousAddress = queryClient.getQueryData(addressKeys.detail(id));

      // Optimistically update
      queryClient.setQueryData(addressKeys.detail(id), (old: Address | undefined) => {
        if (!old) return old;
        return { ...old, ...data };
      });

      return { previousAddress, id };
    },
    onError: (err, variables, context) => {
      if (context?.previousAddress) {
        queryClient.setQueryData(
          addressKeys.detail(context.id),
          context.previousAddress
        );
      }
    },
    onSettled: (data, error, variables) => {
      queryClient.invalidateQueries({ queryKey: addressKeys.detail(variables.id) });
      if (data) {
        queryClient.invalidateQueries({ 
          queryKey: addressKeys.byTenant(data.tenantId!) 
        });
        queryClient.invalidateQueries({ queryKey: addressKeys.lists() });
      }
    },
  });
}

export function useDeleteAddress() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: addressApi.delete,
    onMutate: async (id) => {
      // Get the address data before deletion for rollback
      const address = queryClient.getQueryData(addressKeys.detail(id)) as Address;
      
      // Remove from cache optimistically
      queryClient.removeQueries({ queryKey: addressKeys.detail(id) });
      
      return { address };
    },
    onError: (err, id, context) => {
      // Restore the address if deletion failed
      if (context?.address) {
        queryClient.setQueryData(addressKeys.detail(id), context.address);
      }
    },
    onSettled: (data, error, id, context) => {
      if (context?.address) {
        queryClient.invalidateQueries({ 
          queryKey: addressKeys.byTenant(context.address.tenantId!) 
        });
        queryClient.invalidateQueries({ queryKey: addressKeys.lists() });
        queryClient.invalidateQueries({ 
          queryKey: addressKeys.stats(context.address.tenantId!) 
        });
      }
    },
  });
}

export function useAcceptAddress() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: addressApi.accept,
    onSuccess: (updatedAddress) => {
      // Update the specific address
      queryClient.setQueryData(addressKeys.detail(updatedAddress.id), updatedAddress);
      
      // Invalidate tenant addresses and stats
      queryClient.invalidateQueries({ 
        queryKey: addressKeys.byTenant(updatedAddress.tenantId!) 
      });
      queryClient.invalidateQueries({ 
        queryKey: addressKeys.stats(updatedAddress.tenantId!) 
      });
      queryClient.invalidateQueries({ queryKey: addressKeys.lists() });
    },
  });
}

export function useBulkCreateAddresses() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: addressApi.bulkCreate,
    onSuccess: (newAddresses) => {
      // Invalidate all address-related queries
      queryClient.invalidateQueries({ queryKey: addressKeys.all });
      
      // If all addresses belong to the same tenant, invalidate tenant-specific queries
      const tenantIds = [...new Set(newAddresses.map(addr => addr.tenantId).filter(Boolean))];
      tenantIds.forEach(tenantId => {
        queryClient.invalidateQueries({ queryKey: addressKeys.byTenant(tenantId!) });
        queryClient.invalidateQueries({ queryKey: addressKeys.stats(tenantId!) });
      });
    },
  });
}
