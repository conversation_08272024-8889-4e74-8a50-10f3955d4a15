import { NextRequest, NextResponse } from "next/server";
import { RoleService } from "@/lib/services/role.service";
import { withRB<PERSON> } from "@/lib/middleware/rbac-middleware";

/**
 * API Routes untuk System Roles
 */

/**
 * GET /api/roles/system
 * Get all system roles (built-in roles)
 */
export const GET = withRBAC(
  async (request: NextRequest) => {
    try {
      // Call service untuk get system roles
      const systemRoles = await RoleService.getSystemRoles();

      return NextResponse.json({
        success: true,
        data: systemRoles,
      });
    } catch (error) {
      console.error("GET /api/roles/system error:", error);
      return NextResponse.json(
        { success: false, error: "Internal server error" },
        { status: 500 }
      );
    }
  },
  { module: "roles", action: "read" }
);
