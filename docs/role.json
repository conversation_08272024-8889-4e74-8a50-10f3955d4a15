[{"id": "bnxv5z69ocf24hu9cvvxfnvy", "action": "access", "module": "admin", "display_name": "Admin Access"}, {"id": "ujb8bmjw5pqcp6lpzhfc713f", "action": "create", "module": "bookings", "display_name": "Create Bookings"}, {"id": "mmscpp13bhqw886bqhv6imem", "action": "delete", "module": "bookings", "display_name": "Delete Bookings"}, {"id": "z7vntaxbvnvdk0d17pnhnaro", "action": "manage", "module": "bookings", "display_name": "Manage Bookings"}, {"id": "w6y098e18v766pi792jjrey3", "action": "read", "module": "bookings", "display_name": "View Bookings"}, {"id": "pa8wzwoaorvaj0vejlnhyguw", "action": "update", "module": "bookings", "display_name": "Update Bookings"}, {"id": "c9mcup9wofiibzic4qg5rlt7", "action": "create", "module": "classes", "display_name": "Create Classes"}, {"id": "e1i84k42yrdpe1q8grrizcbz", "action": "delete", "module": "classes", "display_name": "Delete Classes"}, {"id": "rm9cwqr8z3t9mgly4r7retxq", "action": "manage", "module": "classes", "display_name": "Manage Classes"}, {"id": "gb3rv8w6lq3o9yw2smhqtcly", "action": "read", "module": "classes", "display_name": "View Classes"}, {"id": "m3d85kkfagogy61j6apqcif2", "action": "update", "module": "classes", "display_name": "Update Classes"}, {"id": "qycqv3i7ag03rvfp642u7nqn", "action": "create", "module": "customers", "display_name": "Create Customers"}, {"id": "wunqjdx1tgufeyd3dng9pz0f", "action": "delete", "module": "customers", "display_name": "Delete Customers"}, {"id": "zzvpgbhjbm5c7iozfyfj64bq", "action": "manage", "module": "customers", "display_name": "Manage Customers"}, {"id": "t3zqmq155a36tm6ercjhdqmx", "action": "read", "module": "customers", "display_name": "View Customers"}, {"id": "it9b00j4hidy9qrgzxdhr7fk", "action": "update", "module": "customers", "display_name": "Update Customers"}, {"id": "q4cbwcxmrzax45bb3a556eku", "action": "create", "module": "equipment", "display_name": "Create Equipment"}, {"id": "eckmp93b386scwm51j95tf9t", "action": "delete", "module": "equipment", "display_name": "Delete Equipment"}, {"id": "k8f5ohysfk5m1u0oa51qd962", "action": "manage", "module": "equipment", "display_name": "Manage Equipment"}, {"id": "yr8xmgoxoc88bxlk9z8gwhbf", "action": "read", "module": "equipment", "display_name": "View Equipment"}, {"id": "exdw8outfizr5wcw5pbvchhu", "action": "update", "module": "equipment", "display_name": "Update Equipment"}, {"id": "eoke74yyq2klcmjapw3he6lp", "action": "create", "module": "locations", "display_name": "Create Locations"}, {"id": "v0tueu8wl6o46ia6n2l907rp", "action": "delete", "module": "locations", "display_name": "Delete Locations"}, {"id": "m4ltki3e291b8x6jwz3d3pa0", "action": "manage", "module": "locations", "display_name": "Manage Locations"}, {"id": "zvzhtlpx0cu2qjm9sl1c2aek", "action": "read", "module": "locations", "display_name": "View Locations"}, {"id": "q2xew0bp6f44jan2epdztlh3", "action": "update", "module": "locations", "display_name": "Update Locations"}, {"id": "ti037pnsj2fo46dntmkqwvy0", "action": "create", "module": "packages", "display_name": "Create Packages"}, {"id": "l3zebpcqgprmacg2qz0i57d5", "action": "delete", "module": "packages", "display_name": "Delete Packages"}, {"id": "e6iyn24w7xaw84zg4dts98ri", "action": "manage", "module": "packages", "display_name": "Manage Packages"}, {"id": "ejwufacg7memthen3cilwdom", "action": "read", "module": "packages", "display_name": "View Packages"}, {"id": "zjytiff07cd8yikaf7dj5zvh", "action": "update", "module": "packages", "display_name": "Update Packages"}, {"id": "perm_permissions_read_bb96edd9", "action": "read", "module": "permissions", "display_name": "Read Permissions"}, {"id": "m1js3iqval1gnk4ldl2if9eh", "action": "export", "module": "reports", "display_name": "Export Reports"}, {"id": "gpn4nhoqcxdzz2jqnrkd20nj", "action": "read", "module": "reports", "display_name": "View Reports"}, {"id": "enbc1lx0jonel11o3brg0ygq", "action": "assign", "module": "roles", "display_name": "Assign Roles"}, {"id": "drhg519kvdjq9nx2t5q9vs5w", "action": "create", "module": "roles", "display_name": "Create Roles"}, {"id": "suto4bn37z07rqncgcg2h7sr", "action": "delete", "module": "roles", "display_name": "Delete Roles"}, {"id": "e313woa6z4tql1f9vdgdjrcy", "action": "read", "module": "roles", "display_name": "View Roles"}, {"id": "bts4hmqltcqfcm6fya3rsm3t", "action": "revoke", "module": "roles", "display_name": "Revoke Roles"}, {"id": "wr1eynpkk2gapsf6umfxq9a1", "action": "update", "module": "roles", "display_name": "Update Roles"}, {"id": "dx1sodonziwi0ni7rcehitwj", "action": "manage", "module": "system", "display_name": "Manage System"}, {"id": "mpugxnyxukpta1j9a50030rd", "action": "manage", "module": "tenant", "display_name": "Manage Tenant"}, {"id": "htcxmtxh5qa2o3kkcol1ou1s", "action": "read", "module": "tenant", "display_name": "View Tenant"}, {"id": "nxiasltm14rdys080v9glpg7", "action": "create", "module": "users", "display_name": "Create Users"}, {"id": "yp2snunq7prbxm695592peaq", "action": "delete", "module": "users", "display_name": "Delete Users"}, {"id": "w9tw8dg0buiwa96xcnqkjg6k", "action": "manage", "module": "users", "display_name": "Manage Users"}, {"id": "mrngdgwkgrsy7tknetikhtfh", "action": "read", "module": "users", "display_name": "View Users"}, {"id": "perm_users_read_a5dce4b8", "action": "read", "module": "users", "display_name": "Read Users"}, {"id": "fejz4zgjt9nxiqn77ph6e3kd", "action": "update", "module": "users", "display_name": "Update Users"}, {"id": "eoke74yyq2klcmjapw3he6l2", "action": "create", "module": "facilities", "display_name": "Create Facilities"}, {"id": "v0tueu8wl6o46ia6n2l907r7", "action": "delete", "module": "facilities", "display_name": "Delete Failities"}, {"id": "m4ltki3e291b8x6jwz3d3pa9", "action": "manage", "module": "facilities", "display_name": "Manage Failities"}, {"id": "zvzhtlpx0cu2qjm9sl1c2ae1", "action": "read", "module": "facilities", "display_name": "View Failities"}, {"id": "q2xew0bp6f44jan2epdztlh4", "action": "update", "module": "facilities", "display_name": "Update Failities"}]