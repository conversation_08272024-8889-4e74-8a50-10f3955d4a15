import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { z } from "zod";
import { packagePricingService } from "@/lib/services/package-pricing.service";

// Validation schema for updates
const updatePackagePricingSchema = z.object({
  packageId: z.string().min(1, "Package ID is required").optional(),
  pricingGroupId: z.string().min(1, "Pricing group ID is required").optional(),
  price: z.number().min(0, "Price cannot be negative").optional(),
  creditAmount: z.number().int().min(0, "Credit amount cannot be negative").optional(),
  currency: z.string().min(1, "Currency is required").max(10, "Currency code too long").optional(),
});

/**
 * GET /api/package-pricing/[id]
 * Get a specific package pricing by ID with details
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    console.log(`💰 [GET /api/package-pricing/${id}] Fetching package pricing with details`);

    const packagePricing = await packagePricingService.getWithDetails(id);

    if (!packagePricing) {
      return NextResponse.json({ error: "Package pricing not found" }, { status: 404 });
    }

    // For now, skip tenant verification since we don't have tenantId in session
    // TODO: Add proper tenant verification when session includes tenantId

    return NextResponse.json({
      success: true,
      data: packagePricing
    });
  } catch (error) {
    console.error(`💰 [GET /api/package-pricing/${params}] Error:`, error);
    return NextResponse.json(
      { error: "Failed to fetch package pricing" },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/package-pricing/[id]
 * Update a specific package pricing
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    const body = await request.json();
    const validatedData = updatePackagePricingSchema.parse(body);

    console.log(`💰 [PUT /api/package-pricing/${id}] Updating package pricing:`, validatedData);

    // Check if the package pricing exists
    const existingPricing = await packagePricingService.getById(id);
    if (!existingPricing) {
      return NextResponse.json({ error: "Package pricing not found" }, { status: 404 });
    }

    const updatedPricing = await packagePricingService.update(id, validatedData);

    return NextResponse.json({
      success: true,
      data: updatedPricing,
      message: "Package pricing updated successfully"
    });
  } catch (error) {
    console.error(`💰 [PUT /api/package-pricing/${params}] Error:`, error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid package pricing data", details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error && error.message.includes("already exists")) {
      return NextResponse.json(
        { error: error.message },
        { status: 409 }
      );
    }

    return NextResponse.json(
      { error: "Failed to update package pricing" },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/package-pricing/[id]
 * Delete a specific package pricing
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id } = await params;
    console.log(`💰 [DELETE /api/package-pricing/${id}] Deleting package pricing`);

    // Check if the package pricing exists
    const existingPricing = await packagePricingService.getById(id);
    if (!existingPricing) {
      return NextResponse.json({ error: "Package pricing not found" }, { status: 404 });
    }

    const deletedPricing = await packagePricingService.delete(id);

    return NextResponse.json({
      success: true,
      data: deletedPricing,
      message: "Package pricing deleted successfully"
    });
  } catch (error) {
    console.error(`💰 [DELETE /api/package-pricing/${params}] Error:`, error);
    return NextResponse.json(
      { error: "Failed to delete package pricing" },
      { status: 500 }
    );
  }
}
