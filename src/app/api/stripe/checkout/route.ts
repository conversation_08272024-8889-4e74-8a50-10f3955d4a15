import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { getCurrentUser } from "@/lib/auth/utils";
import { createCheckoutSession } from "@/lib/stripe/utils";
import { getCreditPackage } from "@/lib/stripe/config";
import { getAppUrl } from "@/lib/env";

const checkoutSchema = z.object({
  packageId: z.string(),
  organizationId: z.string().optional(),
});

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const user = await getCurrentUser();
    if (!user) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { packageId, organizationId } = checkoutSchema.parse(body);

    // Get credit package
    const creditPackage = getCreditPackage(packageId);
    if (!creditPackage) {
      return NextResponse.json(
        { error: "Invalid package ID" },
        { status: 400 }
      );
    }

    // Create checkout session
    const session = await createCheckoutSession({
      userId: user.id,
      organizationId,
      priceId: creditPackage.stripePriceId,
      credits: creditPackage.credits,
      successUrl: `${getAppUrl()}/billing/success?session_id={CHECKOUT_SESSION_ID}`,
      cancelUrl: `${getAppUrl()}/billing?canceled=true`,
    });

    return NextResponse.json({ sessionId: session.id });
  } catch (error) {
    console.error("Checkout error:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
