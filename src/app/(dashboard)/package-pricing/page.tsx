"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { 
  DollarSign, 
  Plus, 
  Building, 
  BarChart3,
  AlertCircle,
  CheckCircle,
  TrendingUp,
  Package,
  Users,
  Coins
} from "lucide-react";
import { PackagePricingForm } from "@/components/forms/package-pricing-form";
import { PackagePricingList } from "@/components/package-pricing/package-pricing-list";
import { SuccessToast } from "@/components/ui/success-toast";
import { SuccessModal } from "@/components/ui/success-modal";
import { Confetti } from "@/components/ui/confetti";
import { PackagePricing } from "@/lib/db/schema";
import { 
  usePackagePricingList, 
  useCreatePackagePricing, 
  useUpdatePackagePricing,
  usePackagePricingStats,
  CreatePackagePricingData,
  UpdatePackagePricingData,
  PackagePricingWithDetails
} from "@/lib/hooks/queries/use-package-pricing-queries";
import { useTenants } from "@/lib/hooks/queries/use-tenant-queries";

export default function PackagePricingPage() {
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingPricing, setEditingPricing] = useState<PackagePricingWithDetails | null>(null);
  const [selectedTenantId, setSelectedTenantId] = useState<number | null>(null);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [submitSuccess, setSubmitSuccess] = useState<string | null>(null);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showConfetti, setShowConfetti] = useState(false);

  // Fetch data
  const { data: packagePricing = [], isLoading: pricingLoading } = usePackagePricingList({}, 1);
  const { data: tenantsData, isLoading: tenantsLoading } = useTenants();
  const { data: stats = { total: 0, byPackage: {}, byPricingGroup: {}, averagePrice: 0, totalRevenue: 0, currencies: [] }, isLoading: statsLoading } = usePackagePricingStats(1);
  
  const tenants = tenantsData?.tenants || [];
  const createPricingMutation = useCreatePackagePricing();
  const updatePricingMutation = useUpdatePackagePricing();

  const handleCreatePricing = async (data: CreatePackagePricingData) => {
    try {
      setSubmitError(null);
      await createPricingMutation.mutateAsync(data);
      setSubmitSuccess("Package pricing created successfully!");
      setIsFormOpen(false);
      setShowSuccessModal(true);
      setShowConfetti(true);
      setTimeout(() => {
        setShowSuccessModal(false);
        setShowConfetti(false);
        setSubmitSuccess(null);
      }, 3000);
    } catch (error) {
      setSubmitError(error instanceof Error ? error.message : "Failed to create package pricing");
    }
  };

  const handleUpdatePricing = async (data: UpdatePackagePricingData) => {
    if (!editingPricing) return;

    try {
      setSubmitError(null);
      await updatePricingMutation.mutateAsync({ 
        id: editingPricing.id, 
        data: data
      });
      setSubmitSuccess("Package pricing updated successfully!");
      setEditingPricing(null);
      setTimeout(() => setSubmitSuccess(null), 3000);
    } catch (error) {
      setSubmitError(error instanceof Error ? error.message : "Failed to update package pricing");
    }
  };

  const handleEdit = (pricing: PackagePricingWithDetails) => {
    setEditingPricing(pricing);
  };

  const handleAdd = () => {
    setEditingPricing(null);
    setIsFormOpen(true);
  };

  const handleCloseForm = () => {
    setIsFormOpen(false);
    setEditingPricing(null);
    setSubmitError(null);
  };

  // Calculate stats by tenant
  const statsByTenant = tenants.reduce((acc, tenant) => {
    const tenantPricing = packagePricing.filter(p => p.package?.tenantId === tenant.id);
    acc[tenant.id] = {
      total: tenantPricing.length,
      totalRevenue: tenantPricing.reduce((sum, p) => sum + (p.price || 0), 0),
    };
    return acc;
  }, {} as Record<number, { total: number; totalRevenue: number }>);

  if (tenantsLoading || pricingLoading || statsLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Package Pricing</h1>
          <p className="text-muted-foreground">
            Manage pricing for packages across different pricing groups
          </p>
        </div>
        <Button onClick={handleAdd}>
          <Plus className="h-4 w-4 mr-2" />
          Add Pricing
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Pricing Rules</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">
              Across all packages
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Unique Packages</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Object.keys(stats.byPackage).length}
            </div>
            <p className="text-xs text-muted-foreground">
              With pricing configured
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pricing Groups</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Object.keys(stats.byPricingGroup).length}
            </div>
            <p className="text-xs text-muted-foreground">
              With packages assigned
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Price</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${stats.averagePrice.toFixed(2)}
            </div>
            <p className="text-xs text-muted-foreground">
              Across all pricing
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Success/Error Messages */}
      {submitError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{submitError}</AlertDescription>
        </Alert>
      )}

      {submitSuccess && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>{submitSuccess}</AlertDescription>
        </Alert>
      )}

      {/* Main Content */}
      <Tabs defaultValue="all" className="space-y-4">
        <TabsList>
          <TabsTrigger value="all">All Pricing</TabsTrigger>
          {tenants.map((tenant) => {
            const tenantStats = statsByTenant[tenant.id];
            return (
              <TabsTrigger key={tenant.id} value={`tenant-${tenant.id}`}>
                {tenant.name}
                <Badge variant="secondary" className="ml-2">
                  {tenantStats?.total || 0}
                </Badge>
              </TabsTrigger>
            );
          })}
        </TabsList>

        <TabsContent value="all">
          <PackagePricingList
            onEdit={handleEdit}
            onAdd={handleAdd}
            showActions={true}
          />
        </TabsContent>

        {tenants.map((tenant) => (
          <TabsContent key={tenant.id} value={`tenant-${tenant.id}`}>
            <PackagePricingList
              tenantId={tenant.id}
              onEdit={handleEdit}
              onAdd={() => {
                setSelectedTenantId(tenant.id);
                handleAdd();
              }}
              showActions={true}
            />
          </TabsContent>
        ))}
      </Tabs>

      {/* Create/Edit Dialog */}
      <Dialog open={isFormOpen || !!editingPricing} onOpenChange={handleCloseForm}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editingPricing ? "Edit Package Pricing" : "Add New Package Pricing"}
            </DialogTitle>
          </DialogHeader>
          <PackagePricingForm
            entity={editingPricing || undefined}
            tenantId={selectedTenantId || (editingPricing?.package?.tenantId) || (tenants[0]?.id ?? 1)}
            onSubmit={editingPricing ? handleUpdatePricing : handleCreatePricing}
            onCancel={handleCloseForm}
            className="border-0 shadow-none"
          />
        </DialogContent>
      </Dialog>

      {/* Success Modal */}
      <SuccessModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        title="Package Pricing Created!"
        description="Your new package pricing has been successfully created and is ready to use."
      />

      {/* Success Toast */}
      <SuccessToast
        isOpen={!!submitSuccess}
        onClose={() => setSubmitSuccess(null)}
        message={submitSuccess || ""}
      />

      {/* Confetti */}
      {showConfetti && <Confetti />}
    </div>
  );
}
