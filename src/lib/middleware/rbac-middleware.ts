import { NextRequest, NextResponse } from "next/server";
import { getToken } from "next-auth/jwt";
import { PermissionService } from "@/lib/services/permission.service";
import { RoleService } from "@/lib/services/role.service";
import { LocationAccessService } from "@/lib/services/location-access.service";

/**
 * RBAC Middleware
 * 
 * Middleware untuk handle role-based access control di API routes.
 * Mengi<PERSON><PERSON> pattern yang sama dengan middleware lain di project ini.
 * 
 * Ini kayak "security guard" yang cek apakah user punya permission untuk akses endpoint tertentu.
 */

// Interface untuk RBAC context
export interface RBACContext {
  userId: string;
  tenantId?: number | null;
  userRoles: string[];
  userPermissions: string[];
  accessibleLocations: string[];
}

// Interface untuk permission check
export interface PermissionCheck {
  module: string;
  action: string;
  resource?: string;
  locationId?: string;
  tenantId?: number | null;
}

/**
 * Main RBAC middleware function
 * 
 * Ini function utama yang dipanggil di API routes untuk cek permissions.
 * Usage: await checkPermission(request, { module: "classes", action: "create" })
 */
export async function checkPermission(
  request: NextRequest,
  permissionCheck: PermissionCheck
): Promise<{ allowed: boolean; context?: RBACContext; error?: string }> {
  try {
    // Get user token dari NextAuth
    const token = await getToken({ 
      req: request,
      secret: process.env.NEXTAUTH_SECRET 
    });

    if (!token || !token.sub) {
      return {
        allowed: false,
        error: "Authentication required"
      };
    }

    const userId = token.sub;
    const tenantId = permissionCheck.tenantId || (token as any).tenantId || null;

    // Build RBAC context
    const context = await buildRBACContext(userId, tenantId);

    // Special <NAME_EMAIL>
    const userEmail = (token as any).email;
    console.log("RBAC Permission Check - User details:", {
      userId,
      email: userEmail,
      requiredPermission: `${permissionCheck.module}.${permissionCheck.action}`,
      isAdmin: userEmail === "<EMAIL>"
    });

    if (userEmail === "<EMAIL>") {
      console.log("RBAC Permission Check - Admin bypass granted");

      return {
        allowed: true,
        context,
      };
    }

    // Check if user has required permission
    const hasPermission = await PermissionService.userHasPermission(
      userId,
      permissionCheck.module,
      permissionCheck.action,
      tenantId
    );

    console.log("RBAC Permission Check:", {
      userId,
      tenantId,
      requiredPermission: `${permissionCheck.module}.${permissionCheck.action}`,
      hasPermission,
      userPermissions: context.userPermissions
    });

    if (!hasPermission) {
      return {
        allowed: false,
        context,
        error: `Permission denied: ${permissionCheck.module}.${permissionCheck.action}`
      };
    }

    // Check location access if required
    if (permissionCheck.locationId) {
      const hasLocationAccess = await LocationAccessService.userHasLocationAccess(
        userId,
        permissionCheck.locationId,
        "read_only" // minimum required level
      );

      if (!hasLocationAccess) {
        return {
          allowed: false,
          context,
          error: `Location access denied: ${permissionCheck.locationId}`
        };
      }
    }

    return {
      allowed: true,
      context
    };
  } catch (error) {
    console.error("RBAC middleware error:", error);
    return {
      allowed: false,
      error: "Internal server error"
    };
  }
}

/**
 * Build RBAC context untuk user
 * 
 * Ini function yang compile semua informasi RBAC untuk user:
 * - Roles yang dimiliki
 * - Permissions yang dimiliki
 * - Locations yang bisa diakses
 */
async function buildRBACContext(userId: string, tenantId?: number | null): Promise<RBACContext> {
  try {
    // Get user roles
    const userRolesResult = await RoleService.getUserRoles(tenantId, userId);
    const userRoles = userRolesResult.userRoles.map(ur => ur.roleId);

    // Get user permissions
    const userPermissions = await PermissionService.getUserPermissions(userId, tenantId);
    const permissionStrings = userPermissions.map(p => `${p.module}.${p.action}`);

    // Get accessible locations (jika ada tenantId)
    let accessibleLocations: string[] = [];
    if (tenantId) {
      const locationAccess = await LocationAccessService.getUserAccessibleLocations(userId, tenantId);
      accessibleLocations = locationAccess.map(la => la.locationId);
    }

    return {
      userId,
      tenantId,
      userRoles,
      userPermissions: permissionStrings,
      accessibleLocations,
    };
  } catch (error) {
    console.error("Error building RBAC context:", error);
    return {
      userId,
      tenantId,
      userRoles: [],
      userPermissions: [],
      accessibleLocations: [],
    };
  }
}

/**
 * Middleware wrapper untuk API routes
 *
 * Ini higher-order function yang wrap API route handler dengan RBAC check.
 * Usage:
 *
 * export const GET = withRBAC(
 *   async (request, context) => {
 *     // Your API logic here
 *     return NextResponse.json({ data: "success" });
 *   },
 *   { module: "classes", action: "read" }
 * );
 */
export function withRBAC(
  handler: (request: NextRequest, context: RBACContext) => Promise<NextResponse>,
  permissionCheck: Omit<PermissionCheck, 'tenantId'> & {
    extractTenantId?: (request: NextRequest) => number | null;
    extractLocationId?: (request: NextRequest) => string | undefined;
  }
) {
  return async (request: NextRequest) => {
    try {
      // Extract tenantId dan locationId dari request jika ada extractor
      const tenantId = permissionCheck.extractTenantId ?
        permissionCheck.extractTenantId(request) :
        extractTenantIdFromRequest(request);

      const locationId = permissionCheck.extractLocationId ?
        permissionCheck.extractLocationId(request) :
        extractLocationIdFromRequest(request);

      // Build full permission check
      const fullPermissionCheck: PermissionCheck = {
        ...permissionCheck,
        tenantId,
        locationId,
      };

      // Check permission
      const result = await checkPermission(request, fullPermissionCheck);

      if (!result.allowed) {
        return NextResponse.json(
          {
            success: false,
            error: result.error || "Access denied",
            code: "RBAC_ACCESS_DENIED"
          },
          { status: 403 }
        );
      }

      // Call original handler dengan RBAC context
      return await handler(request, result.context!);
    } catch (error) {
      console.error("RBAC wrapper error:", error);
      return NextResponse.json(
        {
          success: false,
          error: "Internal server error",
          code: "RBAC_INTERNAL_ERROR"
        },
        { status: 500 }
      );
    }
  };
}

/**
 * Middleware wrapper untuk API routes dengan dynamic parameters
 *
 * Ini version khusus untuk dynamic routes yang butuh params (seperti [id])
 * Usage:
 *
 * export const PUT = withRBACParams(
 *   async (request, context, { params }) => {
 *     // Your API logic here with params.id
 *     return NextResponse.json({ data: "success" });
 *   },
 *   { module: "roles", action: "update" }
 * );
 */
export function withRBACParams<T = any>(
  handler: (request: NextRequest, context: RBACContext, routeParams: T) => Promise<NextResponse>,
  permissionCheck: Omit<PermissionCheck, 'tenantId'> & {
    extractTenantId?: (request: NextRequest) => number | null;
    extractLocationId?: (request: NextRequest) => string | undefined;
  }
) {
  return async (request: NextRequest, routeParams: T) => {
    try {
      // Extract tenantId dan locationId dari request jika ada extractor
      const tenantId = permissionCheck.extractTenantId ?
        permissionCheck.extractTenantId(request) :
        extractTenantIdFromRequest(request);

      const locationId = permissionCheck.extractLocationId ?
        permissionCheck.extractLocationId(request) :
        extractLocationIdFromRequest(request);

      // Build full permission check
      const fullPermissionCheck: PermissionCheck = {
        ...permissionCheck,
        tenantId,
        locationId,
      };

      // Check permission
      const result = await checkPermission(request, fullPermissionCheck);

      if (!result.allowed) {
        return NextResponse.json(
          {
            success: false,
            error: result.error || "Access denied",
            code: "RBAC_ACCESS_DENIED"
          },
          { status: 403 }
        );
      }

      // Call original handler dengan RBAC context dan route params
      return await handler(request, result.context!, routeParams);
    } catch (error) {
      console.error("RBAC wrapper error:", error);
      return NextResponse.json(
        {
          success: false,
          error: "Internal server error",
          code: "RBAC_INTERNAL_ERROR"
        },
        { status: 500 }
      );
    }
  };
}

/**
 * Helper functions untuk extract data dari request
 */
function extractTenantIdFromRequest(request: NextRequest): number | null {
  // Try dari query params
  const { searchParams } = new URL(request.url);
  const tenantIdParam = searchParams.get("tenantId");
  
  if (tenantIdParam && tenantIdParam !== "null") {
    const parsed = parseInt(tenantIdParam, 10);
    if (!isNaN(parsed)) {
      return parsed;
    }
  }

  // Try dari headers
  const tenantIdHeader = request.headers.get("x-tenant-id");
  if (tenantIdHeader && tenantIdHeader !== "null") {
    const parsed = parseInt(tenantIdHeader, 10);
    if (!isNaN(parsed)) {
      return parsed;
    }
  }

  return null;
}

function extractLocationIdFromRequest(request: NextRequest): string | undefined {
  // Try dari query params
  const { searchParams } = new URL(request.url);
  const locationId = searchParams.get("locationId");
  
  if (locationId) {
    return locationId;
  }

  // Try dari headers
  const locationIdHeader = request.headers.get("x-location-id");
  if (locationIdHeader) {
    return locationIdHeader;
  }

  return undefined;
}

/**
 * Utility functions untuk common permission checks
 */
export const RBACUtils = {
  // Check if user is super admin
  isSuperAdmin: (context: RBACContext): boolean => {
    return context.userPermissions.includes("system.manage") ||
           context.userRoles.some(role => role.includes("super_admin"));
  },

  // Check if user is tenant admin
  isTenantAdmin: (context: RBACContext): boolean => {
    return context.userPermissions.includes("tenant.manage") ||
           context.userRoles.some(role => role.includes("tenant_admin"));
  },

  // Check if user can access specific tenant
  canAccessTenant: (context: RBACContext, tenantId: number): boolean => {
    return RBACUtils.isSuperAdmin(context) || 
           context.tenantId === tenantId;
  },

  // Check if user can access specific location
  canAccessLocation: (context: RBACContext, locationId: string): boolean => {
    return RBACUtils.isSuperAdmin(context) ||
           context.accessibleLocations.includes(locationId);
  },

  // Check if user has specific permission
  hasPermission: (context: RBACContext, module: string, action: string): boolean => {
    return context.userPermissions.includes(`${module}.${action}`);
  },
};

/**
 * Route protection middleware untuk Next.js middleware
 *
 * Ini middleware yang jalan di edge runtime untuk protect routes berdasarkan roles.
 * Dipanggil di middleware.ts di root project.
 */
export async function rbacRouteProtection(request: NextRequest): Promise<NextResponse | null> {
  try {
    const { pathname } = request.nextUrl;

    // Skip untuk public routes
    const publicRoutes = [
      "/api/auth",
      "/auth",
      "/login",
      "/register",
      "/",
      "/about",
      "/contact"
    ];

    if (publicRoutes.some(route => pathname.startsWith(route))) {
      return null; // Continue ke next middleware
    }

    // Get user token
    const token = await getToken({
      req: request,
      secret: process.env.NEXTAUTH_SECRET
    });

    if (!token || !token.sub) {
      // Redirect ke login
      const loginUrl = new URL("/auth/signin", request.url);
      loginUrl.searchParams.set("callbackUrl", request.url);
      return NextResponse.redirect(loginUrl);
    }

    // Define route permissions
    const routePermissions: Record<string, PermissionCheck> = {
      "/dashboard/admin": { module: "admin", action: "access" },
      "/dashboard/classes": { module: "classes", action: "read" },
      "/dashboard/facilities": { module: "facilities", action: "read" },
      "/dashboard/customers": { module: "customers", action: "read" },
      "/dashboard/packages": { module: "packages", action: "read" },
      "/dashboard/locations": { module: "locations", action: "read" },
      "/dashboard/equipment": { module: "equipment", action: "read" },
      "/dashboard/roles": { module: "roles", action: "read" },
      "/dashboard/users": { module: "users", action: "read" },
    };

    // Find matching route permission
    const matchingRoute = Object.keys(routePermissions).find(route =>
      pathname.startsWith(route)
    );

    if (matchingRoute) {
      const permissionCheck = routePermissions[matchingRoute];
      const result = await checkPermission(request, permissionCheck);

      if (!result.allowed) {
        // Redirect ke access denied page
        const accessDeniedUrl = new URL("/access-denied", request.url);
        accessDeniedUrl.searchParams.set("reason", result.error || "Access denied");
        return NextResponse.redirect(accessDeniedUrl);
      }
    }

    return null; // Continue ke next middleware
  } catch (error) {
    console.error("Route protection error:", error);
    return null; // Continue ke next middleware pada error
  }
}
