"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Checkbox } from "@/components/ui/checkbox";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  FileText,
  Edit,
  Trash2,
  Plus,
  Search,
  Building,
  AlertCircle,
  GripVertical,
  Eye,
  EyeOff,
  MoreHorizontal,
  Shield,
  ShieldOff,
  Copy,
  Clock
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { WaiverForm } from "@/lib/db/schema";
import { 
  useWaiverForms, 
  useDeleteWaiverForm, 
  useToggleWaiverFormActive,
  useToggleWaiverFormRequired,
  useReorderWaiverForms,
  useBulkWaiverFormOperation,
  useDuplicateWaiverForm
} from "@/lib/hooks/queries/use-waiver-form-queries";
import { DeleteConfirmationDialog } from "@/components/ui/delete-confirmation-dialog";
import { DeleteSuccessToast } from "@/components/ui/delete-success-toast";

interface WaiverFormListProps {
  onEdit?: (waiverForm: WaiverForm) => void;
  onAdd?: () => void;
  showActions?: boolean;
  tenantId?: number;
}

export function WaiverFormList({
  onEdit,
  onAdd,
  showActions = true,
  tenantId,
}: WaiverFormListProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [deleteError, setDeleteError] = useState<string | null>(null);
  const [deleteConfirmation, setDeleteConfirmation] = useState<{
    isOpen: boolean;
    waiverForm: WaiverForm | null;
  }>({ isOpen: false, waiverForm: null });
  const [showDeleteSuccess, setShowDeleteSuccess] = useState(false);
  const [deletedWaiverFormName, setDeletedWaiverFormName] = useState("");

  const { data: waiverForms = [], isLoading, error } = useWaiverForms({
    tenantId,
    filters: searchQuery ? { search: searchQuery } : undefined,
  });
  const deleteWaiverFormMutation = useDeleteWaiverForm();
  const toggleActiveMutation = useToggleWaiverFormActive();
  const toggleRequiredMutation = useToggleWaiverFormRequired();
  const reorderMutation = useReorderWaiverForms();
  const bulkOperationMutation = useBulkWaiverFormOperation();
  const duplicateMutation = useDuplicateWaiverForm();

  const handleDeleteClick = (waiverForm: WaiverForm) => {
    setDeleteConfirmation({ isOpen: true, waiverForm });
  };

  const handleDeleteConfirm = async () => {
    if (!deleteConfirmation.waiverForm) return;

    const waiverFormName = deleteConfirmation.waiverForm.name || "Unknown waiver form";

    try {
      setDeleteError(null);
      await deleteWaiverFormMutation.mutateAsync(deleteConfirmation.waiverForm.id);

      setDeleteConfirmation({ isOpen: false, waiverForm: null });
      setDeletedWaiverFormName(waiverFormName);
      setShowDeleteSuccess(true);

    } catch (error) {
      setDeleteError(error instanceof Error ? error.message : "Failed to delete waiver form");
    }
  };

  const handleDeleteCancel = () => {
    setDeleteConfirmation({ isOpen: false, waiverForm: null });
  };

  const handleToggleActive = async (waiverForm: WaiverForm) => {
    try {
      await toggleActiveMutation.mutateAsync(waiverForm.id);
    } catch (error) {
      setDeleteError(error instanceof Error ? error.message : "Failed to toggle waiver form status");
    }
  };

  const handleToggleRequired = async (waiverForm: WaiverForm) => {
    try {
      await toggleRequiredMutation.mutateAsync(waiverForm.id);
    } catch (error) {
      setDeleteError(error instanceof Error ? error.message : "Failed to toggle waiver form required status");
    }
  };

  const handleDuplicate = async (waiverForm: WaiverForm) => {
    try {
      await duplicateMutation.mutateAsync({ 
        id: waiverForm.id, 
        name: `${waiverForm.name} (Copy)` 
      });
    } catch (error) {
      setDeleteError(error instanceof Error ? error.message : "Failed to duplicate waiver form");
    }
  };

  const handleSelectItem = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedItems([...selectedItems, id]);
    } else {
      setSelectedItems(selectedItems.filter(item => item !== id));
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedItems(waiverForms.map(wf => wf.id));
    } else {
      setSelectedItems([]);
    }
  };

  const handleBulkOperation = async (action: 'activate' | 'deactivate' | 'require' | 'unrequire' | 'delete') => {
    if (selectedItems.length === 0) return;

    try {
      await bulkOperationMutation.mutateAsync({ ids: selectedItems, action });
      setSelectedItems([]);
    } catch (error) {
      setDeleteError(error instanceof Error ? error.message : `Failed to ${action} waiver forms`);
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="py-8">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Failed to load waiver forms. Please try again.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Waiver Forms
            </CardTitle>
            <CardDescription>
              Manage waiver forms and their settings
            </CardDescription>
          </div>
          {showActions && onAdd && (
            <Button onClick={onAdd}>
              <Plus className="h-4 w-4 mr-2" />
              Add Waiver Form
            </Button>
          )}
        </div>

        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search waiver forms..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Bulk Actions */}
        {selectedItems.length > 0 && (
          <div className="flex items-center gap-2 p-3 bg-blue-50 rounded-lg">
            <span className="text-sm text-blue-700">
              {selectedItems.length} item(s) selected
            </span>
            <div className="flex gap-2 ml-auto">
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleBulkOperation('activate')}
                disabled={bulkOperationMutation.isPending}
              >
                <Eye className="h-4 w-4 mr-1" />
                Activate
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleBulkOperation('deactivate')}
                disabled={bulkOperationMutation.isPending}
              >
                <EyeOff className="h-4 w-4 mr-1" />
                Deactivate
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleBulkOperation('require')}
                disabled={bulkOperationMutation.isPending}
              >
                <Shield className="h-4 w-4 mr-1" />
                Require
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleBulkOperation('unrequire')}
                disabled={bulkOperationMutation.isPending}
              >
                <ShieldOff className="h-4 w-4 mr-1" />
                Optional
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleBulkOperation('delete')}
                disabled={bulkOperationMutation.isPending}
                className="text-red-600 hover:text-red-700"
              >
                <Trash2 className="h-4 w-4 mr-1" />
                Delete
              </Button>
            </div>
          </div>
        )}

        {/* Delete Error */}
        {deleteError && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{deleteError}</AlertDescription>
          </Alert>
        )}
      </CardHeader>

      <CardContent>
        {/* Waiver Form List */}
        {waiverForms.length === 0 ? (
          <div className="text-center py-8">
            <FileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              {searchQuery ? "No waiver forms found" : "No waiver forms yet"}
            </h3>
            <p className="text-gray-500 dark:text-gray-400 mb-4">
              {searchQuery 
                ? "Try adjusting your search terms."
                : "Get started by adding your first waiver form."
              }
            </p>
            {showActions && onAdd && !searchQuery && (
              <Button onClick={onAdd}>
                <Plus className="h-4 w-4 mr-2" />
                Add Waiver Form
              </Button>
            )}
          </div>
        ) : (
          <div className="space-y-2">
            {/* Select All */}
            <div className="flex items-center gap-2 p-2 border-b">
              <Checkbox
                checked={selectedItems.length === waiverForms.length}
                onCheckedChange={handleSelectAll}
              />
              <span className="text-sm text-gray-600">Select All</span>
            </div>

            {waiverForms.map((waiverForm) => (
              <div
                key={waiverForm.id}
                className="border rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
              >
                <div className="flex items-center gap-3">
                  {/* Checkbox */}
                  <Checkbox
                    checked={selectedItems.includes(waiverForm.id)}
                    onCheckedChange={(checked) => handleSelectItem(waiverForm.id, checked as boolean)}
                  />

                  {/* Drag Handle */}
                  <GripVertical className="h-4 w-4 text-gray-400 cursor-grab" />

                  {/* Content */}
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <FileText className="h-4 w-4 text-gray-500" />
                      <h4 className="font-medium text-gray-900 dark:text-gray-100">
                        {waiverForm.name}
                      </h4>
                      <Badge variant={waiverForm.isActive ? "default" : "secondary"}>
                        {waiverForm.isActive ? "Active" : "Inactive"}
                      </Badge>
                      <Badge variant={waiverForm.isRequired ? "destructive" : "outline"}>
                        {waiverForm.isRequired ? "Required" : "Optional"}
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        v{waiverForm.version}
                      </Badge>
                      <Badge variant="outline" className="text-xs">
                        Order: {waiverForm.sortOrder}
                      </Badge>
                    </div>
                    
                    {waiverForm.description && (
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                        {waiverForm.description}
                      </p>
                    )}

                    <div className="flex items-center gap-2">
                      <Badge variant="secondary" className="text-xs">
                        Tenant ID: {waiverForm.tenantId}
                      </Badge>
                      {waiverForm.expiryDays && (
                        <Badge variant="outline" className="text-xs flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          Expires in {waiverForm.expiryDays} days
                        </Badge>
                      )}
                    </div>
                  </div>

                  {/* Active Toggle */}
                  <div className="flex items-center gap-2">
                    <Switch
                      checked={waiverForm.isActive}
                      onCheckedChange={() => handleToggleActive(waiverForm)}
                      disabled={toggleActiveMutation.isPending}
                    />
                  </div>

                  {/* Required Toggle */}
                  <div className="flex items-center gap-2">
                    <Switch
                      checked={waiverForm.isRequired}
                      onCheckedChange={() => handleToggleRequired(waiverForm)}
                      disabled={toggleRequiredMutation.isPending}
                    />
                    <span className="text-xs text-gray-500">
                      {waiverForm.isRequired ? "Required" : "Optional"}
                    </span>
                  </div>

                  {/* Actions */}
                  {showActions && (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        {onEdit && (
                          <DropdownMenuItem onClick={() => onEdit(waiverForm)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuItem onClick={() => handleDuplicate(waiverForm)}>
                          <Copy className="h-4 w-4 mr-2" />
                          Duplicate
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleToggleActive(waiverForm)}>
                          {waiverForm.isActive ? (
                            <>
                              <EyeOff className="h-4 w-4 mr-2" />
                              Deactivate
                            </>
                          ) : (
                            <>
                              <Eye className="h-4 w-4 mr-2" />
                              Activate
                            </>
                          )}
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleToggleRequired(waiverForm)}>
                          {waiverForm.isRequired ? (
                            <>
                              <ShieldOff className="h-4 w-4 mr-2" />
                              Make Optional
                            </>
                          ) : (
                            <>
                              <Shield className="h-4 w-4 mr-2" />
                              Make Required
                            </>
                          )}
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem 
                          onClick={() => handleDeleteClick(waiverForm)}
                          className="text-red-600"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Summary */}
        {waiverForms.length > 0 && (
          <div className="mt-4 pt-4 border-t text-sm text-gray-500 dark:text-gray-400">
            Showing {waiverForms.length} waiver form(s)
          </div>
        )}
      </CardContent>

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationDialog
        isOpen={deleteConfirmation.isOpen}
        onClose={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        title="Delete Waiver Form"
        description="Are you sure you want to delete this waiver form? This action cannot be undone."
        itemName={deleteConfirmation.waiverForm?.name || ""}
        isLoading={deleteWaiverFormMutation.isPending}
      />

      {/* Delete Success Toast */}
      <DeleteSuccessToast
        isVisible={showDeleteSuccess}
        onClose={() => setShowDeleteSuccess(false)}
        itemName={deletedWaiverFormName}
      />
    </Card>
  );
}
