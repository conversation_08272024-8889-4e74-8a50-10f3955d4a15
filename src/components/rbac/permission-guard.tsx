"use client";

import { ReactNode } from "react";
import { useRBAC } from "@/lib/hooks/use-rbac";
import { ShieldX } from "lucide-react";

/**
 * Permission Guard Component
 * 
 * Component ini handle conditional rendering berdasarkan permissions.
 * Kalau user gak punya permission, bisa show fallback atau hide completely.
 */

interface PermissionGuardProps {
  /** Permission yang perlu dicheck (format: "module.action") */
  permission: string;
  /** Children yang akan dirender kalau punya permission */
  children: ReactNode;
  /** Fallback component kalau gak punya permission */
  fallback?: ReactNode;
  /** Show error message kalau gak punya permission (default: false) */
  showError?: boolean;
  /** Custom error message */
  errorMessage?: string;
  /** Hide completely kalau gak punya permission (default: false) */
  hideIfNoAccess?: boolean;
}

export function PermissionGuard({
  permission,
  children,
  fallback,
  showError = false,
  errorMessage,
  hideIfNoAccess = false,
}: PermissionGuardProps) {
  const { hasPermission, isLoading } = useRBAC();

  // Show loading state
  if (isLoading) {
    return <div className="animate-pulse bg-gray-200 h-8 rounded" />;
  }

  const hasAccess = hasPermission(permission);

  // Kalau punya permission, render children
  if (hasAccess) {
    return <>{children}</>;
  }

  // Kalau gak punya permission dan hideIfNoAccess = true, hide completely
  if (hideIfNoAccess) {
    return null;
  }

  // Kalau ada fallback, render fallback
  if (fallback) {
    return <>{fallback}</>;
  }

  // Kalau showError = true, show error message
  if (showError) {
    return (
      <div className="bg-red-50 border border-red-200 rounded p-3">
        <div className="flex items-center gap-2 text-red-700">
          <ShieldX className="h-4 w-4" />
          <span className="text-sm">
            {errorMessage || `Anda tidak memiliki permission: ${permission}`}
          </span>
        </div>
      </div>
    );
  }

  // Default: hide completely
  return null;
}

/**
 * Role Guard Component
 * 
 * Component ini handle conditional rendering berdasarkan roles.
 */

interface RoleGuardProps {
  /** Role yang perlu dicheck */
  role: string;
  /** Children yang akan dirender kalau punya role */
  children: ReactNode;
  /** Fallback component kalau gak punya role */
  fallback?: ReactNode;
  /** Show error message kalau gak punya role */
  showError?: boolean;
  /** Custom error message */
  errorMessage?: string;
  /** Hide completely kalau gak punya role */
  hideIfNoAccess?: boolean;
}

export function RoleGuard({
  role,
  children,
  fallback,
  showError = false,
  errorMessage,
  hideIfNoAccess = false,
}: RoleGuardProps) {
  const { hasRole, isLoading } = useRBAC();

  if (isLoading) {
    return <div className="animate-pulse bg-gray-200 h-8 rounded" />;
  }

  const hasAccess = hasRole(role);

  if (hasAccess) {
    return <>{children}</>;
  }

  if (hideIfNoAccess) {
    return null;
  }

  if (fallback) {
    return <>{fallback}</>;
  }

  if (showError) {
    return (
      <div className="bg-red-50 border border-red-200 rounded p-3">
        <div className="flex items-center gap-2 text-red-700">
          <ShieldX className="h-4 w-4" />
          <span className="text-sm">
            {errorMessage || `Anda tidak memiliki role ${role}`}
          </span>
        </div>
      </div>
    );
  }

  return null;
}

/**
 * Location Guard Component
 * 
 * Component ini handle conditional rendering berdasarkan location access.
 */

interface LocationGuardProps {
  /** Location ID yang perlu dicheck */
  locationId: string;
  /** Children yang akan dirender kalau bisa akses location */
  children: ReactNode;
  /** Fallback component kalau gak bisa akses location */
  fallback?: ReactNode;
  /** Show error message kalau gak bisa akses location */
  showError?: boolean;
  /** Custom error message */
  errorMessage?: string;
  /** Hide completely kalau gak bisa akses location */
  hideIfNoAccess?: boolean;
}

export function LocationGuard({
  locationId,
  children,
  fallback,
  showError = false,
  errorMessage,
  hideIfNoAccess = false,
}: LocationGuardProps) {
  const { canAccessLocation, isSuperAdmin, isLoading } = useRBAC();

  if (isLoading) {
    return <div className="animate-pulse bg-gray-200 h-8 rounded" />;
  }

  // Super admin bisa akses semua location
  const hasAccess = isSuperAdmin() || canAccessLocation(locationId);

  if (hasAccess) {
    return <>{children}</>;
  }

  if (hideIfNoAccess) {
    return null;
  }

  if (fallback) {
    return <>{fallback}</>;
  }

  if (showError) {
    return (
      <div className="bg-red-50 border border-red-200 rounded p-3">
        <div className="flex items-center gap-2 text-red-700">
          <ShieldX className="h-4 w-4" />
          <span className="text-sm">
            {errorMessage || "Anda tidak memiliki akses ke location ini"}
          </span>
        </div>
      </div>
    );
  }

  return null;
}
