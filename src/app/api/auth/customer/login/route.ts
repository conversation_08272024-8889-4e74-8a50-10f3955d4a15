import { NextRequest } from "next/server";
import { z } from "zod";
import { customerAuthService } from "@/lib/services/customer-auth.service";
import { 
  createAuthenticatedResponse, 
  createErrorResponse,
  rateLimit,
  addSecurityHeaders 
} from "@/lib/middleware/customer-auth.middleware";
import { rateLimitConfig } from "@/lib/security/rate-limit.config";

/**
 * Customer Login with Email/Password
 * 
 * POST /api/auth/customer/login
 * 
 * Authenticates customer and returns JWT tokens
 */

const loginSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(1, "Password is required"),
  tenantId: z.number().int().positive(),
  deviceType: z.enum(["web", "mobile", "tablet"]).default("web"),
  deviceId: z.string().optional(),
  rememberMe: z.boolean().default(false),
});

export async function POST(request: NextRequest) {
  try {
    // Apply rate limiting
    const clientIP = request.headers.get("x-forwarded-for")?.split(",")[0] || 
                    request.headers.get("x-real-ip") || 
                    request.ip || "unknown";

    const rateLimitResult = rateLimit({
      windowMs: rateLimitConfig.auth.login.windowMs,
      max: rateLimitConfig.auth.login.max,
      keyGenerator: () => `login:${clientIP}`,
    })(request);

    if (!rateLimitResult.allowed) {
      return createErrorResponse(
        "Too many login attempts",
        "RATE_LIMIT_EXCEEDED",
        429,
        rateLimitResult.headers
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = loginSchema.parse(body);

    // Get user agent
    const userAgent = request.headers.get("user-agent") || undefined;

    // Authenticate customer with credentials and generate JWT tokens
    const authResult = await customerAuthService.authenticateWithCredentialsJWT(
      validatedData.email,
      validatedData.password,
      validatedData.tenantId,
      {
        ipAddress: clientIP,
        userAgent,
        deviceType: validatedData.deviceType,
        deviceId: validatedData.deviceId,
      }
    );

    if (!authResult.success) {
      // Return appropriate error based on the failure reason
      let statusCode = 401;
      let errorCode = "INVALID_CREDENTIALS";

      if (authResult.error?.includes("locked")) {
        statusCode = 423; // Locked
        errorCode = "ACCOUNT_LOCKED";
      } else if (authResult.error?.includes("verification")) {
        statusCode = 403; // Forbidden
        errorCode = "EMAIL_NOT_VERIFIED";
      }

      return createErrorResponse(
        authResult.error || "Authentication failed",
        errorCode,
        statusCode,
        rateLimitResult.headers
      );
    }

    if (!authResult.tokens) {
      return createErrorResponse(
        "Failed to generate authentication tokens",
        "TOKEN_GENERATION_ERROR",
        500,
        rateLimitResult.headers
      );
    }

    // Prepare response data
    const responseData = {
      success: true,
      customer: authResult.customer,
      tokens: {
        accessToken: authResult.tokens.accessToken,
        refreshToken: authResult.tokens.refreshToken,
        tokenType: authResult.tokens.tokenType,
        expiresIn: authResult.tokens.expiresIn,
      },
      requiresEmailVerification: authResult.requiresEmailVerification || false,
    };

    return createAuthenticatedResponse(responseData, 200, rateLimitResult.headers);

  } catch (error) {
    console.error("Login error:", error);

    if (error instanceof z.ZodError) {
      return createErrorResponse(
        "Invalid request data",
        "INVALID_INPUT",
        400
      );
    }

    return createErrorResponse(
      "Login failed",
      "LOGIN_ERROR",
      500
    );
  }
}

// Handle preflight OPTIONS request
export async function OPTIONS(request: NextRequest) {
  const response = new Response(null, { status: 200 });
  return addSecurityHeaders(response);
}
