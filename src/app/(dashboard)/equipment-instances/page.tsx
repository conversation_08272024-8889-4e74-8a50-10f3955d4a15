"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { 
  Package, 
  Plus, 
  MapPin, 
  BarChart3,
  AlertCircle,
  CheckCircle,
  Hash,
  Layers
} from "lucide-react";
import { EquipmentInstanceForm } from "@/components/forms/equipment-instance-form";
import { EquipmentInstanceList } from "@/components/equipment-instances/equipment-instance-list";
import { SuccessToast } from "@/components/ui/success-toast";
import { SuccessModal } from "@/components/ui/success-modal";
import { Confetti } from "@/components/ui/confetti";
import { EquipmentInstanceWithRelations, CreateEquipmentInstanceData, UpdateEquipmentInstanceData } from "@/lib/services/equipment-instances.service";
import { useEquipmentInstances, useCreateEquipmentInstance, useUpdateEquipmentInstance } from "@/lib/hooks/queries/use-equipment-instance-queries";
import { useEquipments } from "@/lib/hooks/queries/use-equipment-queries";
import { useLocations } from "@/lib/hooks/queries/use-location-queries";

export default function EquipmentInstancesPage() {
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingInstance, setEditingInstance] = useState<EquipmentInstanceWithRelations | null>(null);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [submitSuccess, setSubmitSuccess] = useState<string | null>(null);
  
  // Animation states
  const [showSuccessToast, setShowSuccessToast] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showConfetti, setShowConfetti] = useState(false);
  const [createdInstance, setCreatedInstance] = useState<EquipmentInstanceWithRelations | null>(null);

  const { data: instances = [], isLoading: instancesLoading } = useEquipmentInstances();
  const { data: equipments = [], isLoading: equipmentsLoading } = useEquipments();
  const { data: locations = [], isLoading: locationsLoading } = useLocations();
  const createInstanceMutation = useCreateEquipmentInstance();
  const updateInstanceMutation = useUpdateEquipmentInstance();

  // Calculate stats
  const stats = {
    totalInstances: instances.length,
    totalQuantity: instances.reduce((sum, instance) => sum + instance.quantity, 0),
    uniqueEquipment: new Set(instances.map(i => i.equipmentId)).size,
    uniqueLocations: new Set(instances.map(i => i.locationId)).size,
    byEquipment: equipments.reduce((acc, equipment) => {
      const count = instances.filter(i => i.equipmentId === equipment.id).length;
      const quantity = instances
        .filter(i => i.equipmentId === equipment.id)
        .reduce((sum, i) => sum + i.quantity, 0);
      acc[equipment.id] = { count, quantity, name: equipment.name };
      return acc;
    }, {} as Record<string, { count: number; quantity: number; name: string }>),
    byLocation: locations.reduce((acc, location) => {
      const count = instances.filter(i => i.locationId === location.id).length;
      const quantity = instances
        .filter(i => i.locationId === location.id)
        .reduce((sum, i) => sum + i.quantity, 0);
      acc[location.id] = { count, quantity, address: location.addressLine1 };
      return acc;
    }, {} as Record<string, { count: number; quantity: number; address: string | null }>),
  };

  const handleCreateInstance = async (data: CreateEquipmentInstanceData) => {
    try {
      setSubmitError(null);
      const newInstance = await createInstanceMutation.mutateAsync(data);
      
      // Store created instance for modal
      setCreatedInstance(newInstance);
      
      // Close form first
      setIsFormOpen(false);
      
      // Start celebration sequence
      setTimeout(() => {
        setShowConfetti(true);
        setShowSuccessToast(true);
        setShowSuccessModal(true);
      }, 300);
      
      // Auto-hide confetti
      setTimeout(() => setShowConfetti(false), 4000);
      
    } catch (error) {
      setSubmitError(error instanceof Error ? error.message : "Failed to create equipment instance");
    }
  };

  const handleUpdateInstance = async (data: UpdateEquipmentInstanceData) => {
    if (!editingInstance) return;

    try {
      setSubmitError(null);
      await updateInstanceMutation.mutateAsync({ 
        instanceId: editingInstance.id, 
        data: data
      });
      setSubmitSuccess("Equipment instance updated successfully!");
      setEditingInstance(null);
      setTimeout(() => setSubmitSuccess(null), 3000);
    } catch (error) {
      setSubmitError(error instanceof Error ? error.message : "Failed to update equipment instance");
    }
  };

  const handleEdit = (instance: EquipmentInstanceWithRelations) => {
    setEditingInstance(instance);
  };

  const handleAdd = () => {
    setEditingInstance(null);
    setIsFormOpen(true);
  };

  const handleCloseForm = () => {
    setIsFormOpen(false);
    setEditingInstance(null);
    setSubmitError(null);
  };

  const handleCloseSuccessAnimations = () => {
    setShowSuccessToast(false);
    setShowSuccessModal(false);
    setShowConfetti(false);
    setCreatedInstance(null);
  };

  const handleViewInstance = () => {
    handleCloseSuccessAnimations();
    // Could navigate to instance detail or highlight in list
  };

  const handleAddAnother = () => {
    handleCloseSuccessAnimations();
    setIsFormOpen(true);
  };

  if (instancesLoading || equipmentsLoading || locationsLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Equipment Instances</h1>
          <p className="text-gray-500 dark:text-gray-400">
            Manage equipment deployments and track quantities at locations
          </p>
        </div>
        <Button onClick={handleAdd}>
          <Plus className="h-4 w-4 mr-2" />
          Add Instance
        </Button>
      </div>

      {/* Success/Error Messages */}
      {submitSuccess && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>{submitSuccess}</AlertDescription>
        </Alert>
      )}

      {submitError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{submitError}</AlertDescription>
        </Alert>
      )}

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Instances</CardTitle>
            <Layers className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalInstances}</div>
            <p className="text-xs text-muted-foreground">
              Equipment deployments
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Quantity</CardTitle>
            <Hash className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalQuantity}</div>
            <p className="text-xs text-muted-foreground">
              Equipment units
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Equipment Types</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.uniqueEquipment}</div>
            <p className="text-xs text-muted-foreground">
              Different equipment
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Locations</CardTitle>
            <MapPin className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.uniqueLocations}</div>
            <p className="text-xs text-muted-foreground">
              With equipment
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="all" className="space-y-4">
        <TabsList>
          <TabsTrigger value="all">All Instances</TabsTrigger>
          <TabsTrigger value="by-equipment">By Equipment</TabsTrigger>
          <TabsTrigger value="by-location">By Location</TabsTrigger>
        </TabsList>

        <TabsContent value="all">
          <EquipmentInstanceList
            onEdit={handleEdit}
            onAdd={handleAdd}
            showActions={true}
          />
        </TabsContent>

        <TabsContent value="by-equipment">
          <div className="space-y-4">
            {equipments.map((equipment) => {
              const equipmentStats = stats.byEquipment[equipment.id];
              if (!equipmentStats || equipmentStats.count === 0) return null;

              return (
                <Card key={equipment.id}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Package className="h-5 w-5" />
                        <CardTitle className="text-lg">{equipment.name}</CardTitle>
                        <Badge variant="secondary">
                          {equipmentStats.count} instances • {equipmentStats.quantity} units
                        </Badge>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <EquipmentInstanceList
                      equipmentId={equipment.id}
                      onEdit={handleEdit}
                      showActions={true}
                    />
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>

        <TabsContent value="by-location">
          <div className="space-y-4">
            {locations.map((location) => {
              const locationStats = stats.byLocation[location.id];
              if (!locationStats || locationStats.count === 0) return null;

              return (
                <Card key={location.id}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <MapPin className="h-5 w-5" />
                        <CardTitle className="text-lg">{location.addressLine1}</CardTitle>
                        <Badge variant="secondary">
                          {locationStats.count} instances • {locationStats.quantity} units
                        </Badge>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <EquipmentInstanceList
                      locationId={location.id}
                      onEdit={handleEdit}
                      showActions={true}
                    />
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>
      </Tabs>

      {/* Create/Edit Dialog */}
      <Dialog open={isFormOpen || !!editingInstance} onOpenChange={handleCloseForm}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editingInstance ? "Edit Equipment Instance" : "Add New Equipment Instance"}
            </DialogTitle>
          </DialogHeader>
          <EquipmentInstanceForm
            instance={editingInstance || undefined}
            onSubmit={editingInstance ? handleUpdateInstance : handleCreateInstance}
            onCancel={handleCloseForm}
            className="border-0 shadow-none"
          />
        </DialogContent>
      </Dialog>

      {/* Success Animations */}
      <Confetti isActive={showConfetti} />
      
      <SuccessToast
        isVisible={showSuccessToast}
        onClose={() => setShowSuccessToast(false)}
        title="Equipment Instance Created Successfully!"
        description={createdInstance ? 
          `${createdInstance.equipment?.name || 'Equipment'} has been deployed to ${createdInstance.location?.addressLine1 || 'location'}.` : 
          undefined
        }
        type="general"
        action={{
          label: "View All Instances",
          onClick: handleViewInstance
        }}
      />

      <SuccessModal
        isOpen={showSuccessModal}
        onClose={handleCloseSuccessAnimations}
        title="🎉 Equipment Instance Created Successfully!"
        description="Your equipment has been successfully deployed to the specified location."
        locationData={createdInstance ? {
          address: createdInstance.equipment?.name || "",
          city: createdInstance.location?.addressLine1 || "Location",
          country: `Quantity: ${createdInstance.quantity}`
        } : undefined}
        actions={{
          primary: {
            label: "Add Another Instance",
            onClick: handleAddAnother
          },
          secondary: {
            label: "View All Instances",
            onClick: handleViewInstance
          }
        }}
      />
    </div>
  );
}
