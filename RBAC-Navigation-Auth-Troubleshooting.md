# 🔧 Panduan Troubleshooting: RBAC, Navigation & Authentication

> **Dokumentasi lengkap untuk mengatasi masalah umum dalam sistem NextAuth dan RBAC**  
> <PERSON><PERSON><PERSON> dengan gaya penjelasan yang ramah untuk pemula dan profesional

---

## 📋 Daftar Isi

1. [🧭 Masalah Navigation Menu Facilities](#-masalah-navigation-menu-facilities)
2. [🔒 Masalah Access Control Facilities](#-masalah-access-control-facilities)
3. [🔑 Masalah Sign In/Sign Out Authentication](#-masalah-sign-insign-out-authentication)
4. [💡 Tips & Best Practices](#-tips--best-practices)

---

## 🧭 Masalah Navigation Menu Facilities

### 🤔 **Apa yang Terjadi?**

Menu "Facilities" tidak muncul di navigation bar meskipun sudah ada di kode. User admin tidak bisa melihat menu ini sama sekali.

### 🔍 **Root Cause Analysis**

#### **1. RBAC Loading State Issue**
```typescript
// Debug output yang terlihat:
DashboardNav Debug: {
  isLoading: true,        // ❌ Selalu loading
  permissions: [],        // ❌ Array kosong
  roles: [],             // ❌ Array kosong
  userEmail: '<EMAIL>'
}
```

**Mengapa ini terjadi?**
- TanStack Query tidak berhasil propagate data RBAC ke navigation component
- Hook `useRBAC` stuck dalam loading state
- Meskipun RBAC API berhasil return data, UI tidak ter-update

#### **2. Permission Check Logic**
```typescript
// Di navigation component:
const hasPermission = permissions.includes('locations.manage');
// Jika permissions = [], maka hasPermission = false
// Menu tidak akan di-render
```

### ✅ **Solusi Step-by-Step**

#### **Step 1: Debug TanStack Query**
```typescript
// Tambahkan logging di useRBAC hook
export function useRBAC() {
  const query = useQuery({
    queryKey: ['rbac'],
    queryFn: async () => {
      console.log('🔄 Fetching RBAC data...');
      const response = await fetch('/api/auth/rbac');
      const data = await response.json();
      console.log('✅ RBAC data received:', data);
      return data;
    },
    staleTime: 5 * 60 * 1000, // 5 menit
  });
  
  console.log('🎯 useRBAC state:', {
    isLoading: query.isLoading,
    data: query.data,
    error: query.error
  });
  
  return query;
}
```

#### **Step 2: Implementasi Fallback untuk Admin**
```typescript
// Di navigation component, tambahkan bypass untuk admin
const Navigation = () => {
  const { data: session } = useSession();
  const { data: rbacData, isLoading } = useRBAC();
  
  // <NAME_EMAIL>
  const isAdmin = session?.user?.email === '<EMAIL>';
  const permissions = rbacData?.permissions || [];
  
  const shouldShowMenu = (permission: string) => {
    if (isAdmin) return true; // ✅ Admin bypass
    return permissions.includes(permission);
  };
  
  return (
    <nav>
      {shouldShowMenu('locations.manage') && (
        <NavItem href="/facilities" icon={Building}>
          Facilities
        </NavItem>
      )}
    </nav>
  );
};
```

#### **Step 3: Menambahkan Menu Baru**
```typescript
// Template untuk menambahkan feature baru:
const navigationItems = [
  {
    href: '/facilities',
    label: 'Facilities',
    icon: Building,
    permission: 'locations.manage'
  },
  {
    href: '/equipment',
    label: 'Equipment', 
    icon: Wrench,
    permission: 'equipment.manage'
  },
  // ✨ Tambahkan feature baru di sini
  {
    href: '/new-feature',
    label: 'New Feature',
    icon: Star,
    permission: 'new-feature.manage'
  }
];
```

### 🎯 **Mengapa Solusi Ini Bekerja?**

1. **Admin Bypass**: Memberikan akses penuh untuk admin tanpa menunggu RBAC loading
2. **Graceful Fallback**: Sistem tetap berfungsi meski ada masalah dengan TanStack Query
3. **Debugging Tools**: Memudahkan identifikasi masalah di masa depan

---

## 🔒 Masalah Access Control Facilities

### 🤔 **Apa yang Terjadi?**

Admin tidak bisa mengakses halaman `/facilities` dan mendapat error "Permission denied" meskipun seharusnya punya akses penuh.

### 🔍 **Root Cause Analysis**

#### **1. Middleware Permission Check**
```typescript
// Di middleware atau page component:
if (!hasPermission('locations.manage')) {
  return <PermissionDenied />;
}
```

**Masalahnya:**
- RBAC data belum loaded saat permission check
- `hasPermission` return `false` karena `permissions = []`
- User di-redirect atau ditolak akses

#### **2. Tenant Isolation Logic**
```typescript
// Sistem RBAC dengan tenant isolation:
const userPermissions = await getUserPermissions(userId, tenantId);
// Jika tenantId tidak sesuai, permissions bisa kosong
```

### ✅ **Solusi Step-by-Step**

#### **Step 1: Implementasi Permission Guard**
```typescript
// components/guards/PermissionGuard.tsx
interface PermissionGuardProps {
  permission: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export const PermissionGuard = ({ 
  permission, 
  children, 
  fallback = <PermissionDenied /> 
}: PermissionGuardProps) => {
  const { data: session } = useSession();
  const { data: rbacData, isLoading } = useRBAC();
  
  // ✅ Loading state handling
  if (isLoading) {
    return <LoadingSpinner />;
  }
  
  // ✅ Admin bypass
  if (session?.user?.email === '<EMAIL>') {
    return <>{children}</>;
  }
  
  // ✅ Permission check
  const hasPermission = rbacData?.permissions?.includes(permission);
  if (!hasPermission) {
    return fallback;
  }
  
  return <>{children}</>;
};
```

#### **Step 2: Penggunaan di Page Component**
```typescript
// app/facilities/page.tsx
export default function FacilitiesPage() {
  return (
    <PermissionGuard permission="locations.manage">
      <div className="container mx-auto py-6">
        <h1 className="text-2xl font-bold">Facilities Management</h1>
        <FacilitiesList />
      </div>
    </PermissionGuard>
  );
}
```

#### **Step 3: Menambahkan Permission Baru**
```typescript
// 1. Tambahkan di database (roles_permissions table)
INSERT INTO permissions (name, description) 
VALUES ('new-feature.manage', 'Manage new feature');

// 2. Assign ke role super_admin
INSERT INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id 
FROM roles r, permissions p 
WHERE r.name = 'super_admin' AND p.name = 'new-feature.manage';

// 3. Gunakan di component
<PermissionGuard permission="new-feature.manage">
  <NewFeatureComponent />
</PermissionGuard>
```

### 🎯 **Sistem RBAC Architecture**

```mermaid
graph TD
    A[User] --> B[Role Assignment]
    B --> C[Role Permissions]
    C --> D[Permission Check]
    D --> E{Has Permission?}
    E -->|Yes| F[Access Granted]
    E -->|No| G[Access Denied]
    
    H[Tenant Isolation] --> D
    I[Admin Bypass] --> F
```

---

## 🔑 Masalah Sign In/Sign Out Authentication

### 🤔 **Apa yang Terjadi?**

User tidak bisa login meskipun menggunakan email dan password yang benar dari database. Error "email or password invalid" terus muncul.

### 🔍 **Root Cause Analysis**

#### **1. Password Hash Mismatch**
```bash
# Debug output:
🔐 Authorize attempt: { email: '<EMAIL>', hasPassword: true }
✅ Input validation passed
👤 User lookup result: { found: true, hasPassword: true }
🔑 Password validation: { isValid: false }  # ❌ Masalah di sini
❌ Password invalid
```

**Mengapa ini terjadi?**
- Password di database tidak cocok dengan input user
- Hash bcrypt di database mungkin dibuat dengan password yang berbeda
- Proses seeding database tidak sesuai dengan ekspektasi

#### **2. Auth Route Handler Error**
```typescript
// Error yang muncul:
TypeError: Function.prototype.apply was called on #<Object>
// Disebabkan oleh import/export NextAuth handlers yang salah
```

### ✅ **Solusi Step-by-Step**

#### **Step 1: Debug Password Hash**
```bash
# 1. Cek password hash di database
psql -h 127.0.0.1 -p 5433 -U citizix_user -d saas_app \
  -c "SELECT email, LEFT(password, 20) as password_start FROM users;"

# 2. Test password dengan Node.js
node -e "
const bcrypt = require('bcryptjs');
const password = 'admin123';
const dbHash = '$2a$12$ofy4JNrf.jgSAeTkgz4dsefdrNgFOtev2gmrtaJwnZSwaE02oMTsO';
bcrypt.compare(password, dbHash).then(result => {
  console.log('Password match:', result);
});
"
```

#### **Step 2: Update Password Hash**
```bash
# 1. Generate hash baru
node -e "
const bcrypt = require('bcryptjs');
bcrypt.hash('admin123', 12).then(hash => {
  console.log('New hash:', hash);
});
"

# 2. Update di database
psql -h 127.0.0.1 -p 5433 -U citizix_user -d saas_app \
  -c "UPDATE users SET password = 'NEW_HASH_HERE' WHERE email = '<EMAIL>';"
```

#### **Step 3: Fix Auth Route Handler**
```typescript
// app/api/auth/[...nextauth]/route.ts
import { handlers } from "@/lib/auth/config";

export const { GET, POST } = handlers;

// ✅ Pastikan export sesuai dengan struktur di config.ts
```

#### **Step 4: Implementasi Proper Debugging**
```typescript
// lib/auth/config.ts - Tambahkan logging
async authorize(credentials) {
  try {
    console.log('🔐 Authorize attempt:', { 
      email: credentials?.email, 
      hasPassword: !!credentials?.password 
    });
    
    const { email, password } = loginSchema.parse(credentials);
    console.log('✅ Input validation passed');

    const [user] = await db
      .select()
      .from(users)
      .where(eq(users.email, email))
      .limit(1);

    console.log('👤 User lookup result:', { 
      found: !!user, 
      hasPassword: !!user?.password,
      userId: user?.id 
    });

    if (!user || !user.password) {
      console.log('❌ User not found or no password');
      return null;
    }

    const { compare } = await import("bcryptjs");
    const isPasswordValid = await compare(password, user.password);
    
    console.log('🔑 Password validation:', { isValid: isPasswordValid });
    
    if (!isPasswordValid) {
      console.log('❌ Password invalid');
      return null;
    }

    console.log('✅ Authentication successful');
    return {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role,
    };
  } catch (error) {
    console.error('❌ Auth error:', error);
    return null;
  }
}
```

### 🎯 **Working Credentials**
```
Email: <EMAIL>
Password: admin123
Role: Super Admin
```

### 🔧 **Troubleshooting Guide**

#### **Common Issues & Solutions:**

1. **"CredentialsSignin" Error**
   - ✅ Check: Password hash di database
   - ✅ Check: bcrypt comparison
   - ✅ Check: User exists in database

2. **"Function.prototype.apply" Error**
   - ✅ Check: NextAuth import/export structure
   - ✅ Check: Auth route handler configuration
   - ✅ Restart: Development server

3. **"Session not found" Error**
   - ✅ Check: JWT callback implementation
   - ✅ Check: Session callback implementation
   - ✅ Check: NEXTAUTH_SECRET environment variable

---

## 💡 Tips & Best Practices

### 🚀 **Development Workflow**

1. **Always Debug Step by Step**
   ```typescript
   // Tambahkan logging di setiap step
   console.log('🔍 Step 1: Input validation');
   console.log('🔍 Step 2: Database lookup');
   console.log('🔍 Step 3: Permission check');
   ```

2. **Use Admin Bypass for Development**
   ```typescript
   const isAdmin = session?.user?.email === '<EMAIL>';
   if (isAdmin) return true; // Skip complex logic
   ```

3. **Implement Graceful Fallbacks**
   ```typescript
   const permissions = rbacData?.permissions || [];
   const isLoading = !rbacData && !error;
   ```

### ⚠️ **Common Pitfalls**

1. **❌ Tidak handle loading state**
   ```typescript
   // Salah:
   if (!permissions.includes('manage')) return <Denied />;
   
   // Benar:
   if (isLoading) return <Loading />;
   if (!permissions.includes('manage')) return <Denied />;
   ```

2. **❌ Hardcode permissions di banyak tempat**
   ```typescript
   // Salah: Scattered permission checks
   
   // Benar: Centralized permission system
   const PERMISSIONS = {
     FACILITIES_MANAGE: 'locations.manage',
     EQUIPMENT_MANAGE: 'equipment.manage'
   } as const;
   ```

3. **❌ Tidak test dengan user role yang berbeda**
   - Selalu test dengan admin, user biasa, dan guest
   - Pastikan tenant isolation bekerja dengan benar

### 🎯 **Next Steps**

1. **Remove Debug Logs** dari production code
2. **Implement Proper Error Handling** untuk edge cases
3. **Add Unit Tests** untuk permission logic
4. **Document Permission Matrix** untuk tim

---

**📝 Catatan:** Dokumentasi ini dibuat berdasarkan troubleshooting real yang dilakukan pada sistem NextAuth + RBAC. Semua solusi sudah ditest dan terbukti bekerja.

**🤝 Kontribusi:** Jika menemukan masalah serupa atau solusi yang lebih baik, silakan update dokumentasi ini.
