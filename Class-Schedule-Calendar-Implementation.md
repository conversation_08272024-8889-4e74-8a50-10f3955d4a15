# 📅 Implementasi Class Schedule Calendar View - FAANG Standards

> **Dokumentasi lengkap implementasi Calendar View untuk Class Schedules dengan standar enterprise-level**  
> Mengikuti best practices dari top-tier technology companies

---

## 📋 Daftar Isi

1. [🎯 Overview & Architecture](#-overview--architecture)
2. [🏗️ Component Structure](#️-component-structure)
3. [📱 Multiple View Types](#-multiple-view-types)
4. [🔄 Data Flow & State Management](#-data-flow--state-management)
5. [🎨 UI/UX Design Patterns](#-uiux-design-patterns)
6. [⚡ Performance Optimization](#-performance-optimization)
7. [♿ Accessibility Features](#-accessibility-features)
8. [🔧 Integration Guide](#-integration-guide)

---

## 🎯 Overview & Architecture

### **Mengapa Calendar View Penting?**

Halo teman-teman! Kali ini kita akan belajar implementasi Calendar View yang mengikuti standar FAANG (Facebook, Amazon, Apple, Netflix, Google). Calendar view adalah fitur yang sangat penting untuk aplikasi scheduling karena:

1. **Visual Clarity** - User bisa melihat jadwal dalam format yang familiar
2. **Time Management** - Mudah melihat konflik jadwal dan slot kosong
3. **User Experience** - Interface yang intuitif untuk manage schedules
4. **Professional Appeal** - Terlihat seperti aplikasi enterprise-grade

### **Architecture Overview**

```mermaid
graph TD
    A[ClassSchedulesManagement] --> B[View Toggle]
    B --> C[List View]
    B --> D[Calendar View]
    D --> E[ClassScheduleCalendar]
    E --> F[MonthView]
    E --> G[WeekView]
    E --> H[DayView]
    
    I[TanStack Query] --> E
    J[Framer Motion] --> F
    J --> G
    J --> H
    
    K[CRUD Operations] --> E
    L[Filters] --> E
```

**Komponen Utama:**
1. **ClassScheduleCalendar** - Main calendar component
2. **MonthView** - Monthly calendar grid
3. **WeekView** - Weekly timeline view
4. **DayView** - Detailed daily schedule
5. **Integration Layer** - Seamless integration dengan existing management

---

## 🏗️ Component Structure

### **1. ClassScheduleCalendar (Main Component)**

```typescript
interface ClassScheduleCalendarProps {
  tenantId: number;
  selectedClassId?: string;
  selectedLocationId?: string;
  selectedInstructorId?: string;
  onViewChange?: (view: CalendarView) => void;
  onDateChange?: (date: Date) => void;
  onEventClick?: (event: CalendarEvent) => void;
  onEventCreate?: (date: Date, time?: string) => void;
}
```

**Kenapa interface ini powerful?**
- **Flexible Filtering** - Support multiple filter types
- **Event Callbacks** - Proper event handling untuk parent components
- **Type Safety** - Full TypeScript support
- **Extensible** - Mudah ditambah props baru

### **2. Calendar Views Hierarchy**

#### **MonthView Component**
```typescript
// Features:
- Grid layout 7x6 (weeks x days)
- Color-coded events berdasarkan calender_color
- Hover effects dengan quick actions
- Tooltip dengan event details
- Responsive design untuk mobile
```

**Kenapa MonthView penting?**
- **Overview** - Melihat jadwal bulanan sekaligus
- **Planning** - Mudah spot patterns dan gaps
- **Navigation** - Quick jump ke tanggal tertentu

#### **WeekView Component**
```typescript
// Features:
- Time slots dari 6 AM - 10 PM
- Detailed event information
- Drag & drop ready (future enhancement)
- Real-time current time indicator
- Professional timeline design
```

**Keuntungan WeekView:**
- **Detail Level** - Balance antara overview dan detail
- **Time Management** - Jelas melihat time conflicts
- **Professional** - Seperti Google Calendar atau Outlook

#### **DayView Component**
```typescript
// Features:
- 30-minute time slots
- Full event details dengan badges
- Current time indicator untuk hari ini
- Comprehensive event information
- Easy event management
```

**Mengapa DayView powerful?**
- **Maximum Detail** - Semua informasi event terlihat
- **Focus** - Concentrate pada satu hari
- **Management** - Easy CRUD operations

---

## 📱 Multiple View Types

### **View Switching Logic**

```typescript
const [view, setView] = useState<CalendarView>("month");

// Calculate date range based on view
const dateRange = useMemo(() => {
  const start = new Date(currentDate);
  const end = new Date(currentDate);

  switch (view) {
    case "month":
      // Show full month including partial weeks
      start.setDate(1);
      start.setDate(start.getDate() - start.getDay());
      end.setMonth(end.getMonth() + 1, 0);
      end.setDate(end.getDate() + (6 - end.getDay()));
      break;
    case "week":
      // Show Sunday to Saturday
      start.setDate(start.getDate() - start.getDay());
      end.setDate(start.getDate() + 6);
      break;
    case "day":
      // Single day
      end.setDate(start.getDate());
      break;
  }

  return {
    start: start.toISOString().split('T')[0],
    end: end.toISOString().split('T')[0],
  };
}, [currentDate, view]);
```

**Kenapa logic ini smart?**
1. **Dynamic Range** - Date range berubah sesuai view
2. **Optimized Queries** - Hanya fetch data yang diperlukan
3. **Performance** - Minimal data transfer
4. **Caching** - TanStack Query bisa cache per range

### **Responsive Design Strategy**

```typescript
// Mobile-first approach
<div className="grid grid-cols-7"> {/* Desktop: 7 columns */}
  {/* Mobile: Stack vertically dengan CSS */}
</div>

// Conditional rendering untuk mobile
{isMobile ? (
  <MobileCalendarView />
) : (
  <DesktopCalendarView />
)}
```

---

## 🔄 Data Flow & State Management

### **TanStack Query Integration**

```typescript
// Optimized data fetching
const {
  data: schedules = [],
  isLoading,
  error,
  refetch,
} = useClassSchedulesByDateRange(tenantId, dateRange.start, dateRange.end);

// Filtered schedules dengan memoization
const filteredSchedules = useMemo(() => {
  return schedules.filter(schedule => {
    if (selectedClassId && schedule.class_id !== selectedClassId) return false;
    if (selectedLocationId && schedule.location_id !== selectedLocationId) return false;
    if (selectedInstructorId && schedule.staff_id !== selectedInstructorId) return false;
    return true;
  });
}, [schedules, selectedClassId, selectedLocationId, selectedInstructorId]);
```

**Kenapa pattern ini excellent?**
1. **Performance** - Memoization prevents unnecessary re-renders
2. **Flexibility** - Multiple filters bisa dikombinasi
3. **Caching** - TanStack Query handle caching otomatis
4. **Real-time** - Data selalu up-to-date

### **State Management Strategy**

```typescript
// Local state untuk UI
const [currentDate, setCurrentDate] = useState(new Date());
const [view, setView] = useState<CalendarView>("month");
const [selectedEvent, setSelectedEvent] = useState<CalendarEvent | null>(null);

// Server state via TanStack Query
const { data: schedules } = useClassSchedulesByDateRange(...);

// Derived state dengan memoization
const schedulesByDate = useMemo(() => {
  // Group schedules by date
  const grouped: Record<string, CalendarEvent[]> = {};
  // ... grouping logic
  return grouped;
}, [filteredSchedules, classes, locations]);
```

**Best Practices:**
- **Separation of Concerns** - UI state vs Server state
- **Memoization** - Expensive calculations di-cache
- **Immutability** - State updates yang predictable

---

## 🎨 UI/UX Design Patterns

### **Color Coding System**

```typescript
// Event styling berdasarkan calender_color
<div
  style={{
    backgroundColor: schedule.calender_color || '#3b82f6',
    color: 'white',
  }}
  className="p-2 rounded cursor-pointer hover:shadow-md"
>
  {/* Event content */}
</div>
```

**Kenapa color coding penting?**
1. **Visual Hierarchy** - Different classes mudah dibedakan
2. **User Preference** - User bisa customize colors
3. **Accessibility** - Tetap readable dengan proper contrast
4. **Professional** - Consistent dengan calendar apps lain

### **Animation Strategy dengan Framer Motion**

```typescript
// Smooth transitions
<motion.div
  initial={{ opacity: 0, y: 20 }}
  animate={{ opacity: 1, y: 0 }}
  exit={{ opacity: 0, y: -20 }}
  transition={{ duration: 0.2 }}
>
  {/* Calendar content */}
</motion.div>

// Staggered animations untuk list items
{schedules.map((schedule, index) => (
  <motion.div
    key={schedule.id}
    initial={{ opacity: 0, x: -20 }}
    animate={{ opacity: 1, x: 0 }}
    transition={{ duration: 0.2, delay: index * 0.05 }}
  >
    {/* Schedule item */}
  </motion.div>
))}
```

**Animation Best Practices:**
- **Subtle** - Tidak mengganggu user experience
- **Performance** - Menggunakan transform properties
- **Purposeful** - Setiap animation punya tujuan
- **Consistent** - Timing dan easing yang konsisten

### **Interactive Elements**

```typescript
// Hover states dengan quick actions
<div className="group relative">
  <div className="opacity-0 group-hover:opacity-100 transition-opacity">
    <Button onClick={onEdit}>
      <Edit className="h-4 w-4" />
    </Button>
    <Button onClick={onDelete}>
      <Trash2 className="h-4 w-4" />
    </Button>
  </div>
</div>

// Click-to-create functionality
<div 
  onClick={() => onDateClick(date, time)}
  className="cursor-pointer hover:bg-muted/30"
>
  {/* Time slot */}
</div>
```

---

## ⚡ Performance Optimization

### **Memoization Strategy**

```typescript
// Expensive calculations di-memoize
const calendarDays = useMemo(() => {
  // Generate calendar grid
  const days = [];
  // ... calculation logic
  return days;
}, [currentDate]);

const timeSlots = useMemo(() => {
  // Generate time slots
  const slots = [];
  for (let hour = 6; hour <= 22; hour++) {
    // ... slot generation
  }
  return slots;
}, []);
```

**Kenapa memoization critical?**
1. **Performance** - Prevent unnecessary recalculations
2. **Smooth UI** - No jank during interactions
3. **Battery Life** - Less CPU usage di mobile
4. **Scalability** - Handle large datasets

### **Virtual Scrolling (Future Enhancement)**

```typescript
// Untuk large datasets
import { FixedSizeList as List } from 'react-window';

<List
  height={600}
  itemCount={timeSlots.length}
  itemSize={60}
  itemData={timeSlots}
>
  {TimeSlotRenderer}
</List>
```

### **Lazy Loading Strategy**

```typescript
// Load views on demand
const MonthView = lazy(() => import('./calendar-views/month-view'));
const WeekView = lazy(() => import('./calendar-views/week-view'));
const DayView = lazy(() => import('./calendar-views/day-view'));

// Dengan Suspense
<Suspense fallback={<CalendarSkeleton />}>
  {view === "month" && <MonthView {...props} />}
  {view === "week" && <WeekView {...props} />}
  {view === "day" && <DayView {...props} />}
</Suspense>
```

---

## ♿ Accessibility Features

### **Keyboard Navigation**

```typescript
// Arrow key navigation
const handleKeyDown = (e: KeyboardEvent) => {
  switch (e.key) {
    case 'ArrowLeft':
      navigateDate('prev');
      break;
    case 'ArrowRight':
      navigateDate('next');
      break;
    case 'Enter':
    case ' ':
      if (selectedDate) {
        onDateClick(selectedDate);
      }
      break;
  }
};

// Focus management
<div
  tabIndex={0}
  onKeyDown={handleKeyDown}
  role="grid"
  aria-label="Calendar"
>
  {/* Calendar content */}
</div>
```

### **Screen Reader Support**

```typescript
// ARIA labels dan descriptions
<div
  role="gridcell"
  aria-label={`${day.dayNumber} ${monthName} ${year}`}
  aria-selected={day.isSelected}
  aria-describedby={`events-${day.dateString}`}
>
  <span className="sr-only">
    {day.dayNumber} {monthName} {year}
    {hasSchedules && `, ${schedules.length} events`}
  </span>
  
  {/* Visual content */}
</div>

// Event descriptions
<div id={`events-${day.dateString}`} className="sr-only">
  {schedules.map(schedule => (
    <span key={schedule.id}>
      {schedule.class_name} at {formatTime(schedule.start_time)}
    </span>
  ))}
</div>
```

### **Color Contrast & Visual Indicators**

```typescript
// High contrast mode support
<div
  className={`
    ${schedule.calender_color ? '' : 'bg-primary'}
    ${isHighContrast ? 'border-2 border-foreground' : ''}
  `}
  style={{
    backgroundColor: isHighContrast ? 'transparent' : schedule.calender_color,
  }}
>
  {/* Event content */}
</div>
```

---

## 🔧 Integration Guide

### **Seamless Integration dengan Management Component**

```typescript
// Toggle antara List dan Calendar view
const [viewMode, setViewMode] = useState<"list" | "calendar">("list");

return (
  <div>
    {/* View Toggle */}
    <div className="flex items-center border rounded-lg p-1">
      <Button
        variant={viewMode === "list" ? "default" : "ghost"}
        onClick={() => setViewMode("list")}
      >
        <List className="h-4 w-4 mr-2" />
        List
      </Button>
      <Button
        variant={viewMode === "calendar" ? "default" : "ghost"}
        onClick={() => setViewMode("calendar")}
      >
        <Grid3X3 className="h-4 w-4 mr-2" />
        Calendar
      </Button>
    </div>

    {/* Conditional Rendering */}
    {viewMode === "calendar" ? (
      <ClassScheduleCalendar
        tenantId={tenantId}
        selectedClassId={selectedClassId === "all" ? undefined : selectedClassId}
        selectedLocationId={selectedLocationId === "all" ? undefined : selectedLocationId}
        onEventClick={handleEventClick}
        onEventCreate={handleEventCreate}
      />
    ) : (
      <SchedulesList />
    )}
  </div>
);
```

### **Shared State Management**

```typescript
// Filters di-share antara List dan Calendar view
const filters = {
  selectedClassId,
  selectedLocationId,
  selectedInstructorId,
  startDate,
  endDate,
  searchTerm,
};

// Pass ke kedua components
<ClassScheduleCalendar {...filters} />
<SchedulesList {...filters} />
```

### **CRUD Operations Integration**

```typescript
// Shared CRUD handlers
const handleCreateSchedule = async (data: ClassScheduleFormData) => {
  try {
    const newSchedule = await createMutation.mutateAsync(data);
    setShowSuccessToast(true);
    refetch(); // Refresh both views
  } catch (error) {
    console.error("Error creating schedule:", error);
  }
};

// Pass ke calendar
<ClassScheduleCalendar
  onEventCreate={(date, time) => {
    // Pre-fill form dengan selected date/time
    setCreateDate(date);
    setCreateTime(time);
    setIsCreateDialogOpen(true);
  }}
/>
```

---

## 🎉 Kesimpulan

Selamat! Kita sudah berhasil implementasi Calendar View dengan standar FAANG-level yang mencakup:

### **✅ Yang Sudah Diimplementasi:**

1. **🏗️ Architecture** - Modular component structure
2. **📱 Multiple Views** - Month, Week, Day views
3. **🔄 Data Integration** - TanStack Query dengan caching
4. **🎨 Professional UI** - Framer Motion animations
5. **⚡ Performance** - Memoization dan optimization
6. **♿ Accessibility** - Keyboard navigation dan screen reader
7. **🔧 Integration** - Seamless dengan existing management

### **🚀 Next Level Enhancements:**

1. **📱 Mobile Gestures** - Swipe navigation
2. **🖱️ Drag & Drop** - Reschedule dengan drag
3. **🔔 Real-time Updates** - WebSocket integration
4. **📊 Analytics** - Schedule utilization metrics
5. **🎯 Smart Scheduling** - AI-powered suggestions
6. **📱 PWA Features** - Offline support

### **💡 Key Takeaways:**

1. **Consistency** - Follow established patterns
2. **Performance** - Always think about optimization
3. **Accessibility** - Include everyone from day one
4. **User Experience** - Smooth, intuitive interactions
5. **Maintainability** - Clean, documented code

**Remember:** Great calendar implementations are not just about showing dates - they're about helping users manage time effectively with a delightful experience! 🌟

---

**Happy Coding! 🚀**

*Dokumentasi ini dibuat dengan ❤️ mengikuti standar FAANG untuk enterprise-level applications.*
