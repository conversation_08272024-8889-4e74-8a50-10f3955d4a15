"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Plus, Edit, Trash2, ImageIcon, Eye } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { ClassImageForm } from "@/components/forms/class-image-form";
import {
  useClassImages,
  useCreateClassImage,
  useUpdateClassImage,
  useDeleteClassImage,
  uploadClassImage,
  type ClassImageFormData,
} from "@/lib/hooks/queries/use-class-image-queries";
import { type ClassImage } from "@/lib/db/schema";
import { SuccessToast } from "@/components/ui/success-toast";

/**
 * ClassImagesManagement Component
 * 
 * Component untuk manage class images dengan full CRUD functionality.
 * Mengikuti pattern yang sama dengan classes management dan components lainnya.
 * 
 * Features:
 * - Display images dalam grid layout
 * - Create, edit, delete images
 * - Image preview functionality
 * - Success feedback dengan SuccessToast
 * - Professional animations
 */

interface ClassImagesManagementProps {
  classId: string;
  className?: string;
}

export function ClassImagesManagement({ classId, className = "" }: ClassImagesManagementProps) {
  // State management
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingImage, setEditingImage] = useState<ClassImage | null>(null);
  const [deletingImage, setDeletingImage] = useState<ClassImage | null>(null);
  const [previewImage, setPreviewImage] = useState<ClassImage | null>(null);
  const [showSuccessToast, setShowSuccessToast] = useState(false);
  const [createdImage, setCreatedImage] = useState<ClassImage | null>(null);

  // Queries
  const {
    data: images = [],
    isLoading,
    error,
    refetch,
  } = useClassImages(classId);

  // Mutations
  const createMutation = useCreateClassImage();
  const updateMutation = useUpdateClassImage();
  const deleteMutation = useDeleteClassImage();

  // Handlers
  const handleCreateImage = async (data: ClassImageFormData) => {
    try {
      const newImage = await createMutation.mutateAsync(data);

      setIsCreateDialogOpen(false);
      setCreatedImage(newImage);
      setShowSuccessToast(true);

      // Hide success toast after duration
      setTimeout(() => {
        setShowSuccessToast(false);
      }, 4000);
    } catch (error) {
      console.error("Create image error:", error);
    }
  };

  const handleDirectUpload = async (file: File) => {
    try {
      const newImage = await uploadClassImage(classId, file);
      setCreatedImage(newImage);
      setShowSuccessToast(true);

      // Hide success toast after duration
      setTimeout(() => {
        setShowSuccessToast(false);
      }, 4000);

      // Refetch images
      refetch();
    } catch (error) {
      console.error("Direct upload error:", error);
    }
  };

  const handleEditImage = async (data: ClassImageFormData) => {
    if (!editingImage) return;

    try {
      const updatedImage = await updateMutation.mutateAsync({
        id: editingImage.id,
        data: {
          image_url: data.image_url,
        },
      });

      setEditingImage(null);
      setCreatedImage(updatedImage);
      setShowSuccessToast(true);

      // Hide success toast after duration
      setTimeout(() => {
        setShowSuccessToast(false);
      }, 4000);
    } catch (error) {
      console.error("Update image error:", error);
    }
  };

  const handleDeleteImage = async () => {
    if (!deletingImage) return;

    try {
      await deleteMutation.mutateAsync({
        id: deletingImage.id,
        classId: deletingImage.class_id,
      });

      setDeletingImage(null);
      setShowSuccessToast(true);

      // Hide success toast after duration
      setTimeout(() => {
        setShowSuccessToast(false);
      }, 4000);
    } catch (error) {
      console.error("Delete image error:", error);
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className={`flex items-center justify-center min-h-[200px] ${className}`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading images...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className={`flex items-center justify-center min-h-[200px] ${className}`}>
        <div className="text-center">
          <p className="text-red-600 mb-4">Error loading images: {String(error)}</p>
          <Button onClick={() => refetch()} variant="outline">
            Try Again
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div>
          <h2 className="text-xl font-bold tracking-tight">Class Images</h2>
          <p className="text-muted-foreground">
            Manage images for this class ({images.length} image{images.length !== 1 ? 's' : ''})
          </p>
        </div>
        
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              Add Image
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Add Class Image</DialogTitle>
            </DialogHeader>
            <ClassImageForm
              classId={classId}
              onSubmit={handleCreateImage}
              onCancel={() => setIsCreateDialogOpen(false)}
              isLoading={createMutation.isPending}
            />
          </DialogContent>
        </Dialog>
      </div>

      {/* Images Grid */}
      {images.length === 0 ? (
        <div className="text-center py-12">
          <ImageIcon className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium mb-2">No images found</h3>
          <p className="text-muted-foreground mb-4">
            Get started by adding your first class image
          </p>
          <Button onClick={() => setIsCreateDialogOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Add Image
          </Button>
        </div>
      ) : (
        <motion.div 
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          <AnimatePresence>
            {images.map((image: ClassImage) => (
              <motion.div
                key={image.id}
                layout
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.9 }}
                transition={{ duration: 0.2 }}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Card className="hover:shadow-lg transition-all duration-200">
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-sm font-medium mb-2">
                          Class Image
                        </CardTitle>
                        <Badge variant="secondary" className="text-xs">
                          ID: {image.id.slice(-8)}
                        </Badge>
                      </div>
                      <div className="flex gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setPreviewImage(image)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setEditingImage(image)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setDeletingImage(image)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="aspect-video mb-3 overflow-hidden rounded-lg bg-muted">
                      <img
                        src={image.image_url}
                        alt="Class image"
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = "/images/placeholder-image.svg";
                        }}
                      />
                    </div>
                    <div className="flex items-center justify-between text-xs text-muted-foreground">
                      <span>Added {new Date(image.createdAt).toLocaleDateString()}</span>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </AnimatePresence>
        </motion.div>
      )}

      {/* Edit Dialog */}
      <Dialog open={!!editingImage} onOpenChange={() => setEditingImage(null)}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Class Image</DialogTitle>
          </DialogHeader>
          {editingImage && (
            <ClassImageForm
              classImage={editingImage}
              classId={classId}
              onSubmit={handleEditImage}
              onCancel={() => setEditingImage(null)}
              isLoading={updateMutation.isPending}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Preview Dialog */}
      <Dialog open={!!previewImage} onOpenChange={() => setPreviewImage(null)}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Image Preview</DialogTitle>
          </DialogHeader>
          {previewImage && (
            <div className="space-y-4">
              <img
                src={previewImage.image_url}
                alt="Class image preview"
                className="w-full max-h-[70vh] object-contain rounded-lg"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = "/images/placeholder-image.svg";
                }}
              />
              <div className="text-sm text-muted-foreground">
                <p><strong>Image ID:</strong> {previewImage.id}</p>
                <p><strong>Added:</strong> {new Date(previewImage.createdAt).toLocaleString()}</p>
                <p><strong>URL:</strong> <span className="break-all">{previewImage.image_url}</span></p>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deletingImage} onOpenChange={() => setDeletingImage(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Class Image</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this image? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteImage}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Success Animations */}
      <SuccessToast
        isVisible={showSuccessToast}
        onClose={() => setShowSuccessToast(false)}
        title="Class Image Success!"
        description={createdImage ? 
          "Class image has been processed successfully." : 
          "Your class image operation completed successfully."
        }
        type="general"
        action={{
          label: "View All Images",
          onClick: () => setShowSuccessToast(false)
        }}
      />
    </div>
  );
}
