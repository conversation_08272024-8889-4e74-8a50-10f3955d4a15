import { NextRequest } from "next/server";
import { z } from "zod";
import { hash } from "bcryptjs";
import { db } from "@/lib/db";
import { customers } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";
import { 
  createAuthenticatedResponse, 
  createErrorResponse,
  rateLimit,
  addSecurityHeaders 
} from "@/lib/middleware/customer-auth.middleware";
import { rateLimitConfig } from "@/lib/security/rate-limit.config";

/**
 * Customer Registration Endpoint
 * 
 * POST /api/auth/customer/register
 * 
 * Registers a new customer account with email/password
 */

const registerSchema = z.object({
  firstName: z.string().min(2, "First name must be at least 2 characters").max(50),
  lastName: z.string().min(2, "Last name must be at least 2 characters").max(50),
  email: z.string().email("Invalid email address").toLowerCase(),
  password: z.string().min(8, "Password must be at least 8 characters").max(100),
  tenantId: z.number().int().positive(),
});

export async function POST(request: NextRequest) {
  try {
    console.log('🚀 [Customer Register] Processing registration request');

    // Rate limiting
    const clientIP = request.headers.get('x-forwarded-for')?.split(',')[0] || 
                    request.headers.get('x-real-ip') || 
                    'anonymous';

    const rateLimitResult = await rateLimit(
      `customer-register:${clientIP}`,
      rateLimitConfig.auth.register
    );

    if (!rateLimitResult.success) {
      return createErrorResponse(
        "Too many registration attempts",
        "RATE_LIMIT_EXCEEDED",
        429,
        rateLimitResult.headers
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = registerSchema.parse(body);

    console.log(`📧 [Customer Register] Registration attempt for: ${validatedData.email}`);

    // Check if customer already exists
    const [existingCustomer] = await db
      .select()
      .from(customers)
      .where(and(
        eq(customers.email, validatedData.email),
        eq(customers.tenantId, validatedData.tenantId)
      ))
      .limit(1);

    if (existingCustomer) {
      console.log(`❌ [Customer Register] Customer already exists: ${validatedData.email}`);
      return createErrorResponse(
        "An account with this email already exists",
        "EMAIL_ALREADY_EXISTS",
        400
      );
    }

    // Hash password
    const hashedPassword = await hash(validatedData.password, 12);

    // Create customer account
    const [newCustomer] = await db
      .insert(customers)
      .values({
        firstName: validatedData.firstName,
        lastName: validatedData.lastName,
        email: validatedData.email,
        password: hashedPassword,
        tenantId: validatedData.tenantId,
        displayName: `${validatedData.firstName} ${validatedData.lastName}`,
        isActive: true,
        emailVerified: null, // Will be set when email is verified
        membershipType: 'basic',
        preferences: {},
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning({
        id: customers.id,
        email: customers.email,
        firstName: customers.firstName,
        lastName: customers.lastName,
        displayName: customers.displayName,
        tenantId: customers.tenantId,
        createdAt: customers.createdAt,
      });

    console.log(`✅ [Customer Register] Customer created successfully: ${newCustomer.email} (ID: ${newCustomer.id})`);

    // TODO: Send email verification email
    // await sendEmailVerification(newCustomer.email, newCustomer.id);

    // Prepare response data (exclude sensitive information)
    const responseData = {
      success: true,
      message: "Account created successfully",
      customer: {
        id: newCustomer.id,
        email: newCustomer.email,
        firstName: newCustomer.firstName,
        lastName: newCustomer.lastName,
        displayName: newCustomer.displayName,
        tenantId: newCustomer.tenantId,
        isEmailVerified: false,
        createdAt: newCustomer.createdAt,
      },
      nextSteps: {
        emailVerification: true,
        message: "Please check your email to verify your account",
      },
    };

    return createAuthenticatedResponse(responseData, 201, rateLimitResult.headers);

  } catch (error) {
    console.error("❌ [Customer Register] Registration error:", error);

    if (error instanceof z.ZodError) {
      return createErrorResponse(
        "Invalid registration data",
        "INVALID_INPUT",
        400,
        undefined,
        error.errors
      );
    }

    // Check for database constraint errors
    if (error instanceof Error) {
      if (error.message.includes('unique constraint')) {
        return createErrorResponse(
          "An account with this email already exists",
          "EMAIL_ALREADY_EXISTS",
          400
        );
      }
    }

    return createErrorResponse(
      "Registration failed",
      "REGISTRATION_ERROR",
      500
    );
  }
}

// Handle preflight OPTIONS request
export async function OPTIONS(request: NextRequest) {
  const response = new Response(null, { status: 200 });
  return addSecurityHeaders(response);
}
