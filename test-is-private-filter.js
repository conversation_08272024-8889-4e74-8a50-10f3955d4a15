/**
 * Test Script untuk Filter is_private pada Class Schedules API
 *
 * ⚠️  DEPRECATED: Endpoint /api/class-schedules sudah dihapus!
 * Gunakan test-api-key-is-private-filter.js untuk testing endpoint API key.
 *
 * Script ini untuk testing manual implementasi filter is_private
 * Jalankan dengan: node test-is-private-filter.js
 */

const BASE_URL = 'http://localhost:3000';
const TENANT_ID = 1; // Sesuaikan dengan tenant ID yang ada

async function testIsPrivateFilter() {
  console.log('⚠️  DEPRECATED: Endpoint /api/class-schedules sudah dihapus!');
  console.log('🔍 Gunakan test-api-key-is-private-filter.js untuk testing endpoint API key.\n');
  console.log('📋 Endpoint yang tersedia sekarang:');
  console.log('   GET /api/public/v1/class-schedules (dengan API key)');
  console.log('\n🚀 Jalankan: node test-api-key-is-private-filter.js');
  return;
}

// Run tests
testIsPrivateFilter().catch(console.error);
