"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useSession } from "next-auth/react";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { 
  Save, 
  Eye, 
  Upload, 
  X, 
  Plus,
  Loader2,
  AlertCircle,
  CheckCircle2,
  FileText,
  Image as ImageIcon
} from "lucide-react";
import { toast } from "sonner";
import { createBlogPostSchema, updateBlogPostSchema, type CreateBlogPostData, type UpdateBlogPostData } from "@/lib/validations/blog";
import { 
  useCreateBlogPost, 
  useUpdateBlogPost, 
  useBlogCategoriesByTenant,
  useValidateSlug,
  type BlogPostWithAuthor 
} from "@/lib/hooks/queries/use-blog-queries";
import { z } from "zod";

interface BlogPostFormProps {
  open: boolean;
  onClose: () => void;
  title?: string;
  description?: string;
  blogPost?: BlogPostWithAuthor;
  tenantId: number;
}

// Use schema-based type for form data
type FormData = CreateBlogPostData;

export function BlogPostForm({
  open,
  onClose,
  title = "Add Blog Post",
  description = "Create a new blog post",
  blogPost,
  tenantId,
}: BlogPostFormProps) {
  const [tags, setTags] = useState<string[]>([]);
  const [newTag, setNewTag] = useState("");
  const [slugValidation, setSlugValidation] = useState<{ status: 'idle' | 'checking' | 'available' | 'taken'; message?: string }>({ status: 'idle' });

  const { data: session } = useSession();
  const isEditing = !!blogPost;

  const form = useForm<FormData>({
    // Temporarily disable resolver to focus on SelectItem fix
    // 
    defaultValues: {
      tenantId,
      title: "",
      slug: undefined,
      content: "",
      excerpt: undefined,
      featured_image: undefined,
      category_id: undefined,
      tags: [],
      status: "draft",
      is_featured: false,
      author_id: session?.user?.id || "admin", // Get from session
      seo_title: undefined,
      seo_description: undefined,
    },
  });

  // Data fetching
  const { data: categories = [] } = useBlogCategoriesByTenant(tenantId, {
    filters: { is_active: true }
  });

  // Mutations
  const createMutation = useCreateBlogPost();
  const updateMutation = useUpdateBlogPost();
  const validateSlugMutation = useValidateSlug(tenantId);

  // Reset form when dialog opens/closes or blogPost changes
  useEffect(() => {
    if (open) {
      if (blogPost) {
        form.reset({
          title: blogPost.title,
          slug: blogPost.slug,
          content: blogPost.content,
          excerpt: blogPost.excerpt || "",
          featured_image: blogPost.featured_image || "",
          category_id: blogPost.category_id || "",
          tags: blogPost.tags || [],
          status: blogPost.status as "draft" | "published",
          is_featured: blogPost.is_featured,
          seo_title: blogPost.seo_title || "",
          seo_description: blogPost.seo_description || "",
        });
        setTags(blogPost.tags || []);
      } else {
        form.reset({
          tenantId,
          title: "",
          slug: "",
          content: "",
          excerpt: "",
          featured_image: "",
          category_id: "",
          tags: [],
          status: "draft",
          is_featured: false,
          author_id: session?.user?.id || "admin",
          seo_title: "",
          seo_description: "",
        });
        setTags([]);
      }
      setSlugValidation({ status: 'idle' });
    }
  }, [open, blogPost, form, tenantId]);

  // Auto-generate slug from title
  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .trim();
  };

  // Handle title change to auto-generate slug
  const handleTitleChange = (title: string) => {
    form.setValue("title", title);
    if (!isEditing || !form.getValues("slug")) {
      const newSlug = generateSlug(title);
      form.setValue("slug", newSlug);
      if (newSlug) {
        validateSlug(newSlug);
      }
    }
  };

  // Validate slug availability
  const validateSlug = async (slug: string) => {
    if (!slug) {
      setSlugValidation({ status: 'idle' });
      return;
    }

    setSlugValidation({ status: 'checking' });
    
    try {
      const isAvailable = await validateSlugMutation.mutateAsync({
        slug,
        excludeId: blogPost?.id
      });
      
      setSlugValidation({
        status: isAvailable ? 'available' : 'taken',
        message: isAvailable ? 'Slug tersedia' : 'Slug sudah digunakan'
      });
    } catch (error) {
      setSlugValidation({
        status: 'idle',
        message: 'Gagal validasi slug'
      });
    }
  };

  // Handle slug change
  const handleSlugChange = (slug: string) => {
    const cleanSlug = slug.toLowerCase().replace(/[^a-z0-9-]/g, '');
    form.setValue("slug", cleanSlug);
    validateSlug(cleanSlug);
  };

  // Tag management
  const addTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim()) && tags.length < 10) {
      const updatedTags = [...tags, newTag.trim()];
      setTags(updatedTags);
      form.setValue("tags", updatedTags);
      setNewTag("");
    }
  };

  const removeTag = (tagToRemove: string) => {
    const updatedTags = tags.filter(tag => tag !== tagToRemove);
    setTags(updatedTags);
    form.setValue("tags", updatedTags);
  };

  // Form submission
  const onSubmit = async (data: FormData) => {
    try {
      if (slugValidation.status === 'taken') {
        toast.error("Slug sudah digunakan, silakan gunakan slug lain");
        return;
      }

      if (isEditing && blogPost) {
        // For update, only send the changed fields
        const updateData: UpdateBlogPostData = {
          ...data,
          tags,
          // Convert empty strings to undefined for optional fields
          category_id: data.category_id === "none" || data.category_id === "" ? undefined : data.category_id,
          featured_image: data.featured_image === "" ? undefined : data.featured_image,
          excerpt: data.excerpt === "" ? undefined : data.excerpt,
          seo_title: data.seo_title === "" ? undefined : data.seo_title,
          seo_description: data.seo_description === "" ? undefined : data.seo_description,
        };

        await updateMutation.mutateAsync({
          id: blogPost.id,
          data: updateData
        });
        toast.success("Blog post berhasil diupdate");
      } else {
        // For create, include required fields
        const createData: CreateBlogPostData = {
          ...data,
          tags,
          tenantId,
          author_id: session?.user?.id || "admin", // Get from session
          title: data.title || "",
          content: data.content || "",
          slug: data.slug || "",
          // Convert empty strings to undefined for optional fields
          category_id: data.category_id === "none" || data.category_id === "" ? undefined : data.category_id,
          featured_image: data.featured_image === "" ? undefined : data.featured_image,
          excerpt: data.excerpt === "" ? undefined : data.excerpt,
          seo_title: data.seo_title === "" ? undefined : data.seo_title,
          seo_description: data.seo_description === "" ? undefined : data.seo_description,
        };

        await createMutation.mutateAsync(createData);
        toast.success("Blog post berhasil dibuat");
      }

      onClose();
    } catch (error) {
      toast.error(isEditing ? "Gagal update blog post" : "Gagal membuat blog post");
    }
  };

  const isLoading = createMutation.isPending || updateMutation.isPending;

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <Tabs defaultValue="content" className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="content">Content</TabsTrigger>
                <TabsTrigger value="settings">Settings</TabsTrigger>
                <TabsTrigger value="seo">SEO</TabsTrigger>
              </TabsList>

              {/* Content Tab */}
              <TabsContent value="content" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Title */}
                  <FormField
                    control={form.control}
                    name="title"
                    render={({ field }) => (
                      <FormItem className="md:col-span-2">
                        <FormLabel>Title *</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Masukkan judul blog post..."
                            {...field}
                            onChange={(e) => handleTitleChange(e.target.value)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Slug */}
                  <FormField
                    control={form.control}
                    name="slug"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Slug *</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Input
                              placeholder="url-friendly-slug"
                              {...field}
                              onChange={(e) => handleSlugChange(e.target.value)}
                            />
                            {slugValidation.status === 'checking' && (
                              <Loader2 className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 animate-spin" />
                            )}
                            {slugValidation.status === 'available' && (
                              <CheckCircle2 className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-green-500" />
                            )}
                            {slugValidation.status === 'taken' && (
                              <AlertCircle className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-red-500" />
                            )}
                          </div>
                        </FormControl>
                        {slugValidation.message && (
                          <FormDescription className={slugValidation.status === 'taken' ? 'text-red-500' : 'text-green-500'}>
                            {slugValidation.message}
                          </FormDescription>
                        )}
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Category */}
                  <FormField
                    control={form.control}
                    name="category_id"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Category</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Pilih kategori..." />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="none">No Category</SelectItem>
                            {categories.map((category: any) => (
                              <SelectItem key={category.id} value={category.id}>
                                {category.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                {/* Excerpt */}
                <FormField
                  control={form.control}
                  name="excerpt"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Excerpt</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Ringkasan singkat blog post..."
                          className="min-h-[80px]"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Ringkasan yang akan ditampilkan di daftar blog posts
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Content */}
                <FormField
                  control={form.control}
                  name="content"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Content *</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Tulis konten blog post di sini..."
                          className="min-h-[300px]"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Konten utama blog post (mendukung HTML)
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Tags */}
                <div className="space-y-2">
                  <Label>Tags</Label>
                  <div className="flex gap-2">
                    <Input
                      placeholder="Tambah tag..."
                      value={newTag}
                      onChange={(e) => setNewTag(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          addTag();
                        }
                      }}
                    />
                    <Button type="button" onClick={addTag} variant="outline" size="sm">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  {tags.length > 0 && (
                    <div className="flex flex-wrap gap-2">
                      {tags.map((tag) => (
                        <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                          {tag}
                          <X
                            className="h-3 w-3 cursor-pointer"
                            onClick={() => removeTag(tag)}
                          />
                        </Badge>
                      ))}
                    </div>
                  )}
                  <p className="text-sm text-gray-500">
                    {tags.length}/10 tags
                  </p>
                </div>
              </TabsContent>

              {/* Settings Tab */}
              <TabsContent value="settings" className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Featured Image */}
                  <FormField
                    control={form.control}
                    name="featured_image"
                    render={({ field }) => (
                      <FormItem className="md:col-span-2">
                        <FormLabel>Featured Image URL</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="https://example.com/image.jpg"
                            {...field}
                          />
                        </FormControl>
                        <FormDescription>
                          URL gambar utama untuk blog post
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Status */}
                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Status</FormLabel>
                        <Select onValueChange={field.onChange} value={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="draft">Draft</SelectItem>
                            <SelectItem value="published">Published</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Featured */}
                  <FormField
                    control={form.control}
                    name="is_featured"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">Featured Post</FormLabel>
                          <FormDescription>
                            Tampilkan di bagian featured posts
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
              </TabsContent>

              {/* SEO Tab */}
              <TabsContent value="seo" className="space-y-4">
                {/* SEO Title */}
                <FormField
                  control={form.control}
                  name="seo_title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>SEO Title</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Judul untuk SEO (opsional)"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Jika kosong, akan menggunakan judul post. Optimal: 50-60 karakter
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* SEO Description */}
                <FormField
                  control={form.control}
                  name="seo_description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>SEO Description</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Deskripsi untuk SEO (opsional)"
                          className="min-h-[100px]"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Deskripsi yang akan muncul di hasil pencarian. Optimal: 150-160 karakter
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </TabsContent>
            </Tabs>

            {/* Form Actions */}
            <Separator />
            <div className="flex justify-end gap-2">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button type="submit" disabled={isLoading || slugValidation.status === 'taken'}>
                {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                {isEditing ? "Update Post" : "Create Post"}
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
