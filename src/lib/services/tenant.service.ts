import { db } from "../db";
import { tenants, organizations, type Tenant, type NewTenant } from "../db/schema";
import { eq, and, like, desc } from "drizzle-orm";
import { createId } from "@paralleldrive/cuid2";

export class TenantService {
  /**
   * Create a new tenant
   */
  static async createTenant(data: Omit<NewTenant, "id" | "createdAt" | "updatedAt">): Promise<Tenant> {
    // Validate subdomain format
    if (!this.isValidSubdomain(data.subdomain)) {
      throw new Error("Invalid subdomain format. Use only lowercase letters, numbers, and hyphens.");
    }

    // Check if subdomain is already taken
    const existingTenant = await this.getTenantBySubdomain(data.subdomain);
    if (existingTenant) {
      throw new Error("Subdomain is already taken");
    }

    const [tenant] = await db
      .insert(tenants)
      .values({
        ...data,
        subdomain: data.subdomain.toLowerCase(),
      })
      .returning();

    return tenant;
  }

  /**
   * Get tenant by ID
   */
  static async getTenantById(id: number): Promise<Tenant | null> {
    const [tenant] = await db
      .select()
      .from(tenants)
      .where(eq(tenants.id, id))
      .limit(1);

    return tenant || null;
  }

  /**
   * Get tenant by subdomain
   */
  static async getTenantBySubdomain(subdomain: string): Promise<Tenant | null> {
    const [tenant] = await db
      .select()
      .from(tenants)
      .where(eq(tenants.subdomain, subdomain.toLowerCase()))
      .limit(1);

    return tenant || null;
  }

  /**
   * Get tenant by custom domain
   */
  static async getTenantByCustomDomain(domain: string): Promise<Tenant | null> {
    const [tenant] = await db
      .select()
      .from(tenants)
      .where(eq(tenants.customDomain, domain.toLowerCase()))
      .limit(1);

    return tenant || null;
  }

  /**
   * Get all tenants for an organization
   */
  static async getTenantsByOrganization(organizationId: string): Promise<Tenant[]> {
    return await db
      .select()
      .from(tenants)
      .where(eq(tenants.organizationId, organizationId))
      .orderBy(desc(tenants.createdAt));
  }

  /**
   * Update tenant
   */
  static async updateTenant(
    id: number, 
    data: Partial<Omit<NewTenant, "id" | "createdAt">>
  ): Promise<Tenant | null> {
    // If updating subdomain, validate it
    if (data.subdomain) {
      if (!this.isValidSubdomain(data.subdomain)) {
        throw new Error("Invalid subdomain format");
      }

      // Check if new subdomain is already taken by another tenant
      const existingTenant = await this.getTenantBySubdomain(data.subdomain);
      if (existingTenant && existingTenant.id !== id) {
        throw new Error("Subdomain is already taken");
      }

      data.subdomain = data.subdomain.toLowerCase();
    }

    // If updating custom domain, validate it
    if (data.customDomain) {
      data.customDomain = data.customDomain.toLowerCase();
    }

    const [tenant] = await db
      .update(tenants)
      .set({
        ...data,
        updatedAt: new Date(),
      })
      .where(eq(tenants.id, id))
      .returning();

    return tenant || null;
  }

  /**
   * Delete tenant
   */
  static async deleteTenant(id: number): Promise<boolean> {
    const result = await db
      .delete(tenants)
      .where(eq(tenants.id, id));

    return result.rowCount > 0;
  }

  /**
   * Search tenants
   */
  static async searchTenants(query: string, organizationId?: string): Promise<Tenant[]> {
    const conditions = [
      like(tenants.name, `%${query}%`),
    ];

    if (organizationId) {
      conditions.push(eq(tenants.organizationId, organizationId));
    }

    return await db
      .select()
      .from(tenants)
      .where(and(...conditions))
      .orderBy(desc(tenants.createdAt));
  }

  /**
   * Get tenant with organization details
   */
  static async getTenantWithOrganization(id: number) {
    const result = await db
      .select({
        tenant: tenants,
        organization: organizations,
      })
      .from(tenants)
      .leftJoin(organizations, eq(tenants.organizationId, organizations.id))
      .where(eq(tenants.id, id))
      .limit(1);

    return result[0] || null;
  }

  /**
   * Validate subdomain format
   */
  private static isValidSubdomain(subdomain: string): boolean {
    // Subdomain rules:
    // - 3-63 characters
    // - Only lowercase letters, numbers, and hyphens
    // - Cannot start or end with hyphen
    // - Cannot contain consecutive hyphens
    const subdomainRegex = /^[a-z0-9]([a-z0-9-]{1,61}[a-z0-9])?$/;
    
    // Reserved subdomains
    const reservedSubdomains = [
      'www', 'api', 'admin', 'app', 'mail', 'ftp', 'blog', 'shop',
      'support', 'help', 'docs', 'status', 'cdn', 'assets', 'static',
      'media', 'images', 'files', 'download', 'upload', 'secure',
      'ssl', 'vpn', 'test', 'staging', 'dev', 'demo', 'beta'
    ];

    return (
      subdomainRegex.test(subdomain) &&
      !reservedSubdomains.includes(subdomain.toLowerCase())
    );
  }

  /**
   * Check if subdomain is available
   */
  static async isSubdomainAvailable(subdomain: string): Promise<boolean> {
    if (!this.isValidSubdomain(subdomain)) {
      return false;
    }

    const tenant = await this.getTenantBySubdomain(subdomain);
    return !tenant;
  }

  /**
   * Generate available subdomain suggestion
   */
  static async generateSubdomainSuggestion(baseName: string): Promise<string> {
    // Clean base name
    let cleanName = baseName
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '');

    if (cleanName.length < 3) {
      cleanName = cleanName + 'app';
    }

    if (cleanName.length > 20) {
      cleanName = cleanName.substring(0, 20);
    }

    // Try the clean name first
    if (await this.isSubdomainAvailable(cleanName)) {
      return cleanName;
    }

    // Try with numbers
    for (let i = 1; i <= 999; i++) {
      const suggestion = `${cleanName}${i}`;
      if (await this.isSubdomainAvailable(suggestion)) {
        return suggestion;
      }
    }

    // Fallback to random ID
    return `tenant-${createId().substring(0, 8)}`;
  }
}
