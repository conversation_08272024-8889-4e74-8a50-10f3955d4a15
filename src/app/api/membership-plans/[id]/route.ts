import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { MembershipPlanService } from "@/lib/services/membership-plan.service";

/**
 * API Routes untuk Individual Membership Plan Operations
 * 
 * Handle GET, PUT, DELETE untuk single membership plan berdasarkan ID.
 * Mengikuti pattern yang sama dengan classes/[id] dan class-categories/[id].
 */

// Validation schemas
const paramsSchema = z.object({
  id: z.string().min(1, "ID is required"),
});

const querySchema = z.object({
  tenantId: z.string().transform((val) => parseInt(val, 10)),
});

const updateMembershipPlanSchema = z.object({
  name: z.string().min(1, "Name is required").max(255, "Name too long").optional(),
  description: z.string().max(255, "Description too long").optional().or(z.literal("")),
  price: z.number().int().min(0, "Price must be non-negative").optional().or(z.literal(null)),
  currency: z.string().max(255, "Currency too long").optional().or(z.literal("")),
  duration_value: z.number().int().positive("Duration must be positive").optional().or(z.literal(null)),
  duration_unit: z.string().max(50, "Duration unit too long").optional().or(z.literal("")),
  is_active: z.boolean().optional(),
}).transform((data) => {
  // Convert empty strings to undefined for optional fields
  return {
    ...data,
    description: data.description === "" ? undefined : data.description,
    currency: data.currency === "" ? undefined : data.currency,
    duration_unit: data.duration_unit === "" ? undefined : data.duration_unit,
  };
});

/**
 * GET /api/membership-plans/[id]
 * Get a single membership plan by ID
 * 
 * Params:
 * - id: membership plan ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Validate params
    const resolvedParams = await params;
    const validatedParams = paramsSchema.parse(resolvedParams);

    // Get plan by ID
    const planResult = await MembershipPlanService.getById(validatedParams.id);

    if (!planResult) {
      return NextResponse.json(
        { success: false, error: "Membership plan not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: planResult,
    });
  } catch (error) {
    console.error("GET /api/membership-plans/[id] error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: "Invalid parameters", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/membership-plans/[id]
 * Update a membership plan
 * 
 * Params:
 * - id: membership plan ID
 * Query:
 * - tenantId: untuk security (pastikan plan belong to tenant)
 * Body:
 * - field-field yang mau diupdate
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Validate params
    const resolvedParams = await params;
    const validatedParams = paramsSchema.parse(resolvedParams);
    
    // Validate query parameters
    const { searchParams } = new URL(request.url);
    const validatedQuery = querySchema.parse({
      tenantId: searchParams.get("tenantId"),
    });

    // Validate request body
    const body = await request.json();
    const validatedData = updateMembershipPlanSchema.parse(body);

    // Update plan
    const updatedPlan = await MembershipPlanService.update(
      validatedParams.id,
      validatedData
    );

    if (!updatedPlan) {
      return NextResponse.json(
        { success: false, error: "Membership plan not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: updatedPlan,
      message: "Membership plan updated successfully",
    });
  } catch (error) {
    console.error("PUT /api/membership-plans/[id] error:", error);
    
    if (error instanceof z.ZodError) {
      console.log("Validation error details:", error.errors);
      return NextResponse.json(
        { success: false, error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/membership-plans/[id]
 * Delete a membership plan
 * 
 * Params:
 * - id: membership plan ID
 * Query:
 * - tenantId: untuk security (pastikan plan belong to tenant)
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Validate params
    const resolvedParams = await params;
    const validatedParams = paramsSchema.parse(resolvedParams);
    
    // Validate query parameters
    const { searchParams } = new URL(request.url);
    const validatedQuery = querySchema.parse({
      tenantId: searchParams.get("tenantId"),
    });

    // Delete plan
    await MembershipPlanService.delete(validatedParams.id);

    return NextResponse.json({
      success: true,
      message: "Membership plan deleted successfully",
    });
  } catch (error) {
    console.error("DELETE /api/membership-plans/[id] error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { success: false, error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error && error.message.includes("not found")) {
      return NextResponse.json(
        { success: false, error: "Membership plan not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { success: false, error: "Internal server error" },
      { status: 500 }
    );
  }
}
