import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { Package } from '@/lib/db/schema';

// Package API functions
const packageApi = {
  getAll: async (options?: { filters?: any }) => {
    const params = new URLSearchParams();
    if (options?.filters) {
      Object.entries(options.filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          params.append(key, String(value));
        }
      });
    }

    const response = await fetch(`/api/packages?${params.toString()}`);
    if (!response.ok) {
      throw new Error('Failed to fetch packages');
    }

    const result = await response.json();
    return result.data || [];
  },

  getById: async (id: string) => {
    const response = await fetch(`/api/packages/${id}`);
    if (!response.ok) {
      throw new Error('Failed to fetch package');
    }

    const result = await response.json();
    return result.data;
  },

  create: async (data: any) => {
    const response = await fetch('/api/packages', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to create package');
    }

    const result = await response.json();
    return result.data;
  },

  update: async ({ id, ...data }: { id: string; [key: string]: any }) => {
    const response = await fetch(`/api/packages/${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to update package');
    }

    const result = await response.json();
    return result.data;
  },

  delete: async (id: string) => {
    const response = await fetch(`/api/packages/${id}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to delete package');
    }

    const result = await response.json();
    return result.data;
  },

  bulkOperation: async (data: { ids: string[]; action: string }) => {
    const response = await fetch('/api/packages', {
      method: 'DELETE',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to perform bulk operation');
    }

    const result = await response.json();
    return result.data;
  },
};

// Package filters interface
interface PackageFilters {
  search?: string;
  tenantId?: number;
  isActive?: boolean;
  is_private?: boolean;
}

// Custom mutation hooks with proper cache invalidation
export function useCreatePackage() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: packageApi.create,
    onSuccess: () => {
      // Invalidate all package related queries
      queryClient.invalidateQueries({ queryKey: ['packages'] });

      // Specifically invalidate the filtered queries used by the page
      queryClient.invalidateQueries({ queryKey: ['packages', 'filtered'] });
      queryClient.invalidateQueries({ queryKey: ['packages', 'by-tenant'] });
      queryClient.invalidateQueries({ queryKey: ['packages', 'active'] });
      queryClient.invalidateQueries({ queryKey: ['packages', 'stats'] });
    },
  });
}

export function useUpdatePackage() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: packageApi.update,
    onSuccess: (updatedPackage) => {
      // Update cache with updated package
      queryClient.setQueryData(['packages', 'details', updatedPackage.id], updatedPackage);

      // Invalidate all package related queries
      queryClient.invalidateQueries({ queryKey: ['packages'] });

      // Specifically invalidate the filtered queries used by the page
      queryClient.invalidateQueries({ queryKey: ['packages', 'filtered'] });
      queryClient.invalidateQueries({ queryKey: ['packages', 'by-tenant'] });
      queryClient.invalidateQueries({ queryKey: ['packages', 'active'] });
      queryClient.invalidateQueries({ queryKey: ['packages', 'stats'] });
    },
  });
}

export function useDeletePackage() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: packageApi.delete,
    onSuccess: () => {
      // Invalidate all package related queries
      queryClient.invalidateQueries({ queryKey: ['packages'] });

      // Specifically invalidate the filtered queries used by the page
      queryClient.invalidateQueries({ queryKey: ['packages', 'filtered'] });
      queryClient.invalidateQueries({ queryKey: ['packages', 'by-tenant'] });
      queryClient.invalidateQueries({ queryKey: ['packages', 'active'] });
      queryClient.invalidateQueries({ queryKey: ['packages', 'stats'] });
    },
  });
}

// Custom query hooks for specific use cases
export function usePackagesByTenant(tenantId: number) {
  return useQuery({
    queryKey: ['packages', 'by-tenant', tenantId],
    queryFn: async (): Promise<Package[]> => {
      return packageApi.getAll({ filters: { tenantId } });
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function usePackageList(filters: PackageFilters = {}, tenantId: number = 1) {
  return useQuery({
    queryKey: ['packages', 'filtered', { ...filters, tenantId }],
    queryFn: async (): Promise<Package[]> => {
      const allFilters = { ...filters, tenantId };
      return packageApi.getAll({ filters: allFilters });
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function useActivePackagesByTenant(tenantId: number) {
  return useQuery({
    queryKey: ['packages', 'active', 'by-tenant', tenantId],
    queryFn: async (): Promise<Package[]> => {
      return packageApi.getAll({ filters: { tenantId, isActive: true } });
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Export other useful hooks
export function usePackage(id: string) {
  return useQuery({
    queryKey: ['packages', 'details', id],
    queryFn: () => packageApi.getById(id),
    staleTime: 5 * 60 * 1000,
  });
}

export function usePackages() {
  return useQuery({
    queryKey: ['packages', 'all'],
    queryFn: () => packageApi.getAll(),
    staleTime: 5 * 60 * 1000,
  });
}

// Bulk operation hook
export function useBulkPackageOperation() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: packageApi.bulkOperation,
    onSuccess: () => {
      // Invalidate all package related queries
      queryClient.invalidateQueries({ queryKey: ['packages'] });
    },
  });
}

// Export types for external use
export type { Package, PackageFilters };
