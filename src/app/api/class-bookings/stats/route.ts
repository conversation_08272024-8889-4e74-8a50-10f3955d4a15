import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { z } from "zod";
import { ClassBookingService } from "@/lib/services/class-booking.service";

// Validation schema for query parameters
const statsQuerySchema = z.object({
  tenantId: z.string().optional(),
});

/**
 * GET /api/class-bookings/stats
 * Get class booking statistics
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());

    console.log(`📅 [GET /api/class-bookings/stats] Query params:`, queryParams);

    const validatedQuery = statsQuerySchema.parse(queryParams);

    // Check permissions - user harus bisa read bookings atau reports
    if (session.user.role !== "admin" && 
        !session.user.permissions?.includes("bookings.read") &&
        !session.user.permissions?.includes("reports.read")) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      );
    }

    const tenantId = validatedQuery.tenantId ? parseInt(validatedQuery.tenantId) : undefined;

    // Tenant isolation - pastikan user hanya bisa akses stats dari tenant mereka
    if (session.user.role !== "admin" && tenantId && tenantId !== session.user.tenantId) {
      return NextResponse.json(
        { error: "Access denied" },
        { status: 403 }
      );
    }

    // Jika bukan admin dan tidak ada tenantId, gunakan tenantId dari session
    const finalTenantId = session.user.role === "admin" ? tenantId : (tenantId || session.user.tenantId);

    console.log(`📅 [GET /api/class-bookings/stats] Fetching stats for tenant:`, finalTenantId);

    const stats = await ClassBookingService.getStats(finalTenantId);

    return NextResponse.json({
      success: true,
      data: stats,
      meta: {
        tenantId: finalTenantId,
        generatedAt: new Date().toISOString(),
      }
    });
  } catch (error) {
    console.error("📅 [GET /api/class-bookings/stats] Error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid query parameters", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to fetch class booking statistics" },
      { status: 500 }
    );
  }
}
