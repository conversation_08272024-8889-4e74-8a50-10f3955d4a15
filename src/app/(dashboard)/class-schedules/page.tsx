import { Suspense } from "react";
import { auth } from "@/lib/auth/config";
import { redirect } from "next/navigation";
import { ClassSchedulesManagement } from "@/components/class-schedules/class-schedules-management";
import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { PermissionGuard } from "@/components/rbac/permission-guard";

/**
 * Class Schedules Page
 * 
 * Halaman untuk manage class schedules dengan RBAC protection.
 * Mengikuti pattern yang sama dengan pages lain yang sudah ada.
 * 
 * Features:
 * - Authentication check
 * - RBAC permission guard
 * - Tenant isolation
 * - Loading states
 * - Error boundaries
 */

// Loading component untuk Suspense
function ClassSchedulesLoading() {
  return (
    <div className="space-y-6">
      {/* Header Skeleton */}
      <div className="flex justify-between items-center">
        <div className="space-y-2">
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-4 w-64" />
        </div>
        <Skeleton className="h-10 w-32" />
      </div>

      {/* Filters Skeleton */}
      <Card>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="space-y-2">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-10 w-full" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Schedules Grid Skeleton */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {[...Array(6)].map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="space-y-4">
                <div className="flex justify-between items-start">
                  <div className="space-y-2">
                    <Skeleton className="h-5 w-32" />
                    <Skeleton className="h-4 w-24" />
                  </div>
                  <Skeleton className="h-4 w-4 rounded-full" />
                </div>
                <div className="space-y-2">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                </div>
                <div className="flex gap-2">
                  <Skeleton className="h-6 w-16" />
                  <Skeleton className="h-6 w-12" />
                </div>
                <div className="flex justify-end gap-2">
                  <Skeleton className="h-8 w-8" />
                  <Skeleton className="h-8 w-8" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}

export default async function ClassSchedulesPage() {
  // Check authentication
  const session = await auth();
  if (!session?.user) {
    redirect("/auth/signin");
  }

  // Get tenant ID from session atau environment
  // Untuk demo, kita hardcode tenant ID = 1
  // Di production, ini harus diambil dari session user atau context
  const tenantId = 1;

  return (
    <PermissionGuard permission="classes.manage">
      <div className="container mx-auto py-6">
        <Suspense fallback={<ClassSchedulesLoading />}>
          <ClassSchedulesManagement tenantId={tenantId} />
        </Suspense>
      </div>
    </PermissionGuard>
  );
}
