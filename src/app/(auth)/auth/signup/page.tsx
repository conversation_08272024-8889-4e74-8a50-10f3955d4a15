import { Metadata } from "next";
import { CustomerSignupForm } from "@/components/auth/customer-signup-form";

export const metadata: Metadata = {
  title: "Sign Up - Your SaaS App",
  description: "Create your account",
};

export default function SignUpPage() {
  return (
    <div className="container relative min-h-screen flex-col items-center justify-center grid lg:max-w-none lg:grid-cols-2 lg:px-0">
      <div className="relative hidden h-full flex-col bg-muted p-10 text-white lg:flex dark:border-r">
        <div className="absolute inset-0 bg-gradient-to-br from-purple-600 to-blue-700" />
        <div className="relative z-20 flex items-center text-lg font-medium">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="mr-2 h-6 w-6"
          >
            <path d="M15 6v12a3 3 0 1 0 3-3H6a3 3 0 1 0 3 3V6a3 3 0 1 0-3 3h12a3 3 0 1 0-3-3" />
          </svg>
          Your SaaS App
        </div>
        <div className="relative z-20 mt-auto">
          <blockquote className="space-y-2">
            <p className="text-lg">
              &ldquo;Setting up my SaaS was incredibly fast with this template. 
              The authentication system works flawlessly and the code is clean and well-documented.&rdquo;
            </p>
            <footer className="text-sm">Alex Chen, Founder of TechStart</footer>
          </blockquote>
        </div>
      </div>
      <div className="lg:p-8">
        <div className="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[350px]">
          <CustomerSignupForm tenantId={1} redirectPath="/dashboard" />
        </div>
      </div>
    </div>
  );
}
