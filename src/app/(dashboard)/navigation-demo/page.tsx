import { getCurrentUser } from "@/lib/auth/utils";
import { redirect } from "next/navigation";
import { DashboardLayoutRedesigned } from "@/components/dashboard/layout-redesigned";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  Palette, 
  Smartphone, 
  Accessibility, 
  Zap, 
  Shield, 
  Users,
  BarChart3,
  CheckCircle2
} from "lucide-react";

export default async function NavigationDemoPage() {
  const user = await getCurrentUser();
  
  if (!user) {
    redirect("/auth/signin");
  }

  const features = [
    {
      icon: Palette,
      title: "Modern Design System",
      description: "Clean, professional design following FAANG-level UI/UX standards with consistent spacing and typography.",
      status: "Implemented"
    },
    {
      icon: Smartphone,
      title: "Responsive Design",
      description: "Fully responsive navigation that works seamlessly on desktop, tablet, and mobile devices.",
      status: "Implemented"
    },
    {
      icon: Accessibility,
      title: "Accessibility Features",
      description: "ARIA labels, keyboard navigation, screen reader support, and focus management.",
      status: "Implemented"
    },
    {
      icon: Zap,
      title: "Smooth Animations",
      description: "Framer Motion animations for collapsible sections, hover states, and transitions.",
      status: "Implemented"
    },
    {
      icon: Shield,
      title: "RBAC Integration",
      description: "Maintains existing Role-Based Access Control functionality for menu items.",
      status: "Implemented"
    },
    {
      icon: Users,
      title: "User Context",
      description: "Displays user information and role badges in the navigation footer.",
      status: "Implemented"
    }
  ];

  const designPrinciples = [
    "Visual hierarchy with proper grouping and nesting",
    "Consistent color scheme and spacing",
    "Loading states and error handling",
    "Collapsible/expandable sections",
    "Active state indicators",
    "Hover and focus states",
    "Mobile-first responsive design",
    "Keyboard navigation support"
  ];

  return (
    <DashboardLayoutRedesigned user={user}>
      <div className="space-y-8">
        {/* Header */}
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <Badge variant="secondary" className="bg-primary/10 text-primary">
              FAANG-Level Design
            </Badge>
            <Badge variant="outline">
              Navigation Redesign
            </Badge>
          </div>
          <div>
            <h1 className="text-4xl font-bold tracking-tight">
              Enhanced Navigation System
            </h1>
            <p className="text-xl text-muted-foreground mt-2">
              Professional, accessible, and responsive navigation following industry best practices
            </p>
          </div>
        </div>

        {/* Features Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {features.map((feature, index) => (
            <Card key={index} className="relative overflow-hidden">
              <CardHeader className="pb-3">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-primary/10 rounded-lg">
                    <feature.icon className="h-5 w-5 text-primary" />
                  </div>
                  <div className="flex-1">
                    <CardTitle className="text-lg">{feature.title}</CardTitle>
                    <Badge variant="secondary" className="mt-1 text-xs">
                      <CheckCircle2 className="h-3 w-3 mr-1" />
                      {feature.status}
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <CardDescription className="text-sm leading-relaxed">
                  {feature.description}
                </CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Design Principles */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5" />
              <span>Design Principles Implemented</span>
            </CardTitle>
            <CardDescription>
              Key design principles and best practices applied to the navigation system
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-3 md:grid-cols-2">
              {designPrinciples.map((principle, index) => (
                <div key={index} className="flex items-center space-x-3">
                  <CheckCircle2 className="h-4 w-4 text-green-600 flex-shrink-0" />
                  <span className="text-sm">{principle}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Technical Implementation */}
        <Card>
          <CardHeader>
            <CardTitle>Technical Implementation</CardTitle>
            <CardDescription>
              Modern React patterns and technologies used in the navigation redesign
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <h4 className="font-medium">Frontend Technologies</h4>
                <div className="flex flex-wrap gap-2">
                  <Badge variant="outline">React 18</Badge>
                  <Badge variant="outline">TypeScript</Badge>
                  <Badge variant="outline">Framer Motion</Badge>
                  <Badge variant="outline">Tailwind CSS</Badge>
                  <Badge variant="outline">Radix UI</Badge>
                </div>
              </div>
              <div className="space-y-2">
                <h4 className="font-medium">Features</h4>
                <div className="flex flex-wrap gap-2">
                  <Badge variant="outline">RBAC Integration</Badge>
                  <Badge variant="outline">Responsive Design</Badge>
                  <Badge variant="outline">Accessibility</Badge>
                  <Badge variant="outline">Animations</Badge>
                </div>
              </div>
            </div>
            
            <div className="pt-4 border-t">
              <h4 className="font-medium mb-3">Key Components</h4>
              <div className="space-y-2 text-sm text-muted-foreground">
                <p><code className="bg-muted px-2 py-1 rounded">DashboardNavRedesigned</code> - Main navigation component</p>
                <p><code className="bg-muted px-2 py-1 rounded">DashboardHeaderRedesigned</code> - Enhanced header with search and user menu</p>
                <p><code className="bg-muted px-2 py-1 rounded">DashboardLayoutRedesigned</code> - Complete layout wrapper</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Usage Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>How to Use</CardTitle>
            <CardDescription>
              Instructions for implementing the new navigation system
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div>
                <h4 className="font-medium">1. Replace Layout Component</h4>
                <p className="text-sm text-muted-foreground">
                  Update your dashboard layout to use <code className="bg-muted px-1 rounded">DashboardLayoutRedesigned</code>
                </p>
              </div>
              <div>
                <h4 className="font-medium">2. Navigation Structure</h4>
                <p className="text-sm text-muted-foreground">
                  The navigation items are reorganized into logical groups with better hierarchy
                </p>
              </div>
              <div>
                <h4 className="font-medium">3. RBAC Compatibility</h4>
                <p className="text-sm text-muted-foreground">
                  All existing permission checks are maintained and enhanced
                </p>
              </div>
            </div>
            
            <div className="pt-4 border-t">
              <Button className="w-full sm:w-auto">
                View Implementation Guide
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayoutRedesigned>
  );
}
