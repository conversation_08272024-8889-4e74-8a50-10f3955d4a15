"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import { Search, X, Key, Users, Check, ChevronDown, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { useGroupedPermissions } from "@/lib/hooks/queries/use-permission-queries";
import { Permission } from "@/lib/db/schema";

interface PermissionSelectorProps {
  selectedPermissions: Array<{
    id: string;
    module: string;
    action: string;
    display_name: string;
    assigned_at: string;
  }>;
  onSelectionChange: (permissions: Array<{
    id: string;
    module: string;
    action: string;
    display_name: string;
    assigned_at: string;
  }>) => void;
  maxSelections?: number;
  placeholder?: string;
}

export function PermissionSelector({
  selectedPermissions,
  onSelectionChange,
  maxSelections = 50,
  placeholder = "Search permissions..."
}: PermissionSelectorProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [isOpen, setIsOpen] = useState(false);
  const [expandedModules, setExpandedModules] = useState<Set<string>>(new Set());

  // Fetch grouped permissions
  const { data: groupedPermissions = {}, isLoading, error } = useGroupedPermissions();

  // Filter permissions berdasarkan search
  const filteredGroupedPermissions = React.useMemo(() => {
    if (!searchQuery.trim()) {
      return groupedPermissions;
    }

    const filtered: Record<string, Permission[]> = {};
    const query = searchQuery.toLowerCase();

    Object.entries(groupedPermissions).forEach(([module, permissions]) => {
      const matchingPermissions = permissions.filter((permission: Permission) =>
        permission.display_name.toLowerCase().includes(query) ||
        permission.action.toLowerCase().includes(query) ||
        permission.module.toLowerCase().includes(query)
      );

      if (matchingPermissions.length > 0) {
        filtered[module] = matchingPermissions;
      }
    });

    return filtered;
  }, [groupedPermissions, searchQuery]);

  // Get available permissions (not selected)
  const getAvailablePermissions = (permissions: Permission[]) => {
    return permissions.filter((permission: Permission) =>
      !selectedPermissions.some(selected => selected.id === permission.id)
    );
  };

  // Handle permission selection
  const handleSelectPermission = (permission: Permission) => {
    if (selectedPermissions.length >= maxSelections) {
      return;
    }

    const newPermission = {
      id: permission.id,
      module: permission.module,
      action: permission.action,
      display_name: permission.display_name,
      assigned_at: new Date().toISOString(),
    };

    onSelectionChange([...selectedPermissions, newPermission]);
  };

  // Handle remove permission
  const handleRemovePermission = (permissionId: string) => {
    onSelectionChange(selectedPermissions.filter(perm => perm.id !== permissionId));
  };

  // Toggle module expansion
  const toggleModule = (module: string) => {
    const newExpanded = new Set(expandedModules);
    if (newExpanded.has(module)) {
      newExpanded.delete(module);
    } else {
      newExpanded.add(module);
    }
    setExpandedModules(newExpanded);
  };

  // Handle select all permissions in a module
  const handleSelectAllInModule = (permissions: Permission[]) => {
    const availablePermissions = getAvailablePermissions(permissions);
    const remainingSlots = maxSelections - selectedPermissions.length;
    const permissionsToAdd = availablePermissions.slice(0, remainingSlots).map((permission: Permission) => ({
      id: permission.id,
      module: permission.module,
      action: permission.action,
      display_name: permission.display_name,
      assigned_at: new Date().toISOString(),
    }));

    onSelectionChange([...selectedPermissions, ...permissionsToAdd]);
  };

  return (
    <div className="space-y-4">
      {/* Header dengan info */}
      <div className="flex items-center justify-between">
        <Label className="flex items-center gap-2">
          <Key className="h-4 w-4" />
          Assigned Permissions
          {selectedPermissions.length > 0 && (
            <Badge variant="secondary" className="text-xs">
              {selectedPermissions.length} selected
            </Badge>
          )}
        </Label>
      </div>

      {/* Search input */}
      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
        <Input
          type="text"
          placeholder={placeholder}
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          onFocus={() => setIsOpen(true)}
          className="pl-10"
        />
      </div>

      {/* Selected permissions */}
      {selectedPermissions.length > 0 && (
        <div className="space-y-2">
          <Label className="text-sm text-gray-600">Selected Permissions:</Label>
          <div className="flex flex-wrap gap-2">
            {selectedPermissions.map((permission) => (
              <motion.div
                key={permission.id}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                className="flex items-center gap-1"
              >
                <Badge variant="default" className="flex items-center gap-1">
                  <Check className="h-3 w-3" />
                  {permission.display_name}
                  <span className="text-xs opacity-75">({permission.module})</span>
                  <button
                    type="button"
                    onClick={() => handleRemovePermission(permission.id)}
                    className="ml-1 hover:bg-red-500 hover:text-white rounded-full p-0.5 transition-colors"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </Badge>
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {/* Available permissions dropdown */}
      {isOpen && (
        <Card className="border shadow-lg">
          <CardContent className="p-2 max-h-80 overflow-y-auto">
            {isLoading ? (
              <div className="flex items-center justify-center py-4">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                <span className="ml-2 text-sm text-gray-600">Loading permissions...</span>
              </div>
            ) : Object.keys(filteredGroupedPermissions).length === 0 ? (
              <div className="text-center py-4 text-sm text-gray-500">
                {searchQuery ? "No permissions found matching your search" : "No permissions available"}
              </div>
            ) : (
              <div className="space-y-2">
                {Object.entries(filteredGroupedPermissions).map(([module, permissions]) => {
                  const availablePermissions = getAvailablePermissions(permissions);
                  const isExpanded = expandedModules.has(module);
                  
                  return (
                    <Collapsible key={module} open={isExpanded} onOpenChange={() => toggleModule(module)}>
                      <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <CollapsibleTrigger className="flex items-center gap-2 text-sm font-medium">
                          {isExpanded ? (
                            <ChevronDown className="h-4 w-4" />
                          ) : (
                            <ChevronRight className="h-4 w-4" />
                          )}
                          {module.charAt(0).toUpperCase() + module.slice(1)}
                          <Badge variant="outline" className="text-xs">
                            {availablePermissions.length} available
                          </Badge>
                        </CollapsibleTrigger>
                        
                        {availablePermissions.length > 0 && selectedPermissions.length < maxSelections && (
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => handleSelectAllInModule(permissions)}
                            className="text-xs h-6"
                          >
                            Select All
                          </Button>
                        )}
                      </div>
                      
                      <CollapsibleContent className="space-y-1 ml-4">
                        {availablePermissions.map((permission: Permission) => (
                          <button
                            key={permission.id}
                            type="button"
                            onClick={() => handleSelectPermission(permission)}
                            disabled={selectedPermissions.length >= maxSelections}
                            className="w-full text-left p-2 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                          >
                            <div className="flex items-center justify-between">
                              <div>
                                <div className="font-medium text-sm">{permission.display_name}</div>
                                <div className="text-xs text-gray-500">
                                  {permission.action} • {permission.module}
                                </div>
                                {permission.description && (
                                  <div className="text-xs text-gray-400 mt-1">{permission.description}</div>
                                )}
                              </div>
                              <Key className="h-4 w-4 text-gray-400" />
                            </div>
                          </button>
                        ))}
                      </CollapsibleContent>
                    </Collapsible>
                  );
                })}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Click outside to close */}
      {isOpen && (
        <div
          className="fixed inset-0 z-10"
          onClick={() => setIsOpen(false)}
        />
      )}

      {/* Info text */}
      <p className="text-xs text-gray-500">
        Select permissions to assign to this role. 
        {maxSelections && ` Maximum ${maxSelections} permissions allowed.`}
        {selectedPermissions.length >= maxSelections && (
          <span className="text-orange-600 font-medium"> Limit reached!</span>
        )}
      </p>
    </div>
  );
}
