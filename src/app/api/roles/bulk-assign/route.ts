import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { RoleService } from "@/lib/services/role.service";
import { withRBAC } from "@/lib/middleware/rbac-middleware";

/**
 * API Routes untuk Bulk Role Assignment
 */

const bulkAssignRoleSchema = z.object({
  assignments: z.array(z.object({
    userId: z.string().min(1, "User ID is required"),
    roleId: z.string().min(1, "Role ID is required"),
    tenantId: z.number().int().positive("Tenant ID must be positive").optional().nullable(),
    assignedBy: z.string().optional(),
    expiresAt: z.string().optional().transform((val) => {
      if (!val) return undefined;
      return new Date(val);
    }),
  })).min(1, "At least one assignment is required"),
});

/**
 * POST /api/roles/bulk-assign
 * Bulk assign roles to users
 */
export const POST = withRBAC(
  async (request: NextRequest) => {
    try {
      const body = await request.json();
      
      // Validate request body
      const validatedData = bulkAssignRoleSchema.parse(body);

      // Process each assignment
      const results = [];
      const errors = [];

      for (const assignment of validatedData.assignments) {
        try {
          const result = await RoleService.assignRole(assignment);
          results.push(result);
        } catch (error) {
          console.error(`Error assigning role to user ${assignment.userId}:`, error);
          errors.push({
            userId: assignment.userId,
            roleId: assignment.roleId,
            error: error instanceof Error ? error.message : "Unknown error",
          });
        }
      }

      // Return results with any errors
      return NextResponse.json({
        success: true,
        data: results,
        errors: errors.length > 0 ? errors : undefined,
        message: `Successfully assigned ${results.length} roles${errors.length > 0 ? ` with ${errors.length} errors` : ""}`,
      }, { status: 201 });
    } catch (error) {
      console.error("POST /api/roles/bulk-assign error:", error);
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { success: false, error: "Validation failed", details: error.errors },
          { status: 400 }
        );
      }

      return NextResponse.json(
        { success: false, error: "Internal server error" },
        { status: 500 }
      );
    }
  },
  { module: "roles", action: "assign" }
);
