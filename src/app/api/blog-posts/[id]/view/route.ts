import { NextRequest, NextResponse } from "next/server";
import { BlogService } from "@/lib/services/blog.service";

interface RouteParams {
  params: Promise<{ id: string }>;
}

// POST /api/blog-posts/[id]/view - Increment view count
export async function POST(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    const { id } = await params;

    if (!id) {
      return NextResponse.json(
        { error: "Blog post ID is required" },
        { status: 400 }
      );
    }

    await BlogService.incrementViewCount(id);

    return NextResponse.json({
      success: true,
      message: "View count incremented",
    });
  } catch (error) {
    console.error("Error incrementing view count:", error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : "Failed to increment view count",
        success: false 
      },
      { status: 500 }
    );
  }
}
