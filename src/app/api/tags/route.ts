import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { z } from "zod";
import { tagService } from "@/lib/services/tag.service";

// Validation schemas
const createTagSchema = z.object({
  tenantId: z.number().int().positive(),
  name: z.string().min(1, "Name is required").max(255, "Name must be less than 255 characters").optional(),
  description: z.string().max(255, "Description must be less than 255 characters").optional(),
  customColor: z.string().max(255, "Color must be less than 255 characters").optional(),
  customerId: z.string().optional(),
});

const querySchema = z.object({
  search: z.string().optional(),
  customerId: z.string().optional(),
});

/**
 * GET /api/tags
 * Get tags for the current tenant with optional filtering
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const tenantIdParam = searchParams.get("tenantId");
    const queryParams = Object.fromEntries(searchParams.entries());

    // For now, require tenantId in query params since session.user doesn't have tenantId
    const tenantId = tenantIdParam ? parseInt(tenantIdParam) : null;

    if (!tenantId) {
      return NextResponse.json({ error: "Tenant ID required in query parameters" }, { status: 400 });
    }
    
    const validatedQuery = querySchema.parse(queryParams);

    // Build filters
    const filters = {
      search: validatedQuery.search,
      customerId: validatedQuery.customerId,
    };

    console.log(`🏷️ [GET /api/tags] Fetching tags for tenant ${tenantId}:`, filters);

    const tags = await tagService.getByTenant(tenantId, filters);

    return NextResponse.json({
      success: true,
      data: tags,
      meta: {
        total: tags.length,
        filters,
      }
    });
  } catch (error) {
    console.error("🏷️ [GET /api/tags] Error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid query parameters", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to fetch tags" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/tags
 * Create a new tag
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = createTagSchema.parse(body);

    // Get tenantId from validated data
    const tenantId = validatedData.tenantId;

    console.log(`🏷️ [POST /api/tags] Creating tag for tenant ${tenantId}:`, validatedData);

    const tag = await tagService.create(tenantId, validatedData);

    return NextResponse.json({
      success: true,
      data: tag,
      message: "Tag created successfully"
    }, { status: 201 });
  } catch (error) {
    console.error("🏷️ [POST /api/tags] Error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid tag data", details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error && error.message.includes("already exists")) {
      return NextResponse.json(
        { error: error.message },
        { status: 409 }
      );
    }

    return NextResponse.json(
      { error: "Failed to create tag" },
      { status: 500 }
    );
  }
}
