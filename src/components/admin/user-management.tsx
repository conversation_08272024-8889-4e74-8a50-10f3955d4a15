"use client";

import { useEffect, useState } from "react";
import { format } from "date-fns";
import { MoreHorizontal, UserPlus, Search, Filter, X } from "lucide-react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { ComponentLoading } from "@/components/ui/loading";
import { formatCredits } from "@/lib/stripe/config";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useRoles } from "@/lib/hooks/queries/use-role-queries";
import { useSession } from "next-auth/react";

interface User {
  id: string;
  email: string;
  name?: string;
  image?: string;
  role: string; // ✅ Changed: Support dynamic roles instead of hard-coded
  roleName?: string; // ✅ Added: Display name for role
  credits: number;
  isActive: boolean;
  emailVerified?: Date;
  createdAt: Date;
  organizationId?: string;
  organizationName?: string;
  tenantId?: number; // ✅ Added: For tenant isolation
}

export function UserManagement() {
  console.log("🚀 UserManagement component rendered!");
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [roleFilter, setRoleFilter] = useState<string>("all");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [newUserData, setNewUserData] = useState({
    email: "",
    name: "",
    role: "",
    password: "",
  });

  // ✅ Enhanced: Security state management
  const [csrfToken, setCsrfToken] = useState<string>("");
  const [securityError, setSecurityError] = useState<string>("");
  const [rateLimitInfo, setRateLimitInfo] = useState<{
    remaining: number;
    resetTime: number;
  } | null>(null);

  // ✅ Added: Fetch roles from Role Management system
  const { data: session } = useSession();
  const { data: rolesData, isLoading: isLoadingRoles } = useRoles();
  const roles = rolesData?.roles || [];

  useEffect(() => {
    console.log("🔍 UserManagement component mounted, initializing security...");
    initializeSecurity();
  }, []);

  // ✅ Enhanced: Initialize security (fetch CSRF token and users)
  const initializeSecurity = async () => {
    try {
      // Fetch CSRF token first
      await fetchCSRFToken();
      // Then fetch users
      await fetchUsers();
    } catch (error) {
      console.error("Failed to initialize security:", error);
      setSecurityError("Failed to initialize security. Please refresh the page.");
    }
  };

  // ✅ Enhanced: Fetch CSRF token for secure operations
  const fetchCSRFToken = async () => {
    try {
      const response = await fetch("/api/auth/csrf", {
        method: "GET",
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (response.ok) {
        const data = await response.json();
        setCsrfToken(data.csrfToken);
        console.log("✅ CSRF token fetched successfully");
      } else {
        throw new Error("Failed to fetch CSRF token");
      }
    } catch (error) {
      console.error("❌ Failed to fetch CSRF token:", error);
      setSecurityError("Security initialization failed");
    }
  };

  // ✅ Enhanced: Secure user fetching with enterprise-grade error handling
  const fetchUsers = async () => {
    try {
      console.log("🔒 SECURE: Fetching users from /api/admin/users...");
      const response = await fetch("/api/admin/users", {
        method: "GET",
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
          "X-Requested-With": "XMLHttpRequest", // CSRF protection
        },
      });

      console.log("📡 Response status:", response.status);

      // Extract rate limit information
      const rateLimitRemaining = response.headers.get("X-RateLimit-Remaining");
      const rateLimitReset = response.headers.get("X-RateLimit-Reset");

      if (rateLimitRemaining && rateLimitReset) {
        setRateLimitInfo({
          remaining: parseInt(rateLimitRemaining),
          resetTime: parseInt(rateLimitReset)
        });
      }

      if (response.ok) {
        const result = await response.json();
        console.log("✅ SECURE: Users data received:", result);

        // Handle new API response format
        const usersData = result.success ? result.data : result;

        setUsers(usersData.map((user: any) => ({
          ...user,
          createdAt: new Date(user.createdAt),
          emailVerified: user.emailVerified ? new Date(user.emailVerified) : undefined,
        })));

        setSecurityError(""); // Clear any previous errors
      } else {
        // Enhanced error handling for different response types
        const contentType = response.headers.get("content-type");

        if (contentType && contentType.includes("application/json")) {
          const errorData = await response.json();
          console.error("❌ SECURE API Error:", errorData);

          // Handle specific error codes
          switch (errorData.code) {
            case "RATE_LIMIT_EXCEEDED":
              setSecurityError(`Rate limit exceeded. Try again in ${errorData.retryAfter} seconds.`);
              break;
            case "INSUFFICIENT_PERMISSIONS":
              setSecurityError("You don't have permission to view users.");
              break;
            case "SESSION_EXPIRED":
              setSecurityError("Your session has expired. Please sign in again.");
              break;
            default:
              setSecurityError(errorData.error || "Failed to fetch users");
          }
        } else {
          const errorText = await response.text();
          console.error("❌ Non-JSON Error Response:", errorText.substring(0, 200));
          setSecurityError("Authentication or server error occurred");
        }
      }
    } catch (error) {
      console.error("💥 SECURE: Error fetching users:", error);
      setSecurityError("Network error occurred. Please check your connection.");
    } finally {
      setLoading(false);
    }
  };

  // ✅ Enhanced: Secure user actions with enterprise-grade security
  const handleUserAction = async (userId: string, action: string) => {
    try {
      // Security validation
      if (!csrfToken) {
        setSecurityError("Security token not available. Please refresh the page.");
        return;
      }

      // Confirm sensitive actions
      const sensitiveActions = ['delete', 'deactivate', 'change_role'];
      if (sensitiveActions.includes(action)) {
        const confirmed = window.confirm(
          `Are you sure you want to ${action} this user? This action may not be reversible.`
        );
        if (!confirmed) return;
      }

      console.log("🔒 SECURE: Performing user action:", { userId, action });

      const response = await fetch(`/api/admin/users/${userId}`, {
        method: "PATCH",
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
          "X-CSRF-Token": csrfToken, // ✅ CSRF protection
          "X-Requested-With": "XMLHttpRequest",
        },
        body: JSON.stringify({ action }),
      });

      console.log("📡 SECURE: User action response status:", response.status);

      // Extract rate limit information
      const rateLimitRemaining = response.headers.get("X-RateLimit-Remaining");
      if (rateLimitRemaining) {
        setRateLimitInfo(prev => ({
          ...prev,
          remaining: parseInt(rateLimitRemaining)
        }));
      }

      if (response.ok) {
        const result = await response.json();
        console.log("✅ SECURE: User action completed:", result);

        // Refresh the list
        await fetchUsers();
        setSecurityError("");

        // Show success message
        alert(`Action "${action}" completed successfully!`);

      } else {
        // Enhanced error handling with security context
        const contentType = response.headers.get("content-type");

        if (contentType && contentType.includes("application/json")) {
          const errorData = await response.json();
          console.error("❌ SECURE: User action error:", errorData);

          // Handle specific error codes
          switch (errorData.code) {
            case "INSUFFICIENT_PERMISSIONS":
              setSecurityError("You don't have permission to perform this action");
              break;
            case "USER_NOT_FOUND":
              setSecurityError("User not found or has been deleted");
              break;
            case "RATE_LIMIT_EXCEEDED":
              setSecurityError(`Rate limit exceeded. Try again in ${errorData.retryAfter} seconds`);
              break;
            case "INVALID_ACTION":
              setSecurityError("Invalid action requested");
              break;
            default:
              setSecurityError(errorData.error || "Failed to perform action");
          }
        } else {
          const errorText = await response.text();
          console.error("❌ SECURE: Non-JSON error:", errorText.substring(0, 200));
          setSecurityError("Authentication or server error occurred");
        }
      }
    } catch (error) {
      console.error("💥 SECURE: Error performing user action:", error);
      setSecurityError("Network error occurred. Please check your connection.");
    }
  };

  const filteredUsers = users.filter((user) => {
    const matchesSearch =
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.organizationName?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesRole = roleFilter === "all" || user.role === roleFilter;

    return matchesSearch && matchesRole;
  });

  // ✅ Enhanced: Secure user creation with enterprise-grade security
  const handleCreateUser = async () => {
    try {
      // Validate required fields
      if (!newUserData.email || !newUserData.name || !newUserData.role) {
        setSecurityError("All fields are required");
        return;
      }

      // Validate password strength if provided
      if (newUserData.password && newUserData.password.length < 8) {
        setSecurityError("Password must be at least 8 characters long");
        return;
      }

      console.log("🔒 SECURE: Creating user with enhanced security");

      const response = await fetch("/api/admin/users", {
        method: "POST",
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
          "X-CSRF-Token": csrfToken, // ✅ CSRF protection
          "X-Requested-With": "XMLHttpRequest",
        },
        body: JSON.stringify(newUserData),
      });

      console.log("📡 SECURE: Create user response status:", response.status);

      // Extract rate limit information
      const rateLimitRemaining = response.headers.get("X-RateLimit-Remaining");
      if (rateLimitRemaining) {
        setRateLimitInfo(prev => ({
          ...prev,
          remaining: parseInt(rateLimitRemaining)
        }));
      }

      if (response.ok) {
        const result = await response.json();
        console.log("✅ SECURE: User created successfully:", result);

        // Refresh the list
        await fetchUsers();

        // Reset form and close dialog
        setIsCreateDialogOpen(false);
        setNewUserData({ email: "", name: "", role: "", password: "" });
        setSecurityError("");

        // Show success message
        alert(`User ${result.data?.name || newUserData.name} created successfully!`);

      } else {
        // Enhanced error handling with security context
        const contentType = response.headers.get("content-type");

        if (contentType && contentType.includes("application/json")) {
          const errorData = await response.json();
          console.error("❌ SECURE: Create user error:", errorData);

          // Handle specific error codes
          switch (errorData.code) {
            case "EMAIL_EXISTS":
              setSecurityError("A user with this email already exists");
              break;
            case "VALIDATION_ERROR":
              const validationErrors = errorData.details?.map((d: any) => d.message).join(", ");
              setSecurityError(`Validation failed: ${validationErrors}`);
              break;
            case "INSUFFICIENT_PRIVILEGES":
              setSecurityError("You don't have permission to create users with this role");
              break;
            case "RATE_LIMIT_EXCEEDED":
              setSecurityError(`Rate limit exceeded. Try again in ${errorData.retryAfter} seconds`);
              break;
            default:
              setSecurityError(errorData.error || "Failed to create user");
          }
        } else {
          const errorText = await response.text();
          console.error("❌ SECURE: Non-JSON error:", errorText.substring(0, 200));
          setSecurityError("Authentication or server error occurred");
        }
      }
    } catch (error) {
      console.error("💥 SECURE: Error creating user:", error);
      setSecurityError("Network error occurred. Please check your connection.");
    }
  };

  const getInitials = (name?: string) => {
    if (!name) return "U";
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  if (loading) {
    return <ComponentLoading text="Loading users..." />;
  }

  return (
    <Card>
      <CardHeader>
        {/* ✅ Enhanced: Security error display */}
        {securityError && (
          <div className="mb-4 bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Security Error</h3>
                <div className="mt-2 text-sm text-red-700">
                  {securityError}
                </div>
                <div className="mt-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setSecurityError("");
                      initializeSecurity();
                    }}
                  >
                    Retry
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              User Management
              {/* ✅ Enhanced: Security status indicator */}
              {csrfToken && (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  🔒 Secured
                </span>
              )}
            </CardTitle>
            <CardDescription>
              Manage users, their roles, and account status
              {rateLimitInfo && (
                <span className="block text-xs text-muted-foreground mt-1">
                  Rate limit: {rateLimitInfo.remaining} requests remaining
                </span>
              )}
            </CardDescription>
          </div>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <UserPlus className="h-4 w-4 mr-2" />
                Add User
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Create New User</DialogTitle>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label htmlFor="email">Email *</Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder="<EMAIL>"
                    value={newUserData.email}
                    onChange={(e) => setNewUserData(prev => ({ ...prev, email: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="name">Full Name</Label>
                  <Input
                    id="name"
                    placeholder="John Doe"
                    value={newUserData.name}
                    onChange={(e) => setNewUserData(prev => ({ ...prev, name: e.target.value }))}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="role">Role *</Label>
                  <Select
                    value={newUserData.role}
                    onValueChange={(value) => setNewUserData(prev => ({ ...prev, role: value }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a role" />
                    </SelectTrigger>
                    <SelectContent>
                      {/* ✅ Dynamic role options from Role Management */}
                      {roles.map((role: any) => (
                        <SelectItem key={role.id} value={role.name}>
                          {role.display_name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* ✅ Enhanced: Password field with security requirements */}
                <div className="space-y-2">
                  <Label htmlFor="password">Password (Optional)</Label>
                  <Input
                    id="password"
                    type="password"
                    placeholder="Leave empty for email invitation"
                    value={newUserData.password}
                    onChange={(e) => setNewUserData(prev => ({ ...prev, password: e.target.value }))}
                  />
                  <p className="text-xs text-muted-foreground">
                    If provided, password must be at least 8 characters with uppercase, lowercase, and number.
                    Leave empty to send email invitation.
                  </p>
                </div>
                <div className="flex justify-end space-x-2 pt-4">
                  <Button
                    variant="outline"
                    onClick={() => setIsCreateDialogOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    onClick={handleCreateUser}
                    disabled={!newUserData.email || !newUserData.role || isLoadingRoles || !csrfToken || !!securityError}
                  >
                    {!csrfToken ? "Initializing Security..." : "Create User"}
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex items-center space-x-4 mb-6">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search users..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Role: {roleFilter === "all" ? "All" :
                  roles.find(r => r.name === roleFilter)?.display_name || roleFilter}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={() => setRoleFilter("all")}>
                All Roles
              </DropdownMenuItem>
              {/* ✅ Dynamic role options from Role Management */}
              {roles.map((role) => (
                <DropdownMenuItem
                  key={role.id}
                  onClick={() => setRoleFilter(role.name)}
                >
                  {role.display_name}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>User</TableHead>
              <TableHead>Role</TableHead>
              <TableHead>Credits</TableHead>
              <TableHead>Organization</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Joined</TableHead>
              <TableHead className="w-[70px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredUsers.map((user) => (
              <TableRow key={user.id}>
                <TableCell>
                  <div className="flex items-center space-x-3">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={user.image || ""} alt={user.name || ""} />
                      <AvatarFallback>{getInitials(user.name)}</AvatarFallback>
                    </Avatar>
                    <div>
                      <div className="font-medium">{user.name || "Unnamed User"}</div>
                      <div className="text-sm text-muted-foreground">{user.email}</div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant={user.role === "super_admin" || user.role === "tenant_admin" ? "default" : "secondary"}>
                    {user.roleName || roles.find((r: any) => r.name === user.role)?.display_name || user.role}
                  </Badge>
                </TableCell>
                <TableCell>
                  <span className="font-medium">{formatCredits(user.credits)}</span>
                </TableCell>
                <TableCell>
                  {user.organizationName ? (
                    <span className="text-sm">{user.organizationName}</span>
                  ) : (
                    <span className="text-sm text-muted-foreground">No organization</span>
                  )}
                </TableCell>
                <TableCell>
                  <div className="flex flex-col space-y-1">
                    <Badge variant={user.isActive ? "default" : "destructive"}>
                      {user.isActive ? "Active" : "Inactive"}
                    </Badge>
                    {user.emailVerified ? (
                      <Badge variant="secondary" className="text-xs">
                        Verified
                      </Badge>
                    ) : (
                      <Badge variant="outline" className="text-xs">
                        Unverified
                      </Badge>
                    )}
                  </div>
                </TableCell>
                <TableCell className="text-sm text-muted-foreground">
                  {format(user.createdAt, "MMM dd, yyyy")}
                </TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuItem
                        onClick={() => handleUserAction(user.id, user.isActive ? "deactivate" : "activate")}
                      >
                        {user.isActive ? "Deactivate" : "Activate"}
                      </DropdownMenuItem>
                      <DropdownMenuLabel>Change Role</DropdownMenuLabel>
                      {/* ✅ Dynamic role change options */}
                      {roles.filter((role: any) => role.name !== user.role).map((role: any) => (
                        <DropdownMenuItem
                          key={role.id}
                          onClick={() => handleUserAction(user.id, `change_role:${role.name}`)}
                        >
                          Make {role.display_name}
                        </DropdownMenuItem>
                      ))}
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={() => handleUserAction(user.id, "reset_password")}
                      >
                        Reset Password
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => handleUserAction(user.id, "send_verification")}
                      >
                        Send Verification
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>

        {filteredUsers.length === 0 && (
          <div className="text-center py-8 text-muted-foreground">
            No users found matching your criteria
          </div>
        )}
      </CardContent>
    </Card>
  );
}
