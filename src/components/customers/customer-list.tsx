"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Checkbox } from "@/components/ui/checkbox";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  User,
  Edit,
  Trash2,
  Plus,
  Search,
  Building,
  AlertCircle,
  Eye,
  EyeOff,
  MoreHorizontal,
  Copy,
  Mail,
  Phone,
  MapPin,
  DollarSign
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { Customer } from "@/lib/db/schema";
import { 
  useCustomers, 
  useDeleteCustomer, 
  useToggleCustomerActive,
  useBulkCustomerOperation,
  useDuplicateCustomer
} from "@/lib/hooks/queries/use-customer-queries";
import { DeleteConfirmationDialog } from "@/components/ui/delete-confirmation-dialog";
import { DeleteSuccessToast } from "@/components/ui/delete-success-toast";

interface CustomerListProps {
  onEdit?: (customer: Customer) => void;
  onAdd?: () => void;
  showActions?: boolean;
  tenantId?: number;
  locationId?: string;
  pricingGroupId?: string;
}

export function CustomerList({
  onEdit,
  onAdd,
  showActions = true,
  tenantId,
  locationId,
  pricingGroupId,
}: CustomerListProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [deleteError, setDeleteError] = useState<string | null>(null);
  const [deleteConfirmation, setDeleteConfirmation] = useState<{
    isOpen: boolean;
    customer: Customer | null;
  }>({ isOpen: false, customer: null });
  const [showDeleteSuccess, setShowDeleteSuccess] = useState(false);
  const [deletedCustomerName, setDeletedCustomerName] = useState("");

  const { data: customers = [], isLoading, error } = useCustomers({
    tenantId,
    filters: {
      search: searchQuery || undefined,
      locationId,
      pricingGroupId,
    },
  });
  const deleteCustomerMutation = useDeleteCustomer();
  const toggleActiveMutation = useToggleCustomerActive();
  const bulkOperationMutation = useBulkCustomerOperation();
  const duplicateMutation = useDuplicateCustomer();

  const handleDeleteClick = (customer: Customer) => {
    setDeleteConfirmation({ isOpen: true, customer });
  };

  const handleDeleteConfirm = async () => {
    if (!deleteConfirmation.customer) return;

    const customerName = `${deleteConfirmation.customer.firstName} ${deleteConfirmation.customer.lastName || ''}`.trim();

    try {
      setDeleteError(null);
      await deleteCustomerMutation.mutateAsync(deleteConfirmation.customer.id);

      setDeleteConfirmation({ isOpen: false, customer: null });
      setDeletedCustomerName(customerName);
      setShowDeleteSuccess(true);

    } catch (error) {
      setDeleteError(error instanceof Error ? error.message : "Failed to delete customer");
    }
  };

  const handleDeleteCancel = () => {
    setDeleteConfirmation({ isOpen: false, customer: null });
  };

  const handleToggleActive = async (customer: Customer) => {
    try {
      await toggleActiveMutation.mutateAsync(customer.id);
    } catch (error) {
      setDeleteError(error instanceof Error ? error.message : "Failed to toggle customer status");
    }
  };

  const handleDuplicate = async (customer: Customer) => {
    try {
      const newEmail = `copy_${Date.now()}_${customer.email}`;
      await duplicateMutation.mutateAsync({ 
        id: customer.id, 
        email: newEmail
      });
    } catch (error) {
      setDeleteError(error instanceof Error ? error.message : "Failed to duplicate customer");
    }
  };

  const handleSelectItem = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedItems([...selectedItems, id]);
    } else {
      setSelectedItems(selectedItems.filter(item => item !== id));
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedItems(customers.map(c => c.id));
    } else {
      setSelectedItems([]);
    }
  };

  const handleBulkOperation = async (action: 'activate' | 'deactivate' | 'delete') => {
    if (selectedItems.length === 0) return;

    try {
      await bulkOperationMutation.mutateAsync({ ids: selectedItems, action });
      setSelectedItems([]);
    } catch (error) {
      setDeleteError(error instanceof Error ? error.message : `Failed to ${action} customers`);
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="py-8">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Failed to load customers. Please try again.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Customers
            </CardTitle>
            <CardDescription>
              Manage customer information and settings
            </CardDescription>
          </div>
          {showActions && onAdd && (
            <Button onClick={onAdd}>
              <Plus className="h-4 w-4 mr-2" />
              Add Customer
            </Button>
          )}
        </div>

        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search customers..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Bulk Actions */}
        {selectedItems.length > 0 && (
          <div className="flex items-center gap-2 p-3 bg-blue-50 rounded-lg">
            <span className="text-sm text-blue-700">
              {selectedItems.length} item(s) selected
            </span>
            <div className="flex gap-2 ml-auto">
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleBulkOperation('activate')}
                disabled={bulkOperationMutation.isPending}
              >
                <Eye className="h-4 w-4 mr-1" />
                Activate
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleBulkOperation('deactivate')}
                disabled={bulkOperationMutation.isPending}
              >
                <EyeOff className="h-4 w-4 mr-1" />
                Deactivate
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleBulkOperation('delete')}
                disabled={bulkOperationMutation.isPending}
                className="text-red-600 hover:text-red-700"
              >
                <Trash2 className="h-4 w-4 mr-1" />
                Delete
              </Button>
            </div>
          </div>
        )}

        {/* Delete Error */}
        {deleteError && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{deleteError}</AlertDescription>
          </Alert>
        )}
      </CardHeader>

      <CardContent>
        {/* Customer List */}
        {customers.length === 0 ? (
          <div className="text-center py-8">
            <User className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              {searchQuery ? "No customers found" : "No customers yet"}
            </h3>
            <p className="text-gray-500 dark:text-gray-400 mb-4">
              {searchQuery 
                ? "Try adjusting your search terms."
                : "Get started by adding your first customer."
              }
            </p>
            {showActions && onAdd && !searchQuery && (
              <Button onClick={onAdd}>
                <Plus className="h-4 w-4 mr-2" />
                Add Customer
              </Button>
            )}
          </div>
        ) : (
          <div className="space-y-2">
            {/* Select All */}
            <div className="flex items-center gap-2 p-2 border-b">
              <Checkbox
                checked={selectedItems.length === customers.length}
                onCheckedChange={handleSelectAll}
              />
              <span className="text-sm text-gray-600">Select All</span>
            </div>

            {customers.map((customer) => (
              <div
                key={customer.id}
                className="border rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
              >
                <div className="flex items-center gap-3">
                  {/* Checkbox */}
                  <Checkbox
                    checked={selectedItems.includes(customer.id)}
                    onCheckedChange={(checked) => handleSelectItem(customer.id, checked as boolean)}
                  />

                  {/* Content */}
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <User className="h-4 w-4 text-gray-500" />
                      <h4 className="font-medium text-gray-900 dark:text-gray-100">
                        {customer.firstName} {customer.lastName}
                      </h4>
                      <Badge variant={customer.isActive ? "default" : "secondary"}>
                        {customer.isActive ? "Active" : "Inactive"}
                      </Badge>
                    </div>
                    
                    <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400 mb-2">
                      <div className="flex items-center gap-1">
                        <Mail className="h-3 w-3" />
                        {customer.email}
                      </div>
                      {customer.mobileNumber && (
                        <div className="flex items-center gap-1">
                          <Phone className="h-3 w-3" />
                          {customer.mobileCountryCode} {customer.mobileNumber}
                        </div>
                      )}
                    </div>

                    <div className="flex items-center gap-2">
                      <Badge variant="secondary" className="text-xs">
                        Tenant ID: {customer.tenantId}
                      </Badge>
                      {customer.locationId && (
                        <Badge variant="outline" className="text-xs flex items-center gap-1">
                          <MapPin className="h-3 w-3" />
                          Location: {customer.locationId}
                        </Badge>
                      )}
                      {customer.pricingGroupId && (
                        <Badge variant="outline" className="text-xs flex items-center gap-1">
                          <DollarSign className="h-3 w-3" />
                          Pricing: {customer.pricingGroupId}
                        </Badge>
                      )}
                    </div>
                  </div>

                  {/* Active Toggle */}
                  <div className="flex items-center gap-2">
                    <Switch
                      checked={customer.isActive}
                      onCheckedChange={() => handleToggleActive(customer)}
                      disabled={toggleActiveMutation.isPending}
                    />
                  </div>

                  {/* Actions */}
                  {showActions && (
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        {onEdit && (
                          <DropdownMenuItem onClick={() => onEdit(customer)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </DropdownMenuItem>
                        )}
                        <DropdownMenuItem onClick={() => handleDuplicate(customer)}>
                          <Copy className="h-4 w-4 mr-2" />
                          Duplicate
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleToggleActive(customer)}>
                          {customer.isActive ? (
                            <>
                              <EyeOff className="h-4 w-4 mr-2" />
                              Deactivate
                            </>
                          ) : (
                            <>
                              <Eye className="h-4 w-4 mr-2" />
                              Activate
                            </>
                          )}
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem 
                          onClick={() => handleDeleteClick(customer)}
                          className="text-red-600"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Summary */}
        {customers.length > 0 && (
          <div className="mt-4 pt-4 border-t text-sm text-gray-500 dark:text-gray-400">
            Showing {customers.length} customer(s)
          </div>
        )}
      </CardContent>

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationDialog
        isOpen={deleteConfirmation.isOpen}
        onClose={handleDeleteCancel}
        onConfirm={handleDeleteConfirm}
        title="Delete Customer"
        description="Are you sure you want to delete this customer? This action cannot be undone."
        itemName={deleteConfirmation.customer ? 
          `${deleteConfirmation.customer.firstName} ${deleteConfirmation.customer.lastName || ''}`.trim() : 
          ""
        }
        isLoading={deleteCustomerMutation.isPending}
      />

      {/* Delete Success Toast */}
      <DeleteSuccessToast
        isVisible={showDeleteSuccess}
        onClose={() => setShowDeleteSuccess(false)}
        itemName={deletedCustomerName}
      />
    </Card>
  );
}
