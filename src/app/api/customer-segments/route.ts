import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { z } from "zod";
import { customerSegmentsService } from "@/lib/services/customer-segments.service";

// Validation schema for query parameters
const queryFiltersSchema = z.object({
  tenantId: z.string().transform((val) => parseInt(val)),
  search: z.string().optional(),
  type: z.string().optional(),
  isActive: z.string().transform((val) => val === 'true').optional(),
});

/**
 * GET /api/customer-segments
 * Get customer segments with optional filtering
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    // Validate query parameters
    const validatedParams = queryFiltersSchema.parse(queryParams);
    
    const { tenantId, ...filters } = validatedParams;

    if (!tenantId || tenantId <= 0) {
      return NextResponse.json(
        { error: "Valid tenant ID is required" },
        { status: 400 }
      );
    }

    console.log(`👥 [GET /api/customer-segments] Fetching customer segments for tenant ${tenantId}:`, filters);

    const customerSegments = await customerSegmentsService.getByTenant(tenantId, filters);

    return NextResponse.json({
      success: true,
      data: customerSegments,
      meta: {
        total: customerSegments.length,
        tenantId,
        filters,
      }
    });
  } catch (error) {
    console.error("👥 [GET /api/customer-segments] Error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid query parameters", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to fetch customer segments" },
      { status: 500 }
    );
  }
}
