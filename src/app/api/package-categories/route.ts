import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { z } from "zod";
import { packageCategoryService } from "@/lib/services/package-category.service";

// Validation schemas
const createPackageCategorySchema = z.object({
  tenantId: z.number().int().positive("Tenant ID must be positive"),
  name: z.string().min(1, "Name is required").max(255, "Name must be less than 255 characters"),
  description: z.string().max(255, "Description must be less than 255 characters").optional(),
});

const querySchema = z.object({
  search: z.string().optional(),
  tenantId: z.string().optional(),
  limit: z.string().optional(),
  offset: z.string().optional(),
});

const bulkOperationSchema = z.object({
  ids: z.array(z.string().min(1)),
  action: z.enum(["delete"]),
});

/**
 * GET /api/package-categories
 * Get package categories for the current tenant with optional filtering
 */
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const tenantIdParam = searchParams.get("tenantId");
    const queryParams = Object.fromEntries(searchParams.entries());

    // For now, require tenantId in query params since session.user doesn't have tenantId
    const tenantId = tenantIdParam ? parseInt(tenantIdParam) : null;

    if (!tenantId) {
      return NextResponse.json({ error: "Tenant ID required in query parameters" }, { status: 400 });
    }

    const validatedQuery = querySchema.parse(queryParams);

    // Build filters
    const filters = {
      search: validatedQuery.search,
    };

    console.log(`📦 [GET /api/package-categories] Fetching package categories for tenant ${tenantId}:`, filters);

    const packageCategories = await packageCategoryService.getByTenant(tenantId, filters);

    return NextResponse.json({
      success: true,
      data: packageCategories,
      meta: {
        total: packageCategories.length,
        filters,
      }
    });
  } catch (error) {
    console.error("📦 [GET /api/package-categories] Error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid query parameters", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to fetch package categories" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/package-categories
 * Create a new package category
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = createPackageCategorySchema.parse(body);

    console.log(`📦 [POST /api/package-categories] Creating package category:`, validatedData);

    // Check if name already exists for this tenant
    const nameExists = await packageCategoryService.nameExists(
      validatedData.name,
      validatedData.tenantId
    );

    if (nameExists) {
      return NextResponse.json(
        { error: "Package category name already exists for this tenant" },
        { status: 409 }
      );
    }

    const packageCategory = await packageCategoryService.create(validatedData);

    return NextResponse.json({
      success: true,
      data: packageCategory,
      message: "Package category created successfully"
    }, { status: 201 });
  } catch (error) {
    console.error("📦 [POST /api/package-categories] Error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid package category data", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to create package category" },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/package-categories
 * Bulk delete package categories
 */
export async function DELETE(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = bulkOperationSchema.parse(body);

    console.log(`📦 [DELETE /api/package-categories] Bulk operation:`, validatedData);

    if (validatedData.action === "delete") {
      const results = await packageCategoryService.bulkOperation({
        ids: validatedData.ids,
        action: "delete"
      });

      return NextResponse.json({
        success: true,
        data: results,
        message: `Successfully deleted ${validatedData.ids.length} package categories`
      });
    }

    return NextResponse.json(
      { error: "Invalid bulk operation action" },
      { status: 400 }
    );
  } catch (error) {
    console.error("📦 [DELETE /api/package-categories] Error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid bulk operation data", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to perform bulk operation" },
      { status: 500 }
    );
  }
}
