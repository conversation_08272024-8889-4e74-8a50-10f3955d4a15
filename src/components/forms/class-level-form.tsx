"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { motion } from "framer-motion";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, AlertCircle, CheckCircle, GraduationCap, Building, FileText, Hash } from "lucide-react";
import { ClassLevel } from "@/lib/db/schema";
import { useTenants } from "@/lib/hooks/queries/use-tenant-queries";

// Form validation schema
const classLevelSchema = z.object({
  tenantId: z.number().int().positive("Tenant is required"),
  name: z.string().min(1, "Name is required").max(255, "Name must be less than 255 characters"),
  description: z.string().max(500, "Description must be less than 500 characters").optional(),
  sortOrder: z.number().int().min(0, "Sort order must be non-negative").optional(),
  isActive: z.boolean(),
});

type ClassLevelFormData = z.infer<typeof classLevelSchema>;

interface ClassLevelFormProps {
  classLevel?: ClassLevel;
  tenantId?: number;
  onSubmit: (data: ClassLevelFormData) => Promise<void>;
  onCancel: () => void;
  className?: string;
}

export function ClassLevelForm({
  classLevel,
  tenantId,
  onSubmit,
  onCancel,
  className = "",
}: ClassLevelFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [showSuccess, setShowSuccess] = useState(false);

  const { data: tenantsData, isLoading: tenantsLoading } = useTenants();
  const tenants = tenantsData?.tenants || [];

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    formState: { errors, isValid },
  } = useForm<ClassLevelFormData>({
    resolver: zodResolver(classLevelSchema),
    defaultValues: {
      tenantId: classLevel?.tenantId || tenantId || (tenants[0]?.id ?? 1),
      name: classLevel?.name || "",
      description: classLevel?.description || "",
      sortOrder: classLevel?.sortOrder || 0,
      isActive: classLevel?.isActive ?? true,
    },
    mode: "onChange",
  });

  const watchedTenantId = watch("tenantId");
  const watchedIsActive = watch("isActive");

  const handleFormSubmit = async (data: ClassLevelFormData) => {
    try {
      setIsSubmitting(true);
      setSubmitError(null);
      
      await onSubmit(data);
      
      setShowSuccess(true);
      setTimeout(() => setShowSuccess(false), 2000);
    } catch (error) {
      setSubmitError(error instanceof Error ? error.message : "An error occurred");
    } finally {
      setIsSubmitting(false);
    }
  };

  const selectedTenant = tenants.find(tenant => tenant.id === watchedTenantId);

  return (
    <Card className={`w-full max-w-2xl mx-auto ${className}`}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <GraduationCap className="h-5 w-5" />
          {classLevel ? "Edit Class Level" : "Add Class Level"}
        </CardTitle>
        <CardDescription>
          {classLevel 
            ? "Update the class level details below."
            : "Create a new class level by filling out the form below."
          }
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {submitError && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{submitError}</AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          {/* Tenant Selection */}
          <div className="space-y-2">
            <Label htmlFor="tenantId" className="flex items-center gap-2">
              <Building className="h-4 w-4" />
              Tenant *
            </Label>
            <Select
              value={watchedTenantId?.toString()}
              onValueChange={(value) => setValue("tenantId", parseInt(value), { shouldValidate: true })}
              disabled={tenantsLoading}
            >
              <SelectTrigger>
                <SelectValue placeholder={tenantsLoading ? "Loading tenants..." : "Select tenant"} />
              </SelectTrigger>
              <SelectContent>
                {tenants.map((tenant) => (
                  <SelectItem key={tenant.id} value={tenant.id.toString()}>
                    <div className="flex items-center gap-2">
                      <Building className="h-4 w-4" />
                      <span>{tenant.name}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.tenantId && (
              <p className="text-sm text-red-600">{errors.tenantId.message}</p>
            )}
            {selectedTenant && (
              <div className="text-sm text-gray-600 bg-gray-50 p-2 rounded">
                Selected tenant: {selectedTenant.name}
              </div>
            )}
          </div>

          {/* Class Level Name */}
          <div className="space-y-2">
            <Label htmlFor="name" className="flex items-center gap-2">
              <GraduationCap className="h-4 w-4" />
              Class Level Name *
            </Label>
            <Input
              id="name"
              {...register("name")}
              placeholder="Enter class level name (e.g., Beginner, Intermediate, Advanced)"
              className="w-full"
            />
            {errors.name && (
              <p className="text-sm text-red-600">{errors.name.message}</p>
            )}
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description" className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Description (Optional)
            </Label>
            <Textarea
              id="description"
              {...register("description")}
              placeholder="Enter description for this class level"
              className="w-full min-h-[100px]"
            />
            {errors.description && (
              <p className="text-sm text-red-600">{errors.description.message}</p>
            )}
            <p className="text-sm text-gray-500">
              Describe what this class level represents or its requirements
            </p>
          </div>

          {/* Sort Order */}
          <div className="space-y-2">
            <Label htmlFor="sortOrder" className="flex items-center gap-2">
              <Hash className="h-4 w-4" />
              Sort Order
            </Label>
            <Input
              id="sortOrder"
              type="number"
              min="0"
              {...register("sortOrder", { valueAsNumber: true })}
              placeholder="Enter sort order (0 = first)"
              className="w-full"
            />
            {errors.sortOrder && (
              <p className="text-sm text-red-600">{errors.sortOrder.message}</p>
            )}
            <p className="text-sm text-gray-500">
              Lower numbers appear first in the list. Leave as 0 to add at the end.
            </p>
          </div>

          {/* Active Status */}
          <div className="space-y-2">
            <Label className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4" />
              Status
            </Label>
            <div className="flex items-center space-x-2">
              <Switch
                checked={watchedIsActive}
                onCheckedChange={(checked) => setValue("isActive", checked, { shouldValidate: true })}
              />
              <Label className="text-sm">
                {watchedIsActive ? "Active" : "Inactive"}
              </Label>
            </div>
            <p className="text-sm text-gray-500">
              {watchedIsActive 
                ? "This class level is active and available for use"
                : "This class level is inactive and hidden from selection"
              }
            </p>
          </div>

          {/* Form Actions */}
          <div className="flex items-center gap-3 pt-6 border-t">
            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="flex-1"
            >
              <Button
                type="submit"
                disabled={!isValid || isSubmitting || tenantsLoading}
                className="w-full"
              >
                <motion.div
                  initial={false}
                  animate={{
                    scale: showSuccess ? [1, 1.1, 1] : 1,
                    backgroundColor: showSuccess ? "#10b981" : undefined,
                  }}
                  transition={{ duration: 0.3 }}
                  className="flex items-center gap-2"
                >
                  {isSubmitting ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : showSuccess ? (
                    <CheckCircle className="h-4 w-4" />
                  ) : (
                    <GraduationCap className="h-4 w-4" />
                  )}
                  {isSubmitting
                    ? "Saving..."
                    : showSuccess
                    ? "Success!"
                    : classLevel
                    ? "Update Class Level"
                    : "Create Class Level"
                  }
                </motion.div>
              </Button>
            </motion.div>

            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isSubmitting}
              className="px-6"
            >
              Cancel
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
