import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { AddressService } from "@/lib/services/address.service";
import { z } from "zod";

// Validation schema
const createAddressSchema = z.object({
  tenantId: z.number().int().positive(),
  addressLine1: z.string().max(255).optional(),
  addressLine2: z.string().max(255).optional(),
  city: z.string().max(255).optional(),
  state: z.string().max(255).optional(),
  country: z.string().max(255).optional(),
  postalCode: z.string().max(255).optional(),
});

const updateAddressSchema = createAddressSchema.partial().omit({ tenantId: true });

// GET /api/addresses - Get addresses
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get("tenantId");
    const country = searchParams.get("country");
    const state = searchParams.get("state");
    const postalCode = searchParams.get("postalCode");
    const status = searchParams.get("status"); // 'accepted' or 'pending'

    if (!tenantId || tenantId === "null" || tenantId === "undefined" || tenantId.includes("object")) {
      return NextResponse.json(
        { error: "Valid tenantId parameter is required" },
        { status: 400 }
      );
    }

    const tenantIdNum = parseInt(tenantId);
    if (isNaN(tenantIdNum) || tenantIdNum <= 0) {
      return NextResponse.json(
        { error: "tenantId must be a valid positive number" },
        { status: 400 }
      );
    }
    let addresses;

    if (country) {
      addresses = await AddressService.getByCountry(tenantIdNum, country);
    } else if (state) {
      addresses = await AddressService.getByState(tenantIdNum, state);
    } else if (postalCode) {
      addresses = await AddressService.getByPostalCode(tenantIdNum, postalCode);
    } else if (status === 'accepted') {
      addresses = await AddressService.getAcceptedAddresses(tenantIdNum);
    } else if (status === 'pending') {
      addresses = await AddressService.getPendingAddresses(tenantIdNum);
    } else {
      addresses = await AddressService.getByTenantId(tenantIdNum);
    }

    return NextResponse.json({ addresses });
  } catch (error) {
    console.error("Error fetching addresses:", error);
    return NextResponse.json(
      { error: "Failed to fetch addresses" },
      { status: 500 }
    );
  }
}

// POST /api/addresses - Create address
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = createAddressSchema.parse(body);

    // Check if user has permission to create address for this tenant
    if (session.user.role !== "admin") {
      // Add tenant ownership check here
    }

    const address = await AddressService.create(validatedData);

    return NextResponse.json({ address }, { status: 201 });
  } catch (error) {
    console.error("Error creating address:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to create address" },
      { status: 500 }
    );
  }
}

// POST /api/addresses/bulk - Bulk create addresses
export async function POST_BULK(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { addresses: addressesData } = body;

    if (!Array.isArray(addressesData) || addressesData.length === 0) {
      return NextResponse.json(
        { error: "addresses array is required and cannot be empty" },
        { status: 400 }
      );
    }

    // Validate each address
    const validatedAddresses = addressesData.map(addr => 
      createAddressSchema.parse(addr)
    );

    // Check permissions for each tenant
    if (session.user.role !== "admin") {
      // Add tenant ownership checks here
    }

    const addresses = await AddressService.bulkCreate(validatedAddresses);

    return NextResponse.json({ addresses }, { status: 201 });
  } catch (error) {
    console.error("Error bulk creating addresses:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to bulk create addresses" },
      { status: 500 }
    );
  }
}
