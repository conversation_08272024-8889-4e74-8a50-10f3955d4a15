import { auth } from "@/lib/auth/config";
import { EquipmentService } from "@/lib/services/equipment.service";
import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";

export const createEquipmentSchema = z.object({
  tenantId: z.number().int().positive(),
  name: z.string().max(255).optional(),
  default_display_name: z.string().max(255).optional(),
});


export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const tenantId = searchParams.get("tenantId");
    const name = searchParams.get("name");
    const display_name = searchParams.get("default_display_name");

    let equip;

    // If tenantId is provided, validate and filter by tenant
    if (tenantId && tenantId !== "null" && tenantId !== "undefined" && !tenantId.includes("object")) {
      const tenantIdNum = parseInt(tenantId);
      if (isNaN(tenantIdNum) || tenantIdNum <= 0) {
        return NextResponse.json(
          { error: "tenantId must be a valid positive number" },
          { status: 400 }
        );
      }

      if (name) {
        equip = await EquipmentService.searchEquipments(name);
      } else if (name) {
        equip = await EquipmentService.searchDisplayEquipments(name);
      } else {
        equip = await EquipmentService.getByTenantId(tenantIdNum);
      }
    } else {
      // No tenantId provided, return all equipments
      equip = await EquipmentService.getAll();
    }

    return NextResponse.json({ equip });
  } catch (error) {
    console.error("Error fetching equipments:", error);
    return NextResponse.json(
      { error: "Failed to fetch equipments" },
      { status: 500 }
    );
  }
}

// POST /api/equipments - Create equipments
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = createEquipmentSchema.parse(body);

    // Check if user has permission to create equipment for this tenant
    if (session.user.role !== "admin") {
      // Add tenant ownership check here
    }

    const data = await EquipmentService.create(validatedData);

    return NextResponse.json({ data }, { status: 201 });
  } catch (error) {
    console.error("Error creating equipment:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to create data" },
      { status: 500 }
    );
  }
}


// POST /api/equipments/bulk - Bulk create equipments
export async function POST_BULK(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const { eq: equipmentsData } = body;

    if (!Array.isArray(equipmentsData ) || equipmentsData .length === 0) {
      return NextResponse.json(
        { error: "locations array is required and cannot be empty" },
        { status: 400 }
      );
    }

    // Validate each equipment
    const validateEquipments = equipmentsData.map(addr => 
      createEquipmentSchema.parse(addr)
    );

    // Check permissions for each tenant
    if (session.user.role !== "admin") {
      // Add tenant ownership checks here
    }

    const eqs = await EquipmentService.bulkCreate(validateEquipments);

    return NextResponse.json({ eqs }, { status: 201 });
  } catch (error) {
    console.error("Error bulk creating locations:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to bulk create equipments" },
      { status: 500 }
    );
  }
}