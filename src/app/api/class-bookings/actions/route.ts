import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { z } from "zod";
import { ClassBookingService } from "@/lib/services/class-booking.service";

// Validation schemas for different actions
const checkInSchema = z.object({
  action: z.literal("check-in"),
  id: z.string().min(1, "Booking ID is required"),
  tenantId: z.number().int().positive("Tenant ID must be positive"),
});

const cancelSchema = z.object({
  action: z.literal("cancel"),
  id: z.string().min(1, "Booking ID is required"),
  tenantId: z.number().int().positive("Tenant ID must be positive"),
  reason: z.string().max(255).optional(),
});

const bulkUpdateStatusSchema = z.object({
  action: z.literal("bulk-update-status"),
  ids: z.array(z.string()).min(1, "At least one booking ID is required"),
  status: z.string().min(1, "Status is required"),
  tenantId: z.number().int().positive("Tenant ID must be positive"),
});

const actionSchema = z.discriminatedUnion("action", [
  checkInSchema,
  cancelSchema,
  bulkUpdateStatusSchema,
]);

/**
 * POST /api/class-bookings/actions
 * Handle various booking actions like check-in, cancel, bulk operations
 */
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = actionSchema.parse(body);

    console.log(`📅 [POST /api/class-bookings/actions] Action:`, validatedData.action, validatedData);

    // Check permissions - user harus bisa manage bookings
    if (session.user.role !== "admin" && !session.user.permissions?.includes("bookings.manage")) {
      return NextResponse.json(
        { error: "Insufficient permissions" },
        { status: 403 }
      );
    }

    // Tenant isolation - pastikan user hanya bisa action pada tenant mereka
    if (session.user.role !== "admin" && validatedData.tenantId !== session.user.tenantId) {
      return NextResponse.json(
        { error: "Access denied" },
        { status: 403 }
      );
    }

    switch (validatedData.action) {
      case "check-in": {
        const result = await ClassBookingService.checkIn(validatedData.id, validatedData.tenantId);
        
        if (!result) {
          return NextResponse.json(
            { error: "Failed to check in booking" },
            { status: 500 }
          );
        }

        return NextResponse.json({
          success: true,
          data: result,
          message: "Customer checked in successfully"
        });
      }

      case "cancel": {
        const result = await ClassBookingService.cancel(
          validatedData.id, 
          validatedData.tenantId, 
          validatedData.reason
        );
        
        if (!result) {
          return NextResponse.json(
            { error: "Failed to cancel booking" },
            { status: 500 }
          );
        }

        return NextResponse.json({
          success: true,
          data: result,
          message: "Booking cancelled successfully"
        });
      }

      case "bulk-update-status": {
        const result = await ClassBookingService.bulkUpdateStatus(
          validatedData.ids,
          validatedData.status,
          validatedData.tenantId
        );

        return NextResponse.json({
          success: true,
          data: result,
          message: `Successfully updated ${result.length} bookings to ${validatedData.status}`
        });
      }

      default:
        return NextResponse.json(
          { error: "Invalid action" },
          { status: 400 }
        );
    }
  } catch (error) {
    console.error("📅 [POST /api/class-bookings/actions] Error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid action data", details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      // Handle specific business logic errors
      if (error.message.includes("not found")) {
        return NextResponse.json(
          { error: error.message },
          { status: 404 }
        );
      }
      
      if (error.message.includes("already checked in")) {
        return NextResponse.json(
          { error: error.message },
          { status: 409 }
        );
      }
      
      if (error.message.includes("already cancelled")) {
        return NextResponse.json(
          { error: error.message },
          { status: 409 }
        );
      }
    }

    return NextResponse.json(
      { error: "Failed to perform action" },
      { status: 500 }
    );
  }
}
