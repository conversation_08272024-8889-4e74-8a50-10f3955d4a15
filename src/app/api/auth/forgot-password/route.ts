import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { generatePasswordResetToken } from "@/lib/auth/utils";
import { withRateLimit } from "@/lib/auth/rate-limit";
import { sendPasswordResetEmail } from "@/lib/email/send";

const forgotPasswordSchema = z.object({
  email: z.string().email("Invalid email address"),
});

export async function POST(request: NextRequest) {
  try {
    // Check rate limit
    const rateLimitResponse = await withRateLimit("forgotPassword")(request);
    if (rateLimitResponse) {
      return rateLimitResponse;
    }

    const body = await request.json();
    const { email } = forgotPasswordSchema.parse(body);

    // Generate reset token (returns null if user doesn't exist)
    const resetToken = await generatePasswordResetToken(email);

    // Always return success to prevent email enumeration
    // But only send email if user exists
    if (resetToken) {
      try {
        await sendPasswordResetEmail(email, resetToken);
      } catch (emailError) {
        console.error("Failed to send password reset email:", emailError);
        // Don't expose email sending errors to client
      }
    }

    return NextResponse.json(
      {
        message: "If an account with that email exists, we've sent a password reset link.",
      },
      { status: 200 }
    );
  } catch (error) {
    console.error("Forgot password error:", error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation failed", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
