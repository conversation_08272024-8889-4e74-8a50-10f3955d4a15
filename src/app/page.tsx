import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { HomeHeader } from "@/components/home/<USER>";
import {
  Shield,
  Zap,
  Users,
  CreditCard,
  Mail,
  Database,
  Globe,
  Smartphone
} from "lucide-react";

export default function Home() {
  const features = [
    {
      icon: Shield,
      title: "Advanced Authentication",
      description: "NextAuth v5 with email/password, OAuth, WebAuthn, and session management"
    },
    {
      icon: Database,
      title: "Modern Database",
      description: "Drizzle ORM with Cloudflare D1 for production and SQLite for development"
    },
    {
      icon: Mail,
      title: "Email Service",
      description: "React Email with beautiful templates and transactional emails"
    },
    {
      icon: CreditCard,
      title: "Billing System",
      description: "Credit-based pricing with Stripe integration and usage tracking"
    },
    {
      icon: Users,
      title: "Multi-tenancy",
      description: "Organization management with roles and permissions"
    },
    {
      icon: Zap,
      title: "Edge Computing",
      description: "Cloudflare Workers with zero cold starts and global deployment"
    },
    {
      icon: Globe,
      title: "SEO Optimized",
      description: "Meta tags, OpenGraph, sitemap, and internationalization support"
    },
    {
      icon: Smartphone,
      title: "Mobile Ready",
      description: "Responsive design and API ready for mobile app integration"
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Header */}
      <HomeHeader />

      {/* Hero Section */}
      <section className="container mx-auto px-4 py-20 text-center">
        <div className="max-w-4xl mx-auto">
          <Badge variant="secondary" className="mb-4">
            Next.js 15 SaaS Template
          </Badge>
          <h1 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            Build Your SaaS Faster Than Ever
          </h1>
          <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
            A comprehensive Next.js 15 template with authentication, billing, multi-tenancy,
            and everything you need to launch your SaaS product.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" asChild>
              <Link href="/auth/signup">Start Building</Link>
            </Button>
            <Button size="lg" variant="outline" asChild>
              <Link href="/auth/signin">View Demo</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Features Grid */}
      <section className="container mx-auto px-4 py-20">
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold mb-4">Everything You Need</h2>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Built with modern technologies and best practices for scalable SaaS applications
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {features.map((feature, index) => (
            <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-shadow">
              <CardHeader>
                <feature.icon className="h-10 w-10 text-blue-600 mb-2" />
                <CardTitle className="text-lg">{feature.title}</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>{feature.description}</CardDescription>
              </CardContent>
            </Card>
          ))}
        </div>
      </section>

      {/* CTA Section */}
      <section className="container mx-auto px-4 py-20">
        <Card className="border-0 shadow-2xl bg-gradient-to-r from-blue-600 to-purple-600 text-white">
          <CardContent className="p-12 text-center">
            <h2 className="text-3xl font-bold mb-4">Ready to Get Started?</h2>
            <p className="text-xl mb-8 opacity-90">
              Join thousands of developers building amazing SaaS products
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" variant="secondary" asChild>
                <Link href="/auth/signup">Create Account</Link>
              </Button>
              <Button size="lg" variant="outline" className="bg-transparent border-white text-white hover:bg-white hover:text-blue-600" asChild>
                <Link href="/auth/signin">Sign In</Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </section>

      {/* Footer */}
      <footer className="border-t bg-white/80 backdrop-blur-sm dark:bg-gray-900/80">
        <div className="container mx-auto px-4 py-8">
          <div className="flex flex-col md:flex-row items-center justify-between">
            <div className="flex items-center space-x-2 mb-4 md:mb-0">
              <div className="h-6 w-6 bg-gradient-to-br from-blue-600 to-purple-600 rounded"></div>
              <span className="font-semibold">Your SaaS App</span>
            </div>
            <p className="text-sm text-muted-foreground">
              © 2024 Your SaaS App. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}