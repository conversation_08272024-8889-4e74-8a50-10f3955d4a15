import { ClassRepository } from '../../domain/ClassRepository';
import { Class as DomainClass } from '../../domain/Class';
import { db } from '@/lib/db';
import { classes } from '@/lib/db/schema';
import { eq } from 'drizzle-orm';

export class DrizzleClassRepository implements ClassRepository {
  async findById(id: string): Promise<DomainClass | null> {
    const row = await db.select().from(classes).where(eq(classes.id, id)).limit(1);
    if (!row[0]) return null;
    return this.mapRowToClass(row[0]);
  }

  async findAll(): Promise<DomainClass[]> {
    const rows = await db.select().from(classes);
    return rows.map(this.mapRowToClass);
  }

  async create(cls: DomainClass): Promise<DomainClass> {
    const [row] = await db.insert(classes).values({
      id: cls.id,
      name: cls.name,
      description: cls.description,
      categoryId: cls.category,
      tenantId: 1, // TODO: pass real tenantId
      is_active: cls.isActive,
      createdAt: cls.createdAt,
      updatedAt: cls.updatedAt,
    }).returning();
    return this.mapRowToClass(row);
  }

  async update(cls: DomainClass): Promise<DomainClass> {
    const [row] = await db.update(classes).set({
      name: cls.name,
      description: cls.description,
      categoryId: cls.category,
      is_active: cls.isActive,
      updatedAt: cls.updatedAt,
    }).where(eq(classes.id, cls.id)).returning();
    return this.mapRowToClass(row);
  }

  async delete(id: string): Promise<void> {
    await db.delete(classes).where(eq(classes.id, id));
  }

  private mapRowToClass(row: any): DomainClass {
    return new DomainClass(
      row.id,
      row.name,
      row.description,
      row.categoryId,
      row.createdAt,
      row.updatedAt,
      row.is_active,
      row.createdAt,
      row.updatedAt
    );
  }
}
