/**
 * Schedule Conflict Detection Types
 * 
 * Comprehensive TypeScript interfaces untuk handle semua skenario 
 * conflict detection dengan FAANG-level precision dan error handling.
 * 
 * Separated into dedicated file untuk better organization dan reusability.
 */

import { type ClassSchedule } from "@/lib/db/schema";

export interface ScheduleConflict {
  conflictingSchedule: ClassScheduleWithDetails;
  conflictType: ConflictType;
  conflictDetails: ConflictDetails;
  severity: ConflictSeverity;
}

export interface ClassScheduleWithDetails extends ClassSchedule {
  className?: string;
  locationName?: string;
  facilityName?: string;
  instructorName?: string;
}

export interface ConflictDetails {
  timeOverlap: {
    start: Date;
    end: Date;
    durationMinutes: number;
  };
  resourceConflict: {
    location?: string;
    facility?: string;
    staff?: string;
  };
  affectedCapacity?: {
    existingPax: number;
    newPax: number;
    totalCapacity?: number;
  };
}

export type ConflictType = 
  | 'EXACT_TIME_LOCATION'     // Same time, same location
  | 'EXACT_TIME_FACILITY'     // Same time, same facility  
  | 'PARTIAL_TIME_LOCATION'   // Overlapping time, same location
  | 'PARTIAL_TIME_FACILITY'   // Overlapping time, same facility
  | 'STAFF_DOUBLE_BOOKING'    // Same staff, overlapping time
  | 'LOCATION_CAPACITY'       // Location capacity exceeded
  | 'FACILITY_CAPACITY';      // Facility capacity exceeded

export type ConflictSeverity = 'BLOCKING' | 'WARNING' | 'INFO';

export interface ConflictValidationResult {
  hasConflicts: boolean;
  conflicts: ScheduleConflict[];
  canProceed: boolean;
  suggestions?: AlternativeTimeSlot[];
  warnings?: string[];
}

export interface AlternativeTimeSlot {
  suggestedStartTime: Date;
  suggestedEndTime: Date;
  availableLocation?: string;
  availableFacility?: string;
  reason: string;
  confidence: number; // 0-100, how good this suggestion is
}

export interface ConflictCheckParams {
  tenantId: number;
  startTime: Date;
  endTime: Date;
  locationId?: string;
  facilityId?: string;
  staffId?: string;
  excludeScheduleId?: string; // For updates, exclude current schedule
  bufferMinutes?: number; // Optional buffer time between classes
}

/**
 * Error types untuk conflict detection
 */
export class ScheduleConflictError extends Error {
  public readonly conflicts: ScheduleConflict[];
  public readonly suggestions?: AlternativeTimeSlot[];

  constructor(
    message: string, 
    conflicts: ScheduleConflict[], 
    suggestions?: AlternativeTimeSlot[]
  ) {
    super(message);
    this.name = 'ScheduleConflictError';
    this.conflicts = conflicts;
    this.suggestions = suggestions;
  }
}

/**
 * Configuration untuk conflict detection
 */
export interface ConflictDetectionConfig {
  enableBufferTime: boolean;
  defaultBufferMinutes: number;
  enableStaffConflictCheck: boolean;
  enableCapacityCheck: boolean;
  enableRecurringScheduleCheck: boolean;
  maxSuggestions: number;
}

/**
 * Default configuration
 */
export const DEFAULT_CONFLICT_CONFIG: ConflictDetectionConfig = {
  enableBufferTime: false,
  defaultBufferMinutes: 15,
  enableStaffConflictCheck: true,
  enableCapacityCheck: false,
  enableRecurringScheduleCheck: true,
  maxSuggestions: 5,
};

/**
 * Utility types untuk better type safety
 */
export type ConflictCheckResult = {
  success: true;
  data: ConflictValidationResult;
} | {
  success: false;
  error: string;
  details?: any;
};

/**
 * Time slot interface untuk suggestions
 */
export interface TimeSlot {
  start: Date;
  end: Date;
  available: boolean;
  conflictCount: number;
}

/**
 * Resource availability interface
 */
export interface ResourceAvailability {
  locationId?: string;
  facilityId?: string;
  staffId?: string;
  isAvailable: boolean;
  conflictingSchedules: string[]; // Schedule IDs that conflict
}

/**
 * Recurring schedule conflict interface
 */
export interface RecurringConflictInfo {
  parentScheduleId: string;
  recurrenceRule: string;
  conflictDates: Date[];
  totalConflicts: number;
}

/**
 * Conflict resolution strategy
 */
export type ConflictResolutionStrategy = 
  | 'SUGGEST_ALTERNATIVE_TIME'
  | 'SUGGEST_ALTERNATIVE_LOCATION'
  | 'SUGGEST_ALTERNATIVE_FACILITY'
  | 'SUGGEST_SPLIT_CLASS'
  | 'SUGGEST_DIFFERENT_DAY';

export interface ConflictResolution {
  strategy: ConflictResolutionStrategy;
  suggestion: AlternativeTimeSlot;
  reasoning: string;
  feasibilityScore: number; // 0-100
}

/**
 * Bulk conflict check untuk multiple schedules
 */
export interface BulkConflictCheckParams {
  schedules: Array<{
    id?: string; // For updates
    tenantId: number;
    startTime: Date;
    endTime: Date;
    locationId?: string;
    facilityId?: string;
    staffId?: string;
  }>;
  config?: Partial<ConflictDetectionConfig>;
}

export interface BulkConflictCheckResult {
  overallCanProceed: boolean;
  scheduleResults: Array<{
    scheduleIndex: number;
    result: ConflictValidationResult;
  }>;
  crossScheduleConflicts: ScheduleConflict[]; // Conflicts between the schedules being checked
}

/**
 * Performance monitoring untuk conflict detection
 */
export interface ConflictCheckPerformance {
  queryTime: number; // milliseconds
  analysisTime: number; // milliseconds
  totalTime: number; // milliseconds
  schedulesChecked: number;
  conflictsFound: number;
}

/**
 * Audit log untuk conflict detection
 */
export interface ConflictAuditLog {
  timestamp: Date;
  tenantId: number;
  userId?: string;
  action: 'CREATE' | 'UPDATE' | 'DELETE';
  scheduleId: string;
  conflictResult: ConflictValidationResult;
  performance: ConflictCheckPerformance;
  resolution?: 'PROCEEDED' | 'CANCELLED' | 'MODIFIED';
}
