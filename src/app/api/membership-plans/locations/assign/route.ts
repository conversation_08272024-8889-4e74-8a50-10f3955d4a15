import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { MembershipPlanLocationService } from "@/lib/services/membership-plan-location-service";
import { withRBAC } from "@/lib/middleware/rbac-middleware";
import { z } from "zod";

const assignLocationSchema = z.object({
  membershipPlanId: z.string().min(1, "Membership plan ID is required"),
  locationIds: z.array(z.string()).min(0, "Location IDs must be an array"),
  tenantId: z.number().int().positive("Valid tenant ID is required"),
});

async function assignToLocationsHandler(req: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user?.tenantId) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const body = await req.json();
    const validatedData = assignLocationSchema.parse(body);

    // Ensure user can only manage their own tenant's data
    const userTenantId = parseInt(session.user.tenantId);
    if (validatedData.tenantId !== userTenantId) {
      return NextResponse.json(
        { error: "Access denied to this tenant's data" },
        { status: 403 }
      );
    }

    const result = await MembershipPlanLocationService.assignToLocations(
      validatedData.membershipPlanId,
      validatedData.locationIds,
      validatedData.tenantId
    );

    return NextResponse.json({
      success: true,
      message: "Membership plan assigned to locations successfully",
      data: result,
    });
  } catch (error) {
    console.error("Error assigning membership plan to locations:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid request data", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: error instanceof Error ? error.message : "Failed to assign locations" },
      { status: 500 }
    );
  }
}

// Protect with RBAC - require packages.manage permission
export const POST = withRBAC(assignToLocationsHandler, {
  module: "packages",
  action: "manage"
});
