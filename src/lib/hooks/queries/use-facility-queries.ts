import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Facility, NewFacility } from '@/lib/db/schema';
import { createEntityHooks, BaseApiInterface } from '@/lib/core/base-query-hooks';
import { QueryOptions } from '@/lib/core/base-service';

/**
 * Facility Queries Hooks
 * 
 * Hooks untuk manage state dan data fetching facilities menggunakan TanStack Query.
 * Mengikuti pola modular architecture dengan createEntityHooks factory.
 * 
 * Features:
 * - Full CRUD operations dengan optimistic updates
 * - Tenant isolation dan proper caching
 * - Search dan filtering capabilities
 * - Bulk operations support
 * - Statistics dan reporting
 */

// API interface implementation
const facilityApi: BaseApiInterface<Facility, Omit<NewFacility, "id" | "createdAt" | "updatedAt">, Partial<NewFacility>> = {
  getAll: async (options?: QueryOptions): Promise<Facility[]> => {
    const params = new URLSearchParams();
    if (options?.tenantId) params.append('tenantId', options.tenantId.toString());
    if (options?.limit) params.append('limit', options.limit.toString());
    if (options?.offset) params.append('offset', options.offset.toString());
    if (options?.filters?.search) params.append('search', options.filters.search);
    if (options?.filters?.isActive !== undefined) params.append('activeOnly', options.filters.isActive.toString());

    const response = await fetch(`/api/facilities?${params}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch facilities: ${response.statusText}`);
    }
    const data = await response.json();
    return data.facilities || [];
  },

  getById: async (id: string): Promise<Facility> => {
    const response = await fetch(`/api/facilities/${id}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch facility: ${response.statusText}`);
    }
    const data = await response.json();
    return data.facility;
  },

  getByTenant: async (tenantId: number, options?: QueryOptions): Promise<Facility[]> => {
    const params = new URLSearchParams({
      tenantId: tenantId.toString(),
    });
    if (options?.limit) params.append('limit', options.limit.toString());
    if (options?.offset) params.append('offset', options.offset.toString());
    if (options?.filters?.search) params.append('search', options.filters.search);
    if (options?.filters?.isActive !== undefined) params.append('activeOnly', options.filters.isActive.toString());

    const response = await fetch(`/api/facilities?${params}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch facilities: ${response.statusText}`);
    }
    const data = await response.json();
    return data.facilities || [];
  },

  create: async (data: Omit<NewFacility, "id" | "createdAt" | "updatedAt">): Promise<Facility> => {
    console.log('Creating facility with data:', data);
    const response = await fetch('/api/facilities', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    console.log('Create facility response status:', response.status);
    if (!response.ok) {
      let errorMessage = 'Failed to create facility';
      try {
        const errorData = await response.json();
        console.log('Create facility error data:', errorData);
        errorMessage = errorData.error || errorMessage;
      } catch (jsonError) {
        console.log('Failed to parse error response as JSON:', jsonError);
        // If response is not JSON, use status text
        errorMessage = response.statusText || errorMessage;
      }
      throw new Error(errorMessage);
    }
    const result = await response.json();
    console.log('Create facility success result:', result);
    return result.facility;
  },

  update: async ({ id, data }: { id: string; data: Partial<NewFacility> }): Promise<Facility> => {
    console.log('Updating facility with id:', id, 'data:', data);
    console.log('Stringified data:', JSON.stringify(data));

    const response = await fetch(`/api/facilities/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    console.log('Update facility response status:', response.status);
    console.log('Update facility response headers:', Object.fromEntries(response.headers.entries()));
    if (!response.ok) {
      let errorMessage = 'Failed to update facility';
      try {
        const errorData = await response.json();
        console.log('Update facility error data:', errorData);
        errorMessage = errorData.error || errorMessage;
      } catch (jsonError) {
        console.log('Failed to parse error response as JSON:', jsonError);
        // If response is not JSON, use status text
        errorMessage = response.statusText || errorMessage;
      }
      throw new Error(errorMessage);
    }
    const result = await response.json();
    console.log('Update facility success result:', result);
    return result.facility;
  },

  delete: async (id: string): Promise<Facility> => {
    const response = await fetch(`/api/facilities/${id}`, {
      method: 'DELETE',
    });
    if (!response.ok) {
      let errorMessage = 'Failed to delete facility';
      try {
        const errorData = await response.json();
        errorMessage = errorData.error || errorMessage;
      } catch (jsonError) {
        // If response is not JSON, use status text
        errorMessage = response.statusText || errorMessage;
      }
      throw new Error(errorMessage);
    }
    const result = await response.json();
    return result.facility;
  },

  bulkOperation: async (data: any): Promise<Facility[]> => {
    const response = await fetch('/api/facilities', {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to perform bulk operation');
    }
    const result = await response.json();
    return result.facilities || [];
  },

  getStats: async (tenantId: number): Promise<any> => {
    const response = await fetch(`/api/facilities/stats?tenantId=${tenantId}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch facility stats: ${response.statusText}`);
    }
    const data = await response.json();
    return data.stats;
  },
};

// Create base hooks using the factory
const baseHooks = createEntityHooks('facilities', facilityApi);

// Export base hooks with facility-specific names
export const useFacilities = baseHooks.useEntities;
export const useFacility = baseHooks.useEntity;
export const useFacilitiesByTenant = baseHooks.useEntitiesByTenant;
export const useFacilityStats = baseHooks.useEntityStats;
export const useFacilitySearch = baseHooks.useEntitySearch;
export const useCreateFacility = baseHooks.useCreateEntity;
export const useUpdateFacility = baseHooks.useUpdateEntity;
export const useDeleteFacility = baseHooks.useDeleteEntity;
export const useBulkFacilityOperation = baseHooks.useBulkOperation;

// Additional facility-specific hooks
export function useToggleFacilityActive() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string): Promise<Facility> => {
      const response = await fetch(`/api/facilities/${id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ action: 'toggle-status' }),
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to toggle facility status');
      }
      const result = await response.json();
      return result.facility;
    },
    onSuccess: (updatedFacility) => {
      // Update cache with updated facility
      queryClient.setQueryData(baseHooks.queryKeys.detail(updatedFacility.id), updatedFacility);

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: baseHooks.queryKeys.lists() });
      if (updatedFacility.tenantId) {
        queryClient.invalidateQueries({
          queryKey: baseHooks.queryKeys.byTenant(updatedFacility.tenantId)
        });
        queryClient.invalidateQueries({
          queryKey: baseHooks.queryKeys.stats(updatedFacility.tenantId)
        });
      }
      queryClient.invalidateQueries({ queryKey: baseHooks.queryKeys.stats() });
    },
  });
}

// Search facilities with custom parameters
export function useFacilitySearchWithParams() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (params: {
      tenantId: number;
      searchTerm?: string;
      activeOnly?: boolean;
      limit?: number;
      offset?: number;
    }): Promise<Facility[]> => {
      const searchParams = new URLSearchParams({
        tenantId: params.tenantId.toString(),
      });
      
      if (params.searchTerm) searchParams.append('search', params.searchTerm);
      if (params.activeOnly !== undefined) searchParams.append('activeOnly', params.activeOnly.toString());
      if (params.limit) searchParams.append('limit', params.limit.toString());
      if (params.offset) searchParams.append('offset', params.offset.toString());

      const response = await fetch(`/api/facilities?${searchParams}`);
      if (!response.ok) {
        throw new Error(`Failed to search facilities: ${response.statusText}`);
      }
      const data = await response.json();
      return data.facilities || [];
    },
  });
}

// Bulk create facilities
export function useBulkCreateFacilities() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (facilitiesData: Omit<NewFacility, "id" | "createdAt" | "updatedAt">[]): Promise<Facility[]> => {
      const response = await fetch('/api/facilities/bulk', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ facilities: facilitiesData }),
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to bulk create facilities');
      }
      const result = await response.json();
      return result.facilities;
    },
    onSuccess: (newFacilities) => {
      // Invalidate all facility-related queries
      queryClient.invalidateQueries({ queryKey: baseHooks.queryKeys.all });
      
      // If all facilities belong to the same tenant, invalidate tenant-specific queries
      const tenantIds = [...new Set(newFacilities.map(facility => facility.tenantId).filter(Boolean))];
      tenantIds.forEach(tenantId => {
        queryClient.invalidateQueries({ queryKey: baseHooks.queryKeys.byTenant(tenantId!) });
        queryClient.invalidateQueries({ queryKey: baseHooks.queryKeys.stats(tenantId!) });
      });
    },
  });
}

// Export query keys for external use
export const facilityQueryKeys = baseHooks.queryKeys;

// Type definitions for form data
export type FacilityFormData = Omit<NewFacility, "id" | "createdAt" | "updatedAt">;
export type FacilityUpdateData = Partial<Omit<NewFacility, "id" | "tenantId" | "createdAt">>;

// Export API for external use if needed
export { facilityApi };
