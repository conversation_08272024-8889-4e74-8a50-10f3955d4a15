import { SignJWT, jwtVerify, JWTPayload, errors } from "jose";
import { createHash, randomBytes } from "crypto";
import { z } from "zod";
import { createId } from "@paralleldrive/cuid2";
import { customerSessions, customerAuthLogs, db } from "@/lib/db";
import { eq, and, lt, gt } from "drizzle-orm";

/**
 * Customer JWT Service
 * 
 * Handles JWT token generation, validation, and session management for customers
 * FAANG-level implementation with proper security and audit logging
 */

// Environment validation
const jwtConfig = z.object({
  secret: z.string().min(32, "JWT_SECRET must be at least 32 characters"),
  issuer: z.string().default("saas-app"),
  audience: z.string().default("customer"),
  accessTokenExpiry: z.string().default("1h"),
  refreshTokenExpiry: z.string().default("30d"),
}).parse({
  secret: process.env.JWT_SECRET || process.env.NEXTAUTH_SECRET,
  issuer: process.env.JWT_ISSUER,
  audience: process.env.JWT_AUDIENCE,
  accessTokenExpiry: process.env.JWT_ACCESS_TOKEN_EXPIRY,
  refreshTokenExpiry: process.env.JWT_REFRESH_TOKEN_EXPIRY,
});

// Validation schemas
export const tokenGenerationSchema = z.object({
  customerId: z.string().min(1),
  tenantId: z.number().int().positive(),
  deviceType: z.enum(["web", "mobile", "tablet"]).optional(),
  deviceId: z.string().optional(),
  userAgent: z.string().optional(),
  ipAddress: z.string().ip(),
  oauthProvider: z.enum(["google", "credentials"]).optional(),
  oauthAccountId: z.string().optional(),
});

export const tokenValidationSchema = z.object({
  token: z.string().min(1),
  tenantId: z.number().int().positive().optional(),
});

export interface CustomerJWTPayload extends JWTPayload {
  sub: string; // customer ID
  tenantId: number;
  email: string;
  firstName: string;
  lastName: string;
  displayName: string;
  image?: string;
  membershipType: string;
  isEmailVerified: boolean;
  jti: string; // JWT ID for revocation
  iat: number;
  exp: number;
  iss: string;
  aud: string;
  sessionId: string;
}

export interface TokenPair {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
  refreshExpiresIn: number;
  tokenType: "Bearer";
}

export interface TokenValidationResult {
  valid: boolean;
  payload?: CustomerJWTPayload;
  error?: string;
  errorCode?: string;
}

class CustomerJWTService {
  /**
   * Generate JWT token pair for customer
   */
  async generateTokenPair(
    customer: {
      id: string;
      email: string;
      firstName: string;
      lastName: string;
      displayName: string;
      image?: string | null;
      tenantId: number;
      membershipType: string;
      isEmailVerified: boolean;
    },
    sessionParams: z.infer<typeof tokenGenerationSchema>
  ): Promise<TokenPair> {
    const validated = tokenGenerationSchema.parse(sessionParams);
    
    // Generate unique identifiers
    const jti = createId(); // JWT ID for revocation
    const sessionId = createId();
    const refreshTokenId = createId();

    // Calculate expiry times
    const now = Math.floor(Date.now() / 1000);
    const accessTokenExpiry = this.parseExpiry(jwtConfig.accessTokenExpiry);
    const refreshTokenExpiry = this.parseExpiry(jwtConfig.refreshTokenExpiry);

    // Create JWT payload
    const payload: CustomerJWTPayload = {
      sub: customer.id,
      tenantId: customer.tenantId,
      email: customer.email,
      firstName: customer.firstName,
      lastName: customer.lastName,
      displayName: customer.displayName,
      image: customer.image || undefined,
      membershipType: customer.membershipType,
      isEmailVerified: customer.isEmailVerified,
      jti,
      iat: now,
      exp: now + accessTokenExpiry,
      iss: jwtConfig.issuer,
      aud: jwtConfig.audience,
      sessionId,
    };

    // Generate tokens using jose
    const secret = new TextEncoder().encode(jwtConfig.secret);

    const accessToken = await new SignJWT(payload)
      .setProtectedHeader({ alg: "HS256" })
      .setIssuedAt()
      .setExpirationTime(Math.floor((now + accessTokenExpiry) / 1000))
      .setIssuer(jwtConfig.issuer)
      .setAudience(jwtConfig.audience)
      .sign(secret);

    const refreshTokenPayload = {
      sub: customer.id,
      tenantId: customer.tenantId,
      jti: refreshTokenId,
      sessionId,
      type: "refresh",
    };

    const refreshToken = await new SignJWT(refreshTokenPayload)
      .setProtectedHeader({ alg: "HS256" })
      .setIssuedAt()
      .setExpirationTime(Math.floor((now + refreshTokenExpiry) / 1000))
      .setIssuer(jwtConfig.issuer)
      .setAudience(jwtConfig.audience)
      .sign(secret);

    // Store session in database
    await this.createSession({
      sessionId,
      customerId: customer.id,
      tenantId: customer.tenantId,
      jti,
      tokenHash: this.hashToken(accessToken),
      refreshTokenHash: this.hashToken(refreshToken),
      expiresAt: new Date((now + accessTokenExpiry) * 1000),
      refreshExpiresAt: new Date((now + refreshTokenExpiry) * 1000),
      deviceType: validated.deviceType,
      deviceId: validated.deviceId,
      userAgent: validated.userAgent,
      ipAddress: validated.ipAddress,
      oauthProvider: validated.oauthProvider,
      oauthAccountId: validated.oauthAccountId,
    });

    // Log successful token generation
    await this.logAuthEvent({
      customerId: customer.id,
      tenantId: customer.tenantId,
      event: "token_generated",
      method: validated.oauthProvider || "credentials",
      status: "success",
      sessionId,
      ipAddress: validated.ipAddress,
      userAgent: validated.userAgent,
      deviceType: validated.deviceType,
      deviceId: validated.deviceId,
    });

    return {
      accessToken,
      refreshToken,
      expiresIn: accessTokenExpiry,
      refreshExpiresIn: refreshTokenExpiry,
      tokenType: "Bearer",
    };
  }

  /**
   * Validate JWT token
   */
  async validateToken(params: z.infer<typeof tokenValidationSchema>): Promise<TokenValidationResult> {
    try {
      const validated = tokenValidationSchema.parse(params);
      
      // Verify JWT signature and decode using jose
      const secret = new TextEncoder().encode(jwtConfig.secret);
      const { payload } = await jwtVerify(validated.token, secret, {
        algorithms: ["HS256"],
        issuer: jwtConfig.issuer,
        audience: jwtConfig.audience,
      });

      const decoded = payload as CustomerJWTPayload;

      // Check tenant context if provided
      if (validated.tenantId && decoded.tenantId !== validated.tenantId) {
        return {
          valid: false,
          error: "Token tenant mismatch",
          errorCode: "TENANT_MISMATCH",
        };
      }

      // Check if session exists and is active
      const [session] = await db
        .select()
        .from(customerSessions)
        .where(and(
          eq(customerSessions.jti, decoded.jti),
          eq(customerSessions.customerId, decoded.sub),
          eq(customerSessions.tenantId, decoded.tenantId),
          eq(customerSessions.isActive, true),
          gt(customerSessions.expiresAt, new Date())
        ))
        .limit(1);

      if (!session) {
        return {
          valid: false,
          error: "Session not found or expired",
          errorCode: "SESSION_INVALID",
        };
      }

      // Verify token hash matches stored hash
      const tokenHash = this.hashToken(validated.token);
      if (session.tokenHash !== tokenHash) {
        return {
          valid: false,
          error: "Token hash mismatch",
          errorCode: "TOKEN_INVALID",
        };
      }

      // Update last used timestamp
      await db
        .update(customerSessions)
        .set({ lastUsedAt: new Date() })
        .where(eq(customerSessions.id, session.id));

      return {
        valid: true,
        payload: decoded,
      };

    } catch (error) {
      if (error instanceof errors.JWTInvalid) {
        return {
          valid: false,
          error: "Invalid token",
          errorCode: "TOKEN_INVALID",
        };
      }

      if (error instanceof errors.JWTExpired) {
        return {
          valid: false,
          error: "Token expired",
          errorCode: "TOKEN_EXPIRED",
        };
      }

      if (error instanceof errors.JWTClaimValidationFailed) {
        return {
          valid: false,
          error: "Token claim validation failed",
          errorCode: "TOKEN_INVALID",
        };
      }

      console.error("Token validation error:", error);
      return {
        valid: false,
        error: "Token validation failed",
        errorCode: "VALIDATION_ERROR",
      };
    }
  }

  /**
   * Refresh access token using refresh token
   */
  async refreshToken(refreshToken: string, ipAddress: string, userAgent?: string): Promise<TokenPair | null> {
    try {
      // Verify refresh token using jose
      const secret = new TextEncoder().encode(jwtConfig.secret);
      const { payload } = await jwtVerify(refreshToken, secret, {
        algorithms: ["HS256"],
        issuer: jwtConfig.issuer,
        audience: jwtConfig.audience,
      });

      const decoded = payload as any;

      if (decoded.type !== "refresh") {
        throw new Error("Invalid refresh token type");
      }

      // Find session
      const [session] = await db
        .select()
        .from(customerSessions)
        .where(and(
          eq(customerSessions.id, decoded.sessionId),
          eq(customerSessions.customerId, decoded.sub),
          eq(customerSessions.tenantId, decoded.tenantId),
          eq(customerSessions.isActive, true),
          gt(customerSessions.refreshExpiresAt, new Date())
        ))
        .limit(1);

      if (!session) {
        return null;
      }

      // Verify refresh token hash
      const refreshTokenHash = this.hashToken(refreshToken);
      if (session.refreshTokenHash !== refreshTokenHash) {
        return null;
      }

      // Get customer data for new token
      const [customer] = await db
        .select({
          id: customerSessions.customerId,
          email: customerSessions.customerId, // Will need to join with customers table
          // ... other fields needed
        })
        .from(customerSessions)
        .where(eq(customerSessions.id, session.id))
        .limit(1);

      // For now, return null - full implementation needs customer data join
      return null;

    } catch (error) {
      console.error("Refresh token error:", error);
      return null;
    }
  }

  /**
   * Revoke session/token
   */
  async revokeSession(sessionId: string, revokedBy: string, reason: string): Promise<boolean> {
    try {
      const result = await db
        .update(customerSessions)
        .set({
          isActive: false,
          revokedAt: new Date(),
          revokedBy,
          revokedReason: reason,
          updatedAt: new Date(),
        })
        .where(eq(customerSessions.id, sessionId));

      return (result.rowCount || 0) > 0;
    } catch (error) {
      console.error("Session revocation error:", error);
      return false;
    }
  }

  /**
   * Clean up expired sessions
   */
  async cleanupExpiredSessions(): Promise<number> {
    const result = await db
      .delete(customerSessions)
      .where(lt(customerSessions.expiresAt, new Date()));

    return result.rowCount || 0;
  }

  // Private helper methods
  private hashToken(token: string): string {
    return createHash("sha256").update(token).digest("hex");
  }

  private parseExpiry(expiry: string): number {
    const match = expiry.match(/^(\d+)([smhd])$/);
    if (!match) return 3600; // Default 1 hour

    const [, value, unit] = match;
    const multipliers = { s: 1, m: 60, h: 3600, d: 86400 };
    return parseInt(value) * (multipliers[unit as keyof typeof multipliers] || 3600);
  }

  private async createSession(params: any): Promise<void> {
    await db.insert(customerSessions).values({
      id: createId(),
      ...params,
    });
  }

  private async logAuthEvent(params: any): Promise<void> {
    await db.insert(customerAuthLogs).values({
      id: createId(),
      ...params,
      createdAt: new Date(),
    });
  }
}

export const customerJWTService = new CustomerJWTService();
