"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { 
  Wrench, 
  Plus, 
  Building, 
  BarChart3,
  AlertCircle,
  CheckCircle,
  Package
} from "lucide-react";
import { EquipmentForm } from "@/components/forms/equipment-form";
import { SuccessToast } from "@/components/ui/success-toast";
import { SuccessModal } from "@/components/ui/success-modal";
import { Confetti } from "@/components/ui/confetti";
import { Equipment, NewEquipment } from "@/lib/db/schema";
import { useEquipments, useCreateEquipment, useUpdateEquipment } from "@/lib/hooks/queries/use-equipment-queries";
import { useTenants } from "@/lib/hooks/queries/use-tenant-queries";
import { EquipmentList } from "@/components/equipments/equipment-list";

export default function EquipmentPage() {
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingEquipment, setEditingEquipment] = useState<Equipment | null>(null);
  const [selectedTenantId, setSelectedTenantId] = useState<number | null>(null);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [submitSuccess, setSubmitSuccess] = useState<string | null>(null);
  
  // Animation states
  const [showSuccessToast, setShowSuccessToast] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showConfetti, setShowConfetti] = useState(false);
  const [createdEquipment, setCreatedEquipment] = useState<Equipment | null>(null);

  const { data: equipments = [], isLoading: equipmentsLoading } = useEquipments();
  const { data: tenantsData, isLoading: tenantsLoading } = useTenants();
  const tenants = tenantsData?.tenants || [];
  const createEquipmentMutation = useCreateEquipment();
  const updateEquipmentMutation = useUpdateEquipment();

  // Calculate stats
  const stats = {
    total: equipments.length,
    byTenant: tenants.reduce((acc, tenant) => {
      acc[tenant.id] = equipments.filter(eq => eq.tenantId === tenant.id).length;
      return acc;
    }, {} as Record<number, number>),
  };

  const handleCreateEquipment = async (data: Omit<NewEquipment, "id" | "createdAt" | "updatedAt">) => {
    try {
      setSubmitError(null);
      const newEquipment = await createEquipmentMutation.mutateAsync(data);
      
      // Store created equipment for modal
      setCreatedEquipment(newEquipment);
      
      // Close form first
      setIsFormOpen(false);
      
      // Start celebration sequence
      setTimeout(() => {
        setShowConfetti(true);
        setShowSuccessToast(true);
        setShowSuccessModal(true);
      }, 300);
      
      // Auto-hide confetti
      setTimeout(() => setShowConfetti(false), 4000);
      
    } catch (error) {
      setSubmitError(error instanceof Error ? error.message : "Failed to create equipment");
    }
  };

  const handleUpdateEquipment = async (data: Omit<NewEquipment, "id" | "createdAt" | "updatedAt">) => {
    if (!editingEquipment) return;

    try {
      setSubmitError(null);
      await updateEquipmentMutation.mutateAsync({ 
        equipmentId: editingEquipment.id, 
        data: data
      });
      setSubmitSuccess("Equipment updated successfully!");
      setEditingEquipment(null);
      setTimeout(() => setSubmitSuccess(null), 3000);
    } catch (error) {
      setSubmitError(error instanceof Error ? error.message : "Failed to update equipment");
    }
  };

  const handleEdit = (equipment: Equipment) => {
    setEditingEquipment(equipment);
  };

  const handleAdd = () => {
    setEditingEquipment(null);
    setIsFormOpen(true);
  };

  const handleCloseForm = () => {
    setIsFormOpen(false);
    setEditingEquipment(null);
    setSubmitError(null);
  };

  const handleCloseSuccessAnimations = () => {
    setShowSuccessToast(false);
    setShowSuccessModal(false);
    setShowConfetti(false);
    setCreatedEquipment(null);
  };

  const handleViewEquipment = () => {
    handleCloseSuccessAnimations();
    // Could navigate to equipment detail or highlight in list
  };

  const handleAddAnother = () => {
    handleCloseSuccessAnimations();
    setIsFormOpen(true);
  };

  if (equipmentsLoading || tenantsLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Equipment</h1>
          <p className="text-gray-500 dark:text-gray-400">
            Manage equipment inventory and information
          </p>
        </div>
        <Button onClick={handleAdd}>
          <Plus className="h-4 w-4 mr-2" />
          Add Equipment
        </Button>
      </div>

      {/* Success/Error Messages */}
      {submitSuccess && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>{submitSuccess}</AlertDescription>
        </Alert>
      )}

      {submitError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{submitError}</AlertDescription>
        </Alert>
      )}

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Equipment</CardTitle>
            <Wrench className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">
              Across all tenants
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Tenants</CardTitle>
            <Building className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Object.values(stats.byTenant).filter(count => count > 0).length}
            </div>
            <p className="text-xs text-muted-foreground">
              With equipment
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average per Tenant</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {tenants.length > 0 ? (stats.total / tenants.length).toFixed(1) : "0"}
            </div>
            <p className="text-xs text-muted-foreground">
              Equipment per tenant
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="all" className="space-y-4">
        <TabsList>
          <TabsTrigger value="all">All Equipment</TabsTrigger>
          {tenants.map((tenant) => (
            <TabsTrigger key={tenant.id} value={`tenant-${tenant.id}`}>
              {tenant.name}
              <Badge variant="secondary" className="ml-2">
                {stats.byTenant[tenant.id] || 0}
              </Badge>
            </TabsTrigger>
          ))}
        </TabsList>

        <TabsContent value="all">
          <EquipmentList
            onEdit={handleEdit}
            onAdd={handleAdd}
            showActions={true}
          />
        </TabsContent>

        {tenants.map((tenant) => (
          <TabsContent key={tenant.id} value={`tenant-${tenant.id}`}>
            <EquipmentList
              tenantId={tenant.id}
              onEdit={handleEdit}
              onAdd={() => {
                setSelectedTenantId(tenant.id);
                handleAdd();
              }}
              showActions={true}
            />
          </TabsContent>
        ))}
      </Tabs>

      {/* Create/Edit Dialog */}
      <Dialog open={isFormOpen || !!editingEquipment} onOpenChange={handleCloseForm}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editingEquipment ? "Edit Equipment" : "Add New Equipment"}
            </DialogTitle>
          </DialogHeader>
          <EquipmentForm
            equipment={editingEquipment || undefined}
            tenantId={selectedTenantId || editingEquipment?.tenantId || (tenants[0]?.id ?? 1)}
            onSubmit={editingEquipment ? handleUpdateEquipment : handleCreateEquipment}
            onCancel={handleCloseForm}
            className="border-0 shadow-none"
          />
        </DialogContent>
      </Dialog>

      {/* Success Animations */}
      <Confetti isActive={showConfetti} />
      
      <SuccessToast
        isVisible={showSuccessToast}
        onClose={() => setShowSuccessToast(false)}
        title="Equipment Created Successfully!"
        description={createdEquipment ? `${createdEquipment.name} has been added to your equipment inventory.` : undefined}
        type="general"
        action={{
          label: "View All Equipment",
          onClick: handleViewEquipment
        }}
      />

      <SuccessModal
        isOpen={showSuccessModal}
        onClose={handleCloseSuccessAnimations}
        title="🎉 Equipment Added Successfully!"
        description="Your new equipment has been saved and is now available in your equipment inventory."
        locationData={createdEquipment ? {
          address: createdEquipment.name || "",
          city: createdEquipment.default_display_name || "Equipment",
          country: `ID: ${createdEquipment.id.slice(-8)}`
        } : undefined}
        actions={{
          primary: {
            label: "Add Another Equipment",
            onClick: handleAddAnother
          },
          secondary: {
            label: "View All Equipment",
            onClick: handleViewEquipment
          }
        }}
      />
    </div>
  );
}
