import { createId } from "@paralleldrive/cuid2";

export interface UploadResult {
  url: string;
  filename: string;
  size: number;
  type: string;
}

export class FileUploadService {
  private static readonly MAX_FILE_SIZE = 2 * 1024 * 1024; // 2MB
  private static readonly ALLOWED_TYPES = [
    "image/jpeg",
    "image/jpg", 
    "image/png",
    "image/webp",
    "image/gif"
  ];

  /**
   * Validate file before upload
   */
  static validateFile(file: File): { valid: boolean; error?: string } {
    // Check file size
    if (file.size > this.MAX_FILE_SIZE) {
      return {
        valid: false,
        error: `File size must be less than ${this.MAX_FILE_SIZE / 1024 / 1024}MB`
      };
    }

    // Check file type
    if (!this.ALLOWED_TYPES.includes(file.type)) {
      return {
        valid: false,
        error: "File type not supported. Please use JPG, PNG, WebP, or GIF."
      };
    }

    return { valid: true };
  }

  /**
   * Upload file to storage service
   * In production, this would upload to AWS S3, Cloudflare R2, etc.
   */
  static async uploadFile(
    file: File,
    folder: string = "uploads"
  ): Promise<UploadResult> {
    // Validate file first
    const validation = this.validateFile(file);
    if (!validation.valid) {
      throw new Error(validation.error);
    }

    try {
      // Generate unique filename
      const fileExtension = file.name.split('.').pop();
      const uniqueFilename = `${createId()}.${fileExtension}`;
      const filePath = `${folder}/${uniqueFilename}`;

      // In production, implement actual upload to your storage service
      // For now, we'll simulate the upload and return a mock URL
      await this.simulateUpload(file);

      return {
        url: `/api/files/${filePath}`,
        filename: uniqueFilename,
        size: file.size,
        type: file.type,
      };
    } catch (error) {
      throw new Error(`Upload failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Upload business logo
   */
  static async uploadBusinessLogo(file: File, tenantId: number): Promise<UploadResult> {
    return await this.uploadFile(file, `logos/tenant-${tenantId}`);
  }

  /**
   * Delete file from storage
   */
  static async deleteFile(url: string): Promise<boolean> {
    try {
      // In production, implement actual file deletion from your storage service
      console.log(`Deleting file: ${url}`);
      return true;
    } catch (error) {
      console.error("Error deleting file:", error);
      return false;
    }
  }

  /**
   * Get file info from URL
   */
  static getFileInfo(url: string): { filename: string; folder: string } | null {
    try {
      const urlParts = url.split('/');
      const filename = urlParts[urlParts.length - 1];
      const folder = urlParts[urlParts.length - 2];
      
      return { filename, folder };
    } catch {
      return null;
    }
  }

  /**
   * Resize image (client-side)
   */
  static async resizeImage(
    file: File,
    maxWidth: number = 400,
    maxHeight: number = 400,
    quality: number = 0.8
  ): Promise<File> {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // Calculate new dimensions
        let { width, height } = img;
        
        if (width > height) {
          if (width > maxWidth) {
            height = (height * maxWidth) / width;
            width = maxWidth;
          }
        } else {
          if (height > maxHeight) {
            width = (width * maxHeight) / height;
            height = maxHeight;
          }
        }

        // Set canvas dimensions
        canvas.width = width;
        canvas.height = height;

        // Draw and compress
        ctx?.drawImage(img, 0, 0, width, height);
        
        canvas.toBlob(
          (blob) => {
            if (blob) {
              const resizedFile = new File([blob], file.name, {
                type: file.type,
                lastModified: Date.now(),
              });
              resolve(resizedFile);
            } else {
              reject(new Error('Failed to resize image'));
            }
          },
          file.type,
          quality
        );
      };

      img.onerror = () => reject(new Error('Failed to load image'));
      img.src = URL.createObjectURL(file);
    });
  }

  /**
   * Convert file to base64
   */
  static async fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = error => reject(error);
    });
  }

  /**
   * Generate thumbnail
   */
  static async generateThumbnail(
    file: File,
    size: number = 150
  ): Promise<string> {
    const resized = await this.resizeImage(file, size, size, 0.7);
    return await this.fileToBase64(resized);
  }

  /**
   * Simulate file upload (for development)
   */
  private static async simulateUpload(file: File): Promise<void> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
    
    // Simulate occasional failures (5% chance)
    if (Math.random() < 0.05) {
      throw new Error('Network error during upload');
    }
  }

  /**
   * Get supported file types for display
   */
  static getSupportedTypes(): string[] {
    return [...this.ALLOWED_TYPES];
  }

  /**
   * Format file size for display
   */
  static formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Check if file is an image
   */
  static isImage(file: File): boolean {
    return file.type.startsWith('image/');
  }

  /**
   * Get image dimensions
   */
  static async getImageDimensions(file: File): Promise<{ width: number; height: number }> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        resolve({ width: img.naturalWidth, height: img.naturalHeight });
      };
      img.onerror = () => reject(new Error('Failed to load image'));
      img.src = URL.createObjectURL(file);
    });
  }
}
