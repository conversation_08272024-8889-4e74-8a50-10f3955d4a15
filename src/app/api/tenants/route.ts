import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { TenantService } from "@/lib/services/tenant.service";
import { z } from "zod";

// Validation schema
const createTenantSchema = z.object({
  name: z.string().min(1, "Name is required").max(255, "Name too long"),
  subdomain: z.string()
    .min(3, "Subdomain must be at least 3 characters")
    .max(63, "Subdomain too long")
    .regex(/^[a-z0-9-]+$/, "Subdomain can only contain lowercase letters, numbers, and hyphens"),
  organizationId: z.string().optional(),
  customDomain: z.string().optional(),
  settings: z.record(z.any()).optional(),
});

const updateTenantSchema = createTenantSchema.partial();

// GET /api/tenants - List tenants
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const organizationId = searchParams.get("organizationId");
    const search = searchParams.get("search");

    let tenants;

    if (search) {
      tenants = await TenantService.searchTenants(search, organizationId || undefined);
    } else if (organizationId) {
      tenants = await TenantService.getTenantsByOrganization(organizationId);
    } else {
      // For admin users, return all tenants
      // For regular users, return tenants from their organization
      if (session.user.role === "admin") {
        tenants = await TenantService.searchTenants(""); // Get all
      } else {
        tenants = organizationId 
          ? await TenantService.getTenantsByOrganization(organizationId)
          : [];
      }
    }

    return NextResponse.json({ tenants });
  } catch (error) {
    console.error("Error fetching tenants:", error);
    return NextResponse.json(
      { error: "Failed to fetch tenants" },
      { status: 500 }
    );
  }
}

// POST /api/tenants - Create tenant
export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const body = await request.json();
    const validatedData = createTenantSchema.parse(body);

    // Use user's organization if not specified
    if (!validatedData.organizationId && session.user.organizationId) {
      validatedData.organizationId = session.user.organizationId;
    }

    const tenant = await TenantService.createTenant(validatedData);

    return NextResponse.json({ tenant }, { status: 201 });
  } catch (error) {
    console.error("Error creating tenant:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 }
      );
    }

    if (error instanceof Error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to create tenant" },
      { status: 500 }
    );
  }
}
