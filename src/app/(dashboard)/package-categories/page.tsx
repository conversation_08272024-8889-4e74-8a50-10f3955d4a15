"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { 
  Package, 
  Plus, 
  Building, 
  BarChart3,
  AlertCircle,
  CheckCircle,
  TrendingUp,
  Users
} from "lucide-react";
import { PackageCategoryForm } from "@/components/forms/package-category-form";
import { PackageCategoryList } from "@/components/package-categories/package-category-list";
import { SuccessToast } from "@/components/ui/success-toast";
import { SuccessModal } from "@/components/ui/success-modal";
import { Confetti } from "@/components/ui/confetti";
import { PackageCategory } from "@/lib/db/schema";
import { 
  usePackageCategories, 
  useCreatePackageCategory, 
  useUpdatePackageCategory,
  usePackageCategoryStats,
  CreatePackageCategoryData,
  UpdatePackageCategoryData
} from "@/lib/hooks/queries/use-package-category-queries";
import { useTenants } from "@/lib/hooks/queries/use-tenant-queries";

export default function PackageCategoriesPage() {
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingCategory, setEditingCategory] = useState<PackageCategory | null>(null);
  const [selectedTenantId, setSelectedTenantId] = useState<number | null>(null);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [submitSuccess, setSubmitSuccess] = useState<string | null>(null);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showConfetti, setShowConfetti] = useState(false);

  // Fetch data
  const { data: packageCategories = [], isLoading: categoriesLoading } = usePackageCategories({ tenantId: 1 });
  const { data: tenantsData, isLoading: tenantsLoading } = useTenants();
  const { data: stats = { total: 0, byTenant: {} }, isLoading: statsLoading } = usePackageCategoryStats(1);
  
  const tenants = tenantsData?.tenants || [];
  const createCategoryMutation = useCreatePackageCategory();
  const updateCategoryMutation = useUpdatePackageCategory();

  const handleCreateCategory = async (data: CreatePackageCategoryData) => {
    try {
      setSubmitError(null);
      await createCategoryMutation.mutateAsync(data);
      setSubmitSuccess("Package category created successfully!");
      setIsFormOpen(false);
      setShowSuccessModal(true);
      setShowConfetti(true);
      setTimeout(() => {
        setShowSuccessModal(false);
        setShowConfetti(false);
        setSubmitSuccess(null);
      }, 3000);
    } catch (error) {
      setSubmitError(error instanceof Error ? error.message : "Failed to create package category");
    }
  };

  const handleUpdateCategory = async (data: UpdatePackageCategoryData) => {
    if (!editingCategory) return;

    try {
      setSubmitError(null);
      await updateCategoryMutation.mutateAsync({ 
        id: editingCategory.id, 
        data: data
      });
      setSubmitSuccess("Package category updated successfully!");
      setEditingCategory(null);
      setTimeout(() => setSubmitSuccess(null), 3000);
    } catch (error) {
      setSubmitError(error instanceof Error ? error.message : "Failed to update package category");
    }
  };

  const handleEdit = (packageCategory: PackageCategory) => {
    setEditingCategory(packageCategory);
  };

  const handleAdd = () => {
    setEditingCategory(null);
    setIsFormOpen(true);
  };

  const handleCloseForm = () => {
    setIsFormOpen(false);
    setEditingCategory(null);
    setSubmitError(null);
  };

  // Calculate stats by tenant
  const statsByTenant = tenants.reduce((acc, tenant) => {
    const tenantCategories = packageCategories.filter(c => c.tenantId === tenant.id);
    acc[tenant.id] = {
      total: tenantCategories.length,
    };
    return acc;
  }, {} as Record<number, { total: number }>);

  if (tenantsLoading || categoriesLoading || statsLoading) {
    return (
      <div className="container mx-auto py-8">
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Package Categories</h1>
          <p className="text-muted-foreground">
            Organize and manage your package offerings by category
          </p>
        </div>
        <Button onClick={handleAdd}>
          <Plus className="h-4 w-4 mr-2" />
          Add Category
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Categories</CardTitle>
            <Package className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">
              Across all tenants
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Tenants</CardTitle>
            <Building className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Object.keys(stats.byTenant).length}
            </div>
            <p className="text-xs text-muted-foreground">
              With categories
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average per Tenant</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Object.keys(stats.byTenant).length > 0 
                ? Math.round(stats.total / Object.keys(stats.byTenant).length)
                : 0
              }
            </div>
            <p className="text-xs text-muted-foreground">
              Categories per tenant
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Growth</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">+12%</div>
            <p className="text-xs text-muted-foreground">
              From last month
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Success/Error Messages */}
      {submitError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{submitError}</AlertDescription>
        </Alert>
      )}

      {submitSuccess && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>{submitSuccess}</AlertDescription>
        </Alert>
      )}

      {/* Main Content */}
      <Tabs defaultValue="all" className="space-y-4">
        <TabsList>
          <TabsTrigger value="all">All Categories</TabsTrigger>
          {tenants.map((tenant) => {
            const tenantStats = statsByTenant[tenant.id];
            return (
              <TabsTrigger key={tenant.id} value={`tenant-${tenant.id}`}>
                {tenant.name}
                <Badge variant="secondary" className="ml-2">
                  {tenantStats?.total || 0}
                </Badge>
              </TabsTrigger>
            );
          })}
        </TabsList>

        <TabsContent value="all">
          <PackageCategoryList
            onEdit={handleEdit}
            onAdd={handleAdd}
            showActions={true}
          />
        </TabsContent>

        {tenants.map((tenant) => (
          <TabsContent key={tenant.id} value={`tenant-${tenant.id}`}>
            <PackageCategoryList
              tenantId={tenant.id}
              onEdit={handleEdit}
              onAdd={() => {
                setSelectedTenantId(tenant.id);
                handleAdd();
              }}
              showActions={true}
            />
          </TabsContent>
        ))}
      </Tabs>

      {/* Create/Edit Dialog */}
      <Dialog open={isFormOpen || !!editingCategory} onOpenChange={handleCloseForm}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editingCategory ? "Edit Package Category" : "Add New Package Category"}
            </DialogTitle>
          </DialogHeader>
          <PackageCategoryForm
            entity={editingCategory || undefined}
            tenantId={selectedTenantId || editingCategory?.tenantId || (tenants[0]?.id ?? 1)}
            onSubmit={editingCategory ? handleUpdateCategory : handleCreateCategory}
            onCancel={handleCloseForm}
            className="border-0 shadow-none"
          />
        </DialogContent>
      </Dialog>

      {/* Success Modal */}
      <SuccessModal
        isOpen={showSuccessModal}
        onClose={() => setShowSuccessModal(false)}
        title="Package Category Created!"
        description="Your new package category has been successfully created and is ready to use."
      />

      {/* Success Toast */}
      <SuccessToast
        isOpen={!!submitSuccess}
        onClose={() => setSubmitSuccess(null)}
        message={submitSuccess || ""}
      />

      {/* Confetti */}
      {showConfetti && <Confetti />}
    </div>
  );
}
