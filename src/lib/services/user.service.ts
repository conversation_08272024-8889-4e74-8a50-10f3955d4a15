import { db } from "../db";
import { users, tenants, organizations, type User, type NewUser } from "../db/schema";
import { eq, and, like, desc, isNull } from "drizzle-orm";
import { hash } from "bcryptjs";

export class UserService {
  /**
   * Get user by ID with tenant and organization info
   */
  static async getUserById(id: string) {
    const result = await db
      .select({
        user: users,
        tenant: tenants,
        organization: organizations,
      })
      .from(users)
      .leftJoin(tenants, eq(users.tenantId, tenants.id))
      .leftJoin(organizations, eq(users.organizationId, organizations.id))
      .where(eq(users.id, id))
      .limit(1);

    return result[0] || null;
  }

  /**
   * Get users by tenant
   */
  static async getUsersByTenant(tenantId: number): Promise<User[]> {
    return await db
      .select()
      .from(users)
      .where(eq(users.tenantId, tenantId))
      .orderBy(desc(users.createdAt));
  }

  /**
   * Get users by organization
   */
  static async getUsersByOrganization(organizationId: string): Promise<User[]> {
    return await db
      .select()
      .from(users)
      .where(eq(users.organizationId, organizationId))
      .orderBy(desc(users.createdAt));
  }

  /**
   * Get users without tenant (unassigned users)
   */
  static async getUnassignedUsers(organizationId?: string): Promise<User[]> {
    const conditions = [isNull(users.tenantId)];
    
    if (organizationId) {
      conditions.push(eq(users.organizationId, organizationId));
    }

    return await db
      .select()
      .from(users)
      .where(and(...conditions))
      .orderBy(desc(users.createdAt));
  }

  /**
   * Assign user to tenant
   */
  static async assignUserToTenant(userId: string, tenantId: number): Promise<User | null> {
    // Verify tenant exists
    const [tenant] = await db
      .select()
      .from(tenants)
      .where(eq(tenants.id, tenantId))
      .limit(1);

    if (!tenant) {
      throw new Error("Tenant not found");
    }

    // Verify user exists
    const [existingUser] = await db
      .select()
      .from(users)
      .where(eq(users.id, userId))
      .limit(1);

    if (!existingUser) {
      throw new Error("User not found");
    }

    // Update user's tenant
    const [updatedUser] = await db
      .update(users)
      .set({
        tenantId: tenantId,
        updatedAt: new Date(),
      })
      .where(eq(users.id, userId))
      .returning();

    return updatedUser || null;
  }

  /**
   * Remove user from tenant
   */
  static async removeUserFromTenant(userId: string): Promise<User | null> {
    const [updatedUser] = await db
      .update(users)
      .set({
        tenantId: null,
        updatedAt: new Date(),
      })
      .where(eq(users.id, userId))
      .returning();

    return updatedUser || null;
  }

  /**
   * Create user with tenant assignment
   */
  static async createUserWithTenant(
    userData: Omit<NewUser, "id" | "createdAt" | "updatedAt">,
    tenantId?: number
  ): Promise<User> {
    // Hash password if provided
    if (userData.password) {
      userData.password = await hash(userData.password, 12);
    }

    const [user] = await db
      .insert(users)
      .values({
        ...userData,
        tenantId: tenantId || null,
      })
      .returning();

    return user;
  }

  /**
   * Bulk assign users to tenant
   */
  static async bulkAssignUsersToTenant(userIds: string[], tenantId: number): Promise<number> {
    // Verify tenant exists
    const [tenant] = await db
      .select()
      .from(tenants)
      .where(eq(tenants.id, tenantId))
      .limit(1);

    if (!tenant) {
      throw new Error("Tenant not found");
    }

    // Update users
    const result = await db
      .update(users)
      .set({
        tenantId: tenantId,
        updatedAt: new Date(),
      })
      .where(
        and(
          eq(users.id, userIds[0]), // This is a simplified version
          // In real implementation, you'd use `inArray` from drizzle-orm
        )
      );

    return result.rowCount || 0;
  }

  /**
   * Get tenant statistics
   */
  static async getTenantUserStats(tenantId: number) {
    const [totalUsers] = await db
      .select({ count: users.id })
      .from(users)
      .where(eq(users.tenantId, tenantId));

    const [activeUsers] = await db
      .select({ count: users.id })
      .from(users)
      .where(
        and(
          eq(users.tenantId, tenantId),
          eq(users.isActive, true)
        )
      );

    return {
      totalUsers: totalUsers?.count || 0,
      activeUsers: activeUsers?.count || 0,
      inactiveUsers: (totalUsers?.count || 0) - (activeUsers?.count || 0),
    };
  }

  /**
   * Search users within tenant
   */
  static async searchUsersInTenant(tenantId: number, query: string): Promise<User[]> {
    return await db
      .select()
      .from(users)
      .where(
        and(
          eq(users.tenantId, tenantId),
          like(users.name, `%${query}%`)
        )
      )
      .orderBy(desc(users.createdAt));
  }

  /**
   * Search users for role assignment with pagination
   */
  static async searchUsersForRoleAssignment(
    query: string = "",
    tenantId?: number | null,
    limit = 20,
    offset = 0
  ): Promise<{
    users: Array<User & { currentRoles?: string[] }>;
    total: number;
    hasMore: boolean;
  }> {
    try {
      // Build where conditions
      const conditions = [];

      if (query) {
        conditions.push(
          like(users.name, `%${query}%`)
        );
      }

      if (tenantId !== undefined) {
        if (tenantId === null) {
          // For superadmin - show all users
          // No tenant filter
        } else {
          conditions.push(eq(users.tenantId, tenantId));
        }
      }

      const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

      // Get users with pagination
      const userResults = await db
        .select({
          id: users.id,
          name: users.name,
          email: users.email,
          image: users.image,
          role: users.role,
          tenantId: users.tenantId,
          organizationId: users.organizationId,
          createdAt: users.createdAt,
          updatedAt: users.updatedAt,
        })
        .from(users)
        .where(whereClause)
        .orderBy(desc(users.createdAt))
        .limit(limit + 1)
        .offset(offset);

      // Check if there are more results
      const hasMore = userResults.length > limit;
      if (hasMore) {
        userResults.pop();
      }

      return {
        users: userResults,
        total: userResults.length,
        hasMore,
      };
    } catch (error) {
      console.error("Error searching users for role assignment:", error);
      throw error;
    }
  }

  /**
   * Transfer user between tenants
   */
  static async transferUserBetweenTenants(
    userId: string,
    fromTenantId: number,
    toTenantId: number
  ): Promise<User | null> {
    // Verify user belongs to source tenant
    const [user] = await db
      .select()
      .from(users)
      .where(
        and(
          eq(users.id, userId),
          eq(users.tenantId, fromTenantId)
        )
      )
      .limit(1);

    if (!user) {
      throw new Error("User not found in source tenant");
    }

    // Verify destination tenant exists
    const [destinationTenant] = await db
      .select()
      .from(tenants)
      .where(eq(tenants.id, toTenantId))
      .limit(1);

    if (!destinationTenant) {
      throw new Error("Destination tenant not found");
    }

    // Transfer user
    const [updatedUser] = await db
      .update(users)
      .set({
        tenantId: toTenantId,
        updatedAt: new Date(),
      })
      .where(eq(users.id, userId))
      .returning();

    return updatedUser || null;
  }

  /**
   * Get user's tenant context
   */
  static async getUserTenantContext(userId: string) {
    const result = await db
      .select({
        user: {
          id: users.id,
          email: users.email,
          name: users.name,
          role: users.role,
        },
        tenant: {
          id: tenants.id,
          name: tenants.name,
          subdomain: tenants.subdomain,
          customDomain: tenants.customDomain,
          settings: tenants.settings,
        },
        organization: {
          id: organizations.id,
          name: organizations.name,
          slug: organizations.slug,
        },
      })
      .from(users)
      .leftJoin(tenants, eq(users.tenantId, tenants.id))
      .leftJoin(organizations, eq(users.organizationId, organizations.id))
      .where(eq(users.id, userId))
      .limit(1);

    return result[0] || null;
  }
}
