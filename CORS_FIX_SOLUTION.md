# 🚨 CORS Fix untuk Public API Endpoint

## Problem
Frontend di `localhost:3002` tidak bisa akses `/api/public/v1/class-schedules` karena CORS error.

## Root Cause Analysis

### 1. **Import Order Issue**
```typescript
// ❌ SALAH - withCORS digunakan sebelum di-import
export const OPTIONS = withCORS(async (req: NextRequest) => {
  return new NextResponse(null, { status: 200 });
});
import { withCORS } from '@/lib/security/cors.config';
```

### 2. **CORS Configuration Issue**
Di `cors.config.ts`, localhost:3002 mungkin tidak ter-include dengan benar.

### 3. **Missing Proper CORS Headers**
GET endpoint tidak menggunakan `withCORS` wrapper.

## 🔧 SOLUTION

### Step 1: Fix Import Order di `src/app/api/public/v1/class-schedules/route.ts`

```typescript
import { NextRequest, NextResponse } from "next/server";
import { withCORS } from '@/lib/security/cors.config';
import { ClassScheduleService } from "@/lib/services/class-schedule.service";
import { z } from "zod";

// ✅ BENAR - Import dulu, baru gunakan
export const OPTIONS = withCORS(async (req: NextRequest) => {
  return new NextResponse(null, { status: 200 });
});
```

### Step 2: Wrap GET Handler dengan CORS

```typescript
// ✅ Wrap GET handler dengan withCORS
export const GET = withCORS(async (request: NextRequest) => {
  try {
    // Authenticate API key
    const authResult = await authenticateApiKey(request);
    if (!authResult.success) {
      return NextResponse.json(
        { 
          error: authResult.error,
          success: false 
        },
        { status: 401 }
      );
    }

    // ... rest of the logic remains the same
    
    const response = NextResponse.json(responseData);
    
    // ✅ Explicitly add CORS headers for localhost:3002
    response.headers.set('Access-Control-Allow-Origin', 'http://localhost:3002');
    response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-API-Key, X-Tenant-ID');
    response.headers.set('Access-Control-Allow-Credentials', 'true');
    
    return response;
    
  } catch (error) {
    // ... error handling
  }
});
```

### Step 3: Update CORS Config untuk Development

Di `src/lib/security/cors.config.ts`, pastikan localhost:3002 included:

```typescript
const defaultCORSConfig: CORSOptions = {
  origin: (origin: string) => {
    const allowedOrigins = [
      process.env.FRONTEND_URL,
      process.env.ADMIN_URL,
      process.env.MOBILE_APP_URL,
      "http://localhost:3000", // Development
      "http://localhost:3001", // Development admin
      "http://localhost:3002", // ✅ Frontend development
      "https://localhost:3000", // Development HTTPS
      "https://localhost:3001", // Development admin HTTPS
      "https://localhost:3002", // ✅ Frontend development HTTPS
    ].filter(Boolean);

    // Allow requests with no origin (mobile apps, Postman, etc.)
    if (!origin) return true;

    console.log('CORS Origin Check:', { origin, allowedOrigins, allowed: allowedOrigins.includes(origin) });
    
    return allowedOrigins.includes(origin);
  },
  methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
  allowedHeaders: [
    "Content-Type",
    "Authorization",
    "X-Tenant-ID",
    "X-Device-ID",
    "X-Client-Version",
    "X-Request-ID",
    "X-Forwarded-For",
    "User-Agent",
    "Accept",
    "Accept-Language",
    "Accept-Encoding",
    "X-API-Key", // ✅ Important for public API
    "Cache-Control",
    "Pragma",
  ],
  exposedHeaders: [
    "X-Request-ID",
    "X-RateLimit-Limit",
    "X-RateLimit-Remaining",
    "X-RateLimit-Reset",
    "X-Total-Count",
    "X-Page-Count",
  ],
  credentials: true,
  maxAge: 86400, // 24 hours
  optionsSuccessStatus: 200,
};
```

### Step 4: Alternative Quick Fix (Manual CORS Headers)

Jika `withCORS` masih bermasalah, tambahkan manual CORS headers:

```typescript
export async function GET(request: NextRequest) {
  try {
    // ... existing logic ...
    
    const responseData = {
      success: true,
      data: {
        schedules: result.schedules,
        pagination: {
          page: validatedParams.page,
          limit: validatedParams.limit,
          total: result.total,
          totalPages: Math.ceil(result.total / validatedParams.limit),
          hasMore: result.hasMore,
        }
      },
      meta: {
        timestamp: new Date().toISOString(),
        version: "v1",
        endpoint: "/api/public/v1/class-schedules"
      }
    };

    const response = NextResponse.json(responseData);
    
    // ✅ Manual CORS headers untuk localhost:3002
    response.headers.set('Access-Control-Allow-Origin', 'http://localhost:3002');
    response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-API-Key, X-Tenant-ID');
    response.headers.set('Access-Control-Allow-Credentials', 'true');
    
    return response;
    
  } catch (error) {
    console.error("Error fetching public class schedules v1:", error);
    
    const errorResponse = NextResponse.json(
      {
        error: error instanceof Error ? error.message : "Failed to fetch class schedules",
        success: false
      },
      { status: 500 }
    );
    
    // ✅ CORS headers juga untuk error response
    errorResponse.headers.set('Access-Control-Allow-Origin', 'http://localhost:3002');
    errorResponse.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    errorResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-API-Key, X-Tenant-ID');
    errorResponse.headers.set('Access-Control-Allow-Credentials', 'true');
    
    return errorResponse;
  }
}

// ✅ OPTIONS handler untuk preflight
export async function OPTIONS(request: NextRequest) {
  const response = new NextResponse(null, { status: 200 });
  
  response.headers.set('Access-Control-Allow-Origin', 'http://localhost:3002');
  response.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  response.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-API-Key, X-Tenant-ID');
  response.headers.set('Access-Control-Allow-Credentials', 'true');
  response.headers.set('Access-Control-Max-Age', '86400');
  
  return response;
}
```

## 🧪 Testing

### 1. Test dari Browser Console
```javascript
fetch('http://localhost:3000/api/public/v1/class-schedules?tenantId=1&limit=10', {
  method: 'GET',
  headers: {
    'X-API-Key': 'pk_9b4d8a5f3e9ec298be21ac0589d0346d0289f717d9ca3841093498ef4530c64b',
    'Content-Type': 'application/json'
  }
})
.then(response => response.json())
.then(data => console.log('Success:', data))
.catch(error => console.error('Error:', error));
```

### 2. Test CORS Preflight
```javascript
// Test OPTIONS request
fetch('http://localhost:3000/api/public/v1/class-schedules', {
  method: 'OPTIONS',
  headers: {
    'Origin': 'http://localhost:3002',
    'Access-Control-Request-Method': 'GET',
    'Access-Control-Request-Headers': 'X-API-Key, Content-Type'
  }
})
.then(response => {
  console.log('Preflight Status:', response.status);
  console.log('CORS Headers:', {
    'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
    'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
    'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers')
  });
});
```

## 🔍 Debugging CORS Issues

### 1. Check Browser Network Tab
- Lihat apakah ada preflight OPTIONS request
- Check response headers dari server
- Lihat error message di console

### 2. Add Debug Logging
```typescript
export async function GET(request: NextRequest) {
  const origin = request.headers.get('origin');
  console.log('🔍 CORS Debug:', {
    origin,
    method: request.method,
    headers: Object.fromEntries(request.headers.entries())
  });
  
  // ... rest of logic
}
```

### 3. Temporary Allow All Origins (Development Only)
```typescript
// ⚠️ HANYA UNTUK DEBUGGING - JANGAN DI PRODUCTION
response.headers.set('Access-Control-Allow-Origin', '*');
```

## 📝 Implementation Priority

1. **Immediate Fix** - Add manual CORS headers (5 minutes)
2. **Proper Fix** - Fix import order dan gunakan withCORS (15 minutes)
3. **Long-term** - Update CORS config untuk all environments (30 minutes)

## ✅ Expected Result

Setelah fix ini, frontend di `localhost:3002` akan bisa akses endpoint tanpa CORS error:

```
✅ GET /api/public/v1/class-schedules?tenantId=1&limit=10 200 in 57ms
✅ CORS headers properly set
✅ Frontend dapat consume API