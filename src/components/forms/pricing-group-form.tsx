"use client";

import { z } from "zod";
import { DollarSign, Building, Hash, Percent, ToggleLeft, Star } from "lucide-react";
import { createFormComponent } from "@/lib/core/base-form";
import { PricingGroup } from "@/lib/db/schema";
import { useTenants } from "@/lib/hooks/queries/use-tenant-queries";

// Form validation schema
const pricingGroupSchema = z.object({
  tenantId: z.number().int().positive("Tenant is required"),
  name: z.string().min(1, "Name is required").max(255, "Name must be less than 255 characters"),
  description: z.string().max(1000, "Description must be less than 1000 characters").optional(),
  discountPercentage: z.number().int().min(0, "Discount must be at least 0").max(100, "Discount cannot exceed 100").optional(),
  isActive: z.boolean(),
  isDefault: z.boolean(),
  sortOrder: z.number().int().min(0, "Sort order must be non-negative").optional(),
});

type PricingGroupData = z.infer<typeof pricingGroupSchema>;

// Get tenants for dropdown
function useTenantOptions() {
  const { data: tenantsData, isLoading } = useTenants();
  const tenants = tenantsData?.tenants || [];
  
  return {
    options: tenants.map(tenant => ({
      value: tenant.id,
      label: tenant.name,
    })),
    isLoading,
  };
}

// Form configuration
const formConfig = {
  schema: pricingGroupSchema,
  defaultValues: {
    tenantId: 1,
    name: "",
    description: "",
    discountPercentage: 0,
    isActive: true,
    isDefault: false,
    sortOrder: 0,
  },
  fields: [
    {
      name: "tenantId" as const,
      label: "Tenant",
      type: "select" as const,
      placeholder: "Select tenant",
      required: true,
      icon: Building,
      options: [], // Will be populated dynamically
    },
    {
      name: "name" as const,
      label: "Pricing Group Name",
      type: "text" as const,
      placeholder: "Enter pricing group name (e.g., VIP Members, Students, Seniors)",
      required: true,
      icon: DollarSign,
    },
    {
      name: "description" as const,
      label: "Description",
      type: "textarea" as const,
      placeholder: "Enter description for this pricing group",
      icon: DollarSign,
      description: "Describe what this pricing group represents or who it's for",
    },
    {
      name: "discountPercentage" as const,
      label: "Discount Percentage",
      type: "number" as const,
      placeholder: "Enter discount percentage (0-100)",
      icon: Percent,
      description: "Percentage discount for this pricing group (0 = no discount, 100 = free)",
    },
    {
      name: "sortOrder" as const,
      label: "Sort Order",
      type: "number" as const,
      placeholder: "Enter sort order (0 = first)",
      icon: Hash,
      description: "Lower numbers appear first in the list. Leave as 0 to add at the end.",
    },
    {
      name: "isActive" as const,
      label: "Active Status",
      type: "switch" as const,
      icon: ToggleLeft,
      description: "Whether this pricing group is active and available for use",
    },
    {
      name: "isDefault" as const,
      label: "Default Group",
      type: "switch" as const,
      icon: Star,
      description: "Whether this is the default pricing group for new customers",
    },
  ],
  sections: [
    {
      title: "Basic Information",
      description: "General details about the pricing group",
      fields: ["tenantId", "name", "description"] as const,
    },
    {
      title: "Pricing & Ordering",
      description: "Discount settings and display order",
      fields: ["discountPercentage", "sortOrder"] as const,
    },
    {
      title: "Settings",
      description: "Status and behavior settings",
      fields: ["isActive", "isDefault"] as const,
    },
  ],
};

// Create the form component using the factory
const BasePricingGroupForm = createFormComponent<PricingGroup, PricingGroupData>(
  "Pricing Group",
  formConfig
);

// Enhanced component with tenant options
interface PricingGroupFormProps {
  entity?: PricingGroup;
  tenantId?: number;
  onSubmit: (data: PricingGroupData) => Promise<void>;
  onCancel: () => void;
  className?: string;
  title?: string;
  description?: string;
}

export function PricingGroupForm({ tenantId, ...props }: PricingGroupFormProps) {
  const { options: tenantOptions, isLoading: tenantsLoading } = useTenantOptions();

  // Update form config with tenant options and default tenantId
  const enhancedConfig = {
    ...formConfig,
    defaultValues: {
      ...formConfig.defaultValues,
      tenantId: tenantId || formConfig.defaultValues.tenantId,
    },
    fields: formConfig.fields.map(field => 
      field.name === "tenantId" 
        ? { ...field, options: tenantOptions }
        : field
    ),
  };

  // Create enhanced form component
  const EnhancedForm = createFormComponent<PricingGroup, PricingGroupData>(
    "Pricing Group",
    enhancedConfig
  );

  if (tenantsLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
      </div>
    );
  }

  return <EnhancedForm {...props} icon={DollarSign} />;
}
