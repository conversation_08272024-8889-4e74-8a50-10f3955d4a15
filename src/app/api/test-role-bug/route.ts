import { NextRequest, NextResponse } from "next/server";
import { RoleService } from "@/lib/services/role.service";

// Test endpoint to reproduce the role creation bug
export async function POST(request: NextRequest) {
  try {
    console.log("🐛 Testing role creation bug...");
    
    // Step 1: Get current roles to see before state
    console.log("\n1️⃣ Getting current roles...");
    const beforeRoles = await RoleService.searchRoles(undefined, undefined, undefined, 100, 0);
    console.log(`Found ${beforeRoles.roles.length} roles before creation`);
    
    // Log existing role IDs and names
    const existingRoles = beforeRoles.roles.map(r => ({
      id: r.id,
      name: r.name,
      display_name: r.display_name
    }));
    console.log("Existing roles:", existingRoles);

    // Step 2: Create a new role
    console.log("\n2️⃣ Creating new role...");
    const newRoleData = {
      name: `bug_test_${Date.now()}`,
      display_name: "Bug Test Role",
      description: "Role created to test the bug",
      is_system_role: false,
      hierarchy_level: 70,
      tenantId: 1,
      permissions: []
    };

    console.log("Creating role with data:", newRoleData);
    const createdRole = await RoleService.create(newRoleData);
    console.log("✅ Role creation returned:", {
      id: createdRole.id,
      name: createdRole.name,
      display_name: createdRole.display_name
    });

    // Step 3: Get roles after creation to see what changed
    console.log("\n3️⃣ Getting roles after creation...");
    const afterRoles = await RoleService.searchRoles(undefined, undefined, undefined, 100, 0);
    console.log(`Found ${afterRoles.roles.length} roles after creation`);

    // Step 4: Analyze what happened
    console.log("\n4️⃣ Analyzing changes...");
    const countDiff = afterRoles.roles.length - beforeRoles.roles.length;
    console.log(`Role count difference: ${countDiff}`);

    // Check if any existing role was modified
    const modifiedRoles = [];
    for (const beforeRole of beforeRoles.roles) {
      const afterRole = afterRoles.roles.find(r => r.id === beforeRole.id);
      if (afterRole) {
        if (
          beforeRole.name !== afterRole.name ||
          beforeRole.display_name !== afterRole.display_name ||
          beforeRole.description !== afterRole.description
        ) {
          modifiedRoles.push({
            id: beforeRole.id,
            before: {
              name: beforeRole.name,
              display_name: beforeRole.display_name,
              description: beforeRole.description
            },
            after: {
              name: afterRole.name,
              display_name: afterRole.display_name,
              description: afterRole.description
            }
          });
        }
      }
    }

    // Check if the created role actually exists as new
    const createdRoleExists = afterRoles.roles.find(r => r.id === createdRole.id);
    
    return NextResponse.json({
      success: true,
      message: "Role creation bug test completed",
      data: {
        beforeCount: beforeRoles.roles.length,
        afterCount: afterRoles.roles.length,
        countDifference: countDiff,
        createdRole: {
          id: createdRole.id,
          name: createdRole.name,
          display_name: createdRole.display_name
        },
        createdRoleExists: !!createdRoleExists,
        modifiedRoles,
        bugDetected: modifiedRoles.length > 0 || countDiff !== 1,
        analysis: {
          expectedBehavior: "Should create 1 new role, no existing roles modified",
          actualBehavior: `Created ${countDiff} new role(s), modified ${modifiedRoles.length} existing role(s)`,
          isBug: modifiedRoles.length > 0 || countDiff !== 1
        }
      }
    });
  } catch (error) {
    console.error("❌ Role creation bug test failed:", error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}
