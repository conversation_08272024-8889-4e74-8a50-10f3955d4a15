# syntax=docker/dockerfile:1
# =============================================================================
# FAANG-Level Multi-Stage Dockerfile for Next.js 15
# Optimized for: Performance, Security, Maintainability, Developer Experience
# =============================================================================

# -----------------------------------------------------------------------------
# Base Stage: Common foundation for all environments
# -----------------------------------------------------------------------------
FROM node:18-alpine AS base

# Install system dependencies for Alpine
RUN apk add --no-cache \
    libc6-compat \
    dumb-init \
    && rm -rf /var/cache/apk/*

# Set working directory
WORKDIR /app

# Create non-root user early (security best practice)
RUN addgroup --system --gid 1001 nodejs && \
    adduser --system --uid 1001 nextjs

# Copy package files for dependency resolution
COPY package.json package-lock.json* ./

# -----------------------------------------------------------------------------
# Dependencies Stage: Install production dependencies only
# -----------------------------------------------------------------------------
FROM base AS deps

# Install production dependencies with optimizations
RUN --mount=type=cache,target=/root/.npm \
    npm ci --only=production --no-audit --no-fund && \
    npm cache clean --force

# -----------------------------------------------------------------------------
# Dev Dependencies Stage: Install all dependencies for development
# -----------------------------------------------------------------------------
FROM base AS deps-dev

# Install all dependencies including dev dependencies
RUN --mount=type=cache,target=/root/.npm \
    npm ci --no-audit --no-fund

# -----------------------------------------------------------------------------
# Builder Stage: Build the application
# -----------------------------------------------------------------------------
FROM deps-dev AS builder

# Copy source code
COPY . .

# Set build environment variables
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1
ENV SKIP_ENV_VALIDATION=1

# Build the application with optimizations
RUN --mount=type=cache,target=/app/.next/cache \
    npm run build

# -----------------------------------------------------------------------------
# Development Stage: Hot reload and debugging capabilities
# -----------------------------------------------------------------------------
FROM deps-dev AS development

# Set development environment
ENV NODE_ENV=development
ENV NEXT_TELEMETRY_DISABLED=1

# Development-specific optimizations
ENV WATCHPACK_POLLING=true
ENV CHOKIDAR_USEPOLLING=true

# Install development tools
RUN npm install -g nodemon

# Change ownership and switch to non-root user
RUN chown -R nextjs:nodejs /app
USER nextjs

# Expose ports for app and debugging
EXPOSE 3000 9229

# Health check for development
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/api/health || exit 1

# Development command with debugging enabled
CMD ["npm", "run", "dev"]

# -----------------------------------------------------------------------------
# Test Stage: For running tests in CI/CD
# -----------------------------------------------------------------------------
FROM deps-dev AS test

# Copy source code
COPY . .

# Set test environment
ENV NODE_ENV=test
ENV CI=true

# Run tests
RUN npm run test:ci

# -----------------------------------------------------------------------------
# Production Stage: Minimal runtime image
# -----------------------------------------------------------------------------
FROM base AS production

# Set production environment
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

# Copy production dependencies
COPY --from=deps --chown=nextjs:nodejs /app/node_modules ./node_modules

# Copy built application from builder stage
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static
COPY --from=builder --chown=nextjs:nodejs /app/public ./public

# Security hardening
RUN rm -rf /tmp/* /var/tmp/* && \
    chmod -R 755 /app

# Switch to non-root user
USER nextjs

# Expose port
EXPOSE 3000

# Health check for production
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost:3000/api/health || exit 1

# Use dumb-init for proper signal handling
ENTRYPOINT ["dumb-init", "--"]

# Production command
CMD ["node", "server.js"]

# -----------------------------------------------------------------------------
# Debug Stage: For production debugging (optional)
# -----------------------------------------------------------------------------
FROM production AS debug

# Switch back to root for debugging tools
USER root

# Install debugging tools
RUN apk add --no-cache \
    curl \
    htop \
    strace \
    tcpdump

# Switch back to nextjs user
USER nextjs

# Enable Node.js debugging
ENV NODE_OPTIONS="--inspect=0.0.0.0:9229"

# Expose debugging port
EXPOSE 9229

CMD ["node", "--inspect=0.0.0.0:9229", "server.js"]
