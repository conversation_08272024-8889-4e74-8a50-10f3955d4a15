"use client";

import { useState } from "react";
import { Check, Loader2, Star } from "lucide-react";
import { toast } from "sonner";
import { loadStripe } from "@stripe/stripe-js";

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { stripeConfig, formatPrice, formatCredits, calculatePricePerCredit } from "@/lib/stripe/config";

const stripePromise = loadStripe(stripeConfig.publishableKey);

interface CreditPackagesProps {
  organizationId?: string;
}

export function CreditPackages({ organizationId }: CreditPackagesProps) {
  const [loadingPackage, setLoadingPackage] = useState<string | null>(null);

  const handlePurchase = async (packageId: string) => {
    setLoadingPackage(packageId);

    try {
      // Create checkout session
      const response = await fetch("/api/stripe/checkout", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          packageId,
          organizationId,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Failed to create checkout session");
      }

      // Redirect to Stripe Checkout
      const stripe = await stripePromise;
      if (!stripe) {
        throw new Error("Stripe failed to load");
      }

      const { error } = await stripe.redirectToCheckout({
        sessionId: data.sessionId,
      });

      if (error) {
        throw new Error(error.message);
      }
    } catch (error) {
      console.error("Purchase error:", error);
      toast.error(error instanceof Error ? error.message : "Purchase failed");
    } finally {
      setLoadingPackage(null);
    }
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {stripeConfig.creditPackages.map((pkg) => (
        <Card 
          key={pkg.id} 
          className={`relative ${pkg.popular ? 'border-primary shadow-lg scale-105' : ''}`}
        >
          {pkg.popular && (
            <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
              <Badge className="bg-primary text-primary-foreground">
                <Star className="w-3 h-3 mr-1" />
                Most Popular
              </Badge>
            </div>
          )}
          
          <CardHeader className="text-center">
            <CardTitle className="text-xl">{pkg.name}</CardTitle>
            <CardDescription>{pkg.description}</CardDescription>
            <div className="mt-4">
              <div className="text-3xl font-bold">{formatPrice(pkg.price)}</div>
              <div className="text-sm text-muted-foreground">
                {calculatePricePerCredit(pkg.price, pkg.credits)} per credit
              </div>
            </div>
          </CardHeader>
          
          <CardContent className="space-y-4">
            <div className="text-center">
              <div className="text-2xl font-semibold text-primary">
                {formatCredits(pkg.credits)}
              </div>
              <div className="text-sm text-muted-foreground">Credits</div>
            </div>
            
            <div className="space-y-2">
              <div className="flex items-center text-sm">
                <Check className="w-4 h-4 text-green-500 mr-2" />
                <span>Instant credit delivery</span>
              </div>
              <div className="flex items-center text-sm">
                <Check className="w-4 h-4 text-green-500 mr-2" />
                <span>No expiration date</span>
              </div>
              <div className="flex items-center text-sm">
                <Check className="w-4 h-4 text-green-500 mr-2" />
                <span>Full API access</span>
              </div>
              <div className="flex items-center text-sm">
                <Check className="w-4 h-4 text-green-500 mr-2" />
                <span>Priority support</span>
              </div>
            </div>
            
            <Button
              className="w-full"
              onClick={() => handlePurchase(pkg.id)}
              disabled={loadingPackage === pkg.id}
              variant={pkg.popular ? "default" : "outline"}
            >
              {loadingPackage === pkg.id ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Processing...
                </>
              ) : (
                "Purchase Credits"
              )}
            </Button>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
