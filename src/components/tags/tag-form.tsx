"use client";

import React from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { useCreateTag, useUpdateTag, type CreateTagData, type UpdateTagData } from "@/lib/hooks/queries/use-tag-queries";
import { useCustomers } from "@/lib/hooks/queries/use-customer-queries";
import { type Tag, type Customer } from "@/lib/db/schema";
import { toast } from "sonner";
import { Search, User, X } from "lucide-react";
import { Loader2, Palette } from "lucide-react";

// Form validation schema
const tagFormSchema = z.object({
  name: z.string().min(1, "Name is required").max(255, "Name must be less than 255 characters"),
  description: z.string().max(255, "Description must be less than 255 characters").optional(),
  customColor: z.string().max(255, "Color must be less than 255 characters").optional(),
  customerIds: z.array(z.string()).optional(),
});

type TagFormData = z.infer<typeof tagFormSchema>;

interface TagFormProps {
  open: boolean;
  onClose: () => void;
  title: string;
  description: string;
  tag?: Tag; // For editing
}

// Predefined colors
const PRESET_COLORS = [
  "#3B82F6", // Blue
  "#10B981", // Green
  "#F59E0B", // Yellow
  "#EF4444", // Red
  "#8B5CF6", // Purple
  "#06B6D4", // Cyan
  "#84CC16", // Lime
  "#F97316", // Orange
  "#EC4899", // Pink
  "#6B7280", // Gray
];

export function TagForm({ open, onClose, title, description, tag }: TagFormProps) {
  const createTag = useCreateTag();
  const updateTag = useUpdateTag();

  // Fetch customers for the dropdown
  const { data: customers = [], isLoading: customersLoading } = useCustomers({
    tenantId: 1, // TODO: Get actual tenantId from context/session
  });

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    watch,
    setValue,
  } = useForm<TagFormData>({
    resolver: zodResolver(tagFormSchema),
    defaultValues: {
      name: tag?.name || "",
      description: tag?.description || "",
      customColor: tag?.custom_color || "#3B82F6",
      customerIds: tag?.customerId ? [tag.customerId] : [],
    },
  });

  const selectedColor = watch("customColor");
  const selectedCustomerIds = watch("customerIds") || [];

  React.useEffect(() => {
    if (tag) {
      reset({
        name: tag.name || "",
        description: tag.description || "",
        customColor: tag.custom_color || "#3B82F6",
        customerIds: tag.customerId ? [tag.customerId] : [],
      });
    } else {
      reset({
        name: "",
        description: "",
        customColor: "#3B82F6",
        customerIds: [],
      });
    }
  }, [tag, reset]);

  const onSubmit = async (data: TagFormData) => {
    try {
      if (tag) {
        // Update existing tag
        const updateData: UpdateTagData = {
          name: data.name,
          description: data.description || undefined,
          customColor: data.customColor || undefined,
          customerId: data.customerIds?.[0] || undefined, // For now, take first customer
        };

        await updateTag.mutateAsync({ id: tag.id, data: updateData });
        toast.success("Tag updated successfully!");
      } else {
        // Create new tag - for now, create one tag per selected customer
        if (data.customerIds && data.customerIds.length > 0) {
          for (const customerId of data.customerIds) {
            const createData: CreateTagData = {
              tenantId: 1, // TODO: Get actual tenantId from context/session
              name: data.name,
              description: data.description || undefined,
              customColor: data.customColor || undefined,
              customerId: customerId,
            };
            await createTag.mutateAsync(createData);
          }
        } else {
          // Create general tag (no customer)
          const createData: CreateTagData = {
            tenantId: 1, // TODO: Get actual tenantId from context/session
            name: data.name,
            description: data.description || undefined,
            customColor: data.customColor || undefined,
            customerId: undefined,
          };
          await createTag.mutateAsync(createData);
        }
        toast.success("Tag created successfully!");
      }
      
      onClose();
      reset();
    } catch (error) {
      console.error("Error saving tag:", error);
      toast.error(error instanceof Error ? error.message : "Failed to save tag");
    }
  };

  // Customer selection handlers
  const handleCustomerToggle = (customerId: string, checked: boolean) => {
    const currentIds = selectedCustomerIds || [];
    if (checked) {
      setValue("customerIds", [...currentIds, customerId]);
    } else {
      setValue("customerIds", currentIds.filter(id => id !== customerId));
    }
  };

  const handleSelectAllCustomers = (checked: boolean) => {
    if (checked) {
      setValue("customerIds", customers.map(c => c.id));
    } else {
      setValue("customerIds", []);
    }
  };

  const removeCustomer = (customerId: string) => {
    const currentIds = selectedCustomerIds || [];
    setValue("customerIds", currentIds.filter(id => id !== customerId));
  };

  // Filter customers based on search
  const [customerSearch, setCustomerSearch] = React.useState("");
  const filteredCustomers = customers.filter(customer =>
    `${customer.firstName} ${customer.lastName}`.toLowerCase().includes(customerSearch.toLowerCase()) ||
    customer.email.toLowerCase().includes(customerSearch.toLowerCase())
  );

  const getCustomerDisplayName = (customer: Customer) => {
    return `${customer.firstName} ${customer.lastName || ''}`.trim() || customer.email;
  };

  const handleClose = () => {
    onClose();
    reset();
  };

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Name */}
          <div className="space-y-2">
            <Label htmlFor="name">Name *</Label>
            <Input
              id="name"
              placeholder="Enter tag name..."
              {...register("name")}
              error={errors.name?.message}
            />
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              placeholder="Enter tag description..."
              rows={3}
              {...register("description")}
            />
            {errors.description && (
              <p className="text-sm text-red-500">{errors.description.message}</p>
            )}
          </div>

          {/* Customer Selection */}
          <div className="space-y-3">
            <Label className="flex items-center gap-2">
              <User className="h-4 w-4" />
              Select Customers (Optional)
            </Label>

            {/* Selected customers display */}
            {selectedCustomerIds.length > 0 && (
              <div className="flex flex-wrap gap-2 p-2 bg-gray-50 dark:bg-gray-800 rounded-md">
                {selectedCustomerIds.map(customerId => {
                  const customer = customers.find(c => c.id === customerId);
                  if (!customer) return null;
                  return (
                    <div
                      key={customerId}
                      className="flex items-center gap-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 px-2 py-1 rounded-md text-sm"
                    >
                      <span>{getCustomerDisplayName(customer)}</span>
                      <button
                        type="button"
                        onClick={() => removeCustomer(customerId)}
                        className="hover:bg-blue-200 dark:hover:bg-blue-800 rounded-full p-0.5"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </div>
                  );
                })}
              </div>
            )}

            {/* Customer selection dropdown */}
            <Card className="border">
              <CardHeader className="pb-2">
                <div className="flex items-center gap-2">
                  <Search className="h-4 w-4 text-gray-500" />
                  <Input
                    placeholder="Search customers..."
                    value={customerSearch}
                    onChange={(e) => setCustomerSearch(e.target.value)}
                    className="border-0 focus-visible:ring-0 p-0"
                  />
                </div>
              </CardHeader>
              <CardContent className="pt-0">
                {customersLoading ? (
                  <div className="flex items-center justify-center py-4">
                    <Loader2 className="h-4 w-4 animate-spin" />
                    <span className="ml-2 text-sm text-gray-500">Loading customers...</span>
                  </div>
                ) : (
                  <div className="max-h-48 overflow-y-auto">
                    <div className="space-y-2">
                      {/* Select All */}
                      <div className="flex items-center gap-2 p-2 border-b">
                        <Checkbox
                          checked={selectedCustomerIds.length === customers.length && customers.length > 0}
                          onCheckedChange={handleSelectAllCustomers}
                        />
                        <span className="text-sm text-gray-600">Select All ({customers.length})</span>
                      </div>

                      {/* Customer list */}
                      {filteredCustomers.length === 0 ? (
                        <div className="text-center py-4 text-gray-500 text-sm">
                          {customerSearch ? "No customers found" : "No customers available"}
                        </div>
                      ) : (
                        filteredCustomers.map((customer) => (
                          <div
                            key={customer.id}
                            className="flex items-center gap-2 p-2 hover:bg-gray-50 dark:hover:bg-gray-800 rounded-md"
                          >
                            <Checkbox
                              checked={selectedCustomerIds.includes(customer.id)}
                              onCheckedChange={(checked) => handleCustomerToggle(customer.id, checked as boolean)}
                            />
                            <div className="flex-1 min-w-0">
                              <div className="text-sm font-medium truncate">
                                {getCustomerDisplayName(customer)}
                              </div>
                              <div className="text-xs text-gray-500 truncate">
                                {customer.email}
                              </div>
                            </div>
                          </div>
                        ))
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            <p className="text-xs text-gray-500">
              Leave empty to create a general tag not associated with any specific customer
            </p>
          </div>

          {/* Color Picker */}
          <div className="space-y-3">
            <Label>Color</Label>
            
            {/* Preset Colors */}
            <div className="grid grid-cols-5 gap-2">
              {PRESET_COLORS.map((color) => (
                <button
                  key={color}
                  type="button"
                  className={`w-8 h-8 rounded-full border-2 transition-all ${
                    selectedColor === color 
                      ? "border-gray-900 scale-110" 
                      : "border-gray-300 hover:border-gray-500"
                  }`}
                  style={{ backgroundColor: color }}
                  onClick={() => setValue("customColor", color)}
                  title={color}
                />
              ))}
            </div>

            {/* Custom Color Input */}
            <div className="flex items-center space-x-2">
              <Palette className="h-4 w-4 text-gray-500" />
              <Input
                type="color"
                className="w-16 h-8 p-1 border rounded"
                {...register("customColor")}
              />
              <Input
                type="text"
                placeholder="#3B82F6"
                className="flex-1"
                {...register("customColor")}
              />
            </div>
            
            {errors.customColor && (
              <p className="text-sm text-red-500">{errors.customColor.message}</p>
            )}
          </div>

          {/* Preview */}
          <div className="space-y-2">
            <Label>Preview</Label>
            <div className="flex items-center space-x-2 p-3 border rounded-lg bg-gray-50">
              <div 
                className="w-4 h-4 rounded-full" 
                style={{ backgroundColor: selectedColor || "#3B82F6" }}
              />
              <span className="font-medium">
                {watch("name") || "Tag Name"}
              </span>
              {watch("description") && (
                <span className="text-sm text-gray-500">
                  - {watch("description")}
                </span>
              )}
            </div>
          </div>

          {/* Actions */}
          <div className="flex justify-end space-x-3 pt-4">
            <Button type="button" variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={isSubmitting || createTag.isPending || updateTag.isPending}
            >
              {(isSubmitting || createTag.isPending || updateTag.isPending) && (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              )}
              {tag ? "Update Tag" : "Create Tag"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
