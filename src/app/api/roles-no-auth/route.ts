import { NextRequest, NextResponse } from "next/server";
import { RoleService } from "@/lib/services/role.service";

// Temporary endpoint without authentication for testing frontend
export async function GET(request: NextRequest) {
  try {
    console.log("🔓 Roles No Auth - Fetching roles without authentication...");
    
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get("limit") || "20");
    const offset = parseInt(searchParams.get("offset") || "0");
    
    // Call service directly without RBAC
    const result = await RoleService.searchRoles(null, undefined, undefined, limit, offset);
    
    console.log(`✅ Found ${result.roles.length} roles`);

    return NextResponse.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error("❌ Error fetching roles:", error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : "Failed to fetch roles"
    }, { status: 500 });
  }
}

// POST endpoint for creating roles without auth (testing only)
export async function POST(request: NextRequest) {
  try {
    console.log("🔓 Roles No Auth - Creating role without authentication...");
    
    const body = await request.json();
    console.log("Request body:", body);
    
    // Create role directly
    const role = await RoleService.create(body);
    
    console.log("✅ Role created:", role);

    return NextResponse.json({
      success: true,
      data: role,
      message: "Role created successfully",
    }, { status: 201 });
  } catch (error) {
    console.error("❌ Error creating role:", error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : "Failed to create role"
    }, { status: 500 });
  }
}
