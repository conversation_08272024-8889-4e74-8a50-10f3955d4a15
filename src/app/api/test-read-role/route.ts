import { NextRequest, NextResponse } from "next/server";
import { RoleService } from "@/lib/services/role.service";

// Test endpoint to verify READ operations work
export async function GET(request: NextRequest) {
  try {
    console.log("📖 Testing READ operations...");
    
    // Test 1: List all roles
    console.log("\n1️⃣ Testing searchRoles (list all)...");
    const allRoles = await RoleService.searchRoles(undefined, undefined, undefined, 50, 0);
    console.log(`✅ Found ${allRoles.roles.length} roles total`);

    // Test 2: Search with filters
    console.log("\n2️⃣ Testing searchRoles with filters...");
    const systemRoles = await RoleService.searchRoles(undefined, undefined, true, 20, 0);
    console.log(`✅ Found ${systemRoles.roles.length} system roles`);

    const tenantRoles = await RoleService.searchRoles(1, undefined, false, 20, 0);
    console.log(`✅ Found ${tenantRoles.roles.length} tenant roles`);

    // Test 3: Get role by ID (use first role from list)
    if (allRoles.roles.length > 0) {
      const firstRole = allRoles.roles[0];
      console.log(`\n3️⃣ Testing getById with ID: ${firstRole.id}...`);
      
      const roleById = await RoleService.getById(firstRole.id);
      if (roleById) {
        console.log(`✅ Successfully retrieved role: ${roleById.display_name}`);
      } else {
        console.log("❌ Failed to retrieve role by ID");
      }
    }

    // Test 4: Search by name
    console.log("\n4️⃣ Testing search by name...");
    const searchResults = await RoleService.searchRoles(undefined, "admin", undefined, 10, 0);
    console.log(`✅ Found ${searchResults.roles.length} roles containing 'admin'`);

    // Test 5: Get non-existent role
    console.log("\n5️⃣ Testing getById with non-existent ID...");
    const nonExistentRole = await RoleService.getById("non-existent-id");
    console.log(`✅ Non-existent role result: ${nonExistentRole ? 'Found (unexpected)' : 'Not found (expected)'}`);

    return NextResponse.json({
      success: true,
      message: "READ operations test completed",
      data: {
        totalRoles: allRoles.roles.length,
        systemRoles: systemRoles.roles.length,
        tenantRoles: tenantRoles.roles.length,
        searchResults: searchResults.roles.length,
        sampleRoles: allRoles.roles.slice(0, 3).map(r => ({
          id: r.id,
          name: r.name,
          display_name: r.display_name,
          is_system_role: r.is_system_role
        })),
        tests: {
          listAllRoles: allRoles.roles.length > 0,
          filterSystemRoles: systemRoles.roles.length > 0,
          filterTenantRoles: tenantRoles.roles.length >= 0,
          getByIdWorking: allRoles.roles.length > 0 ? !!await RoleService.getById(allRoles.roles[0].id) : false,
          searchByName: searchResults.roles.length >= 0,
          nonExistentHandling: !nonExistentRole
        }
      }
    });
  } catch (error) {
    console.error("❌ READ operations test failed:", error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 });
  }
}
