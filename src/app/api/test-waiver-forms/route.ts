import { NextResponse } from "next/server";

export async function GET() {
  try {
    console.log('🧪 Test waiver forms endpoint called');
    
    // Test waiver forms API
    const response = await fetch('http://localhost:3000/api/waiver-forms?tenantId=1');
    const data = await response.json();
    
    console.log('🧪 Waiver forms response:', {
      status: response.status,
      data: data
    });
    
    return NextResponse.json({
      success: true,
      waiverForms: data.data || data,
      count: (data.data || data)?.length || 0
    });
  } catch (error) {
    console.error('🧪 Error testing waiver forms:', error);
    return NextResponse.json({ error: 'Failed to test waiver forms' }, { status: 500 });
  }
}
