import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { z } from "zod";
import { individualCustomerSelectionService } from "@/lib/services/individual-customer-selection.service";

// Validation schema for query parameters
const querySchema = z.object({
  tenantId: z.string().transform((val) => parseInt(val)),
});

/**
 * GET /api/packages/[id]/individual-customer-targeting
 * Get individual customer targeting for a specific package
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { id: packageId } = await params;
    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    // Validate query parameters
    const { tenantId } = querySchema.parse(queryParams);

    if (!tenantId || tenantId <= 0) {
      return NextResponse.json(
        { error: "Valid tenant ID is required" },
        { status: 400 }
      );
    }

    console.log(`👤 [GET /api/packages/${packageId}/individual-customer-targeting] Getting individual customer targeting for tenant ${tenantId}`);

    const targetedCustomers = await individualCustomerSelectionService.getIndividualCustomerTargeting(packageId, tenantId);

    return NextResponse.json({
      success: true,
      data: targetedCustomers,
      meta: {
        packageId,
        tenantId,
        total: targetedCustomers.length,
      }
    });
  } catch (error) {
    console.error(`👤 [GET /api/packages/${params}/individual-customer-targeting] Error:`, error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid query parameters", details: error.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Failed to get individual customer targeting" },
      { status: 500 }
    );
  }
}
