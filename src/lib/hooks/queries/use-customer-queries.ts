import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';
import { Customer, NewCustomer } from '@/lib/db/schema';
import { createEntityHooks, BaseApiInterface } from '@/lib/core/base-query-hooks';
import { QueryOptions } from '@/lib/core/base-service';

// API interface implementation
const customerApi: BaseApiInterface<Customer, Omit<NewCustomer, "id" | "createdAt" | "updatedAt">, Partial<NewCustomer>> = {
  getAll: async (options?: QueryOptions): Promise<Customer[]> => {
    const params = new URLSearchParams();
    if (options?.tenantId) params.append('tenantId', options.tenantId.toString());
    if (options?.limit) params.append('limit', options.limit.toString());
    if (options?.offset) params.append('offset', options.offset.toString());
    if (options?.filters?.search) params.append('search', options.filters.search);
    if (options?.filters?.isActive !== undefined) params.append('activeOnly', options.filters.isActive.toString());
    if (options?.filters?.locationId) params.append('locationId', options.filters.locationId);
    if (options?.filters?.pricingGroupId) params.append('pricingGroupId', options.filters.pricingGroupId);

    const response = await fetch(`/api/customers?${params}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch customers: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data || [];
  },

  getById: async (id: string): Promise<Customer> => {
    console.log('🔍 [CustomerAPI] Fetching customer by ID:', id);
    const response = await fetch(`/api/customers/${id}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch customer: ${response.statusText}`);
    }
    const data = await response.json();
    console.log('✅ [CustomerAPI] Customer data received:', data.data);
    console.log('🏠 [CustomerAPI] Customer address fields:', {
      addressLine1: data.data.addressLine1,
      addressLine2: data.data.addressLine2,
      city: data.data.city,
      state: data.data.state,
      zip: data.data.zip,
      country: data.data.country,
    });
    return data.data;
  },

  getByIdWithAddress: async (id: string): Promise<Customer & { address?: any }> => {
    const response = await fetch(`/api/customers/${id}?includeAddress=true`);
    if (!response.ok) {
      throw new Error(`Failed to fetch customer: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data;
  },

  getByTenant: async (tenantId: number, options?: QueryOptions): Promise<Customer[]> => {
    return customerApi.getAll({ ...options, tenantId });
  },

  create: async (data: Omit<NewCustomer, "id" | "createdAt" | "updatedAt">): Promise<Customer> => {
    const response = await fetch('/api/customers', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to create customer');
    }
    const result = await response.json();
    return result.data;
  },

  update: async ({ id, data }: { id: string; data: Partial<NewCustomer> }): Promise<Customer> => {
    const response = await fetch(`/api/customers/${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to update customer');
    }
    const result = await response.json();
    return result.data;
  },

  delete: async (id: string): Promise<Customer> => {
    const response = await fetch(`/api/customers/${id}`, {
      method: 'DELETE',
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to delete customer');
    }
    const result = await response.json();
    return result.data;
  },

  bulkOperation: async (data: { ids: string[]; action: string }): Promise<Customer[]> => {
    const response = await fetch('/api/customers', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to perform bulk operation');
    }
    const result = await response.json();
    return result.data;
  },

  search: async (query: string, options?: QueryOptions): Promise<Customer[]> => {
    return customerApi.getAll({ ...options, filters: { search: query } });
  },

  getStats: async (tenantId?: number) => {
    const params = tenantId ? `?tenantId=${tenantId}` : '';
    const response = await fetch(`/api/customers/stats${params}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch customer stats: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data;
  },
};

// Create base hooks using the factory
const baseHooks = createEntityHooks('customers', customerApi);

// Export base hooks with customer-specific names
export const useCustomers = baseHooks.useEntities;
export const useCustomer = baseHooks.useEntity;
export const useCustomersByTenant = baseHooks.useEntitiesByTenant;
export const useCustomerStats = baseHooks.useEntityStats;
export const useCustomerSearch = baseHooks.useEntitySearch;
export const useCreateCustomer = baseHooks.useCreateEntity;
export const useUpdateCustomer = baseHooks.useUpdateEntity;
export const useDeleteCustomer = baseHooks.useDeleteEntity;
export const useBulkCustomerOperation = baseHooks.useBulkOperation;

// Additional customer-specific hooks
export function useToggleCustomerActive() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string): Promise<Customer> => {
      const response = await fetch(`/api/customers/${id}?action=toggle-active`, {
        method: 'PATCH',
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to toggle customer status');
      }
      const result = await response.json();
      return result.data;
    },
    onSuccess: (updatedCustomer) => {
      // Update cache with updated customer
      queryClient.setQueryData(baseHooks.queryKeys.detail(updatedCustomer.id), updatedCustomer);

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: baseHooks.queryKeys.lists() });
      if (updatedCustomer.tenantId) {
        queryClient.invalidateQueries({
          queryKey: baseHooks.queryKeys.byTenant(updatedCustomer.tenantId)
        });
        queryClient.invalidateQueries({
          queryKey: baseHooks.queryKeys.stats(updatedCustomer.tenantId)
        });
      }
      queryClient.invalidateQueries({ queryKey: baseHooks.queryKeys.stats() });
    },
  });
}

export function useDuplicateCustomer() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, email }: { id: string; email?: string }): Promise<Customer> => {
      const response = await fetch(`/api/customers/${id}/duplicate`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email }),
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to duplicate customer');
      }
      const result = await response.json();
      return result.data;
    },
    onSuccess: (newCustomer) => {
      // Update cache with new customer
      queryClient.setQueryData(baseHooks.queryKeys.detail(newCustomer.id), newCustomer);

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: baseHooks.queryKeys.lists() });
      if (newCustomer.tenantId) {
        queryClient.invalidateQueries({
          queryKey: baseHooks.queryKeys.byTenant(newCustomer.tenantId)
        });
        queryClient.invalidateQueries({
          queryKey: baseHooks.queryKeys.stats(newCustomer.tenantId)
        });
      }
      queryClient.invalidateQueries({ queryKey: baseHooks.queryKeys.stats() });
    },
  });
}

// Custom hook for getting customer with address
export function useCustomerWithAddress(id: string) {
  return useQuery({
    queryKey: ['customer-with-address', id],
    queryFn: () => customerApi.getByIdWithAddress(id),
    enabled: !!id,
  });
}

// Export query keys for external use
export const customerQueryKeys = baseHooks.queryKeys;
