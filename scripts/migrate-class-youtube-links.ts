// Migration script to add youtube_links JSONB field to classes table
// and migrate existing class_youtube_links data
// Run with: npx tsx scripts/migrate-class-youtube-links.ts

import { db } from "../src/lib/db";
import { classes, class_youtube_links } from "../src/lib/db/schema";
import { eq } from "drizzle-orm";
import { sql } from "drizzle-orm";

async function migrateClassYoutubeLinks() {
  try {
    console.log("🚀 Starting migration: Add youtube_links JSONB field to classes table");

    // Step 1: Add the new JSONB column to classes table
    console.log("📝 Adding youtube_links column to classes table...");
    
    await db.execute(sql`
      ALTER TABLE classes 
      ADD COLUMN IF NOT EXISTS youtube_links JSONB DEFAULT '[]'::jsonb
    `);

    console.log("✅ Column added successfully");

    // Step 2: Migrate existing data from class_youtube_links to classes.youtube_links
    console.log("📦 Migrating existing class_youtube_links data...");

    // Get all existing class youtube links grouped by class_id
    const existingLinks = await db
      .select()
      .from(class_youtube_links);

    console.log(`📋 Found ${existingLinks.length} existing links to migrate`);

    // Group links by class_id
    const linksByClass = new Map<string, any[]>();
    
    for (const link of existingLinks) {
      if (!linksByClass.has(link.class_id)) {
        linksByClass.set(link.class_id, []);
      }
      
      linksByClass.get(link.class_id)!.push({
        id: link.id,
        yt_url: link.yt_url
      });
    }

    console.log(`🔄 Updating ${linksByClass.size} classes with their YouTube links...`);

    // Update each class with its links
    let updatedCount = 0;
    for (const [classId, links] of linksByClass) {
      try {
        await db
          .update(classes)
          .set({ 
            youtube_links: links,
            updatedAt: new Date()
          })
          .where(eq(classes.id, classId));
        
        updatedCount++;
        console.log(`✅ Updated class ${classId} with ${links.length} YouTube links`);
      } catch (error) {
        console.error(`❌ Failed to update class ${classId}:`, error);
      }
    }

    console.log(`🎉 Migration completed! Updated ${updatedCount} classes`);

    // Step 3: Verify migration
    console.log("🔍 Verifying migration...");
    
    const classesWithLinks = await db
      .select({
        id: classes.id,
        name: classes.name,
        youtube_links: classes.youtube_links
      })
      .from(classes)
      .where(sql`jsonb_array_length(youtube_links) > 0`);

    console.log(`✅ Verification: ${classesWithLinks.length} classes have youtube_links data`);

    // Show sample data
    if (classesWithLinks.length > 0) {
      console.log("\n📋 Sample migrated data:");
      classesWithLinks.slice(0, 3).forEach((cls, index) => {
        console.log(`${index + 1}. Class: ${cls.name}`);
        console.log(`   Links: ${JSON.stringify(cls.youtube_links, null, 2)}`);
      });
    }

    console.log("\n⚠️  IMPORTANT: After verifying the migration is successful,");
    console.log("   you can optionally drop the old class_youtube_links table:");
    console.log("   DROP TABLE class_youtube_links;");
    console.log("\n   But keep it for now as backup until you're 100% sure migration worked correctly.");

  } catch (error) {
    console.error("💥 Migration failed:", error);
    throw error;
  }
}

// Run the migration
migrateClassYoutubeLinks()
  .then(() => {
    console.log("✅ Migration script completed successfully");
    process.exit(0);
  })
  .catch((error) => {
    console.error("💥 Migration script failed:", error);
    process.exit(1);
  });
