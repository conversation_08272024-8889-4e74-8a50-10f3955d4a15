/**
 * Authentication Middleware for Public API
 * 
 * FAANG-level middleware implementation with:
 * - Multiple authentication methods support
 * - Request context enrichment
 * - Security headers injection
 * - Comprehensive error handling
 * - Performance monitoring
 */

import { NextRequest, NextResponse } from 'next/server';
import { APIKeyService } from '../auth/api-key-service';
import { RateLimiterService } from '../auth/rate-limiter-service';
import { AuditService } from '../auth/audit-service';
import { AuthorizationContext, SecurityHeaders, APIError } from '../auth-types';
import { createId } from '@paralleldrive/cuid2';

interface AuthMiddlewareConfig {
  requiredPermission?: {
    resource: string;
    action: string;
  };
  skipRateLimit?: boolean;
  skipAudit?: boolean;
  allowAnonymous?: boolean;
}

interface RequestContext {
  requestId: string;
  startTime: number;
  authContext?: AuthorizationContext;
  apiVersion: string;
  clientInfo: {
    ip: string;
    userAgent: string;
    country?: string;
  };
}

export class AuthMiddleware {
  private apiKeyService: APIKeyService;
  private rateLimiter: RateLimiterService;
  private auditService: AuditService;

  constructor() {
    this.apiKeyService = new APIKeyService();
    this.rateLimiter = new RateLimiterService();
    this.auditService = new AuditService();
  }

  /**
   * Main authentication middleware
   */
  async authenticate(
    request: NextRequest,
    config: AuthMiddlewareConfig = {}
  ): Promise<{
    response?: NextResponse;
    context?: RequestContext;
  }> {
    const startTime = Date.now();
    const requestId = createId();

    try {
      // Extract client information
      const clientInfo = this.extractClientInfo(request);
      
      // Create request context
      const context: RequestContext = {
        requestId,
        startTime,
        apiVersion: this.extractAPIVersion(request),
        clientInfo,
      };

      // Add request ID to headers for tracing
      const headers = new Headers();
      headers.set('X-Request-ID', requestId);

      // Apply security headers
      this.applySecurityHeaders(headers);

      // Skip authentication for anonymous endpoints
      if (config.allowAnonymous) {
        return { context };
      }

      // Extract and validate API key
      const apiKey = this.extractAPIKey(request);
      if (!apiKey) {
        return {
          response: this.createErrorResponse(
            'Missing API key',
            'MISSING_API_KEY',
            401,
            requestId,
            headers
          ),
        };
      }

      // Validate API key
      const validation = await this.apiKeyService.validateAPIKey(
        apiKey,
        config.requiredPermission
      );

      if (!validation.isValid) {
        // Log failed authentication
        if (!config.skipAudit) {
          await this.auditService.log({
            action: 'api_auth_failed',
            resource: 'api_key',
            tenantId: 0, // Unknown tenant for failed auth
            ip: clientInfo.ip,
            userAgent: clientInfo.userAgent,
            requestId,
            success: false,
            error: validation.error,
          });
        }

        return {
          response: this.createErrorResponse(
            validation.error || 'Authentication failed',
            'AUTH_FAILED',
            401,
            requestId,
            headers
          ),
        };
      }

      // Create authorization context
      const authContext: AuthorizationContext = {
        tenantId: validation.apiKey!.tenantId,
        apiKeyId: validation.apiKey!.id,
        permissions: validation.apiKey!.permissions,
        ipAddress: clientInfo.ip,
        userAgent: clientInfo.userAgent,
        requestId,
      };

      context.authContext = authContext;

      // Apply rate limiting
      if (!config.skipRateLimit) {
        try {
          await this.rateLimiter.checkRateLimit(
            validation.apiKey!.id,
            validation.apiKey!.rateLimit
          );
        } catch (error) {
          if (error instanceof Error && error.name === 'RateLimitError') {
            const rateLimitError = error as any;
            headers.set('Retry-After', rateLimitError.retryAfter.toString());
            
            return {
              response: this.createErrorResponse(
                'Rate limit exceeded',
                'RATE_LIMIT_EXCEEDED',
                429,
                requestId,
                headers
              ),
            };
          }
          throw error;
        }
      }

      // Add rate limit headers
      if (validation.rateLimitStatus) {
        headers.set('X-RateLimit-Limit', validation.rateLimitStatus.limit.toString());
        headers.set('X-RateLimit-Remaining', validation.rateLimitStatus.remaining.toString());
        headers.set('X-RateLimit-Reset', validation.rateLimitStatus.resetTime.toISOString());
      }

      // Log successful authentication
      if (!config.skipAudit) {
        await this.auditService.log({
          action: 'api_auth_success',
          resource: 'api_key',
          tenantId: authContext.tenantId,
          apiKeyId: authContext.apiKeyId,
          ip: clientInfo.ip,
          userAgent: clientInfo.userAgent,
          requestId,
          success: true,
        });
      }

      return { context };

    } catch (error) {
      console.error('Authentication middleware error:', error);
      
      return {
        response: this.createErrorResponse(
          'Internal authentication error',
          'AUTH_INTERNAL_ERROR',
          500,
          requestId
        ),
      };
    }
  }

  /**
   * Extract API key from request headers
   */
  private extractAPIKey(request: NextRequest): string | null {
    // Try multiple header formats
    const authHeader = request.headers.get('Authorization');
    if (authHeader?.startsWith('Bearer ')) {
      return authHeader.substring(7);
    }

    // Try X-API-Key header
    const apiKeyHeader = request.headers.get('X-API-Key');
    if (apiKeyHeader) {
      return apiKeyHeader;
    }

    // Try query parameter (less secure, for development only)
    const url = new URL(request.url);
    const apiKeyParam = url.searchParams.get('api_key');
    if (apiKeyParam && process.env.NODE_ENV === 'development') {
      return apiKeyParam;
    }

    return null;
  }

  /**
   * Extract client information from request
   */
  private extractClientInfo(request: NextRequest): {
    ip: string;
    userAgent: string;
    country?: string;
  } {
    // Get IP address (handle various proxy headers)
    const ip = 
      request.headers.get('x-forwarded-for')?.split(',')[0] ||
      request.headers.get('x-real-ip') ||
      request.headers.get('cf-connecting-ip') ||
      request.ip ||
      'unknown';

    const userAgent = request.headers.get('user-agent') || 'unknown';
    const country = request.headers.get('cf-ipcountry'); // Cloudflare country header

    return {
      ip: ip.trim(),
      userAgent,
      country: country || undefined,
    };
  }

  /**
   * Extract API version from request
   */
  private extractAPIVersion(request: NextRequest): string {
    // Try header first
    const versionHeader = request.headers.get('X-API-Version');
    if (versionHeader) {
      return versionHeader;
    }

    // Try URL path
    const url = new URL(request.url);
    const pathMatch = url.pathname.match(/\/api\/(v\d+)\//);
    if (pathMatch) {
      return pathMatch[1];
    }

    // Default version
    return 'v1';
  }

  /**
   * Apply security headers
   */
  private applySecurityHeaders(headers: Headers): void {
    const securityHeaders: SecurityHeaders = {
      'X-Content-Type-Options': 'nosniff',
      'X-Frame-Options': 'DENY',
      'X-XSS-Protection': '1; mode=block',
      'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
      'Content-Security-Policy': "default-src 'self'",
      'Referrer-Policy': 'strict-origin-when-cross-origin',
      'Permissions-Policy': 'geolocation=(), microphone=(), camera=()',
    };

    Object.entries(securityHeaders).forEach(([key, value]) => {
      headers.set(key, value);
    });

    // Add CORS headers for public API
    headers.set('Access-Control-Allow-Origin', 'http://localhost:3002');
    headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-API-Key, X-API-Version, x-api-key' );
    headers.set('Access-Control-Expose-Headers', 'X-Request-ID, X-RateLimit-Limit, X-RateLimit-Remaining, X-RateLimit-Reset');
  }

  /**
   * Create standardized error response
   */
  private createErrorResponse(
    message: string,
    code: string,
    status: number,
    requestId: string,
    headers?: Headers
  ): NextResponse {
    const error: APIError = {
      code,
      message,
      details: {
        requestId,
        timestamp: new Date().toISOString(),
      },
    };

    const response = NextResponse.json(
      {
        success: false,
        error,
        metadata: {
          requestId,
          timestamp: new Date().toISOString(),
          version: 'v1',
          processingTime: 0,
        },
      },
      { status }
    );

    // Apply headers
    if (headers) {
      headers.forEach((value, key) => {
        response.headers.set(key, value);
      });
    }

    return response;
  }

  /**
   * Middleware for OPTIONS requests (CORS preflight)
   */
  handleOptions(request: NextRequest): NextResponse {
    const headers = new Headers();
    this.applySecurityHeaders(headers);
    // CORS headers (must match global CORS config)
    headers.set('Access-Control-Allow-Origin', 'http://localhost:3002');
    headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-API-Key, x-api-key, X-API-Version');
    headers.set('Access-Control-Expose-Headers', 'X-Request-ID, X-RateLimit-Limit, X-RateLimit-Remaining, X-RateLimit-Reset');
    return new NextResponse(null, {
      status: 200,
      headers,
    });
  }

  /**
   * Create success response with metadata
   */
  createSuccessResponse(
    data: any,
    context: RequestContext,
    pagination?: any
  ): NextResponse {
    const processingTime = Date.now() - context.startTime;

    const response = {
      success: true,
      data,
      metadata: {
        requestId: context.requestId,
        timestamp: new Date().toISOString(),
        version: context.apiVersion,
        processingTime,
      },
      ...(pagination && { pagination }),
    };

    const nextResponse = NextResponse.json(response);

    // Apply security headers
    const headers = new Headers();
    this.applySecurityHeaders(headers);
    headers.forEach((value, key) => {
      nextResponse.headers.set(key, value);
    });

    return nextResponse;
  }
}
