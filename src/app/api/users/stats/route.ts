import { NextRequest, NextResponse } from "next/server";
import { auth } from "@/lib/auth/config";
import { db } from "@/lib/db";
import { users } from "@/lib/db/schema";
import { eq, count, and, gte, isNotNull, sql } from "drizzle-orm";

// GET /api/users/stats - Get user statistics
export async function GET(request: NextRequest) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Only admins can view user stats
    if (session.user.role !== "admin") {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Get total users
    const [totalUsersResult] = await db
      .select({ count: count() })
      .from(users);

    // Get active users (users with verified email)
    const [activeUsersResult] = await db
      .select({ count: count() })
      .from(users)
      .where(isNotNull(users.emailVerified));

    // Get admin users
    const [adminUsersResult] = await db
      .select({ count: count() })
      .from(users)
      .where(eq(users.role, "admin"));

    // Get recent signups (last 30 days) - using raw SQL to avoid serialization issues
    let recentSignupsResult;
    try {
      const result = await db.execute(sql`
        SELECT COUNT(*) as count
        FROM users
        WHERE created_at >= NOW() - INTERVAL '30 days'
      `);
      recentSignupsResult = { count: Number(result.rows[0]?.count || 0) };
    } catch (error) {
      console.error("Error in recent signups query:", error);
      recentSignupsResult = { count: 0 };
    }

    const stats = {
      totalUsers: totalUsersResult.count,
      activeUsers: activeUsersResult.count,
      adminUsers: adminUsersResult.count,
      recentSignups: recentSignupsResult.count,
    };

    return NextResponse.json(stats);
  } catch (error) {
    console.error("Error fetching user stats:", error);
    return NextResponse.json(
      { error: "Failed to fetch user stats" },
      { status: 500 }
    );
  }
}
