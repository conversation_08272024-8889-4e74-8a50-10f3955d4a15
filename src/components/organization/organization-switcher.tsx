"use client";

import { useState, useEffect } from "react";
import { Check, ChevronsUpDown, Plus, Building } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import type { AuthUser } from "@/types/auth";

interface Organization {
  id: string;
  name: string;
  slug: string;
  logo?: string;
  role: "owner" | "admin" | "member";
}

interface OrganizationSwitcherProps {
  user: AuthUser;
}

export function OrganizationSwitcher({ user }: OrganizationSwitcherProps) {
  const [open, setOpen] = useState(false);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [currentOrg, setCurrentOrg] = useState<Organization | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchOrganizations();
  }, []);

  const fetchOrganizations = async () => {
    try {
      const response = await fetch("/api/organizations");
      if (response.ok) {
        const data = await response.json();
        setOrganizations(data);
        
        // Set current organization
        const current = data.find((org: Organization) => org.id === user.organizationId);
        setCurrentOrg(current || null);
      }
    } catch (error) {
      console.error("Failed to fetch organizations:", error);
    } finally {
      setLoading(false);
    }
  };

  const switchOrganization = async (orgId: string) => {
    try {
      const response = await fetch("/api/organizations/switch", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ organizationId: orgId }),
      });

      if (response.ok) {
        // Refresh the page to update the session
        window.location.reload();
      }
    } catch (error) {
      console.error("Failed to switch organization:", error);
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  if (loading) {
    return (
      <div className="flex items-center space-x-2">
        <div className="h-8 w-8 bg-muted rounded-lg animate-pulse" />
        <div className="h-4 w-24 bg-muted rounded animate-pulse" />
      </div>
    );
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          aria-label="Select organization"
          className="w-[200px] justify-between"
        >
          {currentOrg ? (
            <div className="flex items-center space-x-2">
              <Avatar className="h-5 w-5">
                <AvatarImage src={currentOrg.logo} alt={currentOrg.name} />
                <AvatarFallback className="text-xs">
                  {getInitials(currentOrg.name)}
                </AvatarFallback>
              </Avatar>
              <span className="truncate">{currentOrg.name}</span>
            </div>
          ) : (
            <div className="flex items-center space-x-2">
              <Building className="h-4 w-4" />
              <span>Personal</span>
            </div>
          )}
          <ChevronsUpDown className="ml-auto h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[200px] p-0">
        <Command>
          <CommandInput placeholder="Search organizations..." />
          <CommandList>
            <CommandEmpty>No organizations found.</CommandEmpty>
            
            {/* Personal Account */}
            <CommandGroup heading="Personal">
              <CommandItem
                onSelect={() => {
                  switchOrganization("");
                  setOpen(false);
                }}
                className="text-sm"
              >
                <Building className="mr-2 h-4 w-4" />
                Personal Account
                {!currentOrg && (
                  <Check className="ml-auto h-4 w-4" />
                )}
              </CommandItem>
            </CommandGroup>

            {/* Organizations */}
            {organizations.length > 0 && (
              <>
                <CommandSeparator />
                <CommandGroup heading="Organizations">
                  {organizations.map((org) => (
                    <CommandItem
                      key={org.id}
                      onSelect={() => {
                        switchOrganization(org.id);
                        setOpen(false);
                      }}
                      className="text-sm"
                    >
                      <Avatar className="mr-2 h-4 w-4">
                        <AvatarImage src={org.logo} alt={org.name} />
                        <AvatarFallback className="text-xs">
                          {getInitials(org.name)}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex flex-col">
                        <span>{org.name}</span>
                        <span className="text-xs text-muted-foreground">
                          {org.role}
                        </span>
                      </div>
                      {currentOrg?.id === org.id && (
                        <Check className="ml-auto h-4 w-4" />
                      )}
                    </CommandItem>
                  ))}
                </CommandGroup>
              </>
            )}

            <CommandSeparator />
            <CommandGroup>
              <CommandItem
                onSelect={() => {
                  setOpen(false);
                  // Open create organization dialog
                }}
              >
                <Plus className="mr-2 h-4 w-4" />
                Create Organization
              </CommandItem>
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
