"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { 
  MapPin, 
  Plus, 
  Building, 
  BarChart3,
  AlertCircle,
  CheckCircle
} from "lucide-react";
import { LocationForm } from "@/components/forms/location-form";
import { LocationList } from "@/components/locations/location-list";
import { SuccessToast } from "@/components/ui/success-toast";
import { SuccessModal } from "@/components/ui/success-modal";
import { Confetti } from "@/components/ui/confetti";
import { Location, NewLocation } from "@/lib/db/schema";
import { useLocations, useCreateLocation, useUpdateLocation } from "@/lib/hooks/queries/use-location-queries";
import { useTenants } from "@/lib/hooks/queries/use-tenant-queries";

export default function LocationsPage() {
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingLocation, setEditingLocation] = useState<Location | null>(null);
  const [selectedTenantId, setSelectedTenantId] = useState<number | null>(null);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [submitSuccess, setSubmitSuccess] = useState<string | null>(null);

  // Animation states
  const [showSuccessToast, setShowSuccessToast] = useState(false);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showConfetti, setShowConfetti] = useState(false);
  const [createdLocation, setCreatedLocation] = useState<Location | null>(null);

  const { data: locations = [], isLoading: locationsLoading } = useLocations();
  const { data: tenantsData, isLoading: tenantsLoading } = useTenants();
  const tenants = tenantsData?.tenants || [];
  const createLocationMutation = useCreateLocation();
  const updateLocationMutation = useUpdateLocation();

  // Calculate stats
  const stats = {
    total: locations.length,
    byTenant: tenants.reduce((acc, tenant) => {
      acc[tenant.id] = locations.filter(loc => loc.tenantId === tenant.id).length;
      return acc;
    }, {} as Record<number, number>),
  };

  const handleCreateLocation = async (data: Omit<NewLocation, "id" | "createdAt" | "updatedAt">) => {
    try {
      setSubmitError(null);
      const newLocation = await createLocationMutation.mutateAsync(data);

      // Store created location for modal
      setCreatedLocation(newLocation);

      // Close form first
      setIsFormOpen(false);

      // Start celebration sequence
      setTimeout(() => {
        setShowConfetti(true);
        setShowSuccessToast(true);
        setShowSuccessModal(true);
      }, 300);

      // Auto-hide confetti
      setTimeout(() => setShowConfetti(false), 4000);

    } catch (error) {
      setSubmitError(error instanceof Error ? error.message : "Failed to create location");
    }
  };

  const handleUpdateLocation = async (data: Omit<NewLocation, "id" | "createdAt" | "updatedAt">) => {
    if (!editingLocation) return;

    try {
      setSubmitError(null);
      await updateLocationMutation.mutateAsync({
        locationId: editingLocation.id,
        data: data
      });
      setSubmitSuccess("Location updated successfully!");
      setEditingLocation(null);
      setTimeout(() => setSubmitSuccess(null), 3000);
    } catch (error) {
      setSubmitError(error instanceof Error ? error.message : "Failed to update location");
    }
  };

  const handleEdit = (location: Location) => {
    setEditingLocation(location);
  };

  const handleAdd = () => {
    setEditingLocation(null);
    setIsFormOpen(true);
  };

  const handleCloseForm = () => {
    setIsFormOpen(false);
    setEditingLocation(null);
    setSubmitError(null);
  };

  const handleCloseSuccessAnimations = () => {
    setShowSuccessToast(false);
    setShowSuccessModal(false);
    setShowConfetti(false);
    setCreatedLocation(null);
  };

  const handleViewLocation = () => {
    handleCloseSuccessAnimations();
    // Could navigate to location detail or highlight in list
  };

  const handleAddAnother = () => {
    handleCloseSuccessAnimations();
    setIsFormOpen(true);
  };

  if (locationsLoading || tenantsLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Locations</h1>
          <p className="text-gray-500 dark:text-gray-400">
            Manage location addresses and contact information
          </p>
        </div>
        <Button onClick={handleAdd}>
          <Plus className="h-4 w-4 mr-2" />
          Add Location
        </Button>
      </div>

      {/* Success/Error Messages */}
      {submitSuccess && (
        <Alert>
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>{submitSuccess}</AlertDescription>
        </Alert>
      )}

      {submitError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{submitError}</AlertDescription>
        </Alert>
      )}

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Locations</CardTitle>
            <MapPin className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">
              Across all tenants
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Tenants</CardTitle>
            <Building className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Object.values(stats.byTenant).filter(count => count > 0).length}
            </div>
            <p className="text-xs text-muted-foreground">
              With locations
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average per Tenant</CardTitle>
            <BarChart3 className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {tenants.length > 0 ? (stats.total / tenants.length).toFixed(1) : "0"}
            </div>
            <p className="text-xs text-muted-foreground">
              Locations per tenant
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="all" className="space-y-4">
        <TabsList>
          <TabsTrigger value="all">All Locations</TabsTrigger>
          {tenants.map((tenant) => (
            <TabsTrigger key={tenant.id} value={`tenant-${tenant.id}`}>
              {tenant.name}
              <Badge variant="secondary" className="ml-2">
                {stats.byTenant[tenant.id] || 0}
              </Badge>
            </TabsTrigger>
          ))}
        </TabsList>

        <TabsContent value="all">
          <LocationList
            onEdit={handleEdit}
            onAdd={handleAdd}
            showActions={true}
          />
        </TabsContent>

        {tenants.map((tenant) => (
          <TabsContent key={tenant.id} value={`tenant-${tenant.id}`}>
            <LocationList
              tenantId={tenant.id}
              onEdit={handleEdit}
              onAdd={() => {
                setSelectedTenantId(tenant.id);
                handleAdd();
              }}
              showActions={true}
            />
          </TabsContent>
        ))}
      </Tabs>

      {/* Create/Edit Dialog */}
      <Dialog open={isFormOpen || !!editingLocation} onOpenChange={handleCloseForm}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {editingLocation ? "Edit Location" : "Add New Location"}
            </DialogTitle>
          </DialogHeader>
          <LocationForm
            location={editingLocation || undefined}
            tenantId={selectedTenantId || editingLocation?.tenantId || (tenants[0]?.id ?? 1)}
            onSubmit={editingLocation ? handleUpdateLocation : handleCreateLocation}
            onCancel={handleCloseForm}
            className="border-0 shadow-none"
          />
        </DialogContent>
      </Dialog>

      {/* Success Animations */}
      <Confetti isActive={showConfetti} />

      <SuccessToast
        isVisible={showSuccessToast}
        onClose={() => setShowSuccessToast(false)}
        title="Location Created Successfully!"
        description={createdLocation ? `${createdLocation.addressLine1} has been added to your locations.` : undefined}
        type="location"
        action={{
          label: "View All Locations",
          onClick: handleViewLocation
        }}
      />

      <SuccessModal
        isOpen={showSuccessModal}
        onClose={handleCloseSuccessAnimations}
        title="🎉 Location Added Successfully!"
        description="Your new location has been saved and is now available in your locations list."
        locationData={createdLocation ? {
          address: createdLocation.addressLine1 || "",
          city: createdLocation.city || "",
          country: createdLocation.country || ""
        } : undefined}
        actions={{
          primary: {
            label: "Add Another Location",
            onClick: handleAddAnother
          },
          secondary: {
            label: "View All Locations",
            onClick: handleViewLocation
          }
        }}
      />
    </div>
  );
}
