import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { Role, UserRole, Permission } from '@/lib/db/schema';

/**
 * Role Queries Hooks
 * 
 * Hooks untuk manage state dan data fetching roles menggunakan TanStack Query.
 * Pattern ini mengikuti use-class-queries.ts yang sudah established dan proven.
 * 
 * Ini kayak "jembatan" antara UI components dan service layer.
 * Semua caching, error handling, dan optimistic updates dihandle di sini.
 */

// Query Keys - ini penting untuk caching strategy
export const roleKeys = {
  all: ['roles'] as const,
  lists: () => [...roleKeys.all, 'list'] as const,
  list: (filters: Record<string, any>) => [...roleKeys.lists(), { filters }] as const,
  details: () => [...roleKeys.all, 'detail'] as const,
  detail: (id: string) => [...roleKeys.details(), id] as const,
  byTenant: (tenantId: number | null) => [...roleKeys.all, 'tenant', tenantId] as const,
  system: () => [...roleKeys.all, 'system'] as const,
  search: (params: any) => [...roleKeys.all, 'search', params] as const,
  userRoles: () => [...roleKeys.all, 'user-roles'] as const,
  userRolesList: (filters: Record<string, any>) => [...roleKeys.userRoles(), { filters }] as const,
};

// API Functions - ini yang handle komunikasi dengan backend
const roleApi = {
  search: async (
    tenantId?: number | null,
    search?: string,
    isSystemRole?: boolean,
    limit = 20,
    offset = 0
  ) => {
    const params = new URLSearchParams({
      limit: limit.toString(),
      offset: offset.toString(),
    });
    
    if (tenantId !== undefined) {
      params.append('tenantId', tenantId?.toString() || 'null');
    }
    if (search) {
      params.append('search', search);
    }
    if (isSystemRole !== undefined) {
      params.append('isSystemRole', isSystemRole.toString());
    }

    const response = await fetch(`/api/roles?${params}`);
    if (!response.ok) {
      throw new Error(`Failed to search roles: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data;
  },

  getById: async (id: string): Promise<Role> => {
    const response = await fetch(`/api/roles/${id}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch role: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data;
  },

  getSystemRoles: async (): Promise<Role[]> => {
    const response = await fetch('/api/roles/system');
    if (!response.ok) {
      throw new Error(`Failed to fetch system roles: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data;
  },

  getByTenant: async (tenantId: number): Promise<Role[]> => {
    const params = new URLSearchParams({
      tenantId: tenantId.toString(),
    });
    const response = await fetch(`/api/roles?${params}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch roles: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data?.roles || [];
  },

  create: async (data: {
    tenantId?: number | null;
    name: string;
    display_name: string;
    description?: string;
    is_system_role?: boolean;
    hierarchy_level?: number;
    permissions?: any[];
  }): Promise<Role> => {
    const response = await fetch('/api/roles', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      throw new Error(`Failed to create role: ${response.statusText}`);
    }
    const result = await response.json();
    return result.data;
  },

  update: async (id: string, data: {
    name?: string;
    display_name?: string;
    description?: string;
    hierarchy_level?: number;
    permissions?: any[];
    is_active?: boolean;
  }): Promise<Role> => {
    const response = await fetch(`/api/roles/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      throw new Error(`Failed to update role: ${response.statusText}`);
    }
    const result = await response.json();
    return result.data;
  },

  delete: async (id: string): Promise<void> => {
    const response = await fetch(`/api/roles/${id}`, {
      method: 'DELETE',
    });
    if (!response.ok) {
      throw new Error(`Failed to delete role: ${response.statusText}`);
    }
  },

  assignRole: async (data: {
    userId: string;
    roleId: string;
    tenantId?: number | null;
    assignedBy?: string;
    expiresAt?: string;
  }): Promise<UserRole> => {
    const response = await fetch('/api/roles/assign', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      throw new Error(`Failed to assign role: ${response.statusText}`);
    }
    const result = await response.json();
    return result.data;
  },

  revokeRole: async (data: {
    userId: string;
    roleId: string;
    tenantId?: number | null;
  }): Promise<void> => {
    const response = await fetch('/api/roles/revoke', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });
    if (!response.ok) {
      throw new Error(`Failed to revoke role: ${response.statusText}`);
    }
  },

  getUserRoles: async (
    tenantId?: number | null,
    userId?: string,
    limit = 20,
    offset = 0
  ) => {
    const params = new URLSearchParams({
      limit: limit.toString(),
      offset: offset.toString(),
    });
    
    if (tenantId !== undefined) {
      params.append('tenantId', tenantId?.toString() || 'null');
    }
    if (userId) {
      params.append('userId', userId);
    }

    const response = await fetch(`/api/roles/user-roles?${params}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch user roles: ${response.statusText}`);
    }
    const data = await response.json();
    return data.data;
  },
};

// Query Hooks - ini yang dipake di components
export function useRoles(tenantId?: number | null, isSystemRole?: boolean) {
  return useQuery({
    queryKey: roleKeys.search({ tenantId, isSystemRole }),
    queryFn: () => roleApi.search(tenantId, undefined, isSystemRole),
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: false,
  });
}

export function useRoleSearch(
  tenantId?: number | null,
  search?: string,
  isSystemRole?: boolean,
  limit = 20,
  offset = 0
) {
  return useQuery({
    queryKey: roleKeys.search({ tenantId, search, isSystemRole, limit, offset }),
    queryFn: () => roleApi.search(tenantId, search, isSystemRole, limit, offset),
    enabled: true,
    staleTime: 30 * 1000, // 30 seconds - shorter for better responsiveness
    refetchInterval: 60 * 1000, // Auto-refetch every minute
    refetchOnWindowFocus: true, // Refetch when window gains focus
  });
}

export function useRole(id: string) {
  return useQuery({
    queryKey: roleKeys.detail(id),
    queryFn: () => roleApi.getById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
}

export function useSystemRoles() {
  return useQuery({
    queryKey: roleKeys.system(),
    queryFn: roleApi.getSystemRoles,
    staleTime: 10 * 60 * 1000, // 10 minutes - system roles jarang berubah
  });
}

export function useRolesByTenant(tenantId: number) {
  return useQuery({
    queryKey: roleKeys.byTenant(tenantId),
    queryFn: () => roleApi.getByTenant(tenantId),
    enabled: !!tenantId,
    staleTime: 5 * 60 * 1000,
  });
}

export function useUserRoles(
  tenantId?: number | null,
  userId?: string,
  limit = 20,
  offset = 0
) {
  return useQuery({
    queryKey: roleKeys.userRolesList({ tenantId, userId, limit, offset }),
    queryFn: () => roleApi.getUserRoles(tenantId, userId, limit, offset),
    staleTime: 2 * 60 * 1000,
  });
}

// Mutation Hooks - ini untuk create, update, delete
export function useCreateRole() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: roleApi.create,
    onSuccess: (data) => {
      console.log("✅ Role created successfully:", data);
      // Invalidate all role queries to force refresh
      queryClient.invalidateQueries({ queryKey: roleKeys.all });
      // Also invalidate specific search queries
      queryClient.invalidateQueries({ queryKey: roleKeys.lists() });
    },
    onError: (error) => {
      console.error("❌ Role creation failed:", error);
    },
  });
}

export function useUpdateRole() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: {
      id: string;
      data: {
        name?: string;
        display_name?: string;
        description?: string;
        hierarchy_level?: number;
        permissions?: any[];
        is_active?: boolean;
      }
    }) => {
      return roleApi.update(id, data);
    },
    onSuccess: (data) => {
      console.log("✅ Role updated successfully:", data);
      // Invalidate all role queries to force refresh
      queryClient.invalidateQueries({ queryKey: roleKeys.all });
      queryClient.invalidateQueries({ queryKey: roleKeys.lists() });
    },
    onError: (error) => {
      console.error("❌ Role update failed:", error);
    },
  });
}

export function useDeleteRole() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: roleApi.delete,
    onSuccess: () => {
      console.log("✅ Role deleted successfully");
      // Invalidate all role queries to force refresh
      queryClient.invalidateQueries({ queryKey: roleKeys.all });
      queryClient.invalidateQueries({ queryKey: roleKeys.lists() });
    },
    onError: (error) => {
      console.error("❌ Role deletion failed:", error);
    },
  });
}

export function useAssignRole() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: roleApi.assignRole,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: roleKeys.userRoles() });
    },
  });
}

export function useRevokeRole() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: roleApi.revokeRole,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: roleKeys.userRoles() });
    },
  });
}

// Export types
export interface RoleSearchParams {
  tenantId?: number | null;
  search?: string;
  isSystemRole?: boolean;
  limit?: number;
  offset?: number;
}

export interface RoleFormData {
  tenantId?: number | null;
  name: string;
  display_name: string;
  description?: string;
  is_system_role?: boolean;
  hierarchy_level?: number;
  permissions?: string[];
}

export interface RoleAssignmentData {
  userId: string;
  roleId: string;
  tenantId?: number | null;
  assignedBy?: string;
  expiresAt?: string;
}
