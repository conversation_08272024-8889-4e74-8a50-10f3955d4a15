import { db } from "@/lib/db";
import { membership_plans, locations, membership_plan_locations } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { createId } from "@paralleldrive/cuid2";

/**
 * Seed script untuk membership plans dan locations
 * Menggunakan Drizzle ORM sesuai best practices
 */

async function seedMembershipPlansAndLocations() {
  console.log("🌱 Seeding membership plans and locations...");

  try {
    // 1. Create sample membership plans untuk tenant 1
    console.log("📦 Creating membership plans...");
    
    const membershipPlansData = [
      {
        id: createId(),
        tenantId: 1,
        name: "Basic Monthly",
        description: "Basic gym access for 1 month",
        price: 50000, // 50k IDR
        currency: "IDR",
        duration_value: 1,
        duration_unit: "month",
        is_active: true,
      },
      {
        id: createId(),
        tenantId: 1,
        name: "Premium Monthly",
        description: "Premium gym access with all facilities",
        price: 100000, // 100k IDR
        currency: "IDR",
        duration_value: 1,
        duration_unit: "month",
        is_active: true,
      },
      {
        id: createId(),
        tenantId: 1,
        name: "Annual VIP",
        description: "VIP access for 1 year with personal trainer",
        price: 1000000, // 1M IDR
        currency: "IDR",
        duration_value: 12,
        duration_unit: "month",
        is_active: true,
      },
    ];

    // Insert membership plans
    const insertedPlans = await db.insert(membership_plans).values(membershipPlansData).returning();
    console.log(`✅ Created ${insertedPlans.length} membership plans`);

    // 2. Create sample locations untuk tenant 1
    console.log("📍 Creating locations...");
    
    const locationsData = [
      {
        id: createId(),
        name: "Jakarta Central",
        tenantId: 1,
        addressLine1: "Jl. Sudirman No. 123",
        city: "Jakarta",
        state: "DKI Jakarta",
        country: "Indonesia",
        postalCode: "10220",
        phoneNumber: "+62-21-1234567",
      },
      {
        id: createId(),
        name: "Jakarta South",
        tenantId: 1,
        addressLine1: "Jl. Senopati No. 456",
        city: "Jakarta",
        state: "DKI Jakarta",
        country: "Indonesia",
        postalCode: "12190",
        phoneNumber: "+62-21-7654321",
      },
      {
        id: createId(),
        name: "Bandung Branch",
        tenantId: 1,
        addressLine1: "Jl. Braga No. 789",
        city: "Bandung",
        state: "Jawa Barat",
        country: "Indonesia",
        postalCode: "40111",
        phoneNumber: "+62-22-9876543",
      },
    ];

    // Insert locations
    const insertedLocations = await db.insert(locations).values(locationsData).returning();
    console.log(`✅ Created ${insertedLocations.length} locations`);

    // 3. Create sample location assignments
    console.log("🔗 Creating location assignments...");
    
    const assignmentsData = [
      // Basic Monthly - hanya Jakarta Central
      {
        id: createId(),
        membership_plan_id: insertedPlans[0].id,
        location_id: insertedLocations[0].id,
        tenantId: 1,
      },
      // Premium Monthly - Jakarta Central dan South
      {
        id: createId(),
        membership_plan_id: insertedPlans[1].id,
        location_id: insertedLocations[0].id,
        tenantId: 1,
      },
      {
        id: createId(),
        membership_plan_id: insertedPlans[1].id,
        location_id: insertedLocations[1].id,
        tenantId: 1,
      },
      // Annual VIP - semua locations
      {
        id: createId(),
        membership_plan_id: insertedPlans[2].id,
        location_id: insertedLocations[0].id,
        tenantId: 1,
      },
      {
        id: createId(),
        membership_plan_id: insertedPlans[2].id,
        location_id: insertedLocations[1].id,
        tenantId: 1,
      },
      {
        id: createId(),
        membership_plan_id: insertedPlans[2].id,
        location_id: insertedLocations[2].id,
        tenantId: 1,
      },
    ];

    // Insert assignments
    const insertedAssignments = await db.insert(membership_plan_locations).values(assignmentsData).returning();
    console.log(`✅ Created ${insertedAssignments.length} location assignments`);

    console.log("🎉 Seeding completed successfully!");
    console.log("\n📊 Summary:");
    console.log(`- Membership Plans: ${insertedPlans.length}`);
    console.log(`- Locations: ${insertedLocations.length}`);
    console.log(`- Location Assignments: ${insertedAssignments.length}`);

  } catch (error) {
    console.error("❌ Seeding failed:", error);
    throw error;
  }
}

async function cleanupTestData() {
  console.log("🧹 Cleaning up test data...");

  try {
    // Delete in reverse order due to foreign key constraints
    await db.delete(membership_plan_locations).where(eq(membership_plan_locations.tenantId, 1));
    await db.delete(membership_plans).where(eq(membership_plans.tenantId, 1));
    await db.delete(locations).where(eq(locations.tenantId, 1));

    console.log("✅ Cleanup completed successfully!");

  } catch (error) {
    console.error("❌ Cleanup failed:", error);
    throw error;
  }
}

// CLI interface
if (require.main === module) {
  const command = process.argv[2];

  switch (command) {
    case "seed":
      seedMembershipPlansAndLocations()
        .then(() => process.exit(0))
        .catch(() => process.exit(1));
      break;
    
    case "cleanup":
      cleanupTestData()
        .then(() => process.exit(0))
        .catch(() => process.exit(1));
      break;
    
    default:
      console.log("Usage: tsx src/lib/db/seed-membership-plan-locations.ts [seed|cleanup]");
      console.log("  seed    - Create test data");
      console.log("  cleanup - Remove test data");
      process.exit(1);
  }
}
