import { db } from "../db";
import { business_profiles, tenants, type BusinessProfile, type NewBusinessProfile } from "../db/schema";
import { eq, and, desc } from "drizzle-orm";

export class BusinessProfileService {
  /**
   * Get business profile by tenant ID
   */
  static async getByTenantId(tenantId: number): Promise<BusinessProfile | null> {
    const [profile] = await db
      .select()
      .from(business_profiles)
      .where(eq(business_profiles.tenantId, tenantId))
      .limit(1);

    return profile || null;
  }

  /**
   * Create business profile
   */
  static async create(data: Omit<NewBusinessProfile, "id" | "createdAt" | "updatedAt">): Promise<BusinessProfile> {
    // Check if profile already exists for this tenant
    const existingProfile = await this.getByTenantId(data.tenantId!);
    if (existingProfile) {
      throw new Error("Business profile already exists for this tenant");
    }

    const [profile] = await db
      .insert(business_profiles)
      .values(data)
      .returning();

    return profile;
  }

  /**
   * Update business profile
   */
  static async update(
    tenantId: number,
    data: Partial<Omit<NewBusinessProfile, "id" | "tenantId" | "createdAt">>
  ): Promise<BusinessProfile | null> {
    const [profile] = await db
      .update(business_profiles)
      .set({
        ...data,
        updatedAt: new Date(),
      })
      .where(eq(business_profiles.tenantId, tenantId))
      .returning();

    return profile || null;
  }

  /**
   * Delete business profile
   */
  static async delete(tenantId: number): Promise<boolean> {
    const result = await db
      .delete(business_profiles)
      .where(eq(business_profiles.tenantId, tenantId));

    return result.rowCount > 0;
  }

  /**
   * Get business profile with tenant info
   */
  static async getWithTenant(tenantId: number) {
    const result = await db
      .select({
        profile: business_profiles,
        tenant: tenants,
      })
      .from(business_profiles)
      .leftJoin(tenants, eq(business_profiles.tenantId, tenants.id))
      .where(eq(business_profiles.tenantId, tenantId))
      .limit(1);

    return result[0] || null;
  }

  /**
   * Update business logo
   */
  static async updateLogo(tenantId: number, logoUrl: string): Promise<BusinessProfile | null> {
    return await this.update(tenantId, { business_logo: logoUrl });
  }

  /**
   * Update WhatsApp settings
   */
  static async updateWhatsAppSettings(
    tenantId: number,
    settings: {
      whatsapp_number?: string;
      show_whatsapp_floating?: boolean;
    }
  ): Promise<BusinessProfile | null> {
    return await this.update(tenantId, settings);
  }

  /**
   * Get all business profiles (admin only)
   */
  static async getAll(): Promise<BusinessProfile[]> {
    return await db
      .select()
      .from(business_profiles)
      .orderBy(desc(business_profiles.createdAt));
  }

  /**
   * Search business profiles by name
   */
  static async searchByName(query: string): Promise<BusinessProfile[]> {
    return await db
      .select()
      .from(business_profiles)
      .where(
        and(
          // Using ILIKE for case-insensitive search (PostgreSQL specific)
          // For cross-database compatibility, you might want to use like() with toLowerCase()
        )
      )
      .orderBy(desc(business_profiles.createdAt));
  }

  /**
   * Check if business name is available
   */
  static async isBusinessNameAvailable(businessName: string, excludeTenantId?: number): Promise<boolean> {
    const conditions = [eq(business_profiles.business_name, businessName)];
    
    if (excludeTenantId) {
      // Add condition to exclude current tenant when updating
      conditions.push(eq(business_profiles.tenantId, excludeTenantId));
    }

    const [existing] = await db
      .select()
      .from(business_profiles)
      .where(and(...conditions))
      .limit(1);

    return !existing;
  }

  /**
   * Get business profile statistics
   */
  static async getStats() {
    const [totalProfiles] = await db
      .select({ count: business_profiles.id })
      .from(business_profiles);

    const [profilesWithLogo] = await db
      .select({ count: business_profiles.id })
      .from(business_profiles)
      .where(eq(business_profiles.business_logo, null)); // This should be NOT NULL

    const [profilesWithWhatsApp] = await db
      .select({ count: business_profiles.id })
      .from(business_profiles)
      .where(eq(business_profiles.show_whatsapp_floating, true));

    return {
      totalProfiles: totalProfiles?.count || 0,
      profilesWithLogo: profilesWithLogo?.count || 0,
      profilesWithWhatsApp: profilesWithWhatsApp?.count || 0,
    };
  }
}
