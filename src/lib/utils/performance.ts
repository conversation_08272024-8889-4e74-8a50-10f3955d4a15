/**
 * Performance Optimization Utilities
 * 
 * High-performance utilities untuk optimize conflict detection
 * dengan timeout handling, caching, dan circuit breaker patterns.
 */

/**
 * Timeout wrapper untuk database queries
 */
export function withTimeout<T>(
  promise: Promise<T>, 
  timeoutMs: number, 
  errorMessage = "Operation timed out"
): Promise<T> {
  return Promise.race([
    promise,
    new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error(errorMessage)), timeoutMs);
    })
  ]);
}

/**
 * Performance monitoring decorator
 */
export class PerformanceMonitor {
  private static metrics: Map<string, {
    totalTime: number;
    callCount: number;
    slowCalls: number;
    lastCall: Date;
  }> = new Map();

  static async measure<T>(
    operation: string,
    fn: () => Promise<T>,
    slowThreshold = 1000
  ): Promise<T> {
    const startTime = Date.now();
    
    try {
      const result = await fn();
      const duration = Date.now() - startTime;
      
      this.recordMetric(operation, duration, slowThreshold);
      
      if (duration > slowThreshold) {
        console.warn(`🐌 Slow operation detected: ${operation} took ${duration}ms`);
      }
      
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      this.recordMetric(operation, duration, slowThreshold, true);
      throw error;
    }
  }

  private static recordMetric(
    operation: string, 
    duration: number, 
    slowThreshold: number, 
    isError = false
  ) {
    const existing = this.metrics.get(operation) || {
      totalTime: 0,
      callCount: 0,
      slowCalls: 0,
      lastCall: new Date()
    };

    existing.totalTime += duration;
    existing.callCount += 1;
    existing.lastCall = new Date();
    
    if (duration > slowThreshold || isError) {
      existing.slowCalls += 1;
    }

    this.metrics.set(operation, existing);
  }

  static getMetrics(operation?: string) {
    if (operation) {
      return this.metrics.get(operation);
    }
    return Object.fromEntries(this.metrics.entries());
  }

  static clearMetrics() {
    this.metrics.clear();
  }
}

/**
 * Circuit Breaker Pattern Implementation
 */
export class CircuitBreaker {
  private failures = 0;
  private lastFailureTime = 0;
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';

  constructor(
    private failureThreshold = 5,
    private recoveryTimeMs = 30000, // 30 seconds
    private timeoutMs = 5000 // 5 seconds
  ) {}

  async execute<T>(operation: () => Promise<T>): Promise<T> {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime > this.recoveryTimeMs) {
        this.state = 'HALF_OPEN';
      } else {
        throw new Error('Circuit breaker is OPEN - service unavailable');
      }
    }

    try {
      const result = await withTimeout(operation(), this.timeoutMs, 'Circuit breaker timeout');
      
      if (this.state === 'HALF_OPEN') {
        this.reset();
      }
      
      return result;
    } catch (error) {
      this.recordFailure();
      throw error;
    }
  }

  private recordFailure() {
    this.failures++;
    this.lastFailureTime = Date.now();
    
    if (this.failures >= this.failureThreshold) {
      this.state = 'OPEN';
      console.warn(`🔴 Circuit breaker opened after ${this.failures} failures`);
    }
  }

  private reset() {
    this.failures = 0;
    this.state = 'CLOSED';
    console.log('🟢 Circuit breaker reset to CLOSED state');
  }

  getState() {
    return {
      state: this.state,
      failures: this.failures,
      lastFailureTime: this.lastFailureTime
    };
  }
}

/**
 * Simple in-memory cache dengan TTL
 */
export class MemoryCache<T> {
  private cache = new Map<string, { value: T; expiry: number }>();

  set(key: string, value: T, ttlMs = 300000): void { // 5 minutes default
    const expiry = Date.now() + ttlMs;
    this.cache.set(key, { value, expiry });
  }

  get(key: string): T | null {
    const item = this.cache.get(key);
    
    if (!item) {
      return null;
    }
    
    if (Date.now() > item.expiry) {
      this.cache.delete(key);
      return null;
    }
    
    return item.value;
  }

  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  size(): number {
    return this.cache.size;
  }

  // Clean expired entries
  cleanup(): void {
    const now = Date.now();
    for (const [key, item] of this.cache.entries()) {
      if (now > item.expiry) {
        this.cache.delete(key);
      }
    }
  }
}

/**
 * Batch processor untuk progressive loading
 */
export class BatchProcessor<T, R> {
  constructor(
    private batchSize = 10,
    private delayMs = 100
  ) {}

  async processBatches<T, R>(
    items: T[],
    processor: (batch: T[]) => Promise<R[]>,
    onProgress?: (processed: number, total: number) => void
  ): Promise<R[]> {
    const results: R[] = [];
    
    for (let i = 0; i < items.length; i += this.batchSize) {
      const batch = items.slice(i, i + this.batchSize);
      const batchResults = await processor(batch);
      results.push(...batchResults);
      
      if (onProgress) {
        onProgress(i + batch.length, items.length);
      }
      
      // Small delay untuk prevent overwhelming the system
      if (i + this.batchSize < items.length && this.delayMs > 0) {
        await new Promise(resolve => setTimeout(resolve, this.delayMs));
      }
    }
    
    return results;
  }
}

/**
 * Query optimization helpers
 */
export class QueryOptimizer {
  /**
   * Generate cache key untuk conflict detection
   */
  static generateCacheKey(params: {
    tenantId: number;
    startTime: Date;
    endTime: Date;
    locationId?: string;
    facilityId?: string;
    staffId?: string;
    excludeScheduleId?: string;
  }): string {
    const keyParts = [
      `t:${params.tenantId}`,
      `st:${params.startTime.getTime()}`,
      `et:${params.endTime.getTime()}`,
      params.locationId ? `l:${params.locationId}` : '',
      params.facilityId ? `f:${params.facilityId}` : '',
      params.staffId ? `s:${params.staffId}` : '',
      params.excludeScheduleId ? `ex:${params.excludeScheduleId}` : ''
    ].filter(Boolean);
    
    return keyParts.join('|');
  }

  /**
   * Determine if conflict check should use fast path
   */
  static shouldUseFastPath(params: {
    startTime: Date;
    endTime: Date;
    locationId?: string;
    facilityId?: string;
    staffId?: string;
  }): boolean {
    const duration = params.endTime.getTime() - params.startTime.getTime();
    const maxDuration = 4 * 60 * 60 * 1000; // 4 hours
    
    // Use fast path for shorter durations and specific resources
    return duration <= maxDuration && (
      !!params.locationId || 
      !!params.facilityId || 
      !!params.staffId
    );
  }

  /**
   * Calculate optimal query limits based on time range
   */
  static calculateQueryLimits(startTime: Date, endTime: Date): {
    maxRecords: number;
    timeWindowHours: number;
  } {
    const durationHours = (endTime.getTime() - startTime.getTime()) / (1000 * 60 * 60);
    
    if (durationHours <= 1) {
      return { maxRecords: 50, timeWindowHours: 24 };
    } else if (durationHours <= 4) {
      return { maxRecords: 100, timeWindowHours: 48 };
    } else {
      return { maxRecords: 200, timeWindowHours: 168 }; // 1 week
    }
  }
}

// Global instances
export const conflictCache = new MemoryCache();
export const conflictCircuitBreaker = new CircuitBreaker(3, 30000, 5000);

// Cleanup cache every 10 minutes
setInterval(() => {
  conflictCache.cleanup();
}, 10 * 60 * 1000);
