import { sql, eq, and, SQL } from "drizzle-orm";
import { waiver_forms } from "@/lib/db/schema";
import { BaseService, CreateEntityData, UpdateEntityData } from "@/lib/core/base-service";
import { WaiverForm, NewWaiverForm } from "@/lib/db/schema";

// Extend base interfaces for waiver-specific data
export interface CreateWaiverFormData extends CreateEntityData {
  tenantId: number;
  name: string;
  description?: string;
  content: string;
  version?: string;
  isActive?: boolean;
  isRequired?: boolean;
  expiryDays?: number;
  sortOrder?: number;
}

export interface UpdateWaiverFormData extends UpdateEntityData {
  tenantId?: number;
  name?: string;
  description?: string;
  content?: string;
  version?: string;
  isActive?: boolean;
  isRequired?: boolean;
  expiryDays?: number;
  sortOrder?: number;
}

export interface WaiverFormStats {
  total: number;
  active: number;
  inactive: number;
  required: number;
  optional: number;
  byTenant: Record<number, {
    total: number;
    active: number;
    required: number;
  }>;
}

// Waiver Forms Service extending BaseService
export class WaiverFormService extends BaseService<WaiverForm, CreateWaiverFormData, UpdateWaiverFormData> {
  constructor() {
    super(waiver_forms, 'Waiver Form');
  }

  // Implement abstract method for filter conditions
  protected buildFilterConditions(filters: Record<string, any>): SQL[] {
    const conditions: SQL[] = [];

    if (filters.isActive !== undefined) {
      conditions.push(sql`${waiver_forms.isActive} = ${filters.isActive}`);
    }

    if (filters.isRequired !== undefined) {
      conditions.push(sql`${waiver_forms.isRequired} = ${filters.isRequired}`);
    }

    if (filters.version) {
      conditions.push(sql`${waiver_forms.version} = ${filters.version}`);
    }

    if (filters.search) {
      conditions.push(
        sql`(LOWER(${waiver_forms.name}) LIKE LOWER(${'%' + filters.search + '%'}) OR 
            LOWER(${waiver_forms.description}) LIKE LOWER(${'%' + filters.search + '%'}))`
      );
    }

    return conditions;
  }

  // Override create to handle sort order
  async create(data: CreateWaiverFormData): Promise<WaiverForm> {
    // Get the next sort order if not provided
    // 
    let sortOrder = data.sortOrder;
    if (sortOrder === undefined) {
      const [maxOrder] = await this.db
        .select({ max: sql<number>`max(${waiver_forms.sortOrder})` })
        .from(waiver_forms)
        .where(eq(waiver_forms.tenantId, data.tenantId));
      
      sortOrder = (maxOrder.max || 0) + 1;
    }

    return super.create({
      ...data,
      sortOrder,
    });
  }

  // Get active waiver forms by tenant
  async getActiveByTenantId(tenantId: number): Promise<WaiverForm[]> {
    return this.getAll({
      tenantId,
      filters: { isActive: true },
      orderField: 'sortOrder',
      orderBy: 'asc',
    });
  }

  // Get required waiver forms by tenant
  async getRequiredByTenantId(tenantId: number): Promise<WaiverForm[]> {
    return this.getAll({
      tenantId,
      filters: { isActive: true, isRequired: true },
      orderField: 'sortOrder',
      orderBy: 'asc',
    });
  }

  // Reorder waiver forms
  async reorder(tenantId: number, reorderData: { id: string; sortOrder: number }[]): Promise<WaiverForm[]> {
    return await this.db.transaction(async (tx) => {
      const updatedWaiverForms = [];
      
      for (const item of reorderData) {
        const [updated] = await tx
          .update(waiver_forms)
          .set({
            sortOrder: item.sortOrder,
            updatedAt: new Date(),
          })
          .where(
            and(
              eq(waiver_forms.id, item.id),
              eq(waiver_forms.tenantId, tenantId)
            )
          )
          .returning();
        
        if (updated) {
          updatedWaiverForms.push(updated);
        }
      }
      
      return updatedWaiverForms;
    });
  }

  // Toggle active status
  async toggleActive(id: string): Promise<WaiverForm> {
    const current = await this.getById(id);
    if (!current) {
      throw new Error("Waiver form not found");
    }

    return this.update(id, { isActive: !current.isActive });
  }

  // Toggle required status
  async toggleRequired(id: string): Promise<WaiverForm> {
    const current = await this.getById(id);
    if (!current) {
      throw new Error("Waiver form not found");
    }

    return this.update(id, { isRequired: !current.isRequired });
  }

  // Duplicate waiver form
  async duplicate(id: string, newName?: string): Promise<WaiverForm> {
    const original = await this.getById(id);
    if (!original) {
      throw new Error("Waiver form not found");
    }

    const duplicatedData: CreateWaiverFormData = {
      tenantId: original.tenantId!,
      name: newName || `${original.name} (Copy)`,
      description: original.description,
      content: original.content,
      version: "1.0", // Reset version for duplicate
      isActive: false, // Start as inactive
      isRequired: false, // Start as optional
      expiryDays: original.expiryDays,
    };

    return this.create(duplicatedData);
  }

  // Get enhanced statistics
  async getStats(tenantId?: number): Promise<WaiverFormStats> {
    const baseStats = await super.getStats(tenantId);
    
    let whereClause = sql`1=1`;
    if (tenantId) {
      whereClause = sql`${waiver_forms.tenantId} = ${tenantId}`;
    }

    const [activeCount] = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(waiver_forms)
      .where(sql`${whereClause} AND ${waiver_forms.isActive} = true`);

    const [inactiveCount] = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(waiver_forms)
      .where(sql`${whereClause} AND ${waiver_forms.isActive} = false`);

    const [requiredCount] = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(waiver_forms)
      .where(sql`${whereClause} AND ${waiver_forms.isRequired} = true`);

    const [optionalCount] = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(waiver_forms)
      .where(sql`${whereClause} AND ${waiver_forms.isRequired} = false`);

    // Get stats by tenant if not filtered by specific tenant
    const byTenant: Record<number, { total: number; active: number; required: number }> = {};
    if (!tenantId) {
      const tenantStats = await this.db
        .select({
          tenantId: waiver_forms.tenantId,
          total: sql<number>`count(*)`,
          active: sql<number>`sum(case when ${waiver_forms.isActive} then 1 else 0 end)`,
          required: sql<number>`sum(case when ${waiver_forms.isRequired} then 1 else 0 end)`,
        })
        .from(waiver_forms)
        .groupBy(waiver_forms.tenantId);

      tenantStats.forEach(stat => {
        if (stat.tenantId) {
          byTenant[stat.tenantId] = {
            total: stat.total,
            active: stat.active,
            required: stat.required,
          };
        }
      });
    }

    return {
      total: baseStats.total,
      active: activeCount.count,
      inactive: inactiveCount.count,
      required: requiredCount.count,
      optional: optionalCount.count,
      byTenant,
    };
  }

  // Bulk operations specific to waiver forms
  async bulkToggleActive(ids: string[], isActive: boolean): Promise<WaiverForm[]> {
    return this.bulkUpdate(ids, { isActive });
  }

  async bulkToggleRequired(ids: string[], isRequired: boolean): Promise<WaiverForm[]> {
    return this.bulkUpdate(ids, { isRequired });
  }

  // Search waiver forms
  async search(query: string, tenantId?: number): Promise<WaiverForm[]> {
    return super.search(query, ['name', 'description', 'content'], { tenantId });
  }

  // Validate waiver form data
  protected validateCreateData(data: CreateWaiverFormData): void {
    if (!data.name?.trim()) {
      throw new Error("Waiver form name is required");
    }
    if (!data.content?.trim()) {
      throw new Error("Waiver form content is required");
    }
    if (data.expiryDays && data.expiryDays < 1) {
      throw new Error("Expiry days must be at least 1");
    }
  }

  protected validateUpdateData(data: UpdateWaiverFormData): void {
    if (data.name !== undefined && !data.name?.trim()) {
      throw new Error("Waiver form name cannot be empty");
    }
    if (data.content !== undefined && !data.content?.trim()) {
      throw new Error("Waiver form content cannot be empty");
    }
    if (data.expiryDays !== undefined && data.expiryDays < 1) {
      throw new Error("Expiry days must be at least 1");
    }
  }
}
