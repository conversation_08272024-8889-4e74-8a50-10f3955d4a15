import { useMutation, useQuery, useQueryClient, UseQueryOptions, UseMutationOptions } from '@tanstack/react-query';
import { BaseEntity, CreateEntityData, UpdateEntityData, QueryOptions, BulkOperationData } from './base-service';

// Generic query key factory
export function createQueryKeys(entityName: string) {
  return {
    all: [entityName] as const,
    lists: () => [...createQueryKeys(entityName).all, 'list'] as const,
    list: (filters: Record<string, any>) => [...createQueryKeys(entityName).lists(), { filters }] as const,
    details: () => [...createQueryKeys(entityName).all, 'detail'] as const,
    detail: (id: string) => [...createQueryKeys(entityName).details(), id] as const,
    byTenant: (tenantId: number) => [...createQueryKeys(entityName).all, 'tenant', tenantId] as const,
    stats: (tenantId?: number) => [...createQueryKeys(entityName).all, 'stats', tenantId] as const,
    search: (query: string) => [...createQueryKeys(entityName).all, 'search', query] as const,
  };
}

// Generic API interface
export interface BaseApiInterface<T extends BaseEntity, C extends CreateEntityData, U extends UpdateEntityData> {
  getAll: (options?: QueryOptions) => Promise<T[]>;
  getById: (id: string) => Promise<T>;
  getByTenant: (tenantId: number, options?: QueryOptions) => Promise<T[]>;
  create: (data: C) => Promise<T>;
  update: (params: { id: string; data: U }) => Promise<T>;
  delete: (id: string) => Promise<T>;
  bulkOperation: (data: BulkOperationData) => Promise<T[]>;
  search: (query: string, options?: QueryOptions) => Promise<T[]>;
  getStats: (tenantId?: number) => Promise<any>;
}

// Generic hook factory
export function createEntityHooks<T extends BaseEntity, C extends CreateEntityData, U extends UpdateEntityData>(
  entityName: string,
  api: BaseApiInterface<T, C, U>
) {
  const queryKeys = createQueryKeys(entityName);

  // Query hooks
  const useEntities = (options?: QueryOptions & { queryOptions?: Omit<UseQueryOptions<T[]>, 'queryKey' | 'queryFn'> }) => {
    const { queryOptions, ...apiOptions } = options || {};
    
    return useQuery({
      queryKey: queryKeys.list(apiOptions || {}),
      queryFn: () => api.getAll(apiOptions),
      staleTime: 5 * 60 * 1000, // 5 minutes
      retry: false,
      ...queryOptions,
    });
  };

  const useEntity = (id: string, options?: Omit<UseQueryOptions<T>, 'queryKey' | 'queryFn'>) => {
    return useQuery({
      queryKey: queryKeys.detail(id),
      queryFn: () => api.getById(id),
      enabled: !!id,
      staleTime: 5 * 60 * 1000,
      ...options,
    });
  };

  const useEntitiesByTenant = (tenantId: number, options?: QueryOptions & { queryOptions?: Omit<UseQueryOptions<T[]>, 'queryKey' | 'queryFn'> }) => {
    const { queryOptions, ...apiOptions } = options || {};

    return useQuery({
      queryKey: queryKeys.list({ tenantId, ...apiOptions }),
      queryFn: () => api.getByTenant(tenantId, apiOptions),
      enabled: !!tenantId,
      staleTime: 5 * 60 * 1000,
      ...queryOptions,
    });
  };

  const useEntityStats = (tenantId?: number, options?: Omit<UseQueryOptions<any>, 'queryKey' | 'queryFn'>) => {
    return useQuery({
      queryKey: queryKeys.stats(tenantId),
      queryFn: () => api.getStats(tenantId),
      staleTime: 2 * 60 * 1000, // 2 minutes
      ...options,
    });
  };

  const useEntitySearch = (query: string, options?: QueryOptions & { queryOptions?: Omit<UseQueryOptions<T[]>, 'queryKey' | 'queryFn'> }) => {
    const { queryOptions, ...apiOptions } = options || {};
    
    return useQuery({
      queryKey: queryKeys.search(query),
      queryFn: () => api.search(query, apiOptions),
      enabled: !!query && query.length > 2,
      staleTime: 1 * 60 * 1000, // 1 minute
      ...queryOptions,
    });
  };

  // Mutation hooks
  const useCreateEntity = (options?: UseMutationOptions<T, Error, C>) => {
    const queryClient = useQueryClient();

    return useMutation({
      mutationFn: api.create,
      onSuccess: (newEntity) => {
        // Update cache with new entity
        queryClient.setQueryData(queryKeys.detail(newEntity.id), newEntity);

        // Invalidate related queries
        queryClient.invalidateQueries({ queryKey: queryKeys.lists() });
        if (newEntity.tenantId) {
          queryClient.invalidateQueries({
            queryKey: queryKeys.byTenant(newEntity.tenantId)
          });
          queryClient.invalidateQueries({
            queryKey: queryKeys.stats(newEntity.tenantId)
          });
        }
        queryClient.invalidateQueries({ queryKey: queryKeys.stats() });
      },
      ...options,
    });
  };

  const useUpdateEntity = (options?: UseMutationOptions<T, Error, { id: string; data: U }>) => {
    const queryClient = useQueryClient();

    return useMutation({
      mutationFn: api.update,
      onSuccess: (updatedEntity) => {
        // Update cache with updated entity
        queryClient.setQueryData(queryKeys.detail(updatedEntity.id), updatedEntity);

        // Invalidate related queries
        queryClient.invalidateQueries({ queryKey: queryKeys.lists() });
        if (updatedEntity.tenantId) {
          queryClient.invalidateQueries({
            queryKey: queryKeys.byTenant(updatedEntity.tenantId)
          });
          queryClient.invalidateQueries({
            queryKey: queryKeys.stats(updatedEntity.tenantId)
          });
        }
        queryClient.invalidateQueries({ queryKey: queryKeys.stats() });
      },
      ...options,
    });
  };

  const useDeleteEntity = (options?: UseMutationOptions<T, Error, string>) => {
    const queryClient = useQueryClient();

    return useMutation({
      mutationFn: api.delete,
      onMutate: async (entityId: string) => {
        // Cancel any outgoing refetches
        await queryClient.cancelQueries({ queryKey: queryKeys.all });

        // Get the entity data before deletion for rollback
        const previousEntity = queryClient.getQueryData(queryKeys.detail(entityId)) as T;

        // Get all entities to update the list optimistically
        const previousEntities = queryClient.getQueryData(queryKeys.lists()) as T[] | undefined;

        // Optimistically remove from all entities list
        if (previousEntities) {
          const updatedEntities = previousEntities.filter(entity => entity.id !== entityId);
          queryClient.setQueryData(queryKeys.lists(), updatedEntities);
        }

        // Remove from detail cache
        queryClient.removeQueries({ queryKey: queryKeys.detail(entityId) });

        return { previousEntity, previousEntities };
      },
      onError: (error, entityId, context) => {
        // Restore the previous state if deletion failed
        if (context?.previousEntities) {
          queryClient.setQueryData(queryKeys.lists(), context.previousEntities);
        }
        if (context?.previousEntity) {
          queryClient.setQueryData(queryKeys.detail(entityId), context.previousEntity);
        }
      },
      onSuccess: (deletedEntity) => {
        // Invalidate and refetch related queries to ensure consistency
        queryClient.invalidateQueries({ queryKey: queryKeys.lists() });

        if (deletedEntity.tenantId) {
          queryClient.invalidateQueries({
            queryKey: queryKeys.byTenant(deletedEntity.tenantId)
          });
          queryClient.invalidateQueries({
            queryKey: queryKeys.stats(deletedEntity.tenantId)
          });
        }
        queryClient.invalidateQueries({ queryKey: queryKeys.stats() });
      },
      ...options,
    });
  };

  const useBulkOperation = (options?: UseMutationOptions<T[], Error, BulkOperationData>) => {
    const queryClient = useQueryClient();

    return useMutation({
      mutationFn: api.bulkOperation,
      onSuccess: () => {
        // Invalidate all related queries
        queryClient.invalidateQueries({ queryKey: queryKeys.all });
      },
      ...options,
    });
  };

  return {
    // Query hooks
    useEntities,
    useEntity,
    useEntitiesByTenant,
    useEntityStats,
    useEntitySearch,
    
    // Mutation hooks
    useCreateEntity,
    useUpdateEntity,
    useDeleteEntity,
    useBulkOperation,
    
    // Query keys for external use
    queryKeys,
  };
}
