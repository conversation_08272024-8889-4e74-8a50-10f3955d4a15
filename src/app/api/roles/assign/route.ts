import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { RoleService } from "@/lib/services/role.service";
import { withRBAC } from "@/lib/middleware/rbac-middleware";

/**
 * API Routes untuk Role Assignment
 */

const assignRoleSchema = z.object({
  userId: z.string().min(1, "User ID is required"),
  roleId: z.string().min(1, "Role ID is required"),
  tenantId: z.number().int().positive("Tenant ID must be positive").optional().nullable(),
  assignedBy: z.string().optional(),
  expiresAt: z.string().optional().transform((val) => {
    if (!val) return undefined;
    return new Date(val);
  }),
});

/**
 * POST /api/roles/assign
 * Assign role to user
 */
export const POST = withRBAC(
  async (request: NextRequest) => {
    try {
      const body = await request.json();
      
      // Validate request body
      const validatedData = assignRoleSchema.parse(body);

      // Call service untuk assign role
      const result = await RoleService.assignRole(validatedData);

      return NextResponse.json({
        success: true,
        data: result,
        message: "Role assigned successfully",
      }, { status: 201 });
    } catch (error) {
      console.error("POST /api/roles/assign error:", error);
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { success: false, error: "Validation failed", details: error.errors },
          { status: 400 }
        );
      }

      return NextResponse.json(
        { success: false, error: "Internal server error" },
        { status: 500 }
      );
    }
  },
  { module: "roles", action: "assign" }
);
