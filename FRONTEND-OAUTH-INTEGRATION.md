# 🚀 **Frontend Integration Guide - Customer OAuth**

## 📋 **Overview**

Frontend (port 3002) perlu diupdate untuk menggunakan Customer OAuth system yang sudah diperbaiki di backend (port 3000).

## 🔧 **Backend Endpoints (Port 3000)**

### **✅ Correct Endpoints:**
```
POST http://localhost:3000/api/auth/customer/google/init
POST http://localhost:3000/api/auth/customer/google/callback  
POST http://localhost:3000/api/auth/customer/register
GET  http://localhost:3000/api/auth/customer/profile
POST http://localhost:3000/api/auth/customer/refresh
POST http://localhost:3000/api/auth/customer/logout
```

### **❌ Wrong Endpoints (Akan di-block oleh middleware):**
```
POST http://localhost:3000/api/auth/google (NextAuth - wrong)
POST http://localhost:3000/api/customer/auth/google (tidak ada)
```

## 🛠️ **Frontend Implementation**

### **1. Environment Variables (.env.local)**
```bash
# Backend API URL
NEXT_PUBLIC_API_URL=http://localhost:3000

# Google OAuth (untuk frontend reference)
NEXT_PUBLIC_GOOGLE_CLIENT_ID=your_google_client_id
```

### **2. Customer OAuth Hook**
```typescript
// hooks/useCustomerOAuth.ts
import { useState } from 'react';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';

export function useCustomerOAuth() {
  const [isLoading, setIsLoading] = useState(false);

  const initiateGoogleOAuth = async (tenantId: number) => {
    setIsLoading(true);
    try {
      const response = await fetch(`${API_URL}/api/auth/customer/google/init`, {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          // ❌ JANGAN kirim API key headers ke OAuth endpoints
        },
        body: JSON.stringify({
          tenantId,
          clientType: 'web', // ✅ Gunakan clientType (bukan deviceType)
          deviceId: `web-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          redirectUri: `${window.location.origin}/auth/callback`
        })
      });

      if (!response.ok) {
        throw new Error(`OAuth init failed: ${response.status}`);
      }

      const data = await response.json();
      
      if (data.success && data.authUrl) {
        // Store state untuk validation
        sessionStorage.setItem('oauth_state', data.state);
        sessionStorage.setItem('oauth_tenant_id', tenantId.toString());
        
        // Redirect ke Google OAuth
        window.location.href = data.authUrl;
      } else {
        throw new Error('Invalid OAuth response');
      }
    } catch (error) {
      console.error('OAuth error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  return { initiateGoogleOAuth, isLoading };
}
```

### **3. Customer Registration Hook**
```typescript
// hooks/useCustomerAuth.ts
const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';

export function useCustomerAuth() {
  const [isLoading, setIsLoading] = useState(false);

  const registerCustomer = async (data: {
    firstName: string;
    lastName: string;
    email: string;
    password: string;
    tenantId: number;
  }) => {
    setIsLoading(true);
    try {
      const response = await fetch(`${API_URL}/api/auth/customer/register`, {
        method: 'POST',
        headers: { 
          'Content-Type': 'application/json',
          // ❌ JANGAN kirim API key headers ke auth endpoints
        },
        body: JSON.stringify(data)
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Registration failed');
      }

      return await response.json();
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  return { registerCustomer, isLoading };
}
```

### **4. OAuth Callback Handler**
```typescript
// pages/auth/callback.tsx atau app/auth/callback/page.tsx
'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3000';

export default function OAuthCallback() {
  const [message, setMessage] = useState('Processing OAuth callback...');
  const router = useRouter();

  useEffect(() => {
    const handleCallback = async () => {
      try {
        const urlParams = new URLSearchParams(window.location.search);
        const code = urlParams.get('code');
        const state = urlParams.get('state');
        const error = urlParams.get('error');

        if (error) {
          throw new Error(`OAuth error: ${error}`);
        }

        if (!code || !state) {
          throw new Error('Missing OAuth parameters');
        }

        // Validate state
        const storedState = sessionStorage.getItem('oauth_state');
        const storedTenantId = sessionStorage.getItem('oauth_tenant_id');

        if (state !== storedState) {
          throw new Error('Invalid OAuth state');
        }

        setMessage('Exchanging authorization code for tokens...');

        // Exchange code for tokens
        const response = await fetch(`${API_URL}/api/auth/customer/google/callback`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            // ❌ JANGAN kirim API key headers
          },
          body: JSON.stringify({
            code,
            state,
            tenantId: parseInt(storedTenantId || '1'),
            clientType: 'web', // ✅ Gunakan clientType
            deviceId: `web-callback-${Date.now()}`
          })
        });

        if (!response.ok) {
          throw new Error(`Callback failed: ${response.status}`);
        }

        const result = await response.json();

        if (result.success && result.tokens) {
          // Store tokens
          localStorage.setItem('customer_access_token', result.tokens.accessToken);
          localStorage.setItem('customer_refresh_token', result.tokens.refreshToken);
          localStorage.setItem('customer_data', JSON.stringify(result.customer));

          // Clean up session storage
          sessionStorage.removeItem('oauth_state');
          sessionStorage.removeItem('oauth_tenant_id');

          setMessage('Login successful! Redirecting...');
          
          // Redirect to dashboard
          setTimeout(() => {
            router.push('/dashboard');
          }, 1000);
        } else {
          throw new Error('Invalid callback response');
        }

      } catch (error) {
        console.error('OAuth callback error:', error);
        setMessage(`Error: ${error.message}`);
        
        // Redirect to login with error
        setTimeout(() => {
          router.push(`/auth/signin?error=${encodeURIComponent(error.message)}`);
        }, 3000);
      }
    };

    handleCallback();
  }, [router]);

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p>{message}</p>
      </div>
    </div>
  );
}
```

### **5. Customer Signup Component**
```typescript
// components/CustomerSignupForm.tsx
'use client';

import { useState } from 'react';
import { useCustomerAuth, useCustomerOAuth } from '@/hooks/useCustomerAuth';

export function CustomerSignupForm({ tenantId = 1 }) {
  const { registerCustomer, isLoading: isRegistering } = useCustomerAuth();
  const { initiateGoogleOAuth, isLoading: isOAuthLoading } = useCustomerOAuth();
  
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    confirmPassword: ''
  });

  const handleGoogleSignup = async () => {
    try {
      await initiateGoogleOAuth(tenantId);
    } catch (error) {
      alert(`Google signup failed: ${error.message}`);
    }
  };

  const handleEmailSignup = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (formData.password !== formData.confirmPassword) {
      alert('Passwords do not match');
      return;
    }

    try {
      await registerCustomer({
        firstName: formData.firstName,
        lastName: formData.lastName,
        email: formData.email,
        password: formData.password,
        tenantId
      });
      
      alert('Registration successful! Please check your email.');
    } catch (error) {
      alert(`Registration failed: ${error.message}`);
    }
  };

  return (
    <div className="max-w-md mx-auto p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-6">Create Account</h2>
      
      {/* Google OAuth Button */}
      <button
        onClick={handleGoogleSignup}
        disabled={isOAuthLoading}
        className="w-full mb-4 px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50"
      >
        {isOAuthLoading ? 'Loading...' : 'Continue with Google'}
      </button>

      <div className="text-center mb-4 text-gray-500">or</div>

      {/* Email/Password Form */}
      <form onSubmit={handleEmailSignup} className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <input
            type="text"
            placeholder="First Name"
            value={formData.firstName}
            onChange={(e) => setFormData({...formData, firstName: e.target.value})}
            className="px-3 py-2 border border-gray-300 rounded-md"
            required
          />
          <input
            type="text"
            placeholder="Last Name"
            value={formData.lastName}
            onChange={(e) => setFormData({...formData, lastName: e.target.value})}
            className="px-3 py-2 border border-gray-300 rounded-md"
            required
          />
        </div>
        
        <input
          type="email"
          placeholder="Email"
          value={formData.email}
          onChange={(e) => setFormData({...formData, email: e.target.value})}
          className="w-full px-3 py-2 border border-gray-300 rounded-md"
          required
        />
        
        <input
          type="password"
          placeholder="Password"
          value={formData.password}
          onChange={(e) => setFormData({...formData, password: e.target.value})}
          className="w-full px-3 py-2 border border-gray-300 rounded-md"
          required
        />
        
        <input
          type="password"
          placeholder="Confirm Password"
          value={formData.confirmPassword}
          onChange={(e) => setFormData({...formData, confirmPassword: e.target.value})}
          className="w-full px-3 py-2 border border-gray-300 rounded-md"
          required
        />
        
        <button
          type="submit"
          disabled={isRegistering}
          className="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
        >
          {isRegistering ? 'Creating Account...' : 'Create Account'}
        </button>
      </form>
    </div>
  );
}
```

## ⚠️ **Important Notes**

### **❌ JANGAN Lakukan:**
1. **Jangan kirim API key headers** ke OAuth endpoints
2. **Jangan gunakan** `/api/auth/google` (NextAuth endpoint)
3. **Jangan gunakan** `deviceType` field (gunakan `clientType`)

### **✅ HARUS Lakukan:**
1. **Gunakan endpoint yang benar**: `/api/auth/customer/google/init`
2. **Gunakan `clientType`** field dalam request body
3. **Validate OAuth state** di callback handler
4. **Store JWT tokens** di localStorage/sessionStorage
5. **Handle errors** dengan proper user feedback

## 🧪 **Testing**

1. **Test Google OAuth flow**:
   - Klik "Continue with Google"
   - Verify redirect ke Google OAuth
   - Check callback handling
   - Verify tokens tersimpan

2. **Test email registration**:
   - Fill form dengan data valid
   - Submit form
   - Check success/error handling

3. **Test error cases**:
   - Invalid data
   - Network errors
   - OAuth errors

## 🎯 **Ready to Implement!**

Dokumentasi ini memberikan semua yang dibutuhkan untuk mengintegrasikan frontend (port 3002) dengan Customer OAuth system di backend (port 3000). 🚀
