openapi: 3.0.3
info:
  title: Package Pricing Public API
  description: |
    Enterprise-grade public API for package pricing management.
    
    ## Authentication
    
    This API uses API key authentication. Include your API key in the request header:
    
    ```
    Authorization: Bearer YOUR_API_KEY
    ```
    
    Or use the X-API-Key header:
    
    ```
    X-API-Key: YOUR_API_KEY
    ```
    
    ## Rate Limiting
    
    API requests are rate limited per API key:
    - 100 requests per minute
    - 1000 requests per hour  
    - 10000 requests per day
    - Burst limit: 50 requests
    
    Rate limit headers are included in responses:
    - `X-RateLimit-Limit`: Request limit for the time window
    - `X-RateLimit-Remaining`: Remaining requests in current window
    - `X-RateLimit-Reset`: Time when the rate limit resets
    
    ## Error Handling
    
    The API uses conventional HTTP response codes and returns detailed error information:
    
    - `200` - Success
    - `400` - Bad Request (validation errors)
    - `401` - Unauthorized (invalid API key)
    - `403` - Forbidden (insufficient permissions)
    - `404` - Not Found
    - `429` - Too Many Requests (rate limit exceeded)
    - `500` - Internal Server Error
    
    ## Pagination
    
    List endpoints support pagination using query parameters:
    - `page`: Page number (default: 1)
    - `limit`: Items per page (default: 20, max: 100)
    
    ## Filtering and Sorting
    
    List endpoints support filtering and sorting:
    - `search`: Search across multiple fields
    - `sort`: Field to sort by
    - `order`: Sort order (`asc` or `desc`)
    
  version: 1.0.0
  contact:
    name: API Support
    email: <EMAIL>
    url: https://docs.example.com
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT
  termsOfService: https://example.com/terms

servers:
  - url: https://api.example.com/public/v1
    description: Production server
  - url: https://staging-api.example.com/public/v1
    description: Staging server
  - url: http://localhost:3000/api/public/v1
    description: Development server

security:
  - ApiKeyAuth: []
  - BearerAuth: []

paths:
  /package-pricing:
    get:
      summary: List package pricing
      description: |
        Retrieve a paginated list of package pricing records with optional filtering.
        
        ## Filtering Options
        
        - `packageId`: Filter by specific package ID
        - `pricingGroupId`: Filter by specific pricing group ID
        - `minPrice`: Minimum price filter
        - `maxPrice`: Maximum price filter
        - `currency`: Filter by currency code (ISO 4217)
        - `isActive`: Filter by active status
        - `search`: Search across package names and descriptions
        
        ## Sorting Options
        
        - `createdAt`: Sort by creation date
        - `updatedAt`: Sort by last update date
        - `price`: Sort by price
        - `packageName`: Sort by package name
        
      operationId: listPackagePricing
      tags:
        - Package Pricing
      parameters:
        - $ref: '#/components/parameters/PageParam'
        - $ref: '#/components/parameters/LimitParam'
        - $ref: '#/components/parameters/SortParam'
        - $ref: '#/components/parameters/OrderParam'
        - name: packageId
          in: query
          description: Filter by package ID
          schema:
            type: string
        - name: pricingGroupId
          in: query
          description: Filter by pricing group ID
          schema:
            type: string
        - name: minPrice
          in: query
          description: Minimum price filter
          schema:
            type: number
            minimum: 0
        - name: maxPrice
          in: query
          description: Maximum price filter
          schema:
            type: number
            minimum: 0
        - name: currency
          in: query
          description: Currency code filter (ISO 4217)
          schema:
            type: string
            pattern: '^[A-Z]{3}$'
            example: USD
        - name: isActive
          in: query
          description: Filter by active status
          schema:
            type: boolean
        - name: search
          in: query
          description: Search across package names and descriptions
          schema:
            type: string
            maxLength: 255
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      data:
                        type: array
                        items:
                          $ref: '#/components/schemas/PackagePricing'
                      pagination:
                        $ref: '#/components/schemas/PaginationMeta'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '429':
          $ref: '#/components/responses/TooManyRequests'
        '500':
          $ref: '#/components/responses/InternalServerError'

    post:
      summary: Create package pricing
      description: |
        Create a new package pricing record or multiple records in bulk.
        
        ## Single Creation
        
        Send a single package pricing object in the request body.
        
        ## Bulk Creation
        
        Send an object with an `items` array containing multiple package pricing objects.
        Maximum 100 items per bulk request.
        
        ## Validation Rules
        
        - Either `price` or `creditAmount` must be provided
        - `packageId` and `pricingGroupId` must exist and belong to your tenant
        - Combination of `packageId` and `pricingGroupId` must be unique
        - `currency` must be a valid ISO 4217 currency code
        
      operationId: createPackagePricing
      tags:
        - Package Pricing
      requestBody:
        required: true
        content:
          application/json:
            schema:
              oneOf:
                - $ref: '#/components/schemas/PackagePricingCreateRequest'
                - $ref: '#/components/schemas/PackagePricingBulkCreateRequest'
            examples:
              single:
                summary: Single package pricing
                value:
                  packageId: "pkg_123"
                  pricingGroupId: "pg_456"
                  price: 99.99
                  currency: "USD"
              bulk:
                summary: Bulk package pricing
                value:
                  items:
                    - packageId: "pkg_123"
                      pricingGroupId: "pg_456"
                      price: 99.99
                      currency: "USD"
                    - packageId: "pkg_124"
                      pricingGroupId: "pg_456"
                      creditAmount: 10
                      currency: "USD"
      responses:
        '201':
          description: Package pricing created successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      data:
                        oneOf:
                          - $ref: '#/components/schemas/PackagePricing'
                          - type: object
                            properties:
                              items:
                                type: array
                                items:
                                  $ref: '#/components/schemas/PackagePricing'
                              created:
                                type: integer
                                description: Number of items created
                              message:
                                type: string
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '409':
          description: Conflict - package pricing already exists
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '429':
          $ref: '#/components/responses/TooManyRequests'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /package-pricing/{id}:
    get:
      summary: Get package pricing by ID
      description: Retrieve a specific package pricing record by its ID.
      operationId: getPackagePricing
      tags:
        - Package Pricing
      parameters:
        - $ref: '#/components/parameters/IdParam'
      responses:
        '200':
          description: Successful response
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/PackagePricing'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '429':
          $ref: '#/components/responses/TooManyRequests'
        '500':
          $ref: '#/components/responses/InternalServerError'

    put:
      summary: Update package pricing
      description: |
        Update an existing package pricing record.
        
        ## Validation Rules
        
        - At least one field must be provided for update
        - If provided, `price` must be >= 0
        - If provided, `creditAmount` must be >= 0
        - If provided, `currency` must be a valid ISO 4217 currency code
        
      operationId: updatePackagePricing
      tags:
        - Package Pricing
      parameters:
        - $ref: '#/components/parameters/IdParam'
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PackagePricingUpdateRequest'
            examples:
              price_update:
                summary: Update price
                value:
                  price: 149.99
              credit_update:
                summary: Update credit amount
                value:
                  creditAmount: 15
              currency_update:
                summary: Update currency
                value:
                  currency: "EUR"
      responses:
        '200':
          description: Package pricing updated successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/PackagePricing'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '429':
          $ref: '#/components/responses/TooManyRequests'
        '500':
          $ref: '#/components/responses/InternalServerError'

    delete:
      summary: Delete package pricing
      description: Delete a specific package pricing record by its ID.
      operationId: deletePackagePricing
      tags:
        - Package Pricing
      parameters:
        - $ref: '#/components/parameters/IdParam'
      responses:
        '200':
          description: Package pricing deleted successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/SuccessResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          id:
                            type: string
                            description: ID of the deleted package pricing
                          message:
                            type: string
                            description: Confirmation message
                          deletedAt:
                            type: string
                            format: date-time
                            description: Deletion timestamp
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '429':
          $ref: '#/components/responses/TooManyRequests'
        '500':
          $ref: '#/components/responses/InternalServerError'

  /health:
    get:
      summary: Health check
      description: Check the health status of the API and its dependencies.
      operationId: healthCheck
      tags:
        - System
      security: []  # No authentication required
      responses:
        '200':
          description: System is healthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthCheck'
        '503':
          description: System is unhealthy
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthCheck'

components:
  securitySchemes:
    ApiKeyAuth:
      type: apiKey
      in: header
      name: X-API-Key
      description: API key for authentication
    BearerAuth:
      type: http
      scheme: bearer
      description: Bearer token authentication

  parameters:
    IdParam:
      name: id
      in: path
      required: true
      description: Resource ID
      schema:
        type: string
        pattern: '^[a-zA-Z0-9_-]+$'
        example: "pkg_pricing_123"

    PageParam:
      name: page
      in: query
      description: Page number for pagination
      schema:
        type: integer
        minimum: 1
        default: 1
        example: 1

    LimitParam:
      name: limit
      in: query
      description: Number of items per page
      schema:
        type: integer
        minimum: 1
        maximum: 100
        default: 20
        example: 20

    SortParam:
      name: sort
      in: query
      description: Field to sort by
      schema:
        type: string
        enum: [createdAt, updatedAt, price, packageName]
        example: createdAt

    OrderParam:
      name: order
      in: query
      description: Sort order
      schema:
        type: string
        enum: [asc, desc]
        default: asc
        example: desc

  schemas:
    PackagePricing:
      type: object
      description: Package pricing information
      required:
        - id
        - packageId
        - packageName
        - pricingGroupId
        - pricingGroupName
        - currency
        - isActive
        - createdAt
        - updatedAt
      properties:
        id:
          type: string
          description: Unique identifier for the package pricing
          example: "pkg_pricing_123"
        packageId:
          type: string
          description: ID of the associated package
          example: "pkg_123"
        packageName:
          type: string
          description: Name of the associated package
          example: "Premium Fitness Package"
        packageDescription:
          type: string
          nullable: true
          description: Description of the associated package
          example: "Comprehensive fitness package with unlimited classes"
        pricingGroupId:
          type: string
          description: ID of the associated pricing group
          example: "pg_456"
        pricingGroupName:
          type: string
          description: Name of the associated pricing group
          example: "Standard Pricing"
        price:
          type: number
          nullable: true
          minimum: 0
          description: Monetary price for the package
          example: 99.99
        creditAmount:
          type: integer
          nullable: true
          minimum: 0
          description: Credit amount for the package
          example: 10
        currency:
          type: string
          pattern: '^[A-Z]{3}$'
          description: Currency code (ISO 4217)
          example: "USD"
        isActive:
          type: boolean
          description: Whether the package pricing is active
          example: true
        createdAt:
          type: string
          format: date-time
          description: Creation timestamp
          example: "2024-01-15T10:30:00Z"
        updatedAt:
          type: string
          format: date-time
          description: Last update timestamp
          example: "2024-01-15T10:30:00Z"
