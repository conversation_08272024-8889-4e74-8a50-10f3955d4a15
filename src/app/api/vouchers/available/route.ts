import { NextRequest, NextResponse } from "next/server";
import { VoucherService } from "@/lib/services/voucher.service";
import { withRBAC } from "@/lib/middleware/rbac-middleware";

// GET /api/vouchers/available - Get available vouchers for a customer
export async function GET(request: NextRequest) {
  return withRBAC(
    async () => {
      try {
        const { searchParams } = new URL(request.url);
        
        const customerId = searchParams.get("customerId");
        const tenantId = searchParams.get("tenantId");

        if (!customerId) {
          return NextResponse.json(
            { error: "Customer ID is required" },
            { status: 400 }
          );
        }

        if (!tenantId) {
          return NextResponse.json(
            { error: "Tenant ID is required" },
            { status: 400 }
          );
        }

        // Get available vouchers for customer
        const vouchers = await VoucherService.getAvailableVouchersForCustomer(
          customerId,
          parseInt(tenantId)
        );

        return NextResponse.json({
          success: true,
          data: vouchers,
          count: vouchers.length,
        });
      } catch (error) {
        console.error("Error fetching available vouchers:", error);
        return NextResponse.json(
          { 
            error: error instanceof Error ? error.message : "Failed to fetch available vouchers",
            success: false 
          },
          { status: 500 }
        );
      }
    },
    { module: "vouchers", action: "read" }
  )(request);
}
