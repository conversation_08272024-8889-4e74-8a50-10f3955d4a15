import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { tenantContextService } from "./lib/services/tenant-context.service";
import { auth } from "./lib/auth";

// Define protected routes
const protectedRoutes = [
  "/dashboard",
  "/admin",
  "/api/admin",
  "/api/user",
  "/settings",
  "/billing",
  "/analytics",
  "/api-keys",
  "/docs",
  "/business",
  "/business-tanstack",
  "/rbac-test",
  "/role-management",
];

// Define admin-only routes (for future use)
// const adminRoutes = [
//   "/admin",
//   "/api/admin",
// ];

// Define auth routes (redirect if already authenticated)
const authRoutes = [
  "/auth/signin",
  "/auth/signup",
  "/auth/error",
  "/auth/verify-request",
  "/auth/forgot-password",
];

const customerRouter = [
  "/dashboard",
  "/profile",
  "/bookings",
  "/schedule",
  "/membership",
  "/settings",
]

const adminRoutes = [
  "/admin",
  "/staff",
  "/management"
]

// Define public routes (always accessible)
const publicRoutes = [
  "/",
  "/about",
  "/pricing",
  "/contact",
  "/blog",
  "/api/auth",
  "/api/webhooks",
  "/api/test-roles",
  "/api/test-role-creation",
  "/api/test-role-flow",
  "/api/test-role-delete",
  "/api/test-role-update",
  "/api/test-role-bug",
  "/api/test-role-direct-query",
  "/api/test-read-role",
  "/api/test-crud-complete",
  "/api/test-voucher-create",
  "/api/test-voucher-frontend",
  "/api/roles-no-auth",
  "/api/public/blog-posts",
  "/api/public/blog-categories",
  "/api/public/class-schedules",
  "/api/public/v1",
  "/api/test-public-class-schedules",
  "/api/roles-bypass",
  "/api/debug-auth",
];

// Rate limiting configuration
interface RateLimitConfig {
  requests: number;
  window: number; // in seconds
}

const rateLimitConfig: Record<string, RateLimitConfig> = {
  "/api/auth/signin": { requests: 5, window: 900 }, // 5 attempts per 15 minutes
  "/api/auth/signup": { requests: 3, window: 3600 }, // 3 attempts per hour
  "/auth/signin": { requests: 10, window: 900 },
  "/auth/signup": { requests: 5, window: 3600 },
  default: { requests: 100, window: 60 }, // Default rate limit
};


// In-memory rate limiting (use Redis in production)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

function checkRateLimit(key: string, config: RateLimitConfig): { success: boolean; remaining: number; resetTime: number } {
  const now = Date.now();
  const record = rateLimitStore.get(key);

  if (!record || now > record.resetTime) {
    const resetTime = now + config.window * 1000;
    rateLimitStore.set(key, { count: 1, resetTime });
    return { success: true, remaining: config.requests - 1, resetTime };
  }

  if (record.count >= config.requests) {
    return { success: false, remaining: 0, resetTime: record.resetTime };
  }

  record.count++;
  return { success: true, remaining: config.requests - record.count, resetTime: record.resetTime };
}


export async function middleware(req: NextRequest) {
  const { nextUrl } = req;
  const pathname = nextUrl.pathname;


  console.log("🔍 Middleware processing:", {
    pathname,
    host: req.headers.get('host'),
    userAgent: req.headers.get('user-agent')?.substring(0, 50),
  });

  console.log("🔍 Middleware - Processing:", nextUrl.pathname);

  // Handle Customer OAuth routes - prevent NextAuth from intercepting
  if (pathname.startsWith('/api/auth/customer/')) {
    console.log(`🔄 [Middleware] Customer OAuth route: ${pathname}`);

    // Check if request has API key (which is wrong for OAuth)
    const authHeader = req.headers.get('Authorization');
    const apiKeyHeader = req.headers.get('X-API-Key');

    if (authHeader?.startsWith('Bearer pk_') || apiKeyHeader?.startsWith('pk_')) {
      console.log('❌ [Middleware] API key detected on OAuth endpoint - blocking request');
      return new NextResponse(
        JSON.stringify({
          success: false,
          error: 'API keys are not allowed on OAuth endpoints. Use proper OAuth flow instead.',
          errorCode: 'INVALID_AUTH_METHOD',
          hint: 'Remove Authorization/X-API-Key headers and use OAuth flow'
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }

    // Allow Customer OAuth routes to proceed without NextAuth interference
    return NextResponse.next();
  }

  // Handle wrong OAuth endpoint calls
  if (pathname === '/api/auth/google' || pathname === '/api/customer/auth/google') {
    console.log(`❌ [Middleware] Wrong OAuth endpoint called: ${pathname}`);

    return new NextResponse(
      JSON.stringify({
        success: false,
        error: 'Wrong OAuth endpoint',
        errorCode: 'WRONG_ENDPOINT',
        correctEndpoint: '/api/auth/customer/google/init',
        hint: 'Use /api/auth/customer/google/init for Customer OAuth initialization'
      }),
      {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }

  // Skip middleware for API routes, static files, and NextAuth
  if (
    nextUrl.pathname.startsWith('/api/auth') ||
    nextUrl.pathname.startsWith('/api/public') ||
    nextUrl.pathname.startsWith('/_next') ||
    nextUrl.pathname.includes('.') ||
    nextUrl.pathname.startsWith('/favicon') ||
    pathname === '/robots.txt' ||
    pathname === '/sitemap.xml'
  ) {
    return NextResponse.next();
  }


  // Get tenant context
  const host = req.headers.get('host');
  if (!host) {
    console.warn('🚨 No host header found');
    return new NextResponse('Bad Request', { status: 400 });
  }

  const tenantContext = await tenantContextService.getTenantByDomain(host);


   if (!tenantContext) {
    console.warn(`🚨 No tenant found for domain: ${host}`);
    
    // Check if this is a subdomain that doesn't exist
    if (host.includes('.') && !host.startsWith('localhost')) {
      return new NextResponse(`
        <html>
          <body style="font-family: system-ui; text-align: center; padding: 50px;">
            <h1>Tenant Not Found</h1>
            <p>The subdomain "${host}" does not exist or is not active.</p>
            <p>Please check the URL and try again.</p>
          </body>
        </html>
      `, {
        status: 404,
        headers: { 'Content-Type': 'text/html' },
      });
    }
    
    return NextResponse.next();
  }


    if (!tenantContext.isActive) {
    console.warn(`🚨 Tenant is inactive: ${tenantContext.id}`);
    return new NextResponse(`
      <html>
        <body style="font-family: system-ui; text-align: center; padding: 50px;">
          <h1>Service Temporarily Unavailable</h1>
          <p>This service is temporarily unavailable.</p>
          <p>Please try again later or contact support.</p>
        </body>
      </html>
    `, {
      status: 503,
      headers: { 'Content-Type': 'text/html' },
    });
  }

  // Rate limiting
  const clientIP = req.headers.get('x-forwarded-for')?.split(',')[0] ||
                  req.headers.get('x-real-ip') ||
                  'anonymous';

  // Determine rate limit config
  let rateLimitKey = 'default';
  for (const [route] of Object.entries(rateLimitConfig)) {
    if (pathname.startsWith(route)) {
      rateLimitKey = route;
      break;
    }
  }


  const config = rateLimitConfig[rateLimitKey] || rateLimitConfig.default;
  const rateLimitResult = checkRateLimit(`${tenantContext.id}:${rateLimitKey}:${clientIP}`, config);

  if (!rateLimitResult.success) {
    console.warn(`🚨 Rate limit exceeded: ${pathname} for IP ${clientIP}`);
    
    return new NextResponse(
      JSON.stringify({
        error: "Too Many Requests",
        message: "Rate limit exceeded. Please try again later.",
        retryAfter: Math.ceil((rateLimitResult.resetTime - Date.now()) / 1000),
      }),
      {
        status: 429,
        headers: {
          "Content-Type": "application/json",
          "Retry-After": Math.ceil((rateLimitResult.resetTime - Date.now()) / 1000).toString(),
          "X-RateLimit-Limit": config.requests.toString(),
          "X-RateLimit-Remaining": rateLimitResult.remaining.toString(),
          "X-RateLimit-Reset": rateLimitResult.resetTime.toString(),
        },
      }
    );
  }


  // Check if route is public
  const isPublicRoute = publicRoutes.some(route => 
    pathname === route || pathname.startsWith(route)
  );

  if (isPublicRoute) {
    const response = NextResponse.next();
    // Add security headers
    response.headers.set("X-Frame-Options", "DENY");
    response.headers.set("X-Content-Type-Options", "nosniff");
    response.headers.set("Referrer-Policy", "strict-origin-when-cross-origin");
    return response;
  }

  const session = await auth();
  const isAuthenticated = !!session?.user;


  console.log("🔐 Auth status:", {
    isAuthenticated,
    userEmail: session?.user?.email,
    userType: session?.user?.userType,
    tenantId: session?.user?.tenantId,
  });

   // Validate tenant context matches session
  if (isAuthenticated && session.user.tenantId !== tenantContext.id) {
    console.warn(`🚨 Tenant mismatch: session=${session.user.tenantId}, context=${tenantContext.id}`);
    
    // Clear session and redirect to sign in
    const signInUrl = new URL("/auth/signin", req.url);
    signInUrl.searchParams.set("error", "tenant_mismatch");
    return NextResponse.redirect(signInUrl);
  }

  // Handle auth routes
  // const isAuthRoute = authRoutes.some(route => pathname.startsWith(route));
  // if (isAuthRoute) {
  //   if (isAuthenticated) {
  //     console.log("🔀 Redirecting authenticated user from auth page");
  //     return NextResponse.redirect(new URL("/dashboard", req.url));
  //   }
  //   return addSecurityHeaders(NextResponse.next(), tenantContext);
  // }


//   // Handle admin routes (if any staff users access customer tenant)
//   const isAdminRoute = adminRoutes.some(route => 
//     pathname === route || pathname.startsWith(route)
//   );

//   if (isAdminRoute) {
//     if (!isAuthenticated) {
//       console.log("🚫 Redirecting unauthenticated admin to signin");
//       return NextResponse.redirect(new URL("/auth/signin", req.url));
//     }

//     // Admin routes should redirect to appropriate admin domain
//     // This is tenant-specific business logic
//     return NextResponse.redirect(new URL("/dashboard", req.url));
//   }

//   return addSecurityHeaders(NextResponse.next(), tenantContext);
// }



  


  

  // Handle customer routes
  const isCustomerRoute = customerRouter.some(route => 
    pathname === route || pathname.startsWith(route)
  );

  if (isCustomerRoute) {
    if (!isAuthenticated) {
      console.log("🚫 Redirecting unauthenticated user to signin");
      const signInUrl = new URL("/auth/signin", req.url);
      signInUrl.searchParams.set("callbackUrl", pathname);
      return NextResponse.redirect(signInUrl);
    }

    // Verify user type is customer
    if (session.user.userType !== "customer") {
      console.log("🚫 Wrong user type for customer route");
      return NextResponse.redirect(new URL("/auth/signin", req.url));
    }

    // Check email verification if required
    if (tenantContext.settings.requireEmailVerification && !session.user.isEmailVerified) {
      console.log("🚫 Email verification required");
      return NextResponse.redirect(new URL("/auth/verify-email", req.url));
    }
  }


  

  // Get session from cookies - check all possible cookie names
  const sessionToken = req.cookies.get("next-auth.session-token")?.value ||
                      req.cookies.get("__Secure-next-auth.session-token")?.value ||
                      req.cookies.get("authjs.session-token")?.value ||
                      req.cookies.get("__Secure-authjs.session-token")?.value;

  const isLoggedIn = !!sessionToken;

  // Check if route is protected
  const isProtectedRoute = protectedRoutes.some(route =>
    nextUrl.pathname.startsWith(route)
  );

  // Check if route is admin-only (for future use)
  // const isAdminRoute = adminRoutes.some(route =>
  //   nextUrl.pathname.startsWith(route)
  // );


  // Check if route is auth route
  const isAuthRoute = authRoutes.some(route =>
    nextUrl.pathname.startsWith(route)
  );

  console.log("🔍 Middleware - Route checks:", {
    pathname: nextUrl.pathname,
    isProtectedRoute,
    isAuthRoute,
    isPublicRoute,
    isLoggedIn
  });

  // Redirect authenticated users away from auth pages
  if (isAuthRoute && isLoggedIn) {
    return NextResponse.redirect(new URL("/dashboard", nextUrl));
  }

  // Handle protected routes
  if (isProtectedRoute && !isLoggedIn) {
    // For API routes, return JSON error instead of redirect
    if (nextUrl.pathname.startsWith('/api/')) {
      return NextResponse.json(
        { error: "Unauthorized - Please sign in" },
        { status: 401 }
      );
    }

    // For page routes, redirect to signin
    const callbackUrl = encodeURIComponent(nextUrl.pathname + nextUrl.search);
    return NextResponse.redirect(
      new URL(`/auth/signin?callbackUrl=${callbackUrl}`, nextUrl)
    );
  }

  // Allow public routes
  if (isPublicRoute) {
    return NextResponse.next();
  }

  // Add security headers
  const response = NextResponse.next();

  // Security headers
  response.headers.set("X-Frame-Options", "DENY");
  response.headers.set("X-Content-Type-Options", "nosniff");
  response.headers.set("Referrer-Policy", "strict-origin-when-cross-origin");
  response.headers.set(
    "Strict-Transport-Security",
    "max-age=31536000; includeSubDomains"
  );

  // CSP header for better security
  const cspHeader = `
    default-src 'self';
    script-src 'self' 'unsafe-eval' 'unsafe-inline' https://js.stripe.com https://challenges.cloudflare.com;
    style-src 'self' 'unsafe-inline';
    img-src 'self' blob: data: https:;
    font-src 'self';
    object-src 'none';
    base-uri 'self';
    form-action 'self';
    frame-ancestors 'none';
    connect-src 'self' https://api.stripe.com https://challenges.cloudflare.com;
  `.replace(/\s{2,}/g, ' ').trim();

  response.headers.set("Content-Security-Policy", cspHeader);

  return response;
}

// Configure which routes the middleware should run on
export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder
     */
    "/((?!_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)",
  ],
};
