import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import type { 
  BlogPost, 
  BlogCategory, 
  NewBlogPost, 
  NewBlogCategory 
} from "@/lib/db/schema";
import type { 
  CreateBlogPostData, 
  UpdateBlogPostData, 
  CreateBlogCategoryData, 
  UpdateBlogCategoryData,
  BlogPostFilters,
  BlogCategoryFilters 
} from "@/lib/validations/blog";
import { createEntityHooks, type BaseApiInterface, type QueryOptions } from "@/lib/core/base-query-hooks";

// Extended types for blog posts with relations
export interface BlogPostWithAuthor extends BlogPost {
  author: {
    id: string;
    name: string;
    email: string;
  };
  category?: BlogCategory;
}

// API interface implementation for blog posts
const blogPostApi: BaseApiInterface<BlogPostWithAuthor, CreateBlogPostData, UpdateBlogPostData> = {
  getAll: async (options?: QueryOptions): Promise<BlogPostWithAuthor[]> => {
    const params = new URLSearchParams();
    if (options?.tenantId) params.append('tenantId', options.tenantId.toString());
    if (options?.limit) params.append('limit', options.limit.toString());
    if (options?.offset) params.append('offset', options.offset.toString());
    if (options?.filters?.search) params.append('search', options.filters.search);
    if (options?.filters?.category_id) params.append('category_id', options.filters.category_id);
    if (options?.filters?.status) params.append('status', options.filters.status);
    if (options?.filters?.is_featured !== undefined) params.append('is_featured', options.filters.is_featured.toString());
    if (options?.filters?.author_id) params.append('author_id', options.filters.author_id);
    if (options?.filters?.tags && options.filters.tags.length > 0) {
      params.append('tags', options.filters.tags.join(','));
    }

    const response = await fetch(`/api/blog-posts?${params}`);
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to fetch blog posts');
    }
    const result = await response.json();
    return result.data;
  },

  getById: async (id: string): Promise<BlogPostWithAuthor> => {
    const response = await fetch(`/api/blog-posts/${id}`);
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to fetch blog post');
    }
    const result = await response.json();
    return result.data;
  },

  getByTenant: async (tenantId: number, options?: QueryOptions): Promise<BlogPostWithAuthor[]> => {
    return blogPostApi.getAll({ ...options, tenantId });
  },

  create: async (data: CreateBlogPostData): Promise<BlogPostWithAuthor> => {
    const response = await fetch('/api/blog-posts', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to create blog post');
    }

    const result = await response.json();
    return result.data;
  },

  update: async (id: string, data: UpdateBlogPostData): Promise<BlogPostWithAuthor> => {
    const response = await fetch(`/api/blog-posts/${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to update blog post');
    }

    const result = await response.json();
    return result.data;
  },

  delete: async (id: string): Promise<void> => {
    const response = await fetch(`/api/blog-posts/${id}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to delete blog post');
    }
  },

  bulkOperation: async (data: any): Promise<BlogPostWithAuthor[]> => {
    const response = await fetch('/api/blog-posts/bulk', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to perform bulk operation');
    }

    const result = await response.json();
    return result.data;
  },
};

// API interface implementation for blog categories
const blogCategoryApi: BaseApiInterface<BlogCategory, CreateBlogCategoryData, UpdateBlogCategoryData> = {
  getAll: async (options?: QueryOptions): Promise<BlogCategory[]> => {
    const params = new URLSearchParams();
    if (options?.tenantId) params.append('tenantId', options.tenantId.toString());
    if (options?.filters?.search) params.append('search', options.filters.search);
    if (options?.filters?.is_active !== undefined) params.append('is_active', options.filters.is_active.toString());

    const response = await fetch(`/api/blog-categories?${params}`);
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to fetch blog categories');
    }
    const result = await response.json();
    return result.data;
  },

  getById: async (id: string): Promise<BlogCategory> => {
    const response = await fetch(`/api/blog-categories/${id}`);
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to fetch blog category');
    }
    const result = await response.json();
    return result.data;
  },

  getByTenant: async (tenantId: number, options?: QueryOptions): Promise<BlogCategory[]> => {
    return blogCategoryApi.getAll({ ...options, tenantId });
  },

  create: async (data: CreateBlogCategoryData): Promise<BlogCategory> => {
    const response = await fetch('/api/blog-categories', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to create blog category');
    }

    const result = await response.json();
    return result.data;
  },

  update: async (id: string, data: UpdateBlogCategoryData): Promise<BlogCategory> => {
    const response = await fetch(`/api/blog-categories/${id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to update blog category');
    }

    const result = await response.json();
    return result.data;
  },

  delete: async (id: string): Promise<void> => {
    const response = await fetch(`/api/blog-categories/${id}`, {
      method: 'DELETE',
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to delete blog category');
    }
  },

  bulkOperation: async (data: any): Promise<BlogCategory[]> => {
    const response = await fetch('/api/blog-categories/bulk', {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.error || 'Failed to perform bulk operation');
    }

    const result = await response.json();
    return result.data;
  },
};

// Create base hooks using the factory
const blogPostHooks = createEntityHooks('blog-posts', blogPostApi);
const blogCategoryHooks = createEntityHooks('blog-categories', blogCategoryApi);

// Export blog post hooks with descriptive names
export const useBlogPosts = blogPostHooks.useEntities;
export const useBlogPost = blogPostHooks.useEntity;
export const useBlogPostsByTenant = blogPostHooks.useEntitiesByTenant;
export const useBlogPostStats = blogPostHooks.useEntityStats;
export const useBlogPostSearch = blogPostHooks.useEntitySearch;
export const useCreateBlogPost = blogPostHooks.useCreateEntity;
export const useUpdateBlogPost = blogPostHooks.useUpdateEntity;
export const useDeleteBlogPost = blogPostHooks.useDeleteEntity;
export const useBulkBlogPostOperation = blogPostHooks.useBulkOperation;

// Export blog category hooks with descriptive names
export const useBlogCategories = blogCategoryHooks.useEntities;
export const useBlogCategory = blogCategoryHooks.useEntity;
export const useBlogCategoriesByTenant = blogCategoryHooks.useEntitiesByTenant;
export const useBlogCategoryStats = blogCategoryHooks.useEntityStats;
export const useBlogCategorySearch = blogCategoryHooks.useEntitySearch;
export const useCreateBlogCategory = blogCategoryHooks.useCreateEntity;
export const useUpdateBlogCategory = blogCategoryHooks.useUpdateEntity;
export const useDeleteBlogCategory = blogCategoryHooks.useDeleteEntity;
export const useBulkBlogCategoryOperation = blogCategoryHooks.useBulkOperation;

// Custom hooks for specific blog functionality
export function useBlogPostBySlug(tenantId: number, slug: string) {
  return useQuery({
    queryKey: ['blog-posts', 'slug', tenantId, slug],
    queryFn: async () => {
      const response = await fetch(`/api/blog-posts/slug/${slug}?tenantId=${tenantId}`);
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch blog post');
      }
      const result = await response.json();
      return result.data as BlogPostWithAuthor;
    },
    enabled: !!tenantId && !!slug,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

export function usePublishedBlogPosts(tenantId: number, filters?: Omit<BlogPostFilters, 'status'>) {
  return useBlogPostsByTenant(tenantId, {
    filters: { ...filters, status: 'published' as const }
  });
}

export function useFeaturedBlogPosts(tenantId: number, limit: number = 5) {
  return useBlogPostsByTenant(tenantId, {
    filters: { status: 'published' as const, is_featured: true },
    limit
  });
}

export function useIncrementViewCount() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (postId: string) => {
      const response = await fetch(`/api/blog-posts/${postId}/view`, {
        method: 'POST',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to increment view count');
      }

      return response.json();
    },
    onSuccess: (_, postId) => {
      // Invalidate the specific post query to update view count
      queryClient.invalidateQueries({ queryKey: ['blog-posts', 'detail', postId] });
    },
  });
}

// Slug validation hook
export function useValidateSlug(tenantId: number) {
  return useMutation({
    mutationFn: async ({ slug, excludeId }: { slug: string; excludeId?: string }) => {
      const params = new URLSearchParams();
      params.append('tenantId', tenantId.toString());
      params.append('slug', slug);
      if (excludeId) params.append('excludeId', excludeId);

      const response = await fetch(`/api/blog-posts/validate-slug?${params}`);
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to validate slug');
      }
      const result = await response.json();
      return result.available as boolean;
    },
  });
}

// Image upload hook
export function useUploadBlogImage() {
  return useMutation({
    mutationFn: async ({ file, tenantId }: { file: File; tenantId: number }) => {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('tenantId', tenantId.toString());

      const response = await fetch('/api/blog-posts/upload-image', {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to upload image');
      }

      const result = await response.json();
      return result.url as string;
    },
    onSuccess: () => {
      toast.success('Gambar berhasil diupload');
    },
    onError: (error) => {
      toast.error(`Gagal upload gambar: ${error.message}`);
    },
  });
}
