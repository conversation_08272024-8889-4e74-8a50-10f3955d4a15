import { Metadata } from "next";
import { getCurrentUser } from "@/lib/auth/utils";
import { redirect } from "next/navigation";
import { db } from "@/lib/db";
import { organizationMembers } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";
import { OrganizationSettings } from "@/components/organization/organization-settings";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";

export const metadata: Metadata = {
  title: "Settings - Your SaaS App",
  description: "Manage your account and organization settings",
};

export default async function SettingsPage() {
  const user = await getCurrentUser();
  
  if (!user) {
    redirect("/auth/signin");
  }

  // Get user's role in current organization
  let userRole: "owner" | "admin" | "member" = "member";
  
  if (user.organizationId) {
    const [membership] = await db
      .select({ role: organizationMembers.role })
      .from(organizationMembers)
      .where(
        and(
          eq(organizationMembers.userId, user.id),
          eq(organizationMembers.organizationId, user.organizationId)
        )
      )
      .limit(1);
    
    if (membership) {
      userRole = membership.role as "owner" | "admin" | "member";
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <Tabs defaultValue={user.organizationId ? "organization" : "account"} className="space-y-6">
        <TabsList>
          <TabsTrigger value="account">Account</TabsTrigger>
          {user.organizationId && (
            <TabsTrigger value="organization">Organization</TabsTrigger>
          )}
          <TabsTrigger value="api">API Keys</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
        </TabsList>

        <TabsContent value="account" className="space-y-6">
          <div>
            <h1 className="text-3xl font-bold">Account Settings</h1>
            <p className="text-muted-foreground">
              Manage your personal account settings and preferences
            </p>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Profile Information</CardTitle>
              <CardDescription>
                Update your personal information
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium">Name</label>
                  <p className="text-sm text-muted-foreground">{user.name || "Not set"}</p>
                </div>
                <div>
                  <label className="text-sm font-medium">Email</label>
                  <p className="text-sm text-muted-foreground">{user.email}</p>
                </div>
                <div>
                  <label className="text-sm font-medium">Role</label>
                  <p className="text-sm text-muted-foreground">{user.role}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Security</CardTitle>
              <CardDescription>
                Manage your account security settings
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                Security settings coming soon...
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {user.organizationId && (
          <TabsContent value="organization" className="space-y-6">
            <OrganizationSettings 
              organizationId={user.organizationId} 
              userRole={userRole}
            />
          </TabsContent>
        )}

        <TabsContent value="api" className="space-y-6">
          <div>
            <h1 className="text-3xl font-bold">API Keys</h1>
            <p className="text-muted-foreground">
              Manage your API keys and access tokens
            </p>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>API Key Management</CardTitle>
              <CardDescription>
                Create and manage API keys for accessing our services
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                API key management coming soon...
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notifications" className="space-y-6">
          <div>
            <h1 className="text-3xl font-bold">Notifications</h1>
            <p className="text-muted-foreground">
              Configure your notification preferences
            </p>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Email Notifications</CardTitle>
              <CardDescription>
                Choose which emails you'd like to receive
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-muted-foreground">
                Notification settings coming soon...
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
