# 🔧 Voucher System - Troubleshooting Checklist

> **Quick Reference untuk Debugging Masalah Voucher System**

---

## 🚨 Emergency Debugging Steps

### **1. Voucher Tidak Muncul di Frontend**

#### **Quick Check Commands:**
```bash
# 1. Cek apakah server ber<PERSON><PERSON>
curl http://localhost:3001/health

# 2. Test voucher API langsung
curl -X GET "http://localhost:3001/api/vouchers?tenantId=1" \
  -H "Cookie: next-auth.session-token=YOUR_SESSION"

# 3. Cek database langsung
psql -h 127.0.0.1 -p 5433 -U citizix_user -d saas_app \
  -c "SELECT id, code, name, tenant_id, is_active FROM vouchers WHERE tenant_id = 1;"
```

#### **Debugging Checklist:**
- [ ] ✅ **Server Running**: Development server berjalan di port yang benar
- [ ] ✅ **Authentication**: User sudah <NAME_EMAIL>
- [ ] ✅ **Database Connection**: Database accessible dan berisi data
- [ ] ✅ **Tenant ID**: tenantId = 1 (bukan 0 atau undefined)
- [ ] ✅ **API Response**: `/api/vouchers` return data dengan structure yang benar
- [ ] ✅ **React Query**: useVouchersByTenant enabled dan tidak error
- [ ] ✅ **Cache**: Query cache tidak stale atau corrupted

#### **Common Fixes:**
```typescript
// ❌ Problem: tenantId = 0
const { tenant } = useTenant(); // undefined
const tenantId = tenant?.id || 0; // Results in 0

// ✅ Solution: Hardcode tenantId
const tenantId = 1;
const { data: vouchers } = useVouchersByTenant(tenantId, { filters });
```

---

## 🔍 Systematic Debugging Process

### **Step 1: Verify Data Layer**
```sql
-- Check if vouchers exist in database
SELECT 
  id, 
  code, 
  name, 
  tenant_id, 
  is_active, 
  created_at 
FROM vouchers 
WHERE tenant_id = 1 
ORDER BY created_at DESC 
LIMIT 10;

-- Check voucher count per tenant
SELECT 
  tenant_id, 
  COUNT(*) as total_vouchers,
  COUNT(CASE WHEN is_active = true THEN 1 END) as active_vouchers
FROM vouchers 
GROUP BY tenant_id;
```

### **Step 2: Test API Layer**
```bash
# Test with authentication
curl -X GET "http://localhost:3001/api/vouchers?tenantId=1&limit=10" \
  -H "Content-Type: application/json" \
  -H "Cookie: next-auth.session-token=YOUR_SESSION_TOKEN"

# Expected response:
{
  "success": true,
  "data": [...vouchers...],
  "count": 2
}
```

### **Step 3: Debug Frontend Layer**
```typescript
// Add debugging to voucher page
const { data: vouchers, isLoading, error } = useVouchersByTenant(tenantId, {
  filters: {
    search: searchQuery || undefined,
    type: typeFilter !== "all" ? typeFilter : undefined,
    is_active: statusFilter === "active" ? true : 
              statusFilter === "inactive" ? false : undefined,
  }
});

// Debug logs
console.log("🔍 Voucher Debug:", {
  tenantId,
  vouchers,
  isLoading,
  error,
  vouchersLength: vouchers?.length,
  filters: {
    search: searchQuery,
    type: typeFilter,
    status: statusFilter
  }
});
```

### **Step 4: Check React Query Cache**
```typescript
// In browser console
const queryClient = window.__REACT_QUERY_DEVTOOLS_GLOBAL_HOOK__?.queryClient;
if (queryClient) {
  // Check all voucher queries
  const voucherQueries = queryClient.getQueryCache().findAll(['vouchers']);
  console.log("Voucher queries in cache:", voucherQueries);
  
  // Check specific query
  const tenantQuery = queryClient.getQueryData(['vouchers', 'list', { tenantId: 1 }]);
  console.log("Tenant vouchers query:", tenantQuery);
}
```

---

## ⚡ Quick Fixes

### **Fix #1: Tenant Context Issue**
```typescript
// File: src/app/(dashboard)/vouchers/page.tsx

// ❌ Before
const { tenant } = useTenant(); // No parameter
const tenantId = tenant?.id || 0; // Results in 0

// ✅ After
const tenantId = 1; // Hardcode for development
// const { tenant } = useTenant(tenantId); // Uncomment when proper tenant context ready
```

### **Fix #2: Query Key Mismatch**
```typescript
// File: src/lib/core/base-query-hooks.ts

// ✅ Ensure query key includes all parameters
const useEntitiesByTenant = (tenantId: number, options?: QueryOptions) => {
  const { queryOptions, ...apiOptions } = options || {};
  
  return useQuery({
    queryKey: queryKeys.list({ tenantId, ...apiOptions }), // ✅ Include all params
    queryFn: () => api.getByTenant(tenantId, apiOptions),
    enabled: !!tenantId, // ✅ Only enabled when tenantId > 0
    staleTime: 5 * 60 * 1000,
    ...queryOptions,
  });
};
```

### **Fix #3: API Authentication**
```typescript
// Check if user is authenticated
const session = await auth();
if (!session?.user) {
  return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
}

// Check RBAC permissions
const result = await checkPermission(request, { module: "vouchers", action: "read" });
if (!result.allowed) {
  return NextResponse.json({ error: result.error }, { status: 403 });
}
```

### **Fix #4: Cache Invalidation**
```typescript
// After create/update/delete voucher
const mutation = useMutation({
  mutationFn: voucherApi.create,
  onSuccess: (newVoucher) => {
    // ✅ Invalidate all related queries
    queryClient.invalidateQueries({ queryKey: ['vouchers'] });
    queryClient.invalidateQueries({ queryKey: ['vouchers', 'list'] });
    queryClient.invalidateQueries({ queryKey: ['vouchers', 'tenant', newVoucher.tenantId] });
  },
});
```

---

## 🛠️ Development Tools

### **React Query Devtools**
```typescript
// Add to your app for debugging
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

function App() {
  return (
    <>
      {/* Your app */}
      <ReactQueryDevtools initialIsOpen={false} />
    </>
  );
}
```

### **Custom Debug Hook**
```typescript
// File: src/lib/hooks/use-debug.ts
export function useDebugVouchers(tenantId: number) {
  const queryClient = useQueryClient();
  
  useEffect(() => {
    const interval = setInterval(() => {
      const queries = queryClient.getQueryCache().findAll(['vouchers']);
      console.log("🔍 Voucher Queries Debug:", {
        totalQueries: queries.length,
        queries: queries.map(q => ({
          queryKey: q.queryKey,
          state: q.state.status,
          data: q.state.data?.length || 0,
          error: q.state.error?.message
        }))
      });
    }, 5000);
    
    return () => clearInterval(interval);
  }, [queryClient]);
}

// Usage in voucher page
export default function VouchersPage() {
  const tenantId = 1;
  useDebugVouchers(tenantId); // Add this line
  
  // Rest of component...
}
```

### **API Debug Endpoint**
```typescript
// File: src/app/api/debug/vouchers/route.ts
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const tenantId = parseInt(searchParams.get("tenantId") || "1");
    
    // Get vouchers from service
    const vouchers = await VoucherService.getByTenantWithFilters(tenantId, {});
    
    // Get raw database data
    const rawVouchers = await db
      .select()
      .from(vouchers)
      .where(eq(vouchers.tenantId, tenantId));
    
    return NextResponse.json({
      success: true,
      debug: {
        tenantId,
        serviceResult: {
          count: vouchers.length,
          vouchers: vouchers.map(v => ({ id: v.id, code: v.code, name: v.name }))
        },
        rawDatabaseResult: {
          count: rawVouchers.length,
          vouchers: rawVouchers.map(v => ({ id: v.id, code: v.code, name: v.name }))
        },
        match: vouchers.length === rawVouchers.length
      }
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}
```

---

## 📋 Production Checklist

### **Before Deployment:**
- [ ] ✅ **Environment Variables**: All required env vars set
- [ ] ✅ **Database Migrations**: All migrations applied
- [ ] ✅ **Authentication**: NextAuth configured properly
- [ ] ✅ **RBAC Permissions**: All voucher permissions defined
- [ ] ✅ **API Endpoints**: All endpoints tested and working
- [ ] ✅ **Error Handling**: Comprehensive error handling implemented
- [ ] ✅ **Validation**: Input validation with Zod schemas
- [ ] ✅ **Performance**: Database indexes created
- [ ] ✅ **Security**: SQL injection prevention, XSS protection
- [ ] ✅ **Monitoring**: Error tracking and logging setup

### **Post-Deployment Verification:**
```bash
# 1. Health check
curl https://your-domain.com/api/health

# 2. Voucher API test
curl -X GET "https://your-domain.com/api/vouchers?tenantId=1" \
  -H "Authorization: Bearer YOUR_TOKEN"

# 3. Database connectivity
# Check database connection and query performance

# 4. Error monitoring
# Check error tracking service for any issues
```

---

## 🆘 Emergency Recovery

### **If Vouchers Completely Broken:**

1. **Immediate Rollback:**
   ```bash
   # Rollback to last working commit
   git revert HEAD
   git push origin main
   
   # Or rollback database if needed
   pg_restore -h HOST -U USER -d DATABASE backup_file.sql
   ```

2. **Quick Hotfix:**
   ```typescript
   // Temporary bypass for critical issues
   export default function VouchersPage() {
     // ✅ Hardcode working data temporarily
     const vouchers = [
       { id: '1', code: 'TEMP10', name: 'Temporary 10% Off', type: 'percentage', value: 10 }
     ];
     
     return (
       <div>
         <Alert>
           <AlertCircle className="h-4 w-4" />
           <AlertDescription>
             Voucher system sedang dalam maintenance. Data di bawah adalah temporary.
           </AlertDescription>
         </Alert>
         {/* Render temporary data */}
       </div>
     );
   }
   ```

3. **Contact Support:**
   - Document the exact error messages
   - Provide steps to reproduce
   - Include browser console logs
   - Share network tab from DevTools

---

## 📞 Support Contacts

### **Development Team:**
- **Frontend Issues**: Check React Query cache and component state
- **Backend Issues**: Check API logs and database queries  
- **Database Issues**: Check connection and query performance
- **Authentication Issues**: Check NextAuth configuration

### **Common Support Scenarios:**
1. **"Vouchers not loading"** → Check tenant context and authentication
2. **"Create voucher fails"** → Check validation and RBAC permissions
3. **"Data not refreshing"** → Check cache invalidation
4. **"Performance issues"** → Check database indexes and query optimization

---

*Troubleshooting Guide v1.0.0 - Last Updated: 2025-01-19*
