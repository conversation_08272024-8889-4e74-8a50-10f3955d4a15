import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { PermissionService } from "@/lib/services/permission.service";
import { withRBAC } from "@/lib/middleware/rbac-middleware";

// Validation schemas
const createPermissionSchema = z.object({
  module: z.string().min(1, "Module is required").max(100, "Module too long"),
  action: z.string().min(1, "Action is required").max(100, "Action too long"),
  display_name: z.string().min(1, "Display name is required").max(255, "Display name too long"),
  description: z.string().max(255, "Description too long").optional(),
  is_system_permission: z.boolean().default(false),
});

/**
 * API Routes untuk Permissions
 */

/**
 * GET /api/permissions
 * Get permissions with optional filtering
 */
export const GET = withRBAC(
  async (request: NextRequest) => {
    try {
      const { searchParams } = new URL(request.url);
      const module = searchParams.get("module") || undefined;
      const action = searchParams.get("action") || undefined;
      const search = searchParams.get("search") || undefined;
      const limit = parseInt(searchParams.get("limit") || "50");
      const offset = parseInt(searchParams.get("offset") || "0");

      // Call service untuk search permissions
      const permissionsResult = await PermissionService.searchPermissions(
        module,
        action,
        search,
        limit,
        offset
      );

      return NextResponse.json({
        success: true,
        data: permissionsResult,
      });
    } catch (error) {
      console.error("GET /api/permissions error:", error);
      return NextResponse.json(
        { success: false, error: "Internal server error" },
        { status: 500 }
      );
    }
  },
  { module: "permissions", action: "read" }
);

/**
 * POST /api/permissions
 * Create new permission
 */
export const POST = withRBAC(
  async (request: NextRequest) => {
    try {
      // Parse dan validate request body
      const body = await request.json();
      const validatedData = createPermissionSchema.parse(body);

      // Check if permission already exists
      const existingPermission = await PermissionService.getByModuleAndAction(
        validatedData.module,
        validatedData.action
      );

      if (existingPermission) {
        return NextResponse.json(
          {
            success: false,
            error: `Permission ${validatedData.module}.${validatedData.action} already exists`
          },
          { status: 409 }
        );
      }

      // Create permission
      const newPermission = await PermissionService.create(validatedData);

      return NextResponse.json({
        success: true,
        data: newPermission,
      }, { status: 201 });
    } catch (error: any) {
      console.error("POST /api/permissions error:", error);

      if (error.name === "ZodError") {
        return NextResponse.json(
          { success: false, error: "Validation failed", details: error.errors },
          { status: 400 }
        );
      }

      return NextResponse.json(
        { success: false, error: "Internal server error" },
        { status: 500 }
      );
    }
  },
  { module: "permissions", action: "create" }
);
