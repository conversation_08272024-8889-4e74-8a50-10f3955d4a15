import { MetadataRoute } from 'next'

export default function manifest(): MetadataRoute.Manifest {
  return {
    name: 'Your SaaS App',
    short_name: 'SaaS App',
    description: 'A comprehensive Next.js 15 SaaS template with authentication, billing, and multi-tenancy',
    start_url: '/',
    display: 'standalone',
    background_color: '#ffffff',
    theme_color: '#3b82f6',
    orientation: 'portrait-primary',
    categories: ['business', 'productivity', 'utilities'],
    lang: 'en',
    dir: 'ltr',
    icons: [
      {
        src: '/icon-192.png',
        sizes: '192x192',
        type: 'image/png',
        purpose: 'maskable',
      },
      {
        src: '/icon-512.png',
        sizes: '512x512',
        type: 'image/png',
        purpose: 'maskable',
      },
      {
        src: '/apple-icon.png',
        sizes: '180x180',
        type: 'image/png',
        purpose: 'any',
      },
    ],
    screenshots: [
      {
        src: '/screenshot-wide.png',
        sizes: '1280x720',
        type: 'image/png',
        form_factor: 'wide',
        label: 'Dashboard view of Your SaaS App',
      },
      {
        src: '/screenshot-narrow.png',
        sizes: '750x1334',
        type: 'image/png',
        form_factor: 'narrow',
        label: 'Mobile view of Your SaaS App',
      },
    ],
    shortcuts: [
      {
        name: 'Dashboard',
        short_name: 'Dashboard',
        description: 'Go to your dashboard',
        url: '/dashboard',
        icons: [
          {
            src: '/shortcut-dashboard.png',
            sizes: '96x96',
            type: 'image/png',
          },
        ],
      },
      {
        name: 'Billing',
        short_name: 'Billing',
        description: 'Manage your billing and credits',
        url: '/billing',
        icons: [
          {
            src: '/shortcut-billing.png',
            sizes: '96x96',
            type: 'image/png',
          },
        ],
      },
    ],
  }
}
