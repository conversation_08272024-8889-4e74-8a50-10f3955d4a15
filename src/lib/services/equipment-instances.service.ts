import { db } from "@/lib/db";
import { equipment_instances, equipment, locations } from "@/lib/db/schema";
import { eq, and, desc, sql } from "drizzle-orm";
import { createId } from "@paralleldrive/cuid2";

export interface EquipmentInstanceWithRelations {
  id: string;
  equipmentId: string | null;
  locationId: string | null;
  quantity: number;
  displayName: string | null;
  createdAt: Date;
  updatedAt: Date;
  equipment?: {
    id: string;
    name: string;
    default_display_name: string | null;
  } | null;
  location?: {
    id: string;
    addressLine1: string | null;
    city: string | null;
    state: string | null;
    country: string | null;
  } | null;
}

export interface CreateEquipmentInstanceData {
  equipmentId: string;
  locationId: string;
  quantity: number;
  displayName?: string;
}

export interface UpdateEquipmentInstanceData {
  equipmentId?: string;
  locationId?: string;
  quantity?: number;
  displayName?: string;
}

export class EquipmentInstanceService {
  // Get all equipment instances with relations
  static async getAll(): Promise<EquipmentInstanceWithRelations[]> {
    const result = await db
      .select({
        id: equipment_instances.id,
        equipmentId: equipment_instances.equipmentId,
        locationId: equipment_instances.locationId,
        quantity: equipment_instances.quantity,
        displayName: equipment_instances.displayName,
        createdAt: equipment_instances.createdAt,
        updatedAt: equipment_instances.updatedAt,
        equipment: {
          id: equipment.id,
          name: equipment.name,
          default_display_name: equipment.default_display_name,
        },
        location: {
          id: locations.id,
          addressLine1: locations.addressLine1,
          city: locations.city,
          state: locations.state,
          country: locations.country,
        },
      })
      .from(equipment_instances)
      .leftJoin(equipment, eq(equipment_instances.equipmentId, equipment.id))
      .leftJoin(locations, eq(equipment_instances.locationId, locations.id))
      .orderBy(desc(equipment_instances.createdAt));

    return result;
  }

  // Get equipment instances by equipment ID
  static async getByEquipmentId(equipmentId: string): Promise<EquipmentInstanceWithRelations[]> {
    const result = await db
      .select({
        id: equipment_instances.id,
        equipmentId: equipment_instances.equipmentId,
        locationId: equipment_instances.locationId,
        quantity: equipment_instances.quantity,
        displayName: equipment_instances.displayName,
        createdAt: equipment_instances.createdAt,
        updatedAt: equipment_instances.updatedAt,
        equipment: {
          id: equipment.id,
          name: equipment.name,
          default_display_name: equipment.default_display_name,
        },
        location: {
          id: locations.id,
          addressLine1: locations.addressLine1,
          city: locations.city,
          state: locations.state,
          country: locations.country,
        },
      })
      .from(equipment_instances)
      .leftJoin(equipment, eq(equipment_instances.equipmentId, equipment.id))
      .leftJoin(locations, eq(equipment_instances.locationId, locations.id))
      .where(eq(equipment_instances.equipmentId, equipmentId))
      .orderBy(desc(equipment_instances.createdAt));

    return result;
  }

  // Get equipment instances by location ID
  static async getByLocationId(locationId: string): Promise<EquipmentInstanceWithRelations[]> {
    const result = await db
      .select({
        id: equipment_instances.id,
        equipmentId: equipment_instances.equipmentId,
        locationId: equipment_instances.locationId,
        quantity: equipment_instances.quantity,
        displayName: equipment_instances.displayName,
        createdAt: equipment_instances.createdAt,
        updatedAt: equipment_instances.updatedAt,
        equipment: {
          id: equipment.id,
          name: equipment.name,
          default_display_name: equipment.default_display_name,
        },
        location: {
          id: locations.id,
          addressLine1: locations.addressLine1,
          city: locations.city,
          state: locations.state,
          country: locations.country,
        },
      })
      .from(equipment_instances)
      .leftJoin(equipment, eq(equipment_instances.equipmentId, equipment.id))
      .leftJoin(locations, eq(equipment_instances.locationId, locations.id))
      .where(eq(equipment_instances.locationId, locationId))
      .orderBy(desc(equipment_instances.createdAt));

    return result;
  }

  // Get single equipment instance by ID
  static async getById(id: string): Promise<EquipmentInstanceWithRelations | null> {
    const [result] = await db
      .select({
        id: equipment_instances.id,
        equipmentId: equipment_instances.equipmentId,
        locationId: equipment_instances.locationId,
        quantity: equipment_instances.quantity,
        displayName: equipment_instances.displayName,
        createdAt: equipment_instances.createdAt,
        updatedAt: equipment_instances.updatedAt,
        equipment: {
          id: equipment.id,
          name: equipment.name,
          default_display_name: equipment.default_display_name,
        },
        location: {
          id: locations.id,
          addressLine1: locations.addressLine1,
          city: locations.city,
          state: locations.state,
          country: locations.country,
        },
      })
      .from(equipment_instances)
      .leftJoin(equipment, eq(equipment_instances.equipmentId, equipment.id))
      .leftJoin(locations, eq(equipment_instances.locationId, locations.id))
      .where(eq(equipment_instances.id, id));

    return result || null;
  }

  // Create new equipment instance
  static async create(data: CreateEquipmentInstanceData): Promise<EquipmentInstanceWithRelations> {
    const id = createId();
    
    const [newInstance] = await db
      .insert(equipment_instances)
      .values({
        id,
        equipmentId: data.equipmentId,
        locationId: data.locationId,
        quantity: data.quantity,
        displayName: data.displayName,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning();

    // Get the created instance with relations
    const result = await this.getById(id);
    if (!result) {
      throw new Error("Failed to retrieve created equipment instance");
    }

    return result;
  }

  // Update equipment instance
  static async update(id: string, data: UpdateEquipmentInstanceData): Promise<EquipmentInstanceWithRelations> {
    const [updatedInstance] = await db
      .update(equipment_instances)
      .set({
        ...data,
        updatedAt: new Date(),
      })
      .where(eq(equipment_instances.id, id))
      .returning();

    if (!updatedInstance) {
      throw new Error("Equipment instance not found");
    }

    // Get the updated instance with relations
    const result = await this.getById(id);
    if (!result) {
      throw new Error("Failed to retrieve updated equipment instance");
    }

    return result;
  }

  // Delete equipment instance
  static async delete(id: string): Promise<EquipmentInstanceWithRelations> {
    // First get the instance before deletion
    const existingInstance = await this.getById(id);
    if (!existingInstance) {
      throw new Error("Equipment instance not found");
    }

    // Delete the instance
    const [deletedInstance] = await db
      .delete(equipment_instances)
      .where(eq(equipment_instances.id, id))
      .returning();

    if (!deletedInstance) {
      throw new Error("Failed to delete equipment instance");
    }

    return existingInstance;
  }

  // Get statistics
  static async getStats() {
    const [totalCount] = await db
      .select({ count: sql<number>`count(*)` })
      .from(equipment_instances);

    const [totalQuantity] = await db
      .select({ total: sql<number>`sum(${equipment_instances.quantity})` })
      .from(equipment_instances);

    const equipmentCounts = await db
      .select({
        equipmentId: equipment_instances.equipmentId,
        equipmentName: equipment.name,
        count: sql<number>`count(*)`,
        totalQuantity: sql<number>`sum(${equipment_instances.quantity})`,
      })
      .from(equipment_instances)
      .leftJoin(equipment, eq(equipment_instances.equipmentId, equipment.id))
      .groupBy(equipment_instances.equipmentId, equipment.name);

    const locationCounts = await db
      .select({
        locationId: equipment_instances.locationId,
        locationAddress: locations.addressLine1,
        count: sql<number>`count(*)`,
        totalQuantity: sql<number>`sum(${equipment_instances.quantity})`,
      })
      .from(equipment_instances)
      .leftJoin(locations, eq(equipment_instances.locationId, locations.id))
      .groupBy(equipment_instances.locationId, locations.addressLine1);

    return {
      totalInstances: totalCount.count,
      totalQuantity: totalQuantity.total || 0,
      byEquipment: equipmentCounts,
      byLocation: locationCounts,
    };
  }
}
