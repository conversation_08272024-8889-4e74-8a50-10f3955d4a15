# 🔐 Panduan Implementasi RBAC System - SaaS Template

## 📋 Daftar Isi
1. [Pengenalan RBAC](#pengenalan-rbac)
2. [Arsitektur Database](#arsitektur-database)
3. [Empat Tipe Actor](#empat-tipe-actor)
4. [<PERSON>](#cara-menggunakan)
5. [API Endpoints](#api-endpoints)
6. [Komponen Frontend](#komponen-frontend)
7. [Middleware & Security](#middleware--security)
8. [Testing](#testing)
9. [Troubleshooting](#troubleshooting)

---

## 🎯 Pengenalan RBAC

**Role-Based Access Control (RBAC)** adalah sistem keamanan yang mengatur akses berdasarkan peran (role) pengguna. Bayangkan seperti sistem keamanan di kantor - ada yang punya akses ke semua ruangan (security), ada yang cuma bisa masuk ke ruang kerja sendiri (karyawan), dan ada yang bisa masuk ke beberapa ruang<PERSON> te<PERSON> (manager).

### Kenapa <PERSON>ai RBAC?
- **<PERSON><PERSON><PERSON>**: <PERSON>iap user cuma bisa akses yang mereka butuhkan
- **<PERSON><PERSON>**: <PERSON>ggal assign role, bukan set permission satu-satu
- **Scalable**: Bisa handle ribuan user dengan mudah
- **Audit Trail**: Semua akses tercatat dengan jelas

### Komponen Utama RBAC
1. **Users** - Pengguna sistem
2. **Roles** - Peran/jabatan (Admin, Instructor, Customer)
3. **Permissions** - Hak akses spesifik (create, read, update, delete)
4. **Resources** - Yang diproteksi (classes, customers, packages)

---

## 🗄️ Arsitektur Database

### Tabel Utama RBAC

#### 1. `roles` - Definisi Peran
```sql
- id: varchar (CUID2)
- tenantId: integer (null untuk system roles)
- name: varchar (super_admin, tenant_admin, instructor, customer)
- display_name: varchar (Super Administrator, dll)
- description: text
- is_system_role: boolean (true untuk built-in roles)
- hierarchy_level: integer (0=tertinggi, 100=terendah)
- permissions: jsonb (JSONB untuk flexibility)
- is_active: boolean
- createdAt, updatedAt: timestamp
```

#### 2. `permissions` - Hak Akses Granular
```sql
- id: varchar (CUID2)
- module: varchar (classes, customers, packages)
- action: varchar (create, read, update, delete)
- resource: varchar (optional, untuk resource spesifik)
- display_name: varchar
- description: text
- is_system_permission: boolean
- createdAt, updatedAt: timestamp
```

#### 3. `user_roles` - Assignment User ke Role
```sql
- id: varchar (CUID2)
- userId: varchar (FK ke users)
- roleId: varchar (FK ke roles)
- tenantId: integer (null untuk super admin)
- assignedBy: varchar (siapa yang assign)
- assignedAt: timestamp
- expiresAt: timestamp (optional)
- is_active: boolean
- createdAt, updatedAt: timestamp
```

#### 4. `role_permissions` - Assignment Role ke Permission
```sql
- id: varchar (CUID2)
- roleId: varchar (FK ke roles)
- permissionId: varchar (FK ke permissions)
- conditions: jsonb (kondisi tambahan)
- createdAt, updatedAt: timestamp
```

#### 5. `user_location_access` - Akses Location-Based
```sql
- id: varchar (CUID2)
- userId: varchar (FK ke users)
- locationId: varchar (FK ke locations)
- tenantId: integer
- access_level: varchar (full, read_only, restricted)
- assignedBy: varchar
- assignedAt: timestamp
- is_active: boolean
- createdAt, updatedAt: timestamp
```

#### 6. `rbac_activity_logs` - Audit Trail
```sql
- id: varchar (CUID2)
- tenantId: integer
- userId: varchar (yang melakukan aksi)
- targetUserId: varchar (yang kena assign/revoke)
- action: varchar (role_assigned, role_revoked, dll)
- resource_type: varchar (role, permission, location_access)
- resource_id: varchar
- old_value, new_value: jsonb
- ip_address: varchar
- user_agent: text
- createdAt: timestamp
```

---

## 👥 Empat Tipe Actor

### 1. 🦸‍♂️ Super Admin
**Akses**: Full system access across all tenants

**Permissions**:
- `system.manage` - Kelola seluruh sistem
- `tenant.manage` - Kelola semua tenant
- `users.manage` - Kelola semua user
- `roles.*` - Semua operasi role
- Semua module permissions

**Use Case**:
- Platform owner/developer
- System maintenance
- Cross-tenant operations

### 2. 👨‍💼 Tenant Admin
**Akses**: Full access dalam tenant mereka

**Permissions**:
- `tenant.read` - Lihat info tenant
- `users.create/read/update/delete` - Kelola user dalam tenant
- `roles.assign/revoke` - Assign role ke user
- Semua module permissions dalam tenant

**Use Case**:
- Business owner
- Studio manager
- Department head

### 3. 🏃‍♂️ Instructor/Trainer
**Akses**: Limited access ke classes dan customers yang assigned

**Permissions**:
- `classes.read/update` - Kelola class yang diajar
- `customers.read/update` - Lihat customer yang ikut class
- `bookings.read/create/update` - Kelola booking
- `packages.read` - Lihat package info

**Use Case**:
- Fitness trainer
- Yoga instructor
- Class teacher

### 4. 🧑‍🤝‍🧑 Customer
**Akses**: Hanya data dan booking mereka sendiri

**Permissions**:
- `bookings.read/create` - Lihat dan buat booking sendiri
- `classes.read` - Lihat class yang tersedia
- `packages.read` - Lihat package yang bisa dibeli

**Use Case**:
- Member gym
- Student
- Client

---

## 🚀 Cara Menggunakan

### 1. Setup Database
```bash
# Jalankan migration untuk create tables
npm run db:migrate

# Seed initial RBAC data
npm run seed:rbac
```

### 2. Assign Role ke User
```typescript
import { RoleService } from "@/lib/services/role.service";

// Assign tenant admin role
await RoleService.assignRole({
  userId: "user123",
  roleId: "role456", 
  tenantId: 1,
  assignedBy: "admin789"
});
```

### 3. Check Permission di Component
```typescript
import { useSession } from "next-auth/react";
import { RBACHelpers } from "@/lib/auth/rbac-integration";

function ClassManagement() {
  const { data: session } = useSession();
  
  const canCreateClass = RBACHelpers.hasPermission(session, "classes", "create");
  const canDeleteClass = RBACHelpers.hasPermission(session, "classes", "delete");
  
  return (
    <div>
      {canCreateClass && <CreateClassButton />}
      {canDeleteClass && <DeleteClassButton />}
    </div>
  );
}
```

### 4. Protect API Route
```typescript
import { withRBAC } from "@/lib/middleware/rbac-middleware";

export const POST = withRBAC(
  async (request, context) => {
    // Your API logic here
    return NextResponse.json({ success: true });
  },
  { module: "classes", action: "create" }
);
```

### 5. Location-Based Access
```typescript
import { LocationAccessService } from "@/lib/services/location-access.service";

// Grant access ke location
await LocationAccessService.grantAccess({
  userId: "user123",
  locationId: "loc456",
  tenantId: 1,
  access_level: "full"
});

// Check access
const hasAccess = await LocationAccessService.userHasLocationAccess(
  "user123", 
  "loc456", 
  "read_only"
);
```

---

## 🔌 API Endpoints

### Roles
- `GET /api/roles` - Search roles
- `POST /api/roles` - Create role
- `GET /api/roles/[id]` - Get role by ID
- `PUT /api/roles/[id]` - Update role
- `DELETE /api/roles/[id]` - Delete role
- `POST /api/roles/assign` - Assign role to user
- `POST /api/roles/revoke` - Revoke role from user
- `GET /api/roles/user-roles` - Get user roles
- `GET /api/roles/system` - Get system roles

### Permissions
- `GET /api/permissions` - Search permissions
- `POST /api/permissions` - Create permission
- `GET /api/permissions/[id]` - Get permission by ID
- `PUT /api/permissions/[id]` - Update permission
- `DELETE /api/permissions/[id]` - Delete permission
- `GET /api/permissions/grouped` - Get permissions grouped by module
- `GET /api/permissions/role/[roleId]` - Get permissions for role
- `GET /api/permissions/user` - Get user permissions
- `POST /api/permissions/assign-to-role` - Assign permission to role
- `POST /api/permissions/revoke-from-role` - Revoke permission from role
- `GET /api/permissions/check` - Check user permission

### Location Access
- `GET /api/location-access` - Search location access
- `POST /api/location-access/grant` - Grant location access
- `POST /api/location-access/revoke` - Revoke location access
- `GET /api/location-access/user` - Get user location access
- `GET /api/location-access/user-accessible` - Get user accessible locations
- `GET /api/location-access/check` - Check user location access
- `POST /api/location-access/bulk-grant` - Bulk grant access
- `POST /api/location-access/bulk-revoke` - Bulk revoke access

---

## 🎨 Komponen Frontend

### 1. RoleSelector
Komponen untuk select multiple roles dengan search dan filter.

```typescript
import { RoleSelector } from "@/components/forms/role-selector";

<RoleSelector
  tenantId={1}
  selectedRoles={selectedRoles}
  onSelectionChange={setSelectedRoles}
  maxSelections={5}
  includeSystemRoles={true}
/>
```

### 2. PermissionSelector
Komponen untuk select permissions dengan grouping by module.

```typescript
import { PermissionSelector } from "@/components/forms/permission-selector";

<PermissionSelector
  selectedPermissions={selectedPermissions}
  onSelectionChange={setSelectedPermissions}
  maxSelections={20}
/>
```

### 3. LocationAccessSelector
Komponen untuk manage location access dengan access levels.

```typescript
import { LocationAccessSelector } from "@/components/forms/location-access-selector";

<LocationAccessSelector
  tenantId={1}
  selectedLocations={selectedLocations}
  onSelectionChange={setSelectedLocations}
  defaultAccessLevel="read_only"
/>
```

---

## 🛡️ Middleware & Security

### 1. RBAC Middleware
Middleware untuk protect API routes berdasarkan permissions.

```typescript
import { withRBAC } from "@/lib/middleware/rbac-middleware";

// Protect dengan permission check
export const GET = withRBAC(handler, { 
  module: "classes", 
  action: "read" 
});

// Dengan custom tenant/location extractor
export const POST = withRBAC(handler, {
  module: "classes",
  action: "create",
  extractTenantId: (req) => parseInt(req.headers.get("x-tenant-id")),
  extractLocationId: (req) => req.headers.get("x-location-id")
});
```

### 2. Route Protection
Middleware untuk protect pages berdasarkan roles.

```typescript
// middleware.ts
import { rbacRouteProtection } from "@/lib/middleware/rbac-middleware";

export async function middleware(request: NextRequest) {
  // RBAC route protection
  const rbacResult = await rbacRouteProtection(request);
  if (rbacResult) return rbacResult;
  
  // Continue to other middleware
  return NextResponse.next();
}
```

### 3. NextAuth Integration
Integration dengan NextAuth untuk include RBAC data di session.

```typescript
// auth.config.ts
import { rbacCallbacks } from "@/lib/auth/rbac-integration";

export const authConfig: NextAuthConfig = {
  callbacks: {
    jwt: rbacCallbacks.jwt,
    session: rbacCallbacks.session,
  },
};
```

---

## 🧪 Testing

### 1. Test Permission Check
```typescript
import { PermissionService } from "@/lib/services/permission.service";

// Test user permission
const hasPermission = await PermissionService.userHasPermission(
  "user123",
  "classes", 
  "create",
  1 // tenantId
);

expect(hasPermission).toBe(true);
```

### 2. Test Role Assignment
```typescript
import { RoleService } from "@/lib/services/role.service";

// Test assign role
const assignment = await RoleService.assignRole({
  userId: "user123",
  roleId: "role456",
  tenantId: 1
});

expect(assignment.is_active).toBe(true);
```

### 3. Test Location Access
```typescript
import { LocationAccessService } from "@/lib/services/location-access.service";

// Test location access
const hasAccess = await LocationAccessService.userHasLocationAccess(
  "user123",
  "loc456", 
  "read_only"
);

expect(hasAccess).toBe(true);
```

---

## 🔧 Troubleshooting

### Problem: User tidak bisa akses setelah assign role
**Solusi**:
1. Check apakah role assignment aktif: `user_roles.is_active = true`
2. Check apakah role punya permission yang dibutuhkan
3. Check apakah user di tenant yang benar
4. Refresh session: logout-login atau trigger JWT refresh

### Problem: Permission check selalu return false
**Solusi**:
1. Check format permission: harus `module.action` (contoh: `classes.create`)
2. Check apakah permission exist di database
3. Check apakah role-permission mapping exist
4. Check tenant isolation: pastikan tenantId match

### Problem: Location access tidak work
**Solusi**:
1. Check apakah user punya location access record
2. Check access level hierarchy: `full > read_only > restricted`
3. Check apakah location masih aktif
4. Check tenant isolation

### Problem: API route masih bisa diakses tanpa permission
**Solusi**:
1. Pastikan pakai `withRBAC` wrapper
2. Check permission module dan action name
3. Check middleware order di `middleware.ts`
4. Check NextAuth token validation

### Problem: Session tidak include RBAC data
**Solusi**:
1. Check NextAuth callbacks configuration
2. Check database connection di JWT callback
3. Check error logs di browser console
4. Force refresh dengan logout-login

---

## 📚 Resources Tambahan

### File Penting
- `src/lib/db/schema.ts` - Database schema
- `src/lib/services/role.service.ts` - Role management
- `src/lib/services/permission.service.ts` - Permission management
- `src/lib/middleware/rbac-middleware.ts` - Security middleware
- `src/lib/auth/rbac-integration.ts` - NextAuth integration
- `src/lib/db/seed-rbac.ts` - Database seeder

### Hooks & Queries
- `src/lib/hooks/queries/use-role-queries.ts` - Role TanStack Query hooks
- `src/lib/hooks/queries/use-permission-queries.ts` - Permission hooks
- `src/lib/hooks/queries/use-location-access-queries.ts` - Location access hooks

### Components
- `src/components/forms/role-selector.tsx` - Role selection component
- `src/components/forms/permission-selector.tsx` - Permission selection
- `src/components/forms/location-access-selector.tsx` - Location access management

---

**🎉 Selamat! RBAC system sudah siap digunakan. Sistem ini mengikuti FAANG-standard best practices dan siap untuk production environment.**
