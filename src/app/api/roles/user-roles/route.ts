import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { RoleService } from "@/lib/services/role.service";
import { withRBAC } from "@/lib/middleware/rbac-middleware";

/**
 * API Routes untuk User Roles
 */

const searchParamsSchema = z.object({
  tenantId: z.string().optional().transform((val) => {
    if (!val || val === 'null') return null;
    const parsed = parseInt(val, 10);
    return isNaN(parsed) ? null : parsed;
  }),
  userId: z.string().optional(),
  limit: z.string().transform((val) => parseInt(val, 10)).optional(),
  offset: z.string().transform((val) => parseInt(val, 10)).optional(),
});

/**
 * GET /api/roles/user-roles
 * Get user roles with user info
 */
export const GET = withRBAC(
  async (request: NextRequest) => {
    try {
      const { searchParams } = new URL(request.url);
      
      // Parse and validate query parameters
      const rawParams = {
        tenantId: searchParams.get("tenantId"),
        userId: searchParams.get("userId"),
        limit: searchParams.get("limit"),
        offset: searchParams.get("offset"),
      };

      // Remove null values
      const cleanParams = Object.fromEntries(
        Object.entries(rawParams).filter(([_, value]) => value !== null)
      );

      const validatedParams = searchParamsSchema.parse(cleanParams);

      // Call service
      const result = await RoleService.getUserRoles(
        validatedParams.tenantId,
        validatedParams.userId,
        validatedParams.limit || 20,
        validatedParams.offset || 0
      );

      return NextResponse.json({
        success: true,
        data: result,
      });
    } catch (error) {
      console.error("GET /api/roles/user-roles error:", error);
      
      if (error instanceof z.ZodError) {
        return NextResponse.json(
          { success: false, error: "Invalid parameters", details: error.errors },
          { status: 400 }
        );
      }

      return NextResponse.json(
        { success: false, error: "Internal server error" },
        { status: 500 }
      );
    }
  },
  { module: "roles", action: "read" }
);
