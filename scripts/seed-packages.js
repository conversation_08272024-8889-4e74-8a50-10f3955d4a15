// Script to seed sample packages for testing
// Run with: node scripts/seed-packages.js

const samplePackages = [
  {
    tenantId: 1,
    name: "Basic Fitness Package",
    description: "Basic fitness package with gym access",
    isActive: true,
    is_private: false,
    validity_duration: 30
  },
  {
    tenantId: 1,
    name: "Premium Fitness Package", 
    description: "Premium package with all amenities",
    isActive: true,
    is_private: false,
    validity_duration: 90
  },
  {
    tenantId: 1,
    name: "Personal Training Package",
    description: "One-on-one personal training sessions",
    isActive: true,
    is_private: false,
    validity_duration: 60
  },
  {
    tenantId: 2,
    name: "Yoga Classes Package",
    description: "Unlimited yoga classes for a month",
    isActive: true,
    is_private: false,
    validity_duration: 30
  }
];

async function seedPackages() {
  const baseUrl = 'http://localhost:3000';
  
  console.log('🌱 Seeding sample packages...');
  
  for (const pkg of samplePackages) {
    try {
      const response = await fetch(`${baseUrl}/api/packages`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(pkg)
      });
      
      if (response.ok) {
        const result = await response.json();
        console.log(`✅ Created package: ${pkg.name} (ID: ${result.data.id})`);
      } else {
        const error = await response.json();
        console.error(`❌ Failed to create package ${pkg.name}:`, error.error);
      }
    } catch (error) {
      console.error(`❌ Error creating package ${pkg.name}:`, error.message);
    }
  }
  
  console.log('🌱 Package seeding completed!');
}

// Run if called directly
if (require.main === module) {
  seedPackages().catch(console.error);
}

module.exports = { seedPackages, samplePackages };
